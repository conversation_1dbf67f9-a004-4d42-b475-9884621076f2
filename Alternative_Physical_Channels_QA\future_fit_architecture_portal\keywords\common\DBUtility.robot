*** Settings ***
#Library    DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Library                                             ../../../common_utilities/CommonUtils.py


*** Variables ***

${DB_HOST}          oss-dsdc-01371.corp.dsarena.com
${DB_USER}          app_account
${DB_PASSWORD}      DG9UIMbXWs
${DB_DATABASE}      ATM_Marketing
${DB_PORT}          3306
${QUERY}            SELECT * FROM ATM_Marketing.Campaign where version in ('v001Q22024') and isActive = true

*** Keywords ***
Execute SQL Query
   [Arguments]  ${DB_TYPE}    ${QUERY}    ${RETURN_DATA_BOOLEAN}    ${RETURN_ALL_RECORDS}=False     &{FIELDS_TO_VALIDATE}

   ${return_data}=    Convert To Boolean    ${RETURN_DATA_BOOLEAN}
   ${return_all}=    Convert To Boolean    ${RETURN_ALL_RECORDS}
   ${data_found}=       Verify Data Using Database   ${DB_TYPE}    ${QUERY}   ${return_data}   ${return_all}    &{FIELDS_TO_VALIDATE}
   #Log To Console    ${data_found}
   Should Not Contain	${data_found}	Failed
   Should Not Contain	${data_found}	${null}
   Should Not Contain	${data_found}	${EMPTY}
   RETURN  ${data_found}


Execute SQL Query and Continue if no records found
   [Arguments]  ${DB_TYPE}    ${QUERY}    ${RETURN_DATA_BOOLEAN}     &{FIELDS_TO_VALIDATE}

   ${return_data}=    Convert To Boolean    ${RETURN_DATA_BOOLEAN}

   ${data_found}=       Verify Data Using Database   ${DB_TYPE}    ${QUERY}   ${return_data}   &{FIELDS_TO_VALIDATE}
   #Log To Console    ${data_found}
   Run Keyword And Continue On Failure  Should Not Contain	${data_found}	Failed
   Run Keyword And Continue On Failure  Should Not Contain	${data_found}	${null}
   Run Keyword And Continue On Failure  Should Not Contain	${data_found}	${EMPTY}
   RETURN  ${data_found}

Get number of records returned by query
    [Arguments]     @{SQL_RECORDS}

     ${dict_count} =    Count Dictionaries    @{SQL_RECORDS}
     RETURN      ${dict_count}