*** Settings ***
#Author Name               : TH<PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/EmailManagement.robot
Resource                                            ../../keywords/common/DatabaseConnector.robot

*** Variables ***


*** Keywords ***
Validate Add New Vendor Email Functionality
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${VENDOR_NAME}    ${VENDOR_EMAIL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - Email Management
    And The user Adds a new VMS Vendor Email    ${VENDOR_NAME}    ${VENDOR_EMAIL}
    And The user navigates to Admin - Email Management
    And Sleep  2s
    And Searches for existing user    ${VENDOR_EMAIL}
    And The created user must be found on VMS Application and Database    ${VENDOR_EMAIL}    ${VENDOR_NAME}

*** Test Cases ***
| Validate Add new vendor email- Email Management: Create a new VMS Vendor Email. |
| | [Documentation] | Create a VMS Vendor Email. |
| | Validate Add New Vendor Email Functionality | Create a VMS Vendor Email. | VMS_UAT | BMS | <EMAIL> |
