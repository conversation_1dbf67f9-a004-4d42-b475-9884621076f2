<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="******** 12:59:09.686" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\development\future-fit-architecture-portal-docker\tests\TC_01_PUT_MarketingCampaignController.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:59:10.399" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value 'Yaash.<PERSON><EMAIL>'.</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:59:10.399" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:59:10.399" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - PUT ATM Marketing Campaign using a Business Approver" line="44">
<kw name="PUT ATM Marketing Campaign">
<arg>PUT ATM Marketing Campaign</arg>
<arg>155057481</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMMarketingCampaign</arg>
<arg>14350</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>Campaign Deactivated</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:59:10.399" level="INFO">Set test documentation to:
PUT ATM Marketing Campaign</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:59:10.399" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057481'.</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<msg timestamp="******** 12:59:10.399" level="INFO">Returning from the enclosing user keyword.</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<status status="NOT RUN" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.399"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:59:10.415" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:59:10.415" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.415"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:59:10.415" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:59:10.415" endtime="******** 12:59:10.415"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:59:10.416" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.416"/>
</kw>
<kw name="And The user makes Put Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 12:59:10.416" level="INFO">${end_point} = /ATMMarketingCampaign/14350</msg>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</kw>
<status status="NOT RUN" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 12:59:10.416" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvO...</msg>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</branch>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.416"/>
</if>
<kw name="PUT On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a PUT request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:59:10.745" level="INFO">PUT Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingCampaign/14350 
 path_url=/ATMMarketingCampaign/14350 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSIsImtpZCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ROe4HCono7xXRXYqdWg8H4gHjxLOQ5AE374-nP-umUwM1DiPlPvrBEGXQgqU_ufhQrUMeXdAhRmys9EG8YoRopccViOVA6pKpLr_pPPrwQ8vEfwuU4wa2M3TXGZJsq4ALIGmIHlt-5jc5z43OcpluhFD03qEu9SBqQNpTBGN2zcfhzJZClywqHoKe2W79Bh0VbFnJekwIusU_UVQ9bnSQWJN_fftuWHvunj4NvCZdeeHOGd24lcPy5-rvxWCPOumzViTe9dS5dldXXKZJWtkkMjdp6uLLVLOnszPiDH2bVPsEEdcSQZN7PQnRJsa0X0zshsWVMATHLzmKjuXPW1GkA', 'Content-Length': '0'} 
 body=None 
 </msg>
<msg timestamp="******** 12:59:10.745" level="INFO">PUT Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingCampaign/14350 
 status=200, reason=OK 
 headers={'Date': 'Tue, 12 Mar 2024 10:59:10 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=Campaign Deactivated 
 </msg>
<msg timestamp="******** 12:59:10.745" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:59:10.745" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.745"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:59:10.745" level="INFO">${response.content} = Campaign Deactivated</msg>
<status status="PASS" starttime="******** 12:59:10.745" endtime="******** 12:59:10.745"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:59:10.745" level="INFO">Campaign Deactivated</msg>
<status status="PASS" starttime="******** 12:59:10.745" endtime="******** 12:59:10.745"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:59:10.754" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 12:59:10.745" endtime="******** 12:59:10.754"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 12:59:10.754" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">${response.content} = Campaign Deactivated</msg>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.416" endtime="******** 12:59:10.755"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<kw name="Log" library="BuiltIn">
<arg>Response Message : ${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">Response Message : Campaign Deactivated</msg>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:59:10.755" level="INFO">Campaign Deactivated</msg>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<kw name="Should Contain" library="BuiltIn">
<arg>As Strings ${response.content}</arg>
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.755" endtime="******** 12:59:10.755"/>
</kw>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.755"/>
</kw>
<doc>PUT ATM Marketing Campaign</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 12:59:10.399" endtime="******** 12:59:10.763"/>
</test>
<doc>This is the test suite for deactivating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 12:59:09.855" endtime="******** 12:59:14.614"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
