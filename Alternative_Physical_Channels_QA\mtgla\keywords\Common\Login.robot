*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation  Login to MTGLA system

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                             RequestsLibrary
Library                                             String
#***********************************PROJECT RESOURCES***************************************
Library                                            ../../utility/Common_Functions.py
Resource                                            ../../keywords/Common/SetEnvironmentVariales.robot

*** Variables ***
${base64_string_P}     %24vc%2A_MT6L%40_U%26T
${base64_string_U}     //CORP%5CSVC-MTGLA_UAT

*** Keywords ***
Begin Web test
    [Arguments]         ${base_u}
    Kill process    ${BROWSER}

    ${is_browser_browser}=   Get Environment Variable    IS_HEADLESS_BROWSER
    #Decide whether to run headles chrome or displayed chrome
    ${is_headless_browser_type}=     Convert To Upper Case       ${is_browser_browser}
    ${browser_name}=     Convert To Upper Case       ${BROWSER}

    #Call Method     ${chrome_options}    add_argument            --headless=new  #Run headless browser

    #Open the browser
    #Set Log Level    NONE
     IF    '${browser_name}' == 'EDGE'
        ${edgedriver_path}=      Set Variable    C:/bin/msedgedriver.exe    #Get EDGE Driver Path
        ${edge_options}=    Get Edge Driver Options
        IF    '${is_headless_browser_type}' == 'YES'
            ${edge_options}=    Get EDGE Driver Options
            Open Browser                        ${base_u}      ${BROWSER}     executable_path=${edgedriver_path}
            Sleep    5s
            Execute JavaScript    location.reload(true)
        ELSE
            Open Browser                        ${base_u}      ${BROWSER}     executable_path=${edgedriver_path}
            Sleep    5s
            Execute JavaScript    location.reload(true)
            #Open Browser     about:blank      ${BROWSER}     executable_path=${edgedriver_path}
        END

    ELSE
        ${chromedriver_path}=       Set Variable    C:/bin/chromedriver.exe                    #C:/Users/<USER>/chromedriver/win64-130.0.6723.59/chromedriver-win64/chromedriver.exe     #Get Chromedriver Path
        ${chrome_options}=     Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys, selenium.webdriver

        IF    '${is_headless_browser_type}' == 'YES'
            ${BROWSER}=   Set Variable      headlesschrome
            Call Method     ${chrome_options}    add_argument            --headless
        END

        ${prefs}  Create Dictionary  useAutomationExtension=${FALSE}  #excludeSwitches=['enable-automation']  #download.prompt_for_download=${FALSE}  plugins.always_open_pdf_externally=${TRUE}  plugins.plugins_disabled=${disabled}
        Call Method     ${chrome_options}   add_experimental_option  prefs  ${prefs}

        ${user_home}=    Get Environment Variable    UserProfile
        Log     Logged in User: ${user_home}

        Call Method     ${chrome_options}    add_argument            user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data
        Call Method     ${chrome_options}    add_argument            --disable-dev-shm-usage  #disable page crash of chrome
        Call Method     ${chrome_options}    add_argument            --ignore-certificate-errors
    #    Call Method     ${chrome_options}    add_argument            --enable-features=NetworkServic
        Call Method     ${chrome_options}    add_argument            --no-sandbox
        ${dc}   Evaluate    sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME  sys, selenium.webdriver
        ${Options}=    Call Method    ${ChromeOptions}    to_capabilities
        #Sleep    10s
        Open Browser                        ${base_u}      ${BROWSER}     options=${chrome_options}       executable_path=${chromedriver_path}
        Sleep    5s
        Execute JavaScript    location.reload(true)
        Capture Page Screenshot     MTGLA_Login.png
    END


End Web Test
    #Close the browser
    Sleep    5s
    Close Browser


the user logs into the MTGLA Web Application    
    #[Arguments]     ${BROWSER}  ${DRIVER_BROWSER}  
    
    ${system}=    Evaluate    platform.system()    platform

    Log to Console    \nOperating System: ${system}

    Log to console  ----The user logs into the MTGLA Web Application----                             

    Run Keyword If    '${system}' == 'Windows'    Windows system Login    ${base64_string_U}  ${base64_string_P}
    ...    ELSE    Linux system login    
    Maximize Browser Window


Windows system Login
    [Arguments]    ${base64_string_U}    ${base64_string_P}
    

    # Use the URL-safe base64-encoded credentials directly
    ${base_u}=    Catenate    https://${base64_string_U}:${base64_string_P}@zaurnbmweb0126
  
    
    Begin Web test    ${base_u}



Linux system login
    #[Arguments]  ${URL}
    ${chrome_options}=    Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys

    Call Method    ${chrome_options}    add_argument    --disable-dev-shm-usage  #disable page crash of chrome

    Call Method    ${chrome_options}    add_argument    --no-sandbox  #disable sandbox mode

    Call Method    ${chrome_options}    add_argument    --headless  #run with no web browser

    Call Method    ${chrome_options}    add_argument    --ignore-certificate-errors
    
    Create Webdriver    driver_name=Chrome    alias=ffadriver    chrome_options=${chrome_options}    executable_path=/usr/local/bin/chromedriver
    
    Go To    ${URL}

   Maximize Browser Window


Kill process
    [Arguments]  ${PROCESS_NAME}

    ${PROCESS_NAME_LowerCase}=    Evaluate     "${PROCESS_NAME}".lower()

    IF    '${PROCESS_NAME_LowerCase}' == 'edge'
         ${PROCESS_NAME_LowerCase}=        Set Variable     msedge
    END
    ${handle} =   Catenate    SEPARATOR=.   ${PROCESS_NAME_LowerCase}   exe

    Log to Console    \nProcess to kill: ${handle}

    ${rc_code}    ${output}    Run And Return Rc And Output    taskkill /F /IM ${handle}
    Run Keyword If    '${rc_code}' != '0'    Log    There was error during termination of process    WARN
