<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-05-27T11:08:56.018960" rpa="false" schemaversion="5">
<suite id="s1" name="RAC29a TC 333 Validate Update Link Email Management" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ADMIN_EMAIL_MANAGEMENT\RAC29a_TC_333_Validate_Update_Link_Email_Management.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<kw name="Set Variable" owner="BuiltIn">
<var>${url}</var>
<arg>${QMETRY_API_URL}/rest/search</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${headers}</var>
<arg>apikey=${API_KEY}</arg>
<arg>project=987</arg>
<arg>Content-Type=application/json</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${payload}</var>
<arg>value=${rac_number}</arg>
<arg>entityType=TC</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${method}</var>
<arg>POST</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Send Rest Request" owner="RestRequestsMarshal">
<var>${response}</var>
<arg>${url}</arg>
<arg>method=${method}</arg>
<arg>headers=${headers}</arg>
<arg>payload=${payload}</arg>
<doc>Sends a generic REST request using the requests library and processes the response.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<arg>${response}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${resp_data_1}</var>
<arg>${response}</arg>
<arg>data</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${resp_data_2}</var>
<arg>${resp_data_1}</arg>
<arg>data</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${resp_data_2}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<var>${resp_contain_data}</var>
<arg>Should Not Be Empty</arg>
<arg>${resp_data_2}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="not ${resp_contain_data}">
<return>
<value>${EMPTY}</value>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</return>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</if>
<kw name="Get From List" owner="Collections">
<var>${first_row_results}</var>
<arg>${resp_data_2}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${test_case_id}</var>
<arg>${first_row_results}</arg>
<arg>tcID</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<return>
<value>${test_case_id}</value>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</return>
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.000000"/>
</kw>
<test id="s1-t1" name="Validate Update Link- Email Management" line="35">
<kw name="Validate Random Email Update Functionality">
<kw name="Set Test Documentation" owner="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.469680" elapsed="0.015559"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.486681" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.486681" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.486681" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.486681" elapsed="0.001087"/>
</kw>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.486681" elapsed="0.001087"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.487768" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.487768" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.487768" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.487768" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.487768" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.487768" elapsed="0.000000"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.488770" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.488770" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.488770" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.488770" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.488770" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-05-27T11:08:58.488770" elapsed="0.000995"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.489765" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.490767" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491768" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491768" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.490767" elapsed="0.001192"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.490767" elapsed="0.001192"/>
</if>
<status status="PASS" start="2025-05-27T11:08:58.490767" elapsed="0.001192"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.489765" elapsed="0.002194"/>
</if>
<status status="PASS" start="2025-05-27T11:08:58.488770" elapsed="0.003189"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Fail" owner="BuiltIn">
<arg>Test URL not provided</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<kw name="Run Keyword Until Success" owner="Navigation">
<kw name="Wait Until Keyword Succeeds" owner="BuiltIn">
<arg>30s</arg>
<arg>1s</arg>
<arg>${KW}</arg>
<arg>${KWARGS}</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<kw name="Run Keyword Until Success" owner="Navigation">
<kw name="Wait Until Keyword Succeeds" owner="BuiltIn">
<arg>30s</arg>
<arg>1s</arg>
<arg>${KW}</arg>
<arg>${KWARGS}</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<kw name="Login to Mocrosoft" owner="Login">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${MS_USERNAME}</var>
<arg>MS_USERNAME</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${MS_PASSWORD}</var>
<arg>MS_PASSWORD</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Username is ${MS_USERNAME} password id ${MS_PASSWORD}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword Until Success" owner="Navigation">
<kw name="Wait Until Keyword Succeeds" owner="BuiltIn">
<arg>30s</arg>
<arg>1s</arg>
<arg>${KW}</arg>
<arg>${KWARGS}</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>Wait Until Element Is Visible</arg>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<arg>${MS_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${MICROSOFT_EMAIL_NEXT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${MICROSOFT_EMAIL_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${MICROSOFT_EMAIL_PASSWORD_INPUT}</arg>
<arg>${MS_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${MICROSOFT_EMAIL_SIGN_IN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${MICROSOFT_DONT_SAVE_PASSWORD}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${MICROSOFT_DONT_SAVE_PASSWORD}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</iter>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-05-27T11:08:58.486681" elapsed="0.005278"/>
</kw>
<kw name="Linux system login" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Create Webdriver" owner="SeleniumLibrary">
<arg>driver_name=Chrome</arg>
<arg>alias=ffadriver</arg>
<arg>chrome_options=${chrome_options}</arg>
<arg>executable_path=/usr/local/bin/chromedriver</arg>
<doc>Creates an instance of Selenium WebDriver.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Go To" owner="SeleniumLibrary">
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>${URL}</arg>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.486681" elapsed="0.005278"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-05-27T11:08:58.485969" elapsed="0.005990"/>
</kw>
<kw name="When The user navigates to Admin - Email Management" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.000000"/>
</kw>
<kw name="And Update Random Email From Database" owner="EmailManagement">
<kw name="Get Random VMS Email" owner="DatabaseConnector">
<kw name="Set Variable" owner="BuiltIn">
<var>${my_query}</var>
<arg>${SQL_GET_RANDOM_VMS_EMAIL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.502177" elapsed="0.000000"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<arg>${my_query}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.502177" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${DB_TYPE}</var>
<arg>MSSQL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.502177" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_HOST}</var>
<arg>MS_DB_HOST</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.502177" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_NAME}</var>
<arg>MS_DB_SCHEMA</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.502177" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_USER}</var>
<arg>MS_DB_User</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_PASSWORD}</var>
<arg>MS_DB_PWD</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<kw name="Execute Select Query" owner="DatabaseConnector">
<kw name="Execute Select Statement" owner="Database_Library">
<var>${results}</var>
<arg>${db_type}</arg>
<arg>${host}</arg>
<arg>${database}</arg>
<arg>${username}</arg>
<arg>${password}</arg>
<arg>${query}</arg>
<doc>Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
Raises an error if the query fails.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<kw name="Return From Keyword" owner="BuiltIn">
<arg>${results}</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<var>${results}</var>
<arg>${DB_TYPE}</arg>
<arg>${DB_HOST}</arg>
<arg>${DB_NAME}</arg>
<arg>${DB_USER}</arg>
<arg>${DB_PASSWORD}</arg>
<arg>${my_query}</arg>
<doc>Execute a database query against the VMS database and return the results
This keyword handles the connection to the VMS database,
executes the query, and returns the results.</doc>
<status status="PASS" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<arg>${results}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${results}</arg>
<arg>msg=No emails found in the database</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.503259" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<var>${random_email_record}</var>
<arg>${results}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.504251" elapsed="0.000000"/>
</kw>
<return>
<value>${random_email_record}</value>
<status status="PASS" start="2025-05-27T11:08:58.504251" elapsed="0.000000"/>
</return>
<var>${random_email_record}</var>
<doc>Get a random email from the VMS database for testing purposes</doc>
<status status="PASS" start="2025-05-27T11:08:58.502177" elapsed="0.002074"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${original_link_id}</var>
<arg>${random_email_record}</arg>
<arg>Link</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.504251" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${original_vendor}</var>
<arg>${random_email_record}</arg>
<arg>Vendor</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.504251" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${original_email}</var>
<arg>${random_email_record}</arg>
<arg>Email</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.504251" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Original Email Details - Link ID: ${original_link_id}, Vendor: ${original_vendor}, Email: ${original_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.504251" elapsed="0.000000"/>
</kw>
<kw name="Searches for existing user" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VENDOR_SEARCH_INPUT}</arg>
<arg>${SEARCH_DATA}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${table_tbody}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<var>${IsElementVisible}</var>
<arg>Element Should Be Visible</arg>
<arg>${table_tbody}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.505259" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${IsElementVisible} == $True">
<kw name="Log" owner="BuiltIn">
<arg>The Vendor: '${SEARCH_DATA}' was returned on the search results, which means it exists on VMS.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.505259" elapsed="0.000989"/>
</branch>
<branch type="ELSE">
<kw name="Fail" owner="BuiltIn">
<arg>The Vendor: '${SEARCH_DATA}' was not returned on the search results, which means it does not exist on VMS</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.505259" elapsed="0.000989"/>
</if>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>User_Search_${SEARCH_DATA}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</kw>
<arg>${original_email}</arg>
<status status="PASS" start="2025-05-27T11:08:58.504251" elapsed="0.001997"/>
</kw>
<kw name="The user updates vendor email" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.506248" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${update_button}</var>
<arg>${table_rows_element}/td[4]/div/a[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${update_button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${EDIT_EMAIL}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Clear Element Text" owner="SeleniumLibrary">
<arg>${EDIT_EMAIL}</arg>
<doc>Clears the value of the text-input-element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${EDIT_EMAIL}</arg>
<arg>${UPDATED_EMAIL}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>Update_Email_Modal.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${UPDATE_CALL}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.507198" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.508242" elapsed="0.000198"/>
</kw>
<var>${confirmation_visible}</var>
<arg>SeleniumLibrary.Element Should Be Visible</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.508242" elapsed="0.000306"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Get Text" owner="SeleniumLibrary">
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION_MESSAGE}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.508548" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>Email_Update_Confirmation.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.508548" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${NEW_VENDOR_EMAIL_ADDED_OK_BTN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.508548" elapsed="0.000000"/>
</kw>
<arg>SeleniumLibrary.Get Text</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION_MESSAGE}</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Update_Confirmation.png</arg>
<arg>AND</arg>
<arg>SeleniumLibrary.Click Element</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_OK_BTN}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-05-27T11:08:58.508548" elapsed="0.000000"/>
</kw>
<arg>${confirmation_visible}</arg>
<arg>Run Keywords</arg>
<arg>SeleniumLibrary.Get Text</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_CONFIRMATION_MESSAGE}</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Update_Confirmation.png</arg>
<arg>AND</arg>
<arg>SeleniumLibrary.Click Element</arg>
<arg>${NEW_VENDOR_EMAIL_ADDED_OK_BTN}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.508548" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword Unless" owner="BuiltIn">
<msg time="2025-05-27T11:08:58.508548" level="WARN">Keyword 'BuiltIn.Run Keyword Unless' is deprecated. </msg>
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<arg>Confirmation dialog not found with expected ID, proceeding with test</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.510898" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>Email_Update_Alternative.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.510898" elapsed="0.001018"/>
</kw>
<arg>Log</arg>
<arg>Confirmation dialog not found with expected ID, proceeding with test</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Update_Alternative.png</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-05-27T11:08:58.510898" elapsed="0.001018"/>
</kw>
<arg>${confirmation_visible}</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>Confirmation dialog not found with expected ID, proceeding with test</arg>
<arg>AND</arg>
<arg>capture page screenshot</arg>
<arg>Email_Update_Alternative.png</arg>
<doc>*DEPRECATED since RF 5.0. Use Native IF/ELSE or `Run Keyword If` instead.*</doc>
<status status="PASS" start="2025-05-27T11:08:58.508548" elapsed="0.003368"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.511916" elapsed="0.000000"/>
</kw>
<arg>${original_vendor}</arg>
<arg>${UPDATED_EMAIL}</arg>
<status status="PASS" start="2025-05-27T11:08:58.506248" elapsed="0.005668"/>
</kw>
<kw name="The user navigates to Admin - Email Management" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.511916" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.511916" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.511916" elapsed="0.000982"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.512898" elapsed="0.000000"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.511916" elapsed="0.001182"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Searches for existing user" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VENDOR_SEARCH_INPUT}</arg>
<arg>${SEARCH_DATA}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.513098" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${table_tbody}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<var>${IsElementVisible}</var>
<arg>Element Should Be Visible</arg>
<arg>${table_tbody}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.513098" elapsed="0.001522"/>
</kw>
<if>
<branch type="IF" condition="${IsElementVisible} == $True">
<kw name="Log" owner="BuiltIn">
<arg>The Vendor: '${SEARCH_DATA}' was returned on the search results, which means it exists on VMS.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Fail" owner="BuiltIn">
<arg>The Vendor: '${SEARCH_DATA}' was not returned on the search results, which means it does not exist on VMS</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</if>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>User_Search_${SEARCH_DATA}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<arg>${UPDATED_EMAIL}</arg>
<status status="PASS" start="2025-05-27T11:08:58.513098" elapsed="0.001522"/>
</kw>
<kw name="The updated email must be found on VMS Application and Database" owner="EmailManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${table_tbody}</var>
<arg>${VMS_USERS_TABLE}</arg>
<arg>/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${table_rows_element}</var>
<arg>${table_tbody}/tr[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${id_element}</var>
<arg>${table_rows_element}/td[1]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${vendor_name_element}</var>
<arg>${table_rows_element}/td[2]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<var>${email_element}</var>
<arg>${table_rows_element}/td[3]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${id_text}</var>
<arg>${id_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${vendor_name_text}</var>
<arg>${vendor_name_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${email_text}</var>
<arg>${email_element}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${email_text}</arg>
<arg>${UPDATED_EMAIL}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Strip String" owner="String">
<var>${vendor_name_text_trimmed}</var>
<arg>${vendor_name_text}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Strip String" owner="String">
<var>${vendor_name_trimmed}</var>
<arg>${VENDOR_NAME}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>UI Vendor Name: "${vendor_name_text}" (length: ${vendor_name_text.__len__()})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Expected Vendor Name: "${VENDOR_NAME}" (length: ${VENDOR_NAME.__len__()})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>UI Vendor Name Trimmed: "${vendor_name_text_trimmed}" (length: ${vendor_name_text_trimmed.__len__()})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Expected Vendor Name Trimmed: "${vendor_name_trimmed}" (length: ${vendor_name_trimmed.__len__()})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${vendor_name_text_trimmed}</arg>
<arg>${vendor_name_trimmed}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<arg>${VENDOR_NAME}_Updated.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Get VMS Vendor Details" owner="DatabaseConnector">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${linkId}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<var>${result1}</var>
<arg>Should Not Be Empty</arg>
<arg>${linkId}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${condition1}</var>
<arg>${result1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Fail" owner="BuiltIn">
<arg>Please make sure that link id parameter value is provided!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.514620" elapsed="0.000000"/>
</kw>
<arg>${condition1} == ${FALSE}</arg>
<arg>Fail</arg>
<arg>Please make sure that link id parameter value is provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.003991"/>
</kw>
<kw name="Replace String" owner="String">
<var>${linkId}</var>
<arg>${linkId}</arg>
<arg>'</arg>
<arg>''</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.518611" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${my_query}</var>
<arg>${SQL_VMS_EMAIL_DETAILS}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.519640" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${my_query}</var>
<arg>${my_query}</arg>
<arg>LINK_ID</arg>
<arg>${linkId}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.519640" elapsed="0.000000"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<arg>${my_query}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.519640" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${DB_TYPE}</var>
<arg>MSSQL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.520672" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_HOST}</var>
<arg>MS_DB_HOST</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.520672" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_NAME}</var>
<arg>MS_DB_SCHEMA</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.520672" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_USER}</var>
<arg>MS_DB_User</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.520672" elapsed="0.000000"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<var>${DB_PASSWORD}</var>
<arg>MS_DB_PWD</arg>
<status status="NOT RUN" start="2025-05-27T11:08:58.520672" elapsed="0.000000"/>
</kw>
<kw name="Execute Select Query" owner="DatabaseConnector">
<kw name="Execute Select Statement" owner="Database_Library">
<var>${results}</var>
<arg>${db_type}</arg>
<arg>${host}</arg>
<arg>${database}</arg>
<arg>${username}</arg>
<arg>${password}</arg>
<arg>${query}</arg>
<doc>Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
Raises an error if the query fails.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.521665" elapsed="0.000000"/>
</kw>
<kw name="Return From Keyword" owner="BuiltIn">
<arg>${results}</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.521665" elapsed="0.000000"/>
</kw>
<var>${results}</var>
<arg>${DB_TYPE}</arg>
<arg>${DB_HOST}</arg>
<arg>${DB_NAME}</arg>
<arg>${DB_USER}</arg>
<arg>${DB_PASSWORD}</arg>
<arg>${my_query}</arg>
<doc>Execute a database query against the VMS database and return the results
This keyword handles the connection to the VMS database,
executes the query, and returns the results.</doc>
<status status="PASS" start="2025-05-27T11:08:58.520672" elapsed="0.000993"/>
</kw>
<kw name="Log Many" owner="BuiltIn">
<arg>${results}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.521665" elapsed="0.000000"/>
</kw>
<return>
<value>${results}</value>
<status status="PASS" start="2025-05-27T11:08:58.521665" elapsed="0.000000"/>
</return>
<var>${email_details}</var>
<arg>${id_text.strip()}</arg>
<doc>Get user details from the VMS database by username</doc>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.007045"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Attempted database verification for ID: ${id_text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.521665" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${email_details}</arg>
<arg>msg=Email record not found in the database</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.521665" elapsed="0.001002"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Email verification in database successful for ID: ${id_text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.522667" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<var>${db_first_row}</var>
<arg>${email_details}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.522667" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${db_vendor_email}</var>
<arg>${db_first_row}</arg>
<arg>Email</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.522667" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${db_vendor}</var>
<arg>${db_first_row}</arg>
<arg>Vendor</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.522667" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Database Link: "${db_vendor_email}", User provided email: "${UPDATED_EMAIL}"</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.522667" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Database Vendor: "${db_vendor}", User provided vendor name: "${VENDOR_NAME}"</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.522667" elapsed="0.000000"/>
</kw>
<kw name="Convert To String" owner="BuiltIn">
<var>${db_vendor_email_trimmed}</var>
<arg>${db_vendor_email}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.523661" elapsed="0.000000"/>
</kw>
<kw name="Strip String" owner="String">
<var>${db_vendor_email_trimmed}</var>
<arg>${db_vendor_email_trimmed}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.523661" elapsed="0.000000"/>
</kw>
<kw name="Convert To String" owner="BuiltIn">
<var>${id_text_trimmed}</var>
<arg>${id_text}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.523661" elapsed="0.000000"/>
</kw>
<kw name="Strip String" owner="String">
<var>${id_text_trimmed}</var>
<arg>${id_text_trimmed}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.523661" elapsed="0.000000"/>
</kw>
<kw name="Convert To String" owner="BuiltIn">
<var>${db_vendor_trimmed}</var>
<arg>${db_vendor}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.523661" elapsed="0.000000"/>
</kw>
<kw name="Strip String" owner="String">
<var>${db_vendor_trimmed}</var>
<arg>${db_vendor_trimmed}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.524660" elapsed="0.000000"/>
</kw>
<kw name="Convert To String" owner="BuiltIn">
<var>${name_trimmed}</var>
<arg>${vendor_name_text}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.524820" elapsed="0.000198"/>
</kw>
<kw name="Strip String" owner="String">
<var>${name_trimmed}</var>
<arg>${name_trimmed}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.525018" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${db_vendor_email_trimmed}</arg>
<arg>${UPDATED_EMAIL}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.525018" elapsed="0.000500"/>
</kw>
<kw name="Strip String" owner="String">
<var>${vendor_name_param_trimmed}</var>
<arg>${VENDOR_NAME}</arg>
<doc>Remove leading and/or trailing whitespaces from the given string.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.525518" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${db_vendor_trimmed}</arg>
<arg>${vendor_name_param_trimmed}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.525518" elapsed="0.000000"/>
</kw>
<return>
<value>${id_text.strip()}</value>
<status status="PASS" start="2025-05-27T11:08:58.525518" elapsed="0.000000"/>
</return>
<var>${current_link_id}</var>
<arg>${UPDATED_EMAIL}</arg>
<arg>${original_vendor}</arg>
<status status="PASS" start="2025-05-27T11:08:58.514620" elapsed="0.010898"/>
</kw>
<kw name="Verify VMS Email Link Updated" owner="DatabaseConnector">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${link_id}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.526581" elapsed="0.000000"/>
</kw>
<var>${result1}</var>
<arg>Should Not Be Empty</arg>
<arg>${link_id}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.526581" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${expected_email}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.527325" elapsed="0.000000"/>
</kw>
<var>${result2}</var>
<arg>Should Not Be Empty</arg>
<arg>${expected_email}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.526581" elapsed="0.000744"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Fail" owner="BuiltIn">
<arg>Please make sure that both link_id and expected_email parameters are provided!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.527325" elapsed="0.000000"/>
</kw>
<arg>${result1} == ${FALSE} or ${result2} == ${FALSE}</arg>
<arg>Fail</arg>
<arg>Please make sure that both link_id and expected_email parameters are provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.527325" elapsed="0.000000"/>
</kw>
<kw name="Get VMS Email Details" owner="DatabaseConnector">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${link_id}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.527325" elapsed="0.000000"/>
</kw>
<var>${result1}</var>
<arg>Should Not Be Empty</arg>
<arg>${link_id}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-05-27T11:08:58.527325" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${condition1}</var>
<arg>${result1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.527325" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Fail" owner="BuiltIn">
<arg>Please make sure that link_id parameter value is provided!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<arg>${condition1} == ${FALSE}</arg>
<arg>Fail</arg>
<arg>Please make sure that link_id parameter value is provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${link_id_data}</var>
<arg>${link_id}</arg>
<arg>'</arg>
<arg>''</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${query}</var>
<arg>SELECT TOP (1000) [Link], [Vendor], [Email] FROM [${VMS_DB_NAME}].[core].[email] WHERE Link = '${link_id_data}'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<kw name="Execute VMS Database Query" owner="DatabaseConnector">
<kw name="Log" owner="BuiltIn">
<arg>Executing VMS query: ${query}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${results}</var>
<arg>__import__('sys').path.append('C:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common/../../../common_utilities')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${db_lib}</var>
<arg>__import__('Database_Library')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.528392" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${results}</var>
<arg>$db_lib.execute_select_statement('${VMS_DB_TYPE}', '${VMS_DB_HOST}', '${VMS_DB_NAME}', '${VMS_DB_USER}', '${VMS_DB_PASSWORD}', $query)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.529397" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Query results: ${results}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.529397" elapsed="0.000000"/>
</kw>
<return>
<value>${results}</value>
<status status="PASS" start="2025-05-27T11:08:58.529397" elapsed="0.000000"/>
</return>
<var>${results}</var>
<arg>${query}</arg>
<doc>Execute a database query against the VMS database and return the results
This keyword handles the connection to the VMS database,
executes the query, and returns the results.</doc>
<status status="PASS" start="2025-05-27T11:08:58.528392" elapsed="0.001005"/>
</kw>
<return>
<value>${results}</value>
<status status="PASS" start="2025-05-27T11:08:58.529397" elapsed="0.000000"/>
</return>
<var>${email_details}</var>
<arg>${link_id}</arg>
<doc>Get email details from the VMS database by link ID</doc>
<status status="PASS" start="2025-05-27T11:08:58.527325" elapsed="0.002072"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<arg>${email_details}</arg>
<arg>msg=No email record found for Link ID ${link_id}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.529397" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<var>${first_row}</var>
<arg>${email_details}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.529397" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${actual_email}</var>
<arg>${first_row}</arg>
<arg>Email</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530395" elapsed="0.000000"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<var>${actual_vendor}</var>
<arg>${first_row}</arg>
<arg>Vendor</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530395" elapsed="0.000255"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Verifying update for Link ID: ${link_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Expected Email: ${expected_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Actual Email: ${actual_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Vendor: ${actual_vendor}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${actual_email}</arg>
<arg>${expected_email}</arg>
<arg>msg=Email update verification failed. Link ID ${link_id} contains '${actual_email}' but expected '${expected_email}'</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Email update verification successful - Link ID ${link_id} now contains email ${expected_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<return>
<value>${actual_email}</value>
<value>${actual_vendor}</value>
<status status="PASS" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</return>
<arg>${current_link_id}</arg>
<arg>${UPDATED_EMAIL}</arg>
<doc>Verify that an email link ID contains the expected updated email address</doc>
<status status="PASS" start="2025-05-27T11:08:58.525518" elapsed="0.005132"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>===== EMAIL UPDATE TEST COMPLETED SUCCESSFULLY =====</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Original Link ID: ${original_link_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Current Link ID: ${current_link_id}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Original Vendor: ${original_vendor}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Original Email: ${original_email}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Updated Email: ${UPDATED_EMAIL}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Verification Status: Email updated and verified in both UI and Database</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Database Verification: Link ID ${current_link_id} now contains email ${UPDATED_EMAIL}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>UI Verification: Email ${UPDATED_EMAIL} found in Email Management search results</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>===== TEST SUMMARY: UPDATE SUCCESSFUL =====</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<return>
<value>${original_link_id}</value>
<value>${original_vendor}</value>
<value>${original_email}</value>
<status status="PASS" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</return>
<arg>${UPDATED_EMAIL}</arg>
<doc>Get a random email from database, update it, and verify the changes</doc>
<status status="PASS" start="2025-05-27T11:08:58.491959" elapsed="0.038691"/>
</kw>
<arg>Validate the update functionality for vendor emails in Email Management.</arg>
<arg>VMS_UAT</arg>
<arg><EMAIL></arg>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.060970"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-05-27T11:08:58.530650" elapsed="0.000000"/>
</kw>
<doc>Validate the update functionality for vendor emails in Email Management.</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" start="2025-05-27T11:08:58.469680" elapsed="0.060970"/>
</test>
<doc>Validate Update Link functionality in Email Management</doc>
<status status="PASS" start="2025-05-27T11:08:56.022990" elapsed="2.512769"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">HEALTHCHECK_STATUS</stat>
<stat pass="1" fail="0" skip="0">Login</stat>
<stat pass="1" fail="0" skip="0">VMS_HEALTHCHECK</stat>
</tag>
<suite>
<stat name="RAC29a TC 333 Validate Update Link Email Management" id="s1" pass="1" fail="0" skip="0">RAC29a TC 333 Validate Update Link Email Management</stat>
</suite>
</statistics>
<errors>
<msg time="2025-05-27T11:08:57.156183" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-05-27T11:08:57.156183" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-05-27T11:08:58.508548" level="WARN">Keyword 'BuiltIn.Run Keyword Unless' is deprecated. </msg>
</errors>
</robot>
