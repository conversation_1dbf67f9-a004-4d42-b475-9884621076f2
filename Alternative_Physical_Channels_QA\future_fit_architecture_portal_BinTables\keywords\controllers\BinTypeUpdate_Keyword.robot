*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Documentation              Bin Tables Update Bin Type Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py
Library                                            ../../keywords/controllers/resources/bintypes/BinTypeUpdate.py

#***********************************PROJECT RESOURCES***************************************
Resource                                           ../../keywords/controllers/common/GenericMethods.robot
Resource                                           ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***
${SQL_GET_UPDATED_BIN_TYPE_DETAILS}                SELECT * FROM BinDbs.BinTypes order by LastModifiedDate desc
${GLOBAL_BIN_TYPE_ID}
${GLOBAL_BIN_TYPE_DESCRIPTION}
${GLOBAL_BIN_TYPE_NAME}

*** Keywords ***

Get the Bin Type id using Bin Type Name
    [Arguments]     ${bintype_name}

    # Ensure the results are not empty
    ${bintype_name_is_not_empty}=     Run Keyword And Return Status       Should Not Be Empty    ${bintype_name}

    Run Keyword If    not ${bintype_name_is_not_empty}
    ...     Fail    Please provide the Bin Type name!

    ${db_results}=     Get the Bin Type details from the Database using the Bin Type Name    ${bintype_name}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database SQL for retrieving a bin type details did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}


    ${first_row_results}=           Get From List    ${db_results}    0    # Get the first row
    ${bin_type_id}=    Get From Dictionary    ${first_row_results}    Id
    ${bin_type_is_deleted}=    Get From Dictionary    ${first_row_results}    IsDeleted

    Run Keyword If    '${bin_type_is_deleted}' == '1'
    ...     Fail    The bin type named: '${bintype_name}' is deleted on the DB!

    RETURN   ${bin_type_id}


The User Populates the Update Bin Type JSON payload with
    [Arguments]     ${bintypeid}     ${name}    ${description}

    ${json_payload}=    Create a Update Bin Type Request   ${bintypeid}     ${name}    ${description}

    ${json_payload_is_created}=    Run Keyword And Return Status     Should Not Be Empty    ${json_payload}

    Run Keyword If    ${json_payload_is_created} == ${False}
    ...    Fail     The JSON payload for update bin type was not created!

    #Save the payload in a Global Variable
    #Set Global Variable    ${REST_PAYLOAD}        ${json_payload}

    Log Many    ${json_payload}


Create a Update Bin Type Request
    [Arguments]    ${bintypeid}     ${name}    ${description}

    ${json_result} =    Create Update Bin Type Request       ${bintypeid}     ${name}    ${description}

    Log Many        ${json_result}
    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal_BinTables/data/UpdateBinType.json
    Write JSON data To File    ${JSON_FILE}    ${json_result}

    RETURN    ${json_result}



The User sends the Update Bin Type API Request
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}


    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/UpdateBinType.json

    Log Many   ${payload}
    ${endpoint}=        Get Endpoint    ${base_url}
    ${method}=          Set Variable   PUT
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     payload=${payload}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'UpdateBinType' REST API did not return the expected status of '${EXPECTED_STATUS_CODE}', the returned status is '${status_code}'

The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}
    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
    IF    "${api_error_message_detail}" != "None"
         ${v2}=      Remove Quotes    ${api_error_message_detail}
    ELSE
         ${v2}=      Set Variable     ${api_error_message_detail}
    END


    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${v1}'     '${v2}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       BinNumber   binNumber   actionDate  binTypeIds  binUploadRequests

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'

             #Remove quotes from the strings
             ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
             IF    '${api_error_message_fields}' != 'None'
                 ${v2}=      Remove Quotes    ${api_error_message_fields}
             ELSE
                 ${v2}=      Set Variable     ${api_error_message_fields}
             END


             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${v1}' in '${v2}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Update' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.



Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}


Get Response Status Code
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

Get Updated Bin Type's ID
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_updated_bin_type_id
    RETURN    ${result}

Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keywords to read error response fields




#The below keywords are for Database Verifications

The updated Bin Type must exist in the database
    Log    Bin Type ID:${GLOBAL_BIN_TYPE_ID}
    Log    Bin Type Name:${GLOBAL_BIN_TYPE_NAME}
    Log    Bin Type Description:${GLOBAL_BIN_TYPE_DESCRIPTION}

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_UPDATED_BIN_TYPE_DETAILS}

    ${updated_bin_type_results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${updated_bin_type_results}

    ${Updated_Bin_Type_Entry_Database}=    Get From List    ${updated_bin_type_results}    0    
    ${DATABASE_UPDATED_BIN_TYPE_ID}=    Get From Dictionary    ${Updated_Bin_Type_Entry_Database}    Id
    ${DATABASE_BIN_TYPE_ID_DESCRIPTION}=    Get From Dictionary    ${Updated_Bin_Type_Entry_Database}    Description
    ${DATABASE_BIN_TYPE_ID_NAME}=    Get From Dictionary    ${Updated_Bin_Type_Entry_Database}    Name

    
    
    Should Be Equal    ${GLOBAL_BIN_TYPE_ID}    ${DATABASE_UPDATED_BIN_TYPE_ID}
    Should Be Equal    ${GLOBAL_BIN_TYPE_DESCRIPTION}          ${DATABASE_BIN_TYPE_ID_DESCRIPTION}
    Should Be Equal    ${GLOBAL_BIN_TYPE_NAME}          ${DATABASE_BIN_TYPE_ID_NAME}

    Log    The updated Bin Type exists in the Database with correct data...