*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Tests filtering

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Resource                                             LandingPage.robot
#***********************************PROJECT RESOURCES***************************************
*** Variables ***
${COMPLAINT_REF_TXT}                              xpath=//label[@id='MainContent_lblRefNo']                                                     
${REASON_TXT}                                     id=MainContent_lblReason
${COMMENT_TXT}                                    id=MainContent_lblComment
${CUSTOMER_NAME_TXT}                              id=MainContent_lblCustomerName
${CUSTOMER_SURNAME_TXT}                           id=MainContent_lblCustomerSurname
${CUSTOMER_CELLPHONE_TXT}                         id=MainContent_lblCustomerCellphone
${CUSTOMER_EMAIL_TXT}                             id=MainContent_lblCustomerEmail
${ATM_NUMBER_TXT}                                 id=MainContent_lblAtmNumber
${ATM_LOCATION_TXT}                               id=MainContent_lblAtmLocation
${CITY_TXT}                                       id=MainContent_lblCity
${REGION_TXT}                                     id=MainContent_lblProvince
${SERIAL_NUMBER_TXT}                              id=MainContent_lblSerialNo
${MODEL_TXT}                                      id=MainContent_lblModel
${ZONE_TXT}                                       id=MainContent_lblZone
${INSTITUTION_TXT}                                id=MainContent_lblInstitution
${COMPLAINT_HISTORY_TXT}                          xpath=//label[contains(text(),'Complaint History')]
${ASSIGNED_TO_TXT}                                id=MainContent_lblAssignedTo

${REASON_EXPECTED_VALUE}                            Card swallowed
${COMMENT_EXPECTED_VALUE}                           Test assignment to Free State
${CUSTOMER_NAME_EXPECTED_VALUE}                     Siviwe Thursday Week 1 Test 3
${CUSTOMER_SURNAME_EXPECTED_VALUE}                  Xhelo Thursday Week 1 Test 3
${CUSTOMER_CELLPHONE_EXPECTED_VALUE}                27715322169
${CUSTOMER_EMAIL_EXPECTED_VALUE}                    <EMAIL>
${ATM_NUMBER_EXPECTED_VALUE}                        S08029
${ATM_LOCATION_EXPECTED_VALUE}                      BLOEMFONTEIN BRANCH 2, Maitland Street
${CITY_EXPECTED_VALUE}                              Bloemfontein
${REGION_EXPECTED_VALUE}                            Free State
${SERIAL_NUMBER_EXPECTED_VALUE}                     45459270
${MODEL_EXPECTED_VALUE}                             NCR 6634 Recycling
${ZONE_EXPECTED_VALUE}                              Branch ATM Serviced by SBV
${INSTITUTION_EXPECTED_VALUE}                       SBV
*** Keywords ***
User filter by region
    [Arguments]  ${REGION}
    Log to console  --------------------------Filtering by region
    
    Wait Until Page Contains    QR Code Complaints and Compliments

    perform dropdown select  ${REGION_DROPDOWN}  ${REGION}
 
    Wait Until Element Is Enabled     id=btnDetails

    User clciks on details link
    
    Wait Until Element Is Visible    id=detailTitle

    User views complaint/compliment region  ${REGION}

User filter by status
    [Arguments]  ${STATUS}
    Log to console  --------------------------Filtering by status

    Wait Until Page Contains    QR Code Complaints and Compliments

    perform dropdown select  ${STATUS2_DROPDOWN}  ${STATUS}

    Wait Until Element Is Enabled     id=btnDetails

    User clciks on details link
    
    Wait Until Element Is Visible    id=detailTitle

    User views complaint/compliment status  ${STATUS}
    

User views complaint/compliment region
    [Arguments]  ${REGION}

    User validates that the task shown belongs to the selected region  ${REGION}

User views complaint/compliment status
    [Arguments]  ${STATUS}
    
    User validates that the task shown belongs to the selected status  ${STATUS}    

User validates that the task shown belongs to the selected region
    [Arguments]  ${REGION}

    Page Should Contain    ${REGION}


User validates that the task shown belongs to the selected status
    [Arguments]  ${STATUS}
    Page Should Contain    ${STATUS}

User validates compliant data on Details - ATM Complaints and Compliments screen
    [Arguments]  ${REFERENCE}
    
    Wait Until Element Is Enabled    id=detailTitle

    Sleep    5s

    ${reference_actual_value}=                      GET TEXT  ${COMPLAINT_REF_TXT}
     
    Should Be Equal    ${REFERENCE}    ${reference_actual_value}
    
    ${reason_actual_value}=                         GET TEXT  ${REASON_TXT} 
    
    Should Be Equal    ${reason_actual_value}    ${REASON_EXPECTED_VALUE}

    ${comment_actual_value}=                        GET TEXT  ${COMMENT_TXT} 
    
    Should Be Equal    ${comment_actual_value}    ${COMMENT_EXPECTED_VALUE}
    
    ${customer_name_actual_value}=                  GET TEXT  ${CUSTOMER_NAME_TXT} 

    Should Be Equal    ${customer_name_actual_value}    ${CUSTOMER_NAME_EXPECTED_VALUE}

    ${customer_surname_actual_value}=               GET TEXT  ${CUSTOMER_SURNAME_TXT} 

    Should Be Equal    ${customer_surname_actual_value}    ${CUSTOMER_SURNAME_EXPECTED_VALUE}

    ${customer_email_actual_value}=                 GET TEXT  ${CUSTOMER_EMAIL_TXT} 

    Should Be Equal    ${customer_email_actual_value}    ${CUSTOMER_EMAIL_EXPECTED_VALUE}

    ${atm_number_actual_value}=                     GET TEXT  ${ATM_NUMBER_TXT} 

    Should Be Equal    ${atm_number_actual_value}    ${ATM_NUMBER_EXPECTED_VALUE}

    ${atm_location_actual_value}=                   GET TEXT  ${ATM_LOCATION_TXT} 

    Should Be Equal    ${atm_location_actual_value}    ${ATM_LOCATION_EXPECTED_VALUE}

    ${city_actual_value}=                           GET TEXT  ${CITY_TXT} 

    Should Be Equal    ${city_actual_value}    ${CITY_EXPECTED_VALUE}

    ${region_actual_value}=                         GET TEXT  ${REGION_TXT} 

    Should Be Equal    ${region_actual_value}    ${REGION_EXPECTED_VALUE}

    ${serial_number_actual_value}=                  GET TEXT  ${SERIAL_NUMBER_TXT} 

    Should Be Equal    ${serial_number_actual_value}    ${SERIAL_NUMBER_EXPECTED_VALUE}

    ${model_actual_value}=                          GET TEXT  ${MODEL_TXT} 

    Should Be Equal    ${model_actual_value}    ${MODEL_EXPECTED_VALUE}

    ${zone_actual_value}=                           GET TEXT  ${ZONE_TXT} 

    Should Be Equal    ${zone_actual_value}    ${ZONE_EXPECTED_VALUE}

    ${institution_actual_value}=                    GET TEXT  ${INSTITUTION_TXT} 

    Should Be Equal    ${institution_actual_value}    ${INSTITUTION_EXPECTED_VALUE}

# ${COMMENT_TXT}                                    id=MainContent_lblComment
# ${CUSTOMER_NAME_TXT}                              id=MainContent_lblCustomerName
# ${CUSTOMER_SURNAME_TXT}                           id=MainContent_lblCustomerSurname
# ${CUSTOMER_CELLPHONE_TXT}                         id=MainContent_lblCustomerCellphone
# ${CUSTOMER_EMAIL_TXT}                             id=MainContent_lblCustomerEmail
# ${ATM_NUMBER_TXT}                                 id=MainContent_lblAtmNumber
# ${ATM_LOCATION_TXT}                               id=MainContent_lblAtmLocation
# ${CITY_TXT}                                       id=MainContent_lblCity
# ${REGION_TXT}                                     id=MainContent_lblProvince
# ${SERIAL_NUMBER_TXT}                              id=MainContent_lblSerialNo
# ${MODEL_TXT}                                      id=MainContent_lblModel
# ${ZONE_TXT}                                       id=MainContent_lblZone
# ${INSTITUTION_TXT}                                id=MainContent_lblInstitution
# ${COMPLAINT_HISTORY_TXT}                          xpath=//label[contains(text(),'Complaint History')]
# ${ASSIGNED_TO_TXT