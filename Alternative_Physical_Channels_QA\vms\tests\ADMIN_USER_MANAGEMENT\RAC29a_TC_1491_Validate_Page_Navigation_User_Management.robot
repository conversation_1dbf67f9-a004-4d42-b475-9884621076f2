*** Settings ***
#Author Name               : TH<PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS - Validate Rows Per Page Filter
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${ROWS_TO_SELECT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And The user clicks the 'Next' button then next page must be displayed



| *Test Case*                                                                                                      |   *DOCUMENTATION*                                                       | *TEST_ENVIRONMENT*  |  *ROWS_TO_SELECT*    |
| Validate Page Navigation- User Management when results are filtered to 5 user per page          | VMS - Validate Rows Per Page Filter | Filter nummber of rows to be displayed per page    |    VMS_UAT          |        5             |
