import mysql.connector
import mysql
import pyodbc


def execute_select_statement_old_version(db_type, host, database, username, password, query):
    """
    Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
    Raises an error if the query fails.

    :param db_type: Database type, either 'mysql' or 'mssql'.
    :param host: Host for the database.
    :param database: Database name.
    :param username: Database username.
    :param password: Database password.
    :param query: The SELECT query to execute.
    :return: A list of dictionaries containing the query results.
    :raises: Exception if the query fails.
    """

    print(db_type, host, database, username, password)
    try:
        if db_type == 'mysql' or db_type == 'MYSQL':
            # MySQL connection
            conn = mysql.connector.connect(
                host=host,
                database=database,
                user=username,
                password=password
            )
        elif db_type == 'mssql' or db_type == 'MSSQL':
            # MSSQL connection
            conn = pyodbc.connect(
                'DRIVER={SQL Server};'
                f'SERVER={host};'
                f'DATABASE={database};'
                f'UID={username};'
                f'PWD={password}'
            )

        else:
            raise ValueError(f"Unsupported database type: {db_type}")

        # Create a cursor object to interact with the database
        cursor = conn.cursor()

        # Execute the SELECT query
        cursor.execute(query)

        # Fetch all rows of the result
        results = cursor.fetchall()

        # Convert each row into a dictionary using column names
        column_names = [column[0] for column in cursor.description]  # Get column names
        results_as_dict = [dict(zip(column_names, row)) for row in results]

        # Close the cursor and connection
        cursor.close()
        conn.close()

        return results_as_dict

    except Exception as e:
        raise Exception(f"Error executing query: {str(e)}")


def execute_select_statement(db_type, host, database, username, password, query):
    """
    Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
    Raises an error if the query fails.

    :param db_type: Database type, either 'mysql' or 'mssql'.
    :param host: Host for the database.
    :param database: Database name.
    :param username: Database username.
    :param password: Database password.
    :param query: The SELECT query to execute.
    :return: A list of dictionaries containing the query results.
    :raises: Exception if the query fails.
    """

    print(db_type, host, database, username, password)
    try:
        if db_type.lower() == 'mysql':
            # MySQL connection
            conn = mysql.connector.connect(
                host=host,
                database=database,
                user=username,
                password=password
            )
        elif db_type.lower() == 'mssql':
            # MSSQL connection
            conn = pyodbc.connect(
                'DRIVER={SQL Server};'
                f'SERVER={host};'
                f'DATABASE={database};'
                f'UID={username};'
                f'PWD={password}'
            )
        else:
            raise ValueError(f"Unsupported database type: {db_type}")

        cursor = conn.cursor()

        # Debugging: Check if the connection is open
        print("Connection established, executing query.")

        # Execute the SELECT query
        cursor.execute(query)

        # Check if the cursor description is available
        if cursor.description is None:
            raise ValueError("No result set returned from the query.")

        results = cursor.fetchall()

        # Check for None or empty results
        if results is None:
            raise ValueError("Query returned None, which is not iterable.")

        # Convert each row into a dictionary using column names
        column_names = [column[0] for column in cursor.description]
        results_as_dict = [dict(zip(column_names, row)) for row in results] if results else []

        # Close the cursor and connection
        cursor.close()
        conn.close()

        return results_as_dict

    except Exception as e:
        raise Exception(f"Error executing query: {str(e)}")


def execute_select_statement_with_parameters(db_type, host, database, username, password, query, parameters):
    """
    Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
    Raises an error if the query fails.

    :param db_type: Database type, either 'mysql' or 'mssql'.
    :param host: Host for the database.
    :param database: Database name.
    :param username: Database username.
    :param password: Database password.
    :param query: The SELECT query to execute.
    :return: A list of dictionaries containing the query results.
    :raises: Exception if the query fails.
    """

    print(db_type, host, database, username, password)
    try:
        if db_type.lower() == 'mysql':
            # MySQL connection
            conn = mysql.connector.connect(
                host=host,
                database=database,
                user=username,
                password=password
            )
        elif db_type.lower() == 'mssql':
            # MSSQL connection
            conn = pyodbc.connect(
                'DRIVER={SQL Server};'
                f'SERVER={host};'
                f'DATABASE={database};'
                f'UID={username};'
                f'PWD={password}'
            )
        else:
            raise ValueError(f"Unsupported database type: {db_type}")

        cursor = conn.cursor()

        # Debugging: Check if the connection is open
        print("Connection established, executing query.")

        # Execute the SET statements separately
        # Split the string by comma
        split_list = parameters.split(',')

        # Loop through the split list
        for item in split_list:
            cursor.execute(item)
#        cursor.execute(parameter_one)
 #       cursor.execute(parameter_two)


        # Execute the SELECT query
        cursor.execute(query)

        # Check if the cursor description is available
        if cursor.description is None:
            raise ValueError("No result set returned from the query.")

        results = cursor.fetchall()

        # Check for None or empty results
        if results is None:
            raise ValueError("Query returned None, which is not iterable.")

        # Convert each row into a dictionary using column names
        column_names = [column[0] for column in cursor.description]
        results_as_dict = [dict(zip(column_names, row)) for row in results] if results else []

        # Close the cursor and connection
        cursor.close()
        conn.close()

        return results_as_dict

    except Exception as e:
        raise Exception(f"Error executing query: {str(e)}")
