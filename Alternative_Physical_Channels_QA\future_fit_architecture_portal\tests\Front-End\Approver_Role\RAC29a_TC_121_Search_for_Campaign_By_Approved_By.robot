*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing the Search Funtion on Campaign Approvals: Search by Approved By user

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../common_utilities/Login.robot
Resource                                            ../../../../common_utilities/Logout.robot
Resource                                            ../../../Keywords/atm_marketing/Dashboard.robot
Resource                                            ../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/atm_marketing/NegativeScenarios.robot
Resource                                            ../../../keywords/common/Navigation.robot
Resource                                            ../../../keywords/atm_marketing/Approvals.robot

*** Keyword ***
Validating the Search function on Campaign Approvals
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    When The user navigates to the Campaign Approvals page   

    And The user inputs an Approved By user on the Search field

    Then The user verifies the search results returned by the Approved By user
    
| *Test Cases*                                                                                                             |      *DOCUMENTATION*          |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   | 
| RAC29a_TC_121_FFT_Approval_Search_for_Campaign_By_Approved_By   |  Validating the Search function on Campaign Approvals  |  Search by Approved By user   |    BUSINESS_APPROVER       |     APC_UAT             |