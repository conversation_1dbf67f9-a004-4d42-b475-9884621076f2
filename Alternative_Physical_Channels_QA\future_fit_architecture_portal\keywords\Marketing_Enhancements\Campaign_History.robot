*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Documentation  VMS Dashboard Validation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                             String
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../../utility/DatabaseUtility.py
Resource                                             ../../keywords/common/DBUtility.robot
Library                                             XML
Library                                             Collections


#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${SEARCH_BOX}       xpath=//input
${CAMPAIGN_HISTORY_PAGE_BUTTON}       xpath=//span[contains(text(), 'Campaign History')]
${SEARCH_RETURN}    xpath=//*[contains(text(), '${SEARCH_BOX}')]
${COLUMN_HEADER_XPATH}      xpath=//th
${TABLE_ROWS}      xpath=//tbody//tr

*** Keywords ***
The user navigates to the Campaign History page
    Wait Until Element Is Visible    ${CAMPAIGN_HISTORY_PAGE_BUTTON}
    Click Element    ${CAMPAIGN_HISTORY_PAGE_BUTTON}
    Log to console      --------------------------The user has clicked Campaign History Page button
    Capture Page Screenshot

The user inputs search criteria
    [Arguments]     ${SEARCH_CRITERIA}
    Wait Until Element Is Visible    ${SEARCH_BOX}
    Click Element    ${SEARCH_BOX}
    Input Text    ${SEARCH_BOX}    ${SEARCH_CRITERIA}
    Capture Page Screenshot

The user verifies search criteria is met
    ${search_return_list}=  Get WebElements    ${SEARCH_RETURN}
    ${search_list_length}=  Get Length    ${search_return_list}
    IF    '${search_list_length}' == 0
         Log To Console    ------------------Search Key has not been found in table
         Fail      ------------------Returned list length of searck key is zero
    ELSE
        Log To Console    ------------------Search Key has been found in table
    END

The user verifies can column can sort by descending or ascedning
    [Arguments]     ${COLUMN}
    Wait Until Element Is Visible   ${COLUMN_HEADER_XPATH}//div[contains(text(), '${COLUMN}')]
    Click Element    ${COLUMN_HEADER_XPATH}//div[contains(text(), '${COLUMN}')]
    ${header_list}=     Get WebElements    ${COLUMN_HEADER_XPATH}
    ${header_list_length}=      Get Length    ${header_list}
    ${column_index}=    Set Variable    0
    FOR    ${counter}    IN RANGE    0    ${header_list_length}    1
        ${column_index}=    Evaluate    ${counter} + 1
        ${column_value}=     Get Text    ${COLUMN_HEADER_XPATH}//div[contains(text(), '${COLUMN}')]
        IF   '${column_value}' == '${COLUMN}'
           Log To Console    Index for Column ${COLUMN} is ${column_index}
           Exit For Loop
        END
    END
    ${column_list}=     Get WebElements    xpath=//tr//td[${column_index}]
    ${column_texts}=    Get Text From Elements      ${column_list}
    ${sorted_texts}=    Evaluate    sorted(${column_texts})

     # Check if the original list matches the sorted list (ascending order)
    Run Keyword If    ${column_texts} == ${sorted_texts}    Log To Console   Column is sorted in ascending order.

    # Check if the original list matches the reverse sorted list (descending order)
    ${reverse_sorted_texts}=   Evaluate    sorted(${column_texts}, reverse=True)
    Run Keyword If    ${column_texts} == ${reverse_sorted_texts}    Log To Console    Column is sorted in descending order.

Get Text From Elements
    [Arguments]    ${elements}
    ${text_list}=   Create List
    FOR    ${element}    IN    @{elements}
        ${text}=    Get Text    ${element}
        Append To List    ${text_list}    ${text}
    END
    [Return]    ${text_list}

The user verifies pagination
    [Arguments]    ${NUM_PAGES}
    Wait Until Element Is Visible    xpath=//div[contains(@class, 'mat-select-trigger ng-tns-c50-11')]
    Click Element    xpath=//div[contains(@class, 'mat-select-trigger ng-tns-c50-11')]
    IF    '${NUM_PAGES}' == '10'
        Wait Until Element Is Visible    xpath=//mat-option[2]//span
        Click Element    xpath=//mat-option[2]//span
    ELSE
        Wait Until Element Is Visible    xpath=//span[contains(text(), '${NUM_PAGES}')]
        Click Element    xpath=//span[contains(text(), '${NUM_PAGES}')]
    END
    ${row_list}=     Get WebElements    ${TABLE_ROWS}
    ${row_list_length}=      Get Length    ${row_list}
    IF     '${row_list_length}' == '${NUM_PAGES}'
        Capture Page Screenshot
        Log To Console    Number of rows returned [${row_list_length}] is equals to the number of rows selected [${NUM_PAGES}]
    ELSE
        Capture Page Screenshot
        Log To Console    Number of rows returned [${row_list_length}] is not equals to the number of rows selected [${NUM_PAGES}],
        Fail    Please check you have enough data before confirming fail
    END
