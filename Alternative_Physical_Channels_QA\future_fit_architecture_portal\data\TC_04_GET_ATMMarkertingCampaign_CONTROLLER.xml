<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240827 10:55:39.769">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\ATMMarketingResults\TC_09_POST_ATMMarketingResults_CONTROLLER.robot">
      <test name="FFT - Controllers - Post ATM Marketing Result and verify the response against the APC Database" id="s1-t1">
         <kw library="Selenium" name="Given The user prepares a json payload">
            <doc>Post ATM Marketing Result DB Verifications</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240827 10:55:42.201" level="INFO">Given The user prepares a json payload</msg>
            <status endtime="20240827 10:55:42.364" status="PASS" starttime="20240827 10:55:42.201"/>
         </kw>
         <kw library="Selenium" name="When The user creates a rest session">
            <doc>Post ATM Marketing Result DB Verifications</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240827 10:55:42.364" level="INFO">When The user creates a rest session</msg>
            <status endtime="20240827 10:55:42.638" status="PASS" starttime="20240827 10:55:42.364"/>
         </kw>
         <kw library="Selenium" name="And The user makes Post Rest Call">
            <doc>Post ATM Marketing Result DB Verifications</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240827 10:55:42.638" level="INFO">And The user makes Post Rest Call</msg>
            <status endtime="20240827 10:55:44.303" status="PASS" starttime="20240827 10:55:42.638"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Post ATM Marketing Result DB Verifications</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240827 10:55:44.305" level="INFO">And The service returns http status</msg>
            <status endtime="20240827 10:55:44.306" status="PASS" starttime="20240827 10:55:44.305"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) returned by the POST ATMMarketing Results controller must correspond to the APC Database">
            <doc>Post ATM Marketing Result DB Verifications</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240827 10:55:44.307" level="INFO">Then The field value(s) returned by the POST ATMMarketing Results controller must correspond to the APC Database</msg>
            <status endtime="20240827 10:55:44.766" status="PASS" starttime="20240827 10:55:44.307"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Post ATM Marketing Result and verify the response against the APC Database</tag>
         </tags>
         <status endtime="20240827 10:55:44.766" critical="yes" status="PASS" starttime="20240827 10:55:41.577"/>
      </test>
      <status endtime="20240827 10:55:44.767" status="PASS" starttime="20240827 10:55:39.769"/>
   </suite>
   <statistics>
      <total>
         <stat pass="1" fail="0">Critical Tests</stat>
         <stat pass="1" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">FFT - Controllers - Post ATM Marketing Result and verify the response against the APC Database</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="1" fail="0" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
