# Alternative_Physical_Channels_QA

- Future Fit Execution

      robot -d reports/ --variable ROBOT_FILE_PATH:"data\POST_CAMPAIGN_REGRESSION.xml" --variable BASE_URL:APC_DEV --variable BROWSER:edge --variable UPLOAD_TEST_STEPS:Yes --variable IS_HEADLESS_BROWSER:No -N "Future Fit Portal" --listener future_fit_architecture_portal\utility\PostExecutionUpdateV2.py future_fit_architecture_portal/tests/Front-End/TC_07_DASHBOARD.robot
  
  1. The test name file must be changed to correspond to the test file that must be executed.
  2. The variable 'BASE_URL' must also be changed depending on the test environment that is being used.



- Bin Tables Controllers Execution

      robot -d future_fit_architecture_portal_BinTables/Results/Reactivate_Bins_Negative --variable ROBOT_FILE_PATH:"data/POST_CAMPAIGN_REGRESSION.xml" --variable BASE_URL:BIN_TABLES_API_DEV_BASE_URL --variable BROWSER:chrome --variable UPLOAD_TEST_STEPS:Yes --variable IS_HEADLESS_BROWSER:No -N "Bin Tables Controllers" future_fit_architecture_portal_BinTables/tests/Controllers/Bins/Reactivate_Bins_Negative.robot

  1. The test name file must be changed to correspond to the test file that must be executed.
  2. The variable 'BASE_URL' must also be changed depending on the test environment that is being used.


-  VMS Execution

       - robot -d vms/Results/User_Management --variable ROBOT_FILE_PATH:"C:\Users\<USER>\source\repos\alternative_physical_channels\vms\data\User_Management.xml" --variable BROWSER:chrome --variable UPLOAD_TEST_STEPS:Yes --variable IS_HEADLESS_BROWSER:No --variable APPLICATION_USERNAME:"AB032TO" --variable APPLICATION_PASSWORD:"yourpassword" -i "VMS HEALTHCHECK"  -N "VMS Portal" --listener vms\utility\PostExecutionUpdateV2.py  'vms/tests/ADMIN_USER_MANAGEMENT/RAC29a_TC_342_Validate_Add_New_User_User_Management.robot'

  1. The test name file must be changed to correspond to the test file that must be executed.
