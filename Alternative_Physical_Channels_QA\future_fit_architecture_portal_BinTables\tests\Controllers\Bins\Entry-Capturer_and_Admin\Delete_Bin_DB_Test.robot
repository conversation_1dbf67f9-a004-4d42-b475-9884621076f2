*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/DeleteBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin




*** Keywords ***
Delete Database Bin
    [Arguments]        ${DOCUMENTATION}    ${BIN_REVIEW_STATUS}     ${EXPECTED_IS_DELETED_STATUS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user gets a bin that is not deleted from the database     ${BIN_REVIEW_STATUS}
    When The user deletes the Bin retrieved from the database using the Delete Bin Controller
    Then The the BIN's isDeleted status must be the same as the expected status     ${EMPTY}    ${EXPECTED_IS_DELETED_STATUS}

| *** Test Cases ***                                                               |                   *DOCUMENTATION*                  |       *BIN_REVIEW_STATUS*   |      *EXPECTED_IS_DELETED_STATUS*    |
| Delete the Bin that has a review status of 'Rejected'.   | Delete Database Bin   | Reject all pending Bins that are in the database   |           Rejected          |               1                      |
| Delete the Bin that has a review status of 'Approved'.   | Delete Database Bin   | Reject all pending Bins that are in the database   |           Approved          |               1                      |
| Delete the Bin that has a review status of 'Pending'.    | Delete Database Bin   | Reject all pending Bins that are in the database   |           Pending           |               1                      |
