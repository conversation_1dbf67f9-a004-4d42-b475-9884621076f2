﻿TEST_CASE_NAME,BASE_URL,SERVICE_PATH,HEADERS,CERT_URL,CERT_PASSWORD,PAYLOAD,REQUEST_METHOD
ATM CC - Validate data on VBMS,https://vmsatmapi.vms-atmcc-sit.rbb-banking.sdc-nonprod.caas.absa.co.za,/atmcomplaints,Accept==application/json,cert/SOAPDEV.p12,Password1,"{
    ""ComplaintDate"" : ""2022-06-02T06:05:20.917"",
    ""CustomerName"" : ""Siviwe Thursday Week 1 Test 3"",
    ""CustomerSurname"": ""Xhelo Thursday Week 1 Test 3"",
    ""CustomerCellphone"": ""***********"",
    ""CustomerEmail"": ""<EMAIL>"",
    ""AtmNumber"": ""S08029"",
    ""AtmLocation"": ""BLOEMFONTEIN BRANCH 2, Maitland Street"",
    ""isComplaint"": true,
    ""Reason"": ""Card swallowed"",
    ""Comments"": ""Test assignment to Free State""
}",REST
TC_CC_COMPLIMENT_POST_API,https://vmsatmapi.vms-atmcc-sit.rbb-banking.sdc-nonprod.caas.absa.co.za,/atmcomplaints,Accept==application/json,cert/SOAPDEV.p12,Password1,"{
    ""ComplaintDate"" : ""2022-06-02T06:05:20.917"",
    ""CustomerName"" : ""Siviwe Thursday Week 1 Test 3"",
    ""CustomerSurname"": ""Xhelo Thursday Week 1 Test 3"",
    ""CustomerCellphone"": ""***********"",
    ""CustomerEmail"": ""<EMAIL>"",
    ""AtmNumber"": ""S08029"",
    ""AtmLocation"": ""BLOEMFONTEIN BRANCH 2, Maitland Street"",
    ""isComplaint"": false,
    ""Reason"": ""Card swallowed"",
    ""Comments"": ""Test assignment to Free State""
}",
TC_CC_GET_ALL_CC_API,https://vmsatmapi.vms-atmcc-sit.rbb-banking.sdc-nonprod.caas.absa.co.za,/atmcomplaints,Accept==application/json,cert/SOAPDEV.p12,Password1,,
WQCASAScreenPrimaryClientV7, https://esb.was.uat.absa.co.za:10442/ecasa.uat.CSA/WQCASAScreenPrimaryClientV7,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"  <soapenv:Envelope xmlns:sch=""http://WQ.absa.co.za/WQCASAScreenPrimaryClientV6/schema"" xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"">
        <soapenv:Header/>
        <soapenv:Body>
            <WQCASAScreenPrimaryClientV7 xmlns=""http://WQ.absa.co.za/WQCASAScreenPrimaryClientV7/schema"">
                <nbsapdpi xmlns="""">
                    <channel>SSD</channel>
                    <application>SSD</application>
                    <trace>Y</trace>
                </nbsapdpi>
                <sar303i xmlns="""">
                    <firstName>KAMOGELO</firstName>
                    <surnameComp>MOHLALA</surnameComp>
                    <idType>01</idType>
                    <idRegNo>9604186283081</idRegNo>
                    <clientTypeGrp>I</clientTypeGrp>
                    <dob>19960418</dob>
                    <nationality>SOU01</nationality>
                    <town>BOKSBURG</town>
                    <territory/>
                    <cntryOfRes>SO003</cntryOfRes>
                    <cntryOfBirth>SO003</cntryOfBirth>
                    <registerCity>BOKSBURG</registerCity>
                    <countryPassport/>
                    <headofficeTown/>
                    <headofficeCntry/>
                    <headofficeOtherCntry1/>
                    <headofficeOtherCntry2/>
                    <headofficeOtherCntry3/>
                    <headofficeOtherCntry4/>
                    <headofficeOtherCntry5/>
                    <sbu>F</sbu>
                    <originatingSys>SSD</originatingSys>
                    <branch>01213</branch>
                    <teller>91090</teller>
                </sar303i>
            </WQCASAScreenPrimaryClientV7>
        </soapenv:Body>
        </soapenv:Envelope>",REST
SVcreateLinkSavingsNoticeDepAccountV6, https://esb.host.uat.absa.co.za:29882/services/SVcreateLinkSavingsNoticeDepAccountV6,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"<Envelope xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"" xmlns=""http://schemas.xmlsoap.org/soap/envelope/"">
        <Header />
        <Body>
            <SVcreateLinkSavingsNoticeDepAccountV6 xmlns=""http://SV.absa.co.za/SVcreateLinkSavingsNoticeDepAccountV6/schema"">
                <nbsapdpi xmlns="""">
                    <channel>BDP</channel>
                    <application>e2e</application>
                    <trace>y</trace>
                </nbsapdpi>
                <nbsmsgi xmlns="""">
                    <msgLanguage>E</msgLanguage>
                    <msgTarget>STD</msgTarget>
                    <finalMsgId>
                        <finalMsgClass />
                        <finalMsgCode />
                    </finalMsgId>
                </nbsmsgi>
                <svpp15i xmlns="""">
                    <clientCode>CHOENTL001</clientCode>
                    <acctSiteCode>8198</acctSiteCode>
                    <acctSiteType>034</acctSiteType>
                    <tranSiteCode>8198</tranSiteCode>
                    <tranSiteType>034</tranSiteType>
                    <brandCode>ABS</brandCode>
                    <productCode>9082</productCode>
                    <versionNbr>0</versionNbr>
                    <effectiveDate>20220622</effectiveDate>
                    <marketersCode>0</marketersCode>
                    <agencyCode>0</agencyCode>
                    <tellerCode>12345</tellerCode>
                    <supervisorCode>22222</supervisorCode>
                    <noticePeriod>0</noticePeriod>
                    <withdrwlPerc>0</withdrwlPerc>
                    <channelInd>I</channelInd>
                    <srcOfFnds1>20</srcOfFnds1>
                    <srcOfFnds2 />
                    <srcOfFnds3 />
                    <srcOfFnds4 />
                    <srcOfFnds5 />
                    <investAmount>0</investAmount>
                    <investTerm>0</investTerm>
                    <rateOption />
                    <crpCode />
                    <freqCode>0</freqCode>
                    <rbaEddRating>M</rbaEddRating>
                </svpp15i>
            </SVcreateLinkSavingsNoticeDepAccountV6>
        </Body>
    </Envelope>",
WQriskProfileClientV7 - Risk Profile - M,https://esb.was.uat.absa.co.za:10442/ecasa.uat/ws/WQriskProfileClientV7,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://WQ.absa.co.za/WQriskProfileClientV7/schema"">
   <soapenv:Header/>
   <soapenv:Body>
      <sch:WQriskProfileClientV7Request>
         <customerTypeCode/>
         <customerStatusCode/>
         <customerSourceUniqueId>*********</customerSourceUniqueId>
         <companyForm>101</companyForm>
         <customerSourceRefID/>
         <primeBranchID>9960</primeBranchID>
         <channel>F2F1</channel>
         <sbu>F</sbu>
         <originatingsystem>ESP</originatingsystem>
         <employmentStatus>1</employmentStatus>
         <occupation>4</occupation>
         <businessSegment1/>
         <cifkey/>
         <absaSourceOfIncomeTable>
            <absaSourceOfIncome>20</absaSourceOfIncome>
         </absaSourceOfIncomeTable>
         <productCodeTable>
            <productCode>16</productCode>
         </productCodeTable>
         <subProductCodeTable>
            <subProductCode>16</subProductCode>
         </subProductCodeTable>
         <userId>EXSC185</userId>
      </sch:WQriskProfileClientV7Request>
   </soapenv:Body>
</soapenv:Envelope>",
WQriskProfileClientV7 - Risk Profile - H,https://esb.was.uat.absa.co.za:10442/ecasa.uat/ws/WQriskProfileClientV7,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://WQ.absa.co.za/WQriskProfileClientV7/schema"">
   <soapenv:Header/>
   <soapenv:Body>
      <sch:WQriskProfileClientV7Request>
         <customerTypeCode/>
         <customerStatusCode/>
         <customerSourceUniqueId>*********</customerSourceUniqueId>
         <companyForm>101</companyForm>
         <customerSourceRefID/>
         <primeBranchID>9960</primeBranchID>
         <channel>F2F1</channel>
         <sbu>F</sbu>
         <originatingsystem>ESP</originatingsystem>
         <employmentStatus>1</employmentStatus>
         <occupation>4</occupation>
         <businessSegment1/>
         <cifkey/>
         <absaSourceOfIncomeTable>
            <absaSourceOfIncome>20</absaSourceOfIncome>
         </absaSourceOfIncomeTable>
         <productCodeTable>
            <productCode>16</productCode>
         </productCodeTable>
         <subProductCodeTable>
            <subProductCode>16</subProductCode>
         </subProductCodeTable>
         <userId>EXSC185</userId>
      </sch:WQriskProfileClientV7Request>
   </soapenv:Body>
</soapenv:Envelope>",
SVupdateAccountOpeningDetailsV2,https://esb.host.uat.absa.co.za:29882/services/SVupdateAccountOpeningDetailsV2,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"<soapenv:Envelope xmlns:sch=""http://SV.absa.co.za/SVupdateAccountOpeningDetailsV2/schema"" xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"">
   <soapenv:Header/>
   <soapenv:Body>
      <sch:SVupdateAccountOpeningDetailsV2>
         <nbsapdpi>
            <channel>bdp</channel>
            <application>E2E</application>
            <trace>y</trace>
         </nbsapdpi>
         <nbsmsgi>
            <msgLanguage>E</msgLanguage>
            <msgTarget>STD</msgTarget>
            <finalMsgId>
               <finalMsgClass/>
               <finalMsgCode/>
            </finalMsgId>
         </nbsmsgi>
         <svpp09i>
            <accountNbr>**********</accountNbr>
            <savingsDepositType>S</savingsDepositType>
            <openReasonCode>01</openReasonCode>
            <accName>Testing</accName>
            <groupSchCd>00</groupSchCd>
            <homeEmpPlCd/>
            <grantType>0</grantType>
            <spAccType>N</spAccType>
            <brokerSrc/>
            <brokerNbr/>
            <crpCode/>
            <divisionCode>ABS</divisionCode>
            <workStation>99</workStation>
            <branchCode>08392</branchCode>
            <agencyCode>00000</agencyCode>
            <agencySiteType>000</agencySiteType>
            <tellerCode>09513</tellerCode>
            <supervisorCode>22222</supervisorCode>
            <branchSiteType>034</branchSiteType>
         </svpp09i>
      </sch:SVupdateAccountOpeningDetailsV2>
   </soapenv:Body>
</soapenv:Envelope>",
WQconfirmDocumentReceiptV2,https://esb.host.uat.absa.co.za:29882/services/WQconfirmDocumentReceiptV2,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"<soapenv:Envelope xmlns:sch=""http://WQ.absa.co.za/WQconfirmDocumentReceiptV2/schema"" xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"">
   <soapenv:Header/>
   <soapenv:Body>
      <sch:WQconfirmDocumentReceiptV2>
         <nbsapdpi>
            <channel>SSD</channel>
            <application>SSD</application>
            <trace>N</trace>
         </nbsapdpi>
         <wqp131i>
            <refNo>*********</refNo>
            <version>1</version>
            <branch>01213</branch>
            <user>91090</user>
            <docNo>21</docNo>
            <requiredDocs>
               <docCode>000001</docCode>
               <received>Y</received>
               <inOrder>Y</inOrder>
               <scanYn>Y</scanYn>
               <scanRef>b7fc5d34-52c6-4403-b7d8-ccdcea18bb1c</scanRef>
            </requiredDocs>
            <requiredDocs>
               <docCode>000004</docCode>
               <received>Y</received>
               <inOrder>Y</inOrder>
               <scanYn>Y</scanYn>
               <scanRef>6627d2b7-b742-4345-89fd-e7ca057b051d</scanRef>
            </requiredDocs>
            <requiredDocs>
               <docCode>000053</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000054</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000055</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000058</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000059</docCode>
               <received>Y</received>
               <inOrder>Y</inOrder>
               <scanYn>Y</scanYn>
               <scanRef>cb260250-d77d-4463-9ec1-bc5b1d46ff51</scanRef>
            </requiredDocs>
            <requiredDocs>
               <docCode>000065</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000066</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000067</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000068</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000069</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000070</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000071</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000072</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000087</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000088</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000092</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000093</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000094</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
            <requiredDocs>
               <docCode>000095</docCode>
               <received>N</received>
               <inOrder>N</inOrder>
               <scanYn>N</scanYn>
               <scanRef/>
            </requiredDocs>
         </wqp131i>
      </sch:WQconfirmDocumentReceiptV2>
   </soapenv:Body>
</soapenv:Envelope>",
GenerateDocument,https://esb.was.uat.absa.co.za:10442/ecasa.uat.CSA/GenerateDocument,,"content-type==text/xml; charset=utf-8, tellerid==91090, X-IBM-Client-Id==08f12f78-d12d-42dc-a087-8059c170ec88, channel==SSD, X-API-Key==fb4199fd-6b4e-460b-bec6-9a146356502c, applicationID==ABSA ATM, X-IBM-Client-Secret==nF2bS1vM1nM1jB0rB6lH8gY7uO6vP5nX4aB4jM1qY4vN4sD0vY,userid==abmmck1,branch==01213,devicetype==22",cert/SOAPDEV.p12,Password1,"<soapenv:Envelope xmlns:sch=""http://SV.absa.co.za/SVupdateAccountOpeningDetailsV2/schema"" xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"">
   <soapenv:Header/>
   <soapenv:Body>
      <sch:SVupdateAccountOpeningDetailsV2>
         <nbsapdpi>
            <channel>bdp</channel>
            <application>E2E</application>
            <trace>y</trace>
         </nbsapdpi>
         <nbsmsgi>
            <msgLanguage>E</msgLanguage>
            <msgTarget>STD</msgTarget>
            <finalMsgId>
               <finalMsgClass/>
               <finalMsgCode/>
            </finalMsgId>
         </nbsmsgi>
         <svpp09i>
            <accountNbr>**********</accountNbr>
            <savingsDepositType>S</savingsDepositType>
            <openReasonCode>01</openReasonCode>
            <accName>Testing</accName>
            <groupSchCd>00</groupSchCd>
            <homeEmpPlCd/>
            <grantType>0</grantType>
            <spAccType>N</spAccType>
            <brokerSrc/>
            <brokerNbr/>
            <crpCode/>
            <divisionCode>ABS</divisionCode>
            <workStation>99</workStation>
            <branchCode>08392</branchCode>
            <agencyCode>00000</agencyCode>
            <agencySiteType>000</agencySiteType>
            <tellerCode>09513</tellerCode>
            <supervisorCode>22222</supervisorCode>
            <branchSiteType>034</branchSiteType>
         </svpp09i>
      </sch:SVupdateAccountOpeningDetailsV2>
   </soapenv:Body>
</soapenv:Envelope>",
