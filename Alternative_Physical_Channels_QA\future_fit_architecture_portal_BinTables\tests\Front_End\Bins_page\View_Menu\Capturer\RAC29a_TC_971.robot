*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-971




*** Keywords ***
Search for Bins on the View Entries page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                                            ${BASE_URL}
    #And The user access for granted for Bin Tables must correspond to the access of the logged in user              Approver
    Then The Bin Table landing page must be displayed

| *** Test Cases ***                                                                                                                           |        *DOCUMENTATION*    		            |         *BASE_URL*                 |
| Capturer_Verify Capturer Can Access BINTable Screen After Selecting the BINs Panel              | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.  |           ${EMPTY}                 |
