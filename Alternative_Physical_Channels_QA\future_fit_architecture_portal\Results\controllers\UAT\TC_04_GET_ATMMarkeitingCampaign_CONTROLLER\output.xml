<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 09:08:21.418" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\ATMMarketingCampaign\TC_04_GET_ATMMarkeitingCampaign_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:21.891" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 09:08:21.891" endtime="******** 09:08:21.891"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:21.891" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 09:08:21.891" endtime="******** 09:08:21.891"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:21.891" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 09:08:21.891" endtime="******** 09:08:21.891"/>
</kw>
<status status="PASS" starttime="******** 09:08:21.891" endtime="******** 09:08:21.891"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Get All Marketing Campaigns approvals using a Business User" line="44">
<kw name="GET marketing campaigns approvals">
<arg>Gets all Marketing Campaigns - Business User</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMMarketingCampaign</arg>
<arg>200</arg>
<arg>OK</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 09:08:21.908" level="INFO">Set test documentation to:
Gets all Marketing Campaigns - Business User</msg>
<status status="PASS" starttime="******** 09:08:21.908" endtime="******** 09:08:21.908"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:21.908" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 09:08:21.908" endtime="******** 09:08:21.908"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 09:08:21.908" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 09:08:21.908" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 09:08:21.908" endtime="******** 09:08:21.908"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 09:08:21.908" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 09:08:21.908" endtime="******** 09:08:22.098"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:08:22.098" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</kw>
<status status="PASS" starttime="******** 09:08:21.908" endtime="******** 09:08:22.098"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 09:08:22.098" level="INFO">${end_point} = /ATMMarketingCampaign/</msg>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</kw>
<status status="NOT RUN" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 09:08:22.098" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</kw>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</branch>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.098"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 09:08:22.734" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingCampaign/ 
 path_url=/ATMMarketingCampaign/ 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fdSR85jLUjiP_yOEBnrdpNgKaAA7PcnB5k4nfJz9joG07H0tJMAo-XE_Wm8aGAgam-zML3jfZSNhcJL_Iw7-atPyO1gvcENTHwKgXnbIq8Hm0C81CWjepOKKVI4vH4Kd0UdadvIjusYbqV8PkaqwRacysjteDM0uF09pgxy041d8hburt3oclCrYAYPa_Ji5yrZfXvPD_mxoGgqTCzONOF4i2ntRthTmlqEjvK0G_vZfCYbqPc5zZ1qQ70PpHqkZ-LxgClyvgwmZviiawi_9DrC8zDGSu_9tCbkxDwfICx0K4LhunT9fgzakjHCJMs5RwgZnlhXGBLXUmVtAl2GGeg'} 
 body=None 
 </msg>
<msg timestamp="******** 09:08:22.734" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingCampaign/ 
 status=200, reason=OK 
 headers={'Date': 'Thu, 30 May 2024 07:08:22 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=[{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"<EMAIL>","updatedDate":"2024-05-09T14:32:45.15697","updateDescription":"Campaign updated","approvalId":1314},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-11T00:45:00","campaignEndDate":"2024-03-12T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14381,"campaignId":"CNQ1328v001Q12024","campaignName":"ATM Targeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10224,"campaignId":14381,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:01:33.455028","updateDescription":"Campaign updated","approvalId":1315},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14382,"campaignId":"CNQ1329v001Q12024","campaignName":"Region Targeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10225,"campaignId":14382,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:29:35.679302","updateDescription":"Campaign updated","approvalId":1316},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14383,"campaignId":"CNQ1330v001Q12024","campaignName":"Untargeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10226,"campaignId":14383,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:40:43.301326","updateDescription":"Campaign updated","approvalId":1317},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14386,"campaignId":"CNQ1333v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10227,"campaignId":14386,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:52:32.249235","updateDescription":"Campaign updated","approvalId":1318},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-11T00:45:00","campaignEndDate":"2024-03-12T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14394,"campaignId":"CNQ1341v001Q12024","campaignName":"rrrr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10229,"campaignId":14394,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T13:50:28.366005","updateDescription":"Campaign updated","approvalId":1319},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14395,"campaignId":"CNQ1342v001Q12024","campaignName":"trerte","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10233,"campaignId":14395,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:52:23.089532","updateDescription":"Campaign updated","approvalId":1322},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T00:00:00","campaignEndDate":"2024-03-15T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14396,"campaignId":"CNQ1343v001Q12024","campaignName":"gregekjwj","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10234,"campaignId":14396,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:54:12.280723","updateDescription":"Campaign updated","approvalId":1323},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14397,"campaignId":"CNQ1344v001Q12024","campaignName":"ghgjhjgjh","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10235,"campaignId":14397,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:54:46.254781","updateDescription":"Campaign updated","approvalId":1324},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14398,"campaignId":"CNQ1345v001Q12024","campaignName":"cghcghcg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10236,"campaignId":14398,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:55:34.495285","updateDescription":"Campaign updated","approvalId":1325},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14399,"campaignId":"CNQ1346v001Q12024","campaignName":"fghfghfgh","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10239,"campaignId":14399,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-15T12:23:05.520235","updateDescription":"Campaign updated","approvalId":1327},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14400,"campaignId":"CNQ1347v001Q12024","campaignName":"hghgghg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10240,"campaignId":14400,"updatedBy":"SVC-marketing_ac_prd","updatedDate":"2024-05-15T12:35:13.320754","updateDescription":"Campaign updated","approvalId":1328},"updatedBy":"SVC-marketing_ac_prd","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14401,"campaignId":"CNQ1348v001Q12024","campaignName":"gfghfvghg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10269,"campaignId":14401,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T10:44:10.701422","updateDescription":"Campaign updated","approvalId":1333},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14402,"campaignId":"CNQ1349v001Q12024","campaignName":"Siv test campaign","campaignBy":"Siviwe Xhelo (ZA)","lastUpdate":{"id":10272,"campaignId":14402,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:05:29.075441","updateDescription":"Campaign updated","approvalId":1336},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-18T00:00:00","campaignEndDate":"2024-03-19T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14403,"campaignId":"CNQ1350v001Q12024","campaignName":"Siv test 8397","campaignBy":"Siviwe Xhelo (ZA)","lastUpdate":{"id":10270,"campaignId":14403,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T11:22:09.077279","updateDescription":"Campaign updated","approvalId":1334},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-21T00:00:00","campaignEndDate":"2024-03-22T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14404,"campaignId":"CNQ1351v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10237,"campaignId":14404,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-05-13T16:14:18.909556","updateDescription":"Camp... (set the log level to DEBUG or TRACE to see the full content) 
 </msg>
<msg timestamp="******** 09:08:22.735" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 09:08:22.738" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">${response.content} = [{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"Yaash.Ramsahaar@absa.a...</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">[{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"<EMAIL>","updatedDate":"2024-05-09T14:32:45.15697","updateDescription":"Campaign updated","approvalId":1314},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-11T00:45:00","campaignEndDate":"2024-03-12T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14381,"campaignId":"CNQ1328v001Q12024","campaignName":"ATM Targeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10224,"campaignId":14381,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:01:33.455028","updateDescription":"Campaign updated","approvalId":1315},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14382,"campaignId":"CNQ1329v001Q12024","campaignName":"Region Targeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10225,"campaignId":14382,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:29:35.679302","updateDescription":"Campaign updated","approvalId":1316},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14383,"campaignId":"CNQ1330v001Q12024","campaignName":"Untargeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10226,"campaignId":14383,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:40:43.301326","updateDescription":"Campaign updated","approvalId":1317},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14386,"campaignId":"CNQ1333v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10227,"campaignId":14386,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:52:32.249235","updateDescription":"Campaign updated","approvalId":1318},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-11T00:45:00","campaignEndDate":"2024-03-12T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14394,"campaignId":"CNQ1341v001Q12024","campaignName":"rrrr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10229,"campaignId":14394,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T13:50:28.366005","updateDescription":"Campaign updated","approvalId":1319},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14395,"campaignId":"CNQ1342v001Q12024","campaignName":"trerte","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10233,"campaignId":14395,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:52:23.089532","updateDescription":"Campaign updated","approvalId":1322},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T00:00:00","campaignEndDate":"2024-03-15T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14396,"campaignId":"CNQ1343v001Q12024","campaignName":"gregekjwj","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10234,"campaignId":14396,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:54:12.280723","updateDescription":"Campaign updated","approvalId":1323},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14397,"campaignId":"CNQ1344v001Q12024","campaignName":"ghgjhjgjh","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10235,"campaignId":14397,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:54:46.254781","updateDescription":"Campaign updated","approvalId":1324},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14398,"campaignId":"CNQ1345v001Q12024","campaignName":"cghcghcg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10236,"campaignId":14398,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:55:34.495285","updateDescription":"Campaign updated","approvalId":1325},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14399,"campaignId":"CNQ1346v001Q12024","campaignName":"fghfghfgh","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10239,"campaignId":14399,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-15T12:23:05.520235","updateDescription":"Campaign updated","approvalId":1327},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14400,"campaignId":"CNQ1347v001Q12024","campaignName":"hghgghg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10240,"campaignId":14400,"updatedBy":"SVC-marketing_ac_prd","updatedDate":"2024-05-15T12:35:13.320754","updateDescription":"Campaign updated","approvalId":1328},"updatedBy":"SVC-marketing_ac_prd","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14401,"campaignId":"CNQ1348v001Q12024","campaignName":"gfghfvghg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10269,"campaignId":14401,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T10:44:10.701422","updateDescription":"Campaign updated","approvalId":1333},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14402,"campaignId":"CNQ1349v001Q12024","campaignName":"Siv test campaign","campaignBy":"Siviwe Xhelo (ZA)","lastUpdate":{"id":10272,"campaignId":14402,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:05:29.075441","updateDescription":"Campaign updated","approvalId":1336},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-18T00:00:00","campaignEndDate":"2024-03-19T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14403,"campaignId":"CNQ1350v001Q12024","campaignName":"Siv test 8397","campaignBy":"Siviwe Xhelo (ZA)","lastUpdate":{"id":10270,"campaignId":14403,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T11:22:09.077279","updateDescription":"Campaign updated","approvalId":1334},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-21T00:00:00","campaignEndDate":"2024-03-22T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14404,"campaignId":"CNQ1351v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10237,"campaignId":14404,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-05-13T16:14:18.909556","updateDescription":"Campaign updated","approvalId":1326},"updatedBy":"Thabo Benjamin Setuke (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T11:23:28.775","campaignEndDate":"2024-03-20T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14405,"campaignId":"CNQ1352v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10271,"campaignId":14405,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T11:34:42.669457","updateDescription":"Campaign updated","approvalId":1335},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T11:27:14.767","campaignEndDate":"2024-03-20T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14406,"campaignId":"CNQ1353v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10273,"campaignId":14406,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:14:17.732088","updateDescription":"Campaign updated","approvalId":1337},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T11:28:47.154","campaignEndDate":"2024-03-20T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14407,"campaignId":"CNQ1354v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10274,"campaignId":14407,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:35:31.223891","updateDescription":"Campaign updated","approvalId":1338},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14408,"campaignId":"CNQ1355v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10278,"campaignId":14408,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T15:53:45.026189","updateDescription":"Campaign updated","approvalId":1342},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14409,"campaignId":"CNQ1356v001Q12024","campaignName":"Campaign History Dates","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":9998,"campaignId":14409,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-03-17T21:16:02.979734","updateDescription":"Create new campaign Campaign History Dates","approvalId":1},"updatedBy":"Minenhle Mpulo (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-18T00:00:00","campaignEndDate":"2024-03-24T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14411,"campaignId":"CNQ1358v001Q12024","campaignName":"Bathande Test Campaign","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10002,"campaignId":14411,"updatedBy":"","updatedDate":"2024-03-25T13:24:28.709085","updateDescription":"Create new campaign Bathande Test Campaign","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-26T00:00:00","campaignEndDate":"2024-03-26T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14412,"campaignId":"CNQ1359v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10003,"campaignId":14412,"updatedBy":"","updatedDate":"2024-03-27T09:45:21.018779","updateDescription":"Create new campaign Test","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14413,"campaignId":"CNQ1360v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10004,"campaignId":14413,"updatedBy":"","updatedDate":"2024-03-27T09:49:04.390427","updateDescription":"Create new campaign Test","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14414,"campaignId":"CNQ1361v001Q12024","campaignName":"AUTOMATION UAT 08397 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10005,"campaignId":14414,"updatedBy":"","updatedDate":"2024-03-27T16:00:37.032904","updateDescription":"Create new campaign AUTOMATION UAT 08397 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-28T00:00:00","campaignEndDate":"2024-03-29T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":true},{"id":14517,"campaignId":"CNQ094v001Q22024","campaignName":"Campaign History Test2","campaignBy":"<EMAIL>","lastUpdate":{"id":10217,"campaignId":14517,"updatedBy":"","updatedDate":"2024-05-08T15:57:46.144061","updateDescription":"Create new campaign Campaign History Test2","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":3,"channel":"BCD","imageResolution":"800x600"},"campaignStartDate":"2024-05-31T00:00:00","campaignEndDate":"2024-05-31T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":false},{"id":14518,"campaignId":"CNQ095v001Q22024","campaignName":"Campaign History Test3","campaignBy":"<EMAIL>","lastUpdate":{"id":10223,"campaignId":14518,"updatedBy":"","updatedDate":"2024-05-10T07:07:36.846122","updateDescription":"Create new campaign Campaign History Test3","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":2,"channel":"SSK","imageResolution":"1024x768"},"campaignStartDate":"2024-05-11T00:00:00","campaignEndDate":"2024-05-31T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":false},{"id":14519,"campaignId":"CNQ096v001Q22024","campaignName":"Edit Camp","campaignBy":"<EMAIL>","lastUpdate":{"id":10232,"campaignId":14519,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:51:02.938969","updateDescription":"Campaign updated","approvalId":1321},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-13T00:00:00","campaignEndDate":"2024-05-17T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14520,"campaignId":"CNQ097v001Q22024","campaignName":"May 13th","campaignBy":"<EMAIL>","lastUpdate":{"id":10231,"campaignId":14520,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:10:15.015522","updateDescription":"Campaign updated","approvalId":1320},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-14T00:00:00","campaignEndDate":"2024-05-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":false},{"id":14521,"campaignId":"CNQ098v001Q22024","campaignName":"Reverse campaign admin","campaignBy":"Yaash Ramsahaar (ZA)","lastUpdate":{"id":10238,"campaignId":14521,"updatedBy":"","updatedDate":"2024-05-15T09:27:53.767546","updateDescription":"Create new campaign Reverse campaign admin","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-16T00:00:00","campaignEndDate":"2024-05-16T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":false},{"id":14522,"campaignId":"CNQ099v001Q22024","campaignName":"Siyasebenza Demo","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10243,"campaignId":14522,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-05-20T09:09:31.632468","updateDescription":"Campaign updated","approvalId":1330},"updatedBy":"Minenhle Mpulo (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-21T00:00:00","campaignEndDate":"2024-05-24T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14523,"campaignId":"CNQ100v001Q22024","campaignName":"Monday and Tuesday Test","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10246,"campaignId":14523,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-24T14:15:10.499711","updateDescription":"Campaign updated","approvalId":1331},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-27T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14524,"campaignId":"CNQ101v001Q22024","campaignName":"Monday Tuesday NCR","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10247,"campaignId":14524,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-24T14:15:19.106133","updateDescription":"Campaign updated","approvalId":1332},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-27T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14525,"campaignId":"CNQ102v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10248,"campaignId":14525,"updatedBy":"","updatedDate":"2024-05-27T15:25:43.665409","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14526,"campaignId":"CNQ103v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10249,"campaignId":14526,"updatedBy":"","updatedDate":"2024-05-27T15:33:16.30409","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14527,"campaignId":"CNQ104v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10250,"campaignId":14527,"updatedBy":"","updatedDate":"2024-05-27T15:35:18.173806","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14528,"campaignId":"CNQ105v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10251,"campaignId":14528,"updatedBy":"","updatedDate":"2024-05-27T15:40:41.775058","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14529,"campaignId":"CNQ106v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10275,"campaignId":14529,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T13:32:59.186636","updateDescription":"Campaign updated","approvalId":1339},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14530,"campaignId":"CNQ107v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10277,"campaignId":14530,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T13:34:17.637111","updateDescription":"Campaign updated","approvalId":1341},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14531,"campaignId":"CNQ108v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10276,"campaignId":14531,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T13:33:24.736359","updateDescription":"Campaign updated","approvalId":1340},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14532,"campaignId":"CNQ109v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10255,"campaignId":14532,"updatedBy":"","updatedDate":"2024-05-27T16:12:37.108135","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14533,"campaignId":"CNQ110v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10256,"campaignId":14533,"updatedBy":"","updatedDate":"2024-05-27T16:14:50.047071","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14534,"campaignId":"CNQ111v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10257,"campaignId":14534,"updatedBy":"","updatedDate":"2024-05-27T16:20:37.421807","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14535,"campaignId":"CNQ112v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10258,"campaignId":14535,"updatedBy":"","updatedDate":"2024-05-27T16:22:47.623021","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14536,"campaignId":"CNQ113v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10259,"campaignId":14536,"updatedBy":"","updatedDate":"2024-05-27T16:30:55.109207","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14537,"campaignId":"CNQ114v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10260,"campaignId":14537,"updatedBy":"","updatedDate":"2024-05-27T16:32:58.823423","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14538,"campaignId":"CNQ115v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_Eng &amp; Afr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10261,"campaignId":14538,"updatedBy":"","updatedDate":"2024-05-27T16:41:31.786991","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_Eng &amp; Afr","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14539,"campaignId":"CNQ116v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_Afrikaans","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10262,"campaignId":14539,"updatedBy":"","updatedDate":"2024-05-27T16:43:37.937399","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_Afrikaans","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14540,"campaignId":"CNQ117v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10263,"campaignId":14540,"updatedBy":"","updatedDate":"2024-05-27T16:45:49.110016","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14541,"campaignId":"CNQ118v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10264,"campaignId":14541,"updatedBy":"","updatedDate":"2024-05-27T16:47:55.639763","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14542,"campaignId":"CNQ119v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10265,"campaignId":14542,"updatedBy":"","updatedDate":"2024-05-27T16:49:52.012021","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14543,"campaignId":"CNQ120v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10266,"campaignId":14543,"updatedBy":"","updatedDate":"2024-05-27T16:51:54.628759","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14544,"campaignId":"CNQ121v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_Eng &amp; Afr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10267,"campaignId":14544,"updatedBy":"","updatedDate":"2024-05-27T16:54:16.863922","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_Eng &amp; Afr","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14545,"campaignId":"CNQ122v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_Afrikaans","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10268,"campaignId":14545,"updatedBy":"","updatedDate":"2024-05-27T16:56:18.084816","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_Afrikaans","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true}]</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">${response.content} = [{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"Yaash.Ramsahaar@absa.a...</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<status status="PASS" starttime="******** 09:08:22.098" endtime="******** 09:08:22.738"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:08:22.738" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}"/>
<var name="${value}"/>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<status status="NOT RUN" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="NOT RUN" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<status status="NOT RUN" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</iter>
<status status="NOT RUN" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</for>
<status status="PASS" starttime="******** 09:08:22.738" endtime="******** 09:08:22.738"/>
</kw>
<status status="PASS" starttime="******** 09:08:21.907" endtime="******** 09:08:22.738"/>
</kw>
<doc>Gets all Marketing Campaigns - Business User</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 09:08:21.891" endtime="******** 09:08:22.738"/>
</test>
<test id="s1-t2" name="FFT - Controllers - Get Marketing Campaigns approval by ID using a Business User" line="45">
<kw name="GET marketing campaigns approvals">
<arg>Gets a Marketing Campaign using the Campaign ID - Business User</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMMarketingCampaign</arg>
<arg>14141</arg>
<arg>200</arg>
<arg>OK</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 09:08:26.027" level="INFO">Set test documentation to:
Gets a Marketing Campaign using the Campaign ID - Business User</msg>
<status status="PASS" starttime="******** 09:08:26.027" endtime="******** 09:08:26.027"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:26.030" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 09:08:26.027" endtime="******** 09:08:26.030"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 09:08:26.030" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 09:08:26.030" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 09:08:26.030" endtime="******** 09:08:26.030"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 09:08:26.030" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 09:08:26.030" endtime="******** 09:08:26.219"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:08:26.219" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</kw>
<status status="PASS" starttime="******** 09:08:26.030" endtime="******** 09:08:26.219"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 09:08:26.219" level="INFO">${end_point} = /ATMMarketingCampaign/14141</msg>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</kw>
<status status="NOT RUN" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 09:08:26.219" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</kw>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</branch>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:26.219"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 09:08:27.818" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingCampaign/14141 
 path_url=/ATMMarketingCampaign/14141 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fdSR85jLUjiP_yOEBnrdpNgKaAA7PcnB5k4nfJz9joG07H0tJMAo-XE_Wm8aGAgam-zML3jfZSNhcJL_Iw7-atPyO1gvcENTHwKgXnbIq8Hm0C81CWjepOKKVI4vH4Kd0UdadvIjusYbqV8PkaqwRacysjteDM0uF09pgxy041d8hburt3oclCrYAYPa_Ji5yrZfXvPD_mxoGgqTCzONOF4i2ntRthTmlqEjvK0G_vZfCYbqPc5zZ1qQ70PpHqkZ-LxgClyvgwmZviiawi_9DrC8zDGSu_9tCbkxDwfICx0K4LhunT9fgzakjHCJMs5RwgZnlhXGBLXUmVtAl2GGeg'} 
 body=None 
 </msg>
<msg timestamp="******** 09:08:27.818" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingCampaign/14141 
 status=200, reason=OK 
 headers={'Date': 'Thu, 30 May 2024 07:08:26 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=[{"id":0,"imageName":"107848 BM 1023 Group Savings campaign ATM v2.jpg","language":{"id":1,"language":"English","languageCode":"en"},"duration":0,"priority":0,"marketingImage":"data:image/jpeg;base64,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... (set the log level to DEBUG or TRACE to see the full content) 
 </msg>
<msg timestamp="******** 09:08:27.818" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 09:08:27.820" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">${response.content} = [{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"Yaash.Ramsahaar@absa.a...</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">[{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"<EMAIL>","updatedDate":"2024-05-09T14:32:45.15697","updateDescription":"Campaign updated","approvalId":1314},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-11T00:45:00","campaignEndDate":"2024-03-12T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14381,"campaignId":"CNQ1328v001Q12024","campaignName":"ATM Targeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10224,"campaignId":14381,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:01:33.455028","updateDescription":"Campaign updated","approvalId":1315},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14382,"campaignId":"CNQ1329v001Q12024","campaignName":"Region Targeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10225,"campaignId":14382,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:29:35.679302","updateDescription":"Campaign updated","approvalId":1316},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14383,"campaignId":"CNQ1330v001Q12024","campaignName":"Untargeted","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10226,"campaignId":14383,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:40:43.301326","updateDescription":"Campaign updated","approvalId":1317},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-13T00:45:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14386,"campaignId":"CNQ1333v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10227,"campaignId":14386,"updatedBy":"<EMAIL>","updatedDate":"2024-05-10T14:52:32.249235","updateDescription":"Campaign updated","approvalId":1318},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-11T00:45:00","campaignEndDate":"2024-03-12T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14394,"campaignId":"CNQ1341v001Q12024","campaignName":"rrrr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10229,"campaignId":14394,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T13:50:28.366005","updateDescription":"Campaign updated","approvalId":1319},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14395,"campaignId":"CNQ1342v001Q12024","campaignName":"trerte","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10233,"campaignId":14395,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:52:23.089532","updateDescription":"Campaign updated","approvalId":1322},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T00:00:00","campaignEndDate":"2024-03-15T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14396,"campaignId":"CNQ1343v001Q12024","campaignName":"gregekjwj","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10234,"campaignId":14396,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:54:12.280723","updateDescription":"Campaign updated","approvalId":1323},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14397,"campaignId":"CNQ1344v001Q12024","campaignName":"ghgjhjgjh","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10235,"campaignId":14397,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:54:46.254781","updateDescription":"Campaign updated","approvalId":1324},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14398,"campaignId":"CNQ1345v001Q12024","campaignName":"cghcghcg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10236,"campaignId":14398,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:55:34.495285","updateDescription":"Campaign updated","approvalId":1325},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14399,"campaignId":"CNQ1346v001Q12024","campaignName":"fghfghfgh","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10239,"campaignId":14399,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-15T12:23:05.520235","updateDescription":"Campaign updated","approvalId":1327},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14400,"campaignId":"CNQ1347v001Q12024","campaignName":"hghgghg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10240,"campaignId":14400,"updatedBy":"SVC-marketing_ac_prd","updatedDate":"2024-05-15T12:35:13.320754","updateDescription":"Campaign updated","approvalId":1328},"updatedBy":"SVC-marketing_ac_prd","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14401,"campaignId":"CNQ1348v001Q12024","campaignName":"gfghfvghg","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10269,"campaignId":14401,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T10:44:10.701422","updateDescription":"Campaign updated","approvalId":1333},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14402,"campaignId":"CNQ1349v001Q12024","campaignName":"Siv test campaign","campaignBy":"Siviwe Xhelo (ZA)","lastUpdate":{"id":10272,"campaignId":14402,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:05:29.075441","updateDescription":"Campaign updated","approvalId":1336},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-18T00:00:00","campaignEndDate":"2024-03-19T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14403,"campaignId":"CNQ1350v001Q12024","campaignName":"Siv test 8397","campaignBy":"Siviwe Xhelo (ZA)","lastUpdate":{"id":10270,"campaignId":14403,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T11:22:09.077279","updateDescription":"Campaign updated","approvalId":1334},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-21T00:00:00","campaignEndDate":"2024-03-22T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14404,"campaignId":"CNQ1351v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10237,"campaignId":14404,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-05-13T16:14:18.909556","updateDescription":"Campaign updated","approvalId":1326},"updatedBy":"Thabo Benjamin Setuke (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T11:23:28.775","campaignEndDate":"2024-03-20T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14405,"campaignId":"CNQ1352v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10271,"campaignId":14405,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T11:34:42.669457","updateDescription":"Campaign updated","approvalId":1335},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T11:27:14.767","campaignEndDate":"2024-03-20T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14406,"campaignId":"CNQ1353v001Q12024","campaignName":"Campaign_Created_by_automation_script","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10273,"campaignId":14406,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:14:17.732088","updateDescription":"Campaign updated","approvalId":1337},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-15T11:28:47.154","campaignEndDate":"2024-03-20T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":true},{"id":14407,"campaignId":"CNQ1354v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10274,"campaignId":14407,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T12:35:31.223891","updateDescription":"Campaign updated","approvalId":1338},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14408,"campaignId":"CNQ1355v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10278,"campaignId":14408,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T15:53:45.026189","updateDescription":"Campaign updated","approvalId":1342},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q12024","isTargetted":false},{"id":14409,"campaignId":"CNQ1356v001Q12024","campaignName":"Campaign History Dates","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":9998,"campaignId":14409,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-03-17T21:16:02.979734","updateDescription":"Create new campaign Campaign History Dates","approvalId":1},"updatedBy":"Minenhle Mpulo (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-18T00:00:00","campaignEndDate":"2024-03-24T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14411,"campaignId":"CNQ1358v001Q12024","campaignName":"Bathande Test Campaign","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10002,"campaignId":14411,"updatedBy":"","updatedDate":"2024-03-25T13:24:28.709085","updateDescription":"Create new campaign Bathande Test Campaign","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-26T00:00:00","campaignEndDate":"2024-03-26T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14412,"campaignId":"CNQ1359v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10003,"campaignId":14412,"updatedBy":"","updatedDate":"2024-03-27T09:45:21.018779","updateDescription":"Create new campaign Test","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14413,"campaignId":"CNQ1360v001Q12024","campaignName":"Test","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10004,"campaignId":14413,"updatedBy":"","updatedDate":"2024-03-27T09:49:04.390427","updateDescription":"Create new campaign Test","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-14T00:00:00","campaignEndDate":"2024-03-14T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":false},{"id":14414,"campaignId":"CNQ1361v001Q12024","campaignName":"AUTOMATION UAT 08397 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10005,"campaignId":14414,"updatedBy":"","updatedDate":"2024-03-27T16:00:37.032904","updateDescription":"Create new campaign AUTOMATION UAT 08397 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-03-28T00:00:00","campaignEndDate":"2024-03-29T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q12024","isTargetted":true},{"id":14517,"campaignId":"CNQ094v001Q22024","campaignName":"Campaign History Test2","campaignBy":"<EMAIL>","lastUpdate":{"id":10217,"campaignId":14517,"updatedBy":"","updatedDate":"2024-05-08T15:57:46.144061","updateDescription":"Create new campaign Campaign History Test2","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":3,"channel":"BCD","imageResolution":"800x600"},"campaignStartDate":"2024-05-31T00:00:00","campaignEndDate":"2024-05-31T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":false},{"id":14518,"campaignId":"CNQ095v001Q22024","campaignName":"Campaign History Test3","campaignBy":"<EMAIL>","lastUpdate":{"id":10223,"campaignId":14518,"updatedBy":"","updatedDate":"2024-05-10T07:07:36.846122","updateDescription":"Create new campaign Campaign History Test3","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":2,"channel":"SSK","imageResolution":"1024x768"},"campaignStartDate":"2024-05-11T00:00:00","campaignEndDate":"2024-05-31T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":false},{"id":14519,"campaignId":"CNQ096v001Q22024","campaignName":"Edit Camp","campaignBy":"<EMAIL>","lastUpdate":{"id":10232,"campaignId":14519,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:51:02.938969","updateDescription":"Campaign updated","approvalId":1321},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-13T00:00:00","campaignEndDate":"2024-05-17T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14520,"campaignId":"CNQ097v001Q22024","campaignName":"May 13th","campaignBy":"<EMAIL>","lastUpdate":{"id":10231,"campaignId":14520,"updatedBy":"<EMAIL>","updatedDate":"2024-05-13T15:10:15.015522","updateDescription":"Campaign updated","approvalId":1320},"updatedBy":"<EMAIL>","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-14T00:00:00","campaignEndDate":"2024-05-14T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":false},{"id":14521,"campaignId":"CNQ098v001Q22024","campaignName":"Reverse campaign admin","campaignBy":"Yaash Ramsahaar (ZA)","lastUpdate":{"id":10238,"campaignId":14521,"updatedBy":"","updatedDate":"2024-05-15T09:27:53.767546","updateDescription":"Create new campaign Reverse campaign admin","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-16T00:00:00","campaignEndDate":"2024-05-16T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":false},{"id":14522,"campaignId":"CNQ099v001Q22024","campaignName":"Siyasebenza Demo","campaignBy":"Minenhle Mpulo (ZA)","lastUpdate":{"id":10243,"campaignId":14522,"updatedBy":"Minenhle Mpulo (ZA)","updatedDate":"2024-05-20T09:09:31.632468","updateDescription":"Campaign updated","approvalId":1330},"updatedBy":"Minenhle Mpulo (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-21T00:00:00","campaignEndDate":"2024-05-24T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14523,"campaignId":"CNQ100v001Q22024","campaignName":"Monday and Tuesday Test","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10246,"campaignId":14523,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-24T14:15:10.499711","updateDescription":"Campaign updated","approvalId":1331},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-27T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14524,"campaignId":"CNQ101v001Q22024","campaignName":"Monday Tuesday NCR","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10247,"campaignId":14524,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-24T14:15:19.106133","updateDescription":"Campaign updated","approvalId":1332},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-27T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14525,"campaignId":"CNQ102v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10248,"campaignId":14525,"updatedBy":"","updatedDate":"2024-05-27T15:25:43.665409","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14526,"campaignId":"CNQ103v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10249,"campaignId":14526,"updatedBy":"","updatedDate":"2024-05-27T15:33:16.30409","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14527,"campaignId":"CNQ104v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10250,"campaignId":14527,"updatedBy":"","updatedDate":"2024-05-27T15:35:18.173806","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14528,"campaignId":"CNQ105v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10251,"campaignId":14528,"updatedBy":"","updatedDate":"2024-05-27T15:40:41.775058","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14529,"campaignId":"CNQ106v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10275,"campaignId":14529,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T13:32:59.186636","updateDescription":"Campaign updated","approvalId":1339},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14530,"campaignId":"CNQ107v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10277,"campaignId":14530,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T13:34:17.637111","updateDescription":"Campaign updated","approvalId":1341},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14531,"campaignId":"CNQ108v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10276,"campaignId":14531,"updatedBy":"Yaash Ramsahaar (ZA)","updatedDate":"2024-05-28T13:33:24.736359","updateDescription":"Campaign updated","approvalId":1340},"updatedBy":"Yaash Ramsahaar (ZA)","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":true,"version":"v001Q22024","isTargetted":true},{"id":14532,"campaignId":"CNQ109v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10255,"campaignId":14532,"updatedBy":"","updatedDate":"2024-05-27T16:12:37.108135","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14533,"campaignId":"CNQ110v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10256,"campaignId":14533,"updatedBy":"","updatedDate":"2024-05-27T16:14:50.047071","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14534,"campaignId":"CNQ111v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10257,"campaignId":14534,"updatedBy":"","updatedDate":"2024-05-27T16:20:37.421807","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14535,"campaignId":"CNQ112v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10258,"campaignId":14535,"updatedBy":"","updatedDate":"2024-05-27T16:22:47.623021","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14536,"campaignId":"CNQ113v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10259,"campaignId":14536,"updatedBy":"","updatedDate":"2024-05-27T16:30:55.109207","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14537,"campaignId":"CNQ114v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10260,"campaignId":14537,"updatedBy":"","updatedDate":"2024-05-27T16:32:58.823423","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-06-30T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14538,"campaignId":"CNQ115v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_Eng &amp; Afr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10261,"campaignId":14538,"updatedBy":"","updatedDate":"2024-05-27T16:41:31.786991","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_Eng &amp; Afr","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14539,"campaignId":"CNQ116v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_Afrikaans","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10262,"campaignId":14539,"updatedBy":"","updatedDate":"2024-05-27T16:43:37.937399","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_Afrikaans","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14540,"campaignId":"CNQ117v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10263,"campaignId":14540,"updatedBy":"","updatedDate":"2024-05-27T16:45:49.110016","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14541,"campaignId":"CNQ118v001Q22024","campaignName":"AUTOMATION UAT 08395 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10264,"campaignId":14541,"updatedBy":"","updatedDate":"2024-05-27T16:47:55.639763","updateDescription":"Create new campaign AUTOMATION UAT 08395 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14542,"campaignId":"CNQ119v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10265,"campaignId":14542,"updatedBy":"","updatedDate":"2024-05-27T16:49:52.012021","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14543,"campaignId":"CNQ120v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_English","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10266,"campaignId":14543,"updatedBy":"","updatedDate":"2024-05-27T16:51:54.628759","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_English","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14544,"campaignId":"CNQ121v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_Eng &amp; Afr","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10267,"campaignId":14544,"updatedBy":"","updatedDate":"2024-05-27T16:54:16.863922","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_Eng &amp; Afr","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true},{"id":14545,"campaignId":"CNQ122v001Q22024","campaignName":"AUTOMATION UAT 09830 Targted_Afrikaans","campaignBy":"Thabo Benjamin Setuke (ZA)","lastUpdate":{"id":10268,"campaignId":14545,"updatedBy":"","updatedDate":"2024-05-27T16:56:18.084816","updateDescription":"Create new campaign AUTOMATION UAT 09830 Targted_Afrikaans","approvalId":1},"updatedBy":"","screen":{"id":0,"screenType":"","screenNumber":"","channelId":0},"marketingChannel":{"id":1,"channel":"ATM","imageResolution":"800x600"},"campaignStartDate":"2024-05-28T00:00:00","campaignEndDate":"2024-05-28T23:59:59","imageList":[],"isActive":true,"isApproved":false,"version":"v001Q22024","isTargetted":true}]</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">${response.content} = [{"id":14380,"campaignId":"CNQ1327v001Q12024","campaignName":"Performance Campaign 1","campaignBy":"SVC-marketing_ac_sa","lastUpdate":{"id":10220,"campaignId":14380,"updatedBy":"Yaash.Ramsahaar@absa.a...</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<status status="PASS" starttime="******** 09:08:26.219" endtime="******** 09:08:27.820"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:08:27.820" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.820"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.822"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:08:27.822" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</kw>
<status status="PASS" starttime="******** 09:08:27.820" endtime="******** 09:08:27.822"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}"/>
<var name="${value}"/>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<status status="NOT RUN" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="NOT RUN" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</kw>
<status status="NOT RUN" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</iter>
<status status="NOT RUN" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</for>
<status status="PASS" starttime="******** 09:08:27.822" endtime="******** 09:08:27.822"/>
</kw>
<status status="PASS" starttime="******** 09:08:26.027" endtime="******** 09:08:27.822"/>
</kw>
<doc>Gets a Marketing Campaign using the Campaign ID - Business User</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 09:08:26.027" endtime="******** 09:08:27.822"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 09:08:21.530" endtime="******** 09:08:31.099"/>
</suite>
<statistics>
<total>
<stat pass="2" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="2" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="2" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="2" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 09:08:26.017" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 09:08:31.097" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
