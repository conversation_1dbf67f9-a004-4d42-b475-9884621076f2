
class CreateRESTRequest:
    def __init__(self, domain, bin_id):
        self.domain = domain
        self.bin_id = bin_id

        self.params = {
            "binid": self.bin_id,  # Adding a query parameter to filter the results by Bin Id
        }


    def get_delete_bin_endpoint(self):
        path = "/api/v1/bintables/entry/bins/delete"
        url = f"{self.domain}{path}"
        return url


    def get_delete_bin_params(self):
        return self.params
