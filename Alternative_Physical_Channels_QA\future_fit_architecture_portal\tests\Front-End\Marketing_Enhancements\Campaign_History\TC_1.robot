*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing Camapaign Approval

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Logout.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../keywords/common/Navigation.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Campaign_History.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/View_Campaign_Data.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Export_Campaign.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Edit_Campaign.robot

*** Keyword ***
Validate Search using column details
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}   ${SEARCH_CRITERIA}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    Then The user navigates to the Campaign History page

    And The user inputs search criteria     ${SEARCH_CRITERIA}

    Then The user verifies search criteria is met

| *Test Cases*                              |      *DOCUMENTATION*     |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   |     *SEARCH_CRITERIA*               |
| RAC29a_TC_745_Verify_Search_using_column_details  |  Validate Search using column details  |  Campaign Export   |    BUSINESS_CAPTURER       |     APC_UAT             |      YES             |