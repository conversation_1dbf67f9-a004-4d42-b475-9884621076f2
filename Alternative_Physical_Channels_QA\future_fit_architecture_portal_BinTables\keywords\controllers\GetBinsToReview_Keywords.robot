*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/GetBinsToReview.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***


*** Keywords ***
The User gets all the Bin numbers that must be reviewed from the Database

    ${db_results}=     Get the Bins to be reviewed from the Database

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for bins awaiting review did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Save the database results in a global variable
    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Log Many    ${DATABASE_RESULTS}

The User gets a Bin number that must be reviewed from the Database

    ${db_results}=     Get the Bin to be approved from the Database

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for bins awaiting review did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Save the database results in a global variable
    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Log Many    ${DATABASE_RESULTS}


The User sends a Get Request for GetBinsToReview to get all bins that are still to be reviwed
    [Arguments]     ${BASE_URL}

    The User sends a Get Request for GetBinsToReview using a Bin Number     ${BASE_URL}         ${EMPTY}

The all the Bin numbers' details returned by the GetBinsToReview Controller must be the same as the details of the bins queried from the Database
    ${num_rows}=    Get Length    ${DATABASE_RESULTS}

    ${db_rows_equals_api_items}=        Run Keyword And Return Status    Should Be Equal As Strings    ${num_rows}    ${TOTAL_NUMBER_OF_BINS}

    Run Keyword If    not ${db_rows_equals_api_items}
    ...    Fail     The total number of Bins returned by the Database is not the same as the number returned by the API. DB Total: '${num_rows}', API Total: '${TOTAL_NUMBER_OF_BINS}'.

    Log     '${num_rows}' database BINS will be verfied against the API
    #Loop through the DB Bins and verify that the Controller has returned the same Bins details
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       BinNumber

        #Get the review details for the current Bin from the database
        ${db_results}=     Get the Bin details for Bins to be Reviwed from the Database    ${binNumber_data}

        # Ensure the results are not empty
        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}


        Run Keyword If    not ${dr_results_contain_data}
        ...     Fail    Database query for bin number: '${binNumber_data}' returned no results



        #Loop Through the review records details for the current BIN NUMBER and verify them against the Controller
        #The below variables are used to detect and filter out the duplicate columns
        ${bin_type_already_read_value}=       Set Variable      ${EMPTY}
        ${bin_type_list}=      Create List
        ${tobeActionedBy_Value}=    Set Variable    ${EMPTY}
        FOR    ${db_results_row}    IN    @{db_results}

            Log Many    ${db_results_row}


            #If the Database BIN Type is deleted do not verify it
            ${binTypeIsDeleted_data}=       Get Column Data By Name       ${db_results_row}       binTypeIsDeleted
            Run Keyword If    '${binTypeIsDeleted_data}' == '1'
            ...    Continue For Loop


            ${BIN_TYPE}=       Get Column Data By Name       ${db_results_row}       binType

            IF    '${bin_type_already_read_value}' == '${EMPTY}'
                ${bin_type_already_read_value}=   Set Variable     ${BIN_TYPE}
                Append To List    ${bin_type_list}      ${BIN_TYPE}
            ELSE
                ${duplicate_found}=     Set Variable  ${False}
                FOR    ${type_name}    IN    @{bin_type_list}
                    IF    '${type_name}' == '${BIN_TYPE}'
                        ${duplicate_found}=     Set Variable  ${True}
                        Exit For Loop
                    END
                END

                IF    ${duplicate_found}
                        CONTINUE
                    ELSE
                        Append To List    ${bin_type_list}      ${BIN_TYPE}
                END
            END

             ${BIN_ID}=  Get Column Data By Name       ${db_results_row}       Bin_ID

            ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${BIN_ID.strip()}
            FOR    ${row_data}    IN    @{action_tracker_db_results}
                ${REVIEW_STATUS}=       Get Column Data By Name       ${row_data}       Status
                ${REVIEW_STATUS}=       Get Bin Status Text           ${REVIEW_STATUS}
                ${OUTCOME}=             Get Column Data By Name       ${row_data}       ActionType
                ${OUTCOME}=             Get Bin Outcome Text          ${OUTCOME}
                ${REJECTED_COMMENT}=    Get Column Data By Name       ${row_data}       RejectionComment
                ${REVIEWED_BY}=         Get Column Data By Name       ${row_data}       ReviewedBy
                ${REVIEWED_DATE}=       Get Column Data By Name       ${row_data}       ReviewedDate
                ${TO_BE_ACTIONED_BY}=   Get Column Data By Name       ${row_data}       ToBeActionedBy
            END

            ${TO_BE_ACTIONED_BY}=   Get Bin To Be Actioned By Text     ${TO_BE_ACTIONED_BY}

            IF    '${tobeActionedBy_Value}' == '${EMPTY}'
                ${tobeActionedBy_Value}=   Set Variable     ${TO_BE_ACTIONED_BY}
            ELSE
                 IF    '${tobeActionedBy_Value}' != '${TO_BE_ACTIONED_BY}'
                     CONTINUE
                 END
            END



            ${BIN_ID}=  Get Column Data By Name       ${db_results_row}       Bin_ID
            ${CAPTURED_DATE}=  Get Column Data By Name       ${db_results_row}       capturedDate
            IF    '${CAPTURED_DATE}' != 'None'
                ${CAPTURED_DATE}=        Format Timestamp for API and Database      ${CAPTURED_DATE}       ${True}
            END
            ${CAPTURED_BY}=         Get Column Data By Name       ${db_results_row}       capturedBy
            ${ACTION_DATE}=         Get Column Data By Name       ${db_results_row}       actionDate
            IF    '${REVIEWED_DATE}' != 'None'
                ${REVIEWED_DATE}=        Format Timestamp for API and Database      ${REVIEWED_DATE}       ${True}
            END
            ${LATEST_SERVER_NUMBER}=       Get Column Data By Name       ${db_results_row}       LatestServerNumber

            #Verify against the Controller results
            The expected Bin Number details are retuned by the API Response     ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${binNumber_data}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE}
        END

    END

The User sends a Get Request for GetBinsToReview using a Bin Number
    [Arguments]     ${BASE_URL}     ${BIN_NUMBER}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetBinsToReview Instance    ${base_url}     ${BIN_NUMBER}     #Create an instance of   GetBinsToReview

    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}  headers=${headers}     method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance

The User sends a Get Request for GetBinsToReview using an incorrect token
    [Arguments]     ${BASE_URL}     ${BIN_NUMBER}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetBinsToReview Instance    ${base_url}     ${BIN_NUMBER}     #Create an instance of   GetBinsToReview

    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=    Set Variable    Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IjNQYUs0RWZ5Qk5RdTNDdGpZc2EzWW1oUTVFMCIsImtpZCI6IjNQYUs0RWZ5Qk5RdTNDdGpZc2EzWW1oUTVFMCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AsJyThyLj--vZAAeZ435fx6-zBBPJ3gGlTDLS9n5D5NzO9DFa0xq5zl-6aHGnoaeAQ_-hNEqrJDJM_XkKtEhKJLaZ95bta2-jyAxXL1-rmxF635TD3JaQIYgeXVEJ1KeyLtD0bEb9K1MgthImiMF98ik0Sd7DQxDvvX_2yMoNg_-cJlTQ3yL3EobkHjYVK7dC424_OrpvVXmCNp-Io2uVBMzYpWcd7nWX4Jtp5z3KTw2GNjCZygzBUSZ8qqfXPc1r_0eI3WxA7LS0sKLrHwmK_VmYlWtLC4eYH82kGq5Tm9mr8yQ8lGQZUDBihOIpmf7G6kEAasya-hSNSlBVrBDtA

    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}  headers=${headers}     method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance


The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'GetBinsToReview' REST API call failed, the returned status is '${status_code}'



The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}

    IF    '${EXPECTED_ERROR_MESSAGE}' == '[]'
        Verify that the API retruned an empty response
        RETURN
    END


Verify that the API retruned an empty response

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    #Verify that the response is empty
    ${verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '[]'     '${bin_object_instace}'

    Run Keyword If    not ${verification_passed}
    ...    Fail     The API did not return an empty response!



The expected Bin Number details are retuned by the API Response

    [Arguments]     ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE}


    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${captured_date_must_be_verified}=    Set Variable If  '${CAPTURED_DATE}' != '${EMPTY}' and '${CAPTURED_DATE}' != 'None'     ${True}     ${False}
    ${captured_date_verification_passed}=    Set Variable If  ${captured_date_must_be_verified}     ${False}     ${True}

    ${captured_by_must_be_verified}=    Set Variable If  '${CAPTURED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${captured_by_verification_passed}=    Set Variable If  ${captured_by_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}


    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${review_status_must_be_verified}=    Set Variable If  '${REVIEW_STATUS}' != '${EMPTY}'     ${True}     ${False}
    ${review_status_verification_passed}=    Set Variable If  ${review_status_must_be_verified}     ${False}     ${True}

    ${outcome_must_be_verified}=    Set Variable If  '${OUTCOME}' != '${EMPTY}'     ${True}     ${False}
    ${outcome_verification_passed}=    Set Variable If  ${outcome_must_be_verified}     ${False}     ${True}

    ${rejected_comment_provided}=       Run Keyword And Return Status    Should Not Be Equal As Strings     ${REJECTED_COMMENT}     None
    IF    ${rejected_comment_provided}
        ${REJECTED_COMMENT}=     Remove Quotes       ${REJECTED_COMMENT}
    END

    ${rejected_comment_must_be_verified}=    Set Variable If  '${REJECTED_COMMENT}' != '${EMPTY}'     ${True}     ${False}
    ${rejected_comment_verification_passed}=    Set Variable If  ${rejected_comment_must_be_verified}     ${False}     ${True}

    ${reviewedBy_must_be_verified}=    Set Variable If  '${REVIEWED_BY}' != '${EMPTY}' and '${REVIEWED_BY}' != 'None'     ${True}     ${False}
    ${reviewedBy_verification_passed}=    Set Variable If  ${reviewedBy_must_be_verified}     ${False}     ${True}

    ${reviewed_date_must_be_verified}=    Set Variable If  '${REVIEWED_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${reviewed_date_verification_passed}=    Set Variable If  ${reviewed_date_must_be_verified}     ${False}     ${True}

    ${latestServerNumber_must_be_verified}=    Set Variable If  '${LATEST_SERVER_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${latestServerNumber_verification_passed}=    Set Variable If  ${latestServerNumber_must_be_verified}     ${False}     ${True}

    ${binType_must_be_verified}=    Set Variable If  '${BIN_TYPE}' != '${EMPTY}'     ${True}     ${False}
    ${binType_verification_passed}=    Set Variable If  ${binType_must_be_verified}     ${False}     ${True}
    
    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    Log     '################################################'

    #Loop through all returned Bins to verify data
    FOR    ${index}    ${element}    IN ENUMERATE    @{bin_object_instace}
        Log    ${index}: ${element}
        ${binNumber_data}=        Get the Bin Number Details     ${element}
        Log    '${binNumber_data}'
        #IF the BIN Number is found then verify its details
        IF    '${binNumber_data}' == '${BIN_NUMBER}'
            Log     '################################################'
            ${binNumber_verification_passed}=      Set Variable    ${True}
            ${binNumber_verified}=      Set Variable    ${True}
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$

            ${binId_data}=         Get the Bin Response field data     ${element}     get_id
            #Check if the Bin ID must be verified against the user's data.
            IF    ${binID_must_be_verified}
                ${binID_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_ID}'     '${binId_data}'
            END


            ${bin_capturedDate_data}=            Get the Bin Response field data     ${element}     get_captured_date
            ${bin_capturedDate_data}=        Format Timestamp for API and Database      ${bin_capturedDate_data}       ${True}

            #Check if the Bin captured_date must be verified against the user's data.
            IF    ${captured_date_must_be_verified}
                ${captured_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_DATE}'     '${bin_capturedDate_data}'
            END


            ${bin_capturedBy_data}=            Get the Bin Response field data     ${element}     get_captured_by
            #Check if the Bin captured_by must be verified against the user's data.
            IF    ${captured_by_must_be_verified}
                ${captured_by_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_BY}'     '${bin_capturedBy_data}'
            END

            ${binActionDate_data}=    Get the Bin Response field data     ${element}     get_action_date
            #Check if the Action Date must be verified against the user's data
            IF    ${actionDate_must_be_verified}
                ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
            END

            ${bin_review_status_data}=    Get the Bin Response field data     ${element}     get_review_status
            #Check if the Action Date must be verified against the user's data
            IF    ${review_status_must_be_verified}
                ${review_status_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEW_STATUS}'     '${bin_review_status_data}'
            END


            ${bin_outcome_data}=    Get the Bin Response field data     ${element}     get_outcome
            #Check if the outcome must be verified against the user's data
            IF    ${outcome_must_be_verified}
                ${outcome_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${OUTCOME}'     '${bin_outcome_data}'
            END


            ${bin_rejected_comment_data}=    Get the Bin Response field data     ${element}     get_rejected_comment
            #Check if the toActionedBy must be verified against the user's data
            IF    ${rejected_comment_must_be_verified}

                ${rejected_comment_provided}=       Run Keyword And Return Status    Should Not Be Equal As Strings     ${bin_rejected_comment_data}     None
                IF    ${rejected_comment_provided}
                    ${bin_rejected_comment_data}=     Remove Quotes       ${bin_rejected_comment_data}
                END
                ${rejected_comment_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REJECTED_COMMENT}'     '${bin_rejected_comment_data}'
            END

            ${bin_reviewedBy_data}=    Get the Bin Response field data     ${element}     get_reviewed_by
            #Check if the toActionedBy must be verified against the user's data
            IF    ${reviewedBy_must_be_verified}
                ${reviewedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_BY}'     '${bin_reviewedBy_data}'
            END


            ${bin_reviewed_date_data}=    Get the Bin Response field data     ${element}     get_reviewed_date

            IF    '${bin_reviewed_date_data}' != 'None'
                ${bin_reviewed_date_data}=        Format Timestamp for API and Database      ${bin_reviewed_date_data}       ${True}
            END

            #Check if the toActionedBy must be verified against the user's data
            IF    ${reviewed_date_must_be_verified}
                ${reviewed_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_DATE}'     '${bin_reviewed_date_data}'
            END


             ${bin_latestServerNumber_data}=    Get the Bin Response field data     ${element}     get_latest_server_number
            #Check if the toActionedBy must be verified against the user's data
            IF    ${latestServerNumber_must_be_verified}
                ${latestServerNumber_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${LATEST_SERVER_NUMBER}'     '${bin_latestServerNumber_data}'
            END


            ${bin_binType_data}=    Get the Bin Response field data     ${element}     get_bin_type
            ${bin_binType_data_array}=      Split String    ${bin_binType_data}         separator=,
            #Check if the Bin Type must be verified against the user's data
            IF    ${binType_must_be_verified}
                FOR    ${bin_type_element}    IN    @{bin_binType_data_array}
                    ${users_binType_data_array}=      Split String    ${BIN_TYPE}         separator=,
                    FOR    ${user_bin_type_element}    IN    @{users_binType_data_array}
                        ${binType_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${user_bin_type_element.strip()}'     '${bin_type_element.strip()}'
                        Run Keyword If    ${binType_verification_passed}
                        ...    Exit For Loop
                    END

                     Run Keyword If    ${binType_verification_passed}
                        ...    Exit For Loop

                END
            END


            #The loop will be exited if all the parameters have been verified against the response
            Run Keyword If    ${binNumber_verified}
            ...    Exit For Loop
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
        END
    END

    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the API response.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${captured_date_must_be_verified}
        Run Keyword If    ${captured_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${captured_by_must_be_verified}
        Run Keyword If    ${captured_by_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${review_status_must_be_verified}
        Run Keyword If    ${review_status_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${outcome_must_be_verified}
        Run Keyword If    ${outcome_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



    IF  ${rejected_comment_must_be_verified}
        Run Keyword If    ${rejected_comment_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${reviewedBy_must_be_verified}
        Run Keyword If    ${reviewedBy_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${reviewed_date_must_be_verified}
        Run Keyword If    ${reviewed_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${latestServerNumber_must_be_verified}
        Run Keyword If    ${latestServerNumber_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binType_must_be_verified}
        Run Keyword If    ${binType_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



#The below keywords are for Database Verifications


The Bin Number details must exist on the Bin Database
    [Arguments]     ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE}

    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${captured_date_must_be_verified}=    Set Variable If  '${CAPTURED_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${captured_date_verification_passed}=    Set Variable If  ${captured_date_must_be_verified}     ${False}     ${True}

    ${captured_by_must_be_verified}=    Set Variable If  '${CAPTURED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${captured_by_verification_passed}=    Set Variable If  ${captured_by_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}


    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${review_status_must_be_verified}=    Set Variable If  '${REVIEW_STATUS}' != '${EMPTY}'     ${True}     ${False}
    ${review_status_verification_passed}=    Set Variable If  ${review_status_must_be_verified}     ${False}     ${True}

    ${outcome_must_be_verified}=    Set Variable If  '${OUTCOME}' != '${EMPTY}'     ${True}     ${False}
    ${outcome_verification_passed}=    Set Variable If  ${outcome_must_be_verified}     ${False}     ${True}

    ${toActionedBy_must_be_verified}=    Set Variable If  '${TO_BE_ACTIONED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${toActionedBy_verification_passed}=    Set Variable If  ${toActionedBy_must_be_verified}     ${False}     ${True}

    ${rejected_comment_must_be_verified}=    Set Variable If  '${REJECTED_COMMENT}' != '${EMPTY}'     ${True}     ${False}
    ${rejected_comment_verification_passed}=    Set Variable If  ${rejected_comment_must_be_verified}     ${False}     ${True}

    ${reviewedBy_must_be_verified}=    Set Variable If  '${REVIEWED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${reviewedBy_verification_passed}=    Set Variable If  ${reviewedBy_must_be_verified}     ${False}     ${True}

    ${reviewed_date_must_be_verified}=    Set Variable If  '${REVIEWED_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${reviewed_date_verification_passed}=    Set Variable If  ${reviewed_date_must_be_verified}     ${False}     ${True}

    ${latestServerNumber_must_be_verified}=    Set Variable If  '${LATEST_SERVER_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${latestServerNumber_verification_passed}=    Set Variable If  ${latestServerNumber_must_be_verified}     ${False}     ${True}

    ${binType_must_be_verified}=    Set Variable If  '${BIN_TYPE}' != '${EMPTY}'     ${True}     ${False}
    ${binType_verification_passed}=    Set Variable If  ${binType_must_be_verified}     ${False}     ${True}

    ${db_results}=     Get the Bin details for Bins to be Reviwed from the Database    ${BIN_NUMBER}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin number: '${BIN_NUMBER}' returned no results

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Loop through the returned DB results and verify them against the user's supplied parameters
    ${num_rows}=    Get Length    ${db_results}
    #Loop Through the review records details for the current BIN NUMBER and verify them against the Controller
    #The below variables are used to detect and filter out the duplicate columns
    ${bin_type_already_read_value}=       Set Variable      ${EMPTY}
    ${tobeActionedBy_Value}=    Set Variable       ${EMPTY}
    FOR    ${row}    IN    @{db_results}
        ${binNumber_verification_passed}=      Set Variable    ${True}
        ${binNumber_verified}=      Set Variable    ${True}

        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType
        IF    '${bin_type_already_read_value}' == '${EMPTY}'
            ${bin_type_already_read_value}=   Set Variable     ${bin_binType_data}
        ELSE
             IF    '${bin_type_already_read_value}' == '${bin_binType_data}'
                 CONTINUE
             END
        END

        ${bin_toActionedBy_data}=        Get Column Data By Name       ${row}       toBeActionedBy
        ${bin_toActionedBy_data}=        Get Bin To Be Actioned By Text     ${bin_toActionedBy_data}

        IF    '${tobeActionedBy_Value}' == '${EMPTY}'
            ${tobeActionedBy_Value}=   Set Variable     ${bin_toActionedBy_data}
        ELSE
             IF    '${tobeActionedBy_Value}' != '${bin_toActionedBy_data}'
                 CONTINUE
             END
        END


        #Verify the bin details
        ${binID_data}=                   Get Column Data By Name       ${row}       Bin_ID
        ${bin_capturedBy_data}=          Get Column Data By Name       ${row}       capturedBy
        ${bin_capturedDate_data}=        Get Column Data By Name       ${row}       capturedDate

        IF    '${bin_capturedDate_data}' != 'None'
            ${bin_capturedDate_data}=        Format Timestamp for API and Database      ${bin_capturedDate_data}       ${True}
        END

        ${binActionDate_data}=           Get Column Data By Name       ${row}       actionDate
        ${bin_review_status_data}=       Get Column Data By Name       ${row}       reviewStatus
        ${bin_review_status_data}=       Get Bin Status Text     ${bin_review_status_data}
        ${bin_outcome_data}=             Get Column Data By Name       ${row}       outcome
        ${bin_outcome_data}=             Get Bin Outcome Text    ${bin_outcome_data}
        ${bin_rejected_comment_data}=    Get Column Data By Name       ${row}       rejectedComment
        ${bin_reviewedBy_data}=          Get Column Data By Name       ${row}       reviewedBy
        ${bin_reviewed_date_data}=       Get Column Data By Name       ${row}       reviewedDate

        IF    '${bin_reviewed_date_data}' != 'None'
            ${bin_reviewed_date_data}=       Format Timestamp for API and Database      ${bin_reviewed_date_data}       ${True}
        END

        ${bin_latestServerNumber_data}=  Get Column Data By Name       ${row}       LatestServerNumber
        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType

        #Check if the Bin ID must be verified against the user's data.
        IF    ${binID_must_be_verified}
            ${binID_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_ID}'     '${binID_data}'
        END


        #Check if the Bin captured_date must be verified against the user's data.
        IF    ${captured_date_must_be_verified}

            ${captured_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_DATE}'     '${bin_capturedDate_data}'
        END

        #Check if the Bin captured_by must be verified against the user's data.
        IF    ${captured_by_must_be_verified}
            ${captured_by_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_BY}'     '${bin_capturedBy_data}'
        END

        #Check if the Action Date must be verified against the user's data
        IF    ${actionDate_must_be_verified}
            ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
        END

        #Check if the Action Date must be verified against the user's data
        IF    ${review_status_must_be_verified}
            ${review_status_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEW_STATUS}'     '${bin_review_status_data}'
        END

        #Check if the outcome must be verified against the user's data
        IF    ${outcome_must_be_verified}
            ${outcome_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${OUTCOME}'     '${bin_outcome_data}'
        END


        #Check if the toActionedBy must be verified against the user's data
        IF    ${toActionedBy_must_be_verified}
            ${toActionedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${TO_BE_ACTIONED_BY}'     '${bin_toActionedBy_data}'
        END

        #Check if the toActionedBy must be verified against the user's data
        IF    ${rejected_comment_must_be_verified}
            ${rejected_comment_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REJECTED_COMMENT}'     '${bin_rejected_comment_data}'
        END

        #Check if the toActionedBy must be verified against the user's data
        IF    ${reviewedBy_must_be_verified}
            ${reviewedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_BY}'     '${bin_reviewedBy_data}'
        END
        #Check if the toActionedBy must be verified against the user's data
        IF    ${reviewed_date_must_be_verified}
            ${reviewed_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_DATE}'     '${bin_reviewed_date_data}'
        END
        #Check if the toActionedBy must be verified against the user's data
        IF    ${latestServerNumber_must_be_verified}
            ${latestServerNumber_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${LATEST_SERVER_NUMBER}'     '${bin_latestServerNumber_data}'
        END

        #Check if the toActionedBy must be verified against the user's data
        ${bin_type_name_array}=     Split String    ${BIN_TYPE}     separator=,

        FOR    ${type_name}    IN    @{bin_type_name_array}
            Log    ${type_name}
            IF    ${binType_must_be_verified}
                ${binType_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${type_name}'     '${bin_binType_data}'
            END

            Run Keyword If    ${binType_verification_passed}
            ...    Exit For Loop
        END
        #The loop will be exited if all the parameters have been verified against the response
        Run Keyword If    ${binNumber_verified}
        ...    Exit For Loop
        #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
    END


    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the Database.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the Database.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${captured_date_must_be_verified}
        Run Keyword If    ${captured_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${captured_by_must_be_verified}
        Run Keyword If    ${captured_by_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${review_status_must_be_verified}
        Run Keyword If    ${review_status_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${outcome_must_be_verified}
        Run Keyword If    ${outcome_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${toActionedBy_must_be_verified}
        Run Keyword If    ${toActionedBy_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The TO_BE_ACTIONED_BY value: '${TO_BE_ACTIONED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the TO_BE_ACTIONED_BY value: '${OUTCOME}', for BIN Number: '${TO_BE_ACTIONED_BY}' was successful.
    END


    IF  ${rejected_comment_must_be_verified}
        Run Keyword If    ${rejected_comment_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${reviewedBy_must_be_verified}
        Run Keyword If    ${reviewedBy_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${reviewed_date_must_be_verified}
        Run Keyword If    ${reviewed_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${latestServerNumber_must_be_verified}
        Run Keyword If    ${latestServerNumber_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binType_must_be_verified}
        Run Keyword If    ${binType_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



# The below keywords are used to interact with the API POJOS

Create GetBinsToReview Instance
    [Arguments]    ${BASE_URL}  ${BIN_NUMBER}
    ${instance}=    Evaluate    GetBinsToReview.CreateRESTRequest('${BASE_URL}','${BIN_NUMBER}')    modules=GetBinsToReview
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords
Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}

    # Count the number of BINS returned
    ${bin_object_instace}=      Get the Bin Details
    ${item_count}=    Get Length    ${bin_object_instace}
    Log Many    ${item_count}
    Set Global Variable    ${TOTAL_NUMBER_OF_BINS}        ${item_count}

Get Response Status Code
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}


#Keyword to read successful response
Get the Bin Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bins_to_review_details
    RETURN    ${result}


Get the Bin Response field data
     [Arguments]     ${BINS_INSTNANCE}   ${METHOD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    ${METHOD_NAME}
    RETURN    ${result}

Get the Bin Number Details
     [Arguments]     ${BINS_INSTNANCE}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_bin_number
    RETURN    ${result}



