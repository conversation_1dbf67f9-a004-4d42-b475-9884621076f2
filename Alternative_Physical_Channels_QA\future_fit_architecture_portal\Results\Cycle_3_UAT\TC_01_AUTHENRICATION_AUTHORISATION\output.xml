<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-18T09:50:16.125640" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\TC_01_AUTHENRICATION_AUTHORISATION.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-18T09:50:19.116097" level="FAIL">Variable '${UPLOAD_TEST_STEPS}' not found.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="FAIL" start="2024-10-18T09:50:19.114080" elapsed="0.002017">Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2024-10-18T09:50:19.116097" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2024-10-18T09:50:19.116097" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" start="2024-10-18T09:50:19.116097" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2024-10-18T09:50:19.113039" elapsed="0.003948">Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</kw>
<test id="s1-t1" name="Login &amp; Logout - BU" line="40">
<tag>FFA_HEALTHCHECK</tag>
<status status="FAIL" start="2024-10-18T09:50:19.116987" elapsed="0.000000">Parent suite setup failed:
Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</test>
<test id="s1-t2" name="Login- BU- Calendar" line="41">
<tag>FFA_HEALTHCHECK</tag>
<status status="FAIL" start="2024-10-18T09:50:19.120983" elapsed="0.000000">Parent suite setup failed:
Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</test>
<test id="s1-t3" name="Login- BU- Dashboard" line="42">
<tag>FFA_HEALTHCHECK</tag>
<status status="FAIL" start="2024-10-18T09:50:19.121986" elapsed="0.000998">Parent suite setup failed:
Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</test>
<test id="s1-t4" name="Login- BU- Capture Campaign" line="43">
<tag>FFA_HEALTHCHECK</tag>
<status status="FAIL" start="2024-10-18T09:50:19.124984" elapsed="0.000000">Parent suite setup failed:
Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</test>
<doc>Testing future fit Authentication &amp; Authorisation</doc>
<status status="FAIL" start="2024-10-18T09:50:16.128618" elapsed="3.000366">Suite setup failed:
Variable '${UPLOAD_TEST_STEPS}' not found.</status>
</suite>
<statistics>
<total>
<stat pass="0" fail="4" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="4" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="0" fail="4" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-18T09:50:16.114614" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\TC_01_AUTHENRICATION_AUTHORISATION.robot' on line 22: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-18T09:50:16.114614" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\TC_01_AUTHENRICATION_AUTHORISATION.robot' on line 35: Singular section headers like '*Test Case*' are deprecated. Use plural format like '*** Test Cases ***' instead.</msg>
<msg time="2024-10-18T09:50:18.718248" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\common\GenericMethods.robot' on line 109: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
</errors>
</robot>
