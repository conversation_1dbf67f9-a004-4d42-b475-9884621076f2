*** Settings ***
# Author Name               : Thabo
# Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/GetAllBinTypes_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID
${TEST_CASE_ID}             RAC29a-TC-1046


*** Keywords ***
GET Request to get all Bin Types
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}   ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a GET Request to get all Bin Types without providing authentication token   ${BASE_URL}
    Then The service returns an expected status code                            ${EXPECTED_STATUS_CODE}


| *** Test Cases ***                                                                                                                                     |              *DOCUMENTATION*    	|    *BASE_URL*                  |   *EXPECTED_STATUS_CODE* |
| Capturer_Verify that the user receives an error when attempting to access the API endpoint without authentication   | GET Request to get all Bin Types | GET Request to get all Bin Types |                                 |       401               |
