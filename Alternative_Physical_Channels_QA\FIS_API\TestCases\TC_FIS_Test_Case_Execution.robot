*** Settings ***
Documentation  This test case execute and validates the results
Suite Setup    Set up environment variables

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                              SeleniumLibrary
Library                                              RequestsLibrary
Library                                              JSONLibrary
Library                                              Collections
Library                                              CSVLibrary
Library                                              ../Keywords/csvLibrary.py
Library                                              OperatingSystem
Library                                              String


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/keywords.robot 
Resource                                            ../keywords/SetEnvironmentVariables.robot


*** Keyword ***
Execute Test case and check for the status  
    [Arguments]    ${DOCUMENTATION}   ${ROW}    ${COLUMN}    ${AUTH_URL}    ${EXECUTION_URL}    ${STATUS_URL}    ${RESULTS_URL}    ${get_name}    ${EXECUTION_PAYLOAD}         
    Set Test Documentation  ${DOCUMENTATION}
    
    Given Test File Exists
 
    And Do a post Request for FIS Auth and validate the response code and response body   ${ROW}    ${COLUMN}    ${AUTH_URL}

    And Do a post Request for FIS execution and validate the response code and response body   ${ROW}    ${EXECUTION_PAYLOAD}    ${EXECUTION_URL}    ${get_name}

    #And Do a GET Request for the Status and validate the response code and response body   ${ROW}    ${STATUS_URL}
    Sleep    40s
    And Do a GET Request for the results and validate the response code and response body    ${ROW}    ${RESULTS_URL}

| *Test Case*                                                                                       |                                  *DOCUMENTATION*                            |    *ROW*   |     *COLUMN*    |    *AUTH_URL*   |    *EXECUTION_URL*    |    *STATUS_URL*    |    *RESULTS_URL*    |    *get_name*    |    *EXECUTION_PAYLOAD* |    
| Execute Test case and check for the status    | Execute Test case and check for the status        |        This test case execute and validates the results                     |    1       |    4            |    1            |    2                  |    3               |    5                |    8             |    6                   |

