*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-979




*** Keywords ***
Search for Bins on the View Entries page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_NUM}     ${BIN_TYPE}     ${ACTION_DATE}     ${CAPTURED_DATE}     ${CAPTURED_BY}     ${OUTCOME}     ${REVIEW_DATE}     ${REVIEWED_BY}     ${REVIEW_STATUS}     ${REJECTED_COMMENT}    ${SEARCH_DATA}
    Set Test Documentation  ${DOCUMENTATION}

    Given The Bin Number exists in the database                               ${BIN_NUM}
    When The user logs into Future Fit Architecture - Bin Tables portal                                            ${BASE_URL}
    And The User clicks Bins Menu
    And The user populates the search text                                                                          ${SEARCH_DATA}
    Then The returned Bin Numbers results must contain the characters of the search-data that was used to search    ${SEARCH_DATA}   ${BIN_NUM}   ${BIN_TYPE}   ${ACTION_DATE}   ${CAPTURED_DATE}   ${CAPTURED_BY}   ${OUTCOME}   ${REVIEW_DATE}   ${REVIEWED_BY}   ${REVIEW_STATUS}   ${REJECTED_COMMENT}

| *** Test Cases ***                                                                                                               |        *DOCUMENTATION*    		            |         *BASE_URL*                  |         *BIN_NUM*           |         *BIN_TYPE*           |         *ACTION_DATE*           |         *CAPTURED_DATE*     |         *CAPTURED_BY*      |         *OUTCOME*           |         *REVIEW_DATE*        |         *REVIEWED_BY*        |         *REVIEW_STATUS*      |         *REJECTED_COMMENT*     |         *SEARCH_DATA*         |
| Capturer_Verify Capturer Can Enter Full BIN Number in the Search Field              | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              123we            |
#| Search for a Bin on the View Entries page using the full bin number.               | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              123we            |
#| Search for a Bin on the View Entries page using the partial bin number.            | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              12               |
#| Search for a Bin on the View Entries page using the full bin type name.            | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              Token            |
#| Search for a Bin on the View Entries page using the partial bin type name.         | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              less             |
#| Search for a Bin on the View Entries page using the full outcome name.             | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              Added            |
#| Search for a Bin on the View Entries page using the partial outcome name.          | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              ded              |
#| Search for a Bin on the View Entries page using the full 'captured by' name.       | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              ab0283c          |
#| Search for a Bin on the View Entries page using the partial 'captured by' name.    | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              028              |
#| Search for a Bin on the View Entries page using the full 'review status' name.     | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              Pending          |
#| Search for a Bin on the View Entries page using the partial 'review status' name.  | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.   |           ${EMPTY}                  |          123we              |     Token, Contactless       |          Jan 24, 2025           |          Jan 9, 2025        |          ab0283c           |          Added              |          ${EMPTY}            |          ${EMPTY}            |          Pending             |          ${EMPTY}              |              ding             |
