*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/Review_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Verify and review Bins displayed on Review Bins Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                    ${BASE_URL}
    And The user access for granted for Bin Tables must correspond to the access of the logged in user      Admin
    When The User clicks Bins Menu
    And The user navigates to 'Review' Bins tab
    Then The Bins Review page must be displayed

| *** Test Cases ***                                                                                                                          |        *DOCUMENTATION*    		                                    |         *BASE_URL*                 |
| Approver_Access Approve Bin Function from Review Menu                                 | Verify and review Bins displayed on Review Bins Page   | Verifies bins against the database data and approves the Bin(s).    |           ${EMPTY}                 |
