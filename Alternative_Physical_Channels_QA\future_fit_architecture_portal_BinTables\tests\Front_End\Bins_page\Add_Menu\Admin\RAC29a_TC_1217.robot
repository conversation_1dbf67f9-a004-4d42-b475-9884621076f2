*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../keywords/front_end/View_Bins_Page.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify Adding a Bin with an Existing Bin Number
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${EXPECTED_ERROR_MESSAGE_DUPLICATE_BIN}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user retrieves an existing Bin Number from the View Menu 
    And The user navigates to 'Add' Bin tab
    And The user inputs the existing Bin Number into the Bin Number field    
    Then The user verifies that the expected error message is displayed    ${EXPECTED_ERROR_MESSAGE_DUPLICATE_BIN}

| *** Test Cases ***                                                                                 |        *DOCUMENTATION*                            |         *BASE_URL*                  |   *EXPECTED_ERROR_MESSAGE_DUPLICATE_BIN*     |      
| Admin_Add New BIN with Duplicated BIN Number   | Verify Adding a Bin with an Existing Bin Number   | Testing adding a in with an existing bin number   |           ${EMPTY}                  |          Duplicate Found!                    |       
