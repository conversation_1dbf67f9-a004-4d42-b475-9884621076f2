*** Settings ***
# Author Name               : Thabo
# Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

Library             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/BinTypeUpdate_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot



*** Variables ***
${SUITE NAME}               BIN Tables - Update Bin Type by ID


*** Keywords ***
Update Bin Type and Verify Response
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}    ${bintypeid}     ${name}    ${description}   ${EXPECTED_STATUS_CODE}


    IF    '${bintypeid}' == '${EMPTY}'
         ${bintypeid}=    Get the Bin Type id using Bin Type Name  ${name}
    END

    IF    '${description}' == '${EMPTY}' or '${description}' == ''
         ${description}=     Generate random word

    END

    Set Global Variable    ${GLOBAL_BIN_TYPE_ID}    ${bintypeid}
    Set Global Variable    ${GLOBAL_BIN_TYPE_NAME}    ${name}
    Set Global Variable    ${GLOBAL_BIN_TYPE_DESCRIPTION}    ${description}

    Given The User Populates the Update Bin Type JSON payload with   ${binTypeId}     ${name}    ${description}
    When The User sends the Update Bin Type API Request                ${BASE_URL}
    And The service returns an expected status code                 ${EXPECTED_STATUS_CODE}
    Then The updated Bin Type must exist in the database            

| *** Test Cases ***                                                                     |             *DOCUMENTATION*           |    *BASE_URL*            | *bintypeid*       | *name*    | *description*   |    *EXPECTED_STATUS_CODE*   |
| Update Bin Type and verify response            | Update Bin Type and Verify Response   | Update Bin Type and verify response   |                          |                   |  Token    |    ${EMPTY}     |      200                    |