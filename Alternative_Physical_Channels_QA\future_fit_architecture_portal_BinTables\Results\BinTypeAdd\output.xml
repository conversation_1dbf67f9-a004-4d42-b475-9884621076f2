<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-12-20T12:45:29.272553" rpa="false" schemaversion="5">
<suite id="s1" name="Bin Tables Controllers" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\tests\Controllers\BinTypes\BinTypeAdd.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-12-20T12:45:29.846149" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-12-20T12:45:29.845149" elapsed="0.001000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-12-20T12:45:29.845149" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-12-20T12:45:29.846149" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data/POST_CAMPAIGN_REGRESSION.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-12-20T12:45:29.846149" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-12-20T12:45:29.846149" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-12-20T12:45:29.846149" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-12-20T12:45:29.846149" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-12-20T12:45:29.846149" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-12-20T12:45:29.847151" level="INFO">Environment variable 'BASE_URL' set to value 'BIN_TABLES_API_DEV_BASE_URL'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-12-20T12:45:29.847151" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-12-20T12:45:29.846149" elapsed="0.001002"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-12-20T12:45:29.847151" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-12-20T12:45:29.847151" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-12-20T12:45:29.847151" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-12-20T12:45:29.845149" elapsed="0.002002"/>
</kw>
<test id="s1-t1" name="Add Bin Type and verify response" line="29">
<kw name="Add Bin Type and Verify Response">
<kw name="Given The User Sends a POST Request to Add a Bin Type" owner="BinTypeAdd_Keyword">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-12-20T12:45:29.848654" elapsed="0.000000"/>
</kw>
<msg time="2024-12-20T12:45:29.848654" level="INFO">${url_exists_on_env_var} = True</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2024-12-20T12:45:29.848654" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-12-20T12:45:29.848654" level="INFO">${BASE_URL} = BIN_TABLES_API_DEV_BASE_URL</msg>
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-12-20T12:45:29.848654" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-12-20T12:45:29.848654" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-12-20T12:45:29.848654" elapsed="0.000000"/>
</if>
<kw name="Get Property From File" owner="CommonFunctions">
<msg time="2024-12-20T12:45:29.851685" level="INFO">Property value fetched is:  https://bin-table-management.bin-table-management-dev.rbb-banking.270-nonprod.caas.absa.co.za</msg>
<msg time="2024-12-20T12:45:29.851685" level="INFO">${base_url} = https://bin-table-management.bin-table-management-dev.rbb-banking.270-nonprod.caas.absa.co.za</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2024-12-20T12:45:29.848654" elapsed="0.003031"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>${base_url}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-12-20T12:45:29.851685" elapsed="0.000973"/>
</kw>
<kw name="Create BinTypeAdd Instance" owner="BinTypeAdd_Keyword">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-12-20T12:45:29.852658" level="INFO">${bin_type_add} = &lt;BinTypeAdd.BinTypeAdd object at 0x000001AD1D6BBDA0&gt;</msg>
<var>${bin_type_add}</var>
<arg>BinTypeAdd.BinTypeAdd('${base_url}')</arg>
<arg>modules=BinTypeAdd</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-12-20T12:45:29.852658" elapsed="0.000000"/>
</kw>
<return>
<value>${bin_type_add}</value>
<status status="PASS" start="2024-12-20T12:45:29.852658" elapsed="0.000000"/>
</return>
<msg time="2024-12-20T12:45:29.852658" level="INFO">${bin_type_add} = &lt;BinTypeAdd.BinTypeAdd object at 0x000001AD1D6BBDA0&gt;</msg>
<var>${bin_type_add}</var>
<arg>${base_url}</arg>
<status status="PASS" start="2024-12-20T12:45:29.852658" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-12-20T12:45:30.141818" level="INFO">Final URL: https://bin-table-management.bin-table-management-dev.rbb-banking.270-nonprod.caas.absa.co.za/api/v1/bintables/BINtypes/add</msg>
<msg time="2024-12-20T12:45:30.141818" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'bin-table-management.bin-table-management-dev.rbb-banking.270-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg time="2024-12-20T12:45:30.143810" level="INFO">${response} = &lt;Response [400]&gt;</msg>
<var>${response}</var>
<arg>${bin_type_add}</arg>
<arg>create_bin_type</arg>
<arg>${BIN_TYPE_NAME}</arg>
<arg>${BIN_TYPE_DETAILS}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-12-20T12:45:29.852658" elapsed="0.291152"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>${response.text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-12-20T12:45:30.143810" elapsed="0.001971"/>
</kw>
<arg>${BASE_URL}</arg>
<arg>${BIN_TYPE_NAME}</arg>
<arg>${BIN_TYPE_DETAILS}</arg>
<status status="PASS" start="2024-12-20T12:45:29.848150" elapsed="0.297631"/>
</kw>
<kw name="When The service returns an expected status code" owner="BinTypeAdd_Keyword">
<kw name="Get Response Status Code" owner="BinTypeAdd_Keyword">
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-12-20T12:45:30.147733" level="FAIL">Variable '${REST_RESPONSE_INSTANCE}' not found.</msg>
<var>${result}</var>
<arg>${REST_RESPONSE_INSTANCE}</arg>
<arg>get_field</arg>
<arg>status_code</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="FAIL" start="2024-12-20T12:45:30.147733" elapsed="0.000000">Variable '${REST_RESPONSE_INSTANCE}' not found.</status>
</kw>
<return>
<value>${result}</value>
<status status="NOT RUN" start="2024-12-20T12:45:30.147733" elapsed="0.000000"/>
</return>
<var>${status_code}</var>
<status status="FAIL" start="2024-12-20T12:45:30.146730" elapsed="0.001003">Variable '${REST_RESPONSE_INSTANCE}' not found.</status>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${status_code}'</arg>
<arg>'${EXPECTED_STATUS_CODE}'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" start="2024-12-20T12:45:30.147733" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The 'Add' REST API call failed, the returned status is '${status_code}'</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2024-12-20T12:45:30.147733" elapsed="0.000000"/>
</kw>
<arg>${EXPECTED_STATUS_CODE}</arg>
<status status="FAIL" start="2024-12-20T12:45:30.145781" elapsed="0.001952">Variable '${REST_RESPONSE_INSTANCE}' not found.</status>
</kw>
<arg>Add Bin Type and verify response</arg>
<arg>Bin123</arg>
<arg>Details Bin 123</arg>
<arg>200</arg>
<status status="FAIL" start="2024-12-20T12:45:29.847151" elapsed="0.300582">Variable '${REST_RESPONSE_INSTANCE}' not found.</status>
</kw>
<tag>BIN_TABLES_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" start="2024-12-20T12:45:29.847151" elapsed="0.301567">Variable '${REST_RESPONSE_INSTANCE}' not found.</status>
</test>
<doc>Bin Tables Controllers Test Suite</doc>
<status status="FAIL" start="2024-12-20T12:45:29.275640" elapsed="0.873636"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">BIN_TABLES_HEALTHCHECK</stat>
<stat pass="0" fail="1" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="0" fail="1" skip="0" id="s1" name="Bin Tables Controllers">Bin Tables Controllers</stat>
</suite>
</statistics>
<errors>
<msg time="2024-12-20T12:45:29.313228" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\BinTypeAdd_Keyword.robot' on line 51: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-12-20T12:45:29.326233" level="ERROR">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\BinTypeAdd_Keyword.robot' on line 10: Library '..\..\keywords\controllers\resources\bins\DeleteBinTypeByID.py' does not exist.</msg>
<msg time="2024-12-20T12:45:29.739267" level="WARN">Imported library 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2024-12-20T12:45:29.753208" level="ERROR">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\BinTypeAdd_Keyword.robot' on line 14: Library 'BinTypeAdd' expected 1 argument, got 0.</msg>
<msg time="2024-12-20T12:45:29.756209" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\common\GenericMethods.robot' on line 83: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-12-20T12:45:29.756209" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\common\GenericMethods.robot' on line 88: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-12-20T12:45:29.826118" level="ERROR">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\BinTypeAdd_Keyword.robot' on line 19: Invalid resource file extension '.py'. Supported extensions are '.json', '.resource', '.rest', '.robot', '.rsrc', '.rst', '.tsv' and '.txt'.</msg>
</errors>
</robot>
