*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/UploadBin_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot



*** Variables ***
${SUITE NAME}               BIN Tables - Upload Bin(s) details to Bin Tables




*** Keywords ***
Upload Bin(s)
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}     ${EXPECTED_ERROR_MESSAGE}   &{BINS_DETAILS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User Populates the Upload Bin JSON payload with    &{BINS_DETAILS}
    When The User sends the Upload Bin API Request               ${BASE_URL}
    And The service returns an expected status code         ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed       ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                         |        *DOCUMENTATION*    		 |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |    *EXPECTED_ERROR_MESSAGE*                                                                      |                *BINS_DETAILS*                                                                                            |
| Upload(Add) Bins to the Bin Tables with the 'binNumber' field populated with '1'         | Upload Bin(s)   | Add Bin(s) to the Bin Tables      |                                     |         400                 |    Bin number length must be greater than or equals to two and smaller than or equals to twelve. |   bin1=1 | date1=2026-11-11 | binIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e          |
| Upload(Add) Bins to the Bin Tables with the 'binNumber' that is empty                    | Upload Bin(s)   | Add Bin(s) to the Bin Tables      |                                     |         500                 |    Value cannot be null. (Parameter 'The BinNumber must not be null or empty.')                  |   bin1= | date1=2026-11-11 | binIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e           |
| Upload(Add) Bins to the Bin Tables with the 'binNumber' that is already in the DB        | Upload Bin(s)   | Add Bin(s) to the Bin Tables      |                                     |         500                 |    An error occurred while saving the entity changes. See the inner exception for details.       |   bin1=3030397 | date1=2026-11-11 | binIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e    |
| Upload(Add) Bins to the Bin Tables with the 'actionDate' having incorrect date format    | Upload Bin(s)   | Add Bin(s) to the Bin Tables      |                                     |         400                 |    Unable to convert \"202-11-11\" to DateOnly.                                                  |   bin1=3030398 | date1=202-11-11 | binIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e     |
| Upload(Add) Bins to the Bin Tables with the 'binTypeIds' having incorrect data           | Upload Bin(s)   | Add Bin(s) to the Bin Tables      |                                     |         400                 |    The JSON value could not be converted to System.Guid. Path: $[0].binTypeIds[0]                |   bin1=3030398 | date1=2026-11-11 | binIds1=,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e     |
