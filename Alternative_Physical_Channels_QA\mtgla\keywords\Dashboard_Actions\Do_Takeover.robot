*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Navigation Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                            RequestsLibrary
Library                                            Collections
Library                                            String
Library                                             DatabaseLibrary
Library                                          ../../utility/DatabaseUtility.py
Library                                            DateTime

#***********************************PROJECT RESOURCES***************************************


*** Variables ***
${Do_Takeover_Query}                                    SELECT TOP (1000) [Id] ,[CustodianAB] ,[<PERSON>ustodianName] ,[ConductedByAB] ,[ConductedByName] ,[Comment] ,[ABAmount] ,[ABConfirm] ,[ABDifference] ,[CDMAmount] ,[CDMConfirm] ,[CDMDifference] ,[REAmount] ,[REConfirm] ,[REDifference] ,[PinAndCombo] ,[ConfirmForm] FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_Daily_AtmRecons] ORDER BY [Id] DESC;
${Do_Takeover_Locator}                                  //*[text()[normalize-space(.)='Do Takeover']]
${CONDUCTED_BY_AB_NUMBER}                               AB1234
${CONDUCTED_BY_AB_LOCATOR}                              //input[@id='ConductedByAB']
${CONDUCTED_BY_NAME}                                    AutomationUSER
${CONDUCTED_BY_NAME_LOCATOR}                            //input[@id='ConductedByName']
${SAVE_ACTION_BUTTON}                                   //button[@data-save='modal']   

${CUSTODIAN_NAME_LOCATOR}                               //input[@id='CustodianName']
${CUSTODIAN_AB_LOCATOR}                                 //input[@id='CustodianAB']
${CDM_Amount_LOCATOR}                                   //*[@id='CDMAmount']
${CDM_Confirm_Amount_LOCATOR}                           //*[@id='CDMConfirm']
${Recycler_Amount_LOCATOR}                              //*[@id='REAmount']
${Recycler_Confirm_Amount_LOCATOR}                      //*[@id='REConfirm']
${All_Bin_Amount_LOCATOR}                               //*[@id='ABAmount']
${All_Bin_Confirm_Amount_LOCATOR}                       //*[@id='ABConfirm']
${Remarks_LOCATOR}                                      //*[@id='Comment']
${Remark}                                               Automation Test in progress
${Checkbox_locator}                                     (//*[contains(@class,'checkmark')])[2]
${Confirmed_Front_End}                                  True
${Pin_And_Combo_Locator}                                (//*[contains(@class,'checkmark')])[1]
${Pin_And_Combo_Front_End}                                        True

#Amounts
${CDM_AMOUNT}                                           1500.0
${CDM_CONFIRMED_AMOUNT}                                 1000.0
${CDM_DIFFERENCE_AMOUNT}                                500.0
${ALL_BIN_AMOUNT}                                       1600.0
${CONFIRMED_ALL_BIN_AMOUNT}                             1450.0
${ALL_BIN_DIFFERENCE_AMOUNT}                            150.0
${RECYCLER_AMOUNT}                                      2000.0
${CONFIRMED_RECYCLER_AMOUNT}                            3000.0
${REYCLER_DIFFERENCE_AMOUNT}                            -1000.0    
${CONDUCTED_BY_AB_NUMBER}                               AB1234
${CONDUCTED_BY_AB_LOCATOR}                              //input[@id='ConductedByAB']
${CONDUCTED_BY_NAME}                                    AutomationUSER
${CONDUCTED_BY_NAME_LOCATOR}                            //input[@id='ConductedByName']

*** Keywords ***
the User Checks And Actions Do Takeover If It Exists
    Page Should Contain    Cost Centre Information
    Page Should Contain    Dashboard 

    ${element_exists}=    Run Keyword And Return Status    Element Should Be Visible    ${Do_Takeover_Locator}
    Run Keyword If    ${element_exists}    Perform Do Takeover
    Run Keyword Unless    ${element_exists}    Log To Console    "Do Takeover action is not available."

Perform Do Takeover
    Log To Console    "Do Takeover is available, proceeding with the action..."
    Click Element     ${Do_Takeover_Locator}
    Sleep    5s

    #GetCustodian Name and AB Number 
    ${CUSTODIAN_AB_NUMBER}=    Get Value    ${CUSTODIAN_AB_LOCATOR}
    Log To Console    Custodian AB Number:${CUSTODIAN_AB_NUMBER}
    Set Suite Variable    ${Custodian_AB_Number}
    
    ${CUSTODIAN_NAME}=    Get Value    ${CUSTODIAN_NAME_LOCATOR}
    Log To Console    Custodian Name:${CUSTODIAN_NAME}
    Set Suite Variable    ${CUSTODIAN_NAME}


    #Inputting Conducted By AB Number field
    Input Text    ${CONDUCTED_BY_AB_LOCATOR}    ${CONDUCTED_BY_AB_NUMBER}

    #Inputting Conducted By Name field 
    Input Text    ${CONDUCTED_BY_NAME_LOCATOR}    ${CONDUCTED_BY_NAME}

    #CDM AMOUNT 
    Sleep    2s
    Click Element    ${CDM_Amount_LOCATOR}
    Press Keys    ${CDM_Amount_LOCATOR}    BACKSPACE BACKSPACE BACKSPACE BACKSPACE BACKSPACE
    Input Text    ${CDM_Amount_LOCATOR}    ${CDM_AMOUNT}
    Sleep    2s

    Sleep    2s
    Click Element    ${CDM_Confirm_Amount_LOCATOR}
    Press Keys    ${CDM_Confirm_Amount_LOCATOR}    BACKSPACE BACKSPACE BACKSPACE BACKSPACE BACKSPACE
    Input Text    ${CDM_Confirm_Amount_LOCATOR}    ${CDM_CONFIRMED_AMOUNT}
    Sleep    2s

    #RECYCLER AMOUNT 
    Sleep    2s
    Click Element    ${Recycler_Amount_LOCATOR}
    Press Keys    ${Recycler_Amount_LOCATOR}    BACKSPACE BACKSPACE BACKSPACE BACKSPACE BACKSPACE
    Input Text    ${Recycler_Amount_LOCATOR}    ${RECYCLER_AMOUNT}
    Sleep    2s

    Sleep    2s
    Click Element    ${Recycler_Confirm_Amount_LOCATOR}
    Press Keys    ${Recycler_Confirm_Amount_LOCATOR}    BACKSPACE BACKSPACE BACKSPACE BACKSPACE BACKSPACE
    Input Text    ${Recycler_Confirm_Amount_LOCATOR}    ${CONFIRMED_RECYCLER_AMOUNT}
    Sleep    2s

    #ALL BIN AMOUNT  
    Sleep    2s
    Click Element    ${All_Bin_Amount_LOCATOR}
    Press Keys    ${All_Bin_Amount_LOCATOR}    BACKSPACE BACKSPACE BACKSPACE BACKSPACE BACKSPACE
    Input Text    ${All_Bin_Amount_LOCATOR}    ${ALL_BIN_AMOUNT}
    Sleep    2s

    Sleep    2s
    Click Element    ${All_Bin_Confirm_Amount_LOCATOR}
    Press Keys    ${All_Bin_Confirm_Amount_LOCATOR}    BACKSPACE BACKSPACE BACKSPACE BACKSPACE BACKSPACE
    Input Text    ${All_Bin_Confirm_Amount_LOCATOR}    ${CONFIRMED_ALL_BIN_AMOUNT}
    Sleep    2s

    #Select Pin And Combo Box 
    Click Element    ${Pin_And_Combo_Locator}

    #Input Remarks 
    Input Text    ${Remarks_LOCATOR}    ${Remark}

    #Checkbox: Correctly inserted figures 
    Click Element    ${Checkbox_locator}

    #Save Do Takeover
    Click element    ${SAVE_ACTION_BUTTON}
 
    #DATABASE VALIDATION
    ${Database_Do_Takeover}=    DatabaseUtility.Execute Sql Query        ${Do_Takeover_Query}    
    Log To Console    ${Database_Do_Takeover}

    #Extract database data
    #Custodian AB Number 
    ${Do_Takeover_Custodian_AB_Number_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Custodian_AB_Number_DB}=    Get From Dictionary    ${Do_Takeover_Custodian_AB_Number_DB}    CustodianAB
    Log To Console    Custodian database AB Number:${Do_Takeover_Custodian_AB_Number_DB}

    #Custodian Name
    ${Do_Takeover_Custodian_Name_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Custodian_Name_DB}=    Get From Dictionary    ${Do_Takeover_Custodian_Name_DB}    CustodianName
    Log To Console    Custodian database Name:${Do_Takeover_Custodian_Name_DB}

    #Conducted By AB
    ${Do_Takeover_Conducted_By_AB_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Conducted_By_AB_DB}=    Get From Dictionary    ${Do_Takeover_Conducted_By_AB_DB}    ConductedByAB
    Log To Console    Conducted By AB database:${Do_Takeover_Conducted_By_AB_DB}

    #Conducted By Name 
    ${Do_Takeover_Conducted_By_Name_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Conducted_By_Name_DB}=    Get From Dictionary    ${Do_Takeover_Conducted_By_Name_DB}    ConductedByName
    Log To Console    Conducted By Name database:${Do_Takeover_Conducted_By_Name_DB}

    #Pin And Combo Extraction
    ${Do_Takeover_Pin_And_Combo_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Pin_And_Combo_DB}=    Get From Dictionary    ${Do_Takeover_Pin_And_Combo_DB}    PinAndCombo
    Log To Console    Pin And Combo database:${Do_Takeover_Pin_And_Combo_DB}
    ${Do_Takeover_Pin_And_Combo_DB_STRING}=    Evaluate    str(${Do_Takeover_Pin_And_Combo_DB})
    Log To Console    Pin And Combo Database (String): ${Do_Takeover_Pin_And_Combo_DB_STRING}

    #Remark
    ${Do_Takeover_Remark_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Remark_DB}=    Get From Dictionary    ${Do_Takeover_Remark_DB}    Comment
    Log To Console    Bi Weekly Remark database:${Do_Takeover_Remark_DB}

    #All Bin Amount 
    ${Do_Takeover_AB_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_AB_Amount_DB}=    Get From Dictionary    ${Do_Takeover_AB_Amount_DB}    ABAmount
    Log To Console    All Bin Amount Database:${Do_Takeover_AB_Amount_DB}
    ${Do_Takeover_AB_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_AB_Amount_DB})
    Log To Console    All Bin Amount Database (String): ${Do_Takeover_AB_Amount_DB_STRING}

    #Confirmed All Bin Amount 
    ${Do_Takeover_Confirmed_AB_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Confirmed_AB_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Confirmed_AB_Amount_DB}    ABConfirm
    Log To Console    Confirmed All Bin Amount Database:${Do_Takeover_Confirmed_AB_Amount_DB}
    ${Do_Takeover_Confirmed_AB_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Confirmed_AB_Amount_DB})
    Log To Console    Confirmed All Bin Amount Database (String): ${Do_Takeover_Confirmed_AB_Amount_DB_STRING}

    #All Bin Difference Amount
    ${Do_Takeover_Difference_AB_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Difference_AB_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Difference_AB_Amount_DB}    ABDifference
    Log To Console     All Bin Amount Difference Database:${Do_Takeover_Difference_AB_Amount_DB}
    ${Do_Takeover_Difference_AB_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Difference_AB_Amount_DB})
    Log To Console    All Bin Amount Difference Database (String): ${Do_Takeover_Difference_AB_Amount_DB_STRING}

    #CDM Amount 
    ${Do_Takeover_CDM_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_CDM_Amount_DB}=    Get From Dictionary    ${Do_Takeover_CDM_Amount_DB}    CDMAmount
    Log To Console    CDM Amount Database:${Do_Takeover_CDM_Amount_DB}
    ${Do_Takeover_CDM_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_CDM_Amount_DB})
    Log To Console    CDM Amount Database (String): ${Do_Takeover_CDM_Amount_DB_STRING}

    #Confirmed CDM Amount 
    ${Do_Takeover_Confirmed_CDM_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Confirmed_CDM_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Confirmed_CDM_Amount_DB}    CDMConfirm
    Log To Console    Confirmed CDM Amount Database:${Do_Takeover_Confirmed_CDM_Amount_DB}
    ${Do_Takeover_Confirmed_CDM_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Confirmed_CDM_Amount_DB})
    Log To Console    Confirmed CDM Amount Database (String): ${Do_Takeover_Confirmed_CDM_Amount_DB_STRING}

    #CDM Difference Amount
    ${Do_Takeover_Difference_CDM_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Difference_CDM_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Difference_CDM_Amount_DB}    CDMDifference
    Log To Console     CDM Amount Difference Database:${Do_Takeover_Difference_CDM_Amount_DB}
    ${Do_Takeover_Difference_CDM_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Difference_CDM_Amount_DB})
    Log To Console    CDM Amount Difference Database (String): ${Do_Takeover_Difference_CDM_Amount_DB_STRING}

    #Recycler Amount 
    ${Do_Takeover_Recycler_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Recycler_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Recycler_Amount_DB}    REAmount
    Log To Console    Recycler Amount Database:${Do_Takeover_Recycler_Amount_DB}
    ${Do_Takeover_Recycler_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Recycler_Amount_DB})
    Log To Console    Recycler Amount Database (String): ${Do_Takeover_Recycler_Amount_DB_STRING}

    #Confirmed Recycler Amount 
    ${Do_Takeover_Confirmed_Recycler_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Confirmed_Recycler_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Confirmed_Recycler_Amount_DB}    REConfirm
    Log To Console    Confirmed Recycler Amount Database:${Do_Takeover_Confirmed_Recycler_Amount_DB}
    ${Do_Takeover_Confirmed_Recycler_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Confirmed_Recycler_Amount_DB})
    Log To Console    Confirmed Recycler Amount Database (String): ${Do_Takeover_Confirmed_Recycler_Amount_DB_STRING}

    #Recycler Difference Amount
    ${Do_Takeover_Difference_Recycler_Amount_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Difference_Recycler_Amount_DB}=    Get From Dictionary    ${Do_Takeover_Difference_Recycler_Amount_DB}    REDifference
    Log To Console     Recycler Amount Difference Database:${Do_Takeover_Difference_Recycler_Amount_DB}
    ${Do_Takeover_Difference_Recycler_Amount_DB_STRING}=   Evaluate    str(${Do_Takeover_Difference_Recycler_Amount_DB})    modules=decimal
    Log To Console    Recycler Amount Difference Database (String): ${Do_Takeover_Difference_Recycler_Amount_DB_STRING}

    #Confirm Form (Correctly inserted figueres checkbox)
    ${Do_Takeover_Confirm_DB}=    Get From List    ${Database_Do_Takeover}    0
    ${Do_Takeover_Confirm_DB}=    Get From Dictionary    ${Do_Takeover_Confirm_DB}    ConfirmForm
    Log To Console     Confirm form Database:${Do_Takeover_Confirm_DB}
    ${Do_Takeover_Confirm_DB_STRING}=    Evaluate    str(${Do_Takeover_Confirm_DB})
    Log To Console    Confirm form Database (String): ${Do_Takeover_Confirm_DB_STRING}

    

    #SUBMITTED FRONT END DO BI WEEKLY BALANCING MUST MATCH THE DATABASE 
    Should Be Equal        ${CUSTODIAN_AB_NUMBER}           ${Do_Takeover_Custodian_AB_Number_DB}
    Should Be Equal        ${CUSTODIAN_NAME}                ${Do_Takeover_Custodian_Name_DB}
    Should Be Equal        ${CONDUCTED_BY_AB_NUMBER}        ${Do_Takeover_Conducted_By_AB_DB}
    Should Be Equal        ${CONDUCTED_BY_NAME}             ${Do_Takeover_Conducted_By_Name_DB}

    Should Be Equal        ${CDM_AMOUNT}                    ${Do_Takeover_CDM_Amount_DB_STRING}
    Should Be Equal        ${CDM_CONFIRMED_AMOUNT}          ${Do_Takeover_Confirmed_CDM_Amount_DB_STRING} 
    Should Be Equal        ${CDM_DIFFERENCE_AMOUNT}         ${Do_Takeover_Difference_CDM_Amount_DB_STRING}

    Should Be Equal        ${RECYCLER_AMOUNT}               ${Do_Takeover_Recycler_Amount_DB_STRING}
    Should Be Equal        ${CONFIRMED_RECYCLER_AMOUNT}     ${Do_Takeover_Confirmed_Recycler_Amount_DB_STRING}
    Should Be Equal        ${REYCLER_DIFFERENCE_AMOUNT}     ${Do_Takeover_Difference_Recycler_Amount_DB_STRING}

    Should Be Equal        ${ALL_BIN_AMOUNT}                ${Do_Takeover_AB_Amount_DB_STRING}
    Should Be Equal        ${CONFIRMED_ALL_BIN_AMOUNT}      ${Do_Takeover_Confirmed_AB_Amount_DB_STRING}
    Should Be Equal        ${ALL_BIN_DIFFERENCE_AMOUNT}     ${Do_Takeover_Difference_AB_Amount_DB_STRING}

    Should Be Equal        ${Pin_And_Combo_Front_End}       ${Do_Takeover_Pin_And_Combo_DB_STRING}
    Should Be Equal        ${Remark}                        ${Do_Takeover_Remark_DB}
    Should Be Equal        ${Confirmed_Front_End}           ${Do_Takeover_Confirm_DB_STRING}

    Sleep    5s
    Log to console    Do Takeover Action successfully saved to the database. The front end data match the data on the database!