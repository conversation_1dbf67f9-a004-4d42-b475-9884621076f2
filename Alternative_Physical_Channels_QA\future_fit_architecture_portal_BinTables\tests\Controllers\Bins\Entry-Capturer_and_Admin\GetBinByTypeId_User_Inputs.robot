*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinById_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using a Bin ID on the GetBinById Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_NUMBER}  ${EXPECTED_STATUS_CODE}   ${BIN_ID}  ${CAPTURED_DATE}  ${CAPTURED_BY}  ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to search for bins using the Bin Id      ${BASE_URL}      ${BIN_ID}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    And The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}
    Then The Bin Number details must exist on the Bin Database                  ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}


| *** Test Cases ***                                                                                                                                                         |        *DOCUMENTATION*    		            |         *BASE_URL*                  |     *BIN_NUMBER*      |    *EXPECTED_STATUS_CODE*   |                *BIN_ID*                   |              *CAPTURED_DATE*                            |       *CAPTURED_BY*            |       *ACTION_DATE*            |       *REVIEW_STATUS*          |       *OUTCOME*            |       *TO_BE_ACTIONED_BY*      |       *REJECTED_COMMENT*            |       *REVIEWED_BY*            |       *REVIEWED_DATE*            |       *LATEST_SERVER_NUMBER*           |                             *BIN_TYPE_IDS*                                            |       *BIN_TYPE_NAMES*           |
| Search for a Bin Numbered '7004535' using GetBinById Controller and verify its details and Bin Type   | Search for a Bin using a Bin ID on the GetBinById Controller       | Search Bin by Number on the GetBinById API   |                                     |       7004535         |           200               |   59ef7be3-c73b-4648-91a1-568064a700ae    |             2024-11-12T06:37:03.694301                  |        ab0283c                 |       2027-11-11               |          Pending               |        Added               |        None                    |             None                    |          None                  |           None                   |                                        |          7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e, a7ff7c25-057b-461b-9fa1-50d471202b52   |          On-Us, Contactless      |
