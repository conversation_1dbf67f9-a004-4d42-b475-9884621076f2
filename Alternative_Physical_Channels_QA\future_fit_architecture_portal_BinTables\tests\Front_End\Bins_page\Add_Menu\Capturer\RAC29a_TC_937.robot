*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify BIN Number field Max Length 12
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_NAME}' == '${EMPTY}' or '${BIN_NAME}' == ''
         ${random_word}=     Generate random bin name
         ${BIN_NAME}=   Set Variable     ${random_word}
         ${BIN_NAME}=    Get Substring    ${BIN_NAME}    0    14    #Only 12 Characters will be accepted for test to pass.  
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The User populates a 14 character Bin name    ${BIN_NAME} 
    And The user verfies that the first 12 characters of the 14 character Bin name was accepted 


| *** Test Cases ***                                                                                        |        *DOCUMENTATION*                   |         *BASE_URL*                  |         *BIN_NAME*          |         *BIN_TYPE_NAME*        |         *BIN_ACTION_DATE*        |
| 	Capturer_BIN Number Field Input Validation (Max Length 12)   | Verify BIN Number field Max Length 12    | Testing the entries added message for added bin   |           ${EMPTY}                  |            ${EMPTY}         |         Domestic               |         06-28-2027               |