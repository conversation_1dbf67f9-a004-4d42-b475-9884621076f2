*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Edit_or_Delete_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify Bin Types displayed on the Edit/Delet page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user navigates to 'Edit/Delete' Bin Type tab
    Then The bin types displayed on the page must exist in the database

| *** Test Cases ***                                                                                     |        *DOCUMENTATION*    		                     |         *BASE_URL*                  |
| Verify Bin Types against the DB data.            | Verify Bin Types displayed on the Edit/Delet page   | Verify Bin Types displayed on the Edit/Delete page.   |           ${EMPTY}                  |
