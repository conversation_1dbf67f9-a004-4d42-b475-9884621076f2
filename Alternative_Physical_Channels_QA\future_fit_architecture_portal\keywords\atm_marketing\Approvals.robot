*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation  Test the Capture Campaign page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             String
Library                                             ../../utility/Utility.py
Library                                             ../../utility/UiActions.py

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/atm_marketing/CalendarView.robot

*** Variables ***
${TOTAL_CAMPAIGNS}                                            0
${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}                SELECT COUNT(*) FROM ATM_Marketing.Campaign WHERE isActive = '1';
${START_DATE_XPATH}                                           //button[contains(@class, 'mat-calendar-body-range-start') and @aria-pressed='true']
${END_DATE_XPATH}                                             //button[contains(@class, 'mat-calendar-body-range-end') and @aria-pressed='true']
${NAVIGATE_PREVIOUS}                                          //button[contains(@class, 'mat-calendar-previous-button')]
${NAVIGATE_NEXT}                                              //button[contains(@class, 'mat-calendar-next-button')]
${ONE_DAY_XPATH}                                              //button[@type='button' and contains(@class, 'mat-calendar-body-active') and @aria-pressed='true' and .//div[contains(@class, 'mat-calendar-body-selected')]]
${CAMPAIGN_IMAGES_XPATH}                                      //*[@id="cdk-step-content-0-1"]//img
${LANGUAGE_XPATH}                                             //mat-chip[contains(text(), 'Language:')] 
${IMAGE_XPATH}                                                //*[@id="cdk-step-content-0-1"]//img
${NEXT_BUTTON_XPATH}                                          //button[contains(@class, 'mat-paginator-navigation-next') and not(@disabled)]
${PREVIOUS_BUTTON_XPATH}                                      //button[contains(@class, 'mat-paginator-navigation-previous')]
${SEARCH_RESULT_XPATH}                                        //tr[contains(@class, 'mat-row')]
${APPROVED_BY_XPATH}                                          //tr[contains(@class, 'mat-row')]//td[contains(@class, 'mat-column-ApprovedBy')][normalize-space(text()) != '']
${DROPDOWN_XPATH}                                             //mat-select[@id='mat-select-0']
${OPTION_10_XPATH}                                            //span[contains(text(), '10')]
${OPTION_100_XPATH}                                           //span[contains(text(), '100')]
${Un-Approved_Campaign}                                       //tr[@role='row' and td[6]//mat-chip[contains(@class, 'not-approved')]]//fa-icon[@mattooltip='Preview']
${APPROVE_CAMPAIGN_BUTTON_XPATH}                              //button[contains(@class, 'mat-button') and contains(span/text(), 'Approve Campaign')]
${UNAPPROVED_CAMPAIGN_ROW_XPATH}                              //tr[@role='row' and td[6]//mat-chip[contains(@class, 'not-approved')]][1]
${Delete_Campaign_Button}                                     //td[@role='cell' and contains(@class, 'mat-column-approve')]//fa-icon[@mattooltip='Delete']
${Deactivate_Campaign_Button}                                 //button[contains(@class, 'approve') and contains(., 'Deactivate Campaign')]
${first_campaign_id}                                          //tr[@role='row' and not(ancestor::thead)]//td[2][1]
${OPTION_BASE_XPATH}                                          //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='{0}']
${ROW_COUNT_XPATH}                                            //tr[@role='row']
${DEFAULT_VALUE}                                              10
@{OPTIONS}                                                    5    10    25    100

*** Keywords ***
The user previews and validates the Next Button on Approval Preview 
    ${more_pages}    Set Variable    True
    ${TOTAL_CAMPAIGNS}    Set Variable    0  #Initialize total campaign counter on approvals page

    WHILE    ${more_pages} == True
        ${campaigns}=    SeleniumLibrary.Get Element Count    xpath=//*[contains(@class,'cdk-table')]//tbody//tr

        #Update total campaigns count
        ${TOTAL_CAMPAIGNS}=    Evaluate    ${TOTAL_CAMPAIGNS} + ${campaigns}  #Count all campaigns on the page

        #Limit to 1 campaign per page for interaction
        ${campaigns_to_loop}    Evaluate    min(${campaigns}, 1)  

        Log    Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}

        FOR    ${i}    IN RANGE    ${campaigns_to_loop}
            #Click Preview button for each campaign
            Click Element    xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']    

            #Click Next Button on preview to validate the functionality
            Wait for spinner to disapear
            Wait Until Element Is Not Visible    xpath=//div[contains(@class, 'overlay')]
            Click Button    xpath=//button[@type='submit']
            Log To Console    ----NEXT BUTTON VALIDATION SUCCESSFUL----

            #Check if the Next Button on campaign Preview allows proceeding
            Sleep    2s
            Page Should Contain    Language:

            #Click previous button
            Wait for spinner to disapear
            Wait Until Element Is Visible    xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]    timeout=5s
            Element Should Be Enabled        xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]
            Click Button                     xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]
            Sleep    2s

            #Close the campaign preview with close button
            Scroll Element Into View    xpath=//mat-dialog-container
            Wait Until Element Is Visible    xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]    timeout=5s
            Click Button                     xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]

        END

        #Check if the next page of campaigns is enabled
        ${more_pages}=    Check If Next Page Button Is Enabled

        Wait Until Element Is Visible    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]    timeout=5s
        Scroll Element Into View    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]

        Run Keyword If    ${more_pages}    Scroll Element Into View    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]
        Run Keyword If    ${more_pages}    Click Next Page Button    

        Sleep    2s    
    END

    Log               Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS} 
    Log To Console    ----Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}----
    
    #Get Approval List Campaign from Database
    ${db_type}=   Set Variable   'MYSQL'

    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}=  Set Variable        ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}
    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}=      Execute SQL Query  ${db_type}  ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}    True
    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}=       Get From Dictionary    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}    COUNT(*) 

    Log               Total campaigns on the approval list (database): ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}  
    Log To Console    ----Total campaigns on the database approval list: ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}----

    Log               Test completed. The Next button on the approval preview functions as expected.  
    Log To Console    ----Test completed. The Next button on the approval preview functions as expected.----  

    #Compare front end campaign count to the database
    Should Be Equal As Numbers    ${TOTAL_CAMPAIGNS}    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}
    Log To Console    ----The front-end and database campaign counts on Approval List match----

Check If Next Page Button Is Enabled
    [Documentation]    Returns True if the "Next" button is enabled.
    Wait Until Element Is Visible    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]
    ${is_disabled}=    SeleniumLibrary.Get Element Attribute    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]    disabled
    ${is_enabled}=    Evaluate    '${is_disabled}' == 'None'  
    Log    Next page button enabled: ${is_enabled}
    [Return]    ${is_enabled}

Click Next Page Button
    [Documentation]    
    Execute JavaScript    document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()

The user previews and validates the Close Button on Approval Preview
    ${more_pages}    Set Variable    True
    ${TOTAL_CAMPAIGNS}    Set Variable    0  #Initialize total campaign counter on approvals page

    WHILE    ${more_pages} == True
        ${campaigns}=    SeleniumLibrary.Get Element Count    xpath=//*[contains(@class,'cdk-table')]//tbody//tr

        #Update total campaigns count
        ${TOTAL_CAMPAIGNS}=    Evaluate    ${TOTAL_CAMPAIGNS} + ${campaigns}  #Count all campaigns on the page

        #Limit to 1 campaign per page for interaction
        ${campaigns_to_loop}    Evaluate    min(${campaigns}, 1)  

        Log    Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}

        FOR    ${i}    IN RANGE    ${campaigns_to_loop}
            #Click Preview button for each campaign
            Click Element    xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']    

            #Close the campaign preview with close button to validate the functionality
            Scroll Element Into View    xpath=//mat-dialog-container
            Wait Until Element Is Visible    xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]    timeout=5s
            Sleep    5s
            Click Button                     xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]
            Log To Console    ----CLOSE BUTTON VALIDATION SUCCESSFUL----
        END

        #Check if the next page of campaigns is enabled
        ${more_pages}=    Check If Next Page Button Is Enabled

        Wait Until Element Is Visible    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]    timeout=10s
        Scroll Element Into View    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]

        Run Keyword If    ${more_pages}    Scroll Element Into View    xpath=//*[contains(@class,'mat-paginator-container')]//button[2]
        Run Keyword If    ${more_pages}    Click Next Page Button    

        Sleep    2s    
    END

    Log    Total campaigns identified on the front-end: ${TOTAL_CAMPAIGNS}  
    Log To Console    ----Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}----  


    #Get the campaign approval list count from the database  
    ${db_type}=     Set Variable    'MYSQL'  

    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}=     Set Variable    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}  
    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}=     Execute SQL Query    ${db_type}    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}    True  
    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}=     Get From Dictionary    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}    COUNT(*)  

    Log    Total campaigns on the approval list (database): ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}  
    Log To Console    ----Total campaigns on the database approval list: ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}----  

    Log    Test completed. The Close button on the approval preview functions as expected.  
    Log To Console    ----Test completed. The Close button on the approval preview functions as expected.----  

    #Validate that the front-end and database campaign counts match  
    Should Be Equal As Numbers    ${TOTAL_CAMPAIGNS}    ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}  
    Log To Console    ----The front-end and database campaign counts match----  


The user navigates to the Campaign Approvals page and previews a single campaign  
    #Initialize total campaigns counter  
    ${TOTAL_CAMPAIGNS}    Set Variable    0  

    #Get the total number of campaigns available  
    ${campaigns}=    Get Element Count    xpath=//*[contains(@class,'cdk-table')]//tbody//tr  

    #Ensure at least one campaign is available for preview  
    Run Keyword If    ${campaigns} == 0    Fail    No campaigns found to preview.  

    #Update the total campaigns count  
    ${TOTAL_CAMPAIGNS} =    Evaluate    ${TOTAL_CAMPAIGNS} + ${campaigns}  

    Log    Total campaigns available: ${TOTAL_CAMPAIGNS}  
    Log To Console    ----Total campaigns available: ${TOTAL_CAMPAIGNS}----

    #Preview the first available campaign  
    Wait Until Element Is Visible    xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']    timeout=10s
    Click Element    xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']  
    Log    Previewed the first campaign successfully.  
    Log To Console    ----Previewed the first campaign successfully.----

        #Validate the Campaign details on the first page of preview  
        ${Campaign_Name}=    Get Element Attribute    xpath=//input[@name='campaignName']    value  
        Should Not Be Empty    ${Campaign_Name}    Campaign name should not be empty.  

        Log    Campaign Name: ${Campaign_Name}  
        Log To Console    ----Campaign Name: ${Campaign_Name}----

        ${campaign_by} =    Get Element Attribute    xpath=//input[@name='campaignBy']    value  
        Should Not Be Empty    ${campaign_by}    Campaign owner should not be empty.
        Log    Campaign By: ${campaign_by}  
        Log To Console    ----Campaign By: ${campaign_by}----

        ${Region_Target}=    Get Element Attribute    xpath=//input[@name='marketingType']    value  
        Should Not Be Empty    ${Region_Target}    Region target should not be empty.  
        Log    Region Target: ${Region_Target}  
        Log To Console    ----Region Target: ${Region_Target}----

        ${Target_Details}=    Get Element Attribute    xpath=//input[@name='screenNumber']    value  
        Should Not Be Empty    ${Target_Details}    Target details should not be empty.  
        Log    Target Details: ${Target_Details}  
        Log To Console    ----Target Details: ${Target_Details}----

        ${Receiver_Device_Type}=    Get Element Attribute    xpath=//input[@name='receiverDeviceType']    value  
        Should Not Be Empty    ${Receiver_Device_Type}    Receiver device type should not be empty.  
        Log    Receiver Device Type: ${Receiver_Device_Type}  
        Log To Console    ----Receiver Device Type: ${Receiver_Device_Type}----

        #Navigate to second preview page
        Wait for spinner to disapear
        Wait Until Element Is Not Visible    xpath=//div[contains(@class, 'overlay')]
        Click Button    xpath=//button[@type='submit']
        Page Should Contain    Language:
        Sleep    5s

        #Get all images using the defined XPath
        ${images}=    Get WebElements    ${CAMPAIGN_IMAGES_XPATH}

        #Count the number of images
        ${image_count} =    Get Length    ${images}
    
        #Log the total number of images
        Log    Total number of images found: ${image_count}
        
        #Get Image details:
        FOR    ${index}    IN RANGE        ${image_count}
            ${language_text}=    Get Text    ${LANGUAGE_XPATH}
            Log    Language for Image ${index}: ${language_text}
        END

        Log    Campaign Name: ${Campaign_Name}  
        Log    Campaign By: ${campaign_by}  
        Log    Region Target: ${Region_Target}  
        Log    Target Details: ${Target_Details} 
        Log    Receiver Device Type: ${Receiver_Device_Type}
        Log    Total number of images found: ${image_count}
        Log    Image ${index}: ${language_text}
        
        Log To Console   ----CAMPAIGN PREVIEW DETAILS----
        Log To Console   Campaign Name: ${Campaign_Name}  
        Log To Console   Campaign By: ${campaign_by}  
        Log To Console   Region Target: ${Region_Target}  
        Log To Console   Target Details: ${Target_Details} 
        Log To Console   Receiver Device Type: ${Receiver_Device_Type}
        Log To Console   Total number of images found: ${image_count}
        Log To Console   Image ${index}: ${language_text}

    #Click Previous button on campaign to navigate back
    Scroll Element Into View    xpath=//button[span[contains(text(),'Previous')]]
    Click Element    xpath=//button[span[contains(text(),'Previous')]]
    Element Should Be Visible    xpath=//mat-dialog-container  

    #Close Campaign  
    Scroll Element Into View    xpath=//mat-dialog-container  
    Wait Until Element Is Visible    xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]    timeout=5s  
    Sleep    5s  
    Click Button    xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]  
    Log To Console    ----CLOSED CAMPAIGN----  



The user inputs a campaign name on the Search field
    Sleep    5s
    #Get Campaign Name
    ${Campaign_Name}    Get Text    xpath=//td[contains(@class, 'mat-column-campaignName')]
    Log    Campaign Name retrieved: ${Campaign_Name}

    #Click search bar and input campaign name 
    ${Search_Bar_XPATH}=    Get WebElement    xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]
    Click Element       ${Search_Bar_XPATH}
    Input Text    ${Search_Bar_XPATH}    ${Campaign_Name}
    Sleep    2s
    Set Suite Variable    ${Campaign_Name}
    Set Suite Variable   ${Search_Bar_XPATH}

The user verifies the search results returned by Campaign Name 
    Wait Until Element Is Visible    ${SEARCH_RESULT_XPATH}    timeout=10s
    ${search_results}=    Get Text    xpath=//td[contains(text(), '${Campaign_Name}')]
    Should Contain    ${search_results}    ${Campaign_Name}

    #Verify if any rows are returned
    ${result_count}=    Get Element Count    ${SEARCH_RESULT_XPATH}
    Should Be True    ${result_count} > 0    msg=No search results found!

    Log               ----Search completed successfully using campaign name: ${Campaign_Name}.----
    Log To Console    ----Search completed successfully using campaign name: ${Campaign_Name}.----


The user inputs an Approved By user on the Search field
    #Expand Page
    Set Focus To Element   ${dropdown_xpath}
    Click Element    ${dropdown_xpath}
    Click Element    ${option_100_xpath}

    Sleep    5s
    #Get Campaign by
    Set Focus To Element    ${APPROVED_BY_XPATH}
    ${Approved_By_User}    Get Text    ${APPROVED_BY_XPATH}
    Log    Campaign Name retrieved: ${Approved_By_User}

    #Click search bar and input campaign By
    ${Search_Bar_XPATH}=    Get WebElement    xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]
    Sleep    2s
    Set Focus To Element    ${Search_Bar_XPATH}
    Sleep    3s
    Click Element       ${Search_Bar_XPATH}
    Input Text    ${Search_Bar_XPATH}    ${Approved_By_User}
    Sleep    2s
    Set Suite Variable    ${Approved_By_User}
    Set Suite Variable   ${Search_Bar_XPATH}

The user verifies the search results returned by the Approved By user
    Wait Until Element Is Visible    ${SEARCH_RESULT_XPATH}    timeout=10s
    ${search_results}=    Get Text    xpath=//tr[contains(@class, 'mat-row')]//td[contains(@class, 'mat-column-ApprovedBy') and normalize-space(text())='${Approved_By_User}']

    Should Contain    ${search_results}    ${Approved_By_User}

    #Verify if any rows are returned
    ${result_count}=    Get Element Count    ${SEARCH_RESULT_XPATH}
    Should Be True    ${result_count} > 0    msg=No search results found!

    Log               ----Search completed successfully using campaign name: ${Approved_By_User}.----
    Log To Console    ----Search completed successfully using campaign name: ${Approved_By_User}.----


The user inputs a campaign ID on the Search field
    Sleep    5s
    #Get Campaign id
    ${Camapaign_ID}    Get Text    xpath=//tr[@role='row']/td[2]

    Log    Campaign Name retrieved: ${Camapaign_ID}

    #Click search bar and input campaign name 
    ${Search_Bar_XPATH}=    Get WebElement    xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]
    Click Element       ${Search_Bar_XPATH}
    Input Text    ${Search_Bar_XPATH}    ${Camapaign_ID}
    Sleep    2s
    Set Suite Variable    ${Camapaign_ID}
    Set Suite Variable   ${Search_Bar_XPATH}

The user verifies the search results returned by Campaign ID
    Wait Until Element Is Visible    ${SEARCH_RESULT_XPATH}    timeout=10s
    ${search_results}=    Get Text    xpath=//td[contains(text(), '${Camapaign_ID}')]
    Should Contain    ${search_results}    ${Camapaign_ID}

    #Verify if any rows are returned
    ${result_count}=    Get Element Count    ${SEARCH_RESULT_XPATH}
    Should Be True    ${result_count} > 0    msg=No search results found!

    Log               ----Search completed successfully using campaign ID: ${Camapaign_ID}.----
    Log To Console    ----Search completed successfully using campaign ID: ${Camapaign_ID}.----

The clicks on the preview button to preview an un-approved campaign
    #Expand Page
    Set Focus To Element   ${dropdown_xpath}
    Click Element    ${dropdown_xpath}
    Click Element    ${option_100_xpath}

    Scroll Element Into View    xpath=${UNAPPROVED_CAMPAIGN_ROW_XPATH}/td[2]  
    Sleep    2s
    ${CAMPAIGN_ID}=    Get Text    xpath=${UNAPPROVED_CAMPAIGN_ROW_XPATH}/td[2]  
    Log    Campaign ID: ${CAMPAIGN_ID}
    Set Suite Variable    ${CAMPAIGN_ID}

    #Click on the Preview button for the unapproved campaign
    Scroll Element Into View    ${Un-Approved_Campaign}
    Sleep    5s
    Click Element    ${Un-Approved_Campaign}

    #Wait for the preview to open, then click the Next button
    Wait Until Element Is Not Visible    xpath=//div[contains(@class, 'overlay')]    timeout=10s
    Click Button    xpath=//button[@type='submit']
    Sleep    2s
The user approves the campaign
    #Wait for the Approve Campaign button to become visible, then click it
    Wait Until Element Is Visible    ${APPROVE_CAMPAIGN_BUTTON_XPATH}
    Sleep    5s
    Click Element    ${APPROVE_CAMPAIGN_BUTTON_XPATH} 

The user verifies that the campaign has been approved
    #Search for the Campaign ID and verify that it has been approved 
    ${Search_Bar_XPATH}=    Get WebElement    xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]
    Click Element       ${Search_Bar_XPATH}
    Input Text    ${Search_Bar_XPATH}    ${CAMPAIGN_ID}
    Sleep    2s

    #Find all results related to the Campaign ID
    ${result_elements}=    Get WebElements    //tr[@role='row' and td[2][normalize-space(text())='${CAMPAIGN_ID}']]
    
    # Check the number of results
    ${result_count}=    Get Length    ${result_elements}

    # Validate based on the number of results found
    Run Keyword If    ${result_count} == 1    Verify Campaign Status    ${result_elements}[0]
    ...    ELSE IF    ${result_count} > 1    Log    Duplicate campaign found with ID: ${CAMPAIGN_ID}
    ...    ELSE    Log    No campaigns found with ID: ${CAMPAIGN_ID}
    ...    
    Log               ----Campaign: ${CAMPAIGN_ID} successfully approved on front end----
    Log To Console    ----Campaign: ${CAMPAIGN_ID} successfully approved on front end----
    Log               ----Approval status set to: ${approval_status}----
    Log               ----Approval status set to: ${approval_status}----
    
Verify Campaign Status
    [Arguments]    ${campaign_row}
    
    #Get the approval status from the status 
    ${status}=    Get Text    //tr[@role='row' and td[2][normalize-space(text())='${CAMPAIGN_ID}']]  
    Log    Campaign Approval Status: ${status}
    
    ${status_list}=    Split String    ${status}    
    ${approval_status}=    Get From List    ${status_list}    -1    

    Log    Campaign Approved with status: ${approval_status}    
    Should Be Equal    ${approval_status.strip()}    true    Campaign approval status is false after approval, failing the test.
    Set Suite Variable    ${approval_status}

The user clicks the delete icon on a campaign
    #Wait for the table to load
    Sleep    5s
    Wait Until Element Is Visible    ${first_campaign_id}

    #Get the first Campaign ID for deactivation
    ${DELETED_CAMPAIGN_ID}=    Get Text    ${first_campaign_id}
    Set Suite Variable    ${DELETED_CAMPAIGN_ID}
    Log    Campaign ID to be deactivated: ${DELETED_CAMPAIGN_ID}

    #Click the delete button
    Scroll Element Into View    ${Delete_Campaign_Button}
    Click Element    ${Delete_Campaign_Button}
    Sleep    3s  
The user clicks the deactivate campaign button to confirm deletion 
    #Click the deactivate campaign button
    Scroll Element Into View    ${Deactivate_Campaign_Button}
    Sleep    3s
    Click Element    ${Deactivate_Campaign_Button}
    Sleep    5s

The user verifies that the campaign has been deleted and removed from the front end 
    # Search for the Campaign ID
    ${Search_Bar}=    Get WebElement    xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]
    Sleep    5s
    Click Element    ${Search_Bar}
    Sleep    2s
    Input Text    ${Search_Bar}    ${DELETED_CAMPAIGN_ID}
    Sleep    2s

    Wait Until Keyword Succeeds    5x    2s    Element Should Not Be Visible    xpath=//tr[@role='row' and td[2][normalize-space(text())='${DELETED_CAMPAIGN_ID}']]

    Log    Campaign successfully deleted. No results found for ID: ${DELETED_CAMPAIGN_ID}.

The user filters campaigns with items per page feature
    #Scroll Element Into View for the dropdown
    Scroll Element Into View    ${DROPDOWN_XPATH}

    #Set the XPath for the selected value
    ${selected_value_xpath}=    Set Variable    //span[contains(@class, 'mat-select-min-line') and text()='10']
    
    #Ensure dropdown is in view
    SeleniumLibrary.Scroll Element Into View    ${DROPDOWN_XPATH}
    
    #Wait until the selected value is visible
    SeleniumLibrary.Wait Until Element Is Visible    ${selected_value_xpath}    timeout=10s

    #Retrieve the text of the selected value
    ${selected_value}=    SeleniumLibrary.Get Text    ${selected_value_xpath}
    
    #Verify if the value is '10'
    Should Be Equal As Strings    ${selected_value}    10

    Log    ---- Default value is correctly set to 10 ----

    #Iterate through the fileter options
    FOR    ${option}    IN    @{OPTIONS}
        # Ensure the dropdown is in view and click to open
        SeleniumLibrary.Scroll Element Into View    ${DROPDOWN_XPATH}
        SeleniumLibrary.Click Element    ${DROPDOWN_XPATH}
        BuiltIn.Sleep    1s
        
        #Generating the option XPath for clicking
        ${option_xpath}=    Set Variable    ${OPTION_BASE_XPATH.format(${option})}
        
        #Wait until the specific option is visible
        SeleniumLibrary.Wait Until Element Is Visible    ${option_xpath}    timeout=10s
        
        #Click the option
        SeleniumLibrary.Click Element    ${option_xpath}
        BuiltIn.Sleep    2s
        
        #Get the total row count 
        ${total_row_count}=    SeleniumLibrary.Get Element Count    ${ROW_COUNT_XPATH}
        
        #subtracting the header count
        ${actual_row_count}=    Evaluate    ${total_row_count} - 1  # Subtract header count (if 1 header row)

        #Verify row count
        BuiltIn.Should Be True    ${actual_row_count} <= ${option}    Expected ${option} items but found ${actual_row_count}
        
        BuiltIn.Log    ---- Verified ${option} items per page successfully ----
    END

The user has an active Approver Role 
    Element Should Be Visible    xpath=//span[contains(@class, 'menu-item') and text()='Admin']
    Page Should Contain    Admin 

The user should not be able to access Capture Campaign
    Wait Until Element Is Visible    xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]
    ${Admin_Expansion}=    Get WebElement    xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]
    Click Element    ${Admin_Expansion}
    
    Wait for spinner to disapear
    ${Approvals_Button}=    Get WebElement    xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span
    Sleep    5s
    Wait Until Element Is Enabled    ${Approvals_Button}
    Click Element    ${Approvals_Button}
    Wait for spinner to disapear

    Log               ---- User does not have access to Capture Campaign as an Approver ----
    Log To Console    ---- User does not have access to Capture Campaign as an Approver ----
