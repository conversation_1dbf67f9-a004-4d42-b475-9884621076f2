*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Export Bins linked to a Bin Type
${TEST_CASE_ID}             RAC29a-TC-1034




*** Keywords ***
Export Bins linked to a Bin Type
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TYPE_TO_VERIFY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal         ${BASE_URL}
    When The User clicks Bin Type Menu
    Then The user must be able to export the Bins linked to Bin Type             ${BIN_TYPE_TO_VERIFY}

| *** Test Cases ***                                                               |        *DOCUMENTATION*    		         |         *BASE_URL*                  |         *BIN_TYPE_TO_VERIFY*           |
| Approver_Export BIN Type Data to CSV Format    | Export Bins linked to a Bin Type   | Search for Bins linked to a Bin Type.   |           ${EMPTY}                  |              OnUs                  |
