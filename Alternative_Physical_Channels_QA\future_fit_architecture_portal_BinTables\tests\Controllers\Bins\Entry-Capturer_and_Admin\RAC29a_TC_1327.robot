*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinById_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1327



*** Keywords ***
Search for a Bin using a Bin ID on the GetBinById Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_NUMBER}  ${EXPECTED_STATUS_CODE}   ${BIN_ID}  ${CAPTURED_DATE}  ${CAPTURED_BY}  ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}
    Set Test Documentation  ${DOCUMENTATION}

    #If the bin Id is empty then get the random Bin from DB
    IF    '${BIN_ID}' == '${EMPTY}'
         &{BIN_DICTIONARY}=         Get the active or inactive Bin from the Database
         ${BIN_NUMBER}=    Get From Dictionary    ${BIN_DICTIONARY}    BIN_NUMBER
         ${BIN_ID}=    Get From Dictionary    ${BIN_DICTIONARY}    BIN_ID
         ${CAPTURED_DATE}=    Get From Dictionary    ${BIN_DICTIONARY}    CAPTURED_DATE
         ${CAPTURED_BY}=    Get From Dictionary    ${BIN_DICTIONARY}    CAPTURED_BY
         ${ACTION_DATE}=    Get From Dictionary    ${BIN_DICTIONARY}    ACTION_DATE
         ${REVIEW_STATUS}=    Get From Dictionary    ${BIN_DICTIONARY}    REVIEW_STATUS
         ${OUTCOME}=    Get From Dictionary    ${BIN_DICTIONARY}    OUTCOME
         ${TO_BE_ACTIONED_BY}=    Get From Dictionary    ${BIN_DICTIONARY}    TO_BE_ACTIONED_BY
         ${REJECTED_COMMENT}=    Get From Dictionary    ${BIN_DICTIONARY}    REJECTED_COMMENT
         ${REVIEWED_BY}=    Get From Dictionary    ${BIN_DICTIONARY}    REVIEWED_BY
         ${REVIEWED_DATE}=    Get From Dictionary    ${BIN_DICTIONARY}    REVIEWED_DATE
         ${LATEST_SERVER_NUMBER}=    Get From Dictionary    ${BIN_DICTIONARY}    LATEST_SERVER_NUMBER
         ${BIN_TYPE_IDS}=    Get From Dictionary    ${BIN_DICTIONARY}    BIN_TYPE_IDS
         ${BIN_TYPE_IDS}=       Create Comma Separated String       ${BIN_TYPE_IDS}
         ${BIN_TYPE_NAMES}=    Get From Dictionary    ${BIN_DICTIONARY}    BIN_TYPE_NAMES
         ${BIN_TYPE_NAMES}=       Create Comma Separated String       ${BIN_TYPE_NAMES}
    END


    Given The User sends a Get Request to search for bins using the Bin Id      ${BASE_URL}      ${BIN_ID}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    And The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}
    Then The Bin Number details must exist on the Bin Database                  ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}


| *** Test Cases ***                                                                                                                                       |        *DOCUMENTATION*    		              |         *BASE_URL*                  |     *BIN_NUMBER*      |    *EXPECTED_STATUS_CODE*   |    *BIN_ID*   |              *CAPTURED_DATE*                            |       *CAPTURED_BY*            |       *ACTION_DATE*            |       *REVIEW_STATUS*          |       *OUTCOME*            |       *TO_BE_ACTIONED_BY*      |       *REJECTED_COMMENT*            |       *REVIEWED_BY*            |       *REVIEWED_DATE*            |       *LATEST_SERVER_NUMBER*           |                             *BIN_TYPE_IDS*                                            |       *BIN_TYPE_NAMES*           |
| Verify only authorized users (capturers and admins) can access the GetBinById API   | Search for a Bin using a Bin ID on the GetBinById Controller       | Search Bin by Number on the GetBinById API   |                                     |       419567          |           200               |   ${EMPTY}    |             2024-11-12T06:37:03.694301                  |        ab0283c                 |       2027-11-11               |          Pending               |        Added               |        None                    |             None                    |          None                  |           None                   |                                        |          7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e, a7ff7c25-057b-461b-9fa1-50d471202b52   |          On-Us, Contactless      |
