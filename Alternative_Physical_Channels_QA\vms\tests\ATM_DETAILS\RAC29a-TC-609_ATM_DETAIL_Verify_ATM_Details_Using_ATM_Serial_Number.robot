*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Validate Details of All ATMs

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Validate Existance of Frontend ATM using Serial Number
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${SERIAL_NUMBER}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user searches FrontEnd for Existing ATM    ${SERIAL_NUMBER}

    #Then The user compares Frontend Search Key ATM Details to the Backend ATM Details

| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |     ***SERIAL_NUMBER***   |
| Validate Existance of Frontend ATM using Serial Number | Validate Existance of Frontend ATM using Serial Number     | Validate Existance of Frontend ATM using Serial Number  |      VMS_UAT             |      43551416             |