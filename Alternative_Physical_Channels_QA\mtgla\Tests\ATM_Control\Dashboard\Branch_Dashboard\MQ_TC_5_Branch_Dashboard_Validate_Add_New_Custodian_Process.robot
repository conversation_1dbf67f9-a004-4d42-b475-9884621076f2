*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control- Branch Dashboard- Validating the Add New Custodian Flow
Suite Setup                 Set up environment variables               
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot



*** Variables ***


*** Keywords ***
The user validates the Add New Custodian Flow on Branch dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page

    And the user navigates to the ATM Control Dashboard  

    And the user selects a branch to access the Branch Dashboard 

    And the user selects the Add New Custodian Menu

    And the user inputs the Custodian Name on the name field 

    And the user inputs the Custodian Lastname on the lastname field 

    And the user inputs the AB Number of Custodian on ABNumber field

    And the user clicks on the date icon to select an Active Date 

    And the user inputs a comment for Adding a New Custodian

    And the user clicks save to save the new custodian 

    Then the user verifies that the custodian has been successfully added 

     
| *Test Cases*                                                                                                                           |      *DOCUMENTATION*                                          | *TEST_ENVIRONMENT*   |
|   MQ_TC_5_Branch_Dashboard_Validate_Add_New_Custodian_Process   | The user validates the Add New Custodian Flow on Branch dashboard    |    Validating the Add New Custodian Flow on Branch dashboard  |    MTGLA_UAT         | 
