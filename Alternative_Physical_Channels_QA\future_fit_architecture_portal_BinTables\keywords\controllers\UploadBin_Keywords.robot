*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/Upload.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************

*** Variables ***



*** Keywords ***
The User Populates the Upload Bin JSON payload with
    [Arguments]     &{BINS_DETAILS}
    ${json_payload}=    Create Bin Requests    &{BINS_DETAILS}

    ${json_payload_is_created}=    Run Keyword And Return Status     Should Not Be Empty    ${json_payload}

    Run Keyword If    ${json_payload_is_created} == ${False}
    ...    Fail     The JSON payload for upload bin was not created!

    #Save the payload in a Global Variable
    #Set Global Variable    ${REST_PAYLOAD}        ${json_payload}

    Log Many    ${json_payload}


Create Bin Requests
    [Arguments]    &{bins}

    #This keyword generates a JSON payload for bin requests by passing a dictionary of bins.
    #Each bin is represented by a set of key-value pairs, with keys like bin1, date1, and binIds1.

    #Arguments:
     #&{BINS_DETAILS}: A dictionary containing bin data. The dictionary should contain the following format for each bin:

        #- binX: The bin number (e.g., bin1=12345).
        #- dateX: The date associated with the bin (e.g., date1=2024-11-11).
        #- binIdsX: A list of bin type IDs associated with the bin (e.g., binIds1=@['uuid1', 'uuid2']).

     #Here X represents an index (e.g., 1, 2, 3, etc.), and the method is flexible enough to accept any number of bins, even those with non-sequential or multi-digit indices (e.g., bin123, bin10).

     #Return Value:
        #Returns the generated JSON payload as a string, formatted with indentation for readability.

     #Usage Example
        #${bins} =    Create Dictionary    bin1=12345    date1=2024-11-11    binIds1=f47ac10b-58cc-4372-a567-0e02b2c3d479,b99f5d75-52c5-4a92-8759-c6d91f89f0cc    bin2=67890    date2=2024-11-12    binIds2=c93f28b1-8a76-40bc-9e0c-c68204ef7e7a',99d2f582-9d4e-4389-b9b4-6395ea2f4d8e    bin3=987    date3=2024-11-13    binIds3=d72b5f47-d0f5-4d62-a0a7-26bf5075ac47,efc53080-22b0-4161-b87b-d72bb59f230d  bin4=54321    date4=2024-11-13    binIds4=d72b5f47-d0f5-4d62-a0a7-26bf5075ac47,efc53080-22b0-4161-b87b-d72bb59f230d
        #${json_result}=    Create Bin Requests    &{bins}

    # Log the received bins to ensure it's a valid dictionary
    Log    Received bins: &{bins}

    #${bin_request_generator} =    Upload    @{bins}
    ${json_result} =    Create Bin Request    &{bins}

    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal_BinTables/data/UploadBin.json
    Write JSON data To File    ${JSON_FILE}    ${json_result}

    RETURN    ${json_result}


The User sends the Upload Bin API Request
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}


    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/UploadBin.json

    Log Many   ${payload}
    ${endpoint}=        Get Endpoint    ${base_url}
    ${method}=          Set Variable   POST
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}
    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     payload=${payload}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'Upload' REST API did not return the expected status of '${EXPECTED_STATUS_CODE}', the returned status is '${status_code}'

The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}
    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
    IF    "${api_error_message_detail}" != "None"
         ${v2}=      Remove Quotes    ${api_error_message_detail}
    ELSE
         ${v2}=      Set Variable     ${api_error_message_detail}
    END


    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${v1}'     '${v2}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       BinNumber   binNumber   actionDate  binTypeIds  binUploadRequests

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'

             #Remove quotes from the strings
             ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
             IF    '${api_error_message_fields}' != 'None'
                 ${v2}=      Remove Quotes    ${api_error_message_fields}
             ELSE
                 ${v2}=      Set Variable     ${api_error_message_fields}
             END


             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${v1}' in '${v2}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Upload' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.



Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}


Get Response Status Code
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keywords to read error response fields




#The below keywords are for Database Verifications
The Created Bin Number(s) details must exist on the Bin Database
    [Arguments]     &{bins}
    # Ensure the bins are not empty
    ${dict_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bins}
    Run Keyword If    not ${dict_contain_data}
    ...     Fail    Please provide the Bin Number(s) that must be queried from the DB.
    ${bin_count}=    Get Bin Count    &{bins}
    Log Many   ${bins}
    FOR    ${index}    IN RANGE    1    ${bin_count} + 1
        ${binNumber_verification_passed}=    Set Variable    ${False}
        ${binID_verification_passed}=        Set Variable    ${False}
        ${actionDate_verification_passed}=   Set Variable    ${False}
        ${binTypeId_verification_passed}=    Set Variable    ${False}
        ${bin_key}=    Set Variable    bin${index}
        ${date_key}=    Set Variable    date${index}
        ${binIds_key}=    Set Variable    binIds${index}
        ${bin_value}=    Get From Dictionary    ${bins}    ${bin_key}
        ${date_value}=    Get From Dictionary    ${bins}    ${date_key}
        ${binIds_value}=    Get From Dictionary    ${bins}    ${binIds_key}
        Log    Bin: ${bin_key} = ${bin_value}, Date: ${date_key} = ${date_value}, BinIds: ${binIds_key} = ${binIds_value}
        ${binIds_list}=    Split String    ${binIds_value}    ,
        FOR    ${binId}    IN    @{binIds_list}
            Log    Processing Bin ID: ${binId}
        END
         #Get the Database details for the current Bin
        ${db_results}=     Get the Bin details for the newly created bin    ${bin_value}
        # Ensure the results are not empty
        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
        Run Keyword If    not ${dr_results_contain_data}
        ...     Fail    Database query for bin number: '${bin_value}' returned no results
         #Check that results is a list using Evaluate
        ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
        Should Be True    ${is_list}
        #Loop through the returned DB results and verify them against the user's supplied parameters
        ${num_rows}=    Get Length    ${db_results}
        FOR    ${row}    IN    @{db_results}

            #Verify the bin details
            ${binID_data}=            Get Column Data By Name       ${row}       Bin_ID
            ${binNumber_data}=        Get Column Data By Name       ${row}       binNumber
            ${binActionDate_data}=    Get Column Data By Name       ${row}       actionDate
            ${BinTypeId_data}=        Get Column Data By Name       ${row}       binTypeID
            ${BinTypeName_data}=      Get Column Data By Name       ${row}       binType

            ${binNumber_verification_passed}=       Run Keyword And Return Status
            ...  Verify that values are equal     '${bin_value}'     '${binNumber_data}'
            ${actionDate_verification_passed}=   Run Keyword And Return Status
            ...  Verify that values are equal     '${date_value}'     '${binActionDate_data}'
            #IF    '${BinTypeId_data}' in ${binIds_list}
            #     ${binTypeId_verification_passed}=  Set Variable  ${True}
            #END

            ${binTypeId_verification_passed}=    Set Variable If  '${BinTypeId_data}' in ${binIds_list}     ${True}     ${False}
        END

        #Verify that all the data that needed verification was verified successfully
        Run Keyword If    ${binNumber_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${bin_value}' was not found on the Database.
        ...  ELSE
        ...    Log    The BIN Number: '${bin_value}' was found on the Database.

        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${date_value}', for BIN Number: '${bin_value}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Action Date value: '${date_value}', for BIN Number: '${bin_value}' was successful.

        Run Keyword If    ${binTypeId_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Bin Type ID value(s): '${binIds_value}', for BIN Number: '${bin_value}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Bin Type ID value: '${binIds_value}', for BIN Number: '${bin_value}' was successful.
    END


Get Bin Count
    [Arguments]    &{kwargs}
    ${count}=    Set Variable     0

    FOR    ${key}    ${value}    IN    &{kwargs}
        ${temp_key}=  Convert To String  ${key}
        ${Key_is_bin_number}=      Run Keyword And Return Status   Should Match Regexp    ${temp_key}    ^bin[0-9]+$
        IF    ${Key_is_bin_number}
             ${count} =    Evaluate    ${count} + 1
        END
    END

    RETURN    ${count}



