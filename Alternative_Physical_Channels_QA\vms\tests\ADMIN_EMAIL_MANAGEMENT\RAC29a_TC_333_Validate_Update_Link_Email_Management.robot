*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Validate Update Link functionality in Email Management

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/EmailManagement.robot
Resource                                            ../../keywords/common/DatabaseConnector.robot

*** Variables ***


*** Keywords ***
Validate Random Email Update Functionality
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${UPDATED_EMAIL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - Email Management
    And Update Random Email From Database    ${UPDATED_EMAIL}

*** Test Cases ***
| Validate Update Link- Email Management |
| | [Documentation] | Validate the update functionality for vendor emails in Email Management. |
| | Validate Random Email Update Functionality | Validate the update functionality for vendor emails in Email Management. | VMS_UAT | <EMAIL> |
