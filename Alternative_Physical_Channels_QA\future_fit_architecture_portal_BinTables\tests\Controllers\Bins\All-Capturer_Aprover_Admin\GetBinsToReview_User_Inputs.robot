*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinsToReview_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using a Bin Number on the GetBinsToReview Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_NUMBER}  ${EXPECTED_STATUS_CODE}   ${BIN_ID}  ${CAPTURED_DATE}  ${CAPTURED_BY}  ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetBinsToReview using a Bin Number    ${BASE_URL}      ${BIN_NUMBER}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    And The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE}
    Then The Bin Number details must exist on the Bin Database                  ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE}


| *** Test Cases ***                                                                                                                                 |        *DOCUMENTATION*    		                 |         *BASE_URL*                  |     *BIN_NUMBER*      |    *EXPECTED_STATUS_CODE*   |                *BIN_ID*                   |              *CAPTURED_DATE*                            |       *CAPTURED_BY*            |       *ACTION_DATE*            |       *REVIEW_STATUS*          |       *OUTCOME*            |       *TO_BE_ACTIONED_BY*      |       *REJECTED_COMMENT*            |       *REVIEWED_BY*            |       *REVIEWED_DATE*            |       *LATEST_SERVER_NUMBER*            |       *BIN_TYPE*            |
| Search for a Bin Numbered '7004535' and verify its details and Bin Type   | Search for a Bin using a Bin Number on the GetBinsToReview Controller   | Search Bin by Number on the GetBinsToReview API   |                                     |       7004535         |           200               |   59ef7be3-c73b-4648-91a1-568064a700ae    |             2024-11-12T06:37:03.694301                  |        ab0283c                 |       2027-11-11               |          Pending               |        Added               |        Approver                |             None                    |          None                  |           None                  |                                         |          On-Us, Contactless      |
