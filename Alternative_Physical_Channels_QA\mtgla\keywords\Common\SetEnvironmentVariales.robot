*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Get environment variables passed as argument from commandline

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Library                                             ../../utility/PostExecutionUpdateV2.py

*** Keywords ***
Set up environment variables   
    Set Environment Variable    UPLOAD_TEST_STEPS    ${UPLOAD_TEST_STEPS}
    Set Environment Variable    ROBOT_FILE_PATH    ${ROBOT_FILE_PATH}
    Set Environment Variable    IS_HEADLESS_BROWSER  ${IS_HEADLESS_BROWSER}
    Set Environment Variable    BROWSER  ${BROWSER}
    Set Environment Variable    CRED_U    ${base64_string_U}
    Set Environment Variable    CRED_P    ${base64_string_P}
    
Clear environment variables
    clear testrail credentials
  