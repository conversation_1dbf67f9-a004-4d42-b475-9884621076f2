
*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       End Web Test

Documentation  Create Campaign page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary 
Library                                             OperatingSystem
Library                                             String
Library                                             DateTime
Library                                             ../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/Navigation.robot
Resource                                            ../../keywords/atm_marketing/UploadMarkrtingScreen.robot
Resource                                            ../../keywords/atm_marketing/CalendarView.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../keywords/atm_marketing/FilloutCampaignTarget.robot
Resource                                            ../../keywords/atm_marketing/FilloutYourCampaign.robot

*** Variables ***

*** Keywords ***
Create marketing campaign    
    [Arguments]        ${DOCUMENTATION}  ${IS_CAMPAIGN_TARGETED}  ${CAMPAIGN_TARGET}  ${CAMPAIGN_TARGETED_REGION_OR_ATM}  ${CAMPAIGN_NAME}  ${MARKETING_TYPE}  ${RECEIVER_DEVICE_TYPE}  ${CAMPAIGN_START_DATE}  ${CAMPAIGN_END_DATE}   ${CAMPAIGN_LANGUAGE}  ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}   ${LOGON_USER}    ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    IF    '${CAMPAIGN_NAME}' == '${EMPTY}' or '${CAMPAIGN_NAME}' == ''
         ${random_word}=     Generate random campaign name
         ${CAMPAIGN_NAME}=   Set Variable     ${random_word}

    END

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    When The user clicks on the Capture campaign link

    And The user fills out Campaign Targeted    ${IS_CAMPAIGN_TARGETED}  ${CAMPAIGN_TARGET}  ${CAMPAIGN_TARGETED_REGION_OR_ATM}
    
    And The user clicks on Next button

    And The user fills out Campaign        ${CAMPAIGN_NAME}  ${MARKETING_TYPE}  ${RECEIVER_DEVICE_TYPE}  ${CAMPAIGN_START_DATE}     ${CAMPAIGN_END_DATE}
        
    And The user clicks on Next button in Fill Out your Campaign screen

    And The user captures marketing screen information   ${CAMPAIGN_LANGUAGE}  ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}

    And The user saves the campaign

    Then The captured campaign must exist on the database


*** Test Cases ***                                                                                                                                           |                                  *DOCUMENTATION*                                                                                |    *IS_CAMPAIGN_TARGETED*    |    *CAMPAIGN_TARGET*         |      *CAMPAIGN_TARGETED_REGION_OR_ATM*                |       *CAMPAIGN_NAME*                                       |      *MARKETING_TYPE*    |   *RECEIVER_DEVICE_TYPE*        |  *CAMPAIGN_START_DATE*   | *CAMPAIGN_END_DATE*   |     * CAMPAIGN_LANGUAGE*      |           *IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY*                                                            |   *LOGON_USER*   |   *TEST_ENVIRONMENT*    |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English And Afrikaans_09005       | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans         |     Yes                      |        ATM                   |     S11782 ABSA LAB, 270 Republic Road - Randburg    |  ${EMPTY}      |           Idle           |          ATM                    |                4         |         6             |        English,Afrikaans      |    images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg,images\\MarketingA_af_2.jpg  | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - Afrikaans_09005                   | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - Afrikaans                      |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}   |           Idle           |          ATM                    |                1         |         5             |        Afrikaans              |    images\\MarketingA_af_2.jpg                                                                             | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_09005                     | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_One                    |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}      |           Idle           |          ATM                    |                1         |         12            |        English                |    images\\3048_2010091absagenericatms_easter365x470-65eb2169a9de2_en.jpg                                  | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_09005_2                   | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_Two                    |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}        |           Idle           |          ATM                    |                1         |         3             |        English                |    images\\3050_2010091absagenericatms_humanrights365x470-65eb2169baa56_en.jpg                             | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_09005_3                   | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_Three                  |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}      |           Idle           |          ATM                    |                1         |         15            |        English                |    images\\3048_2010091absagenericatms_easter365x470-65eb2169a9de2_en.jpg                                  | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_09005_4                   | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_Four                   |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}       |           Idle           |          ATM                    |                1         |         7             |        English                |    images\\3050_2010091absagenericatms_humanrights365x470-65eb2169baa56_en.jpg                             | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English And Afrikaans_09005       | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans_1       |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}  |           Idle           |          ATM                    |                1         |         8             |        English,Afrikaans      |    images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg,images\\MarketingA_af_2.jpg  | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - Afrikaans_09005                   | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - Afrikaans_1                    |     Yes                      |        ATM                   |    S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park   |  ${EMPTY}    |           Idle           |          ATM                    |                1         |         9             |        Afrikaans              |    images\\MarketingA_af_2.jpg                                                                             | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - Region - English                        | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - Region - English                     |     Yes                      |        Region                |    Gauteng                                            |  ${EMPTY}         |           Idle           |          ATM                    |                2         |         2             |        English                |    images\\MarketingA_en_7.jpg                                                                             | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - Region - Afrikaans                      | Create marketing campaign           |   Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - Region - Afrikaans                   |     Yes                      |        Region                |    Gauteng                                            |  ${EMPTY}        |           Idle           |          ATM                    |                2         |         2             |        Afrikaans              |    images\\MarketingA_en_7_af.jpg                                                                          | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - No - English                                                      | Create marketing campaign           |   Positive          FFT - Create campaign - is the campaign targeted - No - English                                             |     No                       |                              |                                                       |  ${EMPTY}          |           Idle           |          ATM                    |                2         |         5             |        English                |    images\\MarketingA_en_7.jpg                                                                             | BUSINESS_USER    | APC_UAT                 |
| FFT - Create campaign - is the campaign targeted - No - Afrikaans                                                    | Create marketing campaign           |   Positive          FFT - Create campaign - is the campaign targeted - No - Afrikaans                                           |     No                       |                              |                                                       |  ${EMPTY}         |           Idle           |          ATM                    |                2         |         9             |        Afrikaans              |    images\\MarketingA_en_7_af.jpg                                                                          | BUSINESS_USER    | APC_UAT                 |

