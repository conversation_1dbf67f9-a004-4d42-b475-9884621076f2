*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Navigation Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                            RequestsLibrary
Library                                            Collections
Library                                            String
Library                                             DatabaseLibrary
Library                                          ../utility/DatabaseUtility.py
Library                                            DateTime

#***********************************PROJECT RESOURCES***************************************


*** Variables ***
${ATM_Control_Dashboard_Table}                          xpath=//*[@id='table']//tbody//tr
${LIST_ITEM_LOCATOR}                                    xpath=//*[@id="table"]//tbody
${COST_CENTRE_QUERY}                                    SELECT TOP (100) [Id] ,[CostCentre]  FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_Daily_CostCentres]
${COST_CENTRE_INFORMATION_QUERY}                        SELECT TOP (1000) [CostCentreName], [CostCentreNumberFull], [CostCentreRegionDescription], [CostCentreTypeDescription] FROM [ATM_Mismatch_DEV].[dbo].[SYSTEM_T_CostCentrePermission] WHERE CostCentreNumberFull = '87708636';
${Custodian_Takeover_Query}                             SELECT TOP (1) [Name] ,[LastName] ,[ABNumber] ,[Comment] ,[CreatedDate] ,[CreatedUserId] ,[CustodianActiveDate] FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_Daily_CustodianAudits] ORDER BY [CreatedDate] DESC;
${Custodian_Takeover_Comment}                           Automation Test: Testing Custodian Takeover
${Added_Custodian_Query}                                SELECT TOP (1) [Id] ,[Name] ,[LastName] ,[ABNumber] ,[Comment] ,[CustodianActiveDate] FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_Daily_CustodianAudits] ORDER BY [Id] DESC; 
${CONDUCTED_BY_AB_NUMBER}                               AB1234
${CONDUCTED_BY_AB_LOCATOR}                              xpath=//input[@id='ConductedByAB']
${CONDUCTED_BY_NAME}                                    AutomationUSER
${CONDUCTED_BY_NAME_LOCATOR}                            xpath=//input[@id='ConductedByName']
${SAVE_ACTION_BUTTON}                                   xpath=//button[@data-save='modal']   
${Custodian_Maintenance_Locator}                        xpath=//*[text()[normalize-space(.)='Custodian maintenance']]
${First_Cusodian_On_Maintenance_List}                   xpath=(//tbody[@id="myUL"]//a/b[starts-with(normalize-space(text()), 'AB')])[1]
${Custodian_Current_Name_Locator}                       xpath=//*[@id='CustodianName']
${Custodian_Current_Last_Name_Locator}                  xpath=//*[@id='CustodianLastName']
${Custodian_Current_AB_Number_Locator}                  xpath=//*[@id='CustodianABNumber']
${Custodian_Current_Active_Date_Locator}                xpath=//input[@id='CustodianActiveDate']
${New_Custodian_Name}                                   Jake
${New_Custodian_Last_Name}                              Brake
${Comment_Field_Locator}                                xpath=//*[@id='Comment']
${Comment_Input}                                        Amended Custodian Name & Last Name
${Edited_Custodian_Query}                               SELECT TOP (1000) [Id] ,[Name] ,[LastName] ,[ABNumber] ,[Comment] ,[CustodianActiveDate] FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_Daily_CustodianAudits] ORDER BY Id DESC;
${PROVINCES_ATMC_DASHBOARD_LOCATOR}                     xpath=//tr/td[@class='w3-button']

${ALL_POSSIBLE_PROVINCES}                               xpath=//tr[@style="font-weight:bolder"]/td[@class="w3-button"]
${ALL_POSSIBLE_COST_CENTRES}                            xpath=//tbody/tr/td/a
${COST_CENTRE_EC_LOCATOR}                               xpath=//tbody[contains(@class, 'Eastern Cape')]/tr/td/a
${COST_CENTRE_FS_LOCATOR}                               xpath=//tbody[contains(@class, 'Free State')]/tr/td/a
${COST_CENTRE_GN_LOCATOR}                               xpath=//tbody[contains(@class, 'Gauteng North')]/tr/td/a
${COST_CENTRE_GS_LOCATOR}                               xpath=//tbody[contains(@class, 'Gauteng South')]/tr/td/a
${COST_CENTRE_KZN_LOCATOR}                              xpath=//tbody[contains(@class, 'Kwazulu Natal')]/tr/td/a
${COST_CENTRE_LIMPOPO_LOCATOR}                          xpath=//tbody[contains(@class, 'Limpopo')]/tr/td/a
${COST_CENTRE_MPUMALANGA_LOCATOR}                       xpath=//tbody[contains(@class, 'Mpumalanga')]/tr/td/a
${COST_CENTRE_NW_LOCATOR}                               xpath=//tbody[contains(@class, 'North West')]/tr/td/a
${COST_CENTRE_NC_LOCATOR}                               xpath=//tbody[contains(@class, 'Northern Cape')]/tr/td/a
${COST_CENTRE_UNKNOWN_LOCATOR}                          xpath=//tbody[contains(@class, 'Unknown')]/tr/td/a
${COST_CENTRE_WC_LOCATOR}                               xpath=//tbody[contains(@class, 'Western Cape')]/tr/td/a

${EC_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'Eastern Cape')]/following-sibling::td[1]
${FS_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'Free State')]/following-sibling::td[1]
${GN_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'Gauteng North')]/following-sibling::td[1]
${GS_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'Gauteng South')]/following-sibling::td[1]
${KZN_TOTAL_COST_CENTRE_COUNT_LOCATOR}                  xpath=//td[@class="w3-button" and contains(text(), 'Kwazulu Natal')]/following-sibling::td[1]
${LIMPOPO_TOTAL_COST_CENTRE_COUNT_LOCATOR}              xpath=//td[@class="w3-button" and contains(text(), 'Limpopo')]/following-sibling::td[1]
${MPUMALANGA_TOTAL_COST_CENTRE_COUNT_LOCATOR}           xpath=//td[@class="w3-button" and contains(text(), 'Mpumalanga')]/following-sibling::td[1]
${NW_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'North West')]/following-sibling::td[1]
${NC_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'Northern Cape')]/following-sibling::td[1]
${UNKNOWN_TOTAL_COST_CENTRE_COUNT_LOCATOR}              xpath=//td[@class="w3-button" and contains(text(), 'Unknown')]/following-sibling::td[1]
${WC_TOTAL_COST_CENTRE_COUNT_LOCATOR}                   xpath=//td[@class="w3-button" and contains(text(), 'Western Cape')]/following-sibling::td[1]
${FILTER_LOCATOR}                                       xpath=//*[text()[normalize-space(.)='Filter']] 
${REGION_FILTER}                                        xpath=//*[@id='Filter_Region']
${REGION_INPUT_EASTERN_CAPE}                            xpath=//select[@id='Filter_Region']/option[@value='1']
${RUN_BUTTON}                                           xpath=//*[text()[normalize-space(.)='Run']]
${ATM_STATUS_FILTER_LOCATOR}                            xpath=//*[@name='Filter.ATMControlStatus']
${ATM_STATUS_REQUIRED_COUNT_INPUT}                      xpath=//label[@for='Filter_ATMControlStatus']/following-sibling::select/option[2]
${COST_CENTRE_FILTER_FIELD}                             xpath=//*[@name='Filter.CostCentre']

*** Keywords ***
the user selects a branch to access the Branch Dashboard
    Sleep    2s
    #Click Element    xpath=//*[text()[normalize-space(.)='87708636 - Aliwal North']]
    Click Element    xpath=//*[text()[normalize-space(.)='83906088 - Vincent Park']]
    Sleep    5s
    Page Should Contain    Cost Centre Information
    Page Should Contain    Dashboard 

the user verifies they have accessed the Branch Dashboard
    Page Should Contain    Cost Centre Information
    Page Should Contain    ATM's to Action

the user verifies access to the ATM Control Dashboard
    Page Should Contain    ATM Control Information
    Page Should Contain    ATM Control Dashboard Date :


Get all ATM Control Dashboard Data 
    ${items} =    Get List Items
    Log    Items found: ${items}
    
    #Create a dictionary to store items
    ${item_dict} =    Create Dictionary
    
    ${counter} =    Set Variable    1
    FOR    ${element}    IN    @{items}
        Log    ${element}
        ${key} =    Set Variable    Item_${counter}
        Set To Dictionary    ${item_dict}    ${key}    ${element}
        ${counter} =    Evaluate    ${counter} + 1
    END

    Log    Final dictionary with all items: ${item_dict}   

    ${Region_1}    Get From Dictionary    ${item_dict}     Item_1
    Log    ${Region_1}


Get List Items
    [Documentation]    Fetches all items displayed in the continuous list.
    ${list_elements} =    Get WebElements    ${LIST_ITEM_LOCATOR}
    ${items} =    Create List
    FOR    ${element}    IN    @{list_elements}
        ${item_text} =    Get Text    ${element}
        Append To List    ${items}    ${item_text}
    END
    RETURN    ${items}
    Set Suite Variable    ${items}


Get Cost Centers
    ${items} =    Get List Items
    Log    Items found: ${items}
    
    #Create a dictionary to store branch cost centers
    ${branch_dict} =    Create Dictionary
    
    ${counter} =    Set Variable    1
    FOR    ${element}    IN    @{items}
        IF    'Required Count' in $element or 'No Count Required' in $element
            Log    Branch Cost Center found: ${element}
            ${branch_key} =    Set Variable    Branch_${counter}
            Set To Dictionary    ${branch_dict}    ${branch_key}    ${element}
            ${counter} =    Evaluate    ${counter} + 1
        END
    END

    Log    Final dictionary with branch cost centers: ${branch_dict}

    Set Suite Variable    ${branch_dict}

Extract Cost Centre From Dictionary
    #Create an empty list to store all cost centres
    ${cost_centres}=    Create List

    #Loop through the dictionary
    FOR    ${key}    IN    @{branch_dict}
        # Get the value of the current item
        ${value}=    Get From Dictionary    ${branch_dict}    ${key}
        
        #Split the value string
        ${number}=    Split String    ${value}    -   
        
        #Extract the first item (cost centre)
        ${first_item} =    Set Variable    ${number[0]}
        
        #Append the extracted cost centre to the list
        Append To List    ${cost_centres}    ${first_item}
    END

    #Log all the collected cost centres
    Log Many    @{cost_centres}

    ${cost_centres_str} =    Evaluate    str(${cost_centres})

    Log    ${cost_centres_str}
    Set Suite Variable    ${cost_centres_str}

Database Connection Point 
    ${db_cost_centres}=    Create List
    ${COST_CENTRE_DATABASE}=    DatabaseUtility.Execute Sql Query              ${COST_CENTRE_QUERY}    
    Log    ${COST_CENTRE_DATABASE}
    Should Not Be Empty    ${COST_CENTRE_DATABASE}

    #Convert the database result to a string
    ${log_string}=    Evaluate    str(${COST_CENTRE_DATABASE})  
    Log    ${log_string}
    
    ${cost_centres}=    Get Cost Centre Numbers    ${log_string}
    Log    ${cost_centres}

    
    Should Be Equal    ${cost_centres_str}    ${cost_centres}

Get Cost Centre Numbers
    [Arguments]    ${log}
    ${cost_centres}=    Create List
    ${items}=    Evaluate    ${log}  #Convert back to a list of dictionaries
    FOR    ${item}    IN    @{items}
        ${cost_centre}=    Get From Dictionary    ${item}    CostCentre
        Append To List    ${cost_centres}    ${cost_centre}
    END
    RETURN    ${cost_centres}

the user verifies that the Cost Center Information is correctly displayed on the Front-End
    ${COST_CENTER_INFORMATION}=    Get Text    xpath=//*[@id="main"]/div/div[1]/div[1]
    Log    Full Information:\n${COST_CENTER_INFORMATION}   
    
    #Data Handling 
    ${info_list}=    String.Split String    ${COST_CENTER_INFORMATION}    \n

    #Validate the length of the info_list to ensure you have enough items
    ${list_length}=    Get Length    ${info_list}
    Should Be True    ${list_length} >= 8    The expected format has not been met.

    #Extract and strip each relevant value using the correct indices
    ${region}=       Set Variable    ${info_list[2].strip()}  #Eastern Cape is at index 2
    ${division}=     Set Variable    ${info_list[4].strip()}  #Retail Branch is at index 4
    ${cost_centre}=  Set Variable    ${info_list[6].strip()}  #87708636 is at index 6
    ${name}=         Set Variable    ${info_list[8].strip()}  #Aliwal North is at index 8

    #Log the extracted values
    Log    Region: ${region}
    Log    Division: ${division}
    Log    Cost Centre: ${cost_centre}
    Log    Cost Centre Name: ${name}

    Set Suite Variable    ${region}
    Set Suite Variable    ${division}
    Set Suite Variable    ${cost_centre}
    Set Suite Variable    ${name}

the user confirms that the Front-End Cost Center Information matches the corresponding data in the database
    #Database Validation
    ${COST_CENTRE_INFORMATION_DATABASE}=    DatabaseUtility.Execute Sql Query        ${COST_CENTRE_INFORMATION_QUERY}    
    Log    ${COST_CENTRE_INFORMATION_DATABASE}
    
    #Extract the first item from the result 
    ${database_info}=    Get From List    ${COST_CENTRE_INFORMATION_DATABASE}    0

    #Extract specific values from the dictionary and assign them to variables
    ${database_region}=    Get From Dictionary    ${database_info}    CostCentreRegionDescription
    ${database_Cost_Center_division}=    Get From Dictionary    ${database_info}    CostCentreTypeDescription
    ${database_Cost_Center_Number}=    Get From Dictionary    ${database_info}    CostCentreNumberFull
    ${database_Cost_Center_name}=    Get From Dictionary    ${database_info}    CostCentreName

    #Log each of the extracted values 
    Log    ${database_region}  
    Log    ${database_Cost_Center_division}
    Log    ${database_Cost_Center_Number}
    Log    ${database_Cost_Center_name}

    Set Suite Variable    ${database_region}  
    Set Suite Variable    ${database_Cost_Center_division}
    Set Suite Variable    ${database_Cost_Center_Number}
    Set Suite Variable    ${database_Cost_Center_name}

the user verifies the Front-end Cost Center Information matches the database information 
    Should Be Equal As Strings    ${region}    ${database_region} 
    Should Be Equal As Strings    ${division}    ${database_Cost_Center_division}
    Should Be Equal As Strings    ${cost_centre}    ${database_Cost_Center_Number}
    Should Be Equal As Strings    ${name}    ${database_Cost_Center_name}


the user verifies the data displayed on the Branch Dashbaord
    Page Should Contain    xpath=//*[@id="main"]/div/div[1]/div[1]/div


the user selects the Custodian Takeover menu
    ${Custodian_Takeover_Button}=    Get WebElement    xpath=//*[text()[normalize-space(.)='Custodian Takeover']]
    Wait Until Element Is Enabled    ${Custodian_Takeover_Button}
    Click Element    ${Custodian_Takeover_Button}
    Sleep    5s

the user selects a Custodian from the list
    #Open Dropdown Menu
    ${Custodian_List_Dropdown}=    Get WebElement    xpath=//*[contains(@class,'select2-selection__arrow')]
    Wait Until Element Is Enabled    ${Custodian_List_Dropdown}
    Click Element    ${Custodian_List_Dropdown}
    Sleep    2s
    
    #Select a Custodian
    ${Custodian_Selection}=    Get WebElement    xpath=//ul[@class='select2-results__options']//li[1]
    Wait Until Element Is Enabled    ${Custodian_Selection}
    Click Element    ${Custodian_Selection}
    Sleep    5s

the user verifies Custodian info is auto-populated after selection
    ${Custodian_Name}=    Get Value    xpath=//*[@name='CustodianName']
    Log               Custodian Name Auto-Populated:${Custodian_Name}
    Log To Console    Custodian Name Auto-Populated:${Custodian_Name}
    Sleep    2s
    Set Suite Variable    ${Custodian_Name}
    
    ${Custodian_Last_Name}=    Get Value    xpath=//*[@name='CustodianLastName']
    Log               Custodian Last Name Auto-Populated:${Custodian_Last_Name}
    Log To Console    Custodian Last Name Auto-Populated:${Custodian_Last_Name}
    Sleep    2s
    Set Suite Variable    ${Custodian_Last_Name}
    
    ${Custodian_AB_Number}=    Get Value    xpath=//*[@name='CustodianABNumber']
    Log               Custodian AB Number Auto-Populated:${Custodian_AB_Number}
    Log To Console    Custodian AB Number Auto-Populated:${Custodian_AB_Number}
    Sleep    2s
    Set Suite Variable    ${Custodian_AB_Number}

    ${Custodian_Active_Date}=    Get Value    xpath=//*[@name='CustodianActiveDate']
    Log               Custodian Active Date Auto-Populated:${Custodian_Active_Date}
    Log To Console    Custodian Active Date Auto-Populated:${Custodian_Active_Date}
    ${Custodian_Active_Date_Ele}=    Catenate    ${Custodian_Active_Date}    00:00:00.0000000
    Sleep    2s
    Set Suite Variable    ${Custodian_Active_Date_Ele}    

the user selects an ATM Number to assign a device to the Custodian
    ${ATM_Number_Drop_Down}=    Get WebElement    xpath=//span[@class='select2-selection select2-selection--multiple']
    Wait Until Element Is Enabled    ${ATM_Number_Drop_Down}
    Click Element    ${ATM_Number_Drop_Down}
    Sleep    2s

    ${ATM_Number_Selection}=    Get WebElement    xpath=//ul[@class='select2-results__options']/li[1]
    Wait Until Element Is Enabled    ${ATM_Number_Selection}
    Click Element    ${ATM_Number_Selection}
    Sleep    2s

the user enters a comment
    ${Comment_Field}=    Get WebElement    xpath=//*[@name='Comment']
    Wait Until Element Is Enabled    ${Comment_Field}
    Click Element    ${Comment_Field}
    Input Text    ${Comment_Field}    ${Custodian_Takeover_Comment}
    Sleep    5s

the user clicks save to confirm the takeover
    ${Save_Button}=    Get WebElement    xpath=//*[text()[normalize-space(.)='Save']]
    Wait Until Element Is Enabled    ${Save_Button}
    Click Element    ${Save_Button}
    Sleep    5s

the user verifies the Custodian Takeover was saved successfully 
    #Database Validation
    ${Database_Custodian_Takeover}=    DatabaseUtility.Execute Sql Query        ${Custodian_Takeover_Query}    
    Log   ${Database_Custodian_Takeover}
    Log To Console    ${Database_Custodian_Takeover}

    Sleep    5s

    #Extract the first item from the result 
    ${database_Custodian_Takeover_info}=    Get From List    ${Database_Custodian_Takeover}    0

    #Extract specific values from the dictionary and assign them to variables
    ${database_Custodian_Name}=    Get From Dictionary    ${database_Custodian_Takeover_info}    Name
    ${database_Custodian_Last_Name}=    Get From Dictionary    ${database_Custodian_Takeover_info}    LastName
    ${database_AB_Number}=    Get From Dictionary    ${database_Custodian_Takeover_info}    ABNumber
    ${database_Takeover_Comment}=    Get From Dictionary    ${database_Custodian_Takeover_info}    Comment
    ${database_Takeover_Created_Date}=    Get From Dictionary    ${database_Custodian_Takeover_info}    CreatedDate
    ${database_Takeover_Created_User}=    Get From Dictionary    ${database_Custodian_Takeover_info}    CreatedUserId
    ${database_Custodian_Active_Date}=    Get From Dictionary    ${database_Custodian_Takeover_info}    CustodianActiveDate

    Log    ${database_Custodian_Name}
    Log    ${database_Custodian_Last_Name}
    Log    ${database_AB_Number}
    Log    ${database_Takeover_Comment}
    Log    ${database_Takeover_Created_Date}
    Log    ${database_Takeover_Created_User}
    Log    ${database_Custodian_Active_Date}

    Should Be Equal As Strings    ${Custodian_Name}    ${database_Custodian_Name}
    Should Be Equal As Strings    ${Custodian_Last_Name}    ${database_Custodian_Last_Name}
    Should Be Equal As Strings    ${Custodian_AB_Number}    ${database_AB_Number}
    Should Be Equal As Strings    ${Custodian_Active_Date_Ele}    ${database_Custodian_Active_Date}
    Should Be Equal As Strings    ${Custodian_Takeover_Comment}    ${database_Takeover_Comment}

    Sleep    5s


the user selects the Add New Custodian Menu
    ${Add_New_Custodian_Button}=    Get WebElement    xpath=//a[contains(@data-url, 'AddNewCustodian')]
    Wait Until Element Is Enabled    ${Add_New_Custodian_Button}
    Click Element    ${Add_New_Custodian_Button}
    Sleep    3s

the user inputs the Custodian Name on the name field
    ${Custodian_Name_Field}=    Get WebElement    xpath=//input[@id='CustodianName']
    Wait Until Element Is Enabled    ${Custodian_Name_Field}
    Click Element    ${Custodian_Name_Field}
    Input Text    ${Custodian_Name_Field}    Test
    Sleep    5s 

the user inputs the Custodian Lastname on the lastname field
    ${Custodian_LastName_Field}=    Get WebElement    xpath=//input[@id='CustodianLastName']
    Wait Until Element Is Enabled    ${Custodian_LastName_Field}
    Click Element    ${Custodian_LastName_Field}
    Input Text    ${Custodian_LastName_Field}    Automation Gen
    Sleep    5s 

the user inputs the AB Number of Custodian on ABNumber field
    ${Custodian_ABNum_Field}=    Get WebElement    xpath=//input[@id='CustodianABNumber']
    Wait Until Element Is Enabled    ${Custodian_ABNum_Field}
    Click Element    ${Custodian_ABNum_Field}
    Input Text    ${Custodian_ABNum_Field}    AB1234CC
    Sleep    5s

the user clicks on the date icon to select an Active Date 
    Log     Active Date set to current by default 
    Log To Console    Active Date set to current by default 

the user inputs a comment for Adding a New Custodian
    ${Comment_Field}=    Get WebElement    xpath=//*[@name='Comment']
    Wait Until Element Is Enabled    ${Comment_Field}
    Click Element    ${Comment_Field}
    Input Text    ${Comment_Field}    Testing Adding New Custodian Flow- Automation Script
    Sleep    5s

the user clicks save to save the new custodian
    ${Save_Button}=    Get WebElement    xpath=//*[text()[normalize-space(.)='Save']]
    Wait Until Element Is Enabled    ${Save_Button}
    Click Element    ${Save_Button}
    Sleep    5s

the user verifies that the custodian has been successfully added
    #Database Validation
    ${Added_Custodian_Database}=    DatabaseUtility.Execute Sql Query        ${Added_Custodian_Query}  
    Log   ${Added_Custodian_Database}
    Log To Console    ${Added_Custodian_Database}

    Sleep    5s

    #Extract the first item from the result 
    ${database_Custodian_Takeover_info}=    Get From List    ${Added_Custodian_Database}    0

    #Extract specific values from the dictionary and assign them to variables
    ${database_Custodian_Name}=    Get From Dictionary    ${database_Custodian_Takeover_info}    Name
    ${database_Custodian_Last_Name}=    Get From Dictionary    ${database_Custodian_Takeover_info}    LastName
    ${database_AB_Number}=    Get From Dictionary    ${database_Custodian_Takeover_info}    ABNumber
    ${database_Takeover_Comment}=    Get From Dictionary    ${database_Custodian_Takeover_info}    Comment
    ${database_Custodian_Active_Date}=    Get From Dictionary    ${database_Custodian_Takeover_info}    CustodianActiveDate

    Log    ${database_Custodian_Name}
    Log    ${database_Custodian_Last_Name}
    Log    ${database_AB_Number}
    Log    ${database_Takeover_Comment}
    Log    ${database_Custodian_Active_Date}

    Log    Successfully added Custodian: ${database_Custodian_Name} ${database_Custodian_Last_Name} ${database_AB_Number}


the user clicks on Custodian Maintenance
    Wait until element is enabled    ${Custodian_Maintenance_Locator}
    Sleep    2s
    Click Element                    ${Custodian_Maintenance_Locator}
    Sleep    5s
    Scroll element into view        ${First_Cusodian_On_Maintenance_List}
    Page should contain    ABNumber	    
    Log to console    Custodian Maintenance Table Expanded

the user selects a Custodian to edit 
    Wait until element is enabled            ${First_Cusodian_On_Maintenance_List}
    Click Element                            ${First_Cusodian_On_Maintenance_List}
    Sleep    3s

the user gets the current Custodian details
    #Custodian Name 
    ${Custodian_Current_Name}=    Get Value    ${Custodian_Current_Name_Locator}
    Log to console    Custodian Current Name:${Custodian_Current_Name}
    Set suite variable    ${Custodian_Current_Name}


    #Custodian Last Name 
    ${Custodian_Current_Last_Name}=    Get Value    ${Custodian_Current_Last_Name_Locator}
    Log to console    Custodian Current Last Name:${Custodian_Current_Last_Name}
    Set Suite Variable    ${Custodian_Current_Last_Name}

    #Custodian ABNumber 
    ${Custodian_Current_AB_Number}=    Get Value    ${Custodian_Current_AB_Number_Locator}
    Log to console    Custodian Current AB Number:${Custodian_Current_AB_Number}
    Set suite Variable    ${Custodian_Current_AB_Number}

    #Custodian Active Date 
    ${Current_Custodian_Active_Date}=    Get Value    ${Custodian_Current_Active_Date_Locator} 
    Log to console    Custodian Current Active Date:${Current_Custodian_Active_Date}
    Set suite variable    ${Current_Custodian_Active_Date}

the user makes changes to the selected Custodian
    #Editing Custodian Name 
    Click Element    ${Custodian_Current_Name_Locator}
    FOR    ${i}    IN RANGE    30
        Press Keys    ${Custodian_Current_Name_Locator}    BACKSPACE
    END

    Sleep    2s
    
    #Amend Custodian First Name 
    Click Element    ${Custodian_Current_Name_Locator}
    Input Text    ${Custodian_Current_Name_Locator}    ${New_Custodian_Name}
    Sleep    2s
    
    #Edit Custodian Last Name
    Click Element    ${Custodian_Current_Last_Name_Locator}
    FOR    ${i}    IN RANGE    30
        Press Keys    ${Custodian_Current_Last_Name_Locator}    BACKSPACE
    END
    
    Sleep    2s

    #Amend Custodian Last Name 
    Click Element    ${Custodian_Current_Last_Name_Locator}
    Input Text    ${Custodian_Current_Last_Name_Locator}    ${New_Custodian_Last_Name}
    Sleep    2s

    #Input Comment
    Click Element    ${Comment_Field_Locator}
    Input Text    ${Comment_Field_Locator}    ${Comment_Input}
    Sleep    2s

the user saves the Custodian change on the front end
    Wait until element is enabled    ${SAVE_ACTION_BUTTON}
    Click Element    ${SAVE_ACTION_BUTTON}
    Sleep    5s
    Log to console    the user saved Custodian Changes

the user verifies that the Custodian change was successfully saved correctly to the database
    #Database Validation
    ${Database_Edited_Custodian}=    DatabaseUtility.Execute Sql Query            ${Edited_Custodian_Query}
    ${database_edited_custodian_details}=    Get From List    ${Database_Edited_Custodian}    0

    ${database_Custodian_Updated_Name}=    Get From Dictionary    ${database_edited_custodian_details}    Name
    ${database_Custodian_Updated_Last_Name}=    Get From Dictionary    ${database_edited_custodian_details}    LastName
    ${database_AB_Number}=    Get From Dictionary    ${database_edited_custodian_details}    ABNumber
    ${database_Edited_Custodian_Comment}=    Get From Dictionary    ${database_edited_custodian_details}    Comment
    ${database_Custodian_Active_Date}=    Get From Dictionary    ${database_edited_custodian_details}    CustodianActiveDate
    ${database_Custodian_Active_Date_Split}    Set Variable    ${database_Custodian_Active_Date.split()[0]}

    Log to console    ${database_Custodian_Updated_Name}
    Log to console    ${database_Custodian_Updated_Last_Name}
    Log to console    ${database_AB_Number}
    Log to console    ${database_Edited_Custodian_Comment}
    Log to console    ${database_Custodian_Active_Date_Split}

    #Custodian Change Comparison
    Should not be equal    ${Custodian_Current_Name}    ${database_Custodian_Updated_Name}
    Log    Custodian Name Successfully updated from:${Custodian_Current_Name} to:${database_Custodian_Updated_Name} as indicated accordingly on the database. 

    Should not be equal    ${Custodian_Current_Last_Name}    ${database_Custodian_Updated_Last_Name}
    Log    Custodian Last Name Successfully updated from:${Custodian_Current_Last_Name} to:${database_Custodian_Updated_Last_Name} as indicated accordingly on the database. 

    #Details not edited should remain unchanged
    Should be Equal    ${Custodian_Current_AB_Number}    ${database_AB_Number} 
    Log    Custodian AB Number remained unchanged as:${database_AB_Number} as per the database.
    Should Be Equal    ${Current_Custodian_Active_Date}    ${database_Custodian_Active_Date_Split}
    Log    Custodian Active Date remained unchanged as:${database_Custodian_Active_Date_Split} as per the database.

    Log to console    The Edit Custodian Flow executed successfully & Test passed.

the user verifies the provinces displayed on the ATM Control Dashboard
    ${PROVINCES_LIST}    Create List
    ${PROVINCES_DISPLAYED}=    Get WebElements    ${PROVINCES_ATMC_DASHBOARD_LOCATOR}

    FOR    ${element}    IN    @{PROVINCES_DISPLAYED}
        ${province_text}    Get Text    ${element}
        Append To List    ${PROVINCES_LIST}    ${province_text}
    END

    Log Many    Provinces displayed on ATM Control Dashboard Front-end:${PROVINCES_LIST}


the user verifies the cost centres displayed on the ATM Control Dashboard
    #Eastern Cape
    ${EC_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_EASTERN_CAPE}=    Get WebElements    ${COST_CENTRE_EC_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_EASTERN_CAPE}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${EC_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${EASTERN_CAPE_COST_CENTRE_COUNT}=    Get length    ${EC_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for Eastern Cape:${EC_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for Eastern Cape:${EASTERN_CAPE_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_EC_COUNT_FE}=    Get web element    ${EC_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_EC_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_EC_COUNT_FE}
    Log    Eastern Cape Front-end Count:${TOTAL_COUNT_EC_FRONT_END}

    Run Keyword If  '${EASTERN_CAPE_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_EC_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  Counts do not match:${EASTERN_CAPE_COST_CENTRE_COUNT} != ${TOTAL_COUNT_EC_FRONT_END}
	

    #Western Cape
    ${WC_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_WESTERN_CAPE}=    Get WebElements    ${COST_CENTRE_WC_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_WESTERN_CAPE}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${WC_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${WESTERN_CAPE_COST_CENTRE_COUNT}=    Get length    ${WC_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for Western Cape:${WC_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for Western Cape:${WESTERN_CAPE_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_WC_COUNT_FE}=    Get web element    ${WC_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_WC_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_WC_COUNT_FE}
    Log    Western Cape Front-end Count:${TOTAL_COUNT_WC_FRONT_END}

    Run Keyword If  '${WESTERN_CAPE_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_WC_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${WESTERN_CAPE_COST_CENTRE_COUNT} != ${TOTAL_COUNT_WC_FRONT_END}"


    #Free State
    ${FS_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_FREE_STATE}=    Get WebElements    ${COST_CENTRE_FS_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_FREE_STATE}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${FS_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${FREE_STATE_COST_CENTRE_COUNT}=    Get length    ${FS_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for FREE_STATE:${FS_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for FREE_STATE:${FREE_STATE_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_FS_COUNT_FE}=    Get web element    ${FS_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_FS_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_FS_COUNT_FE}
    Log    FREE_STATE Front-end Count:${TOTAL_COUNT_FS_FRONT_END}

    Run Keyword If  '${FREE_STATE_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_FS_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${FREE_STATE_COST_CENTRE_COUNT} != ${TOTAL_COUNT_FS_FRONT_END}"
    

    #Gauteng North 
    ${GN_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_GAUTENG_NORTH}=    Get WebElements    ${COST_CENTRE_GN_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_GAUTENG_NORTH}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${GN_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${GAUTENG_NORTH_COST_CENTRE_COUNT}=    Get length    ${GN_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for GAUTENG_NORTH:${GN_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for GAUTENG_NORTH:${GAUTENG_NORTH_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_GN_COUNT_FE}=    Get web element    ${GN_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_GN_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_GN_COUNT_FE}
    Log    GAUTENG_NORTH Front-end Count:${TOTAL_COUNT_GN_FRONT_END}

    Run Keyword If  '${GAUTENG_NORTH_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_GN_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${GAUTENG_NORTH_COST_CENTRE_COUNT} != ${TOTAL_COUNT_GN_FRONT_END}"
    

    #Gauteng South
    ${GS_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_GAUTENG_SOUTH}=    Get WebElements    ${COST_CENTRE_GS_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_GAUTENG_SOUTH}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${GS_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${GAUTENG_SOUTH_COST_CENTRE_COUNT}=    Get length    ${GS_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for GAUTENG_SOUTH:${GS_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for GAUTENG_SOUTH:${GAUTENG_SOUTH_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_GS_COUNT_FE}=    Get web element    ${GS_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_GS_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_GS_COUNT_FE}
    Log    GAUTENG_SOUTH Front-end Count:${TOTAL_COUNT_GS_FRONT_END}

    Run Keyword If  '${GAUTENG_SOUTH_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_GS_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${GAUTENG_SOUTH_COST_CENTRE_COUNT} != ${TOTAL_COUNT_GS_FRONT_END}"
    

    #KWA ZULU NATAL 
    ${KZN_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_KZN}=    Get WebElements    ${COST_CENTRE_KZN_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_KZN}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${KZN_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${KZN_COST_CENTRE_COUNT}=    Get length    ${KZN_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for KZN:${KZN_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for KZN:${KZN_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_KZN_COUNT_FE}=    Get web element    ${KZN_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_KZN_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_KZN_COUNT_FE}
    Log    KZN Front-end Count:${TOTAL_COUNT_KZN_FRONT_END}

    Run Keyword If  '${KZN_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_KZN_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${KZN_COST_CENTRE_COUNT} != ${TOTAL_COUNT_KZN_FRONT_END}"
    
    #Limpopo
    ${LIMPOPO_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_LIMPOPO}=    Get WebElements    ${COST_CENTRE_LIMPOPO_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_LIMPOPO}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${LIMPOPO_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${LIMPOPO_COST_CENTRE_COUNT}=    Get length    ${LIMPOPO_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for LIMPOPO:${LIMPOPO_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for LIMPOPO:${LIMPOPO_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_LIMPOPO_COUNT_FE}=    Get web element    ${LIMPOPO_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_LIMPOPO_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_LIMPOPO_COUNT_FE}
    Log    LIMPOPO Front-end Count:${TOTAL_COUNT_LIMPOPO_FRONT_END}

    Run Keyword If  '${LIMPOPO_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_LIMPOPO_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${LIMPOPO_COST_CENTRE_COUNT} != ${TOTAL_COUNT_LIMPOPO_FRONT_END}"
    
    #Mpumalanga
    ${MPUMALANGA_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_MPUMALANGA}=    Get WebElements    ${COST_CENTRE_MPUMALANGA_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_MPUMALANGA}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${MPUMALANGA_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${MPUMALANGA_COST_CENTRE_COUNT}=    Get length    ${MPUMALANGA_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for MPUMALANGA:${MPUMALANGA_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for MPUMALANGA:${MPUMALANGA_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_MPUMALANGA_COUNT_FE}=    Get web element    ${MPUMALANGA_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_MPUMALANGA_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_MPUMALANGA_COUNT_FE}
    Log    MPUMALANGA Front-end Count:${TOTAL_COUNT_MPUMALANGA_FRONT_END}

    Run Keyword If  '${MPUMALANGA_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_MPUMALANGA_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${MPUMALANGA_COST_CENTRE_COUNT} != ${TOTAL_COUNT_MPUMALANGA_FRONT_END}"
    
    #North West
    ${NORTH_WEST_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_NORTH_WEST}=    Get WebElements    ${COST_CENTRE_NW_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_NORTH_WEST}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${NORTH_WEST_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${NORTH_WEST_COST_CENTRE_COUNT}=    Get length    ${NORTH_WEST_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for NORTH_WEST:${NORTH_WEST_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for NORTH_WEST:${NORTH_WEST_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_NORTH_WEST_COUNT_FE}=    Get web element    ${NW_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_NORTH_WEST_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_NORTH_WEST_COUNT_FE}
    Log    NORTH_WEST Front-end Count:${TOTAL_COUNT_NORTH_WEST_FRONT_END}

    Run Keyword If  '${NORTH_WEST_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_NORTH_WEST_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${NORTH_WEST_COST_CENTRE_COUNT} != ${TOTAL_COUNT_NORTH_WEST_FRONT_END}"
    
    #Northern Cape
    ${NORTHERN_CAPE_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_NORTHERN_CAPE}=    Get WebElements    ${COST_CENTRE_NC_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_NORTHERN_CAPE}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${NORTHERN_CAPE_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${NORTHERN_CAPE_COST_CENTRE_COUNT}=    Get length    ${NORTHERN_CAPE_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for NORTHERN_CAPE:${NORTHERN_CAPE_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for NORTHERN_CAPE:${NORTHERN_CAPE_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_NORTHERN_CAPE_COUNT_FE}=    Get web element    ${NC_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_NORTHERN_CAPE_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_NORTHERN_CAPE_COUNT_FE}
    Log    NORTHERN_CAPE Front-end Count:${TOTAL_COUNT_NORTHERN_CAPE_FRONT_END}

    Run Keyword If  '${NORTHERN_CAPE_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_NORTHERN_CAPE_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${NORTHERN_CAPE_COST_CENTRE_COUNT} != ${TOTAL_COUNT_NORTHERN_CAPE_FRONT_END}"
    
    #Unknown
    ${UNKNOWN_COST_CENTRE_LIST}    Create List
    ${COST_CENTRES_UNKNOWN}=    Get WebElements    ${COST_CENTRE_UNKNOWN_LOCATOR}

    FOR    ${element}    IN    @{COST_CENTRES_UNKNOWN}
        ${cost_centre_text}    Get Text    ${element}
        Append To List    ${UNKNOWN_COST_CENTRE_LIST}    ${cost_centre_text}
    END

    ${UNKNOWN_COST_CENTRE_COUNT}=    Get length    ${UNKNOWN_COST_CENTRE_LIST}
    
    Log Many    Cost Centres displayed for UNKNOWN:${UNKNOWN_COST_CENTRE_LIST}
    Log    Total Cost Centres displayed for UNKNOWN:${UNKNOWN_COST_CENTRE_COUNT}

    #Total Cost Centre length must match total count on front-end 
    ${TOTAL_COST_CENTRE_UNKNOWN_COUNT_FE}=    Get web element    ${UNKNOWN_TOTAL_COST_CENTRE_COUNT_LOCATOR}
    ${TOTAL_COUNT_UNKNOWN_FRONT_END}=    Get Text    ${TOTAL_COST_CENTRE_UNKNOWN_COUNT_FE}
    Log    UNKNOWN Front-end Count:${TOTAL_COUNT_UNKNOWN_FRONT_END}

    Run Keyword If  '${UNKNOWN_COST_CENTRE_COUNT}' == '${TOTAL_COUNT_UNKNOWN_FRONT_END}'  
    ...  Log  "Counts match: Test Passed"  
    ...  ELSE  Fail  "Counts do not match: ${UNKNOWN_COST_CENTRE_COUNT} != ${TOTAL_COUNT_UNKNOWN_FRONT_END}"
    
the user validates the Region filter on the ATM Control Dashboard
    #Open Filter 
    Click Element    ${FILTER_LOCATOR}
    Sleep    2s

    #Click on Region Filter 
    Click Element    ${REGION_FILTER}
    Sleep    2s
    
    #Retrieve Region Input 
    ${PROVINCE_SELECTED_NAME}=    Get Text    ${REGION_INPUT_EASTERN_CAPE}
    Log    ${PROVINCE_SELECTED_NAME}
    ${province_selected_normalized}=  Remove String  ${PROVINCE_SELECTED_NAME}  ${SPACE}
    
    #Select a Region
    Click Element    ${REGION_INPUT_EASTERN_CAPE}

    #Click Run Button 
    Click Element    ${RUN_BUTTON}
    Sleep    5s

    #Verify that only 1 region was returned 
    ${ALL_POSSIBLE_PROVINCES_RETURNED}=    Get WebElements    ${ALL_POSSIBLE_PROVINCES}
    ${province_count}=  Get Length  ${ALL_POSSIBLE_PROVINCES_RETURNED}
    #Fail if more than one province is found
    Run Keyword If    ${province_count} > 1  
    ...    Fail    More than one province returned by region filter! Expected only 1, Found: ${province_count}

    #Verify that only Eastern Cape and its cost centres is displayed 
    FOR    ${element}    IN    ${ALL_POSSIBLE_PROVINCES}
        Wait Until Element Is Visible    ${element}    timeout=10s
        ${province_name}=    Get Text    ${element}
        ${province_name_normalized}=  Remove String  ${province_name}  ${SPACE}
        Log    Province returned by Region filter is:${province_name_normalized}
        Run Keyword If    	'${province_name_normalized}' != '${province_selected_normalized}'	Fail    Region filter returned incorrect result! Expected: ${PROVINCE_SELECTED_NAME}, Found: ${province_name}
    END

the user validates the ATM Status filter on the ATM Control Dashboard
    #Open Filter 
    Click Element    ${FILTER_LOCATOR}
    Sleep    2s

    #Click on ATM Status Filter 
    Click Element    ${ATM_STATUS_FILTER_LOCATOR}
    Sleep    2s
    
    #Select a Status
    Click Element    ${ATM_STATUS_REQUIRED_COUNT_INPUT}

    #Click Run Button 
    Click Element    ${RUN_BUTTON}
    Sleep    5s

    #Extract all 'Required Count' statuses
    ${required_count_elements}=    Get WebElements    //td[contains(., 'Required Count')]

    #Verify that no 'No Required Count' statuses are present
    ${no_required_count_elements}=    Get WebElements    //td[contains(., 'No Required Count')]
    ${length_no_required_count_elements}=    Get Length    ${no_required_count_elements}
    Run Keyword If    ${length_no_required_count_elements} != 0    Fail    "Unexpected: Found 'No Required Count' statuses!"

    #Verify that the correct 'Required Count' statuses are returned
    ${length_required_count_elements}=    Get Length    ${required_count_elements}
    Run Keyword If    ${length_required_count_elements} == 0    Fail    "Critical: No 'Required Count' statuses found!"

    Log    "Success: Only 'Required Count' statuses are returned by the ATM Status filter."

the user validates the Cost Centre filter on the ATM Control Dashboard
    [Arguments]    ${EXPECTED_COST_CENTRE_NUMBER}
    #Open Filter 
    Click Element    ${FILTER_LOCATOR}
    Sleep    2s

    #Click on Cost Centre Filter 
    Click Element    ${COST_CENTRE_FILTER_FIELD}
    Sleep    2s
    Press Key    ${COST_CENTRE_FILTER_FIELD}    BACKSPACE BACKSPACE

    #Input Cost Centre 
    Input Text    ${COST_CENTRE_FILTER_FIELD}    ${EXPECTED_COST_CENTRE_NUMBER}
    Sleep    2s

    #Click Run Button 
    Click Element    ${RUN_BUTTON}
    Sleep    5s

    #Verify that only one Cost Centre was returned 

    ${ALL_POSSIBLE_COST_CENTRES_RETURNED}=    Get WebElements    ${ALL_POSSIBLE_COST_CENTRES}
    ${cost_centre_count}=  Get Length  ${ALL_POSSIBLE_COST_CENTRES_RETURNED}

    #Fail if more than one cost centre is found
    Run Keyword If    ${cost_centre_count} > 1  
    ...    Fail    More than one cost centre returned by cost centre filter! Expected only 1, Found:${cost_centre_count}

    #Verify that only Expected cost centre is displayed 
    FOR    ${element}    IN    ${ALL_POSSIBLE_COST_CENTRES}
        Wait Until Element Is Visible    ${element}    timeout=10s
        ${cost_centre_name}=    Get Text    ${element}
        ${extracted_cost_centre_number}=    String.Split String    ${cost_centre_name}    -
        ${final_cost_centre_number}=    Get From List    ${extracted_cost_centre_number}    0
        ${final_cost_centre_number}=    String.Strip String    ${final_cost_centre_number}
        Log    Cost Centre returned by Cost Centre filter is:${final_cost_centre_number}

        Run Keyword If    	'${final_cost_centre_number}' != '${EXPECTED_COST_CENTRE_NUMBER}'	Fail    Cost Centre filter returned incorrect result! Expected: ${EXPECTED_COST_CENTRE_NUMBER}, Found: ${final_cost_centre_number}  

        Run Keyword If    '${final_cost_centre_number}' == '${EXPECTED_COST_CENTRE_NUMBER}'    Log    Result returned by cost centre filter functions correctly.  

    END
