*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../keywords/front_end/Bin_Table_Landing_Page.robot
Resource                                            ../../../../keywords/front_end/APC_Portal_Landing_Page.robot


*** Variables ***
${SUITE NAME}               APC Landing Page 
${TEST_CASE_ID}             RAC29a-TC-792




*** Keywords ***
Verify Correct Landing Page URL is Displayed After Login
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${EXPECTED_APC_LANDING_PAGE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture                ${BASE_URL}
    When The user is redirected to the APC Portal Landing page
    And the user retrieves the current URL
    Then the URL must match the expected APC Landing page URL    ${EXPECTED_APC_LANDING_PAGE_URL}

| *** Test Cases ***                                                                                                                 |        *DOCUMENTATION*     |         *BASE_URL*         |       *EXPECTED_APC_LANDING_PAGE_URL*                                                         |
| Admin_Verify Correct Landing Page URL is Displayed After Login     | Verify Correct Landing Page URL is Displayed After Login   | Login URL Validation       |           ${EMPTY}         |   https://apc-portal.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/bin/landing    |
