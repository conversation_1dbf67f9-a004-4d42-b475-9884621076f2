*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinsToReview_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for all Bins to be reviewed using a Bin Number on the GetBinsToReview Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User gets all the Bin numbers that must be reviewed from the Database
    When The User sends a Get Request for GetBinsToReview to get all bins that are still to be reviwed      ${BASE_URL}
    And The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The all the Bin numbers' details returned by the GetBinsToReview Controller must be the same as the details of the bins queried from the Database


| *** Test Cases ***                                                                                                                                                                                          |        *DOCUMENTATION*    		                 |         *BASE_URL*                 |    *EXPECTED_STATUS_CODE*   |
| Get all Bins to be reviwed from the database, and verify the Bins' details against getBinsToReview controller   | Search for all Bins to be reviewed using a Bin Number on the GetBinsToReview Controller   | Search Bin by Number on the GetBinsToReview API  |                                    |           200               |
