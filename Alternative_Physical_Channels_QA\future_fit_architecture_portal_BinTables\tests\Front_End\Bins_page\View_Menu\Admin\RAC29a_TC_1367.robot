*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1367




*** Keywords ***
Search for Bins on the View Entries page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                                            ${BASE_URL}
    When The User clicks Bins Menu
    And The user gets the number of Bins displayed on the current page
    And The user clicks the 'Search' input and press Enter button
    Then The total number of BINS displayed on the page must be the same as the initial total

| *** Test Cases ***                                                                                                          |        *DOCUMENTATION*    		            |         *BASE_URL*                 |
| Admin_Verify Admin Cannot Trigger Search if Search Field is Empty              | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.  |           ${EMPTY}                 |
