*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/UpdateBin_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Update Pending Bin(s) details




*** Keywords ***
Update Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${CURRENT_BIN_STATUS}   ${EXPECTED_STATUS_CODE}   ${EXPECTED_ERROR_MESSAGE}     &{BINS_DETAILS}
    Set Test Documentation  ${DOCUMENTATION}
    Given The user verifies that the current BIN is as expected    ${CURRENT_BIN_STATUS}    &{BINS_DETAILS}
    When The User Populates the Update Bin JSON payload with       &{BINS_DETAILS}
    And The User sends the Update Bin API Request                  ${BASE_URL}
    And The service returns an expected status code                ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed              ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                    |        *DOCUMENTATION*    		  |         *BASE_URL*                  |    *CURRENT_BIN_STATUS*     |    *EXPECTED_STATUS_CODE*   |    *EXPECTED_ERROR_MESSAGE*                                                  |                 *BINS_DETAILS*                                                                                                                                                                 |
#| Update (Edit) the deleted Bin.                                        | Update Bin                    |        Edit Pending Bin(s)          |                                     |         1                   |            400              |    The bin with the specified identifier was not found.                      |   binId1=5908d8b3-668f-4d80-9f74-d009978fbdbc  | bin1=999999  | date1=2024-10-28 | binTypeIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208                                       | binOutcome1=Deleted   |
| Update (Edit) the Bin using the binId the belongs to another Bin.     | Update Bin                    |        Edit Pending Bin(s)          |                                     |         0                   |            400              |    The bin with the specified identifier was not found.                      |   binId1=2b89cf43-cf27-4ecc-b795-f10e22560fea  | bin1=3030405 | date1=2027-11-11 | binTypeIds1=a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e  | binOutcome1=added   |
| Update (Edit) the existing Bin using the incorrect 'binId'.           | Update Bin                    |        Edit Pending Bin(s)          |                                     |         0                   |            400              |    The bin with the specified identifier was not found.                      |   binId1=df72ccca-c5fa-4e6c-96fe-03b180929080  | bin1=9046578 | date1=2027-11-11 | binTypeIds1=a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e  | binOutcome1=added   |
| Update (Edit) the existing Bin using the incorrect 'actionDate'.      | Update Bin                    |        Edit Pending Bin(s)          |                                     |         0                   |            400              |    Unable to convert \"12027-11-11\" to DateOnly.                            |   binId1=df62ccca-c5fa-4e6c-96fe-03b180929080  | bin1=9046578 | date1=12027-11-11 | binTypeIds1=a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e | binOutcome1=added  |
| Update (Edit) the existing Bin using the incorrect 'binTypeIds'.      | Update Bin                    |        Edit Pending Bin(s)          |                                     |         0                   |            400              |    The JSON value could not be converted to System.Guid. Path: $.binTypeIds  |   binId1=df62ccca-c5fa-4e6c-96fe-03b180929080  | bin1=9046578 | date1=2027-11-11 | binTypeIds1=my_bin,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e | binOutcome1=Added                                 |
#| Update (Edit) the incorrect Bin Number(Non-Existing Bin).             | Update Bin                    |        Edit Pending Bin(s)          |                                     |         1                   |            400              |    The bin with the specified identifier was not found.    |   binId1=2b89cf43-cf27-4ecc-b795-f10e22560fea  | bin1=123     | date1=2027-11-11 | binTypeIds1=a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e | binOutcome1=Added   |
