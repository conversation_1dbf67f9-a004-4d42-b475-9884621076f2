*** Settings ***

Documentation  Get environment variables passed as argument from commandline

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
#Library                                             ../utility/PostExecutionUpdate.py

*** Keywords ***
Set up environment variables   
    Set Environment Variable    DEVICE_NAME    ${DEVICE_NAME}
    Set Environment Variable    TEST_EXECUTION_NAME    ${TEST_EXECUTION_NAME}


 
  