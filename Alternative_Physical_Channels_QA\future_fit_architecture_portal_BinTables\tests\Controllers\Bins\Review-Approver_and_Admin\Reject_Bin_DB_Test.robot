*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/RejectBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Reject Database Bins
    [Arguments]        ${DOCUMENTATION}    ${REJECTED_BIN_STATUS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user gets all pending Bins from the database that can be rejected
    When The user rejects all pending Bins retrieved from the database
    Then All rejected bins must be showing as expcted on the database     ${REJECTED_BIN_STATUS}

| *** Test Cases ***                                                          |                   *DOCUMENTATION*                  |       *REJECTED_BIN_STATUS*   |
| Reject all database saved bins that are pending.   | Reject Database Bins   | Reject all pending Bins that are in the database   |               Rejected        |
