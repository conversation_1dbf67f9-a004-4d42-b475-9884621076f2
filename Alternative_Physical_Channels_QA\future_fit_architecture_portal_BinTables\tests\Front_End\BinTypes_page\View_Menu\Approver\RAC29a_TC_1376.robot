*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1376




*** Keywords ***
Verify Bin Types displayed on View Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal  ${BASE_URL}
    When The User clicks Bin Type Menu
    Then The user must not be able to add, edit or delete a Bin Type without Admin Access

| *** Test Cases ***                                                                                                                                      |        *DOCUMENTATION*    		         |         *BASE_URL*               |
| Approver_Verify that the user cannot view details for a specific BIN Type if they don't have approval rights        | Verify Bin Types displayed on View Page   | Verify bin types against the database.    |           ${EMPTY}               |
