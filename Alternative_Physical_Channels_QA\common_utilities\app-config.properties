# Database Credentials
BIN_DB_HOST= oss-vip-02186.corp.dsarena.com
BIN_DB_NAME= appdb
BIN_DB_USER= app_account
BIN_DB_PASSWORD= CcQZglNU0E

DB_HOST= oss-dsdc-01371.corp.dsarena.com
DB_SCHEMA= ATM_Marketing
DB_User= app_account
DB_PWD= DG9UIMbXWs
DB_PORT= 3306 

MS_DB_HOST= XZAPBCC1SQL1004
MS_DB_SCHEMA= VMS_UAT
MS_DB_User= apl
MS_DB_PWD= Pa$$w0rd


#APC ATM Marketing Campaign Portal URLs
APC_DEV = https://apc-portal.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/
APC_SIT = https://apc-portal.atm-marketing-sit.rbb-banking.sdc-nonprod.caas.absa.co.za/
APC_UAT = https://apc-portal.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/

#APC ATM Marketing Campaign Controllers Endpoints
APC_API_DEV_BASE_URL = https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za
APC_API_SIT_BASE_URL = https://atm-marketing.atm-marketing-sit.rbb-banking.sdc-nonprod.caas.absa.co.za
APC_API_UAT_BASE_URL = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za
APC_API_LAB_INTEGRATION = https://esb.api.uat.absa.co.za:1001/enterprise-uat/uat/rbbpc/

#APC GROUP Variables

BUSINESS_USER_KEY = 4f3fcaf9-08bb-4af0-8736-aa1acc218567

#QMetry details

entityType = ROBOT
testsuiteName = Bin Table Phase 1 UAT Regression - Front End
testsuiteId = RAC29a-TS-1005
projectID = 987
apikey = XIF8l8jl64MqoO5i6wesZ1i9bM1mJ99Prgy3qXat

#VMS Portal URLs
VMS_UAT = https://vms.uat.absa.africa/
VMS_PROD = https://vms.absa.africa/


#Database Queries
#Main Calls logged for ATMs countrywide
GAUTENG_MAIN_CALLS_QUERY_THIS_WEEK=WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'GT' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Gauteng%') SELECT * FROM ProvinceData;
LIMPOPO_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'NP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Limpopo%') SELECT * FROM ProvinceData;
MPUMALANGA_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'MP' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Mpumalanga%') SELECT * FROM ProvinceData;
NORTH_WEST_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'NW' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%North West%') SELECT * FROM ProvinceData;
FREE_STATE_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'FS' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Free State%') SELECT * FROM ProvinceData;
KWA-ZULU_NATAL_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'NL' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%zulu%') SELECT * FROM ProvinceData;
WESTERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'WC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Western Cape%') SELECT * FROM ProvinceData;
EASTERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'EC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Eastern Cape%') SELECT * FROM ProvinceData;
NORTHERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK= WITH NewTable AS (SELECT g.region AS Region, COUNT(t.[Ref Number]) AS AllCount FROM core.ticket_history t INNER JOIN core.gasper_details g ON t.[ATM Number] = g.ID WHERE t.Mode_link = 1 AND t.Status <> 4 AND (DATEDIFF(DAY, t.[start date], GETDATE()) < 7 OR DATEDIFF(DAY, t.[warning date], GETDATE()) < 1) GROUP BY g.REGION), ProvinceData AS (SELECT 'NC' AS Region, SUM(AllCount) AS Value FROM NewTable WHERE Region LIKE '%Northern Cape%') SELECT * FROM ProvinceData;


#Bin Tables Controllers Endpoints
BIN_TABLES_API_DEV_BASE_URL = https://ffa-loginservicebase.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za
BIN_TABLES_API_SIT_BASE_URL = https://ffa-loginservicebase.atm-marketing-sit.rbb-banking.sdc-nonprod.caas.absa.co.za
BIN_TABLES_API_UAT_BASE_URL = https://ffa-loginservicebase.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za
BIN_TABLES_API_LAB_INTEGRATION = https://esb.api.uat.absa.co.za:1001/enterprise-uat/uat/rbbpc/



