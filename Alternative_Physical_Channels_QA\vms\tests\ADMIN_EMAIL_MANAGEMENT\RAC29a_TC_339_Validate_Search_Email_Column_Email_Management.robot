*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Validate Search-Email column functionality in Email Management

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/EmailManagement.robot
Resource                                            ../../keywords/common/DatabaseConnector.robot

*** Variables ***


*** Keywords ***
Validate Search Email Column Functionality
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - Email Management
    And Validate Email Management Email Search Functionality

*** Test Cases ***
| Validate Search-Email column- Email Management: Validate email search functionality works correctly in Email Management. | 
| | [Documentation] | Validate that the search input field works correctly for email addresses and displays appropriate search results. |
| | Validate Search Email Column Functionality | Validate that the search input field works correctly for email addresses and displays appropriate search results. | VMS_UAT |
