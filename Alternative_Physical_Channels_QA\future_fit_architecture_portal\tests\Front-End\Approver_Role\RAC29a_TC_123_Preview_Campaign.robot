*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing the Preview function on Campaign Approvals Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../common_utilities/Login.robot
Resource                                            ../../../../common_utilities/Logout.robot
Resource                                            ../../../Keywords/atm_marketing/Dashboard.robot
Resource                                            ../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/atm_marketing/NegativeScenarios.robot
Resource                                            ../../../keywords/common/Navigation.robot
Resource                                            ../../../keywords/atm_marketing/Approvals.robot

*** Keyword ***
Validating the Preview function on Approval Page preview 
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    When The user navigates to the Campaign Approvals page   

    Then The user navigates to the Campaign Approvals page and previews a single campaign     #The user navigates to the Campaign Approvals page and previews a single campaign

| *Test Cases*                                                                                      |      *DOCUMENTATION*                         |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   | 
| RAC29a_TC_123_FFT_Preview_Campaign   |  Validating the Preview function on Approval Page preview  |  Close Button on Campaign Approvals Preview   |    BUSINESS_APPROVER       |     APC_UAT             |