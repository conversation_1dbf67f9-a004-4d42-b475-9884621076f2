lsp: 2025-06-18 09:54:38 UTC pid: 27936 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-06-18 09:54:38 UTC pid: 27936 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-06-18 09:54:38 UTC pid: 27936 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-06-18 09:54:38 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 27936

lsp: 2025-06-18 09:54:38 UTC pid: 27936 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-06-18 09:54:38 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']

lsp: 2025-06-18 09:55:04 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 4, method: textDocument/codeAction

lsp: 2025-06-18 09:55:04 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 8, method: textDocument/codeAction

lsp: 2025-06-18 09:55:04 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 9, method: textDocument/codeAction

lsp: 2025-06-18 09:55:04 UTC pid: 27936 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001737E46F560>

lsp: 2025-06-18 09:55:04 UTC pid: 27936 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001737E46F560>

lsp: 2025-06-18 09:55:05 UTC pid: 27936 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001737E46F560>

lsp: 2025-06-18 09:55:07 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 11

lsp: 2025-06-18 09:55:17 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 15, method: textDocument/codeAction

lsp: 2025-06-18 09:57:26 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 17

lsp: 2025-06-18 09:58:39 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 20

lsp: 2025-06-18 09:58:39 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 22, method: textDocument/hover

lsp: 2025-06-18 09:58:39 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 21

lsp: 2025-06-18 09:58:53 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 26, method: textDocument/codeAction

lsp: 2025-06-18 09:58:53 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 27, method: textDocument/hover

lsp: 2025-06-18 09:59:09 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 34, method: textDocument/documentHighlight

lsp: 2025-06-18 10:00:12 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 43, method: textDocument/hover

lsp: 2025-06-18 10:02:20 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 79

lsp: 2025-06-18 10:02:33 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 91, method: textDocument/hover

lsp: 2025-06-18 10:02:33 UTC pid: 27936 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000001737E46F560>

lsp: 2025-06-18 10:06:46 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 141

lsp: 2025-06-18 10:08:02 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 181, method: textDocument/hover

lsp: 2025-06-18 10:08:04 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 183, method: textDocument/hover

lsp: 2025-06-18 10:09:11 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 198, method: textDocument/hover

lsp: 2025-06-18 10:09:23 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 201, method: textDocument/documentSymbol

lsp: 2025-06-18 10:09:23 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 202, method: textDocument/documentSymbol

lsp: 2025-06-18 10:09:23 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 204, method: textDocument/hover

lsp: 2025-06-18 10:09:23 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 205, method: textDocument/hover

lsp: 2025-06-18 10:09:23 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 203

lsp: 2025-06-18 10:09:23 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 207, method: textDocument/hover

lsp: 2025-06-18 10:09:53 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 210, method: textDocument/hover

lsp: 2025-06-18 10:10:35 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 225

lsp: 2025-06-18 10:11:13 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 244, method: textDocument/hover

lsp: 2025-06-18 10:11:14 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 245, method: textDocument/hover

lsp: 2025-06-18 10:12:01 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 259, method: textDocument/hover

lsp: 2025-06-18 10:12:01 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 260

lsp: 2025-06-18 10:12:06 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 261, method: textDocument/hover

lsp: 2025-06-18 10:12:54 UTC pid: 27936 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 278

