lsp: 2025-06-18 11:21:40 UTC pid: 31536 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-06-18 11:21:40 UTC pid: 31536 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-06-18 11:21:40 UTC pid: 31536 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-06-18 11:21:40 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 31536

lsp: 2025-06-18 11:21:40 UTC pid: 31536 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-06-18 11:21:40 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']

lsp: 2025-06-18 11:36:07 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 37, method: textDocument/hover

lsp: 2025-06-18 11:36:07 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:36:10 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 39, method: textDocument/hover

lsp: 2025-06-18 11:36:10 UTC pid: 31536 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:36:11 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 40, method: textDocument/hover

lsp: 2025-06-18 11:36:11 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:36:13 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 45, method: textDocument/hover

lsp: 2025-06-18 11:36:13 UTC pid: 31536 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:36:38 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 59, method: textDocument/hover

lsp: 2025-06-18 11:36:38 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 60, method: textDocument/hover

lsp: 2025-06-18 11:36:38 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:36:38 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 62, method: textDocument/hover

lsp: 2025-06-18 11:37:08 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 100

lsp: 2025-06-18 11:37:13 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 110, method: textDocument/codeAction

lsp: 2025-06-18 11:37:13 UTC pid: 31536 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:37:15 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 117, method: textDocument/hover

lsp: 2025-06-18 11:37:15 UTC pid: 31536 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:37:22 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 128, method: textDocument/hover

lsp: 2025-06-18 11:37:42 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 152, method: textDocument/hover

lsp: 2025-06-18 11:37:48 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 162, method: textDocument/hover

lsp: 2025-06-18 11:37:48 UTC pid: 31536 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:37:59 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 164

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 167

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 165

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 168, method: textDocument/foldingRange

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 169, method: textDocument/codeLens

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 171, method: textDocument/documentSymbol

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 170

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 174

lsp: 2025-06-18 11:38:03 UTC pid: 31536 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:18 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 184

lsp: 2025-06-18 11:38:18 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 191

lsp: 2025-06-18 11:38:31 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 206, method: textDocument/hover

lsp: 2025-06-18 11:38:48 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 226, method: textDocument/hover

lsp: 2025-06-18 11:38:48 UTC pid: 31536 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:49 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 227, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:49 UTC pid: 31536 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:50 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 230, method: textDocument/hover

lsp: 2025-06-18 11:38:50 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:50 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 231, method: textDocument/hover

lsp: 2025-06-18 11:38:50 UTC pid: 31536 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:51 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 232, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:51 UTC pid: 31536 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:52 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 234, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:52 UTC pid: 31536 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:52 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 235, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:52 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:53 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 236

lsp: 2025-06-18 11:38:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 238, method: textDocument/hover

lsp: 2025-06-18 11:38:54 UTC pid: 31536 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:55 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 242, method: textDocument/hover

lsp: 2025-06-18 11:38:55 UTC pid: 31536 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 243, method: textDocument/hover

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 244, method: textDocument/hover

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - ThreadPoolExecutor-0_7 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 240, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 245, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 246, method: textDocument/documentHighlight

lsp: 2025-06-18 11:38:57 UTC pid: 31536 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:38:59 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 249, method: textDocument/hover

lsp: 2025-06-18 11:39:00 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 250, method: textDocument/hover

lsp: 2025-06-18 11:39:00 UTC pid: 31536 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:01 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 254, method: textDocument/hover

lsp: 2025-06-18 11:39:01 UTC pid: 31536 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 256, method: textDocument/hover

lsp: 2025-06-18 11:39:03 UTC pid: 31536 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 264, method: textDocument/hover

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 265, method: textDocument/hover

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 266, method: textDocument/hover

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 267, method: textDocument/hover

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 268, method: textDocument/documentHighlight

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 269, method: textDocument/documentHighlight

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 270, method: textDocument/documentHighlight

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 271, method: textDocument/documentHighlight

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:39:54 UTC pid: 31536 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:40:03 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 273

lsp: 2025-06-18 11:43:25 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 449, method: textDocument/hover

lsp: 2025-06-18 11:43:25 UTC pid: 31536 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:43:25 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 450, method: textDocument/hover

lsp: 2025-06-18 11:43:25 UTC pid: 31536 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 11:43:27 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 453

lsp: 2025-06-18 11:43:33 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 463

lsp: 2025-06-18 13:34:58 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 531

lsp: 2025-06-18 13:34:58 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 533

lsp: 2025-06-18 13:47:29 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Ignoring notification for unknown method $/setTrace

lsp: 2025-06-18 13:50:16 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 558, method: textDocument/hover

lsp: 2025-06-18 13:50:27 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 562, method: textDocument/definition

lsp: 2025-06-18 13:50:27 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 561

lsp: 2025-06-18 13:50:27 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 563

lsp: 2025-06-18 13:50:27 UTC pid: 31536 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000025926A6F560>

lsp: 2025-06-18 13:50:37 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 587, method: textDocument/hover

lsp: 2025-06-18 14:03:12 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 640

lsp: 2025-06-18 15:17:39 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 659, method: textDocument/codeAction

lsp: 2025-06-18 15:46:44 UTC pid: 31536 - Thread-11 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_6, started 26256)>

self: <Condition(<unlocked _thread.lock object at 0x00000259289B4B40>, 1)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\basic.py", line 328, in new_func
   return func(*args, **kwargs)
 File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\robotframework_ls_impl.py", line 987, in _threaded_api_request
   if wait_for_message_matcher(
 File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\client_base.py", line 106, in wait_for_message_matcher
   if message_matcher.event.wait(delta):
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 652, in wait
   with self._cond:
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 367, in wait
   self._waiters.remove(waiter)

=============================== END Thread Dump ===============================

lsp: 2025-06-18 15:49:58 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 662

lsp: 2025-06-18 15:50:07 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 664

lsp: 2025-06-18 15:50:08 UTC pid: 31536 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 665

lsp: 2025-06-18 16:05:01 UTC pid: 31536 - Thread-14 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'jsonrpc': '2.0', 'method': '$/testsCollected', 'params': {'uri': 'file:///c:/Alternative/Alternative_Physical_Channels_QA/common_utilities/common_keywords.robot', 'testInfo': []}} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
OSError: [Errno 22] Invalid argument
lsp: 2025-06-18 16:05:01 UTC pid: 31536 - Thread-14 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'jsonrpc': '2.0', 'method': '$/testsCollected', 'params': {'uri': 'file:///c:/Alternative/Alternative_Physical_Channels_QA/common_utilities/Login.robot', 'testInfo': []}} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
OSError: [Errno 22] Invalid argument
lsp: 2025-06-18 16:05:01 UTC pid: 31536 - Thread-14 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'jsonrpc': '2.0', 'method': '$/testsCollected', 'params': {'uri': 'file:///c:/Alternative/Alternative_Physical_Channels_QA/common_utilities/Logout.robot', 'testInfo': []}} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
OSError: [Errno 22] Invalid argument
