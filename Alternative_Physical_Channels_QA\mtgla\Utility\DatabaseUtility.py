import os
from pathlib import Path
import pyodbc
from jproperties import Properties
from robot.api.deco import keyword
from robot.api.deco import keyword
import  time

class DatabaseUtility:

    @staticmethod
    @keyword('Execute Sql Query')
    def execute_sql_query(query, max_retries=3, delay=5):
        # Load the DB host from the properties file
        db_host = DatabaseUtility.read_property_from_file('db_host')

        # Define the connection string for Windows Authentication
        connection_string = f'DRIVER={{SQL Server}};SERVER={db_host};Database=ATM_Mismatch_DEV;Trusted_Connection=yes;Timeout=30;'
        #connection_string = 'DRIVER={SQL Server};SERVER=ZAUSDCMSQL04058\\MTGLA_MAIN1_UAT;Timeout=30;'

        attempt = 0     #Keeps track of how many connection attempts have been made
        while attempt < max_retries:    #Default 3
            try:
                # Attempt to connect to the database
                conn = pyodbc.connect(connection_string)
                cursor = conn.cursor()
                
                # Execute the query
                cursor.execute(query)

                # Fetch results
                if cursor.description:
                    columns = [column[0] for column in cursor.description]
                    results = cursor.fetchall()
                    return [dict(zip(columns, row)) for row in results]
                else:
                    return None

            except pyodbc.Error as e:
                # Log and retry if a connection error occurs
                print(f"Connection failed on attempt {attempt + 1}: {e}")
                attempt += 1
                if attempt < max_retries:
                    print(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                else:
                    raise RuntimeError(f"Connection failed after {max_retries} attempts: {e}")

            finally:
                # Close the connection if it was established
                if 'conn' in locals():
                    conn.close()



    @staticmethod
    def read_property_from_file(str_property_name):
        configs = Properties()
        root = Path(__file__).parent.parent
        #print("path is:",root)
        configs_file_path = os.path.join(root, 'utility/app-config.properties')

        with open(configs_file_path, 'rb') as config_file:
            configs.load(config_file)
            try:
                if configs[str_property_name] is not None:
                    property_retrieved = configs.get(str_property_name).data
                    # print("Property value fetched is: ", property_retrieved)
                    return property_retrieved
                else:
                    print("Property named: " + str_property_name + " does not exist on file!")
                    return None
            except KeyError as ke:
                print(f'{ke}, lookup key was: ' + str(str_property_name))
                return None
