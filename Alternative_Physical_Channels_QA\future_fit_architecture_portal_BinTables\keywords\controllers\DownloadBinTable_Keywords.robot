*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/deviceversions/DownloadBinTable.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot

#***********************************PROJECT VARIABLES***************************************

*** Variables ***



*** Keywords ***

The User sends a Get Request to download bin table
    [Arguments]     ${BASE_URL}     ${DEVICE_NAME}     ${DEVICE_VERSION_NUMBER}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property   ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create the Download Bin Table Instance   ${base_url}     ${DEVICE_NAME}     ${DEVICE_VERSION_NUMBER}

    #${rest_request}=        Build Rest Request       ${base_url}     ${BIN_NUMBER}     ${ACTION}  #Instantiate the CreateRequest class
    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${response} =       Send Rest Request    ${endpoint}   method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance



Create the Download Bin Table Instance
    [Arguments]    ${BASE_URL}     ${DEVICE_NAME}     ${DEVICE_VERSION_NUMBER}
    ${instance}=    Evaluate    DownloadBinTable.CreateRESTRequest('${BASE_URL}','${DEVICE_NAME}','${DEVICE_VERSION_NUMBER}')    modules=DownloadBinTable
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'downloadbintable' REST API did not return the expected status of '${EXPECTED_STATUS_CODE}', the returned status is '${status_code}'

The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}
    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
    IF    "${api_error_message_detail}" != "None"
         ${v2}=      Remove Quotes    ${api_error_message_detail}
    ELSE
         ${v2}=      Set Variable     ${api_error_message_detail}
    END


    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${v1}'     '${v2}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       devicename    deviceVersionNumber

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'

             #Remove quotes from the strings
             ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
             IF    '${api_error_message_fields}' != 'None'
                 ${v2}=      Remove Quotes    ${api_error_message_fields}
             ELSE
                 ${v2}=      Set Variable     ${api_error_message_fields}
             END


             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${v1}' in '${v2}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'downloadbintable' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.



Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}

Get the Bin Table Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_download_bin_table_details
    RETURN    ${result}

Get the Bin Response field data
     [Arguments]     ${BINS_INSTNANCE}   ${METHOD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    ${METHOD_NAME}
    RETURN    ${result}

Get Data Field
    [Arguments]     ${BINS_INSTNANCE}    ${FIELD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_field   ${FIELD_NAME}
    RETURN    ${result}

Get Response Status Code
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keywords to read error response fields


#The below keywords are for Database Verifications
The downloaded Bin(s) numbers returned by the Controller must exist on the Bin Table Master file
    [Arguments]    ${MASTER_FILE_BEING_TESTED}

    #Verify that master file name has been provided with the correct expected name
    Run Keyword If    '${MASTER_FILE_BEING_TESTED}' == '${EMPTY}' or '${MASTER_FILE_BEING_TESTED}' == 'None'
    ...    Fail  Please specify the path for the Bin Type master file!
    
    ${validation_data}=       Check if Bin Master file name is correct    ${MASTER_FILE_BEING_TESTED}
    ${validation_passed_array}=         Split String    ${validation_data}  separator=,
    ${boolean}=     Set Variable   ${validation_passed_array}[0]
    ${file_type}=   Set Variable   ${validation_passed_array}[1]
    Run Keyword If    not ${boolean}
    ...    Fail  please specify the correct name for the file path.

   #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Table Details
    Log Many    ${bin_object_instace}
    Log     '################################################'
    ${bins_response_data}=      Set Variable    ${EMPTY}

    FOR    ${index}    ${element}    IN ENUMERATE    @{bin_object_instace}
        Log    ${index}: ${element}
       # ${domestic_bins_data}=        Get the Bin Response field data     ${element}     get_bin_table_domestic
        #${on_us_bins_data}=           Get the Bin Response field data     ${element}     get_bin_table_on_us
        #${contactless_bins_data}=     Get the Bin Response field data     ${element}     get_bin_table_contactless
        #${invalid_bins_data}=         Get the Bin Response field data     ${element}     get_bin_table_invalid
        #${token_bins_data}=           Get the Bin Response field data     ${element}     get_bin_table_token
        #Fetch the bin types that must be validated from the response

         ${data_field}=     Set Variable    ${file_type}
         ${bins_response_data}=      Get Data Field    ${element}    ${data_field}

        ${server_version_data}=       Get the Bin Response field data     ${element}     get_server_version
        ${check_sum_data}=            Get the Bin Response field data     ${element}     get_check_sum
        Log Many    ${bins_response_data}
    END

    Run Keyword If    '${bins_response_data}' == 'None'
    ...    Fail     The Bin Download service did not return the data for Bin Type: '${file_type}'

    #Verify that the downloaded Bin Type data is in Base 64 format
    #Also verify the Bin Type master file against the downloaded Bins to make sure that all Bins that are on the master file are downloaded
    ${is_domestic_bins_data_valid_base64}=      Verify if the string is in valid base64 format      ${bins_response_data}
    Run Keyword If    ${is_domestic_bins_data_valid_base64} == ${False}
    ...    Fail     The returned data for ${file_type} bins is not a Base 64 format.

    #Verify that the Bins Type downloaded data contains data that is on the Domestic Bins seed file
    ${domestic_bins_decoded_data_location}=       Construct to be used when saving the downloaded BIN Type    ${file_type}

    Decode Base64 to UTF16 and save       ${bins_response_data}       ${domestic_bins_decoded_data_location}

    ${master_file_data_location}=       Get the BIN Type Master File path       ${file_type}
    Verify the master file against the downloaded bins to ensure that all the bins that are on the master file were downloaded    ${master_file_data_location}    ${domestic_bins_decoded_data_location}       ${file_type}

Check if Bin Master file name is correct
    [Arguments]    ${MASTER_FILE_NAME}

    ${MASTER_FILE_BEING_TESTED}=       Convert To Upper Case    ${MASTER_FILE_NAME}
    ${is_domestic_file}=               Run Keyword And Return Status    Should Contain         ${MASTER_FILE_BEING_TESTED}    DOMESTIC
    ${is_contactless_file}=            Run Keyword And Return Status    Should Contain         ${MASTER_FILE_BEING_TESTED}    CONTACTLESS
    ${is_on_us_file}=                  Run Keyword And Return Status    Should Contain Any     ${MASTER_FILE_BEING_TESTED}    ON-US   ONUS   ON_US
    ${is_token_file}=                  Run Keyword And Return Status    Should Contain         ${MASTER_FILE_BEING_TESTED}    TOKEN
    ${is_invalid_file}=                Run Keyword And Return Status    Should Contain         ${MASTER_FILE_BEING_TESTED}    INVALID

    ${file_is_valid}=   Set Variable    ${False}
    ${file_is_valid}=       Set Variable If    ${is_domestic_file} or ${is_contactless_file} or ${is_on_us_file} or ${is_token_file} or ${is_invalid_file}      ${True}
    ${file_to_be_validated}=        Set Variable If
                                    ...       ${is_domestic_file}       Domestic
                                    ...       ${is_contactless_file}    Contactless
                                    ...       ${is_on_us_file}          OnUs
                                    ...       ${is_token_file}          Token
                                    ...       ${is_invalid_file}        Invalid

    
    IF    ${file_is_valid}
        Log Many  Bin Type Master file path name: ${MASTER_FILE_NAME}  is valid.
        RETURN   ${True},${file_to_be_validated}
    ELSE
        Run Keyword And Continue On Failure    Fail      Bin Type Master file path name: ${MASTER_FILE_NAME}  is not valid.
        RETURN   ${False},None
    END

Get the BIN Type Master File path
    [Arguments]  ${BIN_TYPE}

    ${FILE_BEING_TESTED}=       Convert To Upper Case    ${BIN_TYPE}
    ${is_domestic_file}=               Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    DOMESTIC
    ${is_contactless_file}=            Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    CONTACTLESS
    ${is_on_us_file}=                  Run Keyword And Return Status    Should Contain Any     ${FILE_BEING_TESTED}    ON-US   ONUS   ON_US
    ${is_token_file}=                  Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    TOKEN
    ${is_invalid_file}=                Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    INVALID

    ${file_is_valid}=   Set Variable    ${False}
    ${file_is_valid}=       Set Variable If    ${is_domestic_file} or ${is_contactless_file} or ${is_on_us_file} or ${is_token_file} or ${is_invalid_file}      ${True}
    ${file_to_be_validated}=        Set Variable If
                                    ...       ${is_domestic_file}       future_fit_architecture_portal_BinTables/data/BinTableDomestic.xml
                                    ...       ${is_contactless_file}    future_fit_architecture_portal_BinTables/data/BinTableContactless.xml
                                    ...       ${is_on_us_file}          future_fit_architecture_portal_BinTables/data/BinTableOnUs.xml
                                    ...       ${is_token_file}          future_fit_architecture_portal_BinTables/data/BinTableToken.xml
                                    ...       ${is_invalid_file}        future_fit_architecture_portal_BinTables/data/BinTableInvalid.xml

     RETURN   ${file_to_be_validated}

Construct to be used when saving the downloaded BIN Type
    [Arguments]  ${BIN_TYPE}

    ${FILE_BEING_TESTED}=       Convert To Upper Case    ${BIN_TYPE}
    ${is_domestic_file}=               Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    DOMESTIC
    ${is_contactless_file}=            Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    CONTACTLESS
    ${is_on_us_file}=                  Run Keyword And Return Status    Should Contain Any     ${FILE_BEING_TESTED}    ON-US   ONUS   ON_US
    ${is_token_file}=                  Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    TOKEN
    ${is_invalid_file}=                Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    INVALID

    ${file_is_valid}=   Set Variable    ${False}
    ${file_is_valid}=       Set Variable If    ${is_domestic_file} or ${is_contactless_file} or ${is_on_us_file} or ${is_token_file} or ${is_invalid_file}      ${True}
    ${downloaded_file_path}=        Set Variable If
                                    ...       ${is_domestic_file}       future_fit_architecture_portal_BinTables/data/DownloadedBinTableDomestic.xml
                                    ...       ${is_contactless_file}    future_fit_architecture_portal_BinTables/data/DownloadedBinTableContactless.xml
                                    ...       ${is_on_us_file}          future_fit_architecture_portal_BinTables/data/DownloadedBinTableOnUs.xml
                                    ...       ${is_token_file}          future_fit_architecture_portal_BinTables/data/DownloadedBinTableToken.xml
                                    ...       ${is_invalid_file}        future_fit_architecture_portal_BinTables/data/DownloadedBinTableInvalid.xml

     RETURN   ${downloaded_file_path}

Get Bin Count
    [Arguments]    &{kwargs}
    ${count}=    Set Variable     0

    FOR    ${key}    ${value}    IN    &{kwargs}
        ${temp_key}=  Convert To String  ${key}
        ${Key_is_bin_number}=      Run Keyword And Return Status   Should Match Regexp    ${temp_key}    ^bin[0-9]+$
        ${Key_is_bin_id}=      Run Keyword And Return Status   Should Match Regexp    ${temp_key}    ^binId[0-9]+$
        IF    ${Key_is_bin_number} or ${Key_is_bin_id}
             ${count} =    Evaluate    ${count} + 1
        END
    END

    RETURN    ${count}

Verify the master file against the downloaded bins to ensure that all the bins that are on the master file were downloaded
    [Arguments]     ${MASTER_FILE_PATH}     ${DOWNLOADED_BINS_FILE_PATH}    ${BIN_TYPE_BEING_VALIDATED}

    ${bins_match_master_file_data}=    Verify if the contents of the first xml exist in the second xml file    ${MASTER_FILE_PATH}    ${DOWNLOADED_BINS_FILE_PATH}

    Log Many  ${bins_match_master_file_data}

    ${no_bins_failed}=      Run Keyword And Return Status    Should Be Equal    ${bins_match_master_file_data}    ${True}

    #If all bins on the master file exists on the downloaded file exit the keyword
    Run Keyword If    ${no_bins_failed}
    ...    Log Many     All bins that are on the master file were found on the downloaded file.

    Run Keyword If    ${no_bins_failed}
    ...    Return From Keyword

    Log Many  ${bins_match_master_file_data}[0]

    Log Many  ${bins_match_master_file_data}[1]

    #Loop through all the bins that we found in the master file
    #but are not existing on the Downloaded Bins.
    #Verify that the reason that these Bins were not found on the downloaded is because they were Soft Deleted.
    #If the Bins from master file are not found on the DB then it means they were not captured, this must be raised on the report
    FOR    ${element}    IN    @{bins_match_master_file_data}[1]
        Log Many  Bin Number: ${element}  was found on the master file, but it was not downloaded when the DownloadBinTable Controller was run.

        #Get the Bin Id for the current Bin
        ${bin_id_db_results}=          Get the Bin ID using the Bin Number from the Database    ${element}

        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bin_id_db_results}
        Run Keyword If    not ${dr_results_contain_data}
        ...     Run Keyword And Continue on Failure  Fail    Database query to get Bin Id for bin number: '${element}' returned no results!

        IF    ${dr_results_contain_data}
            ${first_row_results}=             Get From List    ${bin_id_db_results}    0    # Get the first row
            ${bin_id}=    Get Column Data By Name       ${first_row_results}       binId


            #Get the BinActionTracker Details for the current Bin Number
             ${db_results}=     Get the last action tracker details for the Bin    ${bin_id}
            # Ensure the results are not empty
            ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
            Run Keyword If    not ${dr_results_contain_data}
            ...     Fail    Database query to get the Bin Action Tracker Details bin id: '${bin_id}' returned no results!

            ${first_row_results}=             Get From List    ${db_results}    0    # Get the first row

            ${bin_outcome_data}=                       Get Column Data By Name              ${first_row_results}       ActionType
            ${bin_outcome_text}=                       Get Bin Outcome Text                 ${bin_outcome_data}
            ${bin_reject_comment_data}=                Get Column Data By Name              ${first_row_results}       RejectionComment
            ${bin_status_data}=                        Get Column Data By Name              ${first_row_results}       Status
            ${bin_status_text}=                        Get Bin Status Text                  ${bin_status_data}
            ${bin_to_be_actioned_by_data}=             Get Column Data By Name              ${first_row_results}       ToBeActionedBy
            ${bin_to_be_actioned_by_text}=             Get Bin To Be Actioned By Text       ${bin_to_be_actioned_by_data}


            ${bin_is_deleted}=    Get the Bin 'ISDeleted' status    ${element}

            ${bin_outcome_is_delete}=    Run Keyword And Return Status     Should Be Equal As Strings    '${bin_outcome_text}'    'Deleted'

            ${bin_outcome_is_approved}=    Run Keyword And Return Status     Should Be Equal As Strings    '${bin_status_text}'    'Approved'

            ${bin_to_be_actioned_by_is_Nobody}=    Run Keyword And Return Status     Should Be Equal As Strings    '${bin_to_be_actioned_by_text}'    'Nobody'

            ${bin_deletion_is_finalized}=       All Variables are True     ${bin_is_deleted}    ${bin_outcome_is_delete}     ${bin_outcome_is_approved}     ${bin_to_be_actioned_by_is_Nobody}

            #Verify that the Bin 'isDeleted' status is 'True'
            Run Keyword If    ${bin_deletion_is_finalized}
            ...    Log    Bin Number: '${element}' is soft-deleted on the database, hence it must be removed from the Master file.

            IF  not ${bin_deletion_is_finalized}        #If the bin is not soft-deleted, check if the 'Delete' is awaiting approval
                IF    ${bin_outcome_is_delete}   #IF the Bin has the 'Delete' outcome
                     #Check if the bin is to be actioned by the approver
                     IF   '${bin_to_be_actioned_by_text}' == 'Approver' and ${bin_is_deleted} == ${False}
                        Run Keyword And Continue On Failure     Run Keyword And Warn On Failure  Fail  The 'Delete' outcome for Bin Number: '${element}', with the bin type: '${BIN_TYPE_BEING_VALIDATED}', is awaiting approval hence it was not downloaded.
                     ELSE
                        IF    '${bin_to_be_actioned_by_text}' == 'Capturer' and ${bin_is_deleted} == ${False}
                           Run Keyword And Continue On Failure     Run Keyword And Warn On Failure  Fail  The 'Delete' outcome for Bin Number: '${element}', with the bin type: '${BIN_TYPE_BEING_VALIDATED}', was rejected by the Approver and that is why Bin was not downloaded. The Bin is to be actioned by the Capturer.
                        ELSE
                           IF   ${bin_outcome_is_approved} and '${bin_to_be_actioned_by_text}' == 'Nobody'
                                Run Keyword And Continue On Failure  Run Keyword And Warn On Failure  Fail  The Bin Number: '${element}', with the bin type: '${BIN_TYPE_BEING_VALIDATED}', must be removed from the master file because it has been soft-deleted.
                           END
                        END
                     END
                ELSE    #If the Bin outcome is 'Added'

                    #Get the bin details from the database to confirm whether the bin was captured using the correct bin type
                    ${db_results}=     Get the Bin details and active Bin Types from the Database    ${element}

                    ${bin_type_validation_passed}=      Set Variable    ${False}
                    ${associated_bin_types}=    Create List
                    ${previous_bin_type_name}=  Set Variable    ${EMPTY}

                    FOR    ${db_results_row}    IN    @{db_results}
                        Log Many    ${db_results_row}
                        ${BIN_TYPE_NAME}=       Get Column Data By Name       ${db_results_row}       BinTypeName
                         ${BIN_TYPE_NAME}=    Convert To Upper Case    ${BIN_TYPE_NAME}
                        #IF    '${BIN_TYPE_NAME}' == 'ON-US'
                        #     ${BIN_TYPE_NAME}=    Set Variable      ONUS
                        #END

                        IF    '${previous_bin_type_name}' == '${EMPTY}'
                            ${previous_bin_type_name}=  Set Variable    ${BIN_TYPE_NAME}
                            Append To List    ${associated_bin_types}     ${previous_bin_type_name}
                        ELSE
                            IF    '${previous_bin_type_name}' != '${BIN_TYPE_NAME}'
                                 ${previous_bin_type_name}=  Set Variable    ${BIN_TYPE_NAME}
                                 Append To List    ${associated_bin_types}     ${previous_bin_type_name}
                            ELSE
                                CONTINUE
                            END
                        END


                        ${BIN_TYPE_BEING_VALIDATED}=    Convert To Upper Case    ${BIN_TYPE_BEING_VALIDATED}

                        ${bin_type_validation_passed}=      Run Keyword And Return Status    Should Be Equal As Strings    ${BIN_TYPE_NAME}    ${BIN_TYPE_BEING_VALIDATED}
                        Run Keyword If    ${bin_type_validation_passed}
                        ...    Exit For Loop
                    END


                    #Check if the bin is to be actioned by the approver
                     IF   '${bin_to_be_actioned_by_text}' == 'Approver' and ${bin_is_deleted} == ${False}
                        Run Keyword And Continue On Failure     Run Keyword And Warn On Failure  Fail  The 'Added' outcome for Bin Number: '${element}', with the bin type: '${BIN_TYPE_BEING_VALIDATED}', is awaiting approval hence it was not downloaded.
                     ELSE
                        IF    '${bin_to_be_actioned_by_text}' == 'Capturer' and ${bin_is_deleted} == ${False}
                           Run Keyword And Continue On Failure     Run Keyword And Warn On Failure  Fail  The 'Added' outcome for Bin Number: '${element}' was rejected by the Approver and that is why Bin was not downloaded. The Bin is to be actioned by the Capturer.
                        ELSE
                           IF   ${bin_outcome_is_approved} and '${bin_to_be_actioned_by_text}' == 'Nobody'
                                IF    ${bin_type_validation_passed}
                                    Run Keyword And Continue On Failure  Fail  The Bin Number: '${element}', with the bin type: '${BIN_TYPE_BEING_VALIDATED}', was supposed to be downloaded when running the DownloadBinTables cotroller but it was not.
                                ELSE
                                    Run Keyword And Continue On Failure  Run Keyword And Warn On Failure  Fail  The Bin Number: '${element}' could not be downloaded because it is associated to the incorrect bin type(s) on the database. The database linked bin type(s) is(are): '${associated_bin_types}'. The bin was supposed to be linked to '${BIN_TYPE_BEING_VALIDATED}'.
                                END

                           END
                        END
                     END
                END
            END

        END
    END



Get the downloaded Bin Types file
    [Arguments]  ${BIN_TYPE}

    ${FILE_BEING_TESTED}=       Convert To Upper Case    ${BIN_TYPE}
    ${is_domestic_file}=               Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    DOMESTIC
    ${is_contactless_file}=            Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    CONTACTLESS
    ${is_on_us_file}=                  Run Keyword And Return Status    Should Contain Any     ${FILE_BEING_TESTED}    ON-US   ONUS   ON_US
    ${is_token_file}=                  Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    TOKEN
    ${is_invalid_file}=                Run Keyword And Return Status    Should Contain         ${FILE_BEING_TESTED}    INVALID

    ${file_is_valid}=   Set Variable    ${False}
    ${file_is_valid}=       Set Variable If    ${is_domestic_file} or ${is_contactless_file} or ${is_on_us_file} or ${is_token_file} or ${is_invalid_file}      ${True}
    ${downloaded_file_path}=        Set Variable If
                                    ...       ${is_domestic_file}       future_fit_architecture_portal_BinTables/data/DownloadedBinTableDomestic.xml
                                    ...       ${is_contactless_file}    future_fit_architecture_portal_BinTables/data/DownloadedBinTableContactless.xml
                                    ...       ${is_on_us_file}          future_fit_architecture_portal_BinTables/data/DownloadedBinTableOnUs.xml
                                    ...       ${is_token_file}          future_fit_architecture_portal_BinTables/data/DownloadedBinTableToken.xml
                                    ...       ${is_invalid_file}        future_fit_architecture_portal_BinTables/data/DownloadedBinTableInvalid.xml

     RETURN   ${downloaded_file_path}


The downloaded Bins must have the correct review status and associated to the correct bin type on the database
    [Arguments]     ${BIN_TYPE}

     #Verify that bin type file has been specified
    Run Keyword If    '${BIN_TYPE}' == '${EMPTY}' or '${BIN_TYPE}' == 'None'
    ...    Fail  Please specify the Bin Type name for the download file required!

    ${temp_bin_type_name}=      Convert to Upper Case   ${BIN_TYPE}

    IF    '${temp_bin_type_name}' == 'ON-US'
         ${BIN_TYPE}=       Set Variable    OnUs
    END
    ${xml_path}=        Get the downloaded Bin Types file   ${BIN_TYPE}

    ${xml_file_contents}=   Extract XML values and save them in an array        ${xml_path}

    Log Many     ${xml_file_contents}

    ${file_contents_are_valid}=    Evaluate    isinstance(${xml_file_contents}, list)

    Run Keyword If    not ${file_contents_are_valid}
    ...    Fail  The data read from file path '${xml_path}' is not a valid bin type download data.

    #Loop through all returned bins for verification purposes
    ${db_bin_type_name}=        Get Bin Type Name   ${BIN_TYPE}

    FOR    ${bin_number}    IN    @{xml_file_contents}
        ${db_results}=     Get the Bin details from the Database    ${BIN_NUMBER}

        # Ensure the results are not empty
        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

        Run Keyword If    not ${dr_results_contain_data}
        ...   Run Keyword And Continue On Failure   Fail    Database query for bin number: '${BIN_NUMBER}' returned no results

        IF    not ${dr_results_contain_data}
            Run Keyword And Continue On Failure   Fail    The '${BIN_TYPE}' bin number: '${BIN_NUMBER}' was downloaded but it does not exist in the database.
            Continue For Loop
        END
        ${bin_type_verification_passed}=       Set Variable     ${False}
        #Loop through the bin details and verify that the current bin type is active and approved
        FOR    ${db_row}    IN    @{db_results}
             ${bin_review_status}=               Get Column Data By Name       ${db_row}       reviewStatus
             ${bin_review_status_text}=          Get Bin Status Text           ${bin_review_status}
             ${bin_is_deleted}=                  Get Column Data By Name       ${db_row}       binNumberIsDeleted
             ${bin_is_deleted_boolean}=          Check If One Or Zero          ${bin_is_deleted}
             ${bin_type_name}=                   Get Column Data By Name       ${db_row}       binType
             ${bin_type_is_deleted}=             Get Column Data By Name       ${db_row}       binTypeIsDeleted
             ${bin_type_is_deleted_boolean}=     Check If One Or Zero          ${bin_type_is_deleted}
             ${bin_outcome}=             Get Column Data By Name       ${db_row}       outcome
             ${bin_outcome_text}=       Get Bin Outcome Text        ${bin_outcome}
             #IF the bin type is not deleted and it is approved
             IF    not ${bin_is_deleted_boolean} and not ${bin_type_is_deleted_boolean} and '${bin_review_status_text}' == 'Approved'
                  #Verify that the downloaded Bin Type is associated with the bin
                  ${bin_type_verification_passed}=     Run Keyword And Return Status    Should Be Equal As Strings    ${db_bin_type_name}    ${bin_type_name}

                  #If verification passed then exit the loop
                  Run Keyword If    ${bin_type_verification_passed}
                  ...    Exit For Loop
             END
        END

        Run Keyword If    ${bin_type_verification_passed}
        ...    Log      The Bin Number: '${bin_number}' is approved in the database, it is associated with the bin type: '${db_bin_type_name}'.
        ...  ELSE IF    '${bin_review_status_text}' == 'Pending' and '${bin_outcome_text}' == 'Added'
        ...    Run Keyword And Continue On Failure  Fail     The Bin Number: '${bin_number}' is not approved in the database but it was downloaded by the Bin Table service for bin type: '${db_bin_type_name}'. The current status of the Bin is '${bin_review_status_text}'.
        ...  ELSE IF    '${bin_review_status_text}' == 'Pending' and '${bin_outcome_text}' == 'Deleted'
        ...    Run Keyword And Warn On Failure  Fail     The Bin Number: '${bin_number}' is soft-deleted but not approved in the database hence it was downloaded by the Bin Table service for bin type: '${db_bin_type_name}'. The current status of the Bin is '${bin_review_status_text}'.
        ...  ELSE IF    ${bin_is_deleted_boolean} and '${bin_review_status_text}' == 'Approved'
        ...    Run Keyword And Continue On Failure  Fail     The Bin Number: '${bin_number}' is soft-deleted in the database but it was downloaded by the Bin Table service. The downloaded bin type is '${db_bin_type_name}'.
        ...  ELSE IF    ${bin_type_is_deleted_boolean} and '${bin_review_status_text}' == 'Approved'
        ...    Run Keyword And Continue On Failure  Fail     The associated bin type: '${db_bin_type_name}' is soft-deleted on the database, but is was downloaded by the Bin Table service for Bin Number: '${bin_number}'.
        ...  ELSE
        ...    Run Keyword And Continue On Failure  Fail     The associated bin type: '${db_bin_type_name}', for Bin Number: '${bin_number}', does not exist on the database on the database but is was downloaded by the Bin Table service.

    END


Get Bin Type Name
    [Arguments]     ${BIN_TYPE}

    Log     ${BIN_TYPE}

    ${BIN_TYPE}=        Convert To Upper Case    ${BIN_TYPE}
    ${is_domestic_file}=               Run Keyword And Return Status    Should Contain         ${BIN_TYPE}    DOMESTIC
    ${is_contactless_file}=            Run Keyword And Return Status    Should Contain         ${BIN_TYPE}    CONTACTLESS
    ${is_on_us_file}=                  Run Keyword And Return Status    Should Contain Any     ${BIN_TYPE}    ON-US   ONUS   ON_US
    ${is_token_file}=                  Run Keyword And Return Status    Should Contain         ${BIN_TYPE}    TOKEN
    ${is_invalid_file}=                Run Keyword And Return Status    Should Contain         ${BIN_TYPE}    INVALID

    ${file_is_valid}=   Set Variable    ${False}
    ${file_is_valid}=       Set Variable If    ${is_domestic_file} or ${is_contactless_file} or ${is_on_us_file} or ${is_token_file} or ${is_invalid_file}      ${True}
    ${db_bin_type}=        Set Variable If
                                    ...       ${is_domestic_file}       Domestic
                                    ...       ${is_contactless_file}    Contactless
                                    ...       ${is_on_us_file}          OnUs
                                    ...       ${is_token_file}          Token
                                    ...       ${is_invalid_file}        Invalid

     RETURN   ${db_bin_type}

