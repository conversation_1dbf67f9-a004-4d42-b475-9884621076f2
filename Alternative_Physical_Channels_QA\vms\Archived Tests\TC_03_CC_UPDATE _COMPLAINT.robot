*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    UPDATE STATUS
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Test status update functionality of ATM Complaints & Complement

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../keywords/qrcode_cc/UpdateATMComplaints.robot 
Resource                                            ../keywords/qrcode_cc/ViewATMComplaintsAndCompliments.robot
Resource                                            ../keywords/common/common_keywords.robot
Resource                                            ../keywords/common/Login.robot
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
#LOCAL VARIABLES
${SUCCESS_MESSAGE_TEXT}                             Complaint/ Compliment Updated
${REFERNCE_NO}

#For update function this field must be empty
${ASSIGNED_TO_VALUE}                              

*** Keyword ***
Update ATM complaints status
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${FUNCTION}  ${Tag}  ${REFERNCE_NO}  ${NEW_STATUS}  ${CURRENT_STATUS}  ${COMMENT}
    Set Test Documentation  ${DOCUMENTATION}
   
    Log to console  Function: ${FUNCTION} New Status: ${NEW_STATUS} Current Status: ${CURRENT_STATUS} Comment: ${COMMENT}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe
   
    And The user clicks navigate to QR Code Complaints and Compliments screen link
   
    And User searches for a compliant or task  ${REFERNCE_NO}
   
    Sleep  2s
    
    And User clicks on update link

    And User selects status from the dropdown  ${NEW_STATUS}

    And User enters a comment  ${COMMENT} 

    And User clicks on Submit button

    And User confirms action  ${SUCCESS_MESSAGE_TEXT}

    #Search for the updated complaint
    And User searches for a compliant or task  ${REFERNCE_NO}
    
    And User views complaint/compliment details  ${COMMENT}  ${ASSIGNED_TO_VALUE}  ${NEW_STATUS}  ${REFERNCE_NO}

    And User logs out

| *Test Case*                                                                                         |                                  *DOCUMENTATION*                                                          |  *TESTRAIL_TESTCASE_ID*  |    *FUNCTION*       |    *Tag*         |  *REFERNCE_NO*| *NEW_STATUS*        | *CURRENT_STATUS* |                  *COMMENT*                        |
| ATM Complaints Lifecycle - On Hold to In Progress         | Update ATM complaints status            |      ATM Complaints Lifecycle - On Hold to In Progress                                                    |    101597686              |     UPDATE STATUS   | Update status    |  ACR0000542   |   In Progress       |    On Hold       | ATM Complaints Lifecycle - On Hold to In Progress     
| ATM Complaints Lifecycle - On Hold to Resolved            | Update ATM complaints status            |      ATM Complaints Lifecycle - On Hold to Resolved                                                       |    101597687              |     UPDATE STATUS   | Update status    |  ACR0000541   |      Resolved       |     On Hold      | ATM Complaints Lifecycle - On Hold to Resolved    
| ATM Complaints Lifecycle - On Resolved to Closed          | Update ATM complaints status            |      ATM Complaints Lifecycle - On Resolved to Closed                                                     |    101597688              |     UPDATE STATUS   | Update status    |  ACR0000541   |        Closed       |     Resolved     | ATM Complaints Lifecycle - On Resolved to Closed    
| ATM Complaints Lifecycle - On In Progress to Resolved     | Update ATM complaints status            |      ATM Complaints Lifecycle - On In Progress to Resolved                                                |    101597690              |     UPDATE STATUS   | Update status    |  ACR0000349   |        Resolved     |     In Progress  | ATM Complaints Lifecycle - On In Progress to Resolved    
| ATM Complaints Lifecycle - On Assign to In Progress       | Update ATM complaints status            |      ATM Complaints Lifecycle - On Assign to In Progress                                                  |    101597691              |     UPDATE STATUS   | Update status    |  ACR0000125   |        In Progress  |     Assign       | ATM Complaints Lifecycle - On Assign to In Progress    
| ATM Complaints Lifecycle - In Progress to On Hold         | Update ATM complaints status            |      ATM Complaints Lifecycle - In Progress to On Hold                                                    |    101597692              |     UPDATE STATUS   | Update status    |  ACR0000127   |       On Hold       |   In Progress    | ATM Complaints Lifecycle - In Progress to On Hold

# #                                                         #Different refference Number
| ATM Complaints Lifecycle - On Assign to On Hold           | Update ATM complaints status            |      ATM Complaints Lifecycle - On Assign to On Hold                                                      |     101597693             |     UPDATE STATUS   | Update status    |  ACR0000121   |       On Hold       |     Assign       | ATM Complaints Lifecycle - On Assign to On Hold    

