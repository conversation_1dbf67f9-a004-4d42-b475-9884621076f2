<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240918 17:59:30.634">
   <suite name="RAC29a TC 366 Search Criteria Not Met on site maintenance" id="s1" source="C:\development\vms-docker\tests\SITE_MAINTENANCE\RAC29a_TC_366_Search_Criteria_Not_Met_on_site_maintenance.robot">
      <test name="VSearch Criteria Not Met on Site Maintenance" id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Search Criteria Not Met on Site Maintenance</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240918 17:59:31.610" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240918 17:59:41.055" status="PASS" starttime="20240918 17:59:31.610"/>
         </kw>
         <kw library="Selenium" name="And The user lands on the dashboard page">
            <doc>Search Criteria Not Met on Site Maintenance</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240918 17:59:41.056" level="INFO">And The user lands on the dashboard page</msg>
            <status endtime="20240918 17:59:41.211" status="PASS" starttime="20240918 17:59:41.056"/>
         </kw>
         <kw library="Selenium" name="And The user navigates to the Site Maintenance Page">
            <doc>Search Criteria Not Met on Site Maintenance</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240918 17:59:41.212" level="INFO">And The user navigates to the Site Maintenance Page</msg>
            <status endtime="20240918 17:59:52.407" status="PASS" starttime="20240918 17:59:41.212"/>
         </kw>
         <kw library="Selenium" name="When The user performs a search with invalid criteria">
            <doc>Search Criteria Not Met on Site Maintenance</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240918 17:59:52.408" level="INFO">When The user performs a search with invalid criteria</msg>
            <status endtime="20240918 17:59:57.618" status="PASS" starttime="20240918 17:59:52.408"/>
         </kw>
         <kw library="Selenium" name="Then The user should see no search results">
            <doc>Search Criteria Not Met on Site Maintenance</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240918 17:59:57.619" level="INFO">Then The user should see no search results</msg>
            <status endtime="20240918 17:59:57.676" status="PASS" starttime="20240918 17:59:57.619"/>
         </kw>
         <tags>
            <tag>VSearch Criteria Not Met on Site Maintenance</tag>
         </tags>
         <status endtime="20240918 17:59:57.677" critical="yes" status="PASS" starttime="20240918 17:59:31.608"/>
      </test>
      <status endtime="20240918 17:59:57.680" status="PASS" starttime="20240918 17:59:30.634"/>
   </suite>
   <statistics>
      <total>
         <stat pass="1" fail="0">Critical Tests</stat>
         <stat pass="1" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">VSearch Criteria Not Met on Site Maintenance</stat>
      </tag>
      <suite>
         <stat name="RAC29a TC 366 Search Criteria Not Met on site maintenance" pass="1" fail="0" id="s1">RAC29a TC 366 Search Criteria Not Met on site maintenance</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
