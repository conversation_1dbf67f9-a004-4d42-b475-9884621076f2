*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing Camapaign Approval

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Logout.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../keywords/common/Navigation.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Campaign_History.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/View_Campaign_Data.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Export_Campaign.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Edit_Campaign.robot
Resource                                            ../../../../keywords/atm_marketing/UploadMarkrtingScreen.robot

*** Keyword ***
Validating Export Campaign Functionality
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}   ${START_DATE}   ${END_DATE}      ${CAMPAIGN_LANGUAGE}  ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

     Then The user navigates to the Campaign History page

    And The user Edits a Campaign   ${START_DATE}   ${END_DATE}

    And The user captures marketing screen information      ${CAMPAIGN_LANGUAGE}  ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}

    And The user saves the campaign


| *Test Cases*                              |      *DOCUMENTATION*     |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   |     *START_DATE*       |        *END_DATE*       |     * CAMPAIGN_LANGUAGE*      |           *IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY*
| RAC29a_TC_748_Export_Marketing_Campaigns  |  Validating Export Campaign Functionality  |  Campaign Export   |    BUSINESS_CAPTURER       |     APC_UAT             |          26-11-2024      |         26-12-2024      |     English,Afrikaans      |        images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg,images\\MarketingA_af_2.jpg      |