*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             String
Variables                                          ../../utility/SQLVariables.py
#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***
Verify GET ATMMarketing Results controller fields on the database
    [Arguments]     ${json_response}

    ${new_string}=   Convert To String    ${json_response}
    ${dict_value}=      Convert String to Dictionary    ${new_string}

    #Get the marketing_results_id
    ${marketing_results_id}=    Get From Dictionary    ${dict_value}    id
    #Get the marketing_results_deviceId
    ${marketing_results_deviceId}=    Get From Dictionary    ${dict_value}    deviceId
    #Get the marketing_results_deviceType
    ${marketing_results_deviceType}=    Get From Dictionary    ${dict_value}    deviceType
    #Get the marketing_results_scheduleVersion
    ${marketing_results_scheduleVersion}=    Get From Dictionary    ${dict_value}    scheduleVersion
    #Get the marketing_results_uploadDate
    ${marketing_results_uploadDate}=    Get From Dictionary    ${dict_value}    uploadDate
    #Get the marketing_results_uploadResult
    ${marketing_results_uploadResult}=    Get From Dictionary    ${dict_value}    uploadResult
    #Get the marketing_results_resultDescription
    ${marketing_results_resultDescription}=    Get From Dictionary    ${dict_value}    resultDescription
    #Get the marketing_results_isUpaloadSuccessful
    ${marketing_results_isUpaloadSuccessful}=    Get From Dictionary    ${dict_value}    isUpaloadSuccessful

    #Verify the ATM MarketingResult details against the database
    ${data_base_marketing_results_details}=       Get the ATM Marketing Results details from the database       ${marketing_results_deviceId}      ${marketing_results_uploadDate}

    ${db_marketing_results_deviceId}=    Get From Dictionary    ${data_base_marketing_results_details}    deviceId
    Verify if values are equal     ${marketing_results_deviceId}        ${db_marketing_results_deviceId}

    ${db_marketing_results_deviceType}=    Get From Dictionary    ${data_base_marketing_results_details}    deviceType
    Verify if values are equal     ${marketing_results_deviceType}        ${db_marketing_results_deviceType}

    ${db_marketing_results_scheduleVersion}=    Get From Dictionary    ${data_base_marketing_results_details}    scheduleVersion
    Verify if values are equal     ${marketing_results_scheduleVersion}        ${db_marketing_results_scheduleVersion}

    ${db_marketing_results_uploadDate}=    Get From Dictionary    ${data_base_marketing_results_details}    uploadDate
    ${marketing_results_uploadDate}=      Replace String    ${marketing_results_uploadDate}    T    ${SPACE}

    ${length}=      Get Length    ${marketing_results_uploadDate}
    ${length_plus_one} =    Evaluate    ${length} + 1
    ${db_marketing_results_uploadDate}=    Get Substring    ${db_marketing_results_uploadDate}    0     ${length}

    Verify if values are equal     ${marketing_results_uploadDate}        ${db_marketing_results_uploadDate}

    ${db_marketing_results_uploadResult}=    Get From Dictionary    ${data_base_marketing_results_details}    uploadResult
    Verify if values are equal     ${marketing_results_uploadResult}        ${db_marketing_results_uploadResult}

    ${db_marketing_results_resultDescription}=    Get From Dictionary    ${data_base_marketing_results_details}    resultDescription
    Verify if values are equal     ${marketing_results_resultDescription}        ${db_marketing_results_resultDescription}

    ${db_marketing_results_isUpaloadSuccessful}=    Get From Dictionary    ${data_base_marketing_results_details}    isUploadSuccessful

    IF    '${db_marketing_results_isUpaloadSuccessful}' == '1'
        ${db_marketing_results_isUpaloadSuccessful}=    Set Variable    ${True}
    ELSE
        ${db_marketing_results_isUpaloadSuccessful}=    Set Variable    ${False}
    END
    Verify if values are equal     ${marketing_results_isUpaloadSuccessful}        ${db_marketing_results_isUpaloadSuccessful}

Get the ATM Marketing Results details from the database
    [Arguments]     ${DEVICE_ID}    ${UPLOAD_DATE}

    #Verify that all parameters are supplied
    Run Keyword If    '${DEVICE_ID}' == '${EMPTY}' or '${UPLOAD_DATE}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${marketing_results_query}   Set Variable     ${SQL_GET_ATM_MARKETING_RESULTS}
    ${marketing_results_query}=  Replace String      ${marketing_results_query}       atm_number     '${DEVICE_ID}'
    ${marketing_results_query}=  Replace String      ${marketing_results_query}       up_date     '${UPLOAD_DATE}'


    ${data_base_marketing_results_details}=      Execute SQL Query  ${db_type}  ${marketing_results_query}    True
    RETURN      ${data_base_marketing_results_details}





