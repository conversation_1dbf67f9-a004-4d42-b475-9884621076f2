*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1130




*** Keywords ***
Search for Bins on the View Entries page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                                            ${BASE_URL}
    When The User clicks Bins Menu
    Then The user must be able to click and focus on the 'Search' input

| *** Test Cases ***                                                                                                   |        *DOCUMENTATION*    		            |         *BASE_URL*                 |
| Admin_Verify User Can Access and Focus on the Search Field              | Search for Bins on the View Entries page   | Search for Bins on the View Entries page.  |           ${EMPTY}                 |
