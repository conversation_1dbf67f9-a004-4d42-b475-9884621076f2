*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../keywords/front_end/Bin_Table_Landing_Page.robot


*** Variables ***
${SUITE NAME}               BIN Table Landing Page 




*** Keywords ***
Verify User Details Display Correct Information and Role in the Top Right Corner
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The user retreives the information and role displayed on the top right corner on the front-end 
    And The user retrieves the role of the logged-in user from the backend (DevTools)
    Then The user verifies that the role displayed on the front end matches the assigned role retrieved from the backend system (devtools)


| *** Test Cases ***                                                                                                                                                                    |        *DOCUMENTATION*       |         *BASE_URL*                  |         
| Approver_Verify User Details Display Correct Information and Role in the Top Right Corner        | Verify User Details Display Correct Information and Role in the Top Right Corner   | User Role Details            |           ${EMPTY}                  |           
