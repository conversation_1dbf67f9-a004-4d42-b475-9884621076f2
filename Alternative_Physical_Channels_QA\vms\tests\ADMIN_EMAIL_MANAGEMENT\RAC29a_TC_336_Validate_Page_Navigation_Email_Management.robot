*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Validate Page Navigation (Prev/Next) functionality in Email Management

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/EmailManagement.robot
Resource                                            ../../keywords/common/DatabaseConnector.robot

*** Variables ***


*** Keywords ***
Validate Page Navigation Functionality
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - Email Management
    And Validate Email Management Page Navigation

*** Test Cases ***
| Validate Page Navigation (Prev/Next)- Email Management. | 
| | [Documentation] | Validate that Previous and Next page navigation buttons work correctly and display appropriate data. |
| | Validate Page Navigation Functionality | Validate that Previous and Next page navigation buttons work correctly and display appropriate data. | VMS_UAT |
