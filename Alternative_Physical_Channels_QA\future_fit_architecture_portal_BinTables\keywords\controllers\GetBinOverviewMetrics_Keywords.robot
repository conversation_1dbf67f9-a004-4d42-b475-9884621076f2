*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              Bin Tables GetBinOverviewMetrics Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            ../../keywords/controllers/resources/managementinformation/GetBinOverviewMetrics.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py
Library    SeleniumLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot
#***********************************PROJECT VARIABLES***************************************
** Variables ***
${SQL_GET_TOTAL_SERVER_VERSIONS}                    SELECT MAX(Number) AS LatestServerNumber FROM BinDbs.BinVersions

*** Keywords ***
The User sends a Get Request for GetBinOverviewMetrics using the Server Number
    [Arguments]     ${BASE_URL}     ${SERVER_NUMBER}  

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}

     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetBinOverviewMetrics Instance    ${base_url}     ${SERVER_NUMBER}       #Create an instance of   GetBinsToReview

    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   headers=${headers}     method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance





The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
    ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
    Run Keyword If    '${result}' == 'False'    Fail    The 'GetListOfServerVersions' REST API call failed, the returned status is '${status_code}'

    


Create GetBinOverViewMetrics Instance
    [Arguments]    ${BASE_URL}     ${SERVER_NUMBER}
    ${instance}=    Evaluate    GetBinOverviewMetrics.CreateRESTRequest('${BASE_URL}','${SERVER_NUMBER}')    modules=GetBinOverviewMetrics
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords
Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}

    # Count the number of BINS returned
    ${server_version_instance}=      Get Bin Overview Details
    ${server_versions_count}=    Get Length    ${server_version_instance}
    Log Many    ${server_versions_count}
    Set Global Variable    ${TOTAL_NUMBER_OF_SERVER_VERSIONS}        ${server_versions_count}

Get Response Status Code
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

Get Bin Overview Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bin_overview_metrics
    RETURN    ${result}

The expected Server Number details are retuned by the API Response
    Log    ${GLOBAL_SERVER_NUMBER}
