*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobe<PERSON><PERSON>.<EMAIL>

Default Tags                                        FFT_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${TESTRAIL_USERNAME}        <EMAIL>
${TESTRAIL_PASSWORD}        Tshwarelo@1
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
Approve marketing campaign
    [Arguments]        ${DOCUMENTATION}    ${TESTRAIL_TESTCASE_ID}    ${DATA_FILE}    ${BASE_URL}    ${SERVICE_PATH}    ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}    ${EXPECTED_MESSAGE}    &{KW_ARGS}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable                                   TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user prepares a json payload                      ${SUITE_NAME}       ${DATA_FILE}    &{KW_ARGS}
    When The user creates a rest session                        ${BASE_URL}
    And The user makes Post Rest Call                           ${SERVICE_PATH}     ${DATA_FILE}    ${EXPECTED_STATUS_CODE}
    And The service returns http status                         ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The rest service must return the expected message      ${EXPECTED_MESSAGE}

| *** Test Cases ***                                                                                               | *DOCUMENTATION*                                              |  *TESTRAIL_TESTCASE_ID*  |      *DATA_FILE*               | *BASE_URL*                   | *SERVICE_PATH*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON* | *EXPECTED_MESSAGE*  | *KW_ARGS*                                                                  |
#| FFT - Controllers - Approve Marketing Campaign with a Business Approver    | Approve marketing campaign    | Approves a Marketing Campaign with a Business Approver       |      155057487		   |   Approval                     | APC_API_UAT_BASE_URL         | Approval/Approve                          | 200                              | OK                     |                    | campaignId= 14408	 | approvalTime=0 | approvedBy= Yaash Ramsahaar (ZA) |
#| FFT - Contollera - Approve Marketing Campaign with a Business User         | Approve marketing campaign    | Approves a Marketing Campaign with a Business User           |      155057487		   |   Approval                     | APC_API_UAT_BASE_URL         | Approval/Approve                          | 401                              | Unauthorized           |                    | campaignId= 14409 | approvalTime=0 | approvedBy= Thabo Benjamin Setuke (ZA)  |