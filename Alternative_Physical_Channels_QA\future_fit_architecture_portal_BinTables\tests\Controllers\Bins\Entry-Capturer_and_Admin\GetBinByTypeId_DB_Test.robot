*** Settings ***
#Author Name               : Thabo
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinById_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for multiple Bins using a Bin Id on the GetBinById Controller
    [Arguments]        ${BASE_URL}    ${DOCUMENTATION}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User gets all the active and inactive Bins from the Database
    When The user sends a Get Request to search for all database retrieved bins using the Bin Id, the bin details must be retruned by the getbinbyid controller     ${BASE_URL}
#    And The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}
#    Then The Bin Number details must exist on the Bin Database                  ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}    ${BIN_TYPE_NAMES}


| *** Test Cases ***                                                                                                                                                                          |        *BASE_URL*   |               *DOCUMENTATION*                         |
| Search for all Bins existing in the database using GetBinById Controller and verify its details and Bin Type   | Search for multiple Bins using a Bin Id on the GetBinById Controller       |                     |       Search Bin by Number on the GetBinById API      |
