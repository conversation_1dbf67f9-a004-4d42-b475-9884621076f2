*** Settings ***
#Author Name               : Thabo
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite


Library              ../../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type
${TEST_CASE_ID}             RAC29a-TC-972




*** Keywords ***
Add a Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_NAME}' == '${EMPTY}' or '${BIN_NAME}' == ''
         ${random_word}=     Generate random bin name
         ${BIN_NAME}=   Set Variable     ${random_word}
         ${BIN_NAME}=    Get Substring    ${BIN_NAME}    0    12
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The User populates the Bin details and save the Bin to local storage    ${BIN_NAME}    ${BIN_TYPE_NAME}     ${BIN_ACTION_DATE}
    And The created bins are added to the local storage                         ${BIN_NAME}    ${BIN_TYPE_NAME}     ${BIN_ACTION_DATE}
    And The user saves the created bin(s) to the database
    And The created Bin must exist in the database
    Then The modified bin number must be displayed at the top of the Bins results on the View Entries Page      ${BIN_NAME}


| *** Test Cases ***                                                                               |        *DOCUMENTATION*       |         *BASE_URL*                  |         *BIN_NAME*          |         *BIN_TYPE_NAME*        |         *BIN_ACTION_DATE*        |
| Capturer_Verify BINTable Displays BIN Changes in Descending Date Order             | Add a Bin   | Add new Bin to Bin Tables.   |           ${EMPTY}                  |            ${EMPTY}         |         Invalid , Domestic     |         06-28-2027               |
