*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/SearchBinsByNumber_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using a Bin Number
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_NUMBER}  ${ACTION}   ${EXPECTED_STATUS_CODE}   ${BIN_ID}  ${ACTION_DATE}  ${BIN_TYPE_ID}  ${BIN_TYPE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to search for bins using a Bin Number    ${BASE_URL}      ${BIN_NUMBER}        ${ACTION}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    And The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${BIN_NUMBER}   ${ACTION_DATE}  ${BIN_TYPE_ID}  ${BIN_TYPE}
    Then The Bin Number details must exist on the Bin Database                  ${BIN_ID}   ${BIN_NUMBER}   ${ACTION_DATE}  ${BIN_TYPE_ID}  ${BIN_TYPE}


| *** Test Cases ***                                                                                                                                         |        *DOCUMENTATION*    		 |         *BASE_URL*                  |     *BIN_NUMBER*      | *ACTION*     |   *EXPECTED_STATUS_CODE*   |                *BIN_ID*                   |  *ACTION_DATE*   |   *BIN_TYPE_ID*                                                                                                                                             |       *BIN_TYPE*                             |
| Search for a Bin Numbered '333333' using the action type of 'Add', and verify its details and Bin Type.            | Search for a Bin using a Bin Number   | Search Bin by Number on the API   |                                     |       333333          |  add         |        200                 |   5171bf7c-1c96-493f-8f4d-e1747b15f385    |    2024-10-31    |   7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e,0e446d56-d033-498e-9c5d-ed81ad8f3208                                                                                 |        Contactless,Domestic                  |
| Search for a Bin Numbered '333333' using the action type of 'Edit', and verify its details and Bin Type.           | Search for a Bin using a Bin Number   | Search Bin by Number on the API   |                                     |       333333          |  edit        |        200                 |   5171bf7c-1c96-493f-8f4d-e1747b15f385    |    2024-10-31    |   7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e,0e446d56-d033-498e-9c5d-ed81ad8f3208                                                                                 |        Contactless,Domestic                  |
| Search for a Bin Numbered '333333' using the action type of 'Delete', and verify its details and Bin Type.         | Search for a Bin using a Bin Number   | Search Bin by Number on the API   |                                     |       333333          |  delete      |        200                 |   5171bf7c-1c96-493f-8f4d-e1747b15f385    |    2024-10-31    |   7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e,0e446d56-d033-498e-9c5d-ed81ad8f3208                                                                                 |        Contactless,Domestic                  |
#| Search for a Bin Numbered '333333' using the action type of 'Re-activate', and verify its details and Bin Type.    | Search for a Bin using a Bin Number   | Search Bin by Number on the API   |                                     |       999999          |  reactivate |         200                 |   5908d8b3-668f-4d80-9f74-d009978fbdbc    |    2024-10-28    |   a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e,0e446d56-d033-498e-9c5d-ed81ad8f3208,bb679411-b69d-42b1-a6c6-8e7cdc63d6c4       |        On-Us,Contactless,Domestic,Invalid    |
