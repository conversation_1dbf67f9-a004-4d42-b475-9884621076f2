*** Settings ***
# Author Name               : <PERSON>hab<PERSON>
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/GetAllBinTypes_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID
${TEST_CASE_ID}             RAC29a-TC-1046


*** Keywords ***
GET Request to get all Bin Types
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}   ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a GET Request to get all Bin Types   ${BASE_URL}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The User verifies that the Bin Types returned by the service exists on the database


| *** Test Cases ***                                                                                     |              *DOCUMENTATION*    	|    *BASE_URL*                  |   *EXPECTED_STATUS_CODE*
| Admin_API Test: Fetch All BIN Types Using GetAllBINtypes Endpoint   | GET Request to get all Bin Types | GET Request to get all Bin Types |                                 |       200
