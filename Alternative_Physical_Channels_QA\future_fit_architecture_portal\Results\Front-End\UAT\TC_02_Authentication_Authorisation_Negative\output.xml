<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 08:16:15.821" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_02_Authentication_Authorisation_Negative.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:16:16.509" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 08:16:16.509" endtime="******** 08:16:16.509"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:16:16.509" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 08:16:16.509" endtime="******** 08:16:16.509"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:16:16.509" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 08:16:16.509" endtime="******** 08:16:16.509"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.509" endtime="******** 08:16:16.509"/>
</kw>
<test id="s1-t1" name="Login- BU- Validate that the BU does not have access to approve/authorize campaigns" line="46">
<kw name="Validates Authentication and Authorisation Negative Tests">
<arg>Validating that a BU user cannot approve campaigns</arg>
<arg>155057383</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>Validating that a BU user cannot approve campaigns</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:16:16.525" level="INFO">Set test documentation to:
Validating that a BU user cannot approve campaigns</msg>
<status status="PASS" starttime="******** 08:16:16.525" endtime="******** 08:16:16.525"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:16:16.525" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057383'.</msg>
<status status="PASS" starttime="******** 08:16:16.525" endtime="******** 08:16:16.525"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:16:16.634" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:16:16.525" endtime="******** 08:16:16.634"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:16.634"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:16.634"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:16:16.634" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:16.634"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:16:16.634" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:16.634"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:16.634"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:16:16.781" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:16:17.412" level="INFO">${rc_code} = 128</msg>
<msg timestamp="******** 08:16:17.412" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 08:16:17.412" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:16:17.412" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000029F6C61EB40&gt;</msg>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:16:17.412" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:16:17.412" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:16:17.412" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:17.412"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:16:17.412" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:16:17.412" endtime="******** 08:16:22.060"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:16:22.060"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:16:22.067" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:16:22.067" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:16:22.060" endtime="******** 08:16:22.067"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:16:22.067" endtime="******** 08:16:22.074"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:16:22.076" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:16:22.076" endtime="******** 08:16:23.540"/>
</kw>
<status status="PASS" starttime="******** 08:16:22.074" endtime="******** 08:16:23.540"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:16:33.542" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:16:23.541" endtime="******** 08:16:33.542"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:16:33.542" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:16:33.564" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:16:33.542" endtime="******** 08:16:33.564"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:16:33.564" endtime="******** 08:16:33.564"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:16:53.565" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:16:33.564" endtime="******** 08:16:53.565"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:16:53.565" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:16:53.582" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:16:53.565" endtime="******** 08:16:53.582"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:16:53.582" endtime="******** 08:16:53.582"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:17:03.583" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:16:53.582" endtime="******** 08:17:03.583"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:17:03.583" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:17:03.603" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:17:03.583" endtime="******** 08:17:03.603"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:17:03.603" endtime="******** 08:17:03.603"/>
</kw>
<status status="PASS" starttime="******** 08:16:22.060" endtime="******** 08:17:03.603"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:17:03.724" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1.png"&gt;&lt;img src="selenium-screenshot-1.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:17:03.603" endtime="******** 08:17:03.724"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:17:03.724"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.634" endtime="******** 08:17:03.724"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.525" endtime="******** 08:17:03.724"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:17:08.724" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:17:03.724" endtime="******** 08:17:08.724"/>
</kw>
<kw name="And The user validates testcases" library="NegativeScenarios">
<arg>${Test_cases}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Validating BA user cannot capture a campaign"</arg>
<arg>Validating BA user cannot capture a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:17:08.725" endtime="******** 08:17:08.725"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Validating that a BU user cannot approve campaigns"</arg>
<arg>Validating that a BU user cannot approve campaigns</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validating that a BU user cannot approve campaigns" library="NegativeScenarios">
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${CAPTURE_CAMPAIGN_LINK}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="******** 08:17:08.755" level="INFO">Element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/div/mat-list-item/span/span[3]' is displayed.</msg>
<status status="PASS" starttime="******** 08:17:08.726" endtime="******** 08:17:08.755"/>
</kw>
<status status="PASS" starttime="******** 08:17:08.725" endtime="******** 08:17:08.755"/>
</kw>
<status status="PASS" starttime="******** 08:17:08.725" endtime="******** 08:17:08.755"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver not permitted to capture a campaign"</arg>
<arg>Validating BA user cannot capture a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:17:08.757" endtime="******** 08:17:08.757"/>
</kw>
<status status="PASS" starttime="******** 08:17:08.724" endtime="******** 08:17:08.757"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:17:08.757" endtime="******** 08:17:08.770"/>
</kw>
<status status="PASS" starttime="******** 08:17:08.757" endtime="******** 08:17:08.770"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:17:08.770" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:17:08.852" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:17:08.852" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:17:08.770" endtime="******** 08:17:08.852"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:17:08.852" endtime="******** 08:17:08.852"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:17:08.852" endtime="******** 08:17:08.852"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:17:08.852" endtime="******** 08:17:08.852"/>
</kw>
<status status="FAIL" starttime="******** 08:17:08.770" endtime="******** 08:17:08.852"/>
</kw>
<status status="PASS" starttime="******** 08:17:08.770" endtime="******** 08:17:08.852"/>
</kw>
<status status="PASS" starttime="******** 08:17:08.757" endtime="******** 08:17:08.852"/>
</kw>
<status status="PASS" starttime="******** 08:16:16.525" endtime="******** 08:17:08.852"/>
</kw>
<doc>Validating that a BU user cannot approve campaigns</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:16:16.509" endtime="******** 08:17:08.865"/>
</test>
<doc>Testing future fit Authentication &amp; Authorisation Negative testcases</doc>
<status status="PASS" starttime="******** 08:16:16.017" endtime="******** 08:17:12.333"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 08:16:17.412" level="WARN">There was error during termination of process</msg>
<msg timestamp="******** 08:16:19.434" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:16:33.542" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:16:53.565" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:17:03.583" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:17:12.282" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
