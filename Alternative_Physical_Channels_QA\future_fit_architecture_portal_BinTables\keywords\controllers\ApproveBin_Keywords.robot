*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/Approve.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py
Library                                             Process

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***
${BIN_TABLES_ADMIN_USER}                            ac30fc6b-5773-4edc-97f3-412e489e1c1f
${BIN_TABLES_CAPTURER_USER}                         decd98cd-20cf-4663-a6ad-60f6762bd241
${BIN_TABLES_APPROVER_USER}                         e2cec3df-91e1-4830-817b-0cbd22148e54


*** Keywords ***

The User gets all the Bins that are awaiting approval from the Database

    ${db_results}=     Get the Bins(bin id's) to be reviewed from the Database

    # Ensure the results are not empty
     # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    #Run Keyword And Return If    not ${dr_results_contain_data}    Log Many    Database SQL for bins awaiting review did not return results!

    IF  not ${dr_results_contain_data}
         Log Many    Database SQL for bins awaiting review did not return results!      WARN
         Set Global Variable    ${DATABASE_RESULTS}     ${EMPTY}
         Exit Suite
         RETURN
    END
   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Save the database results in a global variable
    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Log Many    ${DATABASE_RESULTS}


The User gets a Bin ID and Bin Number for a BIN that is awaiting approval from the Database

    ${db_results}=     Get a Bin(bin id) to be awaiting approval from the Database

     # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    #Run Keyword And Return If    not ${dr_results_contain_data}    Log Many    Database SQL for bins awaiting review did not return results!

    IF  not ${dr_results_contain_data}
         Run Keyword And Warn On Failure    Fail    Database SQL for bins awaiting review did not return results!
         Set Global Variable    ${DATABASE_RESULTS}     ${EMPTY}
         Exit Suite
         RETURN
    END
   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Save the database results in a global variable
    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Log Many    ${DATABASE_RESULTS}



The user gets the last action type(outcome) performed on the Bins to determine which bins can be approved
    [Arguments]     @{USER_BINS_TO_APPROVE}
    
    #Check if the parameter has value
   
    #Loop though all the bins fetched from the database,
    #verify if the bins can be approved and add them to the Global Variable


    Run Keyword If    ${USER_BINS_TO_APPROVE}    Log    The list is not empty
    Run Keyword If    ${USER_BINS_TO_APPROVE} == []    Log    The list is empty

    Log Many     ${USER_BINS_TO_APPROVE}

    ${num_rows_provided_by_user}=    Get Length    ${USER_BINS_TO_APPROVE}
    ${DATABASE_RESULTS}=       Set Variable If
         ...       ${USER_BINS_TO_APPROVE} == []    ${DATABASE_RESULTS}
         ...       ${USER_BINS_TO_APPROVE}       ${USER_BINS_TO_APPROVE}

    Log Many    ${DATABASE_RESULTS}

    ${num_rows}=    Get Length    ${DATABASE_RESULTS}

     # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${DATABASE_RESULTS}
    #Run Keyword And Return If    not ${dr_results_contain_data}    Log Many    Database SQL for bins awaiting review did not return results!

    IF  not ${dr_results_contain_data}
         Run Keyword And Warn On Failure    Fail    Failed - Database SQL for bins awaiting review did not return results!
         Set Global Variable    ${DATABASE_RESULTS}     ${EMPTY}
         Exit Suite
         RETURN
    END

    Log     '${num_rows}' database BINS will be approved using the API
    ${bins_to_be_approved_dict}=        Create Dictionary
    ${bins_to_be_approved_list}=        Create List
    ${bin_counter}=     Set Variable    1
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${TEMP_LIST}=        Create List
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       binNumber

        #The below bin number is not run because it has a different Status on the Action Tracker table when compared to the controller
        IF    '${binNumber_data}' == '333333'
            CONTINUE
        END

        ${BIN_ID}=  Get Column Data By Name       ${row}       binId

        ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${BIN_ID.strip()}
        FOR    ${row}    IN    @{action_tracker_db_results}
            ${REVIEW_STATUS}=       Get Column Data By Name       ${row}       Status
            ${OUTCOME}=             Get Column Data By Name       ${row}       ActionType
            ${OUTCOME}=             Get Bin Outcome Text          ${OUTCOME}
            ${REJECTED_COMMENT}=    Get Column Data By Name       ${row}       RejectionComment
            ${REVIEWED_BY}=         Get Column Data By Name       ${row}       ReviewedBy
            ${REVIEWED_DATE}=       Get Column Data By Name       ${row}       ReviewedDate
            ${TO_BE_ACTIONED_BY}=   Get Column Data By Name       ${row}       ToBeActionedBy
            ${key_exists_in_dictionary}=        Run Keyword And Return Status       Dictionary Should Contain Key    ${bin_tables_roles}    ${TO_BE_ACTIONED_BY}
            IF    ${key_exists_in_dictionary}
                ${action_by_text}=      Get From Dictionary    ${bin_tables_roles}    ${TO_BE_ACTIONED_BY}
            ELSE
                ${action_by_text}=      Get Bin To Be Actioned By Text    ${TO_BE_ACTIONED_BY}
            END

            ${bin_status_text}=   Get Bin Status Text     ${REVIEW_STATUS}

            Run Keyword If    '${bin_status_text}' != 'Pending' or '${action_by_text}' != 'Approver'
            ...    Run Keyword And Warn On Failure    Fail   The bin number:'${binNumber_data}' cannot be approved because the bin status is '${bin_status_text}' and the bin is assigned to '${action_by_text}'
            ...  ELSE
            ...    Log Many      The bin number:'${binNumber_data}' is eligible for approval, bin status is '${bin_status_text}' and the bin is assigned to '${action_by_text}'

            #Save current bin details to a dictionary
            IF    '${bin_status_text}' == 'Pending' and '${action_by_text}' == 'Approver'
                ${binId_key}=    Set Variable    binId${bin_counter}  # Create dynamic key like "binId1", "binId2", etc.
                ${outcome_key}=    Set Variable    outcome${bin_counter}  # Create dynamic key like "binId1", "binId2", etc.
                Log    Current Key: ${binId_key}
                Set To Dictionary    ${bins_to_be_approved_dict}    ${binId_key}=${BIN_ID}      ${outcome_key}=${OUTCOME}
                Append To List    ${bins_to_be_approved_list}       ${BIN_ID}
                ${TEMP_DICT}=     Create Dictionary     binId=${BIN_ID}      binNumber=${binNumber_data}
                Append To List    ${TEMP_LIST}       ${TEMP_DICT}
                ${bin_counter}=    Evaluate    ${bin_counter} + 1
                Log    Current counter value: ${bin_counter}
            END


        END
   END

   Log Many     ${bins_to_be_approved_dict}

   #Save the Bins to be approved dictionary in a Global Variable
    Set Global Variable    ${DATABASE_RESULTS}        ${TEMP_LIST}
    Set Global Variable    ${BINS_TO_APPROVE}        ${bins_to_be_approved_dict}
    Set Global Variable    ${APPROVED_BINS_LIST}        ${bins_to_be_approved_list}

    Run Keyword And Return If    "${BINS_TO_APPROVE}" == "{}"    Fail    There are no bins to approve!


The user gets the last action type(outcome) performed on the Bins to determine which deleted bins can be approved
    [Arguments]     @{USER_BINS_TO_APPROVE}

    #Check if the parameter has value

    #Loop though all the bins fetched from the database,
    #verify if the bins can be approved and add them to the Global Variable


    Run Keyword If    ${USER_BINS_TO_APPROVE}    Log    The list is not empty
    Run Keyword If    ${USER_BINS_TO_APPROVE} == []    Log    The list is empty

    Log Many     ${USER_BINS_TO_APPROVE}

    ${num_rows_provided_by_user}=    Get Length    ${USER_BINS_TO_APPROVE}
    ${DATABASE_RESULTS}=       Set Variable If
         ...       ${USER_BINS_TO_APPROVE} == []    ${DATABASE_RESULTS}
         ...       ${USER_BINS_TO_APPROVE}       ${USER_BINS_TO_APPROVE}

    Log Many    ${DATABASE_RESULTS}

    ${num_rows}=    Get Length    ${DATABASE_RESULTS}

     # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${DATABASE_RESULTS}
    #Run Keyword And Return If    not ${dr_results_contain_data}    Log Many    Database SQL for bins awaiting review did not return results!

    IF  not ${dr_results_contain_data}
         Run Keyword And Warn On Failure    Fail    Failed - Database SQL for bins awaiting review did not return results!
         Set Global Variable    ${DATABASE_RESULTS}     ${EMPTY}
         Exit Suite
         RETURN
    END

    Log     '${num_rows}' database BINS will be approved using the API
    ${bins_to_be_approved_dict}=        Create Dictionary
    ${bins_to_be_approved_list}=        Create List
    ${bin_counter}=     Set Variable    1
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${TEMP_LIST}=        Create List
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       binNumber

        #The below bin number is not run because it has a different Status on the Action Tracker table when compared to the controller
        IF    '${binNumber_data}' == '333333'
            CONTINUE
        END

        ${BIN_ID}=  Get Column Data By Name       ${row}       binId

        ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${BIN_ID.strip()}
        FOR    ${row}    IN    @{action_tracker_db_results}
            ${REVIEW_STATUS}=       Get Column Data By Name       ${row}       Status
            ${OUTCOME}=             Get Column Data By Name       ${row}       ActionType
            ${OUTCOME}=             Get Bin Outcome Text          ${OUTCOME}

            IF    '${OUTCOME}' == 'Deleted'

                ${REJECTED_COMMENT}=    Get Column Data By Name       ${row}       RejectionComment
                ${REVIEWED_BY}=         Get Column Data By Name       ${row}       ReviewedBy
                ${REVIEWED_DATE}=       Get Column Data By Name       ${row}       ReviewedDate
                ${TO_BE_ACTIONED_BY}=   Get Column Data By Name       ${row}       ToBeActionedBy
                ${key_exists_in_dictionary}=        Run Keyword And Return Status       Dictionary Should Contain Key    ${bin_tables_roles}    ${TO_BE_ACTIONED_BY}
                IF    ${key_exists_in_dictionary}
                    ${action_by_text}=      Get From Dictionary    ${bin_tables_roles}    ${TO_BE_ACTIONED_BY}
                ELSE
                    ${action_by_text}=      Get Bin To Be Actioned By Text    ${TO_BE_ACTIONED_BY}
                END

                ${bin_status_text}=   Get Bin Status Text     ${REVIEW_STATUS}

                Run Keyword If    '${bin_status_text}' != 'Pending' or '${action_by_text}' != 'Approver'
                ...    Run Keyword And Warn On Failure    Fail   The bin number:'${binNumber_data}' cannot be approved because the bin status is '${bin_status_text}' and the bin is assigned to '${action_by_text}'
                ...  ELSE
                ...    Log Many      The bin number:'${binNumber_data}' is eligible for approval, bin status is '${bin_status_text}' and the bin is assigned to '${action_by_text}'

                #Save current bin details to a dictionary
                IF    '${bin_status_text}' == 'Pending' and '${action_by_text}' == 'Approver'
                    ${binId_key}=    Set Variable    binId${bin_counter}  # Create dynamic key like "binId1", "binId2", etc.
                    ${outcome_key}=    Set Variable    outcome${bin_counter}  # Create dynamic key like "binId1", "binId2", etc.
                    Log    Current Key: ${binId_key}
                    Set To Dictionary    ${bins_to_be_approved_dict}    ${binId_key}=${BIN_ID}      ${outcome_key}=${OUTCOME}
                    Append To List    ${bins_to_be_approved_list}       ${BIN_ID}
                    ${TEMP_DICT}=     Create Dictionary     binId=${BIN_ID}      binNumber=${binNumber_data}
                    Append To List    ${TEMP_LIST}       ${TEMP_DICT}
                    ${bin_counter}=    Evaluate    ${bin_counter} + 1
                    Log    Current counter value: ${bin_counter}
                END

            END


        END
   END

   Log Many     ${bins_to_be_approved_dict}

   #Save the Bins to be approved dictionary in a Global Variable
    Set Global Variable    ${DATABASE_RESULTS}        ${TEMP_LIST}
    Set Global Variable    ${BINS_TO_APPROVE}        ${bins_to_be_approved_dict}
    Set Global Variable    ${APPROVED_BINS_LIST}        ${bins_to_be_approved_list}

    Run Keyword And Return If    "${BINS_TO_APPROVE}" == "{}"    Fail    There are no Deleted bins to approve!



The user gets the last action type(outcome) performed on the Bins to determine which added bins can be approved
    [Arguments]     @{USER_BINS_TO_APPROVE}

    #Check if the parameter has value

    #Loop though all the bins fetched from the database,
    #verify if the bins can be approved and add them to the Global Variable


    Run Keyword If    ${USER_BINS_TO_APPROVE}    Log    The list is not empty
    Run Keyword If    ${USER_BINS_TO_APPROVE} == []    Log    The list is empty

    Log Many     ${USER_BINS_TO_APPROVE}

    ${num_rows_provided_by_user}=    Get Length    ${USER_BINS_TO_APPROVE}
    ${DATABASE_RESULTS}=       Set Variable If
         ...       ${USER_BINS_TO_APPROVE} == []    ${DATABASE_RESULTS}
         ...       ${USER_BINS_TO_APPROVE}       ${USER_BINS_TO_APPROVE}

    Log Many    ${DATABASE_RESULTS}

    ${num_rows}=    Get Length    ${DATABASE_RESULTS}

     # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${DATABASE_RESULTS}
    #Run Keyword And Return If    not ${dr_results_contain_data}    Log Many    Database SQL for bins awaiting review did not return results!

    IF  not ${dr_results_contain_data}
         Run Keyword And Warn On Failure    Fail    Failed - Database SQL for bins awaiting review did not return results!
         Set Global Variable    ${DATABASE_RESULTS}     ${EMPTY}
         Exit Suite
         RETURN
    END

    Log     '${num_rows}' database BINS will be approved using the API
    ${bins_to_be_approved_dict}=        Create Dictionary
    ${bins_to_be_approved_list}=        Create List
    ${bin_counter}=     Set Variable    1
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${TEMP_LIST}=        Create List
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       binNumber

        #The below bin number is not run because it has a different Status on the Action Tracker table when compared to the controller
        IF    '${binNumber_data}' == '333333'
            CONTINUE
        END

        ${BIN_ID}=  Get Column Data By Name       ${row}       binId

        ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${BIN_ID.strip()}
        FOR    ${row}    IN    @{action_tracker_db_results}
            ${REVIEW_STATUS}=       Get Column Data By Name       ${row}       Status
            ${OUTCOME}=             Get Column Data By Name       ${row}       ActionType
            ${OUTCOME}=             Get Bin Outcome Text          ${OUTCOME}

            IF    '${OUTCOME}' == 'Added'

                ${REJECTED_COMMENT}=    Get Column Data By Name       ${row}       RejectionComment
                ${REVIEWED_BY}=         Get Column Data By Name       ${row}       ReviewedBy
                ${REVIEWED_DATE}=       Get Column Data By Name       ${row}       ReviewedDate
                ${TO_BE_ACTIONED_BY}=   Get Column Data By Name       ${row}       ToBeActionedBy
                ${key_exists_in_dictionary}=        Run Keyword And Return Status       Dictionary Should Contain Key    ${bin_tables_roles}    ${TO_BE_ACTIONED_BY}
                IF    ${key_exists_in_dictionary}
                    ${action_by_text}=      Get From Dictionary    ${bin_tables_roles}    ${TO_BE_ACTIONED_BY}
                ELSE
                    ${action_by_text}=      Get Bin To Be Actioned By Text    ${TO_BE_ACTIONED_BY}
                END

                ${bin_status_text}=   Get Bin Status Text     ${REVIEW_STATUS}

                Run Keyword If    '${bin_status_text}' != 'Pending' or '${action_by_text}' != 'Approver'
                ...    Run Keyword And Warn On Failure    Fail   The bin number:'${binNumber_data}' cannot be approved because the bin status is '${bin_status_text}' and the bin is assigned to '${action_by_text}'
                ...  ELSE
                ...    Log Many      The bin number:'${binNumber_data}' is eligible for approval, bin status is '${bin_status_text}' and the bin is assigned to '${action_by_text}'

                #Save current bin details to a dictionary
                IF    '${bin_status_text}' == 'Pending' and '${action_by_text}' == 'Approver'
                    ${binId_key}=    Set Variable    binId${bin_counter}  # Create dynamic key like "binId1", "binId2", etc.
                    ${outcome_key}=    Set Variable    outcome${bin_counter}  # Create dynamic key like "binId1", "binId2", etc.
                    Log    Current Key: ${binId_key}
                    Set To Dictionary    ${bins_to_be_approved_dict}    ${binId_key}=${BIN_ID}      ${outcome_key}=${OUTCOME}
                    Append To List    ${bins_to_be_approved_list}       ${BIN_ID}
                    ${TEMP_DICT}=     Create Dictionary     binId=${BIN_ID}      binNumber=${binNumber_data}
                    Append To List    ${TEMP_LIST}       ${TEMP_DICT}
                    ${bin_counter}=    Evaluate    ${bin_counter} + 1
                    Log    Current counter value: ${bin_counter}
                END

            END


        END
   END

   Log Many     ${bins_to_be_approved_dict}

   #Save the Bins to be approved dictionary in a Global Variable
    Set Global Variable    ${DATABASE_RESULTS}        ${TEMP_LIST}
    Set Global Variable    ${BINS_TO_APPROVE}        ${bins_to_be_approved_dict}
    Set Global Variable    ${APPROVED_BINS_LIST}        ${bins_to_be_approved_list}

    Run Keyword And Return If    "${BINS_TO_APPROVE}" == "{}"    Fail    There are no Deleted bins to approve!



The User Populates the Approve Bin JSON payload with data
    [Arguments]     &{USER_BINS_TO_APPROVE}



    IF    "&{USER_BINS_TO_APPROVE}" != "{}"
        &{BINS_TO_APPROVE}=        Set Variable       ${USER_BINS_TO_APPROVE}
    ELSE
       &{BINS_TO_APPROVE}=        Set Variable       ${BINS_TO_APPROVE}
    END

    Log Many    ${BINS_TO_APPROVE}

    ${json_payload}=    Create Bin Requests    &{BINS_TO_APPROVE}

    ${json_payload_is_created}=    Run Keyword And Return Status     Should Not Be Empty    ${json_payload}

    Run Keyword If    ${json_payload_is_created} == ${False}
    ...    Fail     The JSON payload for approve bin(s) was not created!
    Log Many    ${json_payload}


The User sends an API request to Approve the Bin(s)
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}


    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/ApproveBin.json

    ${endpoint}=        Get Endpoint    ${base_url}
    ${method}=          Set Variable   PUT
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     payload=${payload}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance

The User sends an API request to Approve the Bin(s) using the incorrect token
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}


    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/ApproveBin.json

    ${endpoint}=        Get Endpoint    ${base_url}
    ${method}=          Set Variable   PUT
    ${BEARER_TOKEN}=    Set Variable    Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IjNQYUs0RWZ5Qk5RdTNDdGpZc2EzWW1oUTVFMCIsImtpZCI6IjNQYUs0RWZ5Qk5RdTNDdGpZc2EzWW1oUTVFMCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AsJyThyLj--vZAAeZ435fx6-zBBPJ3gGlTDLS9n5D5NzO9DFa0xq5zl-6aHGnoaeAQ_-hNEqrJDJM_XkKtEhKJLaZ95bta2-jyAxXL1-rmxF635TD3JaQIYgeXVEJ1KeyLtD0bEb9K1MgthImiMF98ik0Sd7DQxDvvX_2yMoNg_-cJlTQ3yL3EobkHjYVK7dC424_OrpvVXmCNp-Io2uVBMzYpWcd7nWX4Jtp5z3KTw2GNjCZygzBUSZ8qqfXPc1r_0eI3WxA7LS0sKLrHwmK_VmYlWtLC4eYH82kGq5Tm9mr8yQ8lGQZUDBihOIpmf7G6kEAasya-hSNSlBVrBDtA
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     payload=${payload}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance


The bin(s) database status must be updated to approved
    [Arguments]     @{USER_BINS_TO_APPROVE}

    #Loop though all the approved bins ,
    #verify that their approved status has been updated correctly in the database

    Run Keyword If    ${USER_BINS_TO_APPROVE}    Log    The list is not empty
    Run Keyword If    ${USER_BINS_TO_APPROVE} == []    Log    The list is empty

    Log Many     ${USER_BINS_TO_APPROVE}

    ${num_rows_provided_by_user}=    Get Length    ${USER_BINS_TO_APPROVE}
    ${APPROVED_BINS_LIST}=       Set Variable If
         ...       ${USER_BINS_TO_APPROVE} == []    ${APPROVED_BINS_LIST}
         ...       ${USER_BINS_TO_APPROVE}       ${USER_BINS_TO_APPROVE}

    Log Many    ${APPROVED_BINS_LIST}

    ${num_rows}=    Get Length    ${APPROVED_BINS_LIST}

    Log     '${num_rows}' database BINS will be verified as to whether they are approved.


    FOR    ${index}    ${element}    IN ENUMERATE    @{APPROVED_BINS_LIST}
        Log    ${index}: ${element}

        ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${element.strip()}
        FOR    ${row}    IN    @{action_tracker_db_results}
            ${REVIEW_STATUS}=       Get Column Data By Name       ${row}       Status
            ${OUTCOME}=             Get Column Data By Name       ${row}       ActionType
            ${OUTCOME}=             Get Bin Outcome Text          ${OUTCOME}
            ${REJECTED_COMMENT}=    Get Column Data By Name       ${row}       RejectionComment
            ${REVIEWED_BY}=         Get Column Data By Name       ${row}       ReviewedBy
            ${REVIEWED_DATE}=       Get Column Data By Name       ${row}       ReviewedDate
            ${bin_status_text}=   Get Bin Status Text     ${REVIEW_STATUS}
            ${TO_BE_ACTIONED_BY}=       Get Column Data By Name       ${row}       ToBeActionedBy

            #Verify that the Bin has been approved
            Verify that values are equal     'Approved'   '${bin_status_text.strip()}'

            #Verify that the Bin is not assigned to anybody
            Verify that values are equal     '00000000-0000-0000-0000-000000000000'   '${TO_BE_ACTIONED_BY.strip()}'


        END
   END



Create Bin Requests
    [Arguments]    &{bins}

    #This keyword generates a JSON payload for bin requests by passing a dictionary of bins.
    #Each bin is represented by a set of key-value pairs, with keys like binId1 and outcome1.

    #Arguments:
     #&{BINS_DETAILS}: A dictionary containing bin data. The dictionary should contain the following format for each bin:

        #- binIdX: The bin ID (e.g., binId1=<uuid>).
        #- outcomeX: The outcome associated with the bin (e.g., outcome1=Added).

     #Here X represents an index (e.g., 1, 2, 3, etc.), and the method is flexible enough to accept any number of bins, even those with non-sequential or multi-digit indices (e.g., binId123, binId10).

     #Return Value:
        #Returns the generated JSON payload as a string, formatted with indentation for readability.

     #Usage Example
        #${bins} =    Create Dictionary    binId1=f47ac10b-58cc-4372-a567-0e02b2c3d479    outcome1=Added     binId2=b99f5d75-52c5-4a92-8759-c6d91f89f0cc    outcome2=deleted
        #${json_result}=    Create Bin Requests    &{bins}

    # Log the received bins to ensure it's a valid dictionary
    Log    Received bins: &{bins}

    #${bin_request_generator} =    Upload    @{bins}
    ${json_result} =    Create Bin Request    &{bins}

    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal_BinTables/data/ApproveBin.json
    Write JSON data To File    ${JSON_FILE}    ${json_result}

    RETURN    ${json_result}

The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'Approve' Bin REST API call failed, the returned status is '${status_code}'



The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_detail}'     '${EXPECTED_ERROR_MESSAGE}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       binId     outcome

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  "${EXPECTED_ERROR_MESSAGE}" in "${api_error_message_fields}"     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Approve' Bin(s) REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.
    ...  ELSE
    ...    Log Many  The expected message which is '${EXPECTED_ERROR_MESSAGE}' was found on the 'Approve' Bin API response.

Verify that the API retruned an empty response

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    #Verify that the response is empty
    ${verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '[]'     '${bin_object_instace}'

    Run Keyword If    not ${verification_passed}
    ...    Fail     The API did not return an empty response!





# Respose Keywords

Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}



Get Response Status Code
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

#Keywords to read error response fields
Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keyword to read successful response
Get the Bin Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bin_by_id_details
    RETURN    ${result}

Get the Bin Response field data
     [Arguments]     ${BINS_INSTNANCE}   ${METHOD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    ${METHOD_NAME}
    RETURN    ${result}



