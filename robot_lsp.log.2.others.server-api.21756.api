server-api: 2025-06-12 10:10:58 UTC pid: 21756 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.2.others.api']

server-api: 2025-06-12 10:10:58 UTC pid: 21756 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-12 10:10:58 UTC pid: 21756 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-12 10:10:58 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 21756

server-api: 2025-06-12 10:11:01 UTC pid: 21756 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-12 10:11:01 UTC pid: 21756 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-12 10:11:01 UTC pid: 21756 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-12 10:11:22 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 13, method: document_highlight

server-api: 2025-06-12 10:11:25 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 20, method: document_highlight

server-api: 2025-06-12 10:15:45 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 54

server-api: 2025-06-12 10:17:32 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 63

server-api: 2025-06-12 10:17:32 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 65

server-api: 2025-06-12 10:17:32 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 68, method: document_highlight

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 70

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 67

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 72

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 74

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 75

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 77

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 76

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 80

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 79

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 81

server-api: 2025-06-12 10:17:33 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 82

server-api: 2025-06-12 10:17:34 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 83, method: document_highlight

server-api: 2025-06-12 10:48:52 UTC pid: 21756 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-12 10:58:37 UTC pid: 21756 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 530

server-api: 2025-06-12 11:13:52 UTC pid: 21756 - ThreadPoolExecutor-0_8 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
