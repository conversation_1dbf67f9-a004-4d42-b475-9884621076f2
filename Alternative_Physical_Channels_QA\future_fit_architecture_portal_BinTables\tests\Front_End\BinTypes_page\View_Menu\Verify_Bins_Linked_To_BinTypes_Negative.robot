*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Verify Bin Types displayed on View Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TYPE_TO_VERIFY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                                        ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user gets the details of a soft-deleted bins for a bin type from the database                       ${BIN_TYPE_TO_VERIFY}
    Then The soft-deleted bins previously associated with the bin type must not be displayed on the Front End   ${BIN_TYPE_TO_VERIFY}

| *** Test Cases ***                                                                                                                  |        *DOCUMENTATION*    		          |         *BASE_URL*                 |         *BIN_TYPE_TO_VERIFY*        |
#| Verify that all displayed Linked Bins for Domestic Bin Type are not soft-deteled .      | Verify Bin Types displayed on View Page   | Verify bin types against the database.   |           ${EMPTY}                  |        Domestic                     |
| Verify that all displayed Linked Bins for Contactless Bin Type are not soft-deteled .   | Verify Bin Types displayed on View Page   | Verify bin types against the database.   |           ${EMPTY}                  |        Contactless                  |
#| Verify that all displayed Linked Bins for Token Bin Type are not soft-deteled .         | Verify Bin Types displayed on View Page   | Verify bin types against the database.   |           ${EMPTY}                  |        Token                |
#| Verify that all displayed Linked Bins for On-Us Bin Type are not soft-deteled .         | Verify Bin Types displayed on View Page   | Verify bin types against the database.   |           ${EMPTY}                  |        On-Us                        |
#| Verify that all displayed Linked Bins for Invalid Bin Type are not soft-deteled .       | Verify Bin Types displayed on View Page   | Verify bin types against the database.   |           ${EMPTY}                  |        Invalid                      |
