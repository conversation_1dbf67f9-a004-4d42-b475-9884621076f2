import json
import re


from robot.api.deco import keyword


class AddBinType:
    def __init__(self, **kwargs):
        # Initialize bins as an empty dict
        self.bins = {}
        bin_type_name = ""
        bin_type_description = ""

        if 'bin_type_name' in kwargs:
            print(f"Name: {kwargs['bin_type_name']}")
            bin_type_name = kwargs['bin_type_name']

        if 'bin_type_description' in kwargs:
            print(f"bin_type_description: {kwargs['bin_type_description']}")
            bin_type_description = kwargs['bin_type_description']

        self.bins = {
            "name": bin_type_name,
            "description": bin_type_description
        }

        print('Payload: ', self.bins)





    def get_json_request(self):
        # Return the bins in JSON format
        return json.dumps(self.bins, indent=4)

    @keyword
    def create_add_bin_type_request(self, bin_type_name, bin_type_description):
        """
        This method processes the bins and returns the JSON request.
        """
        kwargs = {'bin_type_name': bin_type_name, 'bin_type_description': bin_type_description}

        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def get_endpoint(self, domain):
        path = "/api/v1/bintables/admin/bintypes/add"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers





