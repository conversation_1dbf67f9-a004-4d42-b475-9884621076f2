*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Search Bar Functionality Search For Empty Input

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Verify Search Bar Functionality Search For Empty Input
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${SEARCH_KEY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user searches FrontEnd for NonExisting ATM     ${SEARCH_KEY}

#    And The user reads and compares Frontend ATM Details to the Backend ATM Details of the Existing ATM     ${SERIAL_NUMBER}

| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |     ***SEARCH_KEY***   |
| Verify Search Bar Functionality Search For Empty Input |  Verify Search Bar Functionality Search For Empty Input    | Verify Search Bar Functionality Search For Existing ATM  |      VMS_PROD             |                     |