from typing import List, Union


class CreateRESTRequest:
    def __init__(self, domain, device_name, device_version_number):
        self.domain = domain
        self.device_name = device_name
        self.device_version_number = device_version_number

        self.params = {
            "devicename": self.device_name,  # Adding a query parameter to filter the results by device name
            "deviceVersionNumber": self.device_version_number  # Adding a query parameter to filter the results by device Version Number
        }

    def get_endpoint(self):
        path = "/api/v1/bintables/deviceversions/downloadbintable"
        url = f"{self.domain}{path}"
        return url

    def get_params(self):
        return self.params