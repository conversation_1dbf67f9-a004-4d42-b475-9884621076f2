*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Sort Selection Functionality: Sorting Across Multiple Pages

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Verify Sort Selection Functionality: Sorting Across Multiple Pages
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${SORT_CRITERIA}    ${NUM_PAGES}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user chooses and clicks Sorting Criteria   ${SORT_CRITERIA}

    Then Go through a number of pages and Verify if Data Set is Sorted by Sort Criteria     ${NUM_PAGES}



| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |      ***SORT_CRITERIA***   |      ***NUM_PAGES***   |
| Verify Sort Selection Functionality: Sorting Across Multiple Pages | Verify Sort Selection Functionality: Sorting Across Multiple Pages | Verify Sort Selection Functionality: Sorting Across Multiple Pages |      VMS_UAT             |  ATM Number  |    10   |