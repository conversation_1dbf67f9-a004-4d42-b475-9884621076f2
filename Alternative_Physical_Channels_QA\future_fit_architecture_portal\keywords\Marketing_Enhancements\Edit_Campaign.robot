*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation  VMS Dashboard Validation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                             String
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../../utility/DatabaseUtility.py
Resource                                             ../../keywords/common/DBUtility.robot
Library                                             XML
Library                                             Collections
Library    DateTime


#***********************************PROJECT RESOURCES***************************************

*** Variables ***

*** Keywords ***
The user Edits a Campaign
    [Arguments]     ${START_DATE}   ${END_DATE}
    The user finds a campaign to edit
    Wait Until Element Is Visible    xpath=//button//span[contains(text(), 'Next') and not(ancestor::button[@disabled])]
    Click Element    xpath=//button//span[contains(text(), 'Next') and not(ancestor::button[@disabled])]
    Wait Until Element Is Visible    xpath=//input[@name='campaignName']
    SeleniumLibrary.Clear Element Text    xpath=//input[@name='campaignName']
    Input Text    xpath=//input[@name='campaignName']    Edit_Campaign
    Log To Console   The user has enter campaign name
    The user input campaign Start and End Date  ${START_DATE}   ${END_DATE}
    Sleep    7
    Wait Until Element Is Visible       xpath=//span[@class='btn-remove-image']
    ${btn_remove_image_list}=    Get WebElements    xpath=//span[@class='btn-remove-image']
    FOR    ${button}    IN    ${btn_remove_image_list}
        Click Element    xpath=//span[@class='btn-remove-image']
    END
    Capture Page Screenshot
    Sleep    5

The user input campaign Start and End Date
    [Arguments]     ${START_DATE}   ${END_DATE}
    ${start_day} =    Get Substring    ${START_DATE}    0    2
    Log To Console   Start Day: ${start_day}
    ${start_month} =    Get Substring    ${START_DATE}    3    5
    Log To Console   Start Month: ${start_month}
    ${start_month_name}=    Get Month Abbreviation    ${start_month}
    Log To Console   Start Month: ${start_month_name}
    ${start_year} =    Get Substring    ${START_DATE}    6    10
    Log To Console    Start Year: ${start_year}
    ${end_day} =    Get Substring    ${END_DATE}    0    2
    Log To Console   End Day: ${end_day}
    ${end_month} =    Get Substring    ${END_DATE}    3    5
    Log To Console   End Month: ${end_month}
    ${end_month_name}=    Get Month Abbreviation    ${end_month}
    Log To Console   End Month: ${end_month_name}
    ${end_year} =    Get Substring    ${END_DATE}    6    10
    Log To Console    End Year: ${end_year}

    Wait Until Element Is Visible    xpath=//button[@aria-label='Choose month and year']
    Click Element    xpath=//button[@aria-label='Choose month and year']
    Wait Until Element Is Visible    xpath=//button//div[contains(text(),'${start_year}')]
    Click Element    xpath=//button//div[contains(text(),'${start_year}')]
    Wait Until Element Is Visible    xpath=//button//div[contains(text(),'${start_month_name}')]
    Click Element    xpath=//button//div[contains(text(),'${start_month_name}')]
    Wait Until Element Is Visible    xpath=//button//div[contains(text(),'${start_day}')]
    Click Element    xpath=//button//div[contains(text(),'${start_day}')]

    Wait Until Element Is Visible    xpath=//button[@aria-label='Choose month and year']
    Click Element    xpath=//button[@aria-label='Choose month and year']
    Wait Until Element Is Visible    xpath=//button//div[contains(text(),'${end_year}')]
    Click Element    xpath=//button//div[contains(text(),'${end_year}')]
    Wait Until Element Is Visible    xpath=//button//div[contains(text(),'${end_month_name}')]
    Click Element    xpath=//button//div[contains(text(),'${end_month_name}')]
    Wait Until Element Is Visible    xpath=//button//div[contains(text(),'${end_day}')]
    Click Element    xpath=//button//div[contains(text(),'${end_day}')]
    Wait Until Element Is Visible    xpath=//button[@class='mat-focus-indicator mat-button mat-button-base ng-star-inserted']
    Click Element    xpath=//button[@class='mat-focus-indicator mat-button mat-button-base ng-star-inserted']
    Capture Page Screenshot


The user finds a campaign to edit
    ${elements}=    Get WebElements    xpath=//fa-icon[@matTooltip='Edit']      
    ${elements_length}=     Get Length    ${elements}
    Run Keyword If    ${elements_length} == 0    Fail    No elements found matching the XPath expression.
    ${first_element}=    Get From List    ${elements}    0
    Click Element    ${first_element}
    Capture Page Screenshot

Get Month Abbreviation
    [Arguments]    ${month_number}
    ${month_abbreviation}=    Set Variable    JAN
    IF    '${month_number}' == '1'
         ${month_abbreviation}=     Set Variable        JAN
    END
    IF    '${month_number}' == '2'
         ${month_abbreviation}=     Set Variable        FEB
    END
    IF    '${month_number}' == '3'
         ${month_abbreviation}=     Set Variable        MAR
    END
    IF    '${month_number}' == '4'
         ${month_abbreviation}=     Set Variable        APR
    END
    IF    '${month_number}' == '5'
         ${month_abbreviation}=     Set Variable        MAY
    END
    IF    '${month_number}' == '6'
         ${month_abbreviation}=     Set Variable        JUN
    END
    IF    '${month_number}' == '7'
         ${month_abbreviation}=     Set Variable        JUL
    END
    IF    '${month_number}' == '8'
         ${month_abbreviation}=     Set Variable        AUG
    END
    IF    '${month_number}' == '9'
         ${month_abbreviation}=     Set Variable        SEP
    END
    IF    '${month_number}' == '10'
         ${month_abbreviation}=     Set Variable        OCT
    END
    IF    '${month_number}' == '11'
         ${month_abbreviation}=     Set Variable        NOV
    END
    IF    '${month_number}' == '12'
         ${month_abbreviation}=     Set Variable        DEC
    END
    RETURN    ${month_abbreviation}

The user saves edited the campaign
    Wait Until Element Is Visible    xpath=//button[@class='mat-focus-indicator mat-stepper-next mat-button mat-button-base']//span[text()='Save']
    Click Element    xpath=//button[@class='mat-focus-indicator mat-stepper-next mat-button mat-button-base']//span[text()='Save']
    Capture Page Screenshot