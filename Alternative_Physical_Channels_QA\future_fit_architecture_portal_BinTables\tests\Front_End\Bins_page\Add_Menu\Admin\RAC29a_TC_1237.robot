*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite


Library             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Add multiple Bins
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     &{BINS}
    Set Test Documentation  ${DOCUMENTATION}

    ${bin_keys}=    Get Dictionary Keys   ${BINS}
    ${bin_keys_len}=    Get Length    ${bin_keys}

    ${bin_loop_len}=    Evaluate    int(${bin_keys_len} / 3)

    FOR    ${index}    IN RANGE    1    ${bin_loop_len+1}
        ${bin_number}    Get From Dictionary    ${BINS}     bin_${index}
        Log Many    Old 'bin_${index}' value:${bin_number}

        ${random_word}=     Generate random bin name
        ${BIN_NAME}=   Set Variable     ${random_word}
        ${BIN_NAME}=    Get Substring    ${BIN_NAME}    0    12

        Set To Dictionary    ${BINS}    bin_${index}    ${BIN_NAME}
        ${bin_number}    Get From Dictionary    ${BINS}     bin_${index}
        Log Many    New 'bin_${index}' value:${bin_number}
    END
    Log Many            ${BINS}
    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The User populates the multiple Bins details and save the Bins to local storage    ${BINS}
    And The created multiple bins are added to the local storage                           ${BINS}
    And The user saves the created bin(s) to the database
    Then The created multiple bins must exist in the database                              ${BINS}


| *** Test Cases ***                                              |        *DOCUMENTATION*       |         *BASE_URL*                  |         *BINS*                                                                                                                                                                                                 |
| Admin_Verify that Capturer can add multiple Bins                | Add multiple Bins   | Add new Bins to Bin Tables.   |           ${EMPTY}                  |        bin_1=Thabo1 |  bin_type_name_1=Testing Bin Type Add,Testing Bin Type Add 3 |  bin_action_date_1=2026-01-21  |  bin_2=Thabo2  |  bin_type_name_2=Testing Bin Type Add  |  bin_action_date_2=2027-01-21  |   bin_3=Thabo3  |  bin_type_name_3=Testing Bin Type Add  |  bin_action_date_3=2028-01-21   |  bin_4=Thabo4  |  bin_type_name_4=Testing Bin Type Add  |  bin_action_date_4=2029-01-21      |
