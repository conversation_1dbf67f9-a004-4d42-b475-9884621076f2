*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        MTGLA HEALTHCHECK    Login
Documentation                                       MTGLA Login 
Suite Setup                                         Set up environment variables         
Test Teardown                                       Close All Browsers                          
#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/Common/Login.robot
Resource                                            ../../keywords/Common/SetEnvironmentVariales.robot

*Variables*


*** Keywords ***
Validates MTGLA Login    
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the MTGLA Web Application    
    

| *Test Case*                                                    |      *DOCUMENTATION*          | *TEST_ENVIRONMENT*   |
| Login MTGLA System                   | Validates MTGLA Login   | Login                         |    MTGLA_UAT         | 
