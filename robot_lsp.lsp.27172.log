lsp: 2025-06-12 10:10:32 UTC pid: 27172 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-06-12 10:10:32 UTC pid: 27172 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-06-12 10:10:32 UTC pid: 27172 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-06-12 10:10:32 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 27172

lsp: 2025-06-12 10:10:32 UTC pid: 27172 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-06-12 10:10:32 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']

lsp: 2025-06-12 10:11:22 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 12, method: textDocument/documentHighlight

lsp: 2025-06-12 10:11:22 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 9

lsp: 2025-06-12 10:11:22 UTC pid: 27172 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:11:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 18

lsp: 2025-06-12 10:11:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 22, method: textDocument/documentHighlight

lsp: 2025-06-12 10:11:25 UTC pid: 27172 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:11:56 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 37, method: textDocument/hover

lsp: 2025-06-12 10:11:56 UTC pid: 27172 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:12:46 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 43, method: textDocument/hover

lsp: 2025-06-12 10:12:46 UTC pid: 27172 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:13:00 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 45, method: textDocument/hover

lsp: 2025-06-12 10:13:43 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 55, method: textDocument/hover

lsp: 2025-06-12 10:13:43 UTC pid: 27172 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:15:37 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 72, method: textDocument/hover

lsp: 2025-06-12 10:15:37 UTC pid: 27172 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:15:45 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 88, method: textDocument/documentSymbol

lsp: 2025-06-12 10:15:45 UTC pid: 27172 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 99

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 102, method: textDocument/hover

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 103

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 100

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 104, method: textDocument/documentHighlight

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 101

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 106, method: textDocument/hover

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 105

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 112, method: textDocument/hover

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 113, method: textDocument/hover

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 114, method: textDocument/documentHighlight

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 115, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 116, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 117, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 118, method: textDocument/hover

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 121, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 107, method: textDocument/foldingRange

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 120, method: textDocument/documentHighlight

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 109, method: textDocument/documentSymbol

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_7 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_10 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_9 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 123, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 122, method: textDocument/foldingRange

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 111, method: textDocument/codeLens

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_12 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_13 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 125, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 124, method: textDocument/foldingRange

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 126, method: textDocument/documentSymbol

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_7 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 127, method: textDocument/documentSymbol

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 128, method: textDocument/codeLens

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_9 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 129, method: textDocument/completion

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 135, method: textDocument/hover

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 136, method: textDocument/documentHighlight

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 131, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_10 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 137, method: textDocument/codeAction

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 130, method: textDocument/foldingRange

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 132, method: textDocument/documentSymbol

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 133, method: textDocument/documentSymbol

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 134, method: textDocument/codeLens

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_7 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_12 - INFO - robotframework_ls.robotframework_ls_completion_impl
Cancelled handling: <function _RobotFrameworkLsCompletionImpl._threaded_document_completion at 0x000002A234574F40>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_13 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_14 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_9 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:31 UTC pid: 27172 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:17:33 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 139, method: textDocument/codeAction

lsp: 2025-06-12 10:17:33 UTC pid: 27172 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:18:13 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 164, method: textDocument/hover

lsp: 2025-06-12 10:22:29 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 184

lsp: 2025-06-12 10:26:18 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Ignoring notification for unknown method $/setTrace

lsp: 2025-06-12 10:33:44 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 264

lsp: 2025-06-12 10:36:32 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 303

lsp: 2025-06-12 10:36:53 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 314

lsp: 2025-06-12 10:39:55 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 383, method: textDocument/hover

lsp: 2025-06-12 10:43:17 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 386

lsp: 2025-06-12 10:43:17 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 392, method: textDocument/codeLens

lsp: 2025-06-12 10:45:33 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 401

lsp: 2025-06-12 10:47:24 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 427

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 429, method: textDocument/hover

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 430, method: textDocument/hover

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 428, method: textDocument/definition

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 435, method: textDocument/hover

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 434, method: textDocument/definition

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 437, method: textDocument/hover

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 436, method: textDocument/definition

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 438, method: textDocument/definition

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 439, method: textDocument/definition

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 440, method: textDocument/definition

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 431

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 442, method: textDocument/hover

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 443, method: textDocument/hover

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 432

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 444, method: textDocument/documentHighlight

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 433

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_9 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_11 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_14 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_12 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:25 UTC pid: 27172 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:47:26 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 447, method: textDocument/hover

lsp: 2025-06-12 10:51:57 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 551

lsp: 2025-06-12 10:51:58 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 553, method: textDocument/hover

lsp: 2025-06-12 10:55:45 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 658, method: textDocument/hover

lsp: 2025-06-12 10:56:01 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 667, method: textDocument/hover

lsp: 2025-06-12 10:56:04 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 671, method: textDocument/hover

lsp: 2025-06-12 10:56:10 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 676, method: textDocument/hover

lsp: 2025-06-12 10:56:44 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 688, method: textDocument/hover

lsp: 2025-06-12 10:58:33 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 727, method: textDocument/hover

lsp: 2025-06-12 10:58:37 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 734, method: textDocument/documentSymbol

lsp: 2025-06-12 10:58:37 UTC pid: 27172 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 10:58:39 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 744

lsp: 2025-06-12 10:58:46 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 763, method: textDocument/foldingRange

lsp: 2025-06-12 10:58:56 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 774, method: textDocument/hover

lsp: 2025-06-12 10:59:04 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 788, method: textDocument/hover

lsp: 2025-06-12 11:00:11 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 802

lsp: 2025-06-12 11:01:47 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 834

lsp: 2025-06-12 11:01:58 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 842, method: textDocument/hover

lsp: 2025-06-12 11:02:09 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 853, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 856, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 857, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 858, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 859, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 860, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 861, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 862, method: textDocument/hover

lsp: 2025-06-12 11:02:20 UTC pid: 27172 - ThreadPoolExecutor-0_14 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x000002A232D8F560>

lsp: 2025-06-12 11:02:34 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 869, method: textDocument/hover

lsp: 2025-06-12 11:04:57 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 919

lsp: 2025-06-12 11:05:05 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 927, method: textDocument/hover

lsp: 2025-06-12 11:06:13 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 947

lsp: 2025-06-12 11:06:20 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 953, method: textDocument/hover

lsp: 2025-06-12 11:09:33 UTC pid: 27172 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 979, method: textDocument/hover

