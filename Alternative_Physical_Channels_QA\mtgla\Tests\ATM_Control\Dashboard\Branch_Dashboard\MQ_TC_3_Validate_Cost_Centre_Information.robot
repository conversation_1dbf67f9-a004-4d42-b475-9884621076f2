*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control- Validate Cost Center Information
Suite Setup                 Set up environment variables               
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot



*** Variables ***


*** Keywords ***
The user validates the Cost Center Information on Branch dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page

    And the user navigates to the ATM Control Dashboard  

    And the user selects a branch to access the Branch Dashboard 

    And the user verifies that the Cost Center Information is correctly displayed on the Front-End

    Then the user confirms that the Front-End Cost Center Information matches the corresponding data in the database 


| *Test Cases*                                                                                                                |      *DOCUMENTATION*                                          | *TEST_ENVIRONMENT*   |
|   MQ-TC-3	Validate Cost Centre Information		 | The user validates the Cost Center Information on Branch dashboard    |    validating the Cost Center Information on Branch dashboard  |    MTGLA_UAT         | 
