*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/ReactivateBin_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${SUITE NAME}               BIN Tables - Reactivate Bin




*** Keywords ***
Re-activate Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}    ${EXPECTED_ERROR_MESSAGE}    &{BINS_DETAILS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User Populates the Reactivate Bin JSON payload with                &{BINS_DETAILS}
    When The User sends the Reactivate Bin API Request                           ${BASE_URL}
    And The service returns an expected status code                              ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed                             ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                |        *DOCUMENTATION*    		                         |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |                     *EXPECTED_ERROR_MESSAGE*                                                             |                *BINS_DETAILS*                                                                                           |
| Reactivate a Bin that is not deleted                                          | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      The bin with the specified identifier was not found.                                                |     binId1=f8557069-1bba-41a3-9488-be8b2e02bf63 | date1=2024-12-09 | binIds1=7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e       |
| Reactivate a Bin without populating the 'binId' field.                        | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      The JSON value could not be converted to System.Nullable`1[System.Guid]. Path: $.binId              |     binId1= | date1=2024-12-09 | binIds1=7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e                                           |
| Reactivate a Bin with the 'binId' field populated with incorrect data.        | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      The JSON value could not be converted to System.Nullable`1[System.Guid]. Path: $.binId              |     binId1=Thabo | date1=2024-12-09 | binIds1=7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e                                      |
| Reactivate a Bin without populating the 'actionDate' field.                   | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      The JSON value could not be converted to System.Nullable`1[System.DateOnly]. Path: $.actionDate     |     binId1=f8557069-1bba-41a3-9488-be8b2e02bf63 | date1= | binIds1=7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e                 |
| Reactivate a Bin with the 'actionDate' field populated with incorrect data.   | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      Unable to convert \"Thabo\" to DateOnly.                                                            |     binId1=f8557069-1bba-41a3-9488-be8b2e02bf63 | date1=Thabo | binIds1=7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e            |
| Reactivate a Bin without populating the 'binTypeIds' field.                   | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      The JSON value could not be converted to System.Guid. Path: $.binTypeIds[0]                         |     binId1=f8557069-1bba-41a3-9488-be8b2e02bf63 | date1=2024-12-09 | binIds1=                                           |
| Reactivate a Bin wit the 'binTypeIds' field populated with incorrect data.    | Re-activate Bin   | Re-activate Bin for the Bin Tables - Negative Testing      |                                     |         400                 |      The JSON value could not be converted to System.Guid. Path: $.binTypeIds[0]                         |     binId1=f8557069-1bba-41a3-9488-be8b2e02bf63 | date1=2024-12-09 | binIds1=                                           |