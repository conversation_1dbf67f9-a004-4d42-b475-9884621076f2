*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1126




*** Keywords ***
Verify Bins displayed on View Entries Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}
    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user scrolls to the bottom of the page
    Then The pagination icon should be visible
    And The user scrolls to the top of the page
    Then The search field should be visible

| *** Test Cases ***                                                                                                                                     |        *DOCUMENTATION*    		         |         *BASE_URL*                  |
| Admin_Verify Horizontal Scroll Bar is Present and Functions Properly to View Hidden Data                | Verify Bins displayed on View Entries Page   | Verify bins against the database data.    |           ${EMPTY}                  |
