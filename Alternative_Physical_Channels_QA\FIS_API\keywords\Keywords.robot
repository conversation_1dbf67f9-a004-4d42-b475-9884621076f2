*** Settings ***

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************

Library                                             OperatingSystem
Library                                             RequestsLibrary
Library                                             Collections
Library                                             String
#***********************************PROJECT RESOURCES***************************************
Library                                              ../Keywords/csvLibrary.py
Library    String

*** Variables ***
${DATA_FILE}        FIS_API/Data/data.csv
${Access_Token}    
*** Keywords ***
Test File Exists
    ${fileExists}=    File Exists    ../Data/data.csv
    Run Keyword If    ${fileExists} is True    Log To Console    Exists! 

Get the first part of the url
     ${campaign_url} =    go_to_nth_row_nth_column    ${DATA_FILE}    1    0    
 
Do a post Request for FIS Auth and validate the response code and response body
    [Arguments]       ${ROW}    ${COLUMN}    ${URL}

    #check row column of the url
    ${BASE_URL} =    go_to_nth_row_nth_column    ${DATA_FILE}    1    1    
    ${AUTH} =    go_to_nth_row_nth_column    ${DATA_FILE}    ${ROW}    ${COLUMN}
    
    #Create a session
    create Session    mysession    ${BASE_URL}

    #Get the payload of the auth
    ${PAYLOAD} =    go_to_nth_row_nth_column    ${DATA_FILE}    ${ROW}    ${COLUMN}

    ${END_URL}=    go_to_nth_row_nth_column    ${DATA_FILE}    1    ${URL} 
    
    ${HEADERS}=    Create Dictionary     accept=text/plain    Content-Type=application/json-patch+json
    ${RESPONSE}=    POST On Session    mysession    /    data=${PAYLOAD}    headers=${HEADERS}   

    #verify the response         
    Status Should Be  200  ${RESPONSE}    

    #get the access token 
    ${json}  Set Variable    ${RESPONSE.json()}
    ${Access_Token}=   Set Variable      ${json['access_token']} 
    Set Global Variable    ${Access_Token}
    

Do a post Request for FIS execution and validate the response code and response body
    [Arguments]       ${ROW}    ${COLUMN}    ${URL}    ${get_name} 

    #check row column of the url
    ${BASE_URL} =    go_to_nth_row_nth_column    ${DATA_FILE}    1    0    

    #Create a session
    create Session    mysession    ${BASE_URL}

    #Get the url
    ${PAYLOAD} =    go_to_nth_row_nth_column    ${DATA_FILE}    ${ROW}    ${COLUMN}    
    ${END_URL}=    go_to_nth_row_nth_column    ${DATA_FILE}    1    ${URL}

    #Get the device name and test execution name
    ${Device}=   Get Environment Variable    DEVICE_NAME
    Set Global Variable    ${Device}
    ${TEST_EXECUTION_NAME}=   Get Environment Variable    TEST_EXECUTION_NAME
      
    #add the device name to the url
    ${END_URL} =   Catenate    ${Device}   ${END_URL}  
    
    ${body_1}=    Create Dictionary    name=${TEST_EXECUTION_NAME}

    ${headers}=    Create Dictionary    accept=text/plain    Authorization=Bearer ${Access_Token}   
    ${response}=    POST On Session    mysession    ${END_URL}    json=${body_1}    headers=${headers}  

    #verify the response
    Status Should Be  201  ${response}
    Should Not Be Empty    ${response.content}
    

Do a GET Request for the Status and validate the response code and response body
    [Arguments]       ${ROW}    ${END_URL} 
    ${status}    Set Variable    Completed  

    #check row column of the url
    ${base_url} =    go_to_nth_row_nth_column    ${DATA_FILE}    1    0    
   
    ${point} =    go_to_nth_row_nth_column    ${DATA_FILE}    ${ROW}    ${END_URL}
    
    Sleep  190s

    Create Session  mysession    ${base_url}    
    ${header_status}=    Create Dictionary    Authorization=Bearer ${Access_Token}   
    ${response}=  GET On Session  mysession    ${point}    headers=${header_status}
        
    #check the response body
    Status Should Be  200  ${response} 
    Should Not Be Empty    ${response.content}
    Log To Console    ${response.text}
    ${json}  Set Variable    ${response.json()}
    Log To Console    ${json['status']}

       
    

Do a GET Request for the results and validate the response code and response body
    [Arguments]       ${ROW}    ${END_URL}   
    ${results}    Set Variable    Pass
    ${status}    Set Variable    Completed

   

    #check row column of the url
    ${base_url} =    go_to_nth_row_nth_column    ${DATA_FILE}    1    0    
   
    ${END_URL} =    go_to_nth_row_nth_column    ${DATA_FILE}    ${ROW}    ${END_URL}
    ${END_URL} =   Catenate    ${Device}   ${END_URL}  

    Create Session  mysession    ${base_url}    
    ${header_status}=    Create Dictionary    Authorization=Bearer ${Access_Token}   
    ${response}=  GET On Session  mysession    ${END_URL}    headers=${header_status}
    
    
    #check the response body
    Status Should Be  200  ${response} 
    Should Not Be Empty    ${response.content}
    
    #get the status and results
    ${transactions}=    Get Value From Json  ${response.json()}    transactions
    ${transactionsList}=  Get From List   ${transactions}  0
    Log To Console    ${transactionsList[0]["verdict"]}
    Log To Console    ${transactionsList[0]["status"]}

    #verify that the result is complete and have passed
    Should Be Equal    ${transactionsList[0]["verdict"]}    ${results}
    Should Be Equal    ${transactionsList[0]["status"]}    ${status}    

   

    
   