*** Settings ***
#Author Name               : <PERSON>hab<PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot


*** Variables ***
${ADD_BIN_TYPE_BTN}                               xpath=//span[contains(text(),'Add')]
${BIN_TYPE_NAME_INPUT}                            xpath=//input[contains(@class, 'mat-input-element') and contains(@placeholder, 'Enter Bintype Name')]
${DUPLICATE_ERR_MSG}                              xpath=//p[contains(@class, 'duplicate') and contains(@class, 'ng-star-inserted')]
${BIN_TYPE_DESC_INPUT}                            xpath=//input[contains(@class, 'mat-input-element') and contains(@placeholder, 'Start typing..')]
${SAVE_BIN_TYPE_BTN}                              xpath=//button[contains(@class, 'mat-stroked-button') and contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-button-base')]
${SAVE_BIN_DIALOG}                                xpath=//mat-dialog-content[contains(@class, 'mat-dialog-content') and contains(@class, 'mat-typography')]
${SAVE_BIN_TYPE_TO_DB_BTN}                        xpath=//button[contains(@class, 'btn-save') and contains(@class, 'mat-button-base')]
${CANCEL_SAVE_BIN_TYPE_TO_DB_BTN}                 xpath=//button[contains(@class, 'btn-close') and contains(@class, 'mat-button-base')]
${VIEW_BIN_TYPES_BTN}                             xpath=//span[contains(text(),'View')]


*** Keywords ***
The user navigates to 'Add' Bin Type tab

    ${add_bin_type_btn_displayed}=     Wait for Element to be enabled    ${ADD_BIN_TYPE_BTN}

    Run Keyword If    not ${add_bin_type_btn_displayed}
    ...    Fail   There 'Add' button is not displayed on the Bin Types page.

     #Click on the bin type to Verify
    SeleniumLibrary.Click Element    ${ADD_BIN_TYPE_BTN}
    Sleep    4s

    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    ADD BINTYPE
    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    ELSE
        Capture Page Screenshot   ADD_BINTYPE_Page_Displayed.png
    END



The User populates the Bin Type details and save the Bin Tpe to the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    ${bin_type_desc_provided}=        Set Variable If
         ...       '${BIN_TYPE_DESCRIPTION}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' == ''             ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_DESCRIPTION}' != ''             ${True}

    Run Keyword If    not ${bin_type_desc_provided}
    ...    Fail     Please provide the description of the Bin Type that must be created!

    Capture Page Screenshot   ${BIN_TYPE_NAME}_details_not_populated.png


    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME_INPUT}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Fail   The   BIN_TYPE_NAME_INPUT is not displayed on the 'Add Bin Type' page.

    #Verify that the 'Add Bin Type' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_TYPE_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Type details
    SeleniumLibrary.Input Text    ${BIN_TYPE_NAME_INPUT}    ${BIN_TYPE_NAME}
    SeleniumLibrary.Input Text    ${BIN_TYPE_DESC_INPUT}    ${BIN_TYPE_DESCRIPTION}

    Capture Page Screenshot   ${BIN_TYPE_NAME}_details_populated.png


    #Verify that the 'Add Bin Type' button is now enabled
    ${web_element2}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_TYPE_BTN}
    ${attr2}=        Get Current Element Attributes      ${web_element2}
    ${disabled_attribute}=      Get From Dictionary     ${attr2}      disabled

     Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Add Bin Type' button is now enabled becuase the Bin Type details have been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is disabled even though the Bin Type details have been populated.

    #Click to add the Bin Type
    SeleniumLibrary.Click Element  ${SAVE_BIN_TYPE_BTN}

    #Verify that the save bin  dialog is displayed
    Capture Page Screenshot   ${BIN_TYPE_NAME}_Confirmation_details_populated.png
    ${add_bin_type_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/p
    ${add_bin_type_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_confirmation_msg_xpath}

    Run Keyword If    '${add_bin_type_confirmation_msg}' == 'You are about to add a new BIN Type:'
    ...    Log Many  The 'Add Bin Type' confirmation message is displayed.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' confirmation message is not displayed.


    ${add_bin_type_name_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/ul/li
    ${add_bin_type_name_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_name_confirmation_msg_xpath}

    Run Keyword If    '${add_bin_type_name_confirmation_msg}' == '${BIN_TYPE_NAME}'
    ...    Log Many  The bin type name: '${BIN_TYPE_NAME}' is displayed correctly on the dialog.
    ...  ELSE
    ...    Fail  The bin type name: '${BIN_TYPE_NAME}' is not displayed correctly on the dialog.

    #Save the Bin Type to Database
    SeleniumLibrary.Click Element    ${SAVE_BIN_TYPE_TO_DB_BTN}
    Capture Page Screenshot   ${BIN_TYPE_NAME}_saved_to_db.png

    Set Global Variable    ${CREATED_BIN_TYPE_NAME}     ${BIN_TYPE_NAME}
    Sleep   4s


The created bin type should exist in the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...     Fail    Please provide the Bin Type Name for the Bin Type that must be verified.


    ${bin_type_description_provided}=        Set Variable If
         ...       '${BIN_TYPE_DESCRIPTION}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' == ''             ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_DESCRIPTION}' != ''             ${True}


    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_TO_ADD}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no bin type callled '${BIN_TYPE_TO_ADD}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_desc}=                        Get Column Data By Name       ${first_row_results}       Description
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    #Verify that the Bin Type details are correct
    Run Keyword If    '${db_bin_type_name}' == '${BIN_TYPE_NAME}'
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is the same as the provided Bin Type name.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The bin type name: '${db_bin_type_name}' from the DB is not the same as the provided Bin Type. The provided Bin Type name is '${BIN_TYPE_NAME}'.

    IF    ${bin_type_description_provided}
        Run Keyword If    '${db_bin_type_desc}' == '${BIN_TYPE_DESCRIPTION}'
        ...    Log Many  The bin type description: '${db_bin_type_desc}' from the DB is the same as the provided Bin Type description.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail  The bin type description: '${db_bin_type_desc}' from the DB is not the same as the provided Bin Type description. The provided Bin Type description is '${BIN_TYPE_DESCRIPTION}'.
    END


    Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is active.
    ...  ELSE
    ...    Fail  The bin type name: '${db_bin_type_name}' from the DB is not active.



The error message must be displayed when the user Adds a Bin Type that is already in the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!

    ${bin_type_desc_provided}=        Set Variable If
         ...       '${BIN_TYPE_DESCRIPTION}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' == ''             ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_DESCRIPTION}' != ''             ${True}

    Run Keyword If    not ${bin_type_desc_provided}
    ...    Fail     Please provide the description of the Bin Type that must be created!

    Capture Page Screenshot   ${BIN_TYPE_NAME}_details_not_populated.png


    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME_INPUT}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Fail   The   BIN_TYPE_NAME_INPUT is not displayed on the 'Add Bin Type' page.

    #Verify that the 'Add Bin Type' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_TYPE_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled because the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Type details
    SeleniumLibrary.Input Text    ${BIN_TYPE_NAME_INPUT}    ${BIN_TYPE_NAME}
    SeleniumLibrary.Input Text    ${BIN_TYPE_DESC_INPUT}    ${BIN_TYPE_DESCRIPTION}

    Capture Page Screenshot   ${BIN_TYPE_NAME}_details_populated.png

   #Verify that the error message is displayed and the 'Add Bin Type' button is not enabled
   ${error_msg_is_displayed}=    Run Keyword And Return Status   SeleniumLibrary.Element Should Be Visible   ${DUPLICATE_ERR_MSG}
   
   IF    ${error_msg_is_displayed}
       Log Many  The error message is displayed.
       ${error_message_text}=       SeleniumLibrary.Get Text    ${DUPLICATE_ERR_MSG}
       Should Be Equal As Strings    Duplicate Found!    ${error_message_text}      msg=The expected error message, which is 'Duplicate Found!', was not displayed.        strip_spaces=True

       #Verify that the 'Add Bin Type' button is disabled
        ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_TYPE_BTN}
        ${attr}=        Get Current Element Attributes     ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

        Log Many    attr:   ${disabled_attribute}

        Run Keyword If    '${disabled_attribute}' == 'True'
        ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type name populated is a lready existing in the database.
        ...  ELSE
        ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details populated exist in the database.

   ELSE
      Fail  The error message is not displayed.  
   END