*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify Search Bin by Number (Full Search) on Front-End
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${FULL_BIN_NUMBER}    ${EXPECTED_FULL_BIN_NUMBER}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The user searches with a full Bin Number    ${FULL_BIN_NUMBER}
    Then The user verifies that the Search Bin By Name API Returned a Result for Full Search on the Front-End    ${EXPECTED_FULL_BIN_NUMBER}

| *** Test Cases ***                                                                                                           |        *DOCUMENTATION*                                    |         *BASE_URL*                  |   *FULL_BIN_NUMBER*     |   *EXPECTED_FULL_BIN_NUMBER*  |         
| Verify user can successfully Search Bin by Number (Full Search)   | Verify Search Bin by Number (Full Search) on Front-End   | Testing Search Bin by Number (Full Search) on Front-End   |           ${EMPTY}                  |        3030020          |      303020                   |        