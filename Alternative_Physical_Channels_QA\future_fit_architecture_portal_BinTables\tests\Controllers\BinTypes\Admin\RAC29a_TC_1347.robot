*** Settings ***
# Author Name               : Thab<PERSON>
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

Library             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/BinTypeAdd_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot



*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
Add Bin Type and Verify Response
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}    ${BIN_TYPE_NAME}    ${BIN_TYPE_DETAILS}    ${EXPECTED_STATUS_CODE}    ${EXPECTED_ERROR_MSG}

    Given The User Populates the Add Bin Type JSON payload with   ${BIN_TYPE_NAME}    ${BIN_TYPE_DETAILS}
    When The User sends the Add Bin Type API Request                ${BASE_URL}
    And The service returns an expected status code                 ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed            ${EXPECTED_ERROR_MSG}

| *** Test Cases ***                                                                                                                                                     |             *DOCUMENTATION*        |    *BASE_URL*            | *BIN_TYPE_NAME*         | *BIN_TYPE_DETAILS*         |    *EXPECTED_STATUS_CODE*    |    *EXPECTED_ERROR_MSG*                                         |
| Verify that the Add API returns an appropriate error when the required fields (Name and Description) are not provided.            | Add Bin Type and Verify Response   | Add Bin Type and verify response   |                          |  ${EMPTY}               |  ${EMPTY}                  |       400                    |    'Name' must not be empty., 'Description' must not be empty.    |