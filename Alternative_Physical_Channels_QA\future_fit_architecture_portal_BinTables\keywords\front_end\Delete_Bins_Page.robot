*** Settings ***
#Author Name               : <PERSON>hab<PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime
Library                                              ../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource    ../../../future_fit_architecture_portal/keywords/common/GenericMethods.robot


*** Variables ***
${ADD_BIN_BTN}                        xpath=//span[contains(text(),'Add')]
${BIN_NAME_INPUT}                     xpath=//input[contains(@name, 'binNumber') and contains(@placeholder, 'Enter Number')]
${BIN_TYPE_INPUT}                     xpath=//input[contains(@name, 'binType') and contains(@placeholder, 'Enter BIN Type')]
${DUPLICATE_ERR_MSG}                  xpath=//p[contains(@class, 'duplicate') and contains(@class, 'ng-star-inserted')]
${DELETE_BIN_BTN}                     xpath=//button[contains(@class, 'mat-stroked-button') and contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-button-base')]
${SAVE_BIN_DIALOG}                    xpath=//mat-dialog-content[contains(@class, 'mat-dialog-content') and contains(@class, 'mat-typography')]
${SAVE_BINS_AND_EXIT_BTN}             xpath=//button[contains(@class, 'btn-save') and contains(@class, 'mat-button-base')]
${CANCEL_SAVE_BIN}                    xpath=//button[contains(@class, 'btn-close') and contains(@class, 'mat-button-base')]
${VIEW_BIN_BTN}                       xpath=//span[contains(text(),'View')]
${DELETE_BINS_BTN}                    xpath=//span[contains(text(),'Delete')]
${OPEN_ACTION_DATE_CAL_BTN}           xpath=//mat-datepicker-toggle[contains(@class, 'mat-datepicker-toggle')]
${LOCAL_STORAGE_BINS_TABLE}           xpath=//table[contains(@class, 'mat-table') and contains(@class, 'cdk-table') and contains(@class, 'mat-sort')]
${SAVE_BIN_DIALOG}                    xpath=//mat-dialog-content[contains(@class, 'mat-dialog-content') and contains(@class, 'mat-typography')]
${SAVE_BIN_TYPE_TO_DB_BTN}            xpath=//mat-dialog-actions[contains(@class, 'mat-dialog-actions') and contains(@class, 'mat-dialog-actions-align-end')]/div/div[2]/button
${CANCEL_SAVE_BIN_TYPE_TO_DB_BTN}     xpath=//mat-dialog-actions[contains(@class, 'mat-dialog-actions') and contains(@class, 'mat-dialog-actions-align-end')]/div/div[1]/button
${80_PERCENT_ZOOM_LEVEL}              0.8  # Set zoom level (1.0 = 100%, 0.8 = 80%, 1.2 = 120%)
${100_PERCENT_ZOOM_LEVEL}             1.0  # Set zoom level (1.0 = 100%, 0.8 = 80%, 1.2 = 120%)
${BIN_NUMBER_DETAILS}                 xpath=//*[contains(@class,'bin-number')]
${BIN_RESULTS_RETURNED_LOCATOR}       xpath=//span[@class='mat-option-text']/div[@class='bin-number']
${BIN_NUMBER_NOT_FOUND_LOCATOR}       xpath=//*[text()[normalize-space(.)='Bin number not found!']]
${ALL_FALSE}                          ${True}
${ACTION_DATE_LOCATOR}                xpath=//button[@aria-label='Open calendar']
${VIEW_MENU_LOCATOR}                  xpath=//*[text()[normalize-space(.)='View Entries']]
${GREYED_OUT_PAST_DATES}              xpath=//td[@role='gridcell']//button[contains(@class, 'mat-calendar-body-disabled')]
${CALENDAR_DATES}                          xpath=//td[@role='gridcell']/button
${BIN_TABLES_ADMIN_USER}                            ac30fc6b-5773-4edc-97f3-412e489e1c1f
${BIN_TABLES_CAPTURER_USER}                         decd98cd-20cf-4663-a6ad-60f6762bd241
${BIN_TABLES_APPROVER_USER}                         e2cec3df-91e1-4830-817b-0cbd22148e54

*** Keywords ***
The Bin Number is active in the Database
    [Arguments]     ${BIN_NAME}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!

    # Ensure the results are not empty
    ${bin_is_deleted}=        Get the Bin 'ISDeleted' status        ${BIN_NAME}
    Run Keyword If    ${bin_is_deleted}
        ...    Fail    The Bin Number: '${BIN_NAME}' is not active in the database.
        ...  ELSE
        ...    Log Many     The Bin Number: '${BIN_NAME}' is active in the database.

The deleted bin must be de-activated on the database
    [Arguments]     ${BIN_NUMBER}

     ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NUMBER}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!

    # Ensure the results are not empty
    ${bin_is_deleted}=        Get the Bin 'ISDeleted' status        ${BIN_NUMBER}
    Run Keyword If    ${bin_is_deleted}
        ...    Log Many    The Bin Number: '${BIN_NUMBER}' is not active in the database.
        ...  ELSE
        ...    Fail     The Bin Number: '${BIN_NUMBER}' is active in the database.


The Bin Number is a Draft
    [Arguments]     ${BIN_NUMBER}
    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NUMBER}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!
    
    # Ensure the results are not empty
    ${bin_is_draft}=        Get the Bin 'IsNotADraft' status        ${BIN_NUMBER}
    Run Keyword If    ${bin_is_draft}==0
        ...    Log    The Bin Number: '${BIN_NUMBER}' is a draft.
        ...  ELSE
        ...    Fail     The Bin Number: '${BIN_NUMBER}' is not a draft. 
        
The Bin Number is not a draft (has been through Admin/Approver process atleast once)
    [Arguments]     ${BIN_NAME}
    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!
    
    # Ensure the results are not empty
    ${bin_is_draft}=        Get the Bin 'IsNotADraft' status        ${BIN_NAME}
    Run Keyword If    ${bin_is_draft}==1
        ...    Log    The Bin Number: '${BIN_NAME}' is not draft.
        ...  ELSE
        ...    Fail     The Bin Number: '${BIN_NAME}' is a draft. 
        
The Bin must be hard-deleted 
    [Arguments]    ${BIN_NAME}

    ${is_bin_hard_deleted}=  Verify if hard deleted bin exists  ${BIN_NAME}

    ${length}=    Evaluate    len(${is_bin_hard_deleted}) if ${is_bin_hard_deleted} is not None else 0

    Run Keyword If    ${length} == 0  
        ...    Log    Bin:${BIN_NAME} not found in database, it has been hard deleted!  INFO  
        ...    ELSE  Fail    Bin found in database, it was NOT hard deleted! Bin details: ${is_bin_hard_deleted}



The User gets a draft bin number for deletion process
    ${draft_bin_number}=    Get Draft Bin Number
    Log    Draft bin retrieved: ${draft_bin_number}
    Set Global Variable    ${global_draft_bin_number}    ${draft_bin_number}


The User gets a Non-draft bin for deletion process
    ${non_draft_bin_number}=    Get Non-Draft Bin Number
    Log    Non-Draft bin retrieved: ${non_draft_bin_number}
    Set Global Variable    ${global_non_draft_bin_number}    ${non_draft_bin_number}



The Bin 'IsDeleted' field should update to true (1) on the database 
    [Arguments]     ${BIN_NAME}

     ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!

    # Ensure the results are not empty
    ${bin_is_deleted}=        Get the Bin 'ISDeleted' status        ${BIN_NAME}
    Run Keyword If    ${bin_is_deleted}=1
        ...    Log Many    The Bin Number: '${BIN_NAME}' is not active in the database.
        ...  ELSE
        ...    Fail     The Bin Number: '${BIN_NAME}' is active in the database.

The Bin 'IsDeleted' field must remain false (0) on the database as the bin deletion is pending approval
    [Arguments]     ${BIN_NAME}

     ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!

    # Ensure the results are not empty
    ${bin_is_deleted}=        Get the Bin 'ISDeleted' status        ${BIN_NAME}
    Run Keyword If    ${bin_is_deleted}=0
        ...    Log Many    The Bin Number: '${BIN_NAME}' IsDeleted field is false on the database as the bin is awaiting approval for deletion
        ...  ELSE
        ...    Fail     The Bin Number: '${BIN_NAME}' IsDeleted field is true on the database even though the bin is awaiting approval for deletion!

The Bin 'Status' field should update to true (1) on the database to indicate that the deletion is pending approval
    [Arguments]     ${BIN_NAME}

     ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin that must be searched in the database!

    # Ensure the results are not empty
    ${bin_status_field}=        Get the Bin 'Status' field        ${BIN_NAME}
    Log    Bin status from DB: ${bin_status_field}
    ${bin_status_int} =  Evaluate  int(${bin_status_field})

    Run Keyword If    ${bin_status_field} == 1
    ...    Log Many    The Bin Number status is set to pending in the database.
    ...  ELSE
    ...    Fail    The Bin status did not update to pending in the database.

The delete bin search results should exist in the database
    [Arguments]     ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}

    #Ensure the results are not empty
    ${bin_number_exist}=        Get the Bins returned by Delete search        ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}
    Log    Bins found on the database:${bin_number_exist}
    
    #Lists for data retruned from the database 
    ${BIN_NUMBERS}    Create List
    ${IS_DELETED_STATUSES}    Create List

    #Loop through database results
    FOR  ${bin}  IN  @{bin_number_exist}
        Append To List    ${BIN_NUMBERS}    ${bin['Number']}
        Append To List    ${IS_DELETED_STATUSES}    ${bin['IsDeleted']}
    END

    Log    BIN NUMBERS: ${BIN_NUMBERS}
    Log    IS DELETED STATUSES: ${IS_DELETED_STATUSES}

    #Verify if all IsDeleted status for bins returned are false 
    FOR  ${status}  IN  @{IS_DELETED_STATUSES}
        Run Keyword If  '${status}' != '0'  
        ...  Set Variable  ${ALL_FALSE}  ${False}
    END
    Run Keyword If  ${ALL_FALSE} == ${True}  
    ...  Log  All bins returned in the delete search are correctly marked as false (IsDeleted = 0)  
    ...  ELSE  
    ...  Fail  Some bins returned have IsDeleted set to true in the database. Deleted bins should not appear in the delete bin search results.
    
    # Conversion to compare bin numbers
    ${FRONT_END_SEARCH_RESULTS_FOR_DELETE_BINS}=    Evaluate    [str(x) for x in ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}]
    Log    Front End BIN Numbers returned by delete search: ${FRONT_END_SEARCH_RESULTS_FOR_DELETE_BINS}

    # Custom sorting to handle bin numbers that can be pure numbers, pure letters, or mixed
    ${SORTED_FRONT_END_RESULTS}=    Evaluate    sorted(${FRONT_END_SEARCH_RESULTS_FOR_DELETE_BINS}, key=lambda x: (int(''.join(filter(str.isdigit, x))) if any(c.isdigit() for c in x) else float('inf'), x))

    # Sort database BIN numbers similarly
    ${DATABASE_BIN_NUMBERS}=    Evaluate    [str(x) for x in ${BIN_NUMBERS}]
    ${SORTED_DATABASE_RESULTS}=    Evaluate    sorted(${DATABASE_BIN_NUMBERS}, key=lambda x: (int(''.join(filter(str.isdigit, x))) if any(c.isdigit() for c in x) else float('inf'), x))

    Log    Database BIN NUMBERS: ${DATABASE_BIN_NUMBERS}

    # CHECK IF FRONT END DELETE SEARCH RESULTS RETURNED EXISTS ON THE DATABASE
    Run Keyword If    ${SORTED_FRONT_END_RESULTS} == ${SORTED_DATABASE_RESULTS}  
    ...    Log    Front-end delete search result bins match the database records  
    ...    ELSE    Fail    Front-end delete search result bins do not match the database records correctly!

    
    


The user navigates to 'Delete' Bin tab

    ${add_bin_type_btn_displayed}=     Wait for Element to be enabled    ${DELETE_BINS_BTN}
    Sleep    2s

    Run Keyword If    not ${add_bin_type_btn_displayed}
    ...    Fail   The 'Delete' button is not displayed on the Bins page.
    
    Wait for spinner to disapear
    #Click on the bin type to Verify
    SeleniumLibrary.Click Element    ${DELETE_BINS_BTN}
    Sleep    4s

    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    DELETE BIN


The User populates the Bin details of the bin to be deleted
    [Arguments]     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be deleted!




    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!

    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Delete Bin' page.

    #Verify that the 'Delete Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Delete Bin Type' button is disabled becuase the Bin details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is enabled even though the Bin details have not yet been populated.

    #Populate the Bin Name
    #SeleniumLibrary.Click Element    ${BIN_NAME_INPUT}
    #Sleep    2s
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    Sleep     5s
    SeleniumLibrary.Click Element    ${BIN_NUMBER_DETAILS}
    Sleep     2s
    #Press the TAB ke
    Press Key   ${BIN_NAME_INPUT}      \\9

    #Get the Bin Type(s) displayed on the Bin Type Name input
    ${displayed_bin_types}=         SeleniumLibrary.Get Text    ${BIN_TYPE_INPUT}

    Log Many        ${displayed_bin_types}

     #Verify that the 'Delete Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Delete Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is enabled even though the Bin Action Date has not yet been selected.

    Sleep    2s
    ${action_date_selected}=      Run Keyword And Return Status    Select Action Date     ${BIN_ACTION_DATE}
    Log Many    action_date_selected: ${action_date_selected}
    Sleep    2s
    Capture Page Screenshot   ${BIN_NAME}actionDate_details_populated.png
    IF    ${action_date_selected} == ${False}
         Fail   Action Date was not selected.
         RETURN
    END

   #Verify that the 'Add Bin' button is now enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Delete Bin Type' button is enabled becuase all the Bin details have been populated.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is still disabled even though all the Bin details have been populated.

    SeleniumLibrary.Click Element    ${DELETE_BIN_BTN}
    Sleep    2s
    Capture Page Screenshot   ${BIN_NAME}_details_added_tolocal.png

    Set Global Variable    ${DELETED_BIN_NUMBER}     ${BIN_NAME}
    Set Global Variable    ${DELETED_BIN_TYPE_NAME}  ${BIN_TYPE_NAME}
    Set Global Variable    ${DELETED_BIN_ACTION_DATE}  ${BIN_ACTION_DATE}

The User populates the deleted Bin number on the binNumber input field
    [Arguments]     ${BIN_NAME}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be deleted!




    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Delete Bin' page.

    #Verify that the 'Delete Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Delete Bin Type' button is disabled becuase the Bin details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is enabled even though the Bin details have not yet been populated.

    #Populate the Bin Name
    #SeleniumLibrary.Click Element    ${BIN_NAME_INPUT}
    #Sleep    2s
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    #Press the TAB ke
    Press Key   ${BIN_NAME_INPUT}      \\9

     #Verify that the 'Delete Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Delete Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is enabled even though the Bin Action Date has not yet been selected.




Select Action Date
    [Arguments]    ${ACTION_DATE_DATA}

    ${date_is_valid}=       Check if valid date     ${ACTION_DATE_DATA}

     Run Keyword If    not ${date_is_valid}
    ...    Fail  The provided data for Action Date is not valid. The data provided is '${ACTION_DATE_DATA}'.

    Log Many    '${ACTION_DATE_DATA}'
    #Extract date components
    ${action_day}=      Extract Date Component      ${ACTION_DATE_DATA}     Day
    ${action_month}=      Extract Date Component      ${ACTION_DATE_DATA}     Month
    ${action_year}=      Extract Date Component      ${ACTION_DATE_DATA}     Year

    #Click the button to Open Action Date calendar
     Sleep   3s
     Scroll Element Into View    ${OPEN_ACTION_DATE_CAL_BTN}

     SeleniumLibrary.Click Element    ${OPEN_ACTION_DATE_CAL_BTN}


     #Verify that the 'Calendar Icon' elements are displayed
    ${calendar_web_elements}=         SeleniumLibrary.Get Webelements  xpath=//mat-calendar[@id='mat-datepicker-0']
    ${number_of_elements_displayed}=    Get Length      ${calendar_web_elements}
    Run Keyword If    ${number_of_elements_displayed} == 0
    ...    Run Keyword And Continue On Failure    Fail  The calendar icon is not displayed.

    #Get the month-year text displayed on the calendar and verify that it is the required one
    ${expected_month_year_text}=        Catenate    ${action_month}${SPACE}${action_year}
    FOR    ${index}    IN RANGE    1    ${number_of_elements_displayed} + 1
        ${displayed_month_year_xpath}=           Catenate  //mat-calendar[@id='mat-datepicker-0'][${index}]/mat-calendar-header/div/div/button[1]
        ${displayed_month_year_text}=       SeleniumLibrary.Get Text    ${displayed_month_year_xpath}
        ${displayed_years_buttons_xpath}=              Catenate  //table[@class='mat-calendar-table']/tbody/tr/td/button

        #If the displayed month-year text is not the required one,
        #select the required month-year
        IF    '${expected_month_year_text}' != '${displayed_month_year_text}'
          SeleniumLibrary.Click Element   ${displayed_month_year_xpath}
          #Select the required year from the
          ${displayed_years_buttons_web_elements}=       SeleniumLibrary.Get Webelements    ${displayed_years_buttons_xpath}
          ${year_found}=    Set Variable    ${False}
          FOR    ${year_element}    IN    @{displayed_years_buttons_web_elements}
              ${displayed_year_text}=       SeleniumLibrary.Get Text    ${year_element}
              Log Many      displayed_year_text: ${displayed_year_text}
              IF    '${displayed_year_text.strip()}' == '${action_year}'
                    SeleniumLibrary.Click Element    ${year_element}
                    ${year_found}=    Set Variable    ${True}
                    Exit For Loop
              END
          END

          IF    ${year_found}
              Log Many  The required year, which is: '${action_year}', was selected on the calendar.
              Sleep     2s
              ${displayed_months_buttons_xpath}=              Catenate  //table[@class='mat-calendar-table']/tbody/tr/td/button
              ${displayed_months_buttons_web_elements}=       SeleniumLibrary.Get Webelements    ${displayed_years_buttons_xpath}
              ${month_found}=    Set Variable    ${False}
              FOR    ${month_element}    IN    @{displayed_months_buttons_web_elements}
                  ${displayed_month_text}=       SeleniumLibrary.Get Text    ${month_element}

                  Log Many      displayed_month_text: ${displayed_month_text}
                  IF    '${displayed_month_text.strip()}' == '${action_month}'
                        SeleniumLibrary.Click Element    ${month_element}
                        ${month_found}=    Set Variable    ${True}
                        Exit For Loop
                  END
              END

              IF    ${month_found}
                    Log Many  The required month, which is: '${month_element}', was selected on the calendar.
              ELSE
                   Run Keyword And Continue On Failure    Fail   The required month, which is: '${month_element}', was not selected on the calendar.
              END
          ELSE
              Run Keyword And Continue On Failure    Fail   The required year, which is: '${action_year}', was not selected on the calendar.
          END
        END

        #Select the day required
        ${backward_button_xpath}=           Catenate  //mat-calendar[@id='mat-datepicker-0'][${index}]/mat-calendar-header/div/div/button[2]
        ${forward_button_xpath}=           Catenate  //mat-calendar[@id='mat-datepicker-0'][${index}]/mat-calendar-header/div/div/button[3]

        ${displayed_days_buttons_xpath}=              Catenate  //table[@class='mat-calendar-table']/tbody/tr/td/button
        ${displayed_days_buttons_web_elements}=       SeleniumLibrary.Get Webelements    ${displayed_years_buttons_xpath}
        ${day_found}=    Set Variable    ${False}
        FOR    ${day_element}    IN    @{displayed_days_buttons_web_elements}
           ${displayed_day_text}=       SeleniumLibrary.Get Text    ${day_element}
           Log Many      displayed_day_text: ${displayed_day_text}
           IF    '${displayed_day_text.strip()}' == '${action_day}'
                SeleniumLibrary.Click Element    ${day_element}
                ${day_found}=    Set Variable    ${True}
                Exit For Loop
           END
        END
        IF    ${day_found}
                Log Many  The required day, which is: '${action_day}', was selected on the calendar.
        ELSE
               Run Keyword And Continue On Failure    Fail   The required day, which is: '${action_day}', was not selected on the calendar.
        END

    END




The user must be able to delete the bin

    #Verify that the save bin  dialog is displayed
    Capture Page Screenshot   ${DELETED_BIN_TYPE_NAME}_Confirmation_details_populated.png
    ${add_bin_type_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/p
    ${add_bin_type_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_confirmation_msg_xpath}

    Run Keyword If    '${add_bin_type_confirmation_msg}' == 'You are about to delete the following BIN:'
    ...    Log Many  The 'Add Bin' confirmation message is displayed.
    ...  ELSE
    ...    Fail  The 'Add Bin' confirmation message is not displayed. 


    #Save the Bin Type to Database

    SeleniumLibrary.Click Element    ${SAVE_BIN_TYPE_TO_DB_BTN}
    Sleep  5s
    Capture Page Screenshot   ${DELETED_BIN_TYPE_NAME}_saved_to_db.png





The error message must be displayed
    Capture Page Screenshot   _details_populated.png
   #Verify that the error message is displayed and the 'Add Bin Type' button is not enabled
   ${error_msg_is_displayed}=    Run Keyword And Return Status   SeleniumLibrary.Element Should Be Visible   ${DUPLICATE_ERR_MSG}

   IF    ${error_msg_is_displayed}
       Log Many  The error message is displayed.
       ${error_message_text}=       SeleniumLibrary.Get Text    ${DUPLICATE_ERR_MSG}
       Should Be Equal As Strings     Bin number not found!    ${error_message_text}      msg=The expected error message, which is ' Bin number not found! ', was not displayed.        strip_spaces=True
   ELSE
      Fail  The error message is not displayed.
   END

The user verifies that only one bin can be deleted
    Log    The Bin Table application allows only one bin to be deleted per flow.

The user searches for a Bin Number to be deleted 
    [Arguments]     ${BIN_NAME}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be deleted!

    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Delete Bin' page.

    #Populate the Bin Name
    SeleniumLibrary.Click Element    ${BIN_NAME_INPUT}
    Sleep    2s
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    Sleep    6s

The user verifies that the Bin search returned results 
    [Arguments]    ${BIN_NAME}
    #Get list of elements
    ${RESULTS_RETURNED}=    Get WebElements    ${BIN_RESULTS_RETURNED_LOCATOR}

    #Initialize an empty list for storing text values
    ${DELETE_SEARCH_RESULTS}=    Create List   

    # Loop through each element and get its text
    FOR    ${ELEMENT}    IN    @{RESULTS_RETURNED}
        ${TEXT}=    Get Text    ${ELEMENT}
        Append To List    ${DELETE_SEARCH_RESULTS}    ${TEXT}
        Log    Bin Returned: ${TEXT}
    END

    Log    ${DELETE_SEARCH_RESULTS}

    ${BIN_RESULTS_STRING} =    Evaluate    "({})".format(",".join(["'{}'".format(bin) for bin in ${DELETE_SEARCH_RESULTS}]))

    # Store the formatted string in a global variable
    Set Global Variable    ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}    ${BIN_RESULTS_STRING}

   
    
The User populates an invalid Bin Number
    [Arguments]    ${INVALID_BIN_NUMBER}
    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Delete Bin' page.
    
    #Input Invalid Bin Number
    Input Text    ${BIN_NAME_INPUT}    ${INVALID_BIN_NUMBER}
    Sleep    3s 
    
The user verifies that the 'Bin not found' error message is displayed
    [Arguments]    ${EXPECTED_ERROR_MESSAGE}
    #Get Error Messsage displayed
    ${FE_ERROR_MESSAGE_INAVLID_BIN}=    Get webelement    ${BIN_NUMBER_NOT_FOUND_LOCATOR} 
    ${ACTUAL_ERROR_DISPLAYED}=    Get Text    ${FE_ERROR_MESSAGE_INAVLID_BIN}
    Log    ${ACTUAL_ERROR_DISPLAYED}

    Run Keyword If  '${ACTUAL_ERROR_DISPLAYED}' == ''  Fail  "Error message for invalid bin is not displayed as expected."

    Should be equal    ${ACTUAL_ERROR_DISPLAYED}    ${EXPECTED_ERROR_MESSAGE}
    Log    The error message correctly displayed as:${ACTUAL_ERROR_DISPLAYED} and matches the expected error message:${EXPECTED_ERROR_MESSAGE}

The user verifies that the Delete Bin button remains disabled
    #Verify that the 'Delete Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Delete Bin Type' button is disabled because the Bin details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is enabled even though the Bin details have not yet been populated.
    
The user verifies that the Bin Type field pre-populated and is greyed out
    #Verify Bin Type Field is pre-populated
    ${bin_type_field_value}    Get Element Attribute    xpath=//input[@placeholder='Enter BIN Type']    value
    Run Keyword If    '${bin_type_field_value}' == ''    Fail    Bin Type field not populated!
    Log    Bin Type field is: ${bin_type_field_value} and is correctly populated

    #Verify Bin Type field is greyed out 
    ${is_bin_type_greyed_out}    Get Element Attribute    xpath=//input[@id='mat-input-1']    disabled
    Run Keyword If    '${is_bin_type_greyed_out}' == 'true'    Log    Field is correctly greyed out.

    Run Keyword If    '${is_bin_type_greyed_out}' != 'true'    Fail    Bin Type Field is NOT greyed out!

The User populates a Bin Number
    [Arguments]    ${BIN_NAME}
    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Delete Bin' page.

    #Populate the Bin Name
    SeleniumLibrary.Click Element    ${BIN_NAME_INPUT}
    Sleep    2s
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    Sleep     5s
    SeleniumLibrary.Click Element    ${BIN_NUMBER_DETAILS}
    Sleep     2s


Click On Action Date Icon
    Click Element    ${ACTION_DATE_LOCATOR}



The User selects an Action Date 
    [Arguments]    ${BIN_ACTION_DATE}
    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!
    
    ${action_date_selected}=      Run Keyword And Return Status    Select Action Date     ${BIN_ACTION_DATE}
    Log Many    action_date_selected: ${action_date_selected}
    Sleep    2s
    
    IF    ${action_date_selected} == ${False}
         Fail   Action Date was not selected.
         RETURN
    END

The user verifies that the delete button is enabled
    #Verify that the 'Add Bin' button is now enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${DELETE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Delete Bin Type' button is enabled becuase all the Bin details have been populated.
    ...  ELSE
    ...    Fail  The 'Delete Bin Type' button is still disabled even though all the Bin details have been populated.
    
The user verifies that the delete bin confirmation pop-up displays after the bin was deleted
    [Arguments]    ${BIN_NAME}

    ${DELETE_CONFIRMATION_LOCATOR}=    Set Variable    xpath=//*[text()[normalize-space(.)=': BIN(s) ${BIN_NAME} have been deleted successfully.']]

    #Confirm deletion confirmation
    ${DELETE_CONFIRMATION_RESULT}=    Run Keyword And Return Status    Element Should Be Visible    ${DELETE_CONFIRMATION_LOCATOR}    timeout=5s
    Run Keyword If   ${DELETE_CONFIRMATION_RESULT}
    ...    Log    Bin Deletion Confirmation Pop-up displayed
    ...    ELSE    Fail    Bin Deletion confirmation message did not display!

The user confirms redirection to the View Menu page
    #Confirm that user is redirected to the View Menu 
    ${DELETION_REDIRECT_RESULT}=    Run Keyword And Return Status    Page Should Contain Element    ${VIEW_MENU_LOCATOR}    timeout=5s
    Run Keyword If    ${DELETION_REDIRECT_RESULT}  
    ...    Log    User is successfully redirected to the View Menu after bin deletion process...
    ...    ELSE    Fail    User was not redirected to the View Menu after Bin Deletion process!


The User Verifies that a bin deleted moves to the top of the View Menu
    [Arguments]    ${BIN_NAME}
    #Confirm recently deleted Bin moves to top of the View Menu table
    #Get the First Bin Number
    ${FIRST_ROW_BIN_LOCATOR}=    Set Variable    xpath=//*[text()[normalize-space(.)='${BIN_NAME}']][1]

    ${TOP_BIN}=    Get Text    ${FIRST_ROW_BIN_LOCATOR}
    Log    First row bin: ${TOP_BIN}
    Sleep    2s

    ${BIN_MOVED}=    Run Keyword And Return Status    Should Be Equal    ${TOP_BIN}    ${BIN_NAME}

    Run Keyword If    ${BIN_MOVED}  
    ...    Log    The bin that was deleted successfully moved to the top of the View Menu list..  
    ...    ELSE  
    ...    Fail    The bin that was deleted did not move to the top of the View Menu list!
    
The User verifies that the delete request was routed to the correct user role (approver)
    [Arguments]    ${BIN_NAME}
    ${bin_number_id}=     Get Bin Id       ${BIN_NAME}
    Log    ${database_bin_id}

    ${get_routed_user_role}=    Retrieve Delete Request Routing Details    ${database_bin_id}
    Log    ${database_user_role}

    Run Keyword If    '${database_user_role}' == '${BIN_TABLES_APPROVER_USER}'    Log    Delete request for bin: ${BIN_NAME} was assigned to the correct user:${BIN_TABLES_APPROVER_USER}  
    ...     ELSE    Fail    Bin Deletion request was instead routed to: ${database_user_role} instead of: ${BIN_TABLES_APPROVER_USER}.
 

The user verifies that no past dates are allowed for Action Date
    Click On Action Date Icon
    Wait Until Element Is Visible    ${GREYED_OUT_PAST_DATES}    timeout=5s

    #Get all past disabled date elements
    ${DISABLED_PAST_DATES}=    Get WebElements    ${GREYED_OUT_PAST_DATES}

    #Ensure at least one past date is found
    Should Not Be Empty    ${DISABLED_PAST_DATES}    No past dates are found, check locator!

    #Create a list to store all non-permitted dates
    ${NOT_PERMITTED_DATES}=    Create List

    #Verify each past date is not clickable (either by disabled attribute or CSS)
    FOR    ${date_element}    IN    @{DISABLED_PAST_DATES}
        ${date_text}=    Get Text    ${date_element}
    
        #Check for aria-disabled attribute
        ${is_disabled}=    Get Element Attribute    ${date_element}    aria-disabled

        #If aria-disabled is "true", it's confirmed as disabled
        IF    '${is_disabled}' == 'true'
            Log    Verified disabled past date (by aria-disabled): ${date_text}
        ELSE
            #If aria-disabled is not "true", check for class name
            ${element_class}=    Get Element Attribute    ${date_element}    class
            Should Contain    ${element_class}    mat-calendar-body-disabled    The past date is not disabled: ${date_text}
            Log    Verified disabled past date (by class): ${date_text}
        END

        #Append the disabled date to the list
        Append To List    ${NOT_PERMITTED_DATES}    ${date_text}
    END

    #Log all disabled dates in one message
    Log    Dates not available for selection: ${NOT_PERMITTED_DATES}

    #Log current date for reference
    ${CURRENT_DATE}=    Get Current Date    result_format=%Y-%m-%d
    Log    User is not able to select an Action Date in the past! Current Date: ${CURRENT_DATE}

The user verifies that only current and future dates can be selected
    Click On Action Date Icon
    Wait Until Element Is Visible    ${CALENDAR_DATES}    timeout=5s

    #Get all date elements
    ${ALL_DATES}=    Get WebElements    ${CALENDAR_DATES}

    #Ensure at least one date is found
    Should Not Be Empty    ${ALL_DATES}    No dates are found, check locator!

    #Create a list to store enabled dates
    ${SELECTABLE_DATES}=    Create List

    #Verify each date is selectable (not disabled)
    FOR    ${date_element}    IN    @{ALL_DATES}
        ${date_text}=    Get Text    ${date_element}
    
        #Check for aria-disabled attribute
        ${is_disabled}=    Get Element Attribute    ${date_element}    aria-disabled

        #If aria-disabled is None or false, check for class attribute
        IF    '${is_disabled}' == 'true'
            Log    Skipping past disabled date: ${date_text}
        ELSE
            ${element_class}=    Get Element Attribute    ${date_element}    class

            #Ensure the date does not have the 'mat-calendar-body-disabled' class
            Should Not Contain    ${element_class}    mat-calendar-body-disabled    Date should be selectable but is disabled: ${date_text}

            Log    Verified selectable date: ${date_text}
            Append To List    ${SELECTABLE_DATES}    ${date_text}
        END
    END

    #Log all selectable dates
    Log    Selectable dates (Current & Future): ${SELECTABLE_DATES}

    #Log current date 
    ${CURRENT_DATE}=    Get Current Date    result_format=%Y-%m-%d
    Log    User is able to select an Action Date from today onwards! Current Date: ${CURRENT_DATE}