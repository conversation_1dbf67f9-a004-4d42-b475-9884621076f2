<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="******** 14:50:43.826" rpa="false" schemaversion="4">
<suite id="s1" name="TC 01 POST APPROVAL CONTROLLER" source="C:\development\future-fit-architecture-portal-docker\tests\Controllers\Approval\TC_01_POST_APPROVAL_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:44.549" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value 'Yaash.<PERSON>@absa.africa'.</msg>
<status status="PASS" starttime="******** 14:50:44.549" endtime="******** 14:50:44.549"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:44.549" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="******** 14:50:44.549" endtime="******** 14:50:44.549"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:44.549" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 14:50:44.549" endtime="******** 14:50:44.549"/>
</kw>
<status status="PASS" starttime="******** 14:50:44.549" endtime="******** 14:50:44.549"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Approve Marketing Campaign with a Business Approver" line="39">
<kw name="Approve marketing campaign">
<arg>Approves a Marketing Campaign with a Business Approver</arg>
<arg>*********</arg>
<arg>Approval</arg>
<arg>APC_API_DEV_BASE_URL</arg>
<arg>Approval</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>campaignId= 14383</arg>
<arg>approvalTime=0</arg>
<arg>approvedBy= Yaash Ramsahar (ZA)</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 14:50:44.549" level="INFO">Set test documentation to:
Approves a Marketing Campaign with a Business Approver</msg>
<status status="PASS" starttime="******** 14:50:44.549" endtime="******** 14:50:44.549"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:44.555" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 14:50:44.549" endtime="******** 14:50:44.555"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 14:50:44.555" endtime="******** 14:50:44.555"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:44.555" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 14:50:44.555" endtime="******** 14:50:44.555"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:44.555" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 14:50:44.555" endtime="******** 14:50:44.555"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:44.555" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 14:50:44.555" endtime="******** 14:50:44.555"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 14:50:44.587" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 14:50:44.555" endtime="******** 14:50:44.587"/>
</kw>
<status status="PASS" starttime="******** 14:50:44.555" endtime="******** 14:50:44.587"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 14:50:44.587" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 14:50:44.587" level="INFO">${base_url} = https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 14:50:44.587" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Session Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:44.587" level="INFO">'Session Created!'</msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:44.587" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:44.587" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:44.587" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.587"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 14:50:44.601" level="INFO">${payload} = {'campaignId': 14383, 'approvalTime': '2024-05-10T14:50:44.555Z', 'approvedBy': 'Yaash Ramsahar (ZA)'}</msg>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:44.601"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${payload}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:44.601" level="INFO">{'campaignId': 14383, 'approvalTime': '2024-05-10T14:50:44.555Z', 'approvedBy': 'Yaash Ramsahar (ZA)'}</msg>
<status status="PASS" starttime="******** 14:50:44.601" endtime="******** 14:50:44.601"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 14:50:44.607" level="INFO">${end_point} = /Approval</msg>
<status status="PASS" starttime="******** 14:50:44.607" endtime="******** 14:50:44.607"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 14:50:44.607" endtime="******** 14:50:44.607"/>
</kw>
<status status="NOT RUN" starttime="******** 14:50:44.607" endtime="******** 14:50:44.607"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 14:50:44.607" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 14:50:44.607" endtime="******** 14:50:44.607"/>
</kw>
<status status="PASS" starttime="******** 14:50:44.607" endtime="******** 14:50:44.607"/>
</branch>
<status status="PASS" starttime="******** 14:50:44.607" endtime="******** 14:50:44.607"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 14:50:46.367" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 path_url=/Approval 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ip4hZGoWCB1-rApXxHwV0iG9rrItK10oE5NX-a_UcomqDkcsQYCqSCQxOMxDDiHbb1dJZ8qmD0hpwaHkOgwDs1JIt10HWlRz7pbsR4aXbwIRmq4mc7VCraR4U-Qos9JUBLGENAnjdH0dAXOsd8A5I8WNfMLtCmL_-kbddWr0MXMkUtBf4VlrOWFnMarNcPYcq5EjXQgUesio9yBbuFCCNs5Z3QSlVmCp2nh79nxDacnvvQuR6nbFm6Vx2L2qEnB6Vntx9lUg8CE1vYdnkFmQ2V4p6xqgjJtKDFDDLcarzcrGzihQ_XbsiJmWVH4Aj_ePrlpKEPqdCitlTprNfd46dw', 'Content-Length': '102'} 
 body=b'{"campaignId": 14383, "approvalTime": "2024-05-10T14:50:44.555Z", "approvedBy": "Yaash Ramsahar (ZA)"}' 
 </msg>
<msg timestamp="******** 14:50:46.367" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 status=405, reason=Method Not Allowed 
 headers={'Date': 'Fri, 10 May 2024 12:50:45 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=None 
 </msg>
<msg timestamp="******** 14:50:46.367" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 14:50:46.375" level="INFO">${response} = &lt;Response [405]&gt;</msg>
<status status="PASS" starttime="******** 14:50:44.607" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:46.375" level="INFO"/>
<status status="PASS" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:46.375" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '405'.</msg>
<status status="PASS" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:46.375" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Method Not Allowed'.</msg>
<status status="PASS" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 14:50:46.375" level="INFO">${response.content} = </msg>
<status status="PASS" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<status status="PASS" starttime="******** 14:50:44.587" endtime="******** 14:50:46.375"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 14:50:46.375" level="INFO">${returned_status_code} = 405</msg>
<status status="PASS" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:46.375" level="INFO">Response Status Code : 405</msg>
<status status="PASS" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<msg timestamp="******** 14:50:46.375" level="FAIL">Url: https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval Expected status: 405 != 200</msg>
<status status="FAIL" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="NOT RUN" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<status status="FAIL" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<status status="NOT RUN" starttime="******** 14:50:46.375" endtime="******** 14:50:46.375"/>
</kw>
<status status="FAIL" starttime="******** 14:50:44.549" endtime="******** 14:50:46.383"/>
</kw>
<doc>Approves a Marketing Campaign with a Business Approver</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="******** 14:50:44.549" endtime="******** 14:50:46.383">Url: https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval Expected status: 405 != 200</status>
</test>
<test id="s1-t2" name="FFT - Contollera - Approve Marketing Campaign with a Business User" line="40">
<kw name="Approve marketing campaign">
<arg>Approves a Marketing Campaign with a Business User</arg>
<arg>*********</arg>
<arg>Approval</arg>
<arg>APC_API_DEV_BASE_URL</arg>
<arg>Approval</arg>
<arg>401</arg>
<arg>Unauthorized</arg>
<arg>campaignId= 14210</arg>
<arg>approvalTime=0</arg>
<arg>approvedBy= Thabo Benjamin Setuke (ZA)</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 14:50:49.034" level="INFO">Set test documentation to:
Approves a Marketing Campaign with a Business User</msg>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.034"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:49.034" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.034"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.034"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:49.034" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.034"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:49.034" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.034"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:49.034" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.034"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 14:50:49.076" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.076"/>
</kw>
<status status="PASS" starttime="******** 14:50:49.034" endtime="******** 14:50:49.076"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 14:50:49.083" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 14:50:49.083" level="INFO">${base_url} = https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 14:50:49.076" endtime="******** 14:50:49.083"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 14:50:49.083" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.083"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.083"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Session Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:49.083" level="INFO">'Session Created!'</msg>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.083"/>
</kw>
<status status="PASS" starttime="******** 14:50:49.076" endtime="******** 14:50:49.083"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:49.083" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.083"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:49.083" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.083"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 14:50:49.083" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.083"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 14:50:49.091" level="INFO">${payload} = {'campaignId': 14210, 'approvalTime': '2024-05-10T14:50:49.058Z', 'approvedBy': 'Thabo Benjamin Setuke (ZA)'}</msg>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.091"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${payload}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:49.091" level="INFO">{'campaignId': 14210, 'approvalTime': '2024-05-10T14:50:49.058Z', 'approvedBy': 'Thabo Benjamin Setuke (ZA)'}</msg>
<status status="PASS" starttime="******** 14:50:49.091" endtime="******** 14:50:49.091"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 14:50:49.099" level="INFO">${end_point} = /Approval</msg>
<status status="PASS" starttime="******** 14:50:49.091" endtime="******** 14:50:49.099"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<msg timestamp="******** 14:50:49.099" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthY...</msg>
<status status="PASS" starttime="******** 14:50:49.099" endtime="******** 14:50:49.099"/>
</kw>
<status status="PASS" starttime="******** 14:50:49.099" endtime="******** 14:50:49.099"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 14:50:49.099" endtime="******** 14:50:49.099"/>
</kw>
<status status="NOT RUN" starttime="******** 14:50:49.099" endtime="******** 14:50:49.099"/>
</branch>
<status status="PASS" starttime="******** 14:50:49.099" endtime="******** 14:50:49.099"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 14:50:49.701" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 path_url=/Approval 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyIsImtpZCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FI26FGo9WE2UvoEncNGM1-63BboD54HmUGkgw_UF2TVGc8-C_0Wziv0KXECaaU-ExQNli2I5ZD4qgamPXa_daS9DVUmkurKQuRCSv_FV811I4ZkDCCYxVyl4G_U2Cbp82Wx9X7Xojur6lOkmu8Mo7F2YZ3gENVyF1httwzYPHFJ3qFJ6jQD7UtNDrG3D5IYZQbA06dRtu0Oo6AQq54EYMplf1SvpX0_nA6trvVsF6V9U09lbtYp6vjAYzebkWtBrNkNhojhVvZeKsgPruGCoPjPLkswCwb4dlKTC2mBeCapiHc3boy7RH0-tvLbnExqqGcW377ah9873_lUqRIBc4g', 'Content-Length': '109'} 
 body=b'{"campaignId": 14210, "approvalTime": "2024-05-10T14:50:49.058Z", "approvedBy": "Thabo Benjamin Setuke (ZA)"}' 
 </msg>
<msg timestamp="******** 14:50:49.701" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 status=405, reason=Method Not Allowed 
 headers={'Date': 'Fri, 10 May 2024 12:50:49 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'Allow': 'GET', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=None 
 </msg>
<msg timestamp="******** 14:50:49.701" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 14:50:49.701" level="INFO">${response} = &lt;Response [405]&gt;</msg>
<status status="PASS" starttime="******** 14:50:49.099" endtime="******** 14:50:49.701"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:49.717" level="INFO"/>
<status status="PASS" starttime="******** 14:50:49.701" endtime="******** 14:50:49.717"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:49.717" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '405'.</msg>
<status status="PASS" starttime="******** 14:50:49.717" endtime="******** 14:50:49.717"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 14:50:49.717" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Method Not Allowed'.</msg>
<status status="PASS" starttime="******** 14:50:49.717" endtime="******** 14:50:49.717"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 14:50:49.717" level="INFO">${response.content} = </msg>
<status status="PASS" starttime="******** 14:50:49.717" endtime="******** 14:50:49.717"/>
</kw>
<status status="PASS" starttime="******** 14:50:49.083" endtime="******** 14:50:49.717"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 14:50:49.717" level="INFO">${returned_status_code} = 405</msg>
<status status="PASS" starttime="******** 14:50:49.717" endtime="******** 14:50:49.717"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 14:50:49.717" level="INFO">Response Status Code : 405</msg>
<status status="PASS" starttime="******** 14:50:49.717" endtime="******** 14:50:49.717"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<msg timestamp="******** 14:50:49.717" level="FAIL">Url: https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval Expected status: 405 != 401</msg>
<status status="FAIL" starttime="******** 14:50:49.717" endtime="******** 14:50:49.717"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 14:50:49.717" endtime="******** 14:50:49.725"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 14:50:49.725" endtime="******** 14:50:49.725"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="NOT RUN" starttime="******** 14:50:49.725" endtime="******** 14:50:49.725"/>
</kw>
<status status="FAIL" starttime="******** 14:50:49.717" endtime="******** 14:50:49.725"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<status status="NOT RUN" starttime="******** 14:50:49.725" endtime="******** 14:50:49.725"/>
</kw>
<status status="FAIL" starttime="******** 14:50:49.034" endtime="******** 14:50:49.725"/>
</kw>
<doc>Approves a Marketing Campaign with a Business User</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="******** 14:50:49.034" endtime="******** 14:50:49.725">Url: https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval Expected status: 405 != 401</status>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="FAIL" starttime="******** 14:50:44.001" endtime="******** 14:50:50.942"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="2" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="2" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="0" fail="2" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="0" fail="2" skip="0" id="s1" name="TC 01 POST APPROVAL CONTROLLER">TC 01 POST APPROVAL CONTROLLER</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
