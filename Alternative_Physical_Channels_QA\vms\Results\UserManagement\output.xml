<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-18T13:35:17.803214" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-228_Validate Search - Model Coloumn - on ATM Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:38.103350" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T13:35:38.103350" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.102348" elapsed="0.002001"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:38.105863" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T13:35:38.105863" elapsed="0.001015"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.104349" elapsed="0.002529"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:38.107881" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T13:35:38.107881" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.106878" elapsed="0.001003"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.109901" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T13:35:38.108896" elapsed="0.001005"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.108896" elapsed="0.001005"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:38.113895" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T13:35:38.112896" elapsed="0.000999"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.110898" elapsed="0.003999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:38.117427" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-18T13:35:38.117427" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.115897" elapsed="0.003112"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.120957" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T13:35:38.119958" elapsed="0.000999"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.119958" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.123957" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T13:35:38.122955" elapsed="0.001002"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.121956" elapsed="0.002001"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-18T13:35:38.125957" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:38.126468" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:35:38.124954" elapsed="0.001514"/>
</branch>
<status status="PASS" start="2025-06-18T13:35:38.123957" elapsed="0.003522"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.131486" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T13:35:38.130482" elapsed="0.001004"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.127479" elapsed="0.005006"/>
</kw>
<status status="PASS" start="2025-06-18T13:35:38.098350" elapsed="0.035132"/>
</kw>
<test id="s1-t1" name="Validate Search - Model Coloumn- on ATM Details" line="38">
<kw name="Validate Search - Model Coloumn- on ATM Details">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.137005" level="INFO">Set test documentation to:
Validate Search - Model Coloumn- on ATM Details</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-18T13:35:38.137005" elapsed="0.001512"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.141532" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T13:35:38.140532" elapsed="0.001000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:35:38.142534" elapsed="0.006550"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:35:38.149084" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:38.156599" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-18T13:35:38.156599" elapsed="0.001012">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-18T13:35:38.157611" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:35:38.155084" elapsed="0.002527"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:38.158609" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:35:38.158609" elapsed="0.001003"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.161621" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:35:38.159612" elapsed="0.006519"/>
</kw>
<status status="PASS" start="2025-06-18T13:35:38.159612" elapsed="0.006519"/>
</branch>
<status status="PASS" start="2025-06-18T13:35:38.158609" elapsed="0.007522"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-18T13:35:38.261414" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-18T13:35:38.262493" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-18T13:35:38.167148" elapsed="0.095345"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.267977" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T13:35:38.266963" elapsed="0.001014"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.268977" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:35:38.268977" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T13:35:38.267977" elapsed="0.001000"/>
</branch>
<status status="PASS" start="2025-06-18T13:35:38.267977" elapsed="0.001999"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-18T13:35:38.269976" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-18T13:35:38.269976" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:35:38.270977" elapsed="0.002848"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-18T13:35:39.411130" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-18T13:35:40.686861" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-18T13:35:40.687861" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-18T13:35:38.274509" elapsed="2.413352"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.688863" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T13:35:40.688863" elapsed="0.000997"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:40.687861" elapsed="0.001999"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-18T13:35:38.266003" elapsed="2.423857"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:40.690862" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-18T13:35:40.689860" elapsed="0.001002"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T13:35:40.690862" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T13:35:40.690862" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-18T13:35:40.691860" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-18T13:35:40.691860" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-18T13:35:40.692858" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-18T13:35:40.692858" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.693855" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:35:40.693855" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-18T13:35:40.694858" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001F0ABA7B9E0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-18T13:35:40.693855" elapsed="0.001003"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-18T13:35:40.694858" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:40.695867" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:35:40.694858" elapsed="0.001009"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.696379" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001F0ABA854C0&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T13:35:40.696379" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.697391" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:35:40.697391" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.698394" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:35:40.697391" elapsed="0.001003"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.698394" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:35:40.698394" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-18T13:35:40.699396" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-18T13:35:40.698394" elapsed="0.001002"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-18T13:35:40.700391" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-18T13:35:40.701393" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T13:35:40.702392" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-18T13:35:40.700391" elapsed="6.314238"/>
</kw>
<status status="PASS" start="2025-06-18T13:35:40.695867" elapsed="6.318762"/>
</branch>
<status status="PASS" start="2025-06-18T13:35:40.694858" elapsed="6.319771"/>
</if>
<status status="PASS" start="2025-06-18T13:35:40.692858" elapsed="6.321771"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.015628" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.016145" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.016145" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.016145" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:35:47.016145" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-18T13:35:47.016145" elapsed="0.001016"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.017161" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.017161" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.017161" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.018175" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.018175" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.019174" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.019174" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.019174" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.020174" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.020174" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.020174" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.020174" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-18T13:35:47.021176" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:35:47.014629" elapsed="0.006547"/>
</branch>
<status status="PASS" start="2025-06-18T13:35:40.692858" elapsed="6.328318"/>
</if>
<status status="PASS" start="2025-06-18T13:35:38.264428" elapsed="8.756748"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-18T13:35:47.022690" elapsed="0.059966"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-18T13:35:47.093230" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-18T13:35:47.092232" elapsed="2.692713"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:47.083661" elapsed="2.701284"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:35:59.786939" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:35:49.785940" elapsed="10.000999"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T13:35:59.787916" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:35:59.821979" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:35:59.787916" elapsed="0.034063"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T13:35:59.821979" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:09.823434" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:35:59.822985" elapsed="10.000449"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:09.824460" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:36:09.843507" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:09.824460" elapsed="0.019047"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T13:36:09.845171" elapsed="0.000573"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:14.846264" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:36:09.845744" elapsed="5.000520"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:14.847321" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:36:14.862905" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:14.847321" elapsed="0.015584"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T13:36:14.863903" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-18T13:35:47.021176" elapsed="27.842727"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:14.900741" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:14.864904" elapsed="0.035837"/>
</kw>
<msg time="2025-06-18T13:36:14.901739" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:36:14.864904" elapsed="0.036835"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:14.902739" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:14.901739" elapsed="0.146360"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:15.048099" elapsed="0.029633"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:15.077732" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:15.077732" elapsed="0.213008"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:15.291739" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:15.291739" elapsed="0.476581"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:17.769604" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:36:15.769319" elapsed="2.000285"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:17.814861" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:17.771745" elapsed="0.043116"/>
</kw>
<msg time="2025-06-18T13:36:17.814861" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:36:17.770733" elapsed="0.044128"/>
</kw>
<status status="PASS" start="2025-06-18T13:36:14.901739" elapsed="2.913122"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:17.815861" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:17.814861" elapsed="0.157900"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:17.972761" elapsed="0.036119"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:18.009879" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:18.008880" elapsed="0.170747"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:18.180640" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:18.180640" elapsed="0.147958"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:20.329003" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:36:18.328598" elapsed="2.000405"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:24.039535" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-948.png"&gt;&lt;img src="selenium-screenshot-948.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T13:36:24.040540" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-18T13:36:20.330512" elapsed="3.718194">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-18T13:36:24.048706" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:36:20.329003" elapsed="3.719703"/>
</kw>
<status status="PASS" start="2025-06-18T13:36:17.814861" elapsed="6.233845"/>
</iter>
<status status="PASS" start="2025-06-18T13:36:14.901739" elapsed="9.146967"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:24.049703" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:24.436525" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-949.png"&gt;&lt;img src="selenium-screenshot-949.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-18T13:36:24.049703" elapsed="0.386822"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-18T13:35:38.153086" elapsed="46.283439"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-18T13:35:38.151088" elapsed="46.285437"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-18T13:35:38.139533" elapsed="46.298008"/>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:24.438552" elapsed="0.043538"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:24.483092" level="INFO">Clicking element 'xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']'.</msg>
<arg>xpath=//a[@class='nav-link' and @href='ATMDetails.aspx']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:24.482090" elapsed="6.431804"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked ATM Details</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:30.913894" elapsed="0.001002"/>
</kw>
<status status="PASS" start="2025-06-18T13:36:24.437541" elapsed="6.477355"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//table[@class='table gs-table']</arg>
<arg>timeout=15s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:30.915893" elapsed="0.053057"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//thead[@class='gs-table-head']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:30.969950" elapsed="0.041974"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.150594" level="INFO">Current page contains text 'ATM Details'.</msg>
<arg>ATM Details</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.011924" elapsed="0.138670"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//span[contains(text(), 'ATM Number')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:31.150594" elapsed="0.045170"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.326620" level="INFO">Current page contains text 'ATM Number'.</msg>
<arg>ATM Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.196277" elapsed="0.130343"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.443924" level="INFO">Current page contains text 'Serial Number'.</msg>
<arg>Serial Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.326620" elapsed="0.117304"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.574596" level="INFO">Current page contains text 'ATM Branch'.</msg>
<arg>ATM Branch</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.443924" elapsed="0.130672"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.692419" level="INFO">Current page contains text 'Phone Number'.</msg>
<arg>Phone Number</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.576939" elapsed="0.115480"/>
</kw>
<msg time="2025-06-18T13:36:31.692419" level="INFO">${phone_present} = True</msg>
<var>${phone_present}</var>
<arg>Page Should Contain</arg>
<arg>Phone Number</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:36:31.575599" elapsed="0.116820"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.828720" level="INFO">Current page contains text 'Model'.</msg>
<arg>Model</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.693420" elapsed="0.136304"/>
</kw>
<msg time="2025-06-18T13:36:31.829724" level="INFO">${model_present} = True</msg>
<var>${model_present}</var>
<arg>Page Should Contain</arg>
<arg>Model</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:36:31.692419" elapsed="0.137305"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.953104" level="INFO">Current page contains text 'Institution'.</msg>
<arg>Institution</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.829724" elapsed="0.123380"/>
</kw>
<msg time="2025-06-18T13:36:31.954124" level="INFO">${institution_present} = True</msg>
<var>${institution_present}</var>
<arg>Page Should Contain</arg>
<arg>Institution</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-18T13:36:31.829724" elapsed="0.124400"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has landed on the ATM Details Page</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:31.954124" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-18T13:36:30.915893" elapsed="1.038231"/>
</kw>
<kw name="Then The user searches FrontEnd for Existing ATM" owner="ATMDetails">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:31.955688" level="INFO">Clicking element 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:31.955688" elapsed="0.168275"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has clicked Search Element</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:32.124960" elapsed="0.000999"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:37.126824" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:36:32.126473" elapsed="5.000904"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:37.129393" level="INFO">Typing text 'NCR 6634 Recycling' into text field 'xpath=//*[@id="searchField"]'.</msg>
<arg>${SEARCH_FIELD}</arg>
<arg>${SEARCH_KEY}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:37.128413" elapsed="0.435930"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user has input Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:37.564343" elapsed="0.001512"/>
</kw>
<kw name="Wait Until Page Contains" owner="SeleniumLibrary">
<arg>${SEARCH_KEY}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" start="2025-06-18T13:36:37.567408" elapsed="0.092241"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>-------------------------- The user has waited and found the Search Key</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:37.660713" elapsed="0.001000"/>
</kw>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-18T13:36:31.954124" elapsed="5.708483"/>
</kw>
<kw name="Then The user verifies that searched key appears in the the correct Column" owner="ATMDetails">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:37.664605" elapsed="0.045471"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Column Element Has Been Found: ${COLUMN}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:37.711080" elapsed="0.000895"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:37.796535" level="INFO">${header_element_text} = Model</msg>
<var>${header_element_text}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span[contains(text(), '${COLUMN}')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:37.711975" elapsed="0.084560"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-18T13:36:37.799536" level="INFO">${cleaned_header_element_text} = Model</msg>
<var>${cleaned_header_element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-18T13:36:37.797538" elapsed="0.001998"/>
</kw>
<kw name="Replace String" owner="String">
<msg time="2025-06-18T13:36:37.803535" level="INFO">${cleaned_h_element_text} = Model</msg>
<var>${cleaned_h_element_text}</var>
<arg>${cleaned_header_element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="PASS" start="2025-06-18T13:36:37.799536" elapsed="0.005112"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Header Text: ${cleaned_h_element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:37.805675" elapsed="0.000618"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:36:37.808359" level="INFO">${index} = None</msg>
<var>${index}</var>
<arg>${None}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:36:37.808359" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:37.809416" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:36:37.892182" level="INFO">${num_headers} = 12</msg>
<var>${num_headers}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th/span</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:37.809416" elapsed="0.082766"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Current Index: ${i}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:37.893182" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:36:37.894184" level="INFO">${header_xpath} = xpath=//*[@id="root"]/div/table/thead/tr/th[1]/span</msg>
<var>${header_xpath}</var>
<arg>xpath=//*[@id="root"]/div/table/thead/tr/th[${i + 1}]/span</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:36:37.893182" elapsed="0.001002"/>
</kw>
<kw name="Run Keyword And Return" owner="BuiltIn">
<kw name="Get Text With Retry" owner="ATMDetails">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-18T13:36:37.895694" level="INFO">${max_retries} = 3</msg>
<var>${max_retries}</var>
<arg>3</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-18T13:36:37.895694" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:38.226236" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-950.png"&gt;&lt;img src="selenium-screenshot-950.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-18T13:36:38.226236" level="FAIL">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</msg>
<arg>${xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="FAIL" start="2025-06-18T13:36:37.897706" elapsed="0.389252">StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
	GetHandleVerifier [0x00007FF7C3F202E5+25029]
	(No symbol) [0x00007FF7C3E752F0]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C419D73A+1947706]
	(No symbol) [0x00007FF7C3C5230C]
	(No symbol) [0x00007FF7C3C54B9C]
	(No symbol) [0x00007FF7C3C54C6F]
	(No symbol) [0x00007FF7C3C93876]
	(No symbol) [0x00007FF7C3CB908A]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3C8E01D]
	(No symbol) [0x00007FF7C3CB9350]
	(No symbol) [0x00007FF7C3C8E15D]
	(No symbol) [0x00007FF7C3CD6698]
	(No symbol) [0x00007FF7C3CB8DF3]
	(No symbol) [0x00007FF7C3C8D6A6]
	(No symbol) [0x00007FF7C3C8CBB3]
	(No symbol) [0x00007FF7C3C8D4D3]
	(No symbol) [0x00007FF7C3D8638D]
	(No symbol) [0x00007FF7C3D93F2F]
	(No symbol) [0x00007FF7C3D8C91F]
	Microsoft::Applications::Events::EventProperty::to_string [0x00007FF7C40076EA+284650]
	(No symbol) [0x00007FF7C3E82C81]
	(No symbol) [0x00007FF7C3E7B724]
	(No symbol) [0x00007FF7C3E7B873]
	(No symbol) [0x00007FF7C3E6D4B6]
	BaseThreadInitThunk [0x00007FF935DE259D+29]
	RtlUserThreadStart [0x00007FF936BAAF58+40]
</status>
</kw>
<msg time="2025-06-18T13:36:38.286958" level="INFO">${status} = FAIL</msg>
<msg time="2025-06-18T13:36:38.286958" level="INFO">${text} = StaleElementReferenceException: Message: stale element reference: stale element not found in the current frame
  (Session info: MicrosoftEdge=137.0.3296.83); For documentation on this error, please vi...</msg>
<var>${status}</var>
<var>${text}</var>
<arg>Get Text</arg>
<arg>${xpath}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-06-18T13:36:37.896706" elapsed="0.391248"/>
</kw>
<if>
<branch type="IF" condition="&quot;${status}&quot; == &quot;PASS&quot;">
<return>
<value>${text}</value>
<status status="NOT RUN" start="2025-06-18T13:36:38.287954" elapsed="0.000000"/>
</return>
<status status="NOT RUN" start="2025-06-18T13:36:38.287954" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-18T13:36:38.287954" elapsed="0.000000"/>
</if>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Retry ${retry + 1} for getting text from ${xpath}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:38.288956" elapsed="0.001000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:39.290246" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:36:38.289956" elapsed="1.000290"/>
</kw>
<var name="${retry}">0</var>
<status status="PASS" start="2025-06-18T13:36:37.896706" elapsed="1.393540"/>
</iter>
<iter>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Get Text" owner="SeleniumLibrary">
<arg>${xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:39.292748" elapsed="0.051585"/>
</kw>
<msg time="2025-06-18T13:36:39.344333" level="INFO">${status} = PASS</msg>
<msg time="2025-06-18T13:36:39.344333" level="INFO">${text} = ATM Number</msg>
<var>${status}</var>
<var>${text}</var>
<arg>Get Text</arg>
<arg>${xpath}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-06-18T13:36:39.291760" elapsed="0.052573"/>
</kw>
<if>
<branch type="IF" condition="&quot;${status}&quot; == &quot;PASS&quot;">
<return>
<value>${text}</value>
<status status="PASS" start="2025-06-18T13:36:39.345846" elapsed="0.000000"/>
</return>
<status status="PASS" start="2025-06-18T13:36:39.344333" elapsed="0.001513"/>
</branch>
<status status="PASS" start="2025-06-18T13:36:39.344333" elapsed="0.001513"/>
</if>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Retry ${retry + 1} for getting text from ${xpath}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.345846" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.346862" elapsed="0.000000"/>
</kw>
<var name="${retry}">1</var>
<status status="PASS" start="2025-06-18T13:36:39.290246" elapsed="0.056616"/>
</iter>
<var>${retry}</var>
<value>${max_retries}</value>
<status status="PASS" start="2025-06-18T13:36:37.895694" elapsed="1.451168"/>
</for>
<kw name="Get Text" owner="SeleniumLibrary">
<var>${text}</var>
<arg>${xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.346862" elapsed="0.000000"/>
</kw>
<return>
<value>${text}</value>
<status status="NOT RUN" start="2025-06-18T13:36:39.347876" elapsed="0.000000"/>
</return>
<arg>${header_xpath}</arg>
<doc>Get text from element with retry logic to handle stale element references</doc>
<status status="PASS" start="2025-06-18T13:36:37.895694" elapsed="1.452182"/>
</kw>
<msg time="2025-06-18T13:36:39.347876" level="INFO">Returning from the enclosing user keyword.</msg>
<var>${header_text}</var>
<arg>Get Text With Retry</arg>
<arg>${header_xpath}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-18T13:36:37.894184" elapsed="1.453692"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${cleaned_header_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${header_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.347876" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${cleaned_h_text}</var>
<arg>${cleaned_header_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.348874" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Indexed Header: ${cleaned_h_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.348874" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$cleaned_h_text == $cleaned_h_element_text">
<kw name="Set Variable" owner="BuiltIn">
<var>${index}</var>
<arg>${i}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.348874" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Index was set to: ${index}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.348874" elapsed="0.001000"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.349874" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:36:39.348874" elapsed="0.001000"/>
</branch>
<status status="NOT RUN" start="2025-06-18T13:36:39.348874" elapsed="0.001000"/>
</if>
<var name="${i}">0</var>
<status status="PASS" start="2025-06-18T13:36:37.892182" elapsed="1.457692"/>
</iter>
<var>${i}</var>
<value>${num_headers}</value>
<status status="PASS" start="2025-06-18T13:36:37.892182" elapsed="1.457692"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${index} == ${None}</arg>
<arg>Log To Console</arg>
<arg>--------------------------Column with text '${COLUMN}' not found.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.350877" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//tbody//tr//td[${index} + 1]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.350877" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<var>${num_elements}</var>
<arg>xpath=//tbody//tr//td[${index} + 1]</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.350877" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${found}</var>
<arg>False</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.351872" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Set Variable" owner="BuiltIn">
<var>${element_xpath}</var>
<arg>xpath=//tbody//tr[${e + 1}]//td[${index} + 1]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.353873" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${element_xpath}</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.353873" elapsed="0.000997"/>
</kw>
<kw name="Get Text With Retry" owner="ATMDetails">
<var>${element_text}</var>
<arg>${element_xpath}</arg>
<doc>Get text from element with retry logic to handle stale element references</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.354870" elapsed="0.001036"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Search Result Retrieved: ${element_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.356481" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${element_text}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${element_text}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.356481" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${element_t}</var>
<arg>${element_text}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.357600" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${search_key}</var>
<arg>re.sub(r'[^a-zA-Z0-9\s]', '', '${SEARCH_KEY}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.358597" elapsed="0.000000"/>
</kw>
<kw name="Replace String" owner="String">
<var>${search_k}</var>
<arg>${search_key}</arg>
<arg>${SPACE}</arg>
<arg>${EMPTY}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.359604" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="$element_t == $search_k">
<kw name="Set Variable" owner="BuiltIn">
<var>${found}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.360602" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------- Search Key was found in the correct column.</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.360602" elapsed="0.000998"/>
</kw>
<kw name="Exit For Loop" owner="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.361600" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-18T13:36:39.359604" elapsed="0.001996"/>
</branch>
<status status="NOT RUN" start="2025-06-18T13:36:39.359604" elapsed="0.001996"/>
</if>
<var name="${e}"/>
<status status="NOT RUN" start="2025-06-18T13:36:39.352967" elapsed="0.009524"/>
</iter>
<var>${e}</var>
<value>${num_elements}</value>
<status status="NOT RUN" start="2025-06-18T13:36:39.351872" elapsed="0.010619"/>
</for>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${found}' == 'False'</arg>
<arg>Fail</arg>
<arg>Search Key was NOT found in the correct column.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" start="2025-06-18T13:36:39.363492" elapsed="0.000000"/>
</kw>
<arg>${COLUMN}</arg>
<arg>${SEARCH_KEY}</arg>
<status status="PASS" start="2025-06-18T13:36:37.663609" elapsed="1.699883"/>
</kw>
<arg>Validate Search - Model Coloumn- on ATM Details</arg>
<arg>VMS_UAT</arg>
<arg>NCR 6634 Recycling</arg>
<arg>Model</arg>
<status status="PASS" start="2025-06-18T13:35:38.135992" elapsed="61.228444"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-18T13:36:39.365529" elapsed="0.051570"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-18T13:36:39.418217" elapsed="0.000993"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-18T13:36:39.420099" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-18T13:36:39.419210" elapsed="3.170509"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-18T13:36:45.591696" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-18T13:36:42.589719" elapsed="3.001977"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-18T13:36:45.591696" elapsed="11.241230"/>
</kw>
<status status="PASS" start="2025-06-18T13:36:39.365529" elapsed="17.467397"/>
</kw>
<doc>Validate Search - Model Coloumn- on ATM Details</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="PASS" start="2025-06-18T13:35:38.134480" elapsed="78.698446"/>
</test>
<doc>Verify Search Bar Functionality Search For Existing ATM</doc>
<status status="PASS" start="2025-06-18T13:35:17.949734" elapsed="98.884184"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">ATM DETAILS</stat>
<stat pass="1" fail="0" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="1" fail="0" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-18T13:35:17.938197" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T13:35:35.577180" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 197: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.586134" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 227: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.587754" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 258: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.589282" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 297: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.592296" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 341: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.593298" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 353: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.594295" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 396: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.597819" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 649: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.604825" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 671: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.616911" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 702: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.623440" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 738: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.624442" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 748: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.632991" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 759: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.633994" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 777: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.634994" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 784: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.635993" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 804: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.636506" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 829: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.637521" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 860: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.639522" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 966: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.647583" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1194: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.653600" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1277: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.719000" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 116: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.720000" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 136: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.805964" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:35.808942" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 370: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-18T13:35:38.026982" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-18T13:35:38.082272" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-18T13:35:40.688863" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-18T13:35:40.701393" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-18T13:35:59.787916" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:36:09.824460" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:36:14.847321" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-18T13:36:37.809416" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
