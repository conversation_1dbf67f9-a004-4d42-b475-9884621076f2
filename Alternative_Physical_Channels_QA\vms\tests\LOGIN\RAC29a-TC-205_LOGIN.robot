*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    Login
Suite Setup                                         Set up environment variables
Documentation                                       VMS Login & Logout Positive Test 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/VMSPage/Main.robot

*Variables*


*** Keywords ***
Validates VMS Login & Logout    
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}     
    
    Then The user clicks navigate to main 

    #Then The user logs out of VMS



| *Test Case*                                                     |      *DOCUMENTATION*          | *TEST_ENVIRONMENT*   |
| Valid Login & Logout            | Validates VMS Login & Logout  | Login & Logout                |    VMS_UAT           | 
