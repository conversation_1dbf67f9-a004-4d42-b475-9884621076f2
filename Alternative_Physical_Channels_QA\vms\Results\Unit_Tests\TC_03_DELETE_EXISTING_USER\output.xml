<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="20240905 11:13:02.691" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT">
<suite id="s1-s1" name="TC 01 ADD NEW USER" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_01_ADD_NEW_USER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:13:03.649" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.649"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:13:03.649" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Users\<USER>\source\repos\vms\data\TC_03_DELETE_EXISTING_USER.xml'.</msg>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.649"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:13:03.649" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.649"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:13:03.649" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.649"/>
</kw>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.649"/>
</kw>
<test id="s1-s1-t1" name="Create a VMS user with a 'Browse' Role Test Case" line="37">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'Browse' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0540to</arg>
<arg>Automation User Browser</arg>
<arg>Browse</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:13:03.649" level="INFO">Set test documentation to:
Create a VMS user with 'Browse' Role</msg>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.649"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:13:03.761" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:13:03.649" endtime="20240905 11:13:03.761"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:03.761"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:03.761"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:13:03.761" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:03.761"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240905 11:13:03.761" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:03.761"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:03.761"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240905 11:13:03.792" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240905 11:13:04.282" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240905 11:13:04.282" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240905 11:13:04.282" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${browser_name} = CHROME</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</branch>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</if>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001A7176AA5A0&gt;</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<status status="NOT RUN" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</branch>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\User...</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:04.282"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20240905 11:13:04.282" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:06.770"/>
</kw>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:06.771"/>
</branch>
<status status="PASS" starttime="20240905 11:13:04.282" endtime="20240905 11:13:06.771"/>
</if>
<status status="PASS" starttime="20240905 11:13:03.761" endtime="20240905 11:13:06.771"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20240905 11:13:06.782" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg timestamp="20240905 11:13:06.782" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<status status="PASS" starttime="20240905 11:13:06.771" endtime="20240905 11:13:06.782"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20240905 11:13:06.784" endtime="20240905 11:13:06.793"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20240905 11:13:06.795" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<status status="PASS" starttime="20240905 11:13:06.795" endtime="20240905 11:13:18.818"/>
</kw>
<status status="PASS" starttime="20240905 11:13:06.793" endtime="20240905 11:13:18.819"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:13:20.821" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240905 11:13:18.819" endtime="20240905 11:13:20.821"/>
</kw>
<status status="PASS" starttime="20240905 11:13:06.771" endtime="20240905 11:13:20.821"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240905 11:13:20.928" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240905 11:13:20.821" endtime="20240905 11:13:20.928"/>
</kw>
<msg timestamp="20240905 11:13:20.928" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240905 11:13:20.821" endtime="20240905 11:13:20.928"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:20.928" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240905 11:13:20.928" endtime="20240905 11:13:21.086"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240905 11:13:21.087" endtime="20240905 11:13:21.115"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:21.117" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240905 11:13:21.115" endtime="20240905 11:13:21.213"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:21.213" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240905 11:13:21.213" endtime="20240905 11:13:21.642"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:13:26.642" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="20240905 11:13:21.642" endtime="20240905 11:13:26.642"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240905 11:13:26.702" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240905 11:13:26.642" endtime="20240905 11:13:26.702"/>
</kw>
<msg timestamp="20240905 11:13:26.702" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240905 11:13:26.642" endtime="20240905 11:13:26.702"/>
</kw>
<status status="PASS" starttime="20240905 11:13:20.928" endtime="20240905 11:13:26.702"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:26.702" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240905 11:13:26.702" endtime="20240905 11:13:26.840"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240905 11:13:26.840" endtime="20240905 11:13:26.866"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:26.866" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240905 11:13:26.866" endtime="20240905 11:13:26.977"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:26.977" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240905 11:13:26.977" endtime="20240905 11:13:30.024"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:13:35.024" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="20240905 11:13:30.024" endtime="20240905 11:13:35.024"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240905 11:13:35.213" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-32.png"&gt;&lt;img src="selenium-screenshot-32.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="20240905 11:13:35.213" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<status status="FAIL" starttime="20240905 11:13:35.024" endtime="20240905 11:13:35.220"/>
</kw>
<msg timestamp="20240905 11:13:35.220" level="INFO">${User_Name_Element_Visible} = False</msg>
<status status="PASS" starttime="20240905 11:13:35.024" endtime="20240905 11:13:35.220"/>
</kw>
<status status="PASS" starttime="20240905 11:13:26.702" endtime="20240905 11:13:35.220"/>
</iter>
<status status="PASS" starttime="20240905 11:13:20.928" endtime="20240905 11:13:35.220"/>
</while>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:13:40.222" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="20240905 11:13:35.222" endtime="20240905 11:13:40.222"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240905 11:13:40.223" endtime="20240905 11:13:40.223"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240905 11:13:40.676" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<msg timestamp="20240905 11:13:40.678" level="FAIL">WebDriverException: Message: unknown error: session deleted because of page crash
from unknown error: cannot determine loading status
from tab crashed
  (Session info: chrome=128.0.6613.119)
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EA73BB1]
	(No symbol) [0x00007FF65EA71AD3]
	(No symbol) [0x00007FF65EA7231F]
	(No symbol) [0x00007FF65EA80C1E]
	(No symbol) [0x00007FF65EA995CD]
	(No symbol) [0x00007FF65EA9F16A]
	(No symbol) [0x00007FF65EA72A6A]
	(No symbol) [0x00007FF65EA99166]
	(No symbol) [0x00007FF65EB264BB]
	(No symbol) [0x00007FF65EB06493]
	(No symbol) [0x00007FF65EAD09B1]
	(No symbol) [0x00007FF65EAD1B11]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EBD5454]
	(No symbol) [0x00007FF65EBD55E0]
	(No symbol) [0x00007FF65EBC4A7F]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<status status="FAIL" starttime="20240905 11:13:40.223" endtime="20240905 11:13:40.683"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:03.761" endtime="20240905 11:13:40.683"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:03.761" endtime="20240905 11:13:40.683"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:03.649" endtime="20240905 11:13:40.683"/>
</kw>
<kw name="When The user navigates to Admin - User Management and Adds a User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:13:40.683" endtime="20240905 11:13:40.683"/>
</kw>
<kw name="Then The created user must be added to the system" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:13:40.683" endtime="20240905 11:13:40.683"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:03.649" endtime="20240905 11:13:40.683"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:13:40.699" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<msg timestamp="20240905 11:13:40.699" level="FAIL">InvalidSessionIdException: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<status status="FAIL" starttime="20240905 11:13:40.683" endtime="20240905 11:13:40.699"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:13:40.699" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:13:40.699" endtime="20240905 11:13:40.699"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:40.715" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:13:40.715" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<msg timestamp="20240905 11:13:40.715" level="FAIL">InvalidSessionIdException: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<status status="FAIL" starttime="20240905 11:13:40.699" endtime="20240905 11:13:40.715"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:13:50.715" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:13:40.715" endtime="20240905 11:13:50.715"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:13:50.715" endtime="20240905 11:13:52.786"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:40.683" endtime="20240905 11:13:52.786">Several failures occurred:

1) InvalidSessionIdException: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
    [ Message content over the limit has been removed. ]

3) InvalidSessionIdException: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]</status>
</kw>
<doc>Create a VMS user with 'Browse' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:13:03.649" endtime="20240905 11:13:52.786">WebDriverException: Message: unknown error: session deleted because of page crash
from unknown error: cannot determine loading status
from tab crashed
  (Session info: chrome=128.0.6613.119)
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EA73BB1]
	(No symbol) [0x00007FF65EA71AD3]
	(No symbol) [0x00007FF65EA7231F]
	(No symbol) [0x00007FF65EA80C1E]
	(No symbol) [0x00007FF65EA995CD]
	(No symbol) [0x00007FF65EA9F16A]
	(No symbol) [0x00007FF65EA72A6A]
	(No symbol) [0x00007FF65EA99166]
	(No symbol) [0x00007FF65EB264BB]
	(No symbol) [0x00007FF65EB06493]
	(No symbol) [0x00007FF65EAD09B1]
	(No symbol) [0x00007FF65EAD1B11]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EBD5454]
	(No symbol) [0x00007FF65EBD55E0]
	(No symbol) [0x00007FF65EBC4A7F]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]


Also teardown failed:
Several failures occurred:

1) InvalidSessionIdException: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
    [ Message content over the limit has been removed. ]

3) InvalidSessionIdException: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]</status>
</test>
<test id="s1-s1-t2" name="Create a VMS user with a 'User' Role Test Case" line="38">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'User' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0541to</arg>
<arg>Automation User</arg>
<arg>User</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:13:52.786" level="INFO">Set test documentation to:
Create a VMS user with 'User' Role</msg>
<status status="PASS" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:13:52.786" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:13:52.786" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="When The user navigates to Admin - User Management and Adds a User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Then The created user must be added to the system" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:13:52.786" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:13:52.786" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:13:52.786" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:13:52.786" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:13:52.786" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:13:52.786" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:13:52.786"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:14:02.787" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:13:52.786" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:14:02.787">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Create a VMS user with 'User' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:13:52.786" endtime="20240905 11:14:02.787">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s1-t3" name="Create a VMS user with a 'Supervisor' Role Test Case" line="39">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'Supervisor' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0542to</arg>
<arg>Automation User Supervisor</arg>
<arg>Supervisor</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:14:02.787" level="INFO">Set test documentation to:
Create a VMS user with 'Supervisor' Role</msg>
<status status="PASS" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:14:02.787" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:02.787" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="When The user navigates to Admin - User Management and Adds a User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Then The created user must be added to the system" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:14:02.787" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:02.787" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:02.787" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:14:02.787" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:14:02.787" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:02.787" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:02.787"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:14:12.787" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:14:02.787" endtime="20240905 11:14:12.787"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:14:12.787" endtime="20240905 11:14:12.787"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:12.787">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Create a VMS user with 'Supervisor' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:14:02.787" endtime="20240905 11:14:12.787">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s1-t4" name="Create a VMS user with a 'Administrator' Role Test Case" line="40">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'Administrator' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0543to</arg>
<arg>Automation User Administrator</arg>
<arg>Administrator</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:14:12.787" level="INFO">Set test documentation to:
Create a VMS user with 'Administrator' Role</msg>
<status status="PASS" starttime="20240905 11:14:12.787" endtime="20240905 11:14:12.787"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:14:12.794" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:14:12.787" endtime="20240905 11:14:12.794"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:12.795" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:12.794" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:12.787" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="When The user navigates to Admin - User Management and Adds a User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="Then The created user must be added to the system" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:12.787" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:14:12.795" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:12.795" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:12.795" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:14:12.795" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:14:12.795" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:12.795" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:12.795" endtime="20240905 11:14:12.795"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:14:22.795" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:14:12.795" endtime="20240905 11:14:22.795"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:14:22.795" endtime="20240905 11:14:22.795"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:12.795" endtime="20240905 11:14:22.795">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Create a VMS user with 'Administrator' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:14:12.787" endtime="20240905 11:14:22.797">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<doc>Add new User to VMS</doc>
<status status="FAIL" starttime="20240905 11:13:02.907" endtime="20240905 11:14:22.797"/>
</suite>
<suite id="s1-s2" name="TC 02 UPDATE EXISTING USER" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_02_UPDATE_EXISTING_USER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Users\<USER>\source\repos\vms\data\TC_03_DELETE_EXISTING_USER.xml'.</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<test id="s1-s2-t1" name="Update the VMS user, change the role to of the user to 'User'." line="37">
<kw name="VMS User Edit">
<arg>Updates a VMS user to have a 'User' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0540to</arg>
<arg>User</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Set test documentation to:
Updates a VMS user to have a 'User' Role</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:22.871" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="And Updates the user's role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Then The user's role must be updated with the expected role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:22.871" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:22.871" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:14:22.871" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:14:22.871" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:22.871" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:22.871"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:14:32.872" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:14:22.871" endtime="20240905 11:14:32.872"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:14:32.872" endtime="20240905 11:14:32.872"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:32.872">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Updates a VMS user to have a 'User' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:14:22.871" endtime="20240905 11:14:32.872">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s2-t2" name="Update the VMS user, change the role to of the user to 'Browse'." line="38">
<kw name="VMS User Edit">
<arg>Updates a VMS user to have a 'Browse' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0541to</arg>
<arg>Browse</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:14:32.874" level="INFO">Set test documentation to:
Updates a VMS user to have a 'Browse' Role</msg>
<status status="PASS" starttime="20240905 11:14:32.874" endtime="20240905 11:14:32.874"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:14:32.874" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:14:32.874" endtime="20240905 11:14:32.874"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:32.874" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:32.874" endtime="20240905 11:14:32.874"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:14:32.874" endtime="20240905 11:14:32.874"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.876"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:32.874" endtime="20240905 11:14:32.876"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.876"/>
</kw>
<kw name="And Updates the user's role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.876"/>
</kw>
<kw name="Then The user's role must be updated with the expected role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.876"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:32.874" endtime="20240905 11:14:32.876"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:14:32.876" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:32.876" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.876"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:32.876" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.876"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:14:32.876" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:14:32.876" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:32.876" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:32.876" endtime="20240905 11:14:32.881"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:14:42.883" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:14:32.881" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:32.876" endtime="20240905 11:14:42.883">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Updates a VMS user to have a 'Browse' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:14:32.874" endtime="20240905 11:14:42.883">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s2-t3" name="Update the VMS user, change the role to of the user to 'Administrator'." line="39">
<kw name="VMS User Edit">
<arg>Updates a VMS user to have a 'Administrator' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0542to</arg>
<arg>Administrator</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:14:42.883" level="INFO">Set test documentation to:
Updates a VMS user to have a 'Administrator' Role</msg>
<status status="PASS" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:14:42.883" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:42.883" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="And Updates the user's role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Then The user's role must be updated with the expected role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:14:42.883" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:42.883" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:42.883" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:14:42.883" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:14:42.883" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:42.883" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:42.883"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:14:52.887" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:14:42.883" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:52.887">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Updates a VMS user to have a 'Administrator' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:14:42.883" endtime="20240905 11:14:52.887">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s2-t4" name="Update the VMS user, change the role to of the user to 'Supervisor'." line="40">
<kw name="VMS User Edit">
<arg>Updates a VMS user to have a 'Supervisor' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0543to</arg>
<arg>Supervisor</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:14:52.887" level="INFO">Set test documentation to:
Updates a VMS user to have a 'Supervisor' Role</msg>
<status status="PASS" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:14:52.887" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:52.887" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="And Updates the user's role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Then The user's role must be updated with the expected role" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:14:52.887" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:52.887" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:14:52.887" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:14:52.887" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:14:52.887" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:14:52.887" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:14:52.887"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:15:02.888" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:14:52.887" endtime="20240905 11:15:02.888"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:15:02.888" endtime="20240905 11:15:02.888"/>
</kw>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:15:02.888">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Updates a VMS user to have a 'Supervisor' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:14:52.887" endtime="20240905 11:15:02.888">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<doc>Add new User to VMS</doc>
<status status="FAIL" starttime="20240905 11:14:22.845" endtime="20240905 11:15:02.888"/>
</suite>
<suite id="s1-s3" name="TC 03 DELETE EXISTING USER" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_03_DELETE_EXISTING_USER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:15:02.921" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20240905 11:15:02.920" endtime="20240905 11:15:02.921"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:15:02.921" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Users\<USER>\source\repos\vms\data\TC_03_DELETE_EXISTING_USER.xml'.</msg>
<status status="PASS" starttime="20240905 11:15:02.921" endtime="20240905 11:15:02.921"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:15:02.921" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20240905 11:15:02.921" endtime="20240905 11:15:02.921"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240905 11:15:02.921" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<status status="PASS" starttime="20240905 11:15:02.921" endtime="20240905 11:15:02.921"/>
</kw>
<status status="PASS" starttime="20240905 11:15:02.920" endtime="20240905 11:15:02.921"/>
</kw>
<test id="s1-s3-t1" name="Delete the VMS user that has a role of 'User'." line="37">
<kw name="VMS User Delete">
<arg>Deletes a VMS user that has a 'User' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0540to</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:15:02.921" level="INFO">Set test documentation to:
Deletes a VMS user that has a 'User' Role</msg>
<status status="PASS" starttime="20240905 11:15:02.921" endtime="20240905 11:15:02.921"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:15:02.923" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:02.923" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:02.921" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="And Deletes the user from the VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Then The user should not be found when you are searching for it on VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:02.921" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:15:02.923" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:02.923" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:02.923" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:15:02.923" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:15:02.923" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:02.923" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:02.923" endtime="20240905 11:15:02.923"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:15:12.928" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:15:02.928" endtime="20240905 11:15:12.928"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:15:12.929" endtime="20240905 11:15:12.929"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:02.923" endtime="20240905 11:15:12.929">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Deletes a VMS user that has a 'User' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:15:02.921" endtime="20240905 11:15:12.930">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s3-t2" name="Delete the VMS user that has a role of 'Browse'." line="38">
<kw name="VMS User Delete">
<arg>Deletes a VMS user that has a 'Browse' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0541to</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:15:12.933" level="INFO">Set test documentation to:
Deletes a VMS user that has a 'Browse' Role</msg>
<status status="PASS" starttime="20240905 11:15:12.933" endtime="20240905 11:15:12.933"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:15:12.934" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:15:12.934" endtime="20240905 11:15:12.934"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:12.934" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:12.934" endtime="20240905 11:15:12.934"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:15:12.934" endtime="20240905 11:15:12.935"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:15:12.935" endtime="20240905 11:15:12.935"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:12.933" endtime="20240905 11:15:12.935"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:15:12.935" endtime="20240905 11:15:12.935"/>
</kw>
<kw name="And Deletes the user from the VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:12.935" endtime="20240905 11:15:12.935"/>
</kw>
<kw name="Then The user should not be found when you are searching for it on VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:12.936" endtime="20240905 11:15:12.936"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:12.933" endtime="20240905 11:15:12.936"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:15:12.937" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:12.937" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:12.936" endtime="20240905 11:15:12.938"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:12.938" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:12.938" endtime="20240905 11:15:12.938"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:15:12.939" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:15:12.939" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:12.939" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:12.938" endtime="20240905 11:15:12.940"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:15:22.941" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:15:12.940" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:12.936" endtime="20240905 11:15:22.941">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Deletes a VMS user that has a 'Browse' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:15:12.932" endtime="20240905 11:15:22.941">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s3-t3" name="Delete the VMS user that has a role of 'Administrator'." line="39">
<kw name="VMS User Delete">
<arg>Deletes a VMS user that has an 'Administrator' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0542to</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:15:22.941" level="INFO">Set test documentation to:
Deletes a VMS user that has an 'Administrator' Role</msg>
<status status="PASS" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:15:22.941" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:22.941" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="And Deletes the user from the VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Then The user should not be found when you are searching for it on VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:15:22.941" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:22.941" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:22.941" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:15:22.941" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:15:22.941" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:22.941" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:22.941"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:15:32.950" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:15:22.941" endtime="20240905 11:15:32.950"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:15:32.950" endtime="20240905 11:15:32.950"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:32.950">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Deletes a VMS user that has an 'Administrator' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:15:22.941" endtime="20240905 11:15:32.951">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-s3-t4" name="Delete the VMS user that has a role of 'Supervisor'." line="40">
<kw name="VMS User Delete">
<arg>Deletes a VMS user that has a 'Supervisor' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0543to</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240905 11:15:32.953" level="INFO">Set test documentation to:
Deletes a VMS user that has a 'Supervisor' Role</msg>
<status status="PASS" starttime="20240905 11:15:32.953" endtime="20240905 11:15:32.953"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240905 11:15:32.954" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240905 11:15:32.954" endtime="20240905 11:15:32.954"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:32.954" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:32.954" endtime="20240905 11:15:32.954"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20240905 11:15:32.956" endtime="20240905 11:15:32.956"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20240905 11:15:32.956" endtime="20240905 11:15:32.956"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:32.953" endtime="20240905 11:15:32.956"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20240905 11:15:32.956" endtime="20240905 11:15:32.956"/>
</kw>
<kw name="And Deletes the user from the VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:32.957" endtime="20240905 11:15:32.957"/>
</kw>
<kw name="Then The user should not be found when you are searching for it on VMS" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20240905 11:15:32.957" endtime="20240905 11:15:32.957"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:32.952" endtime="20240905 11:15:32.957"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20240905 11:15:32.959" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:32.959" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:32.958" endtime="20240905 11:15:32.961"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20240905 11:15:32.961" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20240905 11:15:32.961" endtime="20240905 11:15:32.961"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240905 11:15:32.962" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20240905 11:15:32.962" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20240905 11:15:32.963" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20240905 11:15:32.962" endtime="20240905 11:15:32.965"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240905 11:15:42.972" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="20240905 11:15:32.965" endtime="20240905 11:15:42.972"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240905 11:15:42.972" endtime="20240905 11:15:42.972"/>
</kw>
<status status="FAIL" starttime="20240905 11:15:32.958" endtime="20240905 11:15:42.972">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Deletes a VMS user that has a 'Supervisor' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20240905 11:15:32.952" endtime="20240905 11:15:42.972">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<doc>Add new User to VMS</doc>
<status status="FAIL" starttime="20240905 11:15:02.888" endtime="20240905 11:15:42.972"/>
</suite>
<status status="FAIL" starttime="20240905 11:13:02.892" endtime="20240905 11:15:42.975"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="12" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="12" skip="0">HEALTHCHECK_STATUS</stat>
<stat pass="0" fail="12" skip="0">Login</stat>
<stat pass="0" fail="12" skip="0">VMS_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="0" fail="12" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
<stat pass="0" fail="4" skip="0" id="s1-s1" name="TC 01 ADD NEW USER">Future-Fit Portal.TC 01 ADD NEW USER</stat>
<stat pass="0" fail="4" skip="0" id="s1-s2" name="TC 02 UPDATE EXISTING USER">Future-Fit Portal.TC 02 UPDATE EXISTING USER</stat>
<stat pass="0" fail="4" skip="0" id="s1-s3" name="TC 03 DELETE EXISTING USER">Future-Fit Portal.TC 03 DELETE EXISTING USER</stat>
</suite>
</statistics>
<errors>
<msg timestamp="20240905 11:13:04.282" level="WARN">There was error during termination of process</msg>
<msg timestamp="20240905 11:13:40.676" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<msg timestamp="20240905 11:13:40.699" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<msg timestamp="20240905 11:13:40.715" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x00007FF65EC5B632+29090]
	(No symbol) [0x00007FF65EBCE6E9]
	(No symbol) [0x00007FF65EA8AFF9]
	(No symbol) [0x00007FF65EACFAE4]
	(No symbol) [0x00007FF65EB06599]
	(No symbol) [0x00007FF65EB00111]
	(No symbol) [0x00007FF65EAFF2C1]
	(No symbol) [0x00007FF65EA53D15]
	GetHandleVerifier [0x00007FF65EF7883D+3294125]
	GetHandleVerifier [0x00007FF65EFC4423+3604371]
	GetHandleVerifier [0x00007FF65EFBA2E7+3563095]
	GetHandleVerifier [0x00007FF65ED16F16+797318]
	(No symbol) [0x00007FF65EBD986F]
	(No symbol) [0x00007FF65EA527DA]
	GetHandleVerifier [0x00007FF65F01EE68+3975640]
	BaseThreadInitThunk [0x00007FFE2D01257D+29]
	RtlUserThreadStart [0x00007FFE2E80AF28+40]
</msg>
<msg timestamp="20240905 11:14:22.845" level="ERROR">Calling method 'end_suite' of listener 'C:\Users\<USER>\source\repos\vms\utility\PostExecutionUpdateV2.py' failed: OSError: [WinError 233] No process is on the other end of the pipe</msg>
<msg timestamp="20240905 11:15:02.888" level="ERROR">Calling method 'end_suite' of listener 'C:\Users\<USER>\source\repos\vms\utility\PostExecutionUpdateV2.py' failed: OSError: [WinError 233] No process is on the other end of the pipe</msg>
<msg timestamp="20240905 11:15:42.975" level="ERROR">Calling method 'end_suite' of listener 'C:\Users\<USER>\source\repos\vms\utility\PostExecutionUpdateV2.py' failed: OSError: [WinError 233] No process is on the other end of the pipe</msg>
<msg timestamp="20240905 11:15:42.975" level="ERROR">Calling method 'end_suite' of listener 'C:\Users\<USER>\source\repos\vms\utility\PostExecutionUpdateV2.py' failed: OSError: [WinError 233] No process is on the other end of the pipe</msg>
</errors>
</robot>
