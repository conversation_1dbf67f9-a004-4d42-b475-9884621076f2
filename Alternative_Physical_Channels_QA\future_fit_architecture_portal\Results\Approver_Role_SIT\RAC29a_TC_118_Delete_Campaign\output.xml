<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:17:38.317795" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_118_Delete_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.396978" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:17:39.396978" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.398004" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:17:39.396978" elapsed="0.001026"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.398004" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:17:39.398004" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.398004" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:17:39.398004" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.398004" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:17:39.398004" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:17:39.396978" elapsed="0.001026"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_118_FFT_Approval_Delete_Campaign" line="43">
<kw name="Validating the Deletion function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.398865" level="INFO">Set test documentation to:
Delete Campaign</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:17:39.398865" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.491708" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:17:39.398865" elapsed="0.092843"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:17:39.491708" elapsed="0.000988"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:17:39.492696" elapsed="0.000951"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.493647" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:17:39.493647" elapsed="0.001263"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:17:39.494910" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:17:39.494910" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:17:39.494910" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.494910" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:17:39.494910" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:17:39.494910" elapsed="0.001005"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.524012" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:17:39.829609" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T12:17:39.829609" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:17:39.495915" elapsed="0.333694"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.829609" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:39.829609" elapsed="0.000997"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:17:39.829609" elapsed="0.000997"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:17:39.493647" elapsed="0.336959"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.830606" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:17:39.830606" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:17:39.830606" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:17:39.830606" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:17:39.830606" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:17:39.830606" elapsed="0.001000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:17:39.831606" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.832605" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:17:39.831606" elapsed="0.000999"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.832605" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001B89B8D47A0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:17:39.832605" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:17:39.832605" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:17:39.832605" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:17:39.832605" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:17:39.832605" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.832605" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:17:39.832605" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:17:39.833607" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:17:39.833607" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:39.833607" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:17:39.833607" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:17:39.833607" elapsed="0.001002"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:17:39.834609" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:17:39.834609" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.834609" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:17:39.834609" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:17:39.834609" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:17:39.398865" elapsed="33.452812"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:18:12.851677" elapsed="0.019013"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:12.875397" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.5CD3ADE986C5C5AA5AC7069F77975F7D.e.154")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:12.870690" elapsed="0.004707"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:12.876461" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.5CD3ADE986C5C5AA5AC7069F77975F7D.e.154")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:12.876461" elapsed="0.024258"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:18:12.901732" elapsed="0.007982"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:12.901732" elapsed="0.008924"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:12.916804" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.5CD3ADE986C5C5AA5AC7069F77975F7D.e.155")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:12.910656" elapsed="0.006148"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:17.918099" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:12.916804" elapsed="5.001295"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:18:17.918099" elapsed="0.014155"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:17.933244" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.5CD3ADE986C5C5AA5AC7069F77975F7D.e.155")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:17.932254" elapsed="0.073807"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:18:18.006061" elapsed="0.450128"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:18.006061" elapsed="0.450128"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:18:18.456189" elapsed="0.005115"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:18.456189" elapsed="0.005115"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:18.469360" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:18:18.461304" elapsed="0.008056"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:12.851677" elapsed="5.617683"/>
</kw>
<kw name="And The user clicks the delete icon on a campaign" owner="Approvals">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:23.471008" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:18.470384" elapsed="5.000624"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${first_campaign_id}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:18:23.471008" elapsed="0.018521"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:23.506693" level="INFO">${DELETED_CAMPAIGN_ID} = CNQ012v001Q42024</msg>
<var>${DELETED_CAMPAIGN_ID}</var>
<arg>${first_campaign_id}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:23.489529" elapsed="0.017164"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:18:23.506693" level="INFO">${DELETED_CAMPAIGN_ID} = CNQ012v001Q42024</msg>
<arg>${DELETED_CAMPAIGN_ID}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:18:23.506693" elapsed="0.001028"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:18:23.507721" level="INFO">Campaign ID to be deactivated: CNQ012v001Q42024</msg>
<arg>Campaign ID to be deactivated: ${DELETED_CAMPAIGN_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:18:23.507721" elapsed="0.000000"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${Delete_Campaign_Button}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:18:23.507721" elapsed="0.285337"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:23.793058" level="INFO">Clicking element '//td[@role='cell' and contains(@class, 'mat-column-approve')]//fa-icon[@mattooltip='Delete']'.</msg>
<arg>${Delete_Campaign_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:23.793058" elapsed="0.035093"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:26.828531" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:23.828151" elapsed="3.000380"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:18.470384" elapsed="8.358147"/>
</kw>
<kw name="And The user clicks the deactivate campaign button to confirm deletion" owner="Approvals">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${Deactivate_Campaign_Button}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:18:26.829539" elapsed="0.278651"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:30.108565" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:27.108190" elapsed="3.000375"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:30.109697" level="INFO">Clicking element '//button[contains(@class, 'approve') and contains(., 'Deactivate Campaign')]'.</msg>
<arg>${Deactivate_Campaign_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:30.108565" elapsed="0.034593"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:35.143438" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:30.143158" elapsed="5.000280"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:26.828531" elapsed="8.314907"/>
</kw>
<kw name="And The user navigates back to the Campaign Approvals page" owner="Navigation">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:40.144769" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:35.144547" elapsed="5.000222"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:40.153473" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.5CD3ADE986C5C5AA5AC7069F77975F7D.e.155")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:40.144769" elapsed="0.008704"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:18:40.153473" elapsed="0.014424"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:40.167897" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.5CD3ADE986C5C5AA5AC7069F77975F7D.e.155")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:40.167897" elapsed="0.053184"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:18:40.221081" elapsed="0.440580"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:40.221081" elapsed="0.440580"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:45.661955" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:40.661661" elapsed="5.000534"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:45.672550" level="INFO">Current page contains text 'APPROVALS'.</msg>
<arg>APPROVALS</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:18:45.662195" elapsed="0.010355"/>
</kw>
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:45.673550" level="INFO">Executing JavaScript:
location.reload(true)
Without any arguments.</msg>
<arg>location.reload(true)</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-29T12:18:45.673550" elapsed="0.426282"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:35.143438" elapsed="10.956394"/>
</kw>
<kw name="Then The user verifies that the campaign has been deleted and removed from the front end" owner="Approvals">
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:46.111908" level="INFO">${Search_Bar} = &lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.9FDEABBE4ECEAF822FB9BAF7278E4DD2.e.217")&gt;</msg>
<var>${Search_Bar}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:46.100832" elapsed="0.011076"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:51.112213" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:46.111908" elapsed="5.000305"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:51.112213" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.9FDEABBE4ECEAF822FB9BAF7278E4DD2.e.217")&gt;'.</msg>
<arg>${Search_Bar}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:51.112213" elapsed="0.027261"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:53.141179" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:51.140518" elapsed="2.000661"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:53.141179" level="INFO">Typing text 'CNQ012v001Q42024' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="624903ceada7797e24e6a0caea4df6c3", element="f.C9E002154BCD47976968BC348B222B9D.d.9FDEABBE4ECEAF822FB9BAF7278E4DD2.e.217")&gt;'.</msg>
<arg>${Search_Bar}</arg>
<arg>${DELETED_CAMPAIGN_ID}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:53.141179" elapsed="0.052216"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:55.193965" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:53.193395" elapsed="2.000570"/>
</kw>
<kw name="Wait Until Keyword Succeeds" owner="BuiltIn">
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:55.203416" level="INFO">Element 'xpath=//tr[@role='row' and td[2][normalize-space(text())='CNQ012v001Q42024']]' did not exist.</msg>
<arg>xpath=//tr[@role='row' and td[2][normalize-space(text())='${DELETED_CAMPAIGN_ID}']]</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2024-10-29T12:18:55.193965" elapsed="0.010450"/>
</kw>
<arg>5x</arg>
<arg>2s</arg>
<arg>Element Should Not Be Visible</arg>
<arg>xpath=//tr[@role='row' and td[2][normalize-space(text())='${DELETED_CAMPAIGN_ID}']]</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<status status="PASS" start="2024-10-29T12:18:55.193965" elapsed="0.010450"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:18:55.204415" level="INFO">Campaign successfully deleted. No results found for ID: CNQ012v001Q42024.</msg>
<arg>Campaign successfully deleted. No results found for ID: ${DELETED_CAMPAIGN_ID}.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:18:55.204415" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:46.099832" elapsed="9.104583"/>
</kw>
<arg>Delete Campaign</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:17:39.398865" elapsed="75.805550"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:18:55.205432" elapsed="0.003987"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:55.205432" elapsed="0.003987"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:55.210420" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:18:55.210420" elapsed="0.030417"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:18:58.241382" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:55.240837" elapsed="3.000545"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:18:58.241382" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:18:58.301539" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:18:58.301539" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:18:58.241382" elapsed="0.062482">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:19:00.304379" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:18:58.303864" elapsed="2.000515"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:19:00.304379" elapsed="3.152244"/>
</kw>
<status status="FAIL" start="2024-10-29T12:18:55.210420" elapsed="8.246686">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:18:55.209419" elapsed="8.247687"/>
</kw>
<status status="PASS" start="2024-10-29T12:18:55.204415" elapsed="8.252691"/>
</kw>
<doc>Delete Campaign</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:17:39.398004" elapsed="84.060253"/>
</test>
<doc>Testing Camapaign Deletion</doc>
<status status="PASS" start="2024-10-29T12:17:38.746130" elapsed="84.714897"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:17:38.311768" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_118_Delete_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:17:39.371003" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:17:39.829609" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T12:17:57.755155" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:18:07.767346" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:18:12.776433" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
