import json
from typing import List, Optional

class Person:
    def __init__(self,
                 name: Optional[str] = None,
                 age: Optional[int] = None,
                 email: Optional[str] = None,
                 phone_numbers: Optional[List[str]] = None,
                 addresses: Optional[List[dict]] = None):
        self.name = name
        self.age = age
        self.email = email
        self.phone_numbers = phone_numbers or []
        self.addresses = addresses or []

    def to_json(self) -> str:
        """Convert the Person object to a JSON string."""
        # Serialize only non-None attributes
        data = {
            'name': self.name,
            'age': self.age,
            'email': self.email,
            'phone_numbers': self.phone_numbers if self.phone_numbers else None,
            'addresses': self.addresses if self.addresses else None
        }
        # Filter out keys with None values
        return json.dumps({k: v for k, v in data.items() if v is not None})

    @classmethod
    def from_json(cls, json_string: str):
        """Create a Person object from a JSON string."""
        data = json.loads(json_string)
        return cls(
            name=data.get('name'),
            age=data.get('age'),
            email=data.get('email'),
            phone_numbers=data.get('phone_numbers', []),
            addresses=data.get('addresses', [])
        )

# Example usage
person = Person(
    name="<PERSON>e",
    age=30,
    email="<EMAIL>",
    phone_numbers=["************", "************"],
    addresses=[{"city": "New York", "state": "NY"}, {"city": "Los Angeles", "state": "CA"}]
)

# Convert to JSON
#json_str = person.to_json()
#print(json_str)
# Output: {"name": "John Doe", "age": 30, "email": "<EMAIL>", "phone_numbers": ["************", "************"], "addresses": [{"city": "New York", "state": "NY"}, {"city": "Los Angeles", "state": "CA"}]}

# Create object from JSON
#new_person = Person.from_json(json_str)
#print(new_person.name)           # Output: John Doe
#print(new_person.age)            # Output: 30
#print(new_person.email)          # Output: <EMAIL>
#print(new_person.phone_numbers)  # Output: ['************', '************']
#print(new_person.addresses)      # Output: [{'city': 'New York', 'state': 'NY'}, {'city': 'Los Angeles', 'state': 'CA'}]
