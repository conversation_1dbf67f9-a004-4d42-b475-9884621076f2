*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify searching with partial Bin Number
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${PARTIAL_BIN_NUMBER}    ${EXPECTED_FULL_BIN_NUMBER}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The user searches with a partial Bin Number    ${PARTIAL_BIN_NUMBER}
    Then The user verifies that the search results display the complete Bin Number that matches the partial input    ${EXPECTED_FULL_BIN_NUMBER}

| *** Test Cases ***                                                                    |        *DOCUMENTATION*               |         *BASE_URL*                  |   *PARTIAL_BIN_NUMBER*     |   *EXPECTED_FULL_BIN_NUMBER*  |         
| Capturer_Search for Partial BIN Number   | Verify searching with partial Bin Number   | Testing the partial search feature   |           ${EMPTY}                  |        3030                |      303020                   |        
