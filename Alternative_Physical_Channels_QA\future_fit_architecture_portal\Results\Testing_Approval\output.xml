<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-25T16:33:48.358851" rpa="false" schemaversion="5">
<suite id="s1" name="RAC29a TC 127 Approver Capture Campaign" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_127_Approver_Capture_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-25T16:33:49.766605" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-25T16:33:49.766605" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-25T16:33:49.766605" level="INFO">Environment variable 'BROWSER' set to value 'Chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-25T16:33:49.766605" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-25T16:33:49.765606" elapsed="0.000999"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_122_Show_maximum_campaign_feature" line="37">
<kw name="Validating the Approver Role">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-25T16:33:49.767605" level="INFO">Set test documentation to:
Approver Role</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-25T16:33:49.767605" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-25T16:33:49.861605" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-25T16:33:49.767605" elapsed="0.094993"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-25T16:33:49.862598" elapsed="0.000998"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-25T16:33:49.863596" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-25T16:33:49.865597" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-25T16:33:49.865597" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-25T16:33:49.866597" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-25T16:33:49.865597" elapsed="0.001000"/>
</branch>
<status status="PASS" start="2024-10-25T16:33:49.865597" elapsed="0.001000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-25T16:33:49.866597" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-25T16:33:49.866597" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-25T16:33:49.866597" elapsed="0.000998"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-25T16:33:49.908631" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-25T16:33:50.321539" level="INFO">${rc_code} = 0</msg>
<msg time="2024-10-25T16:33:50.321539" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 26516 has been terminated.
SUCCESS: The process "chrome.exe" with PID 25352 has been terminated.
SUCCESS: The process "chrome.exe" with PID 25744 has been te...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-25T16:33:49.867595" elapsed="0.453944"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-25T16:33:50.321539" elapsed="0.001005"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-25T16:33:49.865597" elapsed="0.457215"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-25T16:33:50.322812" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-25T16:33:50.322812" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-25T16:33:50.322812" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-25T16:33:50.322812" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-25T16:33:50.322812" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-25T16:33:50.322812" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-25T16:33:50.323817" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-25T16:33:50.324363" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-25T16:33:50.324363" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-25T16:33:50.324363" elapsed="0.000508"/>
</kw>
<status status="NOT RUN" start="2024-10-25T16:33:50.324363" elapsed="0.000564"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-25T16:33:50.324927" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-25T16:33:50.324927" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-25T16:33:50.324363" elapsed="0.000564"/>
</if>
<status status="NOT RUN" start="2024-10-25T16:33:50.323817" elapsed="0.001110"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-25T16:33:50.324927" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-25T16:33:50.324927" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-25T16:33:50.325937" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002692709EEA0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-25T16:33:50.324927" elapsed="0.001010"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-25T16:33:50.325937" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-25T16:33:50.325937" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-25T16:33:50.325937" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-25T16:33:50.325937" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-25T16:33:50.327153" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-25T16:33:50.326983" elapsed="0.000675"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-25T16:33:50.327774" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-25T16:33:50.328338" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-25T16:33:50.328338" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-25T16:33:50.328338" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-25T16:33:50.328338" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-25T16:33:50.328338" elapsed="0.001089"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-25T16:33:50.329427" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-25T16:33:50.329427" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-25T16:33:50.329427" elapsed="0.001007"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-25T16:33:50.330494" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-25T16:33:49.767605" elapsed="34.322654"/>
</kw>
<kw name="When The user has an active Approver Role" owner="Approvals">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:24.235982" level="INFO">Element 'xpath=//span[contains(@class, 'menu-item') and text()='Admin']' is displayed.</msg>
<arg>xpath=//span[contains(@class, 'menu-item') and text()='Admin']</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-25T16:34:24.090259" elapsed="0.145723"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:24.245982" level="INFO">Current page contains text 'Admin'.</msg>
<arg>Admin</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-25T16:34:24.235982" elapsed="0.011001"/>
</kw>
<status status="PASS" start="2024-10-25T16:34:24.090259" elapsed="0.156724"/>
</kw>
<kw name="Then The user should not be able to access Capture Campaign" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-25T16:34:24.246983" elapsed="0.011896"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:24.263365" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="e03ee886909bd704f57981aaa315a2d4", element="f.B444C6EF2B6168A6D169D2128D6211FE.d.6DDEEC74BE34BBDED655072FE59BED56.e.73")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-25T16:34:24.258879" elapsed="0.004486"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:24.263365" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="e03ee886909bd704f57981aaa315a2d4", element="f.B444C6EF2B6168A6D169D2128D6211FE.d.6DDEEC74BE34BBDED655072FE59BED56.e.73")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-25T16:34:24.263365" elapsed="0.036333"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-25T16:34:24.299698" elapsed="0.013536"/>
</kw>
<status status="PASS" start="2024-10-25T16:34:24.299698" elapsed="0.013536"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:24.325214" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="e03ee886909bd704f57981aaa315a2d4", element="f.B444C6EF2B6168A6D169D2128D6211FE.d.6DDEEC74BE34BBDED655072FE59BED56.e.74")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-25T16:34:24.313234" elapsed="0.011980"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-25T16:34:29.327468" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-25T16:34:24.326232" elapsed="5.001236"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-25T16:34:29.327468" elapsed="0.018346"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:29.345814" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="e03ee886909bd704f57981aaa315a2d4", element="f.B444C6EF2B6168A6D169D2128D6211FE.d.6DDEEC74BE34BBDED655072FE59BED56.e.74")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-25T16:34:29.345814" elapsed="0.152044"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-25T16:34:29.497858" elapsed="0.667419"/>
</kw>
<status status="PASS" start="2024-10-25T16:34:29.497858" elapsed="0.667419"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-25T16:34:30.165277" level="INFO">---- User does not have access to Capture Campaign as an Approver ----</msg>
<arg>---- User does not have access to Capture Campaign as an Approver ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>---- User does not have access to Capture Campaign as an Approver ----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-25T16:34:24.246983" elapsed="5.918294"/>
</kw>
<arg>Approver Role</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_UAT</arg>
<status status="PASS" start="2024-10-25T16:33:49.767605" elapsed="40.397672"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:30.165277" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="0.046727"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-25T16:34:33.213838" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-25T16:34:30.212987" elapsed="3.000851"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-25T16:34:33.213838" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-25T16:34:33.309575" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-676.png"&gt;&lt;img src="selenium-screenshot-676.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-25T16:34:33.309575" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-25T16:34:33.213838" elapsed="0.136744">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-25T16:34:35.350828" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-25T16:34:33.350582" elapsed="2.000246"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-25T16:34:35.351343" elapsed="3.200681"/>
</kw>
<status status="FAIL" start="2024-10-25T16:34:30.165277" elapsed="8.386747">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="8.386747"/>
</kw>
<status status="PASS" start="2024-10-25T16:34:30.165277" elapsed="8.386747"/>
</kw>
<doc>Approver Role</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-25T16:33:49.766605" elapsed="48.785419"/>
</test>
<doc>Verifying that the approver role does not permit users to capture campaigns</doc>
<status status="PASS" start="2024-10-25T16:33:48.358851" elapsed="50.193173"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="RAC29a TC 127 Approver Capture Campaign">RAC29a TC 127 Approver Capture Campaign</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-25T16:33:48.354172" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_127_Approver_Capture_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-25T16:33:49.428462" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\common\GenericMethods.robot' on line 109: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-25T16:33:49.723563" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 127: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-25T16:34:08.901908" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-25T16:34:18.916602" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-25T16:34:23.930807" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
