*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/ApproveBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Get all Bins to be reviewed using from the database and approve them using the Approve Bin Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_ID}   ${BIN_OUTCOME}    ${EXPECTED_STATUS_CODE}    ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    ${BINS_TO_APPROVE}=     Create Dictionary
    Set Global Variable  ${BINS_TO_APPROVE}
    IF    '${BIN_ID}' != '${EMPTY}' or '${BIN_OUTCOME}' != '${EMPTY}'
        Set To Dictionary    ${BINS_TO_APPROVE}     binId1=${BIN_ID}    outcome1=${BIN_OUTCOME}
    END

    Given The User Populates the Approve Bin JSON payload with data  &{BINS_TO_APPROVE}
    When The User sends an API request to Approve the Bin(s)       ${BASE_URL}
    And The service returns an expected status code     ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed     ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                                                                            |        *BASE_URL*       |          *DOCUMENTATION*            |          *BIN_ID*                          |          *BIN_OUTCOME*         |       *EXPECTED_STATUS_CODE*  |             *EXPECTED_ERROR_MESSAGE*                                                         |
| Approve a bin without providing the outcome data.   | Get all Bins to be reviewed using from the database and approve them using the Approve Bin Controller   |                         |    Approve bin using invalid data   |  0d1a7576-285f-4716-88ef-5c0695ae593d      |                                |               500             |   Value cannot be null. (Parameter 'The Outcome must not be null or empty.')                 |
| Approve a bin without providing the bin id  data.   | Get all Bins to be reviewed using from the database and approve them using the Approve Bin Controller   |                         |    Approve bin using invalid data   |                                            |           added                |               400             |   The JSON value could not be converted to System.Nullable`1[System.Guid]. Path: $[0].binId  |
