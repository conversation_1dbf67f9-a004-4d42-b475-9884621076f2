<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-15T21:12:56.750379" rpa="false" schemaversion="5">
<suite id="s1" name="MQ TC 3 Validate Cost Centre Information" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\mtgla\tests\ATM_Control\Dashboard\MQ_TC_3_Validate_Cost_Centre_Information.robot">
<test id="s1-t1" name="MQ-TC-3&#09;Validate Cost Centre Information" line="42">
<kw name="The user validates the Cost Center Information on Branch dashboard">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-15T21:12:57.733227" level="INFO">Set test documentation to:
validating the Cost Center Information on Branch dashboard</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-15T21:12:57.732224" elapsed="0.001003"/>
</kw>
<kw name="Given the user is logged into the MTGLA Web Application" owner="Login">
<kw name="Set Log Level" owner="BuiltIn">
<arg>NONE</arg>
<doc>Sets the log threshold to the specified level.</doc>
<status status="PASS" start="2024-10-15T21:12:57.733227" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dencryptedData_P}</var>
<arg>base64.b64decode('${base64_string_P}').decode("utf-8")</arg>
<arg>modules=base64</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-15T21:12:57.733227" elapsed="0.000999"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dencryptedData_U}</var>
<arg>base64.b64decode('${base64_string_U}').decode("utf-8")</arg>
<arg>modules=base64</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-15T21:12:57.734226" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>https://${dencryptedData_U}:${dencryptedData_P}@zaurnbmweb0126</arg>
<arg>chrome</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2024-10-15T21:12:57.734226" elapsed="41.584574"/>
</kw>
<kw name="Set Log Level" owner="BuiltIn">
<arg>INFO</arg>
<doc>Sets the log threshold to the specified level.</doc>
<status status="PASS" start="2024-10-15T21:13:39.318800" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:13:44.319419" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:39.318800" elapsed="5.000619"/>
</kw>
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2024-10-15T21:13:44.319419" elapsed="0.043274"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:44.375207" level="INFO">Current page contains text 'Mismatch GL Automation'.</msg>
<arg>Mismatch GL Automation</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-15T21:13:44.362693" elapsed="0.012514"/>
</kw>
<status status="PASS" start="2024-10-15T21:12:57.733227" elapsed="46.641980"/>
</kw>
<kw name="When the user lands on the Home page" owner="HomePage">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:44.382206" level="INFO">Current page contains text 'Mismatch GL Automation'.</msg>
<arg>Mismatch GL Automation</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-15T21:13:44.375207" elapsed="0.006999"/>
</kw>
<status status="PASS" start="2024-10-15T21:13:44.375207" elapsed="0.007999"/>
</kw>
<kw name="And the user navigates to the ATM Control Dashboard" owner="Navigation">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:13:46.383454" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:44.383206" elapsed="2.000248"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:46.383454" level="INFO">Clicking element 'xpath=/html/body/div[2]/button[1]'.</msg>
<arg>xpath=/html/body/div[2]/button[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-15T21:13:46.383454" elapsed="0.045945"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:13:48.429722" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:46.429399" elapsed="2.000323"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:48.430261" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='ATM Control']]'.</msg>
<arg>xpath=//*[text()[normalize-space(.)='ATM Control']]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-15T21:13:48.429722" elapsed="0.034269"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:13:50.464489" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:48.463991" elapsed="2.000498"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:50.464489" level="INFO">Clicking element 'xpath=//*[@id="ATMControl"]/a[1]'.</msg>
<arg>xpath=//*[@id="ATMControl"]/a[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-15T21:13:50.464489" elapsed="1.176325"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:13:53.643182" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:51.642084" elapsed="2.001098"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:53.650627" level="INFO">Current page contains text 'ATM Control Dashboard Date'.</msg>
<arg>ATM Control Dashboard Date</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-15T21:13:53.643182" elapsed="0.007445"/>
</kw>
<status status="PASS" start="2024-10-15T21:13:44.383206" elapsed="9.267421"/>
</kw>
<kw name="And the user clicks on a branch to access the Branch Dashboard" owner="ATM_Control_Dashboard">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:13:55.651116" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:53.650627" elapsed="2.000489"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-15T21:13:55.651116" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='87708636 - Aliwal North']]'.</msg>
<arg>xpath=//*[text()[normalize-space(.)='87708636 - Aliwal North']]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-15T21:13:55.651116" elapsed="1.212793"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.865480" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-15T21:13:56.864913" elapsed="5.000567"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-15T21:14:01.877560" level="INFO">Current page contains text 'Cost Centre Information'.</msg>
<arg>Cost Centre Information</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-15T21:14:01.865480" elapsed="0.012080"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-15T21:14:01.889831" level="INFO">Current page contains text 'Custodian Information'.</msg>
<arg>Custodian Information</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-15T21:14:01.877560" elapsed="0.012271"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-15T21:14:01.901499" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-15T21:14:01.889831" elapsed="0.011668"/>
</kw>
<status status="PASS" start="2024-10-15T21:13:53.650627" elapsed="8.250872"/>
</kw>
<kw name="And the user verifies the Cost Center Information is displayed correctly" owner="ATM_Control_Dashboard">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-15T21:14:01.919808" level="INFO">${COST_CENTER_INFORMATION} = Cost Centre Information
Region:
Eastern Cape
Division:
Retail Branch
Cost Centre:
87708636
Cost Centre Name:
Aliwal North</msg>
<var>${COST_CENTER_INFORMATION}</var>
<arg>xpath=//*[@id="main"]/div/div[1]/div[1]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-15T21:14:01.902498" elapsed="0.017310"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.921023" level="INFO">Full Information:
Cost Centre Information
Region:
Eastern Cape
Division:
Retail Branch
Cost Centre:
87708636
Cost Centre Name:
Aliwal North</msg>
<arg>Full Information:\n${COST_CENTER_INFORMATION}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-15T21:14:01.919808" elapsed="0.001215"/>
</kw>
<kw name="Split String" owner="String">
<msg time="2024-10-15T21:14:01.921023" level="INFO">${info_list} = ['Cost Centre Information', 'Region:', 'Eastern Cape', 'Division:', 'Retail Branch', 'Cost Centre:', '87708636', 'Cost Centre Name:', 'Aliwal North']</msg>
<var>${info_list}</var>
<arg>${COST_CENTER_INFORMATION}</arg>
<arg>\n</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<status status="PASS" start="2024-10-15T21:14:01.921023" elapsed="0.000000"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.921023" level="INFO">Length is 9.</msg>
<msg time="2024-10-15T21:14:01.921023" level="INFO">${list_length} = 9</msg>
<var>${list_length}</var>
<arg>${info_list}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2024-10-15T21:14:01.921023" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${list_length} &gt;= 8</arg>
<arg>The expected format has not been met.</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-15T21:14:01.921023" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.922057" level="INFO">${region} = Eastern Cape</msg>
<var>${region}</var>
<arg>${info_list[2].strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-15T21:14:01.921023" elapsed="0.001034"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.922057" level="INFO">${division} = Retail Branch</msg>
<var>${division}</var>
<arg>${info_list[4].strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-15T21:14:01.922057" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.922057" level="INFO">${cost_centre} = 87708636</msg>
<var>${cost_centre}</var>
<arg>${info_list[6].strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-15T21:14:01.922057" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.923039" level="INFO">${name} = Aliwal North</msg>
<var>${name}</var>
<arg>${info_list[8].strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-15T21:14:01.922057" elapsed="0.000982"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.923039" level="INFO">Region: Eastern Cape</msg>
<arg>Region: ${region}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-15T21:14:01.923039" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.923039" level="INFO">Division: Retail Branch</msg>
<arg>Division: ${division}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-15T21:14:01.923039" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.923039" level="INFO">Cost Centre: 87708636</msg>
<arg>Cost Centre: ${cost_centre}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-15T21:14:01.923039" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-15T21:14:01.923039" level="INFO">Cost Centre Name: Aliwal North</msg>
<arg>Cost Centre Name: ${name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-15T21:14:01.923039" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-15T21:14:01.902498" elapsed="0.020541"/>
</kw>
<arg>validating the Cost Center Information on Branch dashboard</arg>
<arg>MTGLA_UAT</arg>
<status status="PASS" start="2024-10-15T21:12:57.729275" elapsed="64.193764"/>
</kw>
<kw name="Close All Browsers" owner="SeleniumLibrary" type="TEARDOWN">
<doc>Closes all open browsers and resets the browser cache.</doc>
<status status="PASS" start="2024-10-15T21:14:01.924041" elapsed="6.358201"/>
</kw>
<doc>validating the Cost Center Information on Branch dashboard</doc>
<tag>MTGLA HEALTHCHECK</tag>
<status status="PASS" start="2024-10-15T21:12:57.728273" elapsed="70.554970"/>
</test>
<doc>ATM Control- Validate Cost Center Information</doc>
<status status="PASS" start="2024-10-15T21:12:56.753379" elapsed="71.530864"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">MTGLA HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="MQ TC 3 Validate Cost Centre Information">MQ TC 3 Validate Cost Centre Information</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
