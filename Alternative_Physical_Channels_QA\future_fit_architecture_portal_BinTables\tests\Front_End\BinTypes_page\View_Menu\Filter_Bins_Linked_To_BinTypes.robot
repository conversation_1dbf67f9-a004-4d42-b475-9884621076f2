*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Filter Bins linked to a Bin Type
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TYPE_TO_VERIFY}    ${FILTER_DATA}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal         ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user selects a Bin Type on the View Bin Types Page                        ${BIN_TYPE_TO_VERIFY}
    And The selected Bin Type Page which contains linked Bins is be displayed         ${BIN_TYPE_TO_VERIFY}
    Then The user must be able to filter the Bins displayed on the page               ${FILTER_DATA}

| *** Test Cases ***                                                                                      |        *DOCUMENTATION*    		    |         *BASE_URL*                  |         *BIN_TYPE_TO_VERIFY*          |         *FILTER_DATA*        |
| Filter Invalid Bins to display 5 Bins per page.                | Filter Bins linked to a Bin Type       | Filter Bins linked to a Bin Type.   |           ${EMPTY}                  |              Invalid                  |               5              |
| Filter Invalid Bins to display 10 Bins per page.               | Filter Bins linked to a Bin Type       | Filter Bins linked to a Bin Type.   |           ${EMPTY}                  |              Invalid                  |               10             |
| Filter Invalid Bins to display 25 Bins per page.               | Filter Bins linked to a Bin Type       | Filter Bins linked to a Bin Type.   |           ${EMPTY}                  |              Invalid                  |               25             |
| Filter Invalid Bins to display 100 Bins per page.              | Filter Bins linked to a Bin Type       | Filter Bins linked to a Bin Type.   |           ${EMPTY}                  |              Invalid                  |               100            |
