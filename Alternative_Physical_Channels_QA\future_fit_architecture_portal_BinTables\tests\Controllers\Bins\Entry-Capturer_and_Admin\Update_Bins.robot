*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/UpdateBin_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Update Pending Bin(s) details




*** Keywords ***
Update Bin(s)
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}   &{BINS_DETAILS}
    Set Test Documentation  ${DOCUMENTATION}
    Given The user verifies that the current BIN is not Deleted    &{BINS_DETAILS}
    When The User Populates the Update Bin JSON payload with    &{BINS_DETAILS}
    And The User sends the Update Bin API Request               ${BASE_URL}
    And The service returns an expected status code         ${EXPECTED_STATUS_CODE}
    Then The Edited Bin Number details must exist on the Bin Database    &{BINS_DETAILS}

| *** Test Cases ***                                             |        *DOCUMENTATION*    		  |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |                *BINS_DETAILS*                                                                                                                                                                                                |
| Update (Edit) existing Bin, change the outcome to be 'added'   | Update Bin(s)                      | Edit Pending Bin(s)                 |                             |         200                 |   binId1=59ef7be3-c73b-4648-91a1-568064a700ae  | bin1=7004535 | date1=2027-11-11 | binTypeIds1=a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e | binOutcome1=added    |
| Update (Edit) existing Bin, change the outcome to be 'deleted' | Update Bin(s)                      | Edit Pending Bin(s)                 |                             |         200                 |   binId1=59ef7be3-c73b-4648-91a1-568064a700ae  | bin1=7004535 | date1=2027-11-11 | binTypeIds1=a7ff7c25-057b-461b-9fa1-50d471202b52,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e | binOutcome1=deleted  |
