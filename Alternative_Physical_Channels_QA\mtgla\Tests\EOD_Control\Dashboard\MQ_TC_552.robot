*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/EOD_Control_Dashboard.robot

*** Variables ***

*** Keywords ***
Validates the Regions Displayed on EOD Dashboard
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page 

    And the user navigates to the EOD Control Dashboard Menu

    Then the user verifies the Regions Displayed on EOD Dashboard


| *Test Cases*                                                                                          |      *DOCUMENTATION* | *TEST_ENVIRONMENT*   |
| 	Verify the Regions Displayed on EOD Dashboard	| Validates the Regions Displayed on EOD Dashboard   |    Attestation Tile  |    MTGLA_UAT         | 
