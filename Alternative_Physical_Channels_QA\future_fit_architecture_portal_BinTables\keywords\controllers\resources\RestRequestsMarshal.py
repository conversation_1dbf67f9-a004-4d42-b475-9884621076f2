import json

import urllib3
import requests
from robot.api.deco import keyword


def read_rest_response(response):
    """
    A generic function to read and process a REST API response regardless of status code.

    :param response: The response object returned by the `requests` library.
    :return: Parsed data or error message, based on the status code and content.
    """
    try:
        # Try parsing the response as JSON (commonly used in APIs)
        try:
            response_data = response.json()
        except ValueError:
            response_data = response.text  # If not JSON, return raw text (e.g., HTML, plain text)

        # Check if the response was successful (status code 200-299)
        if 200 <= response.status_code < 300:
            # Success - return the JSON data or the raw text
            return {
                "status": "success",
                "status_code": response.status_code,
                "data": response_data
            }
        else:
            # Failure - return status code and error message (with the body content)
            return {
                "status": "error",
                "status_code": response.status_code,
                "data": response_data  # Return whatever the API sent back (error body)
            }

    except Exception as e:
        # Handle any errors during response processing
        return {
            "status": "error",
            "status_code": None,
            "message": f"An error occurred while processing the response: {str(e)}"
        }


def extract_all_fields_from_data(data):
    """
    Extracts all the fields from the 'data' object in the API response.

    :param data: The 'data' part of the response (could be a dict or a list of dicts).
    :return: A list of tuples with field names and their corresponding values.
    """
    extracted_fields = []

    # If the data is a dictionary, we will extract all the key-value pairs
    if isinstance(data, dict):
        for key, value in data.items():
            extracted_fields.append((key, value))

    # If the data is a list (of dictionaries), we will extract fields from each dictionary
    elif isinstance(data, list):
        for index, item in enumerate(data):
            if isinstance(item, dict):
                for key, value in item.items():
                    extracted_fields.append((f"Item {index} - {key}", value))

    return extracted_fields



@keyword
def send_rest_request(url, method="GET", headers=None, params=None, data=None, payload=None):
    """
    Sends a generic REST request using the requests library and processes the response.

    :param url: The URL to send the request to.
    :param method: HTTP method (GET, POST, PUT, DELETE, etc.). Default is "GET".
    :param headers: Optional dictionary of headers to send with the request.
    :param params: Optional dictionary of query parameters to append to the URL (for GET).
    :param data: Optional dictionary or string of data to send in the body (for POST/PUT).
    :param payload: Optional JSON data to send in the body (for POST/PUT, overrides 'data').
    :return: Parsed response or error message.
    """
    method = method.upper()

    try:
        #Suppress SSL warnings
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        # Send the request based on the HTTP method
        if method == "GET":
            response = requests.get(url, headers=headers, params=params, verify=False)
        elif method == "POST":
            response = requests.post(url, headers=headers, params=params, data=data, json=payload, verify=False)
        elif method == "PUT":
            response = requests.put(url, headers=headers, params=params, data=data, json=payload, verify=False)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers, params=params, verify=False)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        # Read and return the response using the read_rest_response function
        print('REST RESPONSE : \n', response)
        return read_rest_response(response)

    except requests.RequestException as e:
        # Handle request exceptions (e.g., network error, invalid URL)
        return {
            "status": "error",
            "status_code": None,
            "message": f"Request failed: {str(e)}"
        }


@keyword
def create_payload(payload_type="dict", **kwargs):
    """
    Creates a JSON payload as either a dictionary or a list of dictionaries, depending on the payload type.
    This function handles fields with comma-separated values and constructs a list of dictionaries as needed.
    """
    # Step 1: Process fields with comma-separated values into lists of values
    print(kwargs.items())
    for key, value in kwargs.items():
        print('key: ', key, 'Value: ', value)
        if isinstance(value, str) and ',' in value:  # If it's a comma-separated list (string)
            kwargs[key] = value.split(',')  # Convert to list of strings

    # Step 2: Determine the maximum number of items to generate
    max_items = 1
    for key, value in kwargs.items():
        print('key: ', key, 'Value: ', value)
        if isinstance(value, list):  # If the value is a list, check its length
            max_items = max(max_items, len(value))

    # Step 3: Handle payload type and construct the payload
    if payload_type == "dict":
        # Create a single dictionary where lists of values are added as they are
        payload = {}
        for key, value in kwargs.items():
            print('List')
            print('key: ', key, 'Value: ', value)
            if isinstance(value, list):  # If the value is a list, include it directly
                payload[key] = value
            else:
                payload[key] = value  # For non-list values, just include them directly
        return json.dumps(payload, indent=4)

    elif payload_type == "list":
        # Handle the case where the payload should be a list of dictionaries
        payload = []

        # Step 4: Create records based on the maximum number of items
        for i in range(max_items):
            item = {}
            for key, value in kwargs.items():
                if isinstance(value, list):  # If the value is a list
                    # Assign each value from the list to the current item (cycle through if shorter)
                    item[key] = value  # Ensure all values in the list are added
                else:
                    # For non-list values, just add them directly
                    item[key] = value
            payload.append(item)

        return json.dumps(payload, indent=4)

    else:
        raise ValueError("Invalid payload type. Use 'dict' or 'list'.")


