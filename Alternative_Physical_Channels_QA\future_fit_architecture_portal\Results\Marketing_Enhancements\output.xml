<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.2 on win32)" generated="******** 15:06:47.789" rpa="false" schemaversion="4">
<suite id="s1" name="Future Fit Portal" source="C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Marketing_Enhancements\Edit_Campaign\RAC29a_TC_741_Edit_a_campaign_become_1_month_campaign.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:06:49.204" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 15:06:49.204" endtime="******** 15:06:49.204"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:06:49.204" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="******** 15:06:49.204" endtime="******** 15:06:49.204"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:06:49.205" level="INFO">Environment variable 'BASE_URL' set to value 'APC_DEV'.</msg>
<status status="PASS" starttime="******** 15:06:49.205" endtime="******** 15:06:49.205"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.204" endtime="******** 15:06:49.205"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_748_Export_Marketing_Campaigns" line="43">
<kw name="Validating Export Campaign Functionality">
<arg>Campaign Export</arg>
<arg>BUSINESS_CAPTURER</arg>
<arg>APC_UAT</arg>
<arg>26-11-2024</arg>
<arg>26-12-2024</arg>
<arg>English,Afrikaans</arg>
<arg>images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg,images\\MarketingA_af_2.jpg</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 15:06:49.206" level="INFO">Set test documentation to:
Campaign Export</msg>
<status status="PASS" starttime="******** 15:06:49.205" endtime="******** 15:06:49.206"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:06:49.328" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 15:06:49.206" endtime="******** 15:06:49.328"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:06:49.328" endtime="******** 15:06:49.329"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:06:49.329" endtime="******** 15:06:49.330"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:06:49.331" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<status status="PASS" starttime="******** 15:06:49.331" endtime="******** 15:06:49.331"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:06:49.332" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<status status="PASS" starttime="******** 15:06:49.332" endtime="******** 15:06:49.332"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.331" endtime="******** 15:06:49.332"/>
</branch>
<status status="PASS" starttime="******** 15:06:49.331" endtime="******** 15:06:49.332"/>
</if>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:06:49.332" level="INFO">${handle} = msedge.exe</msg>
<status status="PASS" starttime="******** 15:06:49.332" endtime="******** 15:06:49.332"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:06:49.332" endtime="******** 15:06:49.332"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 15:06:49.369" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 15:06:49.768" level="INFO">${rc_code} = 128</msg>
<msg timestamp="******** 15:06:49.768" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<status status="PASS" starttime="******** 15:06:49.332" endtime="******** 15:06:49.768"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:06:49.770" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="******** 15:06:49.769" endtime="******** 15:06:49.771"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.768" endtime="******** 15:06:49.771"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.330" endtime="******** 15:06:49.771"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:06:49.771" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="******** 15:06:49.771" endtime="******** 15:06:49.771"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 15:06:49.772" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="******** 15:06:49.771" endtime="******** 15:06:49.772"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 15:06:49.772" level="INFO">${browser_name} = EDGE</msg>
<status status="PASS" starttime="******** 15:06:49.772" endtime="******** 15:06:49.772"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:06:49.772" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<status status="PASS" starttime="******** 15:06:49.772" endtime="******** 15:06:49.772"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<msg timestamp="******** 15:06:49.773" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001F35B93C170&gt;</msg>
<status status="PASS" starttime="******** 15:06:49.772" endtime="******** 15:06:49.773"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="******** 15:06:49.773" endtime="******** 15:06:49.773"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 15:06:49.773" endtime="******** 15:06:49.773"/>
</kw>
<status status="NOT RUN" starttime="******** 15:06:49.773" endtime="******** 15:06:49.773"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="******** 15:06:49.774" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<status status="PASS" starttime="******** 15:06:49.773" endtime="******** 15:06:51.623"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.773" endtime="******** 15:06:51.623"/>
</branch>
<status status="PASS" starttime="******** 15:06:49.773" endtime="******** 15:06:51.623"/>
</if>
<status status="PASS" starttime="******** 15:06:49.772" endtime="******** 15:06:51.623"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.623" endtime="******** 15:06:51.623"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.623" endtime="******** 15:06:51.623"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.623" endtime="******** 15:06:51.623"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.625" endtime="******** 15:06:51.625"/>
</kw>
<status status="NOT RUN" starttime="******** 15:06:51.623" endtime="******** 15:06:51.625"/>
</branch>
<status status="NOT RUN" starttime="******** 15:06:51.623" endtime="******** 15:06:51.625"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.625" endtime="******** 15:06:51.625"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.625" endtime="******** 15:06:51.625"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.625" endtime="******** 15:06:51.625"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.625" endtime="******** 15:06:51.625"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 15:06:51.626" endtime="******** 15:06:51.626"/>
</kw>
<status status="NOT RUN" starttime="******** 15:06:51.623" endtime="******** 15:06:51.626"/>
</branch>
<status status="PASS" starttime="******** 15:06:49.772" endtime="******** 15:06:51.627"/>
</if>
<status status="PASS" starttime="******** 15:06:49.330" endtime="******** 15:06:51.627"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:06:51.627" level="INFO">${BASE_URL} = APC_DEV</msg>
<status status="PASS" starttime="******** 15:06:51.627" endtime="******** 15:06:51.627"/>
</kw>
<kw name="Load" library="Login">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 15:06:51.635" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 15:06:51.635" level="INFO">${base_url} = https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 15:06:51.627" endtime="******** 15:06:51.635"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 15:06:51.635" endtime="******** 15:06:51.669"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 15:06:51.672" level="INFO">Opening url 'https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 15:06:51.672" endtime="******** 15:06:51.909"/>
</kw>
<status status="PASS" starttime="******** 15:06:51.669" endtime="******** 15:06:51.909"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:01.911" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 15:06:51.909" endtime="******** 15:07:01.911"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 15:07:01.911" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:07:03.108" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 15:07:01.911" endtime="******** 15:07:03.108"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 15:07:03.109" endtime="******** 15:07:03.109"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:13.110" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 15:07:03.109" endtime="******** 15:07:13.110"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 15:07:13.110" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:07:13.130" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 15:07:13.111" endtime="******** 15:07:13.130"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 15:07:13.130" endtime="******** 15:07:13.130"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:18.130" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 15:07:13.130" endtime="******** 15:07:18.130"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 15:07:18.130" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:07:18.155" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 15:07:18.130" endtime="******** 15:07:18.155"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 15:07:18.155" endtime="******** 15:07:18.155"/>
</kw>
<status status="PASS" starttime="******** 15:06:51.627" endtime="******** 15:07:18.156"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:18.346" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-582.png"&gt;&lt;img src="selenium-screenshot-582.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:18.156" endtime="******** 15:07:18.346"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.330" endtime="******** 15:07:18.346"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.330" endtime="******** 15:07:18.346"/>
</kw>
<kw name="The user navigates to their Specified Dashboard" library="common_keywords">
<arg>ATM_MARKETING_DASHBOARD</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>$DASHBOARD == 'ATM_MARKETING_DASHBOARD'</arg>
<arg>The user clicks Dashboard</arg>
<arg>${ATM_MARKETING_DASHBOARD_XPATH}</arg>
<arg>ELSE IF</arg>
<arg>$DASHBOARD == 'BIN_TABLES'</arg>
<arg>The user clicks Dashboard</arg>
<arg>${BIN_TABLES_DASHBOARD_XPATH}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="The user clicks Dashboard" library="common_keywords">
<arg>${ATM_MARKETING_DASHBOARD_XPATH}</arg>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${DASHBOARD_XPATH}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:18.347" endtime="******** 15:07:18.395"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DASHBOARD_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:18.396" level="INFO">Clicking element 'xpath=//div[contains(@class, 'landing-content')]//p[contains(text(), 'ATM Marketing')]'.</msg>
<status status="PASS" starttime="******** 15:07:18.395" endtime="******** 15:07:18.627"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user has clicked the specified dashboard</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:18.628" endtime="******** 15:07:18.628"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:18.973" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-583.png"&gt;&lt;img src="selenium-screenshot-583.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:18.628" endtime="******** 15:07:18.974"/>
</kw>
<status status="PASS" starttime="******** 15:07:18.347" endtime="******** 15:07:18.974"/>
</kw>
<status status="PASS" starttime="******** 15:07:18.346" endtime="******** 15:07:18.974"/>
</kw>
<status status="PASS" starttime="******** 15:07:18.346" endtime="******** 15:07:18.974"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.206" endtime="******** 15:07:18.974"/>
</kw>
<kw name="Then The user navigates to the Campaign History page" library="Campaign_History">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_HISTORY_PAGE_BUTTON}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:18.974" endtime="******** 15:07:19.000"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_HISTORY_PAGE_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:19.002" level="INFO">Clicking element 'xpath=//span[contains(text(), 'Campaign History')]'.</msg>
<status status="PASS" starttime="******** 15:07:19.001" endtime="******** 15:07:19.147"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user has clicked Campaign History Page button</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:19.147" endtime="******** 15:07:19.148"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:19.362" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-584.png"&gt;&lt;img src="selenium-screenshot-584.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:19.148" endtime="******** 15:07:19.362"/>
</kw>
<status status="PASS" starttime="******** 15:07:18.974" endtime="******** 15:07:19.362"/>
</kw>
<kw name="And The user Edits a Campaign" library="Edit_Campaign">
<arg>${START_DATE}</arg>
<arg>${END_DATE}</arg>
<kw name="The user finds a campaign to edit" library="Edit_Campaign">
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${elements}</var>
<arg>xpath=//fa-icon[@matTooltip='Edit']</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="******** 15:07:19.378" level="INFO">${elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="62f1224ac903b2f99c22ca94f1ec8b69", element="f.385C8DF9554220F9FE2830C6E23235F1.d.AFD7B986E3A1F14B0AA577D6EBEDFB7B.e.175")&gt;, &lt;selenium.webdri...</msg>
<status status="PASS" starttime="******** 15:07:19.363" endtime="******** 15:07:19.378"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${elements_length}</var>
<arg>${elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 15:07:19.378" level="INFO">Length is 5</msg>
<msg timestamp="******** 15:07:19.378" level="INFO">${elements_length} = 5</msg>
<status status="PASS" starttime="******** 15:07:19.378" endtime="******** 15:07:19.378"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${elements_length} == 0</arg>
<arg>Fail</arg>
<arg>No elements found matching the XPath expression.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:07:19.378" endtime="******** 15:07:19.378"/>
</kw>
<kw name="Get From List" library="Collections">
<var>${first_element}</var>
<arg>${elements}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<msg timestamp="******** 15:07:19.379" level="INFO">${first_element} = &lt;selenium.webdriver.remote.webelement.WebElement (session="62f1224ac903b2f99c22ca94f1ec8b69", element="f.385C8DF9554220F9FE2830C6E23235F1.d.AFD7B986E3A1F14B0AA577D6EBEDFB7B.e.175")&gt;</msg>
<status status="PASS" starttime="******** 15:07:19.378" endtime="******** 15:07:19.379"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${first_element}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:19.379" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="62f1224ac903b2f99c22ca94f1ec8b69", element="f.385C8DF9554220F9FE2830C6E23235F1.d.AFD7B986E3A1F14B0AA577D6EBEDFB7B.e.175")&gt;'.</msg>
<status status="PASS" starttime="******** 15:07:19.379" endtime="******** 15:07:19.488"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:19.704" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-585.png"&gt;&lt;img src="selenium-screenshot-585.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:19.488" endtime="******** 15:07:19.704"/>
</kw>
<status status="PASS" starttime="******** 15:07:19.362" endtime="******** 15:07:19.704"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//span[contains(text(), 'Next') and not(ancestor::button[@disabled])]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:19.705" endtime="******** 15:07:19.738"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//span[contains(text(), 'Next') and not(ancestor::button[@disabled])]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:19.739" level="INFO">Clicking element 'xpath=//button//span[contains(text(), 'Next') and not(ancestor::button[@disabled])]'.</msg>
<status status="PASS" starttime="******** 15:07:19.739" endtime="******** 15:07:20.526"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//input[@name='campaignName']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:20.526" endtime="******** 15:07:20.795"/>
</kw>
<kw name="Clear Element Text" library="SeleniumLibrary">
<arg>xpath=//input[@name='campaignName']</arg>
<doc>Clears the value of the text-input-element identified by ``locator``.</doc>
<status status="PASS" starttime="******** 15:07:20.795" endtime="******** 15:07:20.862"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>xpath=//input[@name='campaignName']</arg>
<arg>Edit_Campaign</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="******** 15:07:20.862" level="INFO">Typing text 'Edit_Campaign' into text field 'xpath=//input[@name='campaignName']'.</msg>
<status status="PASS" starttime="******** 15:07:20.862" endtime="******** 15:07:21.112"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>The user has enter campaign name</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.112" endtime="******** 15:07:21.113"/>
</kw>
<kw name="The user input campaign Start and End Date" library="Edit_Campaign">
<arg>${START_DATE}</arg>
<arg>${END_DATE}</arg>
<kw name="Get Substring" library="String">
<var>${start_day}</var>
<arg>${START_DATE}</arg>
<arg>0</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 15:07:21.114" level="INFO">${start_day} = 26</msg>
<status status="PASS" starttime="******** 15:07:21.114" endtime="******** 15:07:21.114"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Start Day: ${start_day}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.115" endtime="******** 15:07:21.115"/>
</kw>
<kw name="Get Substring" library="String">
<var>${start_month}</var>
<arg>${START_DATE}</arg>
<arg>3</arg>
<arg>5</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 15:07:21.116" level="INFO">${start_month} = 11</msg>
<status status="PASS" starttime="******** 15:07:21.115" endtime="******** 15:07:21.116"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Start Month: ${start_month}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.117" endtime="******** 15:07:21.118"/>
</kw>
<kw name="Get Month Abbreviation" library="Edit_Campaign">
<var>${start_month_name}</var>
<arg>${start_month}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JAN</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:21.119" level="INFO">${month_abbreviation} = JAN</msg>
<status status="PASS" starttime="******** 15:07:21.119" endtime="******** 15:07:21.119"/>
</kw>
<if>
<branch type="IF" condition="'${month_number}' == '1'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JAN</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.120" endtime="******** 15:07:21.120"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.120" endtime="******** 15:07:21.121"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.120" endtime="******** 15:07:21.121"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '2'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>FEB</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.121" endtime="******** 15:07:21.122"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.121" endtime="******** 15:07:21.122"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.121" endtime="******** 15:07:21.122"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '3'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>MAR</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.123" endtime="******** 15:07:21.123"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.122" endtime="******** 15:07:21.123"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.122" endtime="******** 15:07:21.123"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '4'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>APR</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.124" endtime="******** 15:07:21.124"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.123" endtime="******** 15:07:21.124"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.123" endtime="******** 15:07:21.124"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '5'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>MAY</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.125" endtime="******** 15:07:21.125"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.124" endtime="******** 15:07:21.125"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.124" endtime="******** 15:07:21.125"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '6'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JUN</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.125" endtime="******** 15:07:21.125"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.125" endtime="******** 15:07:21.126"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.125" endtime="******** 15:07:21.126"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '7'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JUL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.126" endtime="******** 15:07:21.126"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.126" endtime="******** 15:07:21.126"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.126" endtime="******** 15:07:21.126"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '8'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>AUG</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.127" endtime="******** 15:07:21.127"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.126" endtime="******** 15:07:21.127"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.126" endtime="******** 15:07:21.127"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '9'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>SEP</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.127" endtime="******** 15:07:21.127"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.127" endtime="******** 15:07:21.127"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.127" endtime="******** 15:07:21.127"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '10'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>OCT</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.128" endtime="******** 15:07:21.128"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.128" endtime="******** 15:07:21.128"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.128" endtime="******** 15:07:21.128"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '11'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>NOV</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:21.129" level="INFO">${month_abbreviation} = NOV</msg>
<status status="PASS" starttime="******** 15:07:21.128" endtime="******** 15:07:21.129"/>
</kw>
<status status="PASS" starttime="******** 15:07:21.128" endtime="******** 15:07:21.129"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.128" endtime="******** 15:07:21.129"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '12'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>DEC</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.129" endtime="******** 15:07:21.129"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.129" endtime="******** 15:07:21.129"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.129" endtime="******** 15:07:21.129"/>
</if>
<return>
<value>${month_abbreviation}</value>
<status status="PASS" starttime="******** 15:07:21.129" endtime="******** 15:07:21.130"/>
</return>
<msg timestamp="******** 15:07:21.130" level="INFO">${start_month_name} = NOV</msg>
<status status="PASS" starttime="******** 15:07:21.118" endtime="******** 15:07:21.130"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Start Month: ${start_month_name}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.130" endtime="******** 15:07:21.131"/>
</kw>
<kw name="Get Substring" library="String">
<var>${start_year}</var>
<arg>${START_DATE}</arg>
<arg>6</arg>
<arg>10</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 15:07:21.132" level="INFO">${start_year} = 2024</msg>
<status status="PASS" starttime="******** 15:07:21.131" endtime="******** 15:07:21.132"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Start Year: ${start_year}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.132" endtime="******** 15:07:21.133"/>
</kw>
<kw name="Get Substring" library="String">
<var>${end_day}</var>
<arg>${END_DATE}</arg>
<arg>0</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 15:07:21.133" level="INFO">${end_day} = 26</msg>
<status status="PASS" starttime="******** 15:07:21.133" endtime="******** 15:07:21.133"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>End Day: ${end_day}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.134" endtime="******** 15:07:21.134"/>
</kw>
<kw name="Get Substring" library="String">
<var>${end_month}</var>
<arg>${END_DATE}</arg>
<arg>3</arg>
<arg>5</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 15:07:21.135" level="INFO">${end_month} = 12</msg>
<status status="PASS" starttime="******** 15:07:21.135" endtime="******** 15:07:21.135"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>End Month: ${end_month}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.135" endtime="******** 15:07:21.136"/>
</kw>
<kw name="Get Month Abbreviation" library="Edit_Campaign">
<var>${end_month_name}</var>
<arg>${end_month}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JAN</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:21.137" level="INFO">${month_abbreviation} = JAN</msg>
<status status="PASS" starttime="******** 15:07:21.136" endtime="******** 15:07:21.137"/>
</kw>
<if>
<branch type="IF" condition="'${month_number}' == '1'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JAN</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.138" endtime="******** 15:07:21.138"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.137" endtime="******** 15:07:21.138"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.137" endtime="******** 15:07:21.138"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '2'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>FEB</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.139" endtime="******** 15:07:21.139"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.138" endtime="******** 15:07:21.139"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.138" endtime="******** 15:07:21.139"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '3'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>MAR</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.140" endtime="******** 15:07:21.140"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.139" endtime="******** 15:07:21.140"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.139" endtime="******** 15:07:21.140"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '4'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>APR</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.140" endtime="******** 15:07:21.141"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.140" endtime="******** 15:07:21.141"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.140" endtime="******** 15:07:21.141"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '5'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>MAY</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.141" endtime="******** 15:07:21.141"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.141" endtime="******** 15:07:21.141"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.141" endtime="******** 15:07:21.141"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '6'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JUN</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.142" endtime="******** 15:07:21.142"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.141" endtime="******** 15:07:21.142"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.141" endtime="******** 15:07:21.142"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '7'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>JUL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.143" endtime="******** 15:07:21.143"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.142" endtime="******** 15:07:21.143"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.142" endtime="******** 15:07:21.143"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '8'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>AUG</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.143" endtime="******** 15:07:21.143"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.143" endtime="******** 15:07:21.143"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.143" endtime="******** 15:07:21.143"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '9'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>SEP</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.144" endtime="******** 15:07:21.144"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.144" endtime="******** 15:07:21.144"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.144" endtime="******** 15:07:21.144"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '10'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>OCT</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.144" endtime="******** 15:07:21.145"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.144" endtime="******** 15:07:21.145"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.144" endtime="******** 15:07:21.145"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '11'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>NOV</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 15:07:21.146" endtime="******** 15:07:21.146"/>
</kw>
<status status="NOT RUN" starttime="******** 15:07:21.145" endtime="******** 15:07:21.146"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.145" endtime="******** 15:07:21.146"/>
</if>
<if>
<branch type="IF" condition="'${month_number}' == '12'">
<kw name="Set Variable" library="BuiltIn">
<var>${month_abbreviation}</var>
<arg>DEC</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:21.147" level="INFO">${month_abbreviation} = DEC</msg>
<status status="PASS" starttime="******** 15:07:21.146" endtime="******** 15:07:21.147"/>
</kw>
<status status="PASS" starttime="******** 15:07:21.146" endtime="******** 15:07:21.147"/>
</branch>
<status status="PASS" starttime="******** 15:07:21.146" endtime="******** 15:07:21.147"/>
</if>
<return>
<value>${month_abbreviation}</value>
<status status="PASS" starttime="******** 15:07:21.147" endtime="******** 15:07:21.147"/>
</return>
<msg timestamp="******** 15:07:21.147" level="INFO">${end_month_name} = DEC</msg>
<status status="PASS" starttime="******** 15:07:21.136" endtime="******** 15:07:21.147"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>End Month: ${end_month_name}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.147" endtime="******** 15:07:21.148"/>
</kw>
<kw name="Get Substring" library="String">
<var>${end_year}</var>
<arg>${END_DATE}</arg>
<arg>6</arg>
<arg>10</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 15:07:21.149" level="INFO">${end_year} = 2024</msg>
<status status="PASS" starttime="******** 15:07:21.148" endtime="******** 15:07:21.149"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>End Year: ${end_year}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:21.149" endtime="******** 15:07:21.149"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button[@aria-label='Choose month and year']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.150" endtime="******** 15:07:21.186"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[@aria-label='Choose month and year']</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:21.187" level="INFO">Clicking element 'xpath=//button[@aria-label='Choose month and year']'.</msg>
<status status="PASS" starttime="******** 15:07:21.187" endtime="******** 15:07:21.298"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${start_year}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.298" endtime="******** 15:07:21.341"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${start_year}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:21.343" level="INFO">Clicking element 'xpath=//button//div[contains(text(),'2024')]'.</msg>
<status status="PASS" starttime="******** 15:07:21.341" endtime="******** 15:07:21.462"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${start_month_name}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.462" endtime="******** 15:07:21.512"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${start_month_name}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:21.513" level="INFO">Clicking element 'xpath=//button//div[contains(text(),'NOV')]'.</msg>
<status status="PASS" starttime="******** 15:07:21.512" endtime="******** 15:07:21.614"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${start_day}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.614" endtime="******** 15:07:21.655"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${start_day}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:21.656" level="INFO">Clicking element 'xpath=//button//div[contains(text(),'26')]'.</msg>
<status status="PASS" starttime="******** 15:07:21.655" endtime="******** 15:07:21.743"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button[@aria-label='Choose month and year']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.743" endtime="******** 15:07:21.776"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[@aria-label='Choose month and year']</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:21.777" level="INFO">Clicking element 'xpath=//button[@aria-label='Choose month and year']'.</msg>
<status status="PASS" starttime="******** 15:07:21.776" endtime="******** 15:07:21.854"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${end_year}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.855" endtime="******** 15:07:21.888"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${end_year}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:21.889" level="INFO">Clicking element 'xpath=//button//div[contains(text(),'2024')]'.</msg>
<status status="PASS" starttime="******** 15:07:21.888" endtime="******** 15:07:21.973"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${end_month_name}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:21.973" endtime="******** 15:07:22.014"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${end_month_name}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:22.015" level="INFO">Clicking element 'xpath=//button//div[contains(text(),'DEC')]'.</msg>
<status status="PASS" starttime="******** 15:07:22.014" endtime="******** 15:07:22.106"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${end_day}')]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:22.107" endtime="******** 15:07:22.136"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button//div[contains(text(),'${end_day}')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:22.137" level="INFO">Clicking element 'xpath=//button//div[contains(text(),'26')]'.</msg>
<status status="PASS" starttime="******** 15:07:22.136" endtime="******** 15:07:22.207"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button[@class='mat-focus-indicator mat-button mat-button-base ng-star-inserted']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:22.207" endtime="******** 15:07:22.237"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[@class='mat-focus-indicator mat-button mat-button-base ng-star-inserted']</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:22.238" level="INFO">Clicking element 'xpath=//button[@class='mat-focus-indicator mat-button mat-button-base ng-star-inserted']'.</msg>
<status status="PASS" starttime="******** 15:07:22.237" endtime="******** 15:07:22.448"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:22.675" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-586.png"&gt;&lt;img src="selenium-screenshot-586.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:22.448" endtime="******** 15:07:22.675"/>
</kw>
<status status="PASS" starttime="******** 15:07:21.113" endtime="******** 15:07:22.675"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:32.675" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 15:07:22.675" endtime="******** 15:07:32.675"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//span[@class='btn-remove-image']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:07:32.676" endtime="******** 15:07:32.700"/>
</kw>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${btn_remove_image_list}</var>
<arg>xpath=//span[@class='btn-remove-image']</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="******** 15:07:32.708" level="INFO">${btn_remove_image_list} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="62f1224ac903b2f99c22ca94f1ec8b69", element="f.385C8DF9554220F9FE2830C6E23235F1.d.AFD7B986E3A1F14B0AA577D6EBEDFB7B.e.225")&gt;]</msg>
<status status="PASS" starttime="******** 15:07:32.700" endtime="******** 15:07:32.708"/>
</kw>
<for flavor="IN">
<var>${button}</var>
<value>${btn_remove_image_list}</value>
<iter>
<var name="${button}">[&lt;selenium.webdriver.remote.webelement.WebElement (session="62f1224ac903b2f99c22ca94f1ec8b69", element="f.385C8DF9554220F9FE2830C6E23235F1.d.AFD7B986E3A1F14B0AA577D6EBEDFB7B.e.225")&gt;]</var>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//span[@class='btn-remove-image']</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:32.709" level="INFO">Clicking element 'xpath=//span[@class='btn-remove-image']'.</msg>
<status status="PASS" starttime="******** 15:07:32.709" endtime="******** 15:07:32.748"/>
</kw>
<status status="PASS" starttime="******** 15:07:32.709" endtime="******** 15:07:32.748"/>
</iter>
<status status="PASS" starttime="******** 15:07:32.708" endtime="******** 15:07:32.748"/>
</for>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:32.896" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-587.png"&gt;&lt;img src="selenium-screenshot-587.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:32.749" endtime="******** 15:07:32.896"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:42.897" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 15:07:32.896" endtime="******** 15:07:42.897"/>
</kw>
<status status="PASS" starttime="******** 15:07:19.362" endtime="******** 15:07:42.897"/>
</kw>
<kw name="And The user captures marketing screen information" library="UploadMarkrtingScreen">
<arg>${CAMPAIGN_LANGUAGE}</arg>
<arg>${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:42.898" level="INFO">${counter} = 0</msg>
<status status="PASS" starttime="******** 15:07:42.898" endtime="******** 15:07:42.898"/>
</kw>
<kw name="Split String" library="String">
<var>@{campaign_laguages}</var>
<arg>${CAMPAIGN_LANGUAGE}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 15:07:42.898" level="INFO">@{campaign_laguages} = [ English | Afrikaans ]</msg>
<status status="PASS" starttime="******** 15:07:42.898" endtime="******** 15:07:42.898"/>
</kw>
<kw name="Split String" library="String">
<var>@{campaign_images}</var>
<arg>${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 15:07:42.899" level="INFO">@{campaign_images} = [ images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg | images\MarketingA_af_2.jpg ]</msg>
<status status="PASS" starttime="******** 15:07:42.899" endtime="******** 15:07:42.899"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${campaign_laguages_length}</var>
<arg>${campaign_laguages}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 15:07:42.899" level="INFO">Length is 2</msg>
<msg timestamp="******** 15:07:42.899" level="INFO">${campaign_laguages_length} = 2</msg>
<status status="PASS" starttime="******** 15:07:42.899" endtime="******** 15:07:42.899"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_laguages}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:07:42.900" level="INFO">['English', 'Afrikaans']</msg>
<status status="PASS" starttime="******** 15:07:42.899" endtime="******** 15:07:42.900"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${campaign_images_length}</var>
<arg>${campaign_images}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 15:07:42.900" level="INFO">Length is 2</msg>
<msg timestamp="******** 15:07:42.900" level="INFO">${campaign_images_length} = 2</msg>
<status status="PASS" starttime="******** 15:07:42.900" endtime="******** 15:07:42.900"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_images}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:07:42.901" level="INFO">['images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg', 'images\\MarketingA_af_2.jpg']</msg>
<status status="PASS" starttime="******** 15:07:42.900" endtime="******** 15:07:42.901"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>@{campaign_laguages}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:42.901" endtime="******** 15:07:42.902"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>@{campaign_images}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:42.902" endtime="******** 15:07:42.903"/>
</kw>
<for flavor="IN">
<var>${language}</var>
<value>@{campaign_laguages}</value>
<iter>
<var name="${language}">English</var>
<kw name="Log To Console" library="BuiltIn">
<arg>Language is</arg>
<arg>:</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:42.903" endtime="******** 15:07:42.905"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${current_image}</var>
<arg>''</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:42.905" level="INFO">${current_image} = ''</msg>
<status status="PASS" starttime="******** 15:07:42.905" endtime="******** 15:07:42.905"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<arg>Fail</arg>
<arg>Please provide an image path variable for '${language.strip()}' language!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:07:42.905" endtime="******** 15:07:42.906"/>
</kw>
<kw name="Exit For Loop If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<doc>Stops executing the enclosing FOR loop if the ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:07:42.906" endtime="******** 15:07:42.906"/>
</kw>
<kw name="Select from dropdown" library="Navigation">
<arg>${LANGUAGE_DROPDOWN}</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:42.908" endtime="******** 15:07:42.908"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:44.908" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:07:42.908" endtime="******** 15:07:44.908"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:44.909" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="language"]'.</msg>
<status status="PASS" starttime="******** 15:07:44.908" endtime="******** 15:07:44.988"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:49.989" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 15:07:44.989" endtime="******** 15:07:49.989"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:07:49.990" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 15:07:49.990" endtime="******** 15:07:49.990"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:49.990" endtime="******** 15:07:49.990"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:07:49.991" level="INFO">${path_string} = //span[text()=' English </msg>
<status status="PASS" starttime="******** 15:07:49.990" endtime="******** 15:07:49.991"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:49.991" endtime="******** 15:07:49.992"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:07:49.992" level="INFO">${path_string} = //span[text()=' English ']</msg>
<status status="PASS" starttime="******** 15:07:49.992" endtime="******** 15:07:49.992"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:49.992" endtime="******** 15:07:49.993"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:07:49.993" endtime="******** 15:07:50.030"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:50.031" level="INFO">Clicking element 'xpath=//span[text()=' English ']'.</msg>
<status status="PASS" starttime="******** 15:07:50.030" endtime="******** 15:07:50.084"/>
</kw>
<status status="PASS" starttime="******** 15:07:42.906" endtime="******** 15:07:50.084"/>
</kw>
<kw name="Get Path" library="Utility">
<var>${image_directory}</var>
<msg timestamp="******** 15:07:50.085" level="INFO">${image_directory} = C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA</msg>
<status status="PASS" starttime="******** 15:07:50.085" endtime="******** 15:07:50.085"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${final_image_path}</var>
<arg>SEPARATOR=\\</arg>
<arg>${image_directory}</arg>
<arg>future_fit_architecture_portal</arg>
<arg>${campaign_images[${counter}].strip()}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:07:50.086" level="INFO">${final_image_path} = C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg</msg>
<status status="PASS" starttime="******** 15:07:50.085" endtime="******** 15:07:50.086"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Image path is : ${campaign_images[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:50.086" endtime="******** 15:07:50.087"/>
</kw>
<kw name="Choose File" library="SeleniumLibrary">
<arg>xpath://input[@value='select' and @class='ng-star-inserted']</arg>
<arg>${final_image_path}</arg>
<doc>Inputs the ``file_path`` into the file input field ``locator``.</doc>
<msg timestamp="******** 15:07:50.088" level="INFO">Sending C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg to browser.</msg>
<status status="PASS" starttime="******** 15:07:50.088" endtime="******** 15:07:50.132"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:53.132" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 15:07:50.132" endtime="******** 15:07:53.132"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>UploadMarkrtingScreen.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:07:53.274" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="UploadMarkrtingScreen.png"&gt;&lt;img src="UploadMarkrtingScreen.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:07:53.132" endtime="******** 15:07:53.275"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>${counter}+1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:53.275" level="INFO">${counter} = 0+1</msg>
<status status="PASS" starttime="******** 15:07:53.275" endtime="******** 15:07:53.275"/>
</kw>
<status status="PASS" starttime="******** 15:07:42.903" endtime="******** 15:07:53.275"/>
</iter>
<iter>
<var name="${language}">Afrikaans</var>
<kw name="Log To Console" library="BuiltIn">
<arg>Language is</arg>
<arg>:</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:53.275" endtime="******** 15:07:53.276"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${current_image}</var>
<arg>''</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:07:53.276" level="INFO">${current_image} = ''</msg>
<status status="PASS" starttime="******** 15:07:53.276" endtime="******** 15:07:53.276"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<arg>Fail</arg>
<arg>Please provide an image path variable for '${language.strip()}' language!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:07:53.277" endtime="******** 15:07:53.277"/>
</kw>
<kw name="Exit For Loop If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<doc>Stops executing the enclosing FOR loop if the ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:07:53.277" endtime="******** 15:07:53.277"/>
</kw>
<kw name="Select from dropdown" library="Navigation">
<arg>${LANGUAGE_DROPDOWN}</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:07:53.278" endtime="******** 15:07:53.278"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:07:55.280" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:07:53.279" endtime="******** 15:07:55.280"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:07:55.280" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="language"]'.</msg>
<status status="PASS" starttime="******** 15:07:55.280" endtime="******** 15:07:55.335"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:08:00.336" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 15:07:55.335" endtime="******** 15:08:00.336"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:08:00.336" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 15:08:00.336" endtime="******** 15:08:00.336"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:08:00.336" endtime="******** 15:08:00.337"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:08:00.337" level="INFO">${path_string} = //span[text()=' Afrikaans </msg>
<status status="PASS" starttime="******** 15:08:00.337" endtime="******** 15:08:00.337"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:08:00.337" endtime="******** 15:08:00.338"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:08:00.338" level="INFO">${path_string} = //span[text()=' Afrikaans ']</msg>
<status status="PASS" starttime="******** 15:08:00.338" endtime="******** 15:08:00.338"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:08:00.339" endtime="******** 15:08:00.339"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:08:00.339" endtime="******** 15:08:00.370"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:08:00.370" level="INFO">Clicking element 'xpath=//span[text()=' Afrikaans ']'.</msg>
<status status="PASS" starttime="******** 15:08:00.370" endtime="******** 15:08:00.412"/>
</kw>
<status status="PASS" starttime="******** 15:07:53.277" endtime="******** 15:08:00.412"/>
</kw>
<kw name="Get Path" library="Utility">
<var>${image_directory}</var>
<msg timestamp="******** 15:08:00.413" level="INFO">${image_directory} = C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA</msg>
<status status="PASS" starttime="******** 15:08:00.413" endtime="******** 15:08:00.413"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${final_image_path}</var>
<arg>SEPARATOR=\\</arg>
<arg>${image_directory}</arg>
<arg>future_fit_architecture_portal</arg>
<arg>${campaign_images[${counter}].strip()}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:08:00.414" level="INFO">${final_image_path} = C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\images\MarketingA_af_2.jpg</msg>
<status status="PASS" starttime="******** 15:08:00.413" endtime="******** 15:08:00.414"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Image path is : ${campaign_images[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:08:00.414" endtime="******** 15:08:00.416"/>
</kw>
<kw name="Choose File" library="SeleniumLibrary">
<arg>xpath://input[@value='select' and @class='ng-star-inserted']</arg>
<arg>${final_image_path}</arg>
<doc>Inputs the ``file_path`` into the file input field ``locator``.</doc>
<msg timestamp="******** 15:08:00.416" level="INFO">Sending C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\images\MarketingA_af_2.jpg to browser.</msg>
<status status="PASS" starttime="******** 15:08:00.416" endtime="******** 15:08:00.456"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:08:03.457" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 15:08:00.456" endtime="******** 15:08:03.457"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>UploadMarkrtingScreen.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:08:03.640" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="UploadMarkrtingScreen.png"&gt;&lt;img src="UploadMarkrtingScreen.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:08:03.457" endtime="******** 15:08:03.640"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>${counter}+1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:08:03.640" level="INFO">${counter} = 0+1+1</msg>
<status status="PASS" starttime="******** 15:08:03.640" endtime="******** 15:08:03.640"/>
</kw>
<status status="PASS" starttime="******** 15:07:53.275" endtime="******** 15:08:03.640"/>
</iter>
<status status="PASS" starttime="******** 15:07:42.903" endtime="******** 15:08:03.640"/>
</for>
<status status="PASS" starttime="******** 15:07:42.897" endtime="******** 15:08:03.640"/>
</kw>
<kw name="And The user saves edited the campaign" library="Edit_Campaign">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>xpath=//button[@class='mat-focus-indicator mat-stepper-next mat-button mat-button-base']//span[text()='Save']</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 15:08:03.641" endtime="******** 15:08:03.664"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[@class='mat-focus-indicator mat-stepper-next mat-button mat-button-base']//span[text()='Save']</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:08:03.664" level="INFO">Clicking element 'xpath=//button[@class='mat-focus-indicator mat-stepper-next mat-button mat-button-base']//span[text()='Save']'.</msg>
<status status="PASS" starttime="******** 15:08:03.664" endtime="******** 15:08:03.913"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:08:04.309" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-588.png"&gt;&lt;img src="selenium-screenshot-588.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:08:03.913" endtime="******** 15:08:04.309"/>
</kw>
<status status="PASS" starttime="******** 15:08:03.640" endtime="******** 15:08:04.309"/>
</kw>
<status status="PASS" starttime="******** 15:06:49.205" endtime="******** 15:08:04.309"/>
</kw>
<kw name="User logs out" library="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 15:08:04.312" endtime="******** 15:08:05.784"/>
</kw>
<status status="PASS" starttime="******** 15:08:04.311" endtime="******** 15:08:05.784"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:08:05.785" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<status status="PASS" starttime="******** 15:08:05.785" endtime="******** 15:08:05.831"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:08:08.832" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 15:08:05.831" endtime="******** 15:08:08.832"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:08:08.832" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg timestamp="******** 15:08:09.002" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-589.png"&gt;&lt;img src="selenium-screenshot-589.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 15:08:09.002" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<status status="FAIL" starttime="******** 15:08:08.832" endtime="******** 15:08:09.065"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:08:11.065" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:08:09.065" endtime="******** 15:08:11.065"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="******** 15:08:11.065" endtime="******** 15:08:13.531"/>
</kw>
<status status="FAIL" starttime="******** 15:08:05.784" endtime="******** 15:08:13.531"/>
</kw>
<status status="PASS" starttime="******** 15:08:05.784" endtime="******** 15:08:13.531"/>
</kw>
<status status="PASS" starttime="******** 15:08:04.311" endtime="******** 15:08:13.531"/>
</kw>
<doc>Campaign Export</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" starttime="******** 15:06:49.205" endtime="******** 15:08:13.532"/>
</test>
<doc>Testing Camapaign Approval</doc>
<status status="PASS" starttime="******** 15:06:47.797" endtime="******** 15:08:13.534"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 15:06:47.794" level="ERROR">Taking listener 'future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg timestamp="******** 15:06:49.136" level="ERROR">Error in file 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\common\SetEnvironmentVariales.robot' on line 11: Importing library 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg timestamp="******** 15:06:49.770" level="WARN">There was error during termination of process</msg>
<msg timestamp="******** 15:07:01.911" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:07:13.110" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:07:18.130" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
