*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    EMAIL MANAGEMENT
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Email management - Add,update and delete email vendor 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot    
Resource                                            ../keywords/qrcode_cc/EmailManagement.robot 
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Keyword ***
Email management

    [Arguments]    ${DOCUMENTATION}    ${TESTRAIL_TESTCASE_ID}    ${VENDOR}    ${EMAIL}    ${NAME}    ${UPDATED_EMAIL}
    Set Test Documentation    ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe
   
    And The user clicks navigate to email management link    ${VENDOR}    ${EMAIL}    ${TESTRAIL_TESTCASE_ID}    ${NAME}    ${UPDATED_EMAIL}

    Sleep  2s

    And User logs out
    
| *Test Case*                                                                               |    *DOCUMENTATION*                                    |    *TESTRAIL_TESTCASE_ID*    |   *VENDOR*        |   *EMAIL*                      | *NAME*            | *UPDATED_EMAIL*             |
| Email management - Add,update and delete email vendor              | Email management     | Email management - Add,update and delete email vendor |     101597680                 | BMS               | <EMAIL>    |  Mbalentle        | <EMAIL> |
| Email management - negative scenario for adding a new vendor email | Email management     | Email management - Add,update and delete email vendor |     101597683                 | BMS               | <EMAIL>    |  M                | <EMAIL> |