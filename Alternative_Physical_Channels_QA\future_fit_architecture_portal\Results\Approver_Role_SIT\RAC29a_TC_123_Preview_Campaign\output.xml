<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:00:16.036170" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_123_Preview_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.131531" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:00:17.131531" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.132528" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:00:17.132528" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.132528" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:00:17.132528" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.132528" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:00:17.132528" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.132528" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:00:17.132528" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:17.130530" elapsed="0.001998"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_123_FFT_Preview_Campaign" line="36">
<kw name="Validating the Preview function on Approval Page preview">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.134846" level="INFO">Set test documentation to:
Close Button on Campaign Approvals Preview</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:00:17.133527" elapsed="0.001319"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.236833" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:00:17.134846" elapsed="0.101987"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:17.237838" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:17.237838" elapsed="0.001374"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.240213" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:00:17.240213" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:00:17.240213" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:00:17.240213" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:00:17.240213" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.241210" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:00:17.241210" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:17.241210" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.276072" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:00:17.608034" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T12:00:17.608034" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:00:17.241210" elapsed="0.366824"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.608034" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:17.608034" elapsed="0.000994"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:00:17.608034" elapsed="0.000994"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:00:17.240213" elapsed="0.368815"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.609028" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:00:17.609028" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:00:17.610031" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:00:17.609028" elapsed="0.001003"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:00:17.610031" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:00:17.610031" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:00:17.611027" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000996"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000996"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:00:17.610031" elapsed="0.000996"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.611027" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:00:17.611027" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.611027" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002679DBD0F50&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:00:17.611027" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:00:17.611027" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:00:17.611027" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:00:17.611027" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:00:17.611027" elapsed="0.001001"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.612028" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:00:17.612028" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:00:17.612028" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:00:17.612028" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:17.612028" elapsed="0.001005"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:00:17.613033" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:00:17.613033" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:00:17.613033" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:00:17.613033" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.614030" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:00:17.614030" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:00:17.614030" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:00:17.134846" elapsed="33.586683"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:00:50.721529" elapsed="0.018279"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:50.746824" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="09be370afb491003cda558b0aec6d246", element="f.B9A4074DAB7426735DC4565F56FF4641.d.9E2006EBA925D1A4EE259C71F6AE0243.e.152")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:50.739808" elapsed="0.007016"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:50.746824" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="09be370afb491003cda558b0aec6d246", element="f.B9A4074DAB7426735DC4565F56FF4641.d.9E2006EBA925D1A4EE259C71F6AE0243.e.152")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:50.746824" elapsed="0.026225"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:00:50.773049" elapsed="0.009100"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:50.773049" elapsed="0.009100"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:50.788691" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="09be370afb491003cda558b0aec6d246", element="f.B9A4074DAB7426735DC4565F56FF4641.d.9E2006EBA925D1A4EE259C71F6AE0243.e.153")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:50.783178" elapsed="0.006511"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:00:55.789940" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:00:50.789689" elapsed="5.000770"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:00:55.790814" elapsed="0.020449"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:55.811263" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="09be370afb491003cda558b0aec6d246", element="f.B9A4074DAB7426735DC4565F56FF4641.d.9E2006EBA925D1A4EE259C71F6AE0243.e.153")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:55.811263" elapsed="0.075117"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:00:55.886380" elapsed="0.444045"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:55.886380" elapsed="0.444045"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:00:56.330425" elapsed="0.004998"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:56.330425" elapsed="0.004998"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.345563" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.335423" elapsed="0.010140"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:50.721529" elapsed="5.624034"/>
</kw>
<kw name="Then The user navigates to the Campaign Approvals page and previews a single campaign" owner="Approvals">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.345563" level="INFO">${TOTAL_CAMPAIGNS} = 0</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:00:56.345563" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.346564" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:00:56.353558" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.346564" elapsed="0.006994"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${campaigns} == 0</arg>
<arg>Fail</arg>
<arg>No campaigns found to preview.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:00:56.353558" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.354576" level="INFO">${TOTAL_CAMPAIGNS} = 10</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:00:56.354576" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.354576" level="INFO">Total campaigns available: 10</msg>
<arg>Total campaigns available: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.354576" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns available: ${TOTAL_CAMPAIGNS}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.354576" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:00:56.355559" elapsed="0.013437"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.368996" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.368996" elapsed="0.073890"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.443881" level="INFO">Previewed the first campaign successfully.</msg>
<arg>Previewed the first campaign successfully.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.443881" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Previewed the first campaign successfully.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.443881" elapsed="0.001002"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.444883" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.471779" level="INFO">${Campaign_Name} = SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<var>${Campaign_Name}</var>
<arg>xpath=//input[@name='campaignName']</arg>
<arg>value</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.444883" elapsed="0.026896"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.471779" level="INFO">Length is 49.</msg>
<arg>${Campaign_Name}</arg>
<arg>Campaign name should not be empty.</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-29T12:00:56.471779" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.471779" level="INFO">Campaign Name: SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<arg>Campaign Name: ${Campaign_Name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.471779" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Campaign Name: ${Campaign_Name}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.471779" elapsed="0.001263"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.473042" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.491654" level="INFO">${campaign_by} = Thabo Benjamin Setuke (ZA)</msg>
<var>${campaign_by}</var>
<arg>xpath=//input[@name='campaignBy']</arg>
<arg>value</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.473042" elapsed="0.018612"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.492652" level="INFO">Length is 26.</msg>
<arg>${campaign_by}</arg>
<arg>Campaign owner should not be empty.</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-29T12:00:56.492652" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.492652" level="INFO">Campaign By: Thabo Benjamin Setuke (ZA)</msg>
<arg>Campaign By: ${campaign_by}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.492652" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Campaign By: ${campaign_by}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.492652" elapsed="0.001019"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.493671" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.511791" level="INFO">${Region_Target} = NO</msg>
<var>${Region_Target}</var>
<arg>xpath=//input[@name='marketingType']</arg>
<arg>value</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.493671" elapsed="0.018120"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.512790" level="INFO">Length is 2.</msg>
<arg>${Region_Target}</arg>
<arg>Region target should not be empty.</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-29T12:00:56.511791" elapsed="0.000999"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.512790" level="INFO">Region Target: NO</msg>
<arg>Region Target: ${Region_Target}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.512790" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Region Target: ${Region_Target}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.512790" elapsed="0.000000"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.512790" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.532266" level="INFO">${Target_Details} = ---Generic---</msg>
<var>${Target_Details}</var>
<arg>xpath=//input[@name='screenNumber']</arg>
<arg>value</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.512790" elapsed="0.019476"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.532266" level="INFO">Length is 13.</msg>
<arg>${Target_Details}</arg>
<arg>Target details should not be empty.</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-29T12:00:56.532266" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.532266" level="INFO">Target Details: ---Generic---</msg>
<arg>Target Details: ${Target_Details}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.532266" elapsed="0.000987"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Target Details: ${Target_Details}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.533253" elapsed="0.000000"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:56.533253" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.550793" level="INFO">${Receiver_Device_Type} = ATM</msg>
<var>${Receiver_Device_Type}</var>
<arg>xpath=//input[@name='receiverDeviceType']</arg>
<arg>value</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:56.533253" elapsed="0.017540"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.550793" level="INFO">Length is 3.</msg>
<arg>${Receiver_Device_Type}</arg>
<arg>Receiver device type should not be empty.</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-29T12:00:56.550793" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:00:56.550793" level="INFO">Receiver Device Type: ATM</msg>
<arg>Receiver Device Type: ${Receiver_Device_Type}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:00:56.550793" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Receiver Device Type: ${Receiver_Device_Type}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:00:56.550793" elapsed="0.001017"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:00:56.551810" elapsed="1.315383"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:56.551810" elapsed="1.315383"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:00:57.867193" elapsed="0.016199"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:57.883392" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:00:57.883392" elapsed="0.055371"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:00:57.952761" level="INFO">Current page contains text 'Language:'.</msg>
<arg>Language:</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:00:57.938763" elapsed="0.013998"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.952995" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:00:57.952761" elapsed="5.000234"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:02.971914" level="INFO">${images} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="09be370afb491003cda558b0aec6d246", element="f.B9A4074DAB7426735DC4565F56FF4641.d.9E2006EBA925D1A4EE259C71F6AE0243.e.203")&gt;]</msg>
<var>${images}</var>
<arg>${CAMPAIGN_IMAGES_XPATH}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:01:02.954064" elapsed="0.017850"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.971914" level="INFO">Length is 1.</msg>
<msg time="2024-10-29T12:01:02.971914" level="INFO">${image_count} = 1</msg>
<var>${image_count}</var>
<arg>${images}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2024-10-29T12:01:02.971914" elapsed="0.001001"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.972915" level="INFO">Total number of images found: 1</msg>
<arg>Total number of images found: ${image_count}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.972915" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:02.987618" level="INFO">${language_text} = Language: English</msg>
<var>${language_text}</var>
<arg>${LANGUAGE_XPATH}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:01:02.972915" elapsed="0.014703"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.988147" level="INFO">Language for Image 0: Language: English</msg>
<arg>Language for Image ${index}: ${language_text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.988147" elapsed="0.000000"/>
</kw>
<var name="${index}">0</var>
<status status="PASS" start="2024-10-29T12:01:02.972915" elapsed="0.015232"/>
</iter>
<var>${index}</var>
<value>${image_count}</value>
<status status="PASS" start="2024-10-29T12:01:02.972915" elapsed="0.015232"/>
</for>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.988147" level="INFO">Campaign Name: SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<arg>Campaign Name: ${Campaign_Name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.988147" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.988147" level="INFO">Campaign By: Thabo Benjamin Setuke (ZA)</msg>
<arg>Campaign By: ${campaign_by}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.988147" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.988147" level="INFO">Region Target: NO</msg>
<arg>Region Target: ${Region_Target}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.988147" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.988147" level="INFO">Target Details: ---Generic---</msg>
<arg>Target Details: ${Target_Details}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.988147" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.989169" level="INFO">Receiver Device Type: ATM</msg>
<arg>Receiver Device Type: ${Receiver_Device_Type}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.989169" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.989169" level="INFO">Total number of images found: 1</msg>
<arg>Total number of images found: ${image_count}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.989169" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:02.989169" level="INFO">Image 0: Language: English</msg>
<arg>Image ${index}: ${language_text}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:02.989169" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CAMPAIGN PREVIEW DETAILS----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.989169" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Campaign Name: ${Campaign_Name}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.989169" elapsed="0.000984"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Campaign By: ${campaign_by}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.990153" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Region Target: ${Region_Target}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.990153" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Target Details: ${Target_Details}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.990153" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Receiver Device Type: ${Receiver_Device_Type}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.991152" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Total number of images found: ${image_count}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.991152" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>Image ${index}: ${language_text}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:02.991152" elapsed="0.000000"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//button[span[contains(text(),'Previous')]]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:01:02.991152" elapsed="0.272682"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:03.263834" level="INFO">Clicking element 'xpath=//button[span[contains(text(),'Previous')]]'.</msg>
<arg>xpath=//button[span[contains(text(),'Previous')]]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:01:03.263834" elapsed="0.029190"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:03.306446" level="INFO">Element 'xpath=//mat-dialog-container' is displayed.</msg>
<arg>xpath=//mat-dialog-container</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:01:03.293024" elapsed="0.013422"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:01:03.306446" elapsed="0.278354"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:01:03.585803" elapsed="0.014734"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:01:08.601974" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:01:03.600537" elapsed="5.001931"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:08.602774" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:01:08.602774" elapsed="0.040080"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CLOSED CAMPAIGN----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:08.642854" elapsed="0.001119"/>
</kw>
<status status="PASS" start="2024-10-29T12:00:56.345563" elapsed="12.298410"/>
</kw>
<arg>Close Button on Campaign Approvals Preview</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:00:17.133527" elapsed="51.510446"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:01:08.643973" elapsed="0.007740"/>
</kw>
<status status="PASS" start="2024-10-29T12:01:08.643973" elapsed="0.007740"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:08.652838" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:01:08.651713" elapsed="0.046091"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:01:11.698065" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:01:08.697804" elapsed="3.000261"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:01:11.698065" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:01:11.766823" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:01:11.766823" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:01:11.698065" elapsed="0.071756">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:01:13.770341" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:01:11.769821" elapsed="2.000520"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:01:13.770341" elapsed="3.153047"/>
</kw>
<status status="FAIL" start="2024-10-29T12:01:08.651713" elapsed="8.271675">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:01:08.651713" elapsed="8.271675"/>
</kw>
<status status="PASS" start="2024-10-29T12:01:08.643973" elapsed="8.279415"/>
</kw>
<doc>Close Button on Campaign Approvals Preview</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:00:17.132528" elapsed="59.791747"/>
</test>
<doc>Testing the Preview function on Campaign Approvals Page</doc>
<status status="PASS" start="2024-10-29T12:00:16.437949" elapsed="60.487835"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:00:16.028575" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_123_Preview_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:00:17.102715" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:00:17.608034" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T12:00:35.609028" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:00:45.620735" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:00:50.628423" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:00:56.346564" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:00:56.444883" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.473042" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.493671" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.512790" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
<msg time="2024-10-29T12:00:56.533253" level="WARN">Keyword 'Get Element Attribute' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Attribute' or 'XML.Get Element Attribute'.</msg>
</errors>
</robot>
