*** Settings ***
#Author Name               : TH<PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS User Edit
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${USER_NAME}      ${USER_ROLE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And Searches for existing user  ${USER_NAME}
    And Updates the VMS User's role   ${USER_NAME}       ${USER_ROLE}
    And The user navigates to Admin - User Management
    And Searches for existing user  ${USER_NAME}
    Then The user's role must be updated with the expected role     ${USER_NAME}       ${USER_ROLE}



| *Test Case*                                                                                         |      *DOCUMENTATION*                                | *TEST_ENVIRONMENT*   |  *USER_NAME*    |  *USER_ROLE*      |
# | Validate Update Link - User Management: Update the VMS user, change the role to of the user to 'User'.          | VMS User Edit             | Updates a VMS user to have a 'User' Role            |    VMS_UAT           |    AB0540to     |    User           |
# | Validate Update Link - User Management: Update the VMS user, change the role to the user to 'Browse'.        | VMS User Edit             | Updates a VMS user to have a 'Browse' Role          |    VMS_UAT           |    AB0541to     |    Browse         |
| Validate Update Link - User Management: Update the VMS user, change the role of the user to 'Administrator'. | VMS User Edit             | Updates a VMS user to have a 'Administrator' Role   |    VMS_UAT           |    AB0542to     |    Administrator  |
# | Validate Update Link - User Management: Update the VMS user, change the role to of the user to 'Supervisor'.    | VMS User Edit             | Updates a VMS user to have a 'Supervisor' Role      |    VMS_UAT           |    AB0543to     |    Supervisor     |
