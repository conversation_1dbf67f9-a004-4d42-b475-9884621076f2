<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T11:54:22.937243" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_124_Preview_Campaign_Close_Button.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.145992" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:54:24.144971" elapsed="0.001021"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.145992" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:54:24.145992" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.145992" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:54:24.145992" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.145992" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:54:24.145992" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.145992" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:54:24.145992" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T11:54:24.144971" elapsed="0.001021"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_124_FFT_Preview_Campaign_Close_Button" line="36">
<kw name="Validating the Close Button on Approval Page preview">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.147972" level="INFO">Set test documentation to:
Close Button on Campaign Approvals Preview</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T11:54:24.146986" elapsed="0.000986"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.262621" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:54:24.147972" elapsed="0.114649"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:54:24.262621" elapsed="0.000999"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:54:24.263620" elapsed="0.001001"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.265620" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:54:24.265620" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T11:54:24.265620" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:54:24.265620" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T11:54:24.265620" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.266620" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T11:54:24.266620" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:54:24.266620" elapsed="0.001000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.305625" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T11:54:24.646991" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T11:54:24.646991" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T11:54:24.267620" elapsed="0.379371"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.648024" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:54:24.648024" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:54:24.648024" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T11:54:24.265620" elapsed="0.383372"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.648992" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T11:54:24.648992" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T11:54:24.648992" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T11:54:24.648992" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T11:54:24.648992" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T11:54:24.648992" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T11:54:24.648992" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-29T11:54:24.648992" elapsed="0.001015"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.650007" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.650007" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000028CF4674E90&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:54:24.650007" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T11:54:24.651008" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T11:54:24.651008" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:54:24.651008" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T11:54:24.651008" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.651008" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T11:54:24.651008" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:54:24.651008" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:54:24.651990" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:54:24.651990" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:54:24.651990" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:54:24.651990" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:54:24.651990" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:54:24.653050" elapsed="0.000074"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.653306" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:54:24.653124" elapsed="0.000182"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T11:54:24.653306" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T11:54:24.147972" elapsed="33.690252"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:54:57.839374" elapsed="0.018016"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T11:54:57.864758" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="1756bd95a47b592ad2603c276fa75136", element="f.4B12DAFC6024D9A9B589C8594D0D1A63.d.112D8C9DF3AF9854410FA050EE54842E.e.146")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:54:57.858547" elapsed="0.006211"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:54:57.864758" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="1756bd95a47b592ad2603c276fa75136", element="f.4B12DAFC6024D9A9B589C8594D0D1A63.d.112D8C9DF3AF9854410FA050EE54842E.e.146")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:54:57.864758" elapsed="0.030423"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:54:57.896545" elapsed="0.008210"/>
</kw>
<status status="PASS" start="2024-10-29T11:54:57.896545" elapsed="0.008210"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T11:54:57.911744" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="1756bd95a47b592ad2603c276fa75136", element="f.4B12DAFC6024D9A9B589C8594D0D1A63.d.112D8C9DF3AF9854410FA050EE54842E.e.147")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:54:57.904755" elapsed="0.006989"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:02.912538" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:54:57.911744" elapsed="5.000794"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T11:55:02.913157" elapsed="0.015162"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:02.929324" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="1756bd95a47b592ad2603c276fa75136", element="f.4B12DAFC6024D9A9B589C8594D0D1A63.d.112D8C9DF3AF9854410FA050EE54842E.e.147")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:02.928319" elapsed="0.072471"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:55:03.000790" elapsed="0.660760"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:03.000790" elapsed="0.661760"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:55:03.662550" elapsed="0.007003"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:03.662550" elapsed="0.007003"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:03.680566" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T11:55:03.669553" elapsed="0.011013"/>
</kw>
<status status="PASS" start="2024-10-29T11:54:57.839374" elapsed="5.841192"/>
</kw>
<kw name="Then The user previews and validates the Close Button on Approval Preview" owner="Approvals">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:55:03.680566" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:55:03.680566" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:55:03.681565" level="INFO">${TOTAL_CAMPAIGNS} = 0</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:55:03.681565" elapsed="0.000000"/>
</kw>
<while condition="${more_pages} == True">
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:03.689548" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:03.681565" elapsed="0.007983"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:55:03.690568" level="INFO">${TOTAL_CAMPAIGNS} = 10</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:55:03.689548" elapsed="0.001020"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:55:03.690568" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:55:03.690568" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:03.690568" level="INFO">Current page campaigns: 10 | Total so far: 10</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:03.690568" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:03.691565" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:03.690568" elapsed="0.073517"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:55:03.765319" elapsed="0.304361"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:55:04.069680" elapsed="0.021675"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:09.092704" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:04.092372" elapsed="5.000332"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:09.092704" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:09.092704" elapsed="0.046794"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CLOSE BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:55:09.139498" elapsed="0.000998"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-29T11:55:03.690568" elapsed="5.449928"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-29T11:55:03.690568" elapsed="5.449928"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:55:09.140496" elapsed="0.017848"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:09.174183" level="INFO">${is_disabled} = None</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:09.158344" elapsed="0.015839"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:55:09.175201" level="INFO">${is_enabled} = True</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:55:09.174183" elapsed="0.001018"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:09.175201" level="INFO">Next page button enabled: True</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:09.175201" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-29T11:55:09.175201" elapsed="0.000000"/>
</return>
<msg time="2024-10-29T11:55:09.175201" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-29T11:55:09.140496" elapsed="0.034705"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:55:09.175201" elapsed="0.015000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:14.191347" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:09.191201" elapsed="5.000146"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:55:14.191863" elapsed="0.283516"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:55:14.476480" elapsed="0.285550"/>
</kw>
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:55:14.475379" elapsed="0.286651"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Next Page Button" owner="Approvals">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:14.764022" level="INFO">Executing JavaScript:
document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()
Without any arguments.</msg>
<arg>document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-29T11:55:14.763026" elapsed="0.014229"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:14.763026" elapsed="0.015006"/>
</kw>
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:55:14.762030" elapsed="0.016002"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:16.778525" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:14.778032" elapsed="2.000493"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:03.681565" elapsed="13.096960"/>
</iter>
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:16.788306" level="INFO">${campaigns} = 6</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:16.779634" elapsed="0.008672"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:55:16.789425" level="INFO">${TOTAL_CAMPAIGNS} = 16</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:55:16.788306" elapsed="0.001119"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:55:16.789425" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:55:16.789425" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:16.789425" level="INFO">Current page campaigns: 6 | Total so far: 16</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:16.789425" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:16.790314" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:16.789425" elapsed="0.137974"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:55:16.927399" elapsed="0.314029"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:55:17.241428" elapsed="0.016702"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:22.258746" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:17.258130" elapsed="5.000616"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:22.258746" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:22.258746" elapsed="0.041198"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CLOSE BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:55:22.299944" elapsed="0.000998"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-29T11:55:16.789425" elapsed="5.511517"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-29T11:55:16.789425" elapsed="5.511517"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:55:22.300942" elapsed="0.012118"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:22.325943" level="INFO">${is_disabled} = true</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:22.313060" elapsed="0.012883"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:55:22.325943" level="INFO">${is_enabled} = False</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:55:22.325943" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:22.325943" level="INFO">Next page button enabled: False</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:22.325943" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-29T11:55:22.325943" elapsed="0.000000"/>
</return>
<msg time="2024-10-29T11:55:22.325943" level="INFO">${more_pages} = False</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-29T11:55:22.300942" elapsed="0.025001"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:55:22.326943" elapsed="0.012895"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:27.341015" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:22.340840" elapsed="5.000175"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:55:27.341636" elapsed="0.281430"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:55:27.624087" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:55:27.624087" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:29.625465" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:27.624087" elapsed="2.001378"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:16.778525" elapsed="12.846940"/>
</iter>
<status status="PASS" start="2024-10-29T11:55:03.681565" elapsed="25.943900"/>
</while>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:29.625976" level="INFO">Total campaigns identified on the front-end: 16</msg>
<arg>Total campaigns identified on the front-end: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:29.625976" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:55:29.625976" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:55:29.626982" level="INFO">${db_type} = 'MYSQL'</msg>
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:55:29.626982" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:55:29.626982" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY} = SELECT COUNT(*) FROM ATM_Marketing.Campaign WHERE isActive = '1';</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:55:29.626982" elapsed="0.000000"/>
</kw>
<kw name="Execute SQL Query" owner="DBUtility">
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-29T11:55:29.627979" level="INFO">${return_data} = True</msg>
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-29T11:55:29.626982" elapsed="0.000997"/>
</kw>
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-29T11:55:29.627979" level="INFO">${return_all} = False</msg>
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-29T11:55:29.627979" elapsed="0.000000"/>
</kw>
<kw name="Verify Data Using Database" owner="DatabaseUtility">
<msg time="2024-10-29T11:55:30.214452" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg time="2024-10-29T11:55:30.214452" level="INFO">${data_found} = {'COUNT(*)': 16}</msg>
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<status status="PASS" start="2024-10-29T11:55:29.627979" elapsed="0.586473"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T11:55:30.215448" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T11:55:30.215448" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T11:55:30.216442" elapsed="0.000997"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" start="2024-10-29T11:55:30.217439" elapsed="0.000000"/>
</return>
<msg time="2024-10-29T11:55:30.217439" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = {'COUNT(*)': 16}</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${db_type}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<arg>True</arg>
<status status="PASS" start="2024-10-29T11:55:29.626982" elapsed="0.590457"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2024-10-29T11:55:30.217439" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = 16</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<arg>COUNT(*)</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2024-10-29T11:55:30.217439" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:30.218439" level="INFO">Total campaigns on the approval list (database): 16</msg>
<arg>Total campaigns on the approval list (database): ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:30.218439" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns on the database approval list: ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:55:30.218439" elapsed="0.000746"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:55:30.219185" level="INFO">Test completed. The Close button on the approval preview functions as expected.</msg>
<arg>Test completed. The Close button on the approval preview functions as expected.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:55:30.219185" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Test completed. The Close button on the approval preview functions as expected.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:55:30.219185" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${TOTAL_CAMPAIGNS}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2024-10-29T11:55:30.219185" elapsed="0.001013"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----The front-end and database campaign counts match----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:55:30.220198" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:03.680566" elapsed="26.539632"/>
</kw>
<arg>Close Button on Campaign Approvals Preview</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T11:54:24.146986" elapsed="66.073212"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:55:30.220868" elapsed="0.007005"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:30.220868" elapsed="0.008005"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:30.230357" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:55:30.228873" elapsed="0.033896"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:33.263981" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:30.263771" elapsed="3.000210"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:55:33.263981" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T11:55:33.339504" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T11:55:33.340504" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T11:55:33.263981" elapsed="0.078522">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:55:35.343770" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:55:33.342503" elapsed="2.001267"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T11:55:35.343770" elapsed="3.165572"/>
</kw>
<status status="FAIL" start="2024-10-29T11:55:30.228873" elapsed="8.280609">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T11:55:30.228873" elapsed="8.280609"/>
</kw>
<status status="PASS" start="2024-10-29T11:55:30.220868" elapsed="8.288614"/>
</kw>
<doc>Close Button on Campaign Approvals Preview</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T11:54:24.146986" elapsed="74.363499"/>
</test>
<doc>Testing the Close Button on Campaign Approvals Preview</doc>
<status status="PASS" start="2024-10-29T11:54:23.366361" elapsed="75.147109"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T11:54:22.928313" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_124_Preview_Campaign_Close_Button.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T11:54:24.105494" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T11:54:24.648024" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T11:54:42.737582" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T11:54:52.753961" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T11:54:57.764406" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
