from matplotlib import colors as mcolors
from math import sqrt
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from selenium.webdriver.edge.options import Options
from datetime import datetime
import os
import ast
import re




def extract_empty_strings_and_amounts(data):
    # Regex pattern to match amounts starting with 'R' and having a decimal point
    amount_pattern = r'R\s[\d,]+\.\d+'
    
    # Extract empty strings and amounts
    empty_strings = [item for item in data if item == '']
    amounts = [item for item in data if re.match(amount_pattern, item)]
    
    return empty_strings, amounts

def filter_details(data):
    # Regex pattern to match amounts starting with 'R' and having a decimal point
    # We will allow for potential spaces and commas in the amount.
    amount_pattern = r'R\s?[\d,]+(?:\.\d+)?'  # This regex should cover spaces, commas, and optional decimals
    
    # Filter out empty strings and amounts matching the pattern
    filtered_data = [item for item in data if item != '' and not re.match(amount_pattern, item)]
    
    return filtered_data


def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple."""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i + 2], 16) / 255.0 for i in (0, 2, 4))


def rgb_distance(rgb1, rgb2):
    """Calculate the Euclidean distance between two RGB colors."""
    return sqrt(sum((a - b) ** 2 for a, b in zip(rgb1, rgb2)))


def closest_color_name(hex_color):
    """Find the closest named color to the given hex color."""
    rgb_color = hex_to_rgb(hex_color)
    closest_name = None
    closest_distance = float('inf')

    # Retrieve named colors from matplotlib
    named_colors = mcolors.CSS4_COLORS  # or mcolors.XKCD_COLORS for a different set of colors

    for name, hex_code in named_colors.items():
        named_rgb = hex_to_rgb(hex_code)
        distance = rgb_distance(rgb_color, named_rgb)
        if distance < closest_distance:
            closest_distance = distance
            closest_name = name

    return closest_name


def get_chromedriver_path():
    # Automatically download and install the latest ChromeDriver
    chrome_install = ChromeDriverManager().install()
    folder = os.path.dirname(chrome_install)
    print(chrome_install)
    chromedriver_path = os.path.join(folder, "chromedriver.exe")
    return chromedriver_path


def get_edge_driver_path():
    """
    Installs the Microsoft Edge WebDriver and returns the path to the installed driver.
    """
    # Initialize the EdgeDriverManager to download and set up the Edge WebDriver
    edge_driver_manager = EdgeChromiumDriverManager()

    # Install the Edge WebDriver and get the path
    edge_driver_path = edge_driver_manager.install()

    return edge_driver_path


def get_edge_driver_options():
    # Initialize Edge options
    options = Options()

    # Set any desired options here
    # For example:
    options.add_argument('--headless')  # Run in headless mode
    options.add_argument('--disable-gpu')  # Disable GPU hardware acceleration

    # Return options for use in Robot Framework
    return options


def validate_integer(number):
    try:
        # Attempt to convert the parameter to an integer
        int_value = int(number)

        return "valid integer"
    except ValueError:
        return "integer not valid"


def check_element_in_list(string_list, element):
    try:
        actual_list = ast.literal_eval(string_list)
        return element in actual_list
    except (ValueError, SyntaxError):
        print('exception block')
        return False


def format_db_timestamp(timestamp):
    # Parse the timestamp to a datetime object
    dt = datetime.strptime(timestamp, "%Y-%m-%d %H:%M:%S.%f")
    # Format the datetime object to the desired string
    return dt.strftime("%d/%m/%Y %I:%M:%S %p")

def format_date(date_string):
    """Converts database date format to front-end format."""
    try:
        # Parse the datetime string from the database
        date_obj = datetime.strptime(date_string, '%Y-%m-%d %H:%M:%S.%f')
        # Format the date to match the front-end format (YYYY/MM/DD)
        return date_obj.strftime('%Y/%m/%d')
    except ValueError:
        return None

# driver_path = get_chromedriver_path()
# print(f"Microsoft Edge WebDriver installed at: {driver_path}")

# Example usage
# hex_color = "#118811"
# color_name = closest_color_name(hex_color)
# print(f"The closest color name for {hex_color} is {color_name}.")


