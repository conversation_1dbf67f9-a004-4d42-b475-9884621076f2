<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240905 08:11:42.218">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_02_UPDATE_EXISTING_USER.robot">
      <test name="Update the VMS user, change the role to of the user to 'User'." id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Updates a VMS user to have a 'User' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:11:44.538" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240905 08:12:23.710" status="PASS" starttime="20240905 08:11:44.538"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Updates a VMS user to have a 'User' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:12:23.710" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20240905 08:12:24.241" status="PASS" starttime="20240905 08:12:23.710"/>
         </kw>
         <kw library="Selenium" name="And Updates the user's role">
            <doc>Updates a VMS user to have a 'User' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:12:24.242" level="INFO">And Updates the user's role</msg>
            <status endtime="20240905 08:12:35.221" status="FAIL" starttime="20240905 08:12:24.242"/>
         </kw>
         <kw library="Selenium" name="Then The user's role must be updated with the expected role">
            <doc>Updates a VMS user to have a 'User' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:12:35.229" level="INFO">Then The user's role must be updated with the expected role</msg>
            <status endtime="20240905 08:12:35.229" status="NOT RUN" starttime="20240905 08:12:35.229"/>
         </kw>
         <tags>
            <tag>Update the VMS user, change the role to of the user to 'User'.</tag>
         </tags>
         <status endtime="20240905 08:12:48.067" critical="yes" status="FAIL" starttime="20240905 08:11:44.538"/>
      </test>
      <test name="Update the VMS user, change the role to of the user to 'Browse'." id="s1-t2">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Updates a VMS user to have a 'Browse' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:12:48.067" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240905 08:13:25.181" status="PASS" starttime="20240905 08:12:48.067"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Updates a VMS user to have a 'Browse' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:13:25.181" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20240905 08:13:26.238" status="PASS" starttime="20240905 08:13:25.181"/>
         </kw>
         <kw library="Selenium" name="And Updates the user's role">
            <doc>Updates a VMS user to have a 'Browse' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:13:26.238" level="INFO">And Updates the user's role</msg>
            <status endtime="20240905 08:13:37.291" status="FAIL" starttime="20240905 08:13:26.238"/>
         </kw>
         <kw library="Selenium" name="Then The user's role must be updated with the expected role">
            <doc>Updates a VMS user to have a 'Browse' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:13:37.292" level="INFO">Then The user's role must be updated with the expected role</msg>
            <status endtime="20240905 08:13:37.292" status="NOT RUN" starttime="20240905 08:13:37.292"/>
         </kw>
         <tags>
            <tag>Update the VMS user, change the role to of the user to 'Browse'.</tag>
         </tags>
         <status endtime="20240905 08:13:49.916" critical="yes" status="FAIL" starttime="20240905 08:12:48.067"/>
      </test>
      <test name="Update the VMS user, change the role to of the user to 'Administrator'." id="s1-t3">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Updates a VMS user to have a 'Administrator' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:13:49.919" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240905 08:14:25.058" status="PASS" starttime="20240905 08:13:49.919"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Updates a VMS user to have a 'Administrator' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:14:25.058" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20240905 08:14:25.877" status="PASS" starttime="20240905 08:14:25.058"/>
         </kw>
         <kw library="Selenium" name="And Updates the user's role">
            <doc>Updates a VMS user to have a 'Administrator' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:14:25.877" level="INFO">And Updates the user's role</msg>
            <status endtime="20240905 08:14:37.855" status="FAIL" starttime="20240905 08:14:25.877"/>
         </kw>
         <kw library="Selenium" name="Then The user's role must be updated with the expected role">
            <doc>Updates a VMS user to have a 'Administrator' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:14:37.855" level="INFO">Then The user's role must be updated with the expected role</msg>
            <status endtime="20240905 08:14:37.855" status="NOT RUN" starttime="20240905 08:14:37.855"/>
         </kw>
         <tags>
            <tag>Update the VMS user, change the role to of the user to 'Administrator'.</tag>
         </tags>
         <status endtime="20240905 08:14:50.742" critical="yes" status="FAIL" starttime="20240905 08:13:49.916"/>
      </test>
      <test name="Update the VMS user, change the role to of the user to 'Supervisor'." id="s1-t4">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Updates a VMS user to have a 'Supervisor' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:14:50.742" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240905 08:16:14.735" status="PASS" starttime="20240905 08:14:50.742"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Updates a VMS user to have a 'Supervisor' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:16:14.735" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20240905 08:16:16.034" status="PASS" starttime="20240905 08:16:14.735"/>
         </kw>
         <kw library="Selenium" name="And Updates the user's role">
            <doc>Updates a VMS user to have a 'Supervisor' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:16:16.034" level="INFO">And Updates the user's role</msg>
            <status endtime="20240905 08:16:28.172" status="FAIL" starttime="20240905 08:16:16.034"/>
         </kw>
         <kw library="Selenium" name="Then The user's role must be updated with the expected role">
            <doc>Updates a VMS user to have a 'Supervisor' Role</doc>
            <arguments>
               <arg>Test execution failed</arg>
            </arguments>
            <msg timestamp="20240905 08:16:28.172" level="INFO">Then The user's role must be updated with the expected role</msg>
            <status endtime="20240905 08:16:28.172" status="NOT RUN" starttime="20240905 08:16:28.172"/>
         </kw>
         <tags>
            <tag>Update the VMS user, change the role to of the user to 'Supervisor'.</tag>
         </tags>
         <status endtime="20240905 08:16:42.790" critical="yes" status="FAIL" starttime="20240905 08:14:50.742"/>
      </test>
      <status endtime="20240905 08:16:42.794" status="FAIL" starttime="20240905 08:11:42.218"/>
   </suite>
   <statistics>
      <total>
         <stat pass="0" fail="4">Critical Tests</stat>
         <stat pass="0" fail="4">All Tests</stat>
      </total>
      <tag>
         <stat pass="0" fail="1">Update the VMS user, change the role to of the user to 'User'.</stat>
         <stat pass="0" fail="1">Update the VMS user, change the role to of the user to 'Browse'.</stat>
         <stat pass="0" fail="1">Update the VMS user, change the role to of the user to 'Administrator'.</stat>
         <stat pass="0" fail="1">Update the VMS user, change the role to of the user to 'Supervisor'.</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="0" fail="4" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
