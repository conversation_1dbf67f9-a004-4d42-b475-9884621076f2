
class CreateRESTRequest:
    def __init__(self, domain, name, action):
        self.domain = domain
        self.name = name
        self.action = action

        self.params = {
            "Name": self.name,
            "Action": self.action,  # Adding a query parameter to filter the results by Bin Id
        }


    def get_endpoint(self):
        path = "/api/v1/bintables/admin/bintypes/searchbintypesbyname"
        url = f"{self.domain}{path}"
        return url


    def get_params(self):
        return self.params
