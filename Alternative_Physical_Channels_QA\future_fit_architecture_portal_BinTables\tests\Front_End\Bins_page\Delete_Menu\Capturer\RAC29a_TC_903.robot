*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Setup                                          The User gets a draft bin number for deletion process 
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-903




*** Keywords ***
Verifies that the delete button is enabled after the action date is selected
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The Bin Number is active in the Database                         ${BIN_NAME}
    When The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    And The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates a Bin Number    ${BIN_NAME}
    And The User selects an Action Date    ${BIN_ACTION_DATE}
    Then The user verifies that the delete button is enabled  
    

| *** Test Cases ***                                                                                                                                                  |        *DOCUMENTATION*           |         *BASE_URL*             |         *BIN_NAME*          |   *BIN_ACTION_DATE*    | 
| Capturer_Delete Bin Button Enabled After BIN is Found and Action Date Selected     | Verifies that the delete button is enabled after the action date is selected   |       Verifying Delete Button    |           ${EMPTY}             |  ${global_draft_bin_number} |     3/27/2025          |
