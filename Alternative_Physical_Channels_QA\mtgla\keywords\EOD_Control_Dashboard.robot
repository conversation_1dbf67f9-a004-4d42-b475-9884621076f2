*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Calendar Management Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                            RequestsLibrary
Library                                            Collections
Library                                            String
Library                                             DatabaseLibrary
Library                                          ../utility/DatabaseUtility.py
Library                                            DateTime

#***********************************PROJECT RESOURCES***************************************


*** Variables ***
#Tiles 
${OUT_OF_SLA_TABLE_TILE}            xpath=//div[contains(@class, 'w3-col s8 w3-container w3-padding')]
${FLAGGED_INFO_TILE}                xpath=//span[contains(text(),'Flagged Information')]/parent::div

#Regions
${ALL_REGIONS}                      xpath=//td[contains(@class, 'w3-button') and @onclick and not(descendant::*)]
${EASTERN_CAPE_REGION}              xpath=//*[text()[normalize-space(.)='Eastern Cape']]
${FREE_STATE_REGION}                xpath=//*[text()[normalize-space(.)='Free State']]
${GAUTENG_NORTH_REGION}             xpath=//*[text()[normalize-space(.)='Gauteng North']]
${GAUTENG_SOUTH_REGION}             xpath=//*[text()[normalize-space(.)='Gauteng South']]
${KWA_ZULU_NATAL_REGION}            xpath=//*[text()[normalize-space(.)='Kwazulu Natal']]
${LIMPOPO_REGION}                   xpath=//*[text()[normalize-space(.)='Limpopo']]
${MPUMALANGA_REGION}                xpath=//*[text()[normalize-space(.)='Mpumalanga']]
${NORTH_WEST_REGION}                xpath=//*[text()[normalize-space(.)='North West']]
${NORTHERN_CAPE_REGION}             xpath=//*[text()[normalize-space(.)='Northern Cape']]
${WESTERN_CAPE_REGION}              xpath=//*[text()[normalize-space(.)='Western Cape']]

${ALL_COST_CENTRES}                 xpath=//tbody[contains(@class, '')]//tr[@class='w3-medium']//td[@class='w3-button']
${ALL_ACCOUNTS}                     xpath=//tr[contains(@class, 'w3-small')]//td/a
${EASTERN_CAPE_COST_CENTRES}        xpath=//tbody[contains(@class, 'Eastern Cape')]//tr[@class='w3-medium']//td[@class='w3-button']


*** Keywords ***
the user verifies the Out of SLA Information displayed  
    #Extract In SLA Label and Value
    Wait Until Element Is Visible    ${OUT_OF_SLA_TABLE_TILE}//div[contains(text(),'In SLA :')]/following-sibling::div

    ${IN_SLA_LABEL}    Get Text    ${OUT_OF_SLA_TABLE_TILE}//div[contains(text(),'In SLA :')]
    ${IN_SLA_VALUE}    Get Text    ${OUT_OF_SLA_TABLE_TILE}//div[contains(text(),'In SLA :')]/following-sibling::div
    Log    Label:${IN_SLA_LABEL}
    Log    Value:${IN_SLA_VALUE}
    ${IN_SLA_RESULT}    Set Variable    ${IN_SLA_LABEL} ${IN_SLA_VALUE}
    Log    ${IN_SLA_RESULT}

    #Extract Out of SLA
    ${OUT_SLA_LABEL}    Get Text    ${OUT_OF_SLA_TABLE_TILE}//div[contains(text(),'Out of SLA :')]
    ${OUT_SLA_VALUE}    Get Text    ${OUT_OF_SLA_TABLE_TILE}//div[contains(text(),'Out of SLA :')]/following-sibling::div
    ${OUT_SLA_RESULT}    Set Variable    ${OUT_SLA_LABEL} ${OUT_SLA_VALUE}
    Log    ${OUT_SLA_RESULT}

    #${ALL_DIVS}    Get WebElements    ${OUT_OF_SLA_TABLE_TILE}//div
        #FOR    ${ELEMENT}    IN    @{ALL_DIVS}
            #${TEXT}    Get Text    ${ELEMENT}
            #Log    ${TEXT}
        #END

    ${TOTAL_LABEL}    Get Text     (//*[text()[normalize-space(.)='Total :']])[1]
    ${TOTAL_VALUE}    Get Text    (//*[text()[normalize-space(.)='Total :']])[1]/following::div[1]
    ${TOTAL_RESULT}    Set Variable    ${TOTAL_LABEL} ${TOTAL_VALUE}
    Log    ${TOTAL_RESULT}

    Log    ${IN_SLA_RESULT}
    Log    ${OUT_SLA_RESULT}
    Log    ${TOTAL_RESULT}


    #Database Validation 


the user verifies the Flagged Information displayed  
    ${COMPLETE_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='Complete :']]
    ${COMPLETE_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='Complete :']]/following-sibling::div
    ${COMPLETE_RESULT}    Set Variable    ${COMPLETE_LABEL} ${COMPLETE_VALUE}

    ${NOT_COMPLETE_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='Not Complete :']]
    ${NOT_COMPLETE_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='Not Complete :']]/following-sibling::div
    ${NOT_COMPLETE_RESULT}    Set Variable    ${NOT_COMPLETE_LABEL} ${NOT_COMPLETE_VALUE}

    ${TOTAL_LABEL}    Get Text    (//*[text()[normalize-space(.)='Total :']])[2]
    ${TOTAL_VALUE}    Get Text    (//*[text()[normalize-space(.)='Total :']])[2]/following::div[1]
    ${TOTAL_RESULT}    Set Variable    ${TOTAL_LABEL} ${TOTAL_VALUE}

    Log    ${COMPLETE_RESULT}
    Log    ${NOT_COMPLETE_RESULT}
    Log    ${TOTAL_RESULT}

    
the user verifies the Attestation Tile displayed
    ${NEW_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='New :']]
    ${NEW_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='New :']]/following-sibling::div
    ${NEW_RESULT}    Set Variable    ${NEW_LABEL} ${NEW_VALUE}

    ${NO_RECON_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='No Recon Required :']]
    ${NO_RECON_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='No Recon Required :']]/following-sibling::div
    ${NO_RECON_RESULT}    Set Variable    ${NO_RECON_LABEL} ${NO_RECON_VALUE}

    ${PREPARED_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='Prepared :']]
    ${PREPARED_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='Prepared :']]/following-sibling::div
    ${PREPARED_RESULT}    Set Variable    ${PREPARED_LABEL} ${PREPARED_VALUE}

    ${REVIEWED_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='Reviewed :']]
    ${REVIEWED_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='Reviewed :']]/following-sibling::div
    ${REVIEWED_RESULT}    Set Variable    ${REVIEWED_LABEL} ${REVIEWED_VALUE}

    ${CONTROLLED_LABEL}    Get Text    xpath=//*[text()[normalize-space(.)='Controlled :']]
    ${CONTROLLED_VALUE}    Get Text    xpath=//*[text()[normalize-space(.)='Controlled :']]/following-sibling::div
    ${CONTROLLED_RESULT}    Set Variable    ${CONTROLLED_LABEL} ${CONTROLLED_VALUE}

    ${TOTAL_LABEL}    Get Text    xpath=(//*[text()[normalize-space(.)='Total :']])[3]
    ${TOTAL_VALUE}    Get Text    xpath=(//*[text()[normalize-space(.)='Total :']])[3]/following::div[1]
    ${TOTAL_RESULT}    Set Variable    ${TOTAL_LABEL} ${TOTAL_VALUE}
    
    Log    ${NEW_RESULT}
    Log    ${NO_RECON_RESULT}
    Log    ${PREPARED_RESULT}
    Log    ${REVIEWED_RESULT}
    Log    ${CONTROLLED_RESULT}
    Log    ${TOTAL_RESULT}

the user verifies the Regions Displayed on EOD Dashboard
    ${REGION_LIST}    Create List
    ${REGION_ELEMENTS}    Get WebElements    ${ALL_REGIONS}

    FOR    ${ELEMENT}    IN    @{REGION_ELEMENTS}
        ${REGION_NAME}    Get Text    ${ELEMENT}
        Append To List    ${REGION_LIST}    ${REGION_NAME}
    END

    Log Many    ${REGION_LIST}

the user verifies that the regions can be expanded to display the cost centres
    @{REGION_ELEMENTS}    Get WebElements    ${ALL_REGIONS}

    FOR    ${ELEMENT}    IN    @{REGION_ELEMENTS}
        #Sleep    3s
        Click Element    ${ELEMENT}
        ${REGION}=    Get Text    ${ELEMENT}
        Log    Clicked Region:${REGION}
        #Sleep    3s

        #Get all cost centres displayed after clicking a region
        @{COST_CENTRE_ELEMENTS}    Get WebElements    ${ALL_COST_CENTRES}
        #Extract text from each cost centre and log it
        ${COST_CENTRE_TEXT}    Create List
        FOR    ${COST_CENTRE}    IN    @{COST_CENTRE_ELEMENTS}
            ${COST_CENTRE_NAME}=    Get Text    ${COST_CENTRE}
            Append To List    ${COST_CENTRE_TEXT}    ${COST_CENTRE_NAME}
        END

        Log    Cost Centres for ${REGION}: ${COST_CENTRE_TEXT}
        
    END

    Log    User was able to expand a region to view cost centres. 

the user verifies that the cost centres can be expanded to display the accounts 
    # Get all regions and click only the first one
    @{REGION_ELEMENTS}    Get WebElements    ${ALL_REGIONS}
    Click Element    ${REGION_ELEMENTS}[0]
    ${REGION}=    Get Text    ${REGION_ELEMENTS}[0]
    Log    Clicked Region: ${REGION}

    # Get all cost centres under the selected region and click only the first one
    @{COST_CENTRE_ELEMENTS}    Get WebElements    ${ALL_COST_CENTRES}
    Click Element    ${COST_CENTRE_ELEMENTS}[0]
    ${COST_CENTRE_NAME}=    Get Text    ${COST_CENTRE_ELEMENTS}[0]
    Log    Clicked Cost Centre: ${COST_CENTRE_NAME}

    # Get all accounts under the selected cost centre
    @{ACCOUNT_ELEMENTS}    Get WebElements    ${ALL_ACCOUNTS}
    ${ACCOUNTS_FOUND}    Create List
    FOR    ${ACCOUNT}    IN    @{ACCOUNT_ELEMENTS}
        ${ACCOUNT_NAME}=    Get Text    ${ACCOUNT}
        Append To List   ${ACCOUNTS_FOUND}    ${ACCOUNT_NAME}
    END
    
    Log    User was able to expand a Cost Centre to view accounts:${ACCOUNTS_FOUND} 
    Log    Accounts for Cost Centre ${COST_CENTRE_NAME}: ${ACCOUNTS_FOUND}

the user verifies the Cost Centre count for regions on the EOD dashboard
    @{REGION_ELEMENTS}    Get WebElements    ${ALL_REGIONS}

    FOR    ${ELEMENT}    IN    @{REGION_ELEMENTS}
        Click Element    ${ELEMENT}
        ${REGION}=    Get Text    ${ELEMENT}
        Log    Clicked Region: ${REGION}

        ${COST_CENTRE_TEXT}    Create List
        ## Extract text from each cost centre and store in a list
         @{COST_CENTRE_ELEMENTS}    Get WebElements    //tbody[contains(@class, '${REGION}')]//tr[@class='w3-medium']//td[@class='w3-button']
        FOR    ${COST_CENTRE}    IN    @{COST_CENTRE_ELEMENTS}
            ${COST_CENTRE_NAME}=    Get Text    ${COST_CENTRE}
            Append To List    ${COST_CENTRE_TEXT}    ${COST_CENTRE_NAME}
        END
        
        Log    ${COST_CENTRE_NAME}

        # Get the count of cost centres
        ${COST_CENTRE_COUNT}=    Get Length    ${COST_CENTRE_TEXT}
        Log    Cost Centre Count for:${REGION}:is:${COST_CENTRE_NAME}with count:${COST_CENTRE_COUNT}

        ${FRONT_END_COST_CENTRE_COUNT}=    Get Text    //td[contains(text(), '${REGION}')]/following-sibling::td[@class='w3-right-align']
        ${FRONT_END_COST_CENTRE_COUNT}=    Convert To Integer    ${FRONT_END_COST_CENTRE_COUNT}
        Log    Region:${REGION} Front End count is:${FRONT_END_COST_CENTRE_COUNT}
        Should Be Equal    ${COST_CENTRE_COUNT}    ${FRONT_END_COST_CENTRE_COUNT}    Region: ${REGION} counted cost centres: ${COST_CENTRE_COUNT} match the front end populated count: ${FRONT_END_COST_CENTRE_COUNT}

    END
    
    Set suite variable     ${COST_CENTRE_NAME}
the user verifies the Account count for Cost Centres on the EOD dashboard
    # User verifies the Account count for Cost Centres on the EOD dashboard
    @{REGION_ELEMENTS}    Get WebElements    ${ALL_REGIONS}

    FOR    ${ELEMENT}    IN    @{REGION_ELEMENTS}
        Click Element    ${ELEMENT}
        ${REGION}=    Get Text    ${ELEMENT}
        Log    Clicked Region: ${REGION}

        ${COST_CENTRE_TEXT}    Create List
        @{COST_CENTRE_ELEMENTS}    Get WebElements    //tbody[contains(@class, '${REGION}')]//tr[@class='w3-medium']//td[@class='w3-button']
    
        FOR    ${COST_CENTRE}    IN    @{COST_CENTRE_ELEMENTS}
            ${COST_CENTRE_NAME}=    Get Text    ${COST_CENTRE}
            Append To List    ${COST_CENTRE_TEXT}    ${COST_CENTRE_NAME}
            #${COST_CENTRE_NUMBER}=    Get Regexp Matches    ${COST_CENTRE_NAME}    (\d+)    1
            #Log    Extracted cost centre number: ${COST_CENTRE_NUMBER}
        END
    END
    
The
        ${COST_CENTRE_COUNT}=    Get Length    ${COST_CENTRE_TEXT}
        Log    Cost Centre Count for ${REGION}: ${COST_CENTRE_COUNT}

        ${FRONT_END_COST_CENTRE_COUNT}=    Get Text    //td[contains(text(), '${REGION}')]/following-sibling::td[@class='w3-right-align']
        ${FRONT_END_COST_CENTRE_COUNT}=    Convert To Integer    ${FRONT_END_COST_CENTRE_COUNT}
        Log    Region:${REGION} Front End count is:${FRONT_END_COST_CENTRE_COUNT}
        Should Be Equal    ${COST_CENTRE_COUNT}    ${FRONT_END_COST_CENTRE_COUNT}    Region: ${REGION} counted cost centres: ${COST_CENTRE_COUNT} match the front end populated count: ${FRONT_END_COST_CENTRE_COUNT}

        FOR    ${COST_CENTRE}    IN    @{COST_CENTRE_ELEMENTS}
            ${COST_CENTRE_NAME}=    Get Text    ${COST_CENTRE}
            Log    Checking accounts for cost centre: ${COST_CENTRE_NAME}

            Click Element    ${COST_CENTRE}
        
            # Wait for accounts to load
            Wait Until Element Is Visible    xpath=//a[contains(@href, 'costCentre=${COST_CENTRE_NAME}') and contains(@href, 'account=')]    5s

            @{ACCOUNT_ELEMENTS}=    Get WebElements    xpath=//a[contains(@href, 'costCentre=${COST_CENTRE_NUMBER}') and contains(@href, 'account=')]    5s

            ${ACCOUNT_TEXTS}=    Create List
            FOR    ${ACCOUNT}    IN    @{ACCOUNT_ELEMENTS}
                ${TEXT}=    Get Text    ${ACCOUNT}
                Append To List    ${ACCOUNT_TEXTS}    ${TEXT}
            END

            ${ACCOUNT_COUNT}=    Get Length    ${ACCOUNT_TEXTS}
            Log    Account count for ${COST_CENTRE_NAME}: ${ACCOUNT_COUNT}
        END
    END
