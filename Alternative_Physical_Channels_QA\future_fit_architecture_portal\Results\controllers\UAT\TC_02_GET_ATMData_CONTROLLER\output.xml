<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 12:58:33.335" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\ATMData\TC_02_GET_ATMData_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:33.850" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:33.850"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:33.850" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Users\<USER>\source\repos\future_fit_repo\data\Test2.xml'.</msg>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:33.850"/>
</kw>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:33.850"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Get ATM DATA using a valid auth token" line="43">
<kw name="GET ATM DATA">
<arg>Gets ATMs Details using valid auth</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMData</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>[*]:atmNumber=S08397</arg>
<arg>[*]:model=31729758</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:58:33.850" level="INFO">Set test documentation to:
Gets ATMs Details using valid auth</msg>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:33.850"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:33.850" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:33.850"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:58:33.859" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:58:33.859" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:33.859"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=false</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:58:33.859" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:58:33.859" endtime="******** 12:58:34.065"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Session Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:58:34.065" level="INFO">'Session Created!'</msg>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</kw>
<status status="PASS" starttime="******** 12:58:33.850" endtime="******** 12:58:34.065"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 12:58:34.065" level="INFO">${end_point} = /ATMData</msg>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</kw>
<status status="NOT RUN" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 12:58:34.065" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</branch>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.065"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:58:34.251" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMData 
 path_url=/ATMData 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MphB5yzUibIVxLpRlKCTFswy8kiU_aE26QDXU2xl6GG_CU2kyG8B9lzip4C6FAn3DoBd0PsDXRyZSMhqL2Syc-nDLT9tGDn4DDH8nFpud-PPkiXFTssBH3Mr2--_t9Qm2tMXPW2gjEdd2zl8b_4HXPuA4qA0CCyr8HI78VkjNSr8eYIKUwOI3dxaM2800NwsJev7gg4GQyMnushPalTu-cLpn0snG0G0baLTiL3X78zOHWCEsWMjh0MgPrw6_1s3DKHaGdXeH2YivI78WBf6ydRbSU-eQQeC4OL8lot6-35NbOoqUy7KGlFiGPoZkI6dnVlY_WYh7JqJsZXNgaJNLQ'} 
 body=None 
 </msg>
<msg timestamp="******** 12:58:34.251" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMData 
 status=401, reason=Unauthorized 
 headers={'Date': 'Fri, 21 Jun 2024 10:58:34 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'WWW-Authenticate': 'Bearer error="invalid_token", error_description="The token expired at \'05/30/2024 10:45:40\'"', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=None 
 </msg>
<msg timestamp="******** 12:58:34.251" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:58:34.251" level="INFO">${response} = &lt;Response [401]&gt;</msg>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.251"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO">${response.content} = </msg>
<status status="PASS" starttime="******** 12:58:34.251" endtime="******** 12:58:34.253"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO"/>
<status status="PASS" starttime="******** 12:58:34.253" endtime="******** 12:58:34.253"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '401'.</msg>
<status status="PASS" starttime="******** 12:58:34.253" endtime="******** 12:58:34.253"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Unauthorized'.</msg>
<status status="PASS" starttime="******** 12:58:34.253" endtime="******** 12:58:34.253"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO">${response.content} = </msg>
<status status="PASS" starttime="******** 12:58:34.253" endtime="******** 12:58:34.253"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.065" endtime="******** 12:58:34.253"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO">${returned_status_code} = 401</msg>
<status status="PASS" starttime="******** 12:58:34.253" endtime="******** 12:58:34.253"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:58:34.253" level="INFO">Response Status Code : 401</msg>
<status status="PASS" starttime="******** 12:58:34.253" endtime="******** 12:58:34.253"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<msg timestamp="******** 12:58:34.253" level="FAIL">Url: https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMData Expected status: 401 != 200</msg>
<status status="FAIL" starttime="******** 12:58:34.253" endtime="******** 12:58:34.260"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 12:58:34.260" endtime="******** 12:58:34.260"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 12:58:34.260" endtime="******** 12:58:34.260"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="NOT RUN" starttime="******** 12:58:34.260" endtime="******** 12:58:34.260"/>
</kw>
<status status="FAIL" starttime="******** 12:58:34.253" endtime="******** 12:58:34.260"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<status status="NOT RUN" starttime="******** 12:58:34.260" endtime="******** 12:58:34.260"/>
</kw>
<status status="FAIL" starttime="******** 12:58:33.850" endtime="******** 12:58:34.260"/>
</kw>
<doc>Gets ATMs Details using valid auth</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="******** 12:58:33.850" endtime="******** 12:58:34.260">Url: https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMData Expected status: 401 != 200</status>
</test>
<test id="s1-t2" name="FFT - Controllers - Get ATM DATA using an expired auth token" line="44">
<kw name="GET ATM DATA">
<arg>Gets ATMs Details using invalid auth</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMData</arg>
<arg>401</arg>
<arg>Unauthorized</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:58:34.265" level="INFO">Set test documentation to:
Gets ATMs Details using invalid auth</msg>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.265"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:34.265" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.265"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:58:34.265" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:58:34.265" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.265"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=false</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:58:34.267" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.462"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.462"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Session Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:58:34.462" level="INFO">'Session Created!'</msg>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.462"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.462"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 12:58:34.462" level="INFO">${end_point} = /ATMData</msg>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.462"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<msg timestamp="******** 12:58:34.464" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthY...</msg>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.464"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.464"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:58:34.464" endtime="******** 12:58:34.464"/>
</kw>
<status status="NOT RUN" starttime="******** 12:58:34.464" endtime="******** 12:58:34.464"/>
</branch>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.464"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:58:34.697" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMData 
 path_url=/ATMData 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyIsImtpZCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FI26FGo9WE2UvoEncNGM1-63BboD54HmUGkgw_UF2TVGc8-C_0Wziv0KXECaaU-ExQNli2I5ZD4qgamPXa_daS9DVUmkurKQuRCSv_FV811I4ZkDCCYxVyl4G_U2Cbp82Wx9X7Xojur6lOkmu8Mo7F2YZ3gENVyF1httwzYPHFJ3qFJ6jQD7UtNDrG3D5IYZQbA06dRtu0Oo6AQq54EYMplf1SvpX0_nA6trvVsF6V9U09lbtYp6vjAYzebkWtBrNkNhojhVvZeKsgPruGCoPjPLkswCwb4dlKTC2mBeCapiHc3boy7RH0-tvLbnExqqGcW377ah9873_lUqRIBc4g'} 
 body=None 
 </msg>
<msg timestamp="******** 12:58:34.697" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMData 
 status=401, reason=Unauthorized 
 headers={'Date': 'Fri, 21 Jun 2024 10:58:34 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'WWW-Authenticate': 'Bearer error="invalid_token", error_description="The token expired at \'02/12/2024 07:20:36\'"', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=None 
 </msg>
<msg timestamp="******** 12:58:34.697" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:58:34.702" level="INFO">${response} = &lt;Response [401]&gt;</msg>
<status status="PASS" starttime="******** 12:58:34.464" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">${response.content} = </msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO"/>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '401'.</msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Unauthorized'.</msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">${response.content} = </msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.462" endtime="******** 12:58:34.702"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">${returned_status_code} = 401</msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">Response Status Code : 401</msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:58:34.702" level="INFO">${returned_status_reason} = Unauthorized</msg>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.702"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}"/>
<var name="${value}"/>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<status status="NOT RUN" starttime="******** 12:58:34.708" endtime="******** 12:58:34.708"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="NOT RUN" starttime="******** 12:58:34.708" endtime="******** 12:58:34.708"/>
</kw>
<status status="NOT RUN" starttime="******** 12:58:34.708" endtime="******** 12:58:34.708"/>
</iter>
<status status="NOT RUN" starttime="******** 12:58:34.702" endtime="******** 12:58:34.708"/>
</for>
<status status="PASS" starttime="******** 12:58:34.702" endtime="******** 12:58:34.708"/>
</kw>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.708"/>
</kw>
<doc>Gets ATMs Details using invalid auth</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 12:58:34.265" endtime="******** 12:58:34.708"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="FAIL" starttime="******** 12:58:33.499" endtime="******** 12:58:34.708"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="1" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="1" fail="1" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="1" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
