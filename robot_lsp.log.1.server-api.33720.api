server-api: 2025-06-18 11:21:50 UTC pid: 33720 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.1.api']

server-api: 2025-06-18 11:21:50 UTC pid: 33720 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-18 11:21:50 UTC pid: 33720 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-18 11:21:50 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 33720

server-api: 2025-06-18 11:21:51 UTC pid: 33720 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-18 11:21:51 UTC pid: 33720 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-18 11:21:51 UTC pid: 33720 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-18 11:36:07 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 17, method: hover

server-api: 2025-06-18 11:36:10 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 18, method: hover

server-api: 2025-06-18 11:36:11 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 19, method: hover

server-api: 2025-06-18 11:36:13 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 22, method: hover

server-api: 2025-06-18 11:36:14 UTC pid: 33720 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
server-api: 2025-06-18 11:36:38 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 29, method: hover

server-api: 2025-06-18 11:37:13 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 46, method: code_action

server-api: 2025-06-18 11:37:15 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 48, method: hover

server-api: 2025-06-18 11:37:15 UTC pid: 33720 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

server-api: 2025-06-18 11:37:47 UTC pid: 33720 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

server-api: 2025-06-18 11:37:48 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 62, method: hover

server-api: 2025-06-18 11:38:48 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 86, method: hover

server-api: 2025-06-18 11:38:50 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 88, method: hover

server-api: 2025-06-18 11:38:50 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 89, method: hover

server-api: 2025-06-18 11:38:54 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 93, method: hover

server-api: 2025-06-18 11:38:55 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 96, method: hover

server-api: 2025-06-18 11:38:57 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 97, method: hover

server-api: 2025-06-18 11:38:57 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 98, method: hover

server-api: 2025-06-18 11:39:00 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 102, method: hover

server-api: 2025-06-18 11:39:01 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 105, method: hover

server-api: 2025-06-18 11:39:03 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 107, method: hover

server-api: 2025-06-18 11:39:54 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 114, method: hover

server-api: 2025-06-18 11:39:54 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 115, method: hover

server-api: 2025-06-18 11:43:25 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 188

server-api: 2025-06-18 11:43:25 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 189, method: hover

server-api: 2025-06-18 13:50:27 UTC pid: 33720 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 240, method: findDefinition

server-api: 2025-06-18 16:05:00 UTC pid: 33720 - MainThread - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'stop_tracking', 'on_change_id': '33720 - 9 - 0.24473152451801516'} - closed: False

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
