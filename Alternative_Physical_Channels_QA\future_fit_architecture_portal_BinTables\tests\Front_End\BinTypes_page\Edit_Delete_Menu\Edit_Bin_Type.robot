*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Edit_or_Delete_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Edit Bin Types displayed on the Edit/Delete page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}       ${NEW_BIN_TYPE_NAME}     ${NEW_BIN_TYPE_DESC}
    Set Test Documentation  ${DOCUMENTATION}

    IF    "${NEW_BIN_TYPE_DESC}" == "${EMPTY}" or "${NEW_BIN_TYPE_DESC}" == "''"
         ${random_word}=     Generate random word
         ${NEW_BIN_TYPE_DESC}=   Set Variable     ${random_word}
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user navigates to 'Edit/Delete' Bin Type tab
    And The user edits a Bin Type                                          ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}       ${NEW_BIN_TYPE_NAME}     ${NEW_BIN_TYPE_DESC}      POSITIVE
    Then The edited Bin Type must exist in the database                    ${NEW_BIN_TYPE_NAME}     ${NEW_BIN_TYPE_DESC}

| *** Test Cases ***                                                                     |        *DOCUMENTATION*    		                   |         *BASE_URL*                  |         *CURR_BIN_TYPE_NAME*             |         *CURR_BIN_TYPE_DESC*             |         *NEW_BIN_TYPE_NAME*     |         *NEW_BIN_TYPE_DESC*             |
| Edit a Bin Type.                  | Edit Bin Types displayed on the Edit/Delete page   | Edit Bin Types displayed on the Edit/Delete page.   |           ${EMPTY}                  |           Token                          |           ${EMPTY}                       |           Token                 |     ''                                   |
