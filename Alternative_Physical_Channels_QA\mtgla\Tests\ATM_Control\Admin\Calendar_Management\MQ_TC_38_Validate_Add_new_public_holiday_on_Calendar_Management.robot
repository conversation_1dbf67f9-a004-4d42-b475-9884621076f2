*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Calendar Management 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../keywords/Common/Login.robot
Resource                                            ../../../../keywords/Common/HomePage.robot
Resource                                            ../../../../keywords/Common/Navigation.robot
Resource                                            ../../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../../keywords/Common/SetEnvironmentVariales.robot
Resource                                            ../../../../keywords/Calendar_Management.robot

*** Variables ***

*** Keywords ***
Validates Calendar Management on ATM Control 
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page 

    And the user navigates to Calendar Management Link

    And the user clicks the add button on the ATM Control Calendar Management

    And The user inputs a date and Public Holiday Description

    And The user clicks the save button 

    Then The User verifies that the Public Holiday was saved to the database



| *Test Cases*                                                                                                        |      *DOCUMENTATION*                          | *TEST_ENVIRONMENT*   |
| MQ_TC_38_Validate_Add_new_public_holiday_on_Calendar_Management    | Validates Calendar Management on ATM Control   |    User can access dashboard for ATM Control  |    MTGLA_UAT         | 
