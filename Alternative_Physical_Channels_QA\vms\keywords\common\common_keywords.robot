*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  All keywords that are used across functions

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             DateTime
Library                                             String
Library                                             Collections

#***********************************PROJECT RESOURCES***************************************
Library                                             ../../utility/UiActions.py
Resource                                            ../VMSPage/LandingPage.robot

*** Variables ***
${CONFIRM_MESSAGE}                                  id=ConfirmMsg
${OK_BTN}                                           id=MainContent_btnSubmitComplete

*** Keywords ***
User confirms action
	[Arguments]    ${SUCCESS_MESSAGE_TEXT}
    Log to console  --------------------------User confirms action

    Wait Until Element Is Visible                   ${CONFIRM_MESSAGE}

    Wait until Page Contains                        ${SUCCESS_MESSAGE_TEXT}

    Sleep  3s

    Capture page screenshot  Confirmation_popup.png

    Click Element                                    ${OK_BTN}

    Sleep  3s

# Get Current Date
#     [Documentation]  Get current date and convert it to local time

#     ${date} =	Get Current Date  time_zone=local

#     ${space}=      Set Variable  ${SPACE}

#     ${current_date_in_local_zone} =	Replace String	 ${date}  ${space}  T

#     [Return]    ${current_date_in_local_zone}

Get Today Date
    ${date} =	Get Current Date  time_zone=local
    ${space}=      Set Variable  ${SPACE}
    ${current_date_in_local_zone} =	Replace String	 ${date}  ${space}  T
    RETURN    ${current_date_in_local_zone}

Verify if values are equal
    [Arguments]     ${EXPECTED_VALUE}    ${ACTUAL_VALUE}
         Run Keyword And Continue On Failure    Should Be Equal As Strings    ${EXPECTED_VALUE}    ${ACTUAL_VALUE}
         Log Many     Comparison result  : Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}

All Variables are True
    [Arguments]    @{variables}
    #The all() function in Python takes an iterable (like a list or tuple)
    #and returns True if all elements of the iterable are true (or if the iterable is empty).
    #If any element is False, all() returns False.

    ${all_true}=    Evaluate    all(${variables})
    RETURN    ${all_true}


Retrieve Validation Message from the Browser Using Java Script
    # This code checks for invalid input fields and retrieves their validation message.
    # If no such field is found, it returns a default message indicating no validation message was found.

    # Execute JavaScript to get the validation message
    ${validation_message}=    Execute JavaScript    return document.querySelector('input:invalid') ? document.querySelector('input:invalid').validationMessage : 'No validation message found';
    RETURN      ${validation_message}


Get All Attributes Of An Element Using XPath
    [Arguments]     ${element}
    ${attributes}=    Execute JavaScript    return Array.from(arguments[0].attributes).reduce((acc, attr) => { acc[attr.name] = attr.value; return acc; }, {});    ${element}


    Log    ${attributes}

Check element in list
    [Arguments]     ${list}    ${element}
    ${is_in_list}=    Evaluate    "${element}" in ${list}
    RETURN    ${is_in_list}


Compare ATM Field Values
    [Documentation]    Compare frontend and backend ATM field values with proper logging
    [Arguments]    ${field_name}    ${frontend_value}    ${backend_value}    ${atm_id}

    # Clean values by stripping leading and trailing whitespaces (like Python .strip())
    ${clean_frontend}=    Strip String    ${frontend_value}
    ${clean_backend}=    Strip String    ${backend_value}

    # Perform comparison
    ${values_match}=    Run Keyword And Return Status    Should Be Equal As Strings    ${clean_frontend}    ${clean_backend}

    # Log results based on comparison outcome
    IF    ${values_match}
        Log To Console    --------------------------✓ ${field_name}(${clean_frontend}) matches Database ${field_name}(${clean_backend}). ATM: ${atm_id}
    ELSE
        Log To Console    -----------FAIL---------------✗ ${field_name}(${clean_frontend}) does not match Database ${field_name}(${clean_backend}). ATM: ${atm_id}
        # Continue on failure to collect all validation errors
        Run Keyword And Continue On Failure    Fail    ${field_name} mismatch for ATM ${atm_id}: Frontend='${clean_frontend}', Database='${clean_backend}'
    END

    RETURN    ${values_match}

Create ATM Field Mapping Dictionary
    [Documentation]    Create a mapping dictionary for ATM frontend headers to database fields
    [Return]    Dictionary mapping frontend headers to database field names

    ${field_mapping}=    Create Dictionary
    ...    ATMNumber=ID
    ...    SerialNumber=SERIAL_NUM
    ...    ATMBranch=BRANCH
    ...    PhoneNumber=DATA_LINE
    ...    Model=OBJECT_TYPE
    ...    Institution=INSTITUTION
    ...    ATMName=ADDRESS
    ...    ATMAddress=ADDRESS,ADDRESS2
    ...    City=CITY
    ...    Province=REGION
    ...    ZoneSLA=ZONE

    RETURN    ${field_mapping}

Get Database Field Value
    [Documentation]    Get the appropriate database field value based on field mapping
    [Arguments]    ${db_row}    ${field_name}    ${field_mapping}
    [Return]    Database field value (may be concatenated for composite fields)

    ${db_field_name}=    Get From Dictionary    ${field_mapping}    ${field_name}

    # Handle composite fields (like ADDRESS,ADDRESS2)
    ${is_composite}=    Run Keyword And Return Status    Should Contain    ${db_field_name}    ,

    IF    ${is_composite}
        ${field_parts}=    Split String    ${db_field_name}    ,
        ${composite_value}=    Set Variable    ${EMPTY}

        FOR    ${field_part}    IN    @{field_parts}
            ${part_value}=    Get From Dictionary    ${db_row}    ${field_part}
            ${part_value}=    Convert To String    ${part_value}
            # Strip whitespaces from each part before concatenating
            ${part_value}=    Strip String    ${part_value}
            ${composite_value}=    Catenate    SEPARATOR=,    ${composite_value}    ${part_value}
        END

        # Strip the final composite value as well
        ${composite_value}=    Strip String    ${composite_value}
        RETURN    ${composite_value}
    ELSE
        ${field_value}=    Get From Dictionary    ${db_row}    ${db_field_name}
        ${field_value}=    Convert To String    ${field_value}
        # Strip whitespaces from database field value
        ${field_value}=    Strip String    ${field_value}
        RETURN    ${field_value}
    END