<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="******** 12:35:17.822" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\development\future-fit-architecture-portal-docker\tests\TC_01_GET_CampaignLookup_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:18.305" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value 'Yaash.<PERSON><EMAIL>'.</msg>
<status status="PASS" starttime="******** 12:35:18.305" endtime="******** 12:35:18.305"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:18.305" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="******** 12:35:18.305" endtime="******** 12:35:18.305"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:18.305" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 12:35:18.305" endtime="******** 12:35:18.305"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.305" endtime="******** 12:35:18.305"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Get CampaignLookUp using a Business User" line="43">
<kw name="CampaignLookUp">
<arg>Gets CampaignLookUp Result using a Business User auth</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>CAMPAIGNLOOKUP</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>marketingChannels[0]:channel=ATM</arg>
<arg>languages[1]:language=Afrikaans</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:35:18.321" level="INFO">Set test documentation to:
Gets CampaignLookUp Result using a Business User auth</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:18.321" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:35:18.321" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:35:18.321" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:35:18.321" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:35:18.321" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 12:35:18.321" level="INFO">${end_point} = /CampaignLookup/</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<status status="NOT RUN" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 12:35:18.321" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvO...</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</branch>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.321"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:35:18.600" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/CampaignLookup/ 
 path_url=/CampaignLookup/ 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSIsImtpZCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WjkG_4jeZZMbQAehZd2koeIUuVYif9xT23FJ6aPsyQTv5OFSr_6gT6wU5D7BlAfVUfPLM9JJtkr6wa5wLyfrVj99NsIfMJkW3bSm_6--IMIH94T9s-WrF0z1DyGIov5s46jAkothDSOIkgPeqEwu-kRn9eXWfYuk6IGPgHsDkslmvUiwS45rBsq_qniofGzbsJbUMiHad_ud-rv9bKhWs220srSmS2f2zVJs6EVbO_rR3MKe6GNK1JJvNn6Bm_5C7eVxDKWNT4mt_4ObjGMGZyHFBx9Mk90d8ybdfPlYedKDW6UtWhKlbMIqnRfkVkQLJQ9-AFq-BSCq42mM61uG-g'} 
 body=None 
 </msg>
<msg timestamp="******** 12:35:18.600" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/CampaignLookup/ 
 status=200, reason=OK 
 headers={'Date': 'Tue, 12 Mar 2024 10:35:18 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body={"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":"","imageResolution":""},{"id":6,"channel":"","imageResolution":""},{"id":7,"channel":"","imageResolution":""}],"marketingScreens":[{"id":1,"screenType":"Transactional","screenNumber":"","channelId":1},{"id":2,"screenType":"Idle","screenNumber":"Screen 1","channelId":1},{"id":3,"screenType":"Idle","screenNumber":"Screen 2","channelId":1},{"id":4,"screenType":"Idle","screenNumber":"Screen 3","channelId":1},{"id":5,"screenType":"Idle","screenNumber":"Screen 4","channelId":1},{"id":6,"screenType":"Idle","screenNumber":"Screen 5","channelId":1},{"id":7,"screenType":"Idle","screenNumber":"Screen 6","channelId":1}],"languages":[{"id":1,"language":"English","languageCode":"en"},{"id":2,"language":"Afrikaans","languageCode":"af"},{"id":3,"language":"Northern Sotho","languageCode":"nso"},{"id":4,"language":"Sotho","languageCode":"sot"},{"id":5,"language":"Tsonga","languageCode":"tso"},{"id":6,"language":"Venda","languageCode":"ven"},{"id":7,"language":"Xhosa","languageCode":"xho"},{"id":8,"language":"Zulu","languageCode":"zul"}],"regions":["Blank","Boland","Dar es Salaam","Eastern Cape","Empty1","Free State","Gauteng","Gauteng North","Gauteng South","Gauteng West","Internal","Kwazulu Natal","Limpopo","Mobile","Mpumalanga","North West","Northern Cape","Not selected","PILOT","RHINO CASH ULUNDI","SAVEWAYS BRANCH","Score Senoane","Test","TOTAL DOUGLASDALE","TOWERS LANGENHOVEN PARK","Transkei","VIVA BINGO ATTERBURY","VIVA BINGO BALLITO","Western Cape"]} 
 </msg>
<msg timestamp="******** 12:35:18.600" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:35:18.600" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.600"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:35:18.600" level="INFO">${response.content} = {"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":...</msg>
<status status="PASS" starttime="******** 12:35:18.600" endtime="******** 12:35:18.600"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:35:18.600" level="INFO">{"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":"","imageResolution":""},{"id":6,"channel":"","imageResolution":""},{"id":7,"channel":"","imageResolution":""}],"marketingScreens":[{"id":1,"screenType":"Transactional","screenNumber":"","channelId":1},{"id":2,"screenType":"Idle","screenNumber":"Screen 1","channelId":1},{"id":3,"screenType":"Idle","screenNumber":"Screen 2","channelId":1},{"id":4,"screenType":"Idle","screenNumber":"Screen 3","channelId":1},{"id":5,"screenType":"Idle","screenNumber":"Screen 4","channelId":1},{"id":6,"screenType":"Idle","screenNumber":"Screen 5","channelId":1},{"id":7,"screenType":"Idle","screenNumber":"Screen 6","channelId":1}],"languages":[{"id":1,"language":"English","languageCode":"en"},{"id":2,"language":"Afrikaans","languageCode":"af"},{"id":3,"language":"Northern Sotho","languageCode":"nso"},{"id":4,"language":"Sotho","languageCode":"sot"},{"id":5,"language":"Tsonga","languageCode":"tso"},{"id":6,"language":"Venda","languageCode":"ven"},{"id":7,"language":"Xhosa","languageCode":"xho"},{"id":8,"language":"Zulu","languageCode":"zul"}],"regions":["Blank","Boland","Dar es Salaam","Eastern Cape","Empty1","Free State","Gauteng","Gauteng North","Gauteng South","Gauteng West","Internal","Kwazulu Natal","Limpopo","Mobile","Mpumalanga","North West","Northern Cape","Not selected","PILOT","RHINO CASH ULUNDI","SAVEWAYS BRANCH","Score Senoane","Test","TOTAL DOUGLASDALE","TOWERS LANGENHOVEN PARK","Transkei","VIVA BINGO ATTERBURY","VIVA BINGO BALLITO","Western Cape"]}</msg>
<status status="PASS" starttime="******** 12:35:18.600" endtime="******** 12:35:18.600"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:18.600" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 12:35:18.600" endtime="******** 12:35:18.600"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:18.600" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 12:35:18.600" endtime="******** 12:35:18.600"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:35:18.615" level="INFO">${response.content} = {"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":...</msg>
<status status="PASS" starttime="******** 12:35:18.600" endtime="******** 12:35:18.615"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.615"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:35:18.618" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 12:35:18.615" endtime="******** 12:35:18.618"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:35:18.619" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 12:35:18.619" endtime="******** 12:35:18.619"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 12:35:18.619" endtime="******** 12:35:18.620"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.620"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:35:18.620" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.620"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.620"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.615" endtime="******** 12:35:18.620"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}">marketingChannels[0]:channel</var>
<var name="${value}">ATM</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 12:35:18.637" level="INFO">Field: marketingChannels[0]:channel , having a value: ATM was found.</msg>
<msg timestamp="******** 12:35:18.637" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.637"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 12:35:18.637" endtime="******** 12:35:18.637"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.637"/>
</iter>
<iter>
<var name="${key}">languages[1]:language</var>
<var name="${value}">Afrikaans</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 12:35:18.637" level="INFO">Field: languages[1]:language , having a value: Afrikaans was found.</msg>
<msg timestamp="******** 12:35:18.637" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 12:35:18.637" endtime="******** 12:35:18.637"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 12:35:18.637" endtime="******** 12:35:18.637"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.637" endtime="******** 12:35:18.637"/>
</iter>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.637"/>
</for>
<status status="PASS" starttime="******** 12:35:18.620" endtime="******** 12:35:18.637"/>
</kw>
<status status="PASS" starttime="******** 12:35:18.321" endtime="******** 12:35:18.637"/>
</kw>
<doc>Gets CampaignLookUp Result using a Business User auth</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 12:35:18.305" endtime="******** 12:35:18.637"/>
</test>
<test id="s1-t2" name="FFT - Controllers - Get CampaignLookUp: id using a Approver" line="44">
<kw name="CampaignLookUp">
<arg>Gets CampaignLookUp id Result using a Business App auth</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>CAMPAIGNLOOKUP</arg>
<arg>14239</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>marketingChannels[0]:channel=ATM</arg>
<arg>languages[1]:language=Afrikaans</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:35:20.578" level="INFO">Set test documentation to:
Gets CampaignLookUp id Result using a Business App auth</msg>
<status status="PASS" starttime="******** 12:35:20.578" endtime="******** 12:35:20.578"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:20.594" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 12:35:20.578" endtime="******** 12:35:20.594"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:35:20.597" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:35:20.597" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:35:20.594" endtime="******** 12:35:20.597"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:35:20.597" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:35:20.597" endtime="******** 12:35:20.597"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:35:20.597" endtime="******** 12:35:20.597"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:35:20.597" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 12:35:20.597" endtime="******** 12:35:20.597"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.594" endtime="******** 12:35:20.597"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 12:35:20.601" level="INFO">${end_point} = /CampaignLookup/14239</msg>
<status status="PASS" starttime="******** 12:35:20.601" endtime="******** 12:35:20.601"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:35:20.602" endtime="******** 12:35:20.602"/>
</kw>
<status status="NOT RUN" starttime="******** 12:35:20.601" endtime="******** 12:35:20.602"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 12:35:20.602" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvO...</msg>
<status status="PASS" starttime="******** 12:35:20.602" endtime="******** 12:35:20.603"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.602" endtime="******** 12:35:20.603"/>
</branch>
<status status="PASS" starttime="******** 12:35:20.601" endtime="******** 12:35:20.603"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:35:20.827" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/CampaignLookup/14239 
 path_url=/CampaignLookup/14239 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSIsImtpZCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WjkG_4jeZZMbQAehZd2koeIUuVYif9xT23FJ6aPsyQTv5OFSr_6gT6wU5D7BlAfVUfPLM9JJtkr6wa5wLyfrVj99NsIfMJkW3bSm_6--IMIH94T9s-WrF0z1DyGIov5s46jAkothDSOIkgPeqEwu-kRn9eXWfYuk6IGPgHsDkslmvUiwS45rBsq_qniofGzbsJbUMiHad_ud-rv9bKhWs220srSmS2f2zVJs6EVbO_rR3MKe6GNK1JJvNn6Bm_5C7eVxDKWNT4mt_4ObjGMGZyHFBx9Mk90d8ybdfPlYedKDW6UtWhKlbMIqnRfkVkQLJQ9-AFq-BSCq42mM61uG-g'} 
 body=None 
 </msg>
<msg timestamp="******** 12:35:20.828" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/CampaignLookup/14239 
 status=200, reason=OK 
 headers={'Date': 'Tue, 12 Mar 2024 10:35:20 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=value 
 </msg>
<msg timestamp="******** 12:35:20.828" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:35:20.828" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 12:35:20.603" endtime="******** 12:35:20.828"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:35:20.828" level="INFO">${response.content} = {"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":...</msg>
<status status="PASS" starttime="******** 12:35:20.828" endtime="******** 12:35:20.828"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:35:20.828" level="INFO">{"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":"","imageResolution":""},{"id":6,"channel":"","imageResolution":""},{"id":7,"channel":"","imageResolution":""}],"marketingScreens":[{"id":1,"screenType":"Transactional","screenNumber":"","channelId":1},{"id":2,"screenType":"Idle","screenNumber":"Screen 1","channelId":1},{"id":3,"screenType":"Idle","screenNumber":"Screen 2","channelId":1},{"id":4,"screenType":"Idle","screenNumber":"Screen 3","channelId":1},{"id":5,"screenType":"Idle","screenNumber":"Screen 4","channelId":1},{"id":6,"screenType":"Idle","screenNumber":"Screen 5","channelId":1},{"id":7,"screenType":"Idle","screenNumber":"Screen 6","channelId":1}],"languages":[{"id":1,"language":"English","languageCode":"en"},{"id":2,"language":"Afrikaans","languageCode":"af"},{"id":3,"language":"Northern Sotho","languageCode":"nso"},{"id":4,"language":"Sotho","languageCode":"sot"},{"id":5,"language":"Tsonga","languageCode":"tso"},{"id":6,"language":"Venda","languageCode":"ven"},{"id":7,"language":"Xhosa","languageCode":"xho"},{"id":8,"language":"Zulu","languageCode":"zul"}],"regions":["Blank","Boland","Dar es Salaam","Eastern Cape","Empty1","Free State","Gauteng","Gauteng North","Gauteng South","Gauteng West","Internal","Kwazulu Natal","Limpopo","Mobile","Mpumalanga","North West","Northern Cape","Not selected","PILOT","RHINO CASH ULUNDI","SAVEWAYS BRANCH","Score Senoane","Test","TOTAL DOUGLASDALE","TOWERS LANGENHOVEN PARK","Transkei","VIVA BINGO ATTERBURY","VIVA BINGO BALLITO","Western Cape"]}</msg>
<status status="PASS" starttime="******** 12:35:20.828" endtime="******** 12:35:20.828"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:20.834" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 12:35:20.828" endtime="******** 12:35:20.834"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:35:20.836" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 12:35:20.834" endtime="******** 12:35:20.836"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:35:20.838" level="INFO">${response.content} = {"marketingChannels":[{"id":1,"channel":"ATM","imageResolution":"800x600"},{"id":2,"channel":"SSK","imageResolution":"1024x768"},{"id":3,"channel":"BCD","imageResolution":"800x600"},{"id":5,"channel":...</msg>
<status status="PASS" starttime="******** 12:35:20.836" endtime="******** 12:35:20.838"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.601" endtime="******** 12:35:20.838"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:35:20.840" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.840"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:35:20.840" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.840"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.840"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.840"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:35:20.840" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.840"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.840"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.838" endtime="******** 12:35:20.840"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}">marketingChannels[0]:channel</var>
<var name="${value}">ATM</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 12:35:20.852" level="INFO">Field: marketingChannels[0]:channel , having a value: ATM was found.</msg>
<msg timestamp="******** 12:35:20.852" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.852"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 12:35:20.852" endtime="******** 12:35:20.852"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.852"/>
</iter>
<iter>
<var name="${key}">languages[1]:language</var>
<var name="${value}">Afrikaans</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 12:35:20.852" level="INFO">Field: languages[1]:language , having a value: Afrikaans was found.</msg>
<msg timestamp="******** 12:35:20.852" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 12:35:20.852" endtime="******** 12:35:20.852"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 12:35:20.852" endtime="******** 12:35:20.852"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.852" endtime="******** 12:35:20.852"/>
</iter>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.852"/>
</for>
<status status="PASS" starttime="******** 12:35:20.840" endtime="******** 12:35:20.852"/>
</kw>
<status status="PASS" starttime="******** 12:35:20.578" endtime="******** 12:35:20.852"/>
</kw>
<doc>Gets CampaignLookUp id Result using a Business App auth</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 12:35:20.578" endtime="******** 12:35:20.852"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 12:35:17.939" endtime="******** 12:35:23.777"/>
</suite>
<statistics>
<total>
<stat pass="2" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="2" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="2" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="2" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
