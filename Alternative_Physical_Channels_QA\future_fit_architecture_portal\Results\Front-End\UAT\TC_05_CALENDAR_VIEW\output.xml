<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 08:52:00.260" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_05_CALENDAR_VIEW.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:52:00.598" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.598"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:52:00.598" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.598"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:52:00.598" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.598"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.598"/>
</kw>
<test id="s1-t1" name="BU - Calendar View - Navigation - &lt; &amp; &gt; buttons" line="42">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057350</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:52:00.598" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.598"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:52:00.598" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057350'.</msg>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.598"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:52:00.713" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:00.713"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:00.713"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:00.713"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:52:00.713" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:00.713"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:52:00.713" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:00.713"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:00.713"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:52:00.908" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:52:01.447" level="INFO">${rc_code} = 128</msg>
<msg timestamp="******** 08:52:01.447" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 08:52:01.447" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:52:01.447" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002032CEA98E0&gt;</msg>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:52:01.447" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:52:01.447" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:52:01.447" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:01.447"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:52:01.447" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:52:01.447" endtime="******** 08:52:06.231"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:06.231"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:52:06.234" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:52:06.234" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:52:06.232" endtime="******** 08:52:06.234"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:52:06.234" endtime="******** 08:52:06.240"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:52:06.245" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:52:06.245" endtime="******** 08:52:06.509"/>
</kw>
<status status="PASS" starttime="******** 08:52:06.242" endtime="******** 08:52:06.509"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:52:16.511" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:52:06.509" endtime="******** 08:52:16.511"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:52:16.511" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:52:16.527" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:52:16.511" endtime="******** 08:52:16.527"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:52:16.527" endtime="******** 08:52:16.527"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:52:36.527" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:52:16.527" endtime="******** 08:52:36.527"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:52:36.527" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:52:36.574" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:52:36.527" endtime="******** 08:52:36.574"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:52:36.574" endtime="******** 08:52:36.574"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:52:46.574" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:52:36.574" endtime="******** 08:52:46.574"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:52:46.574" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:52:46.599" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:52:46.574" endtime="******** 08:52:46.599"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:52:46.599" endtime="******** 08:52:46.601"/>
</kw>
<status status="PASS" starttime="******** 08:52:06.231" endtime="******** 08:52:46.601"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:52:46.722" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-26.png"&gt;&lt;img src="selenium-screenshot-26.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:52:46.601" endtime="******** 08:52:46.722"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:46.722"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.713" endtime="******** 08:52:46.722"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:52:46.722"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:52:51.735" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:52:46.723" endtime="******** 08:52:51.735"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:52:51.735" endtime="******** 08:52:51.746"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:52:51.746" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 08:52:51.746" endtime="******** 08:52:52.111"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:52:52.111" endtime="******** 08:52:52.140"/>
</kw>
<status status="PASS" starttime="******** 08:52:51.735" endtime="******** 08:52:52.140"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>---------------------------- validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:52:52.140" endtime="******** 08:52:52.140"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:53:02.140" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:52:52.140" endtime="******** 08:53:02.140"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${get_month_year}</var>
<arg>${CURRENT_MONTH_YEAR_LOCATOR}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 08:53:02.151" level="INFO">${get_month_year} = May 2024</msg>
<status status="PASS" starttime="******** 08:53:02.140" endtime="******** 08:53:02.151"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${get_month_year}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:02.151" endtime="******** 08:53:02.151"/>
</kw>
<kw name="Get Current Date" library="DateTime">
<var>${current}</var>
<arg>result_format=%Y-%m-%d %H:%M:%S</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="******** 08:53:02.151" level="INFO">${current} = 2024-05-30 08:53:02</msg>
<status status="PASS" starttime="******** 08:53:02.151" endtime="******** 08:53:02.151"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${CURRENT_DATE}</var>
<arg>${current}</arg>
<arg>result_format=%B %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 08:53:02.169" level="INFO">${CURRENT_DATE} = May 2024</msg>
<status status="PASS" starttime="******** 08:53:02.151" endtime="******** 08:53:02.169"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>NEXT MONTH ${CURRENT_DATE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:02.169" endtime="******** 08:53:02.169"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>${CURRENT_DATE}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 08:53:02.169" endtime="******** 08:53:02.239"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${PREVIOUS_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:53:02.240" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[1]'.</msg>
<status status="PASS" starttime="******** 08:53:02.239" endtime="******** 08:53:02.571"/>
</kw>
<kw name="Subtract Time From Date" library="DateTime">
<var>${get_previous_month}</var>
<arg>${current}</arg>
<arg>31 days</arg>
<doc>Subtracts time from date and returns the resulting date.</doc>
<msg timestamp="******** 08:53:02.571" level="INFO">${get_previous_month} = 2024-04-29 08:53:02.000</msg>
<status status="PASS" starttime="******** 08:53:02.571" endtime="******** 08:53:02.571"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${PREVIOUS_MONTH_DATE}</var>
<arg>${get_previous_month}</arg>
<arg>result_format=%B %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 08:53:02.571" level="INFO">${PREVIOUS_MONTH_DATE} = April 2024</msg>
<status status="PASS" starttime="******** 08:53:02.571" endtime="******** 08:53:02.571"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>PREVIOUS MONTH ${PREVIOUS_MONTH_DATE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:02.572" endtime="******** 08:53:02.574"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>${PREVIOUS_MONTH_DATE}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 08:53:02.575" endtime="******** 08:53:02.652"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:53:07.653" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:53:02.652" endtime="******** 08:53:07.653"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:53:07.653" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[2]'.</msg>
<status status="PASS" starttime="******** 08:53:07.653" endtime="******** 08:53:08.232"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:53:08.233" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[2]'.</msg>
<status status="PASS" starttime="******** 08:53:08.233" endtime="******** 08:53:08.323"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${get_next_month}</var>
<arg>${current}</arg>
<arg>31 days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="******** 08:53:08.323" level="INFO">${get_next_month} = 2024-06-30 08:53:02.000</msg>
<status status="PASS" starttime="******** 08:53:08.323" endtime="******** 08:53:08.323"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${NEXT_MONTH_DATE}</var>
<arg>${get_next_month}</arg>
<arg>result_format=%B %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 08:53:08.324" level="INFO">${NEXT_MONTH_DATE} = June 2024</msg>
<status status="PASS" starttime="******** 08:53:08.324" endtime="******** 08:53:08.324"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>NEXT MONTH ${NEXT_MONTH_DATE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:08.324" endtime="******** 08:53:08.325"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>${NEXT_MONTH_DATE}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 08:53:08.325" endtime="******** 08:53:08.338"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>validateUserCanNavigate.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:53:08.437" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="validateUserCanNavigate.png"&gt;&lt;img src="validateUserCanNavigate.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:53:08.338" endtime="******** 08:53:08.437"/>
</kw>
<status status="PASS" starttime="******** 08:52:52.140" endtime="******** 08:53:08.437"/>
</kw>
<status status="PASS" starttime="******** 08:52:52.140" endtime="******** 08:53:08.437"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.437" endtime="******** 08:53:08.439"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.439" endtime="******** 08:53:08.440"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.440"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.440"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.440"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.440"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.440"/>
</kw>
<status status="PASS" starttime="******** 08:52:52.140" endtime="******** 08:53:08.440"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.450"/>
</kw>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.454"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:53:08.454" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:53:08.555" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-27.png"&gt;&lt;img src="selenium-screenshot-27.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:53:08.555" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:53:08.454" endtime="******** 08:53:08.561"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:53:08.561" endtime="******** 08:53:08.561"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:53:08.561" endtime="******** 08:53:08.561"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:53:08.561" endtime="******** 08:53:08.561"/>
</kw>
<status status="FAIL" starttime="******** 08:53:08.454" endtime="******** 08:53:08.561"/>
</kw>
<status status="PASS" starttime="******** 08:53:08.454" endtime="******** 08:53:08.561"/>
</kw>
<status status="PASS" starttime="******** 08:53:08.440" endtime="******** 08:53:08.561"/>
</kw>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:53:08.561"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:52:00.598" endtime="******** 08:53:08.592"/>
</test>
<test id="s1-t2" name="BU- Calendar View- Preview" line="44">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057349</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>user can view a campaign</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:53:12.032" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 08:53:12.032" endtime="******** 08:53:12.032"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:53:12.032" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057349'.</msg>
<status status="PASS" starttime="******** 08:53:12.032" endtime="******** 08:53:12.032"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:53:12.032" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:53:12.032" endtime="******** 08:53:12.032"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:12.032" endtime="******** 08:53:12.032"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:12.032" endtime="******** 08:53:12.033"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:53:12.033" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:53:12.033"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:53:12.033" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:53:12.033"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:53:12.033"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:53:12.173" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:53:12.949" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:53:12.949" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 26476 has been terminated.
SUCCESS: The process "chrome.exe" with PID 21748 has been terminated.
SUCCESS: The process "chrome.exe" with PID 6224 has been ter...</msg>
<status status="PASS" starttime="******** 08:53:12.034" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:53:12.949" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002032CECEA80&gt;</msg>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:53:12.949" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:53:12.949" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:53:12.949" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:12.949"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:53:12.949" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:53:12.949" endtime="******** 08:53:17.757"/>
</kw>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:53:17.757"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:53:17.762" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:53:17.762" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:53:17.758" endtime="******** 08:53:17.762"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:53:17.763" endtime="******** 08:53:17.771"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:53:17.777" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:53:17.777" endtime="******** 08:53:19.217"/>
</kw>
<status status="PASS" starttime="******** 08:53:17.771" endtime="******** 08:53:19.217"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:53:29.229" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:53:19.217" endtime="******** 08:53:29.229"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:53:29.229" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:53:29.246" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:53:29.229" endtime="******** 08:53:29.246"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:53:29.246" endtime="******** 08:53:29.246"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:53:49.250" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:53:29.246" endtime="******** 08:53:49.250"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:53:49.250" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:53:49.844" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:53:49.251" endtime="******** 08:53:49.844"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:53:49.844" endtime="******** 08:53:49.844"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:53:59.916" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:53:49.844" endtime="******** 08:53:59.916"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:53:59.916" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:53:59.975" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:53:59.916" endtime="******** 08:53:59.975"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:53:59.975" endtime="******** 08:53:59.975"/>
</kw>
<status status="PASS" starttime="******** 08:53:17.758" endtime="******** 08:53:59.975"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:54:00.105" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-28.png"&gt;&lt;img src="selenium-screenshot-28.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:53:59.975" endtime="******** 08:54:00.105"/>
</kw>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:54:00.105"/>
</kw>
<status status="PASS" starttime="******** 08:53:12.033" endtime="******** 08:54:00.105"/>
</kw>
<status status="PASS" starttime="******** 08:53:12.032" endtime="******** 08:54:00.105"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:54:05.129" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:54:00.105" endtime="******** 08:54:05.129"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:05.131" endtime="******** 08:54:05.132"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:05.132" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 08:54:05.132" endtime="******** 08:54:05.288"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:54:05.288" endtime="******** 08:54:05.369"/>
</kw>
<status status="PASS" starttime="******** 08:54:05.131" endtime="******** 08:54:05.369"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:05.369" endtime="******** 08:54:05.371"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="validate that the user can view a campaign" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>---------------------------- user views details inside the calendar</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:05.371" endtime="******** 08:54:05.371"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:54:10.372" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:54:05.371" endtime="******** 08:54:10.372"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${CAMPAIGN_ID_DESCRIPTION}</var>
<arg>${CAMPAIGN_ID_DESCRIPTION_xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:10.458" level="INFO">${CAMPAIGN_ID_DESCRIPTION} = May 2024
month
Sun
Mon
Tue
Wed
Thu
Fri
Sat
28
29
30
1
2
3
4
5
6
7
8
9
10
11
Campaign History Test3 ( SSK )
12
Campaign History Test3 ( SSK )
13
Edit Camp ( ATM )
14
May 13th ( ATM )
15
16
Reverse camp...</msg>
<status status="PASS" starttime="******** 08:54:10.372" endtime="******** 08:54:10.458"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>-----------------</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:10.458" endtime="******** 08:54:10.458"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${CAMPAIGN_ID_DESCRIPTION}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:10.458" endtime="******** 08:54:10.474"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:54:15.474" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:54:10.474" endtime="******** 08:54:15.474"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAMPAIGN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:15.475" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[3]/td[2]/div/div[2]/div[1]/a'.</msg>
<status status="PASS" starttime="******** 08:54:15.474" endtime="******** 08:54:15.963"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>15s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:54:30.964" level="INFO">Slept 15 seconds</msg>
<status status="PASS" starttime="******** 08:54:15.963" endtime="******** 08:54:30.964"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:30.984" level="INFO">Current page contains element 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 08:54:30.964" endtime="******** 08:54:30.984"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_BY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:31.036" level="INFO">Current page contains element 'name=campaignBy'.</msg>
<status status="PASS" starttime="******** 08:54:30.984" endtime="******** 08:54:31.036"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${RECEIVE_DEVICE_TYPE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:31.052" level="INFO">Current page contains element 'name=receiverDeviceType'.</msg>
<status status="PASS" starttime="******** 08:54:31.036" endtime="******** 08:54:31.052"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:31.064" level="INFO">Current page contains element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 08:54:31.052" endtime="******** 08:54:31.064"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:31.073" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button'.</msg>
<status status="PASS" starttime="******** 08:54:31.064" endtime="******** 08:54:31.073"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_WITH_CAMPAIGN_DATE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:31.180" level="INFO">Current page contains element 'id=testing'.</msg>
<status status="PASS" starttime="******** 08:54:31.073" endtime="******** 08:54:31.180"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:31.180" level="INFO">Clicking element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 08:54:31.180" endtime="******** 08:54:31.296"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:54:36.304" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:54:31.296" endtime="******** 08:54:36.304"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${REVIEW_MARKETING_SCREENS}</arg>
<arg>5</arg>
<arg>Review Marketing Screens</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:54:36.304" endtime="******** 08:54:36.399"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${LANGUAGE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.469" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer/div[1]'.</msg>
<status status="PASS" starttime="******** 08:54:36.399" endtime="******** 08:54:36.469"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${DURATION}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.509" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[1]/div[1]/div[2]/mat-card/mat-calendar/div/mat-month-view/table/tbody/tr[1]/td[3]/button/div[2]'.</msg>
<status status="PASS" starttime="******** 08:54:36.469" endtime="******** 08:54:36.509"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${PRIORITY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.522" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer'.</msg>
<status status="PASS" starttime="******** 08:54:36.509" endtime="******** 08:54:36.522"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${BACK_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:36.522" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/button'.</msg>
<status status="PASS" starttime="******** 08:54:36.522" endtime="******** 08:54:36.727"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.769" level="INFO">Current page contains element 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 08:54:36.727" endtime="******** 08:54:36.769"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_BY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.777" level="INFO">Current page contains element 'name=campaignBy'.</msg>
<status status="PASS" starttime="******** 08:54:36.769" endtime="******** 08:54:36.777"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${RECEIVE_DEVICE_TYPE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.783" level="INFO">Current page contains element 'name=receiverDeviceType'.</msg>
<status status="PASS" starttime="******** 08:54:36.777" endtime="******** 08:54:36.783"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.793" level="INFO">Current page contains element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 08:54:36.783" endtime="******** 08:54:36.793"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.793" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button'.</msg>
<status status="PASS" starttime="******** 08:54:36.793" endtime="******** 08:54:36.793"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_WITH_CAMPAIGN_DATE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:36.810" level="INFO">Current page contains element 'id=testing'.</msg>
<status status="PASS" starttime="******** 08:54:36.793" endtime="******** 08:54:36.810"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:36.810" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button'.</msg>
<status status="PASS" starttime="******** 08:54:36.810" endtime="******** 08:54:37.065"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:54:42.066" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:54:37.065" endtime="******** 08:54:42.066"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${FULL_CALENDAR}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:54:42.072" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar'.</msg>
<status status="PASS" starttime="******** 08:54:42.066" endtime="******** 08:54:42.072"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>validateUserCanViewCampaign.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:54:42.171" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="validateUserCanViewCampaign.png"&gt;&lt;img src="validateUserCanViewCampaign.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:54:42.072" endtime="******** 08:54:42.171"/>
</kw>
<status status="PASS" starttime="******** 08:54:05.371" endtime="******** 08:54:42.171"/>
</kw>
<status status="PASS" starttime="******** 08:54:05.371" endtime="******** 08:54:42.171"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.171"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.171"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.171"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.171"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.171"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.171"/>
</kw>
<status status="PASS" starttime="******** 08:54:05.369" endtime="******** 08:54:42.171"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.198"/>
</kw>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.198"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:54:42.203" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:54:42.322" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-29.png"&gt;&lt;img src="selenium-screenshot-29.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:54:42.322" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:54:42.203" endtime="******** 08:54:42.322"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:54:42.322" endtime="******** 08:54:42.322"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:54:42.322" endtime="******** 08:54:42.322"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:54:42.322" endtime="******** 08:54:42.322"/>
</kw>
<status status="FAIL" starttime="******** 08:54:42.203" endtime="******** 08:54:42.322"/>
</kw>
<status status="PASS" starttime="******** 08:54:42.198" endtime="******** 08:54:42.322"/>
</kw>
<status status="PASS" starttime="******** 08:54:42.171" endtime="******** 08:54:42.322"/>
</kw>
<status status="PASS" starttime="******** 08:53:12.031" endtime="******** 08:54:42.322"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:53:12.031" endtime="******** 08:54:42.322"/>
</test>
<test id="s1-t3" name="BU- Calendar view- Preview- Next, Back &amp; Close Buttons" line="46">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057373</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>user can view a campaign</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:54:45.664" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:54:45.664" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057373'.</msg>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:54:45.664" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:54:45.664" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:54:45.664" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:45.664"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:54:45.792" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:54:46.511" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:54:46.511" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 5692 has been terminated.
SUCCESS: The process "chrome.exe" with PID 23468 has been terminated.
SUCCESS: The process "chrome.exe" with PID 4528 has been term...</msg>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:54:46.511" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002032CECDA30&gt;</msg>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:54:46.511" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:54:46.511" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:54:46.511" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:46.511"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:54:46.511" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:54:46.511" endtime="******** 08:54:51.285"/>
</kw>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:54:51.285"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:54:51.286" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:54:51.286" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:54:51.286" endtime="******** 08:54:51.286"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:54:51.286" endtime="******** 08:54:51.291"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:54:51.293" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:54:51.293" endtime="******** 08:54:51.505"/>
</kw>
<status status="PASS" starttime="******** 08:54:51.291" endtime="******** 08:54:51.505"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:55:01.505" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:54:51.505" endtime="******** 08:55:01.505"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:55:01.505" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:55:01.555" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:55:01.505" endtime="******** 08:55:01.555"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:55:01.555" endtime="******** 08:55:01.555"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:55:21.556" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:55:01.555" endtime="******** 08:55:21.556"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:55:21.556" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:55:21.630" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:55:21.556" endtime="******** 08:55:21.630"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:55:21.630" endtime="******** 08:55:21.630"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:55:31.630" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:55:21.630" endtime="******** 08:55:31.631"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:55:31.631" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:55:31.637" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:55:31.631" endtime="******** 08:55:31.637"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:55:31.637" endtime="******** 08:55:31.637"/>
</kw>
<status status="PASS" starttime="******** 08:54:51.285" endtime="******** 08:55:31.637"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:55:31.801" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-30.png"&gt;&lt;img src="selenium-screenshot-30.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:55:31.637" endtime="******** 08:55:31.801"/>
</kw>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:55:31.801"/>
</kw>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:55:31.801"/>
</kw>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:55:31.801"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:55:36.815" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:55:31.803" endtime="******** 08:55:36.815"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:55:36.815" endtime="******** 08:55:36.822"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:55:36.822" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 08:55:36.822" endtime="******** 08:55:36.883"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:55:36.884" endtime="******** 08:55:37.008"/>
</kw>
<status status="PASS" starttime="******** 08:55:36.815" endtime="******** 08:55:37.008"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:55:37.008" endtime="******** 08:55:37.008"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="validate that the user can view a campaign" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>---------------------------- user views details inside the calendar</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:55:37.008" endtime="******** 08:55:37.013"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:55:42.014" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:55:37.013" endtime="******** 08:55:42.014"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${CAMPAIGN_ID_DESCRIPTION}</var>
<arg>${CAMPAIGN_ID_DESCRIPTION_xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 08:55:42.158" level="INFO">${CAMPAIGN_ID_DESCRIPTION} = May 2024
month
Sun
Mon
Tue
Wed
Thu
Fri
Sat
28
29
30
1
2
3
4
5
6
7
8
9
10
11
Campaign History Test3 ( SSK )
12
Campaign History Test3 ( SSK )
13
Edit Camp ( ATM )
14
May 13th ( ATM )
15
16
Reverse camp...</msg>
<status status="PASS" starttime="******** 08:55:42.014" endtime="******** 08:55:42.158"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>-----------------</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:55:42.158" endtime="******** 08:55:42.158"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${CAMPAIGN_ID_DESCRIPTION}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:55:42.158" endtime="******** 08:55:42.167"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:55:47.168" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:55:42.167" endtime="******** 08:55:47.168"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAMPAIGN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:55:47.168" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[3]/td[2]/div/div[2]/div[1]/a'.</msg>
<status status="PASS" starttime="******** 08:55:47.168" endtime="******** 08:55:47.732"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>15s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:56:02.791" level="INFO">Slept 15 seconds</msg>
<status status="PASS" starttime="******** 08:55:47.732" endtime="******** 08:56:02.791"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:02.898" level="INFO">Current page contains element 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 08:56:02.791" endtime="******** 08:56:02.898"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_BY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:02.971" level="INFO">Current page contains element 'name=campaignBy'.</msg>
<status status="PASS" starttime="******** 08:56:02.898" endtime="******** 08:56:02.971"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${RECEIVE_DEVICE_TYPE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:02.988" level="INFO">Current page contains element 'name=receiverDeviceType'.</msg>
<status status="PASS" starttime="******** 08:56:02.971" endtime="******** 08:56:02.988"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:03.068" level="INFO">Current page contains element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 08:56:02.988" endtime="******** 08:56:03.068"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:03.083" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button'.</msg>
<status status="PASS" starttime="******** 08:56:03.068" endtime="******** 08:56:03.083"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_WITH_CAMPAIGN_DATE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:03.135" level="INFO">Current page contains element 'id=testing'.</msg>
<status status="PASS" starttime="******** 08:56:03.083" endtime="******** 08:56:03.135"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:56:03.135" level="INFO">Clicking element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 08:56:03.135" endtime="******** 08:56:03.474"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:56:08.474" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:56:03.474" endtime="******** 08:56:08.474"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${REVIEW_MARKETING_SCREENS}</arg>
<arg>5</arg>
<arg>Review Marketing Screens</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:56:08.474" endtime="******** 08:56:08.593"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${LANGUAGE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.613" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer/div[1]'.</msg>
<status status="PASS" starttime="******** 08:56:08.594" endtime="******** 08:56:08.613"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${DURATION}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.622" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[1]/div[1]/div[2]/mat-card/mat-calendar/div/mat-month-view/table/tbody/tr[1]/td[3]/button/div[2]'.</msg>
<status status="PASS" starttime="******** 08:56:08.613" endtime="******** 08:56:08.623"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${PRIORITY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.630" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer'.</msg>
<status status="PASS" starttime="******** 08:56:08.623" endtime="******** 08:56:08.630"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${BACK_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:56:08.630" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/button'.</msg>
<status status="PASS" starttime="******** 08:56:08.630" endtime="******** 08:56:08.784"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.951" level="INFO">Current page contains element 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 08:56:08.785" endtime="******** 08:56:08.951"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_BY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.959" level="INFO">Current page contains element 'name=campaignBy'.</msg>
<status status="PASS" starttime="******** 08:56:08.951" endtime="******** 08:56:08.959"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${RECEIVE_DEVICE_TYPE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.965" level="INFO">Current page contains element 'name=receiverDeviceType'.</msg>
<status status="PASS" starttime="******** 08:56:08.959" endtime="******** 08:56:08.965"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.972" level="INFO">Current page contains element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 08:56:08.965" endtime="******** 08:56:08.972"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.978" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button'.</msg>
<status status="PASS" starttime="******** 08:56:08.972" endtime="******** 08:56:08.978"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_WITH_CAMPAIGN_DATE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:08.984" level="INFO">Current page contains element 'id=testing'.</msg>
<status status="PASS" starttime="******** 08:56:08.978" endtime="******** 08:56:08.984"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:56:08.984" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button'.</msg>
<status status="PASS" starttime="******** 08:56:08.984" endtime="******** 08:56:09.199"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:56:14.199" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:56:09.199" endtime="******** 08:56:14.199"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${FULL_CALENDAR}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 08:56:14.204" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar'.</msg>
<status status="PASS" starttime="******** 08:56:14.199" endtime="******** 08:56:14.204"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>validateUserCanViewCampaign.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:56:14.307" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="validateUserCanViewCampaign.png"&gt;&lt;img src="validateUserCanViewCampaign.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:56:14.204" endtime="******** 08:56:14.307"/>
</kw>
<status status="PASS" starttime="******** 08:55:37.008" endtime="******** 08:56:14.307"/>
</kw>
<status status="PASS" starttime="******** 08:55:37.008" endtime="******** 08:56:14.307"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.307"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.307"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.307"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.307"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.307"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.307"/>
</kw>
<status status="PASS" starttime="******** 08:55:37.008" endtime="******** 08:56:14.307"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.338"/>
</kw>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.338"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:56:14.338" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:56:14.449" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-31.png"&gt;&lt;img src="selenium-screenshot-31.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:56:14.449" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:56:14.338" endtime="******** 08:56:14.449"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:56:14.449" endtime="******** 08:56:14.449"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:56:14.449" endtime="******** 08:56:14.449"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:56:14.449" endtime="******** 08:56:14.449"/>
</kw>
<status status="FAIL" starttime="******** 08:56:14.338" endtime="******** 08:56:14.449"/>
</kw>
<status status="PASS" starttime="******** 08:56:14.338" endtime="******** 08:56:14.449"/>
</kw>
<status status="PASS" starttime="******** 08:56:14.307" endtime="******** 08:56:14.449"/>
</kw>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:56:14.449"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:54:45.664" endtime="******** 08:56:14.449"/>
</test>
<test id="s1-t4" name="BU- Verify approved campaigns are highlighted in green" line="49">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057395</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>BU validates that approved campaigns are highlighted in green</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:56:17.758" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:56:17.758" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057395'.</msg>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:56:17.758" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:56:17.758" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:56:17.758" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:17.758"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:56:17.916" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:56:18.535" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:56:18.535" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 17564 has been terminated.
SUCCESS: The process "chrome.exe" with PID 15852 has been terminated.
SUCCESS: The process "chrome.exe" with PID 6912 has been ter...</msg>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:56:18.535" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002032CE87650&gt;</msg>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:56:18.535" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:56:18.535" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:56:18.535" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:18.535"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:56:18.535" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:56:18.535" endtime="******** 08:56:23.502"/>
</kw>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:56:23.502"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:56:23.505" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:56:23.505" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:56:23.502" endtime="******** 08:56:23.505"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:56:23.508" endtime="******** 08:56:23.513"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:56:23.516" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:56:23.516" endtime="******** 08:56:23.730"/>
</kw>
<status status="PASS" starttime="******** 08:56:23.513" endtime="******** 08:56:23.731"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:56:33.731" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:56:23.731" endtime="******** 08:56:33.731"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:56:33.731" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:56:33.752" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:56:33.731" endtime="******** 08:56:33.752"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:56:33.752" endtime="******** 08:56:33.752"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:56:53.753" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:56:33.752" endtime="******** 08:56:53.753"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:56:53.753" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:56:53.753" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:56:53.753" endtime="******** 08:56:53.753"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:56:53.753" endtime="******** 08:56:53.760"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:57:03.760" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:56:53.760" endtime="******** 08:57:03.760"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:57:03.760" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:57:03.801" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:57:03.760" endtime="******** 08:57:03.801"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:57:03.801" endtime="******** 08:57:03.801"/>
</kw>
<status status="PASS" starttime="******** 08:56:23.502" endtime="******** 08:57:03.801"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:57:03.926" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-32.png"&gt;&lt;img src="selenium-screenshot-32.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:57:03.801" endtime="******** 08:57:03.926"/>
</kw>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:57:03.927"/>
</kw>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:57:03.927"/>
</kw>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:57:03.927"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:57:08.927" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:57:03.927" endtime="******** 08:57:08.927"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:57:08.927" endtime="******** 08:57:08.987"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:57:08.988" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 08:57:08.988" endtime="******** 08:57:09.483"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:57:09.483" endtime="******** 08:57:09.507"/>
</kw>
<status status="PASS" starttime="******** 08:57:08.927" endtime="******** 08:57:09.507"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.507"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.507"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.507"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate Approved Campaigns Are Green" library="CalendarView">
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${approved_elements}</var>
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[3]/td[2]/div/div[2]/div[1]/a</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="******** 08:57:09.517" level="INFO">${approved_elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="5c5964044a700247b8896d6e9702b41a", element="f.3678983DC73DE6CFCC0F89CF131F3C53.d.01AE7628577D87B0048659DC921C0237.e.202")&gt;]</msg>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.518"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${approved_elements}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 08:57:09.518" endtime="******** 08:57:09.518"/>
</kw>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.518"/>
</kw>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.518"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.518" endtime="******** 08:57:09.518"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.519" endtime="******** 08:57:09.519"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.519" endtime="******** 08:57:09.519"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:09.519" endtime="******** 08:57:09.519"/>
</kw>
<status status="PASS" starttime="******** 08:57:09.507" endtime="******** 08:57:09.519"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:57:09.519" endtime="******** 08:57:09.528"/>
</kw>
<status status="PASS" starttime="******** 08:57:09.519" endtime="******** 08:57:09.528"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:57:09.528" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:57:09.729" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-33.png"&gt;&lt;img src="selenium-screenshot-33.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:57:09.729" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:57:09.528" endtime="******** 08:57:09.732"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:57:09.732" endtime="******** 08:57:09.732"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:57:09.732" endtime="******** 08:57:09.732"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:57:09.732" endtime="******** 08:57:09.732"/>
</kw>
<status status="FAIL" starttime="******** 08:57:09.528" endtime="******** 08:57:09.732"/>
</kw>
<status status="PASS" starttime="******** 08:57:09.528" endtime="******** 08:57:09.732"/>
</kw>
<status status="PASS" starttime="******** 08:57:09.519" endtime="******** 08:57:09.732"/>
</kw>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:57:09.732"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:56:17.758" endtime="******** 08:57:09.820"/>
</test>
<test id="s1-t5" name="BU- Verify non-approved campaigns are marked grey" line="51">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057397</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>BU Validates that non-approved campaigns are marked grey</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:57:13.427" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:57:13.427" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057397'.</msg>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:57:13.427" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:57:13.427" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:57:13.427" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.427"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:13.480"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:57:13.966" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:57:14.676" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:57:14.676" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 6004 has been terminated.
SUCCESS: The process "chrome.exe" with PID 30584 has been terminated.
SUCCESS: The process "chrome.exe" with PID 9180 has been term...</msg>
<status status="PASS" starttime="******** 08:57:13.480" endtime="******** 08:57:14.676"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:57:14.677" endtime="******** 08:57:14.677"/>
</kw>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:14.677"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:57:14.678" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002032CE78770&gt;</msg>
<status status="PASS" starttime="******** 08:57:14.677" endtime="******** 08:57:14.678"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:57:14.678" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:57:14.678" endtime="******** 08:57:14.678"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:57:14.678" endtime="******** 08:57:14.678"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:57:14.679" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:57:14.679" endtime="******** 08:57:14.679"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:57:14.679" endtime="******** 08:57:14.679"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:57:14.679" endtime="******** 08:57:14.679"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:57:14.680" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:57:14.680" endtime="******** 08:57:14.680"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:57:14.680" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:57:14.680" endtime="******** 08:57:20.146"/>
</kw>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:57:20.146"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:57:20.149" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:57:20.150" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:57:20.147" endtime="******** 08:57:20.150"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:57:20.150" endtime="******** 08:57:20.156"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:57:20.158" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:57:20.158" endtime="******** 08:57:20.426"/>
</kw>
<status status="PASS" starttime="******** 08:57:20.156" endtime="******** 08:57:20.426"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:57:30.427" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:57:20.426" endtime="******** 08:57:30.427"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:57:30.427" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:57:30.625" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:57:30.427" endtime="******** 08:57:30.625"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:57:30.625" endtime="******** 08:57:30.625"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:57:50.626" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:57:30.625" endtime="******** 08:57:50.626"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:57:50.626" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:57:50.644" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:57:50.626" endtime="******** 08:57:50.644"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:57:50.644" endtime="******** 08:57:50.644"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:58:00.648" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:57:50.644" endtime="******** 08:58:00.648"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:58:00.648" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:58:00.661" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:58:00.648" endtime="******** 08:58:00.661"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:58:00.661" endtime="******** 08:58:00.661"/>
</kw>
<status status="PASS" starttime="******** 08:57:20.146" endtime="******** 08:58:00.661"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:58:00.772" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-34.png"&gt;&lt;img src="selenium-screenshot-34.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:58:00.661" endtime="******** 08:58:00.772"/>
</kw>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:58:00.772"/>
</kw>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:58:00.772"/>
</kw>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:58:00.772"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:58:05.773" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:58:00.772" endtime="******** 08:58:05.773"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:58:05.773" endtime="******** 08:58:05.773"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:58:05.773" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 08:58:05.773" endtime="******** 08:58:05.856"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 08:58:05.856" endtime="******** 08:58:05.876"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.773" endtime="******** 08:58:05.876"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.876"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.876"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.876"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.876"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.876"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate Non-approved Campaigns Are Grey" library="CalendarView">
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${non_approved_elements}</var>
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[3]/td[1]/div/div[2]/div[1]/a</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="******** 08:58:05.886" level="INFO">${non_approved_elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="1d00260a7efd4a781a5f2c659a92120d", element="f.06220AB32DF87C0B6B1FBE13A8866809.d.E680E5A928BA9E7197A7267962EC87CC.e.202")&gt;]</msg>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.886"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${non_approved_elements}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 08:58:05.886" endtime="******** 08:58:05.886"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.886"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.886"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.886" endtime="******** 08:58:05.886"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:58:05.886" endtime="******** 08:58:05.886"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.876" endtime="******** 08:58:05.886"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:58:05.886" endtime="******** 08:58:05.906"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.886" endtime="******** 08:58:05.906"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:58:05.906" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:58:06.054" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-35.png"&gt;&lt;img src="selenium-screenshot-35.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:58:06.054" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:58:05.906" endtime="******** 08:58:06.056"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:58:06.056" endtime="******** 08:58:06.056"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:58:06.056" endtime="******** 08:58:06.056"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:58:06.056" endtime="******** 08:58:06.056"/>
</kw>
<status status="FAIL" starttime="******** 08:58:05.906" endtime="******** 08:58:06.057"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.906" endtime="******** 08:58:06.057"/>
</kw>
<status status="PASS" starttime="******** 08:58:05.886" endtime="******** 08:58:06.057"/>
</kw>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:58:06.057"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:57:13.427" endtime="******** 08:58:06.059"/>
</test>
<doc>Testing future fit APC Portal Calendar</doc>
<status status="PASS" starttime="******** 08:52:00.370" endtime="******** 08:58:09.564"/>
</suite>
<statistics>
<total>
<stat pass="5" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="5" fail="0" skip="0">FFT HEALTHCHECK</stat>
<stat pass="5" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="5" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 08:52:01.447" level="WARN">There was error during termination of process</msg>
<msg timestamp="******** 08:52:03.458" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:52:16.511" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:52:36.527" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:52:46.574" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:53:12.028" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:53:15.072" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:53:29.229" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:53:49.250" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:53:59.916" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:54:45.638" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:54:48.564" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:55:01.505" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:55:21.556" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:55:31.631" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:56:17.756" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:56:20.665" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:56:33.731" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:56:53.753" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:57:03.760" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:57:13.372" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:57:16.979" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:57:30.427" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:57:50.626" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:58:00.648" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:58:09.405" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
