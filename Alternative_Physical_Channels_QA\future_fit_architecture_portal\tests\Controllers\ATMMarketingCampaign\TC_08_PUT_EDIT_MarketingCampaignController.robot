*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for editing an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_GET_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
Edit UnTargeted Campaign
    [Arguments]        ${DOCUMENTATION}    ${DATA_FILE}    ${BASE_URL}    ${SERVICE_PATH}         ${CAMPAIGN_ID}         ${CAMPAIGN_NAME}         ${RECEIVER_DEVICE_TYPE}        ${CAMPAIGN_START_DATE}        ${CAMPAIGN_END_DATE}        ${CAMPAIGN_IMAGE1}        ${CAMPAIGN_IMAGE_2}        ${CAMPAIGN_LANGUAGE_1}        ${CAMPAIGN_LANGUAGE_2}        ${SERVICE_PATH_ID}    ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}      ${EXPECTED_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the environment variables data
    ${CAMP_IMAGES}=      Create List        ${CAMPAIGN_IMAGE1}      ${CAMPAIGN_IMAGE_2}
    ${list_string} =    Evaluate    ','.join(${CAMP_IMAGES})
    Set Environment Variable  CAMP_IMAGES     ${list_string}

    ${CAMP_LNGS}=      Create List      ${CAMPAIGN_LANGUAGE_1}      ${CAMPAIGN_LANGUAGE_2}
    ${list_string} =    Evaluate    ','.join(${CAMP_LNGS})
    Set Environment Variable  CAMP_LNGS     ${list_string}

    Given The user creates JSON request for Edit Campaign      ${CAMPAIGN_ID}      ${CAMPAIGN_NAME}        ${RECEIVER_DEVICE_TYPE}    ${CAMPAIGN_START_DATE}      ${CAMPAIGN_END_DATE}
    When The user creates a rest session                       ${BASE_URL}
    And The user makes Put Rest Call with JSON payload         ${SERVICE_PATH}    ${SERVICE_PATH_ID}    ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                        ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The rest service must return the expected message     ${EXPECTED_MESSAGE}


| *** Test Cases ***                                                                                    |        *DOCUMENTATION*    		|         *DATA_FILE*                                            |     *BASE_URL*                    | *SERVICE_PATH*              |   *CAMPAIGN_ID*   |                *CAMPAIGN_NAME*                   |   *RECEIVER_DEVICE_TYPE*   |   *CAMPAIGN_START_DATE*   |   *CAMPAIGN_END_DATE*     |       *CAMPAIGN_IMAGE1*               |       *CAMPAIGN_IMAGE_2*               |    *CAMPAIGN_LANGUAGE_1*    |    *CAMPAIGN_LANGUAGE_2*    | *SERVICE_PATH_ID* | *EXPECTED_STATUS_CODE*    |    *JSON_RESPONSE_REASON*   |     *EXPECTED_MESSAGE*         |
#| FFT - Controllers - Change the Campaign Name                     | Edit UnTargeted Campaign    | Change the Campaign Name  		|     future_fit_architecture_portal/data/Campaign_EDIT.json     |    APC_API_DEV_BASE_URL           |  ATMMarketingCampaign       |        14694      |        Edited using the Automation API Script2   |                            |                           |                           |     images/MarketingB_en_1.jpg        |     images/Marketing_af_1.jpg          |            English		   |            Afrikaans		 |        0          |        200                |             OK              |     Campaign has been edited   |
#| FFT - Controllers - Change the Campaign Start Date               | Edit UnTargeted Campaign    | Change the Start Date     		|     future_fit_architecture_portal/data/Campaign_EDIT.json     |    APC_API_DEV_BASE_URL           |  ATMMarketingCampaign       |        14694      |        										   | 						    |           2               |							| 	  images/MarketingB_en_1.jpg	    |	  images/Marketing_af_1.jpg 		 |			  English		   | 			Afrikaans		 |        0          |        200                |             OK              |     Campaign has been edited   |
#| FFT - Controllers - Change the Campaign End Date                 | Edit UnTargeted Campaign    | Change the End Date       		|     future_fit_architecture_portal/data/Campaign_EDIT.json     |    APC_API_DEV_BASE_URL           |  ATMMarketingCampaign       |        14694      |        										   |							|							|           5               |     images/MarketingB_en_1.jpg	    |	  images/Marketing_af_1.jpg			 |			  English   	   |    		Afrikaans		 |        0          |        200                |             OK              |     Campaign has been edited   |
#| FFT - Controllers - Change the Campaign Images                   | Edit UnTargeted Campaign    | Change the Images		 		|     future_fit_architecture_portal/data/Campaign_EDIT.json     |    APC_API_DEV_BASE_URL           |  ATMMarketingCampaign       |        14694      |        										   | 							|							|							|     images/MarketingB_en_1.jpg        |     images/Marketing_af_1.jpg      	 |            English          |            Afrikaans        |        0          |        200                |             OK              |     Campaign has been edited   |
#| FFT - Controllers - Change the Campaign Receiver Device Type     | Edit UnTargeted Campaign    | Change the Receiver Device Type  |     future_fit_architecture_portal/data/Campaign_EDIT.json     |    APC_API_DEV_BASE_URL           |  ATMMarketingCampaign       |        14694      |        										   |           SSK              |                           |                           |     images/MarketingB_en_1.jpg    	|     images/Marketing_af_1.jpg		     |            English          |            Afrikaans        |        0          |        200                |             OK              |     Campaign has been edited   |
| FFT - Controllers - Change all editable fields of the Campaign   | Edit UnTargeted Campaign    | Change the Campaign Name  		|     future_fit_architecture_portal/data/Campaign_EDIT.json     |    APC_API_UAT_BASE_URL           |  ATMMarketingCampaign       |        15158      |        Edited using Automated Script             |           ATM              |           2               |           8               |     images/MarketingA_en_7_sot.jpg    |     images/MarketingB_en_7_zul.jpg     |            English            |            Afrikaans      |        0          |        200                |             OK              |     Campaign has been edited   |
