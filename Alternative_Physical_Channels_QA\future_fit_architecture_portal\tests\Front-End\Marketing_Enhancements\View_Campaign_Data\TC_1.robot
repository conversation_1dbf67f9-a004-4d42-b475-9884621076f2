*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing Camapaign Approval

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Logout.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../keywords/common/Navigation.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Campaign_History.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/View_Campaign_Data.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Export_Campaign.robot
Resource                                            ../../../../keywords/Marketing_Enhancements/Edit_Campaign.robot

*** Keyword ***
Validate Un Targeted Campaign Data
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    Then The user navigates to the Calendar View page

    And The user verifies Generic campaing data


| *Test Cases*                              |      *DOCUMENTATION*     |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   |
| RAC29a_TC_742_Verify_Un_Targeted_Campaign_Data  |  Validate Un Targeted Campaign Data  |  Campaign Export   |    BUSINESS_CAPTURER       |     APC_UAT             |