*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        FFT_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for deactivating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_GET_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${TESTRAIL_USERNAME}        <EMAIL>
${TESTRAIL_PASSWORD}        PasswordTR
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
PUT ATM Marketing Campaign 
    [Arguments]        ${DOCUMENTATION}    ${TESTRAIL_TESTCASE_ID}    ${DATA_FILE}    ${BASE_URL}    ${SERVICE_PATH}         ${SERVICE_PATH_ID}   ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}      ${EXPECTED_MESSAGE}    
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable                                   TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user prepares a json payload                             ${SUITE_NAME}       ${DATA_FILE}   
    When The user creates a rest session                               ${BASE_URL}
    And The user makes Put Rest Call                                  ${SERVICE_PATH}    ${SERVICE_PATH_ID}    ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                                ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The rest service must return the expected message      ${EXPECTED_MESSAGE}


| *** Test Cases ***                                                                                                                                             |               *DOCUMENTATION*                   |  *TESTRAIL_TESTCASE_ID* |      *DATA_FILE*                             |     *BASE_URL*                    | *SERVICE_PATH*              | *SERVICE_PATH_ID* | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON*              |     *EXPECTED_MESSAGE*      |
| FFT - Controllers - PUT ATM Marketing Campaign using a Business Approver   | PUT ATM Marketing Campaign                                                 | PUT ATM Marketing Campaign                      |    155057481		      |                                              |    APC_API_UAT_BASE_URL           |  ATMMarketingCampaign       | 14397             | 200                              | OK                                  |     Campaign Deactivated    |
