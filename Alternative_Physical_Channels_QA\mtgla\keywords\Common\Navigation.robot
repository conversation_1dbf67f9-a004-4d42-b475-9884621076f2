*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Navigation Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                            RequestsLibrary
Library                                            Screenshot
Library                                            ../../keywords/Account_Information.robot
#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${Calendar_Management_Link}            //*[text()[normalize-space(.)='Calendar Management']]

*** Keywords ***
the user navigates to the ATM Control Dashboard  
    Sleep    2s
    Click Element   xpath=/html/body/div[2]/button[1]
    Sleep    2s
    Click Element    xpath=//*[text()[normalize-space(.)='ATM Control']]
    Sleep    2s
    Click Element    xpath=//*[@id="ATMControl"]/a[1]
    Sleep    2s
    Page Should Contain    ATM Control Dashboard Date

the user navigates to Calendar Management Link
    Sleep    2s
    Click Element   xpath=/html/body/div[2]/button[1]
    Sleep    2s
    Click Element    xpath=//*[text()[normalize-space(.)='ATM Control']]
    Wait Until Element Is Enabled    ${Calendar_Management_Link}
    Click Element    ${Calendar_Management_Link}
    Page Should Contain    ATM Control Calendar Management
    Sleep    5s

the user navigates to the EOD Control Dashboard Menu
    Sleep    2s
    Click Element   xpath=/html/body/div[2]/button[1]
    Sleep    2s
    Click Element    xpath=//*[text()[normalize-space(.)='EOD Control']]
    Wait Until Element Is Enabled    //*[text()[normalize-space(.)='Dashboard']]
    Click Element    //*[text()[normalize-space(.)='Dashboard']]
    Page Should Contain    EOD Dashboard
    Sleep    5s

    Get Current Date and apend as Recon Date and Filter Date
    
    