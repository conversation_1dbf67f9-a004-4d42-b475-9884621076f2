*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             String
Variables                                          ../../utility/SQLVariables.py
#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***
Verify GET GasperDetails controller fields on the database
    [Arguments]     ${json_response}

    ${new_string}=   Convert To String    ${json_response}
    ${dict_value}=      Convert String to Dictionary    ${new_string}
    #Loop through the ATMs dictionary
    FOR    ${item}    IN    @{dict_value}

        ${cleaned_item_dict}=    Evaluate    {k.replace(' ', ''): v for k, v in ${item}.items()}

        #Get the current atm_id
        ${atm_id}=    Get From Dictionary    ${cleaned_item_dict}    id
        #Get the current atm_serial_num
        ${atm_serial_num}=    Get From Dictionary    ${cleaned_item_dict}    seriaL_NUM
        #Get the current atm 'address'
        ${atm_address}=    Get From Dictionary    ${cleaned_item_dict}    address
        #Get the current atm 'addresS2'
        ${atm_address2}=    Get From Dictionary    ${cleaned_item_dict}    addresS2
        #Get the current atm 'city'
        ${atm_city}=    Get From Dictionary    ${cleaned_item_dict}    city
        #Get the current atm 'region'
        ${atm_region}=    Get From Dictionary    ${cleaned_item_dict}    region
        #Get the current atm 'class'
        ${atm_class}=    Get From Dictionary    ${cleaned_item_dict}    class
        #Get the current atm 'datA_LINE'
        ${atm_data_line}=    Get From Dictionary    ${cleaned_item_dict}    datA_LINE
        #Get the current atm 'branch'
        ${atm_branch}=    Get From Dictionary    ${cleaned_item_dict}    branch
        #Get the current atm 'institution'
        ${atm_institution}=    Get From Dictionary    ${cleaned_item_dict}    institution
        #Get the current atm 'objecT_TYPE'
        ${atm_object_type}=    Get From Dictionary    ${cleaned_item_dict}    objecT_TYPE
        #Get the current atm 'iN_SERVICE'
        ${atm_in_service}=    Get From Dictionary    ${cleaned_item_dict}    iN_SERVICE

        IF      '${atm_in_service}' != 'None'
             ${atm_in_service}=    Replace String  ${atm_in_service}     T       ${SPACE}
        END
        #Get the current atm 'ouT_OF_SERVICE'
        ${atm_out_of_service}=    Get From Dictionary    ${cleaned_item_dict}    ouT_OF_SERVICE
        IF    "${atm_out_of_service}" != "None"
            ${atm_out_of_service}=    Replace String  ${atm_out_of_service}     T       ${SPACE}
        END
         #Get the current atm 'zip'
        ${atm_zip}=    Get From Dictionary    ${cleaned_item_dict}    zip
        #Get the current atm 'editoR_NOTE'
        ${atm_editor_note}=    Get From Dictionary    ${cleaned_item_dict}    editoR_NOTE
        #Get the current atm 'iP_ADDRESS'
        ${atm_ip_address}=    Get From Dictionary    ${cleaned_item_dict}    iP_ADDRESS
        #Get the current atm 'vendoR_SITE_ID'
        ${atm_vendor_site_id}=    Get From Dictionary    ${cleaned_item_dict}    vendoR_SITE_ID
        #Get the current atm 'pm'
        ${atm_pm}=    Get From Dictionary    ${cleaned_item_dict}    pm

        #Retrive the details of the current ATM from the database and verify them against the controller details
        ${db_gasper_data}=        Get the Gasper details from the database      ${atm_id}
        ${dict_as_string}=    Convert To String    ${db_gasper_data}
        ${dict_as_string}=          Set Variable        ${dict_as_string.replace('\n','').strip()}

        ${dict_as_string}=    Replace String    ${dict_as_string}    '    "
        #If the ATM exists in the Database then verification must be performed
        IF    '${dict_as_string}' != 'Failed'

             ${cleaned_dict}=    Evaluate    {k.replace(' ', ''): v for k, v in ${db_gasper_data}.items()}


            #verify the details of the current ATM using the database information
            ${db_atm_id}=    Get From Dictionary    ${cleaned_dict}    ID
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_id}    ${db_atm_id.strip()}
            Log Many     Comparison result for 'atm_id' : Controller Results = ${atm_id}, Database Results = ${db_atm_id.strip()}

            ${db_atm_serial_num}=    Get From Dictionary    ${cleaned_dict}    SERIAL_NUM
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_serial_num}    ${db_atm_serial_num}
            Log Many     Comparison result for 'atm_serial_num' : Controller Results = ${atm_serial_num}, Database Results = ${db_atm_serial_num}

            ${db_atm_address}=    Get From Dictionary    ${cleaned_dict}    ADDRESS
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_address}    ${db_atm_address}
            Log Many     Comparison result for 'atm_address' : Controller Results = ${atm_address}, Database Results = ${db_atm_address}

            ${db_atm_address2}=    Get From Dictionary    ${cleaned_dict}    ADDRESS2
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_address2}    ${db_atm_address2}
            Log Many     Comparison result for 'atm_address2' : Controller Results = ${atm_address2}, Database Results = ${db_atm_address2}

            ${db_atm_city}=    Get From Dictionary    ${cleaned_dict}    CITY
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_city}    ${db_atm_city}
            Log Many     Comparison result for 'atm_city' : Controller Results = ${atm_city}, Database Results = ${db_atm_city}

            ${db_atm_region}=    Get From Dictionary    ${cleaned_dict}    REGION
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_region}    ${db_atm_region.strip()}
            Log Many     Comparison result for 'atm_region' : Controller Results = ${atm_region}, Database Results = ${db_atm_region.strip()}

            ${db_atm_class}=    Get From Dictionary    ${cleaned_dict}    CLASS
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_class}    ${db_atm_class}
            Log Many     Comparison result for 'atm_class' : Controller Results = ${atm_class}, Database Results = ${db_atm_class}

            ${db_atm_data_line}=    Get From Dictionary    ${cleaned_dict}    DATA_LINE
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_data_line}    ${db_atm_data_line}
            Log Many     Comparison result for 'atm_data_line' : Controller Results = ${atm_data_line}, Database Results = ${db_atm_data_line}

            ${db_atm_branch}=    Get From Dictionary    ${cleaned_dict}    BRANCH
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_branch}    ${db_atm_branch}
            Log Many     Comparison result for 'atm_branch' : Controller Results = ${atm_branch}, Database Results = ${db_atm_branch}

            ${db_atm_institution}=    Get From Dictionary    ${cleaned_dict}    INSTITUTION
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_institution}    ${db_atm_institution}
            Log Many     Comparison result for 'atm_institution' : Controller Results = ${atm_institution}, Database Results = ${db_atm_institution}

            ${db_atm_object_type}=    Get From Dictionary    ${cleaned_dict}    OBJECT_TYPE
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_object_type}    ${db_atm_object_type}
            Log Many     Comparison result for 'atm_object_type' : Controller Results = ${atm_object_type}, Database Results = ${db_atm_object_type}

            ${db_atm_in_service}=    Get From Dictionary    ${cleaned_dict}    IN_SERVICE
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_in_service}    ${db_atm_in_service}
            Log Many     Comparison result for 'atm_in_service' : Controller Results = ${atm_in_service}, Database Results = ${db_atm_in_service}

            ${db_atm_out_of_service}=    Get From Dictionary    ${cleaned_dict}    OUT_OF_SERVICE
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_out_of_service}    ${db_atm_out_of_service}
            Log Many     Comparison result for 'atm_out_of_service' : Controller Results = ${atm_out_of_service}, Database Results = ${db_atm_out_of_service}

            ${db_atm_zip}=    Get From Dictionary    ${cleaned_dict}    ZIP
            # Modify the string if the first character is '0'
            IF  '${db_atm_zip}' != 'None'
                 ${db_atm_zip}=    Evaluate    '${db_atm_zip.strip()}'[1:] if '${db_atm_zip.strip()}'.startswith('0') else '${db_atm_zip.strip()}'
            END
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_zip}    ${db_atm_zip}
            Log Many     Comparison result for 'atm_zip' : Controller Results = ${atm_zip}, Database Results = ${db_atm_zip}

            ${db_atm_editor_note}=    Get From Dictionary    ${cleaned_dict}    ZIP
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_editor_note}    ${db_atm_editor_note}
            Log Many     Comparison result for 'atm_editor_note' : Controller Results = ${atm_editor_note}, Database Results = ${db_atm_editor_note}

            ${db_atm_ip_address}=    Get From Dictionary    ${cleaned_dict}    IP_ADDRESS
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${atm_ip_address}    ${db_atm_ip_address}
            Log Many     Comparison result for 'atm_ip_address' : Controller Results = ${atm_ip_address}, Database Results = ${db_atm_ip_address}

        END

    END


Get the Gasper details from the database
    [Arguments]     ${ATM_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${ATM_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MSSQL'
    #Check if Target Data Exists for the Campaign
    ${atm_gasper_details_query}   Set Variable     ${SQL_GET_GASPER_DETAILS}
    ${atm_gasper_details_query}=  Replace String      ${atm_gasper_details_query}       atm_id     '${ATM_ID}'
    ${atm_gasper_details}=      Execute SQL Query and Continue if no records found  ${db_type}  ${atm_gasper_details_query}    True

    RETURN   ${atm_gasper_details}






