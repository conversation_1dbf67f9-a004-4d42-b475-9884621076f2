*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../keywords/Common/Login.robot
Resource                                            ../../../../keywords/Common/HomePage.robot
Resource                                            ../../../../keywords/Common/Navigation.robot
Resource                                            ../../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../../keywords/Common/SetEnvironmentVariales.robot
Resource                                            ../../../../keywords/EOD_Control_Dashboard.robot
Resource                                            ../../../../keywords/Account_Information.robot

*** Variables ***

*** Keywords ***
Validates the Attestation wording on Retail Management
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application
    When the user lands on the Home page 
    And the user navigates to the EOD Control Dashboard Menu
    And the user accesses an account with Flagged Transactions
    And the user adds a comment to substantiate the flagged transactions
    And the user navigates to the Attestation menu on the account
    And the user completes the Add Comment and Upload Document actions in the Attestation menu
    #Then the Reviewed button is displayed
    #When the user clicks the Reviewed button to view the Reviewer Attestation
    #Then the user verifies the wording on the Reviewer Attestation for Retail Management
    #And the user verifies that the wording on the Reviewer Attestation matches the expected wording
    #And the user completes the Reviewer Attestation and verifies that the Attestation box updates correctly
    #Then the Controlled Attestation button is displayed
    #When the user clicks the Controlled button to view the Controller Attestation
    #Then the user verifies the wording on the Controller Attestation for Retail Management
    #And the user verifies that the wording on the Controller Attestation matches the expected wording for Retail Management
    #Then the user completes the Controller Attestation and verifies that the Attestation box updates correctly


| *Test Cases*                                                                                                    |      *DOCUMENTATION*    | *TEST_ENVIRONMENT*   |
| 	Verify the Attestation wording on Retail Management	| Validates the Attestation wording on Retail Management  |    Account Information  |    MTGLA_UAT         | 
