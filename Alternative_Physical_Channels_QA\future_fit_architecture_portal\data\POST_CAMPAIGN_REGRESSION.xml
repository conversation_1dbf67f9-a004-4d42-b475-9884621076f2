<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20241219 13:09:33.576">
   <suite name="Future Fit Controller Dry Run" id="s1" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Controllers\Dashboard\TC_11_GET_Dashboard_CONTROLLER.robot">
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data" id="s1-t1">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:09:36.453" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20241219 13:09:36.761" status="PASS" starttime="20241219 13:09:36.453"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:09:36.761" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20241219 13:10:06.892" status="PASS" starttime="20241219 13:09:36.761"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:06.892" level="INFO">And The service returns http status</msg>
            <status endtime="20241219 13:10:06.892" status="PASS" starttime="20241219 13:10:06.892"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:06.892" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20241219 13:10:06.900" status="PASS" starttime="20241219 13:10:06.892"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:06.900" level="INFO">Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database</msg>
            <status endtime="20241219 13:10:08.252" status="PASS" starttime="20241219 13:10:06.900"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data</tag>
         </tags>
         <status endtime="20241219 13:10:08.252" critical="yes" status="PASS" starttime="20241219 13:09:36.453"/>
      </test>
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data" id="s1-t2">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:08.254" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20241219 13:10:08.471" status="PASS" starttime="20241219 13:10:08.254"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:08.471" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20241219 13:10:39.139" status="PASS" starttime="20241219 13:10:08.471"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:39.139" level="INFO">And The service returns http status</msg>
            <status endtime="20241219 13:10:39.139" status="PASS" starttime="20241219 13:10:39.139"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:39.139" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20241219 13:10:39.147" status="PASS" starttime="20241219 13:10:39.139"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:39.147" level="INFO">Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database</msg>
            <status endtime="20241219 13:10:40.149" status="PASS" starttime="20241219 13:10:39.147"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data</tag>
         </tags>
         <status endtime="20241219 13:10:40.149" critical="yes" status="PASS" starttime="20241219 13:10:08.254"/>
      </test>
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data" id="s1-t3">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:40.149" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20241219 13:10:40.369" status="PASS" starttime="20241219 13:10:40.149"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:10:40.369" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20241219 13:11:08.910" status="PASS" starttime="20241219 13:10:40.369"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:08.910" level="INFO">And The service returns http status</msg>
            <status endtime="20241219 13:11:08.914" status="PASS" starttime="20241219 13:11:08.910"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:08.914" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20241219 13:11:08.914" status="PASS" starttime="20241219 13:11:08.914"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:08.914" level="INFO">Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database</msg>
            <status endtime="20241219 13:11:10.034" status="PASS" starttime="20241219 13:11:08.914"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data</tag>
         </tags>
         <status endtime="20241219 13:11:10.034" critical="yes" status="PASS" starttime="20241219 13:10:40.149"/>
      </test>
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data" id="s1-t4">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:10.046" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20241219 13:11:10.281" status="PASS" starttime="20241219 13:11:10.046"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:10.281" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20241219 13:11:40.068" status="PASS" starttime="20241219 13:11:10.281"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:40.068" level="INFO">And The service returns http status</msg>
            <status endtime="20241219 13:11:40.070" status="PASS" starttime="20241219 13:11:40.068"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:40.070" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20241219 13:11:40.083" status="PASS" starttime="20241219 13:11:40.070"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241219 13:11:40.083" level="INFO">Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database</msg>
            <status endtime="20241219 13:11:40.531" status="PASS" starttime="20241219 13:11:40.083"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data</tag>
         </tags>
         <status endtime="20241219 13:11:40.531" critical="yes" status="PASS" starttime="20241219 13:11:10.034"/>
      </test>
      <status endtime="20241219 13:11:40.533" status="PASS" starttime="20241219 13:09:33.576"/>
   </suite>
   <statistics>
      <total>
         <stat pass="4" fail="0">Critical Tests</stat>
         <stat pass="4" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data</stat>
      </tag>
      <suite>
         <stat name="Future Fit Controller Dry Run" pass="4" fail="0" id="s1">Future Fit Controller Dry Run</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
