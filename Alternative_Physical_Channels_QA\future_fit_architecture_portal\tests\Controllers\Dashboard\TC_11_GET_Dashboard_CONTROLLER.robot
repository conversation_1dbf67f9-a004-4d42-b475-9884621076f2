*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        FFT_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${IS_HEADLESS_BROWSER}      No
${BROWSER}                  chrome



*** Keywords ***
GET Dashboard
    [Arguments]        ${DOCUMENTATION}     ${FIELD_TO_VERIFY}     ${DATA_FILE}  ${BASE_URL}     ${SERVICE_PATH}   ${SERVICE_PATH_ID}   ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}      &{EXPECTED_FIELDS_VALUES}
    Set Test Documentation  ${DOCUMENTATION}


    Given The user creates a rest session                               ${BASE_URL}
    When The user makes Get Rest Call                                   ${SERVICE_PATH}     ${SERVICE_PATH_ID}     ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                                 ${EXPECTED_STATUS_CODE}  ${JSON_RESPONSE_REASON}
    And The user reads the field value returned by the controller       ${FIELD_TO_VERIFY}
    Then The field value(s) returned by the Dashboard Controller must correspond to the APC Database

| *** Test Cases ***                                                                                                                                                         | *DOCUMENTATION*                                                                   |  *FIELD_TO_VERIFY*          | *DATA_FILE* | *BASE_URL*                    | *SERVICE_PATH* | *SERVICE_PATH_ID*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON*          |
| FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data             | GET Dashboard               | Verify the cotroller data for 'totalCampaigns' against the database               |  totalCampaigns             |             | APC_API_UAT_BASE_URL          | Dashboard      |                                      | 200                              | OK                              |
| FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data        | GET Dashboard               | Verify the cotroller data for 'latestScheduleCount' against the database          |  latestScheduleCount        |             | APC_API_UAT_BASE_URL          | Dashboard      |                                      | 200                              | OK                              |
| FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data | GET Dashboard               | Verify the cotroller data for 'failedUploadSchedulesCount' against the database   |  failedUploadSchedulesCount |             | APC_API_UAT_BASE_URL          | Dashboard      |                                      | 200                              | OK                              |
| FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data             | GET Dashboard               | Verify the cotroller data for 'currentVersion' against the database               |  currentVersion             |             | APC_API_UAT_BASE_URL          | Dashboard      |                                      | 200                              | OK                              |
#| FFT - Controllers - Get all Dashboard Details and verify that the 'atmScheduleResultVersions' field data is the same a database data  | GET Dashboard               | Verify the cotroller data for 'atmScheduleResultVersions' against the database    |  atmScheduleResultVersions  |             | APC_API_UAT_BASE_URL          | Dashboard      |                                      | 200                              | OK                              |
