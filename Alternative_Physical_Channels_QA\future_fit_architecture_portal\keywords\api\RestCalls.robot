*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             RequestsLibrary
Library                                             JSONLibrary
Library                                            ../../keywords/api/Resources/CreateCampaignAPI.py
Library                                             String
Library                                            ../../../common_utilities/APCAccessToken.py
Library                                             Collections
Library                                             BuiltIn
Library                                             ../../../common_utilities/CommonUtils.py
Library                                             ../../utility/Common_Functions.py

#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/atm_marketing/DashboardController_DB_Verifications.robot
Resource                                            ../../keywords/atm_marketing/GETATMMarketingCampaign_DB_Verifications.robot
Resource                                            ../../keywords/atm_marketing/GETGasperDetails_DB_Verifications.robot
Resource                                            ../../keywords/atm_marketing/GETCampaignLookup_DB_Verifications.robot
Resource                                            ../../keywords/atm_marketing/POSTATMMarketing_DB_Verifications.robot
Resource                                            ../../keywords/atm_marketing/POSTATMMarketing_Results_DB_Verifications.robot
Resource                                            ../../keywords/common/GenericMethods.robot
Resource                                            ../../keywords/atm_marketing/PUTATMMarketingCampaign_DB_Verifications.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***
${SESSION}
${my_session}
${REST_HEADERS}
${INVALID_BEARER_TOKEN}             Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyIsImtpZCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FI26FGo9WE2UvoEncNGM1-63BboD54HmUGkgw_UF2TVGc8-C_0Wziv0KXECaaU-ExQNli2I5ZD4qgamPXa_daS9DVUmkurKQuRCSv_FV811I4ZkDCCYxVyl4G_U2Cbp82Wx9X7Xojur6lOkmu8Mo7F2YZ3gENVyF1httwzYPHFJ3qFJ6jQD7UtNDrG3D5IYZQbA06dRtu0Oo6AQq54EYMplf1SvpX0_nA6trvVsF6V9U09lbtYp6vjAYzebkWtBrNkNhojhVvZeKsgPruGCoPjPLkswCwb4dlKTC2mBeCapiHc3boy7RH0-tvLbnExqqGcW377ah9873_lUqRIBc4g
${BEARER_TOKEN}
${indent}                           ${SPACE*4}
${FIELD_FOUND}                      ${False}
${DASHBOARD_TOTAL_CAMPAIGNS_SQL}    SELECT count(*) as total_campaigns FROM ATM_Marketing.Campaign where version = 'scheduleVersion' and isActive = true
${SCHEDULE_VERSION}                 v001Q32024

*** Keywords ***
The user prepares a json payload
    [Arguments]  ${SUITE_NAME}     ${DATA_FILE}    &{KW_ARGS}

    Return From Keyword If	  '${DATA_FILE}'==''

    ${root}=            Set Variable  future_fit_architecture_portal/data/
    ${extension}=       Set Variable  .json
    ${path}=            Set Variable  ${root}${DATA_FILE}${extension}
    Populate Json File With     ${path}     &{KW_ARGS}

The user creates JSON request for Edit Campaign
    [Arguments]     ${CAMPAIGN_ID}      ${CAMPAIGN_NAME}        ${CAMPAIGN_RECEIVER_DEVICE_TYPE}    ${CAMPAIGN_START_DATE}      ${CAMPAIGN_END_DATE}

    ${campaign_details}=        Get Campaign History details for the Campaign to be edited       ${CAMPAIGN_ID}
    ${today_date}=     Get Current Date In ISO Format

    IF    '${CAMPAIGN_START_DATE}' != '${EMPTY}'
            ${integer_validation}=       Validate Integer    ${CAMPAIGN_START_DATE}
            Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for CAMPAIGN_START_DATE, which is '${CAMPAIGN_START_DATE}', is not an integer. The CAMPAIGN_START_DATE must be specified as a number of days from today; e.g if the campaign must start tomorrow then you must specify the data for CAMPAIGN_START_DATE as 1

            ${start_date_string}=  Add days to String date      ${today_date}       ${CAMPAIGN_START_DATE}
            ${start_date_string_array}=     Split String    ${start_date_string}    T
            ${start_date_string}=   Catenate    ${start_date_string_array}[0]T00:00:00.000Z
    END

    IF    '${CAMPAIGN_END_DATE}' != '${EMPTY}'
        ${integer_validation}=       Validate Integer    ${CAMPAIGN_END_DATE}
        Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for CAMPAIGN_END_DATE, which is '${CAMPAIGN_END_DATE}', is not an integer. The CAMPAIGN_END_DATE must be specified as a number of days from today; e.g if the campaign must expire tomorrow then you must specify the data for CAMPAIGN_START_DATE as 1

        ${end_date_string}=  Add days to String date      ${today_date}       ${CAMPAIGN_END_DATE}
        ${end_date_string_array}=     Split String    ${end_date_string}    T
        ${end_date_string}=   Catenate    ${end_date_string_array}[0]T00:00:00.000Z
    END


    #Create the imageList JSON Object
    ${type}=    Evaluate    type($campaign_details)
    ${images}=    Create List
    ${image_counter}=       Set Variable    0
    IF  "${type}" == "<class 'dict'>"
        Log     A campaign has 1 image
        ${length}=      Set Variable    1
        ${images}=    Set Up the image List Object         ${images}   ${CAMPAIGN_ID}       ${image_counter}     ${length}
        ${campaign_fields}=     Set Variable    ${campaign_details}
    ELSE IF  "${type}" == "<class 'list'>"
        ${length} =    Get number of records returned by query    ${campaign_details}
        Log     A campaign has ${length} images
        FOR    ${item}    IN    @{campaign_details}
            ${campaign_fields}=     Set Variable    ${item}
            ${images}=    Set Up the image List Object         ${images}   ${CAMPAIGN_ID}       ${image_counter}     ${length}
            ${image_counter} =    Evaluate    ${image_counter} + 1  #increase the image counter
        END
    END

    ${campaign_isApproved}=       Get From Dictionary    ${campaign_fields}    isApproved
    ${campaign_isApproved_boolean}=    Set Variable If    ${campaign_isApproved} == 1    True    False

    Run Keyword If    ${campaign_isApproved_boolean}       Fail    The campaign: '${CAMPAIGN_ID}' is approved hence it cannot be edited.

    #Create the lastUpdate JSON Object
    ${last_update_id}=      Set Variable    0
    ${last_update_campaignId}=      Set Variable    0
    ${last_update_updatedDate}=      Get From Dictionary    ${campaign_fields}    campaignHistoryUpdateDate
    ${last_update_approvalId}=      Set Variable    0
    ${last_update}=     Create the lastUpdate object         ${last_update_id}    ${last_update_campaignId}     ${last_update_approvalId}

    #Log Many    ${last_update}

    #Create the targetData JSON Object
    ${targetData_isTargetRegion}=      Set Variable     ${False}
    ${targetData_targetRegionOrAtm}=      Set Variable     ${EMPTY}
    ${targetData}=      Create the targetData object    ${targetData_isTargetRegion}    ${targetData_targetRegionOrAtm}

    #Create the rest of Campaign Edit JSON Objects

    IF    '${CAMPAIGN_NAME}' == '${EMPTY}'      #If the campaign name parameter is empty then use the value from the database
        ${campaignName}=       Get From Dictionary    ${campaign_fields}    campaignName
    ELSE
        ${campaignName}=       Set Variable    ${CAMPAIGN_NAME}
    END

    IF    '${CAMPAIGN_START_DATE}' == '${EMPTY}'
        ${start_date_string}=       Get From Dictionary    ${campaign_fields}    campaignStartDate
        ${start_date_string_array}=     Split String    ${start_date_string}    ${SPACE}
        ${start_date_string}=   Catenate    ${start_date_string_array}[0]T00:00:00.000Z
    END

    IF    '${CAMPAIGN_END_DATE}' == '${EMPTY}'
        ${end_date_string}=       Get From Dictionary    ${campaign_fields}    campaignEndDate
        ${end_date_string_array}=     Split String    ${end_date_string}    ${SPACE}
        ${end_date_string}=   Catenate    ${end_date_string_array}[0]T00:00:00.000Z
    END

    #Log     Campaign Name: '${campaignName}'
    IF    '${CAMPAIGN_RECEIVER_DEVICE_TYPE}' == '${EMPTY}'      #If the campaign marketingChannelID parameter is empty then use the value from the database
        ${campaign_marketingChannelID}=       Get From Dictionary    ${campaign_fields}    marketingChannelID
    ELSE
        ${_marketingChannelID}=       Set Variable    ${CAMPAIGN_RECEIVER_DEVICE_TYPE}


        ${condition1}=   Evaluate    '${_marketingChannelID}' == 'ATM'
        ${condition2}=   Evaluate    '${_marketingChannelID}' == 'SSK'
        ${condition3}=   Evaluate    '${_marketingChannelID}' == 'BCD'

        IF    ${condition1}
            ${campaign_marketingChannelID}=     Set Variable    1
        ELSE
             IF    ${condition2}
                ${campaign_marketingChannelID}=     Set Variable    2
             ELSE
                ${campaign_marketingChannelID}=     Set Variable    3
             END
        END
    END

    #Log     Campaign marketingChannelID: '${campaign_marketingChannelID}'

    ${campaign_id}=       Get From Dictionary    ${campaign_fields}    id
    ${campaign_createdBy}=       Get From Dictionary    ${campaign_fields}    campaignBy
    ${campaign_campaignId}=       Get From Dictionary    ${campaign_fields}    campaignId
    ${campaign_screenId}=       Get From Dictionary    ${campaign_fields}    marketingScreenID
    ${campaign_updatedBy}=       Set Variable
    ${campaign_isActive}=       Get From Dictionary    ${campaign_fields}    isActive
    ${campaign_isActive_boolean}=    Set Variable If    ${campaign_isActive} == 1    True    False
    ${campaign_version}=       Set Variable     1.0
    ${campaign_isTargetted}=       Get From Dictionary    ${campaign_fields}    isTargetted

    ${campaign_isTargetted_boolean}=    Set Variable If    ${campaign_isTargetted} == 1    True    False


    Run Keyword If    ${campaign_isTargetted_boolean}       Fail    The campaign: '${CAMPAIGN_ID}' is targeted hence it cannot be edited.

    #Build the Complete JSON Request of Campaign Edit
    ${json_payload}=        Generate Campaign JSON Request      ${images}       ${last_update}       ${targetData}       ${campaignName}       ${CAMPAIGN_CREATED_BY}       ${start_date_string}       ${end_date_string}       ${campaign_marketingChannelID}       ${campaign_id}       ${campaign_campaignId}       ${campaign_screenId}       ${campaign_updatedBy}       ${campaign_isActive_boolean}       ${campaign_isApproved_boolean}       ${campaign_version}       ${campaign_isTargetted_boolean}

    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal/data/Campaign_EDIT.json
    Write JSON data To File    ${JSON_FILE}    ${json_payload}

    Set Environment Variable  CAMP_LNGS     ${JSON_FILE}


Set Up the image List Object
    [Arguments]     ${IMAGE_LIST_OBJECT}   ${CAMPAIGN_ID}       ${IMAGE_COUNTER}        ${TOTAL_NUMBER_OF_IMAGES}

    ${integer_validation}=       Validate Integer    ${IMAGE_COUNTER}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for IMAGE_COUNTER, which is '${IMAGE_COUNTER}', is not an integer.

     #Loop through the images List Dictionary to read the images fields

    ${IMAGE_NAMES}=   Get Environment Variable    CAMP_IMAGES
    ${image_list}=    Split String    ${IMAGE_NAMES}    ,
    ${number_of_images_to_upload} =    Get Length    ${image_list}
    ${LANGUAGES}=   Get Environment Variable    CAMP_LNGS
    ${language_list}=    Split String    ${LANGUAGES}    ,
    ${number_of_image_laguages_provided} =    Get Length    ${language_list}

    #Verify that the number of image names provided by the user is the same as the number of images found on the current campaign
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${number_of_images_to_upload}'     '${TOTAL_NUMBER_OF_IMAGES}'
     Run Keyword If    '${result}' == 'False'    Fail    The campaign with the ID: ${CAMPAIGN_ID} has ${TOTAL_NUMBER_OF_IMAGES} images. Please provide the same number of images before the campaign could be edited!

    #Verify that the number of languages provided by the user is the same as the number of languages found on the current campaign
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${number_of_image_laguages_provided}'     '${TOTAL_NUMBER_OF_IMAGES}'
     Run Keyword If    '${result}' == 'False'    Fail    The campaign with the ID: ${CAMPAIGN_ID} has ${TOTAL_NUMBER_OF_IMAGES} languages. Please provide the same number of languages before the campaign could be edited!

    #Verify that the user has provided the campaign start date and end date variables
#    ${result}=  Run Keyword And Return Status    Should Not be Empty     '${CAMPAIGN_START_DATE}'
#    Run Keyword If    '${result}' == 'False'    Fail    Please provide the campaign start date before the campaign could be edited.
#    ${result}=  Run Keyword And Return Status    Should Not be Empty     '${CAMPAIGN_END_DATE}'
#    Run Keyword If    '${result}' == 'False'    Fail    Please provide the campaign end date before the campaign could be edited.


    ${image_id}=       Set Variable    ${image_counter}

     ${user_provided_imageName} =    Set Variable    ${image_list}[${image_counter}]
     ${user_image_array}=       Split String    ${user_provided_imageName}        /
     ${actual_image_name}=      Set Variable    ${user_image_array[1]}
     #Covert the image to base64 string

    ${base64_image}=       Create Base64 Image String    ${user_provided_imageName}
    ${image_string_array}=      Split String    ${base64_image}        ,
    ${image_string}=    Set Variable    ${image_string_array[1]}
    ${result}=    Is Base64 Encoded    ${image_string}

    Run Keyword If    '${result}' == 'False'   Fail    The image named '${user_provided_imageName}', could not be converted to Base64 string. Please ensure that the image path and extention are valid

    ${user_provided_languageName} =    Set Variable    ${language_list}[${image_counter}]
    ${db_language_details}=        Get the language details from the database      ${user_provided_languageName}
    ${result}=  Run Keyword And Return Status    Should Not Be Equal    "${db_language_details}"    "'Failed'"
    Run Keyword If    '${result}' == 'False'    Fail    The provided value for MARKETING_LANGUAGE which is '${user_provided_languageName}', is not valid.

    ${language_id}=       Get From Dictionary    ${db_language_details}    id


    ${duaration}=       Set Variable    0
    ${priority}=       Set Variable    0

    #Append an image object to the JSON Object list
    ${images}=    Create the imageList Object       ${IMAGE_LIST_OBJECT}       ${image_id}     ${base64_image}     ${language_id}      ${user_provided_languageName}       ${actual_image_name}        ${duaration}        ${priority}

    RETURN      ${images}

The user creates a rest session
    [Arguments]         ${BASE_URL}

    ${BASE_URL}=   Get Environment Variable    BASE_URL
    ${base_url}=        Read Config Property      ${BASE_URL}
    Create Session      ffa_session      ${baseUrl}    verify=false
    Set Environment Variable      FFA_REST_SESSION        ffa_session
    Log     'Session Created!'

The user makes Post Rest Call
    [Arguments]         ${REST_PATH}    ${DATA_FILE}    ${EXPECTED_STATUS_CODE}

    ${root}=            Set Variable  future_fit_architecture_portal/data/
    ${extension}=       Set Variable  .json
    ${path}=            Set Variable  ${root}${DATA_FILE}${extension}

    ${payload}=  Load JSON From File	${path}

    Log     ${payload}
    ${end_point}=       Get service path  ${REST_PATH}

    IF    '${EXPECTED_STATUS_CODE}' != '200'
        ${REST_HEADERS}=     Get Rest API headers    ${INVALID_BEARER_TOKEN}
    ELSE
        ${BEARER_TOKEN}=     Get Bearer Token
        ${REST_HEADERS}=     Get Rest API headers    ${BEARER_TOKEN}
    END

    ${response}=    Set Variable    ${EMPTY}

    ${api_session}=   Get Environment Variable    FFA_REST_SESSION

    ${response}=       POST On Session         ${api_session}     ${end_point}      headers=${REST_HEADERS}     json=${payload}        expected_status=anything

    #Log To Console    ${response}
    #Log To Console    ${response.text}

    Log Many    ${response}
    Log Many    ${response.text}

	Log		${response.content}
    Set Global Variable    ${response.content}      ${response.content}
    Set Global Variable    ${response.text}      ${response.text}

	Set Environment Variable  JSON_RESPONSE_CODE     ${response.status_code}
    Set Environment Variable  JSON_RESPONSE_REASON     ${response.reason}
     #Set Environment Variable  JSON_RESPONSE_MESSAGE     ${response.content}


The user makes Get Rest Call
    [Arguments]         ${REST_PATH}    ${REST_PATH_ID}    ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    ${end_point}=       Get service path  ${REST_PATH}      ${REST_PATH_ID}

    IF    '${EXPECTED_STATUS_CODE}' != '200'
        ${REST_HEADERS}=     Get Rest API headers    ${INVALID_BEARER_TOKEN}
    ELSE
        ${BEARER_TOKEN}=     Get Bearer Token
        ${REST_HEADERS}=     Get Rest API headers    ${BEARER_TOKEN}
    END

    ${response}=    Set Variable    ${EMPTY}
    ${api_session}=   Get Environment Variable    FFA_REST_SESSION

    ${response}=     GET On Session     ${api_session}    ${end_point}  headers=${REST_HEADERS}     expected_status=anything

    Set Global Variable    ${response.content}      ${response.content}

	Log		${response.content}
	Set Environment Variable  JSON_RESPONSE_CODE     ${response.status_code}
	Set Environment Variable  JSON_RESPONSE_REASON     ${response.reason}

The user makes Put Rest Call
    [Arguments]         ${REST_PATH}    ${REST_PATH_ID}    ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    ${end_point}=       Get service path  ${REST_PATH}      ${REST_PATH_ID}



    IF    '${EXPECTED_STATUS_CODE}' != '200'
        ${REST_HEADERS}=     Get Rest API headers    ${INVALID_BEARER_TOKEN}
    ELSE
        ${BEARER_TOKEN}=     Get Bearer Token
        ${REST_HEADERS}=     Get Rest API headers    ${BEARER_TOKEN}
    END
    ${response}=    Set Variable    ${EMPTY}
    ${api_session}=   Get Environment Variable    FFA_REST_SESSION

    ${response}=     PUT On Session     ${api_session}    ${end_point}  headers=${REST_HEADERS}     expected_status=anything

    Set Global Variable    ${response.content}      ${response.content}
    Set Global Variable    ${response.text}      ${response.text}
	Log		${response.content}
	Set Environment Variable  JSON_RESPONSE_CODE     ${response.status_code}
	Set Environment Variable  JSON_RESPONSE_REASON     ${response.reason}


The user makes Put Rest Call with JSON payload
    [Arguments]         ${REST_PATH}    ${REST_PATH_ID}    ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    ${end_point}=       Get service path  ${REST_PATH}      ${REST_PATH_ID}



    IF    '${EXPECTED_STATUS_CODE}' != '200'
        ${REST_HEADERS}=     Get Rest API headers    ${INVALID_BEARER_TOKEN}
    ELSE
        ${BEARER_TOKEN}=     Get Bearer Token
        ${REST_HEADERS}=     Get Rest API headers    ${BEARER_TOKEN}
    END

    ${payload}=  Load JSON From File	${DATA_FILE}

    ${response}=    Set Variable    ${EMPTY}
    ${api_session}=   Get Environment Variable    FFA_REST_SESSION

    ${response}=     PUT On Session     ${api_session}    ${end_point}  headers=${REST_HEADERS}     json=${payload}     expected_status=anything

    Set Global Variable    ${response.content}      ${response.content}
	Set Global Variable    ${response.text}      ${response.text}

	Log		${response.content}
	Set Environment Variable  JSON_RESPONSE_CODE     ${response.status_code}
	Set Environment Variable  JSON_RESPONSE_REASON     ${response.reason}

The service returns http status
    [Arguments]    ${STATUS_CODE}       ${JSON_RESPONSE_REASON}
    ${returned_status_code}=   Get Environment Variable    JSON_RESPONSE_CODE
    Log     Response Status Code : ${returned_status_code}
    Status Should Be  ${STATUS_CODE}
    Return From Keyword If	  '${JSON_RESPONSE_REASON}'==''
    ${returned_status_reason}=   Get Environment Variable    JSON_RESPONSE_REASON
    Should Be Equal    ${returned_status_reason}    ${JSON_RESPONSE_REASON}



The rest service must return the expected message
    [Arguments]    ${EXPECTED_MESSAGE}

    Log             Response Message : ${response.text}
    Log             ${EXPECTED_MESSAGE}
    Should Contain  As Strings ${response.text}  ${EXPECTED_MESSAGE}

The rest service must return the response which contains
    [Arguments]    &{EXPECTED_FIELDS_VALUES}

    #Log    ${EXPECTED_FIELDS_VALUES}           # Log as list
    #Log Many    &{EXPECTED_FIELDS_VALUES}      # Log each item separately

   FOR    ${key}    ${value}    IN   &{EXPECTED_FIELDS_VALUES}
       ${key_value_found}=     Value Exists On Json Response    ${key}      ${value}      ${response.content}
       Should Be True    ${key_value_found}
   END



The user reads the field value returned by the controller
    [Arguments]  ${field_to_read}

   ${source data}=    Evaluate     json.loads("""${response.content}""")    json

   Set Environment Variable    FIELD_FOUND        ${False}
   ${field_value}=   Get field value from JSON    ${source data}        ${field_to_read}

   #Save the field value to the environment variable
   Set Environment Variable    FIELD_TO_VERIFY        ${field_to_read}
   Set Environment Variable    ${field_to_read}        ${field_value}


The field value(s) returned by the Dashboard Controller must correspond to the APC Database
    Verify dashboard controller field on the database

The field value(s) returned by the GET ATMMarketingCampaign controller must correspond to the APC Database
    Verify GET ATMMarketingCampaign controller fields on the database       ${response.content}


The field value(s) returned by the GET GasperDetails controller must correspond to the APC Database
    Verify GET GasperDetails controller fields on the database       ${response.content}


The field value(s) returned by the GET CampaignLookup controller must correspond to the APC Database
    Verify GET CampaignLookup controller fields on the database       ${response.content}

The field value(s) returned by the POST ATMMarketing controller must correspond to the APC Database
    Verify GET ATMMarketing controller fields on the database       ${response.content}


The field value(s) returned by the POST ATMMarketing Results controller must correspond to the APC Database
    Verify GET ATMMarketing Results controller fields on the database       ${response.content}


Get current schedule version
    ${version}=     Get the current schedule version from the database
    RETURN      ${version}

Modify Kwargs Value
    [Arguments]     ${key}    ${value}      &{kwargs}
    Set To Dictionary    ${kwargs}    ${key}=${value}
    RETURN      &{kwargs}

Get Current Timestamp With Milliseconds
    ${now}=    Get Timestamp With Milliseconds
    RETURN   ${now}


