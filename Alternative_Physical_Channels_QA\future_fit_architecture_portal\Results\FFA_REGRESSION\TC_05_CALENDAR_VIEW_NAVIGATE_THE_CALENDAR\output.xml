<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="20241023 10:14:00.408" rpa="false" schemaversion="4">
<suite id="s1" name="Future Fit Portal">
<suite id="s1-s1" name="TC 05 CALENDAR VIEW NAVIGATE THE CALENDAR" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_05_CALENDAR_VIEW_NAVIGATE_THE_CALENDAR.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.386" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241023 10:14:03.386" endtime="20241023 10:14:03.386"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.387" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20241023 10:14:03.387" endtime="20241023 10:14:03.387"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.387" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241023 10:14:03.387" endtime="20241023 10:14:03.387"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.387" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241023 10:14:03.387" endtime="20241023 10:14:03.387"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.388" level="FAIL">Variable '${BASE_URL}' not found.</msg>
<status status="FAIL" starttime="20241023 10:14:03.387" endtime="20241023 10:14:03.389"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>URL</arg>
<arg>${URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" starttime="20241023 10:14:03.389" endtime="20241023 10:14:03.389"/>
</kw>
<status status="FAIL" starttime="20241023 10:14:03.386" endtime="20241023 10:14:03.389"/>
</kw>
<test id="s1-s1-t1" name="Business User - Calendar View- Preview" line="33">
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="20241023 10:14:03.389" endtime="20241023 10:14:03.389">Parent suite setup failed:
Variable '${BASE_URL}' not found.</status>
</test>
<doc>Testing Future Fit APC Portal Calendar Navigation</doc>
<status status="FAIL" starttime="20241023 10:14:00.834" endtime="20241023 10:14:03.390">Suite setup failed:
Variable '${BASE_URL}' not found.</status>
</suite>
<suite id="s1-s2" name="TC 05 CALENDAR VIEW PREVIEW ACTIVE CAMPAIGN" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_05_CALENDAR_VIEW_PREVIEW_ACTIVE_CAMPAIGN.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.417" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241023 10:14:03.417" endtime="20241023 10:14:03.417"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.418" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20241023 10:14:03.417" endtime="20241023 10:14:03.418"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.418" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241023 10:14:03.418" endtime="20241023 10:14:03.418"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.418" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241023 10:14:03.418" endtime="20241023 10:14:03.418"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.419" level="FAIL">Variable '${BASE_URL}' not found.</msg>
<status status="FAIL" starttime="20241023 10:14:03.418" endtime="20241023 10:14:03.419"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>URL</arg>
<arg>${URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" starttime="20241023 10:14:03.419" endtime="20241023 10:14:03.419"/>
</kw>
<status status="FAIL" starttime="20241023 10:14:03.417" endtime="20241023 10:14:03.419"/>
</kw>
<test id="s1-s2-t1" name="Business User - Calendar View- Preview" line="35">
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="20241023 10:14:03.419" endtime="20241023 10:14:03.420">Parent suite setup failed:
Variable '${BASE_URL}' not found.</status>
</test>
<doc>Testing Future Fit APC Portal Calendar Preview</doc>
<status status="FAIL" starttime="20241023 10:14:03.392" endtime="20241023 10:14:03.420">Suite setup failed:
Variable '${BASE_URL}' not found.</status>
</suite>
<suite id="s1-s3" name="TC 05 CALENDAR VIEW PREVIEW APPROVED CAMPAIGN" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_05_CALENDAR_VIEW_PREVIEW_APPROVED_CAMPAIGN.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.452" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241023 10:14:03.452" endtime="20241023 10:14:03.452"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.452" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20241023 10:14:03.452" endtime="20241023 10:14:03.452"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.452" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241023 10:14:03.452" endtime="20241023 10:14:03.452"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.453" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241023 10:14:03.452" endtime="20241023 10:14:03.453"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.453" level="FAIL">Variable '${BASE_URL}' not found.</msg>
<status status="FAIL" starttime="20241023 10:14:03.453" endtime="20241023 10:14:03.453"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>URL</arg>
<arg>${URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" starttime="20241023 10:14:03.454" endtime="20241023 10:14:03.454"/>
</kw>
<status status="FAIL" starttime="20241023 10:14:03.452" endtime="20241023 10:14:03.454"/>
</kw>
<test id="s1-s3-t1" name="Business User - Calendar View- Preview" line="37">
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="20241023 10:14:03.454" endtime="20241023 10:14:03.454">Parent suite setup failed:
Variable '${BASE_URL}' not found.</status>
</test>
<doc>Testing Future Fit APC Portal Calendar Preview Approved Campaign</doc>
<status status="FAIL" starttime="20241023 10:14:03.423" endtime="20241023 10:14:03.455">Suite setup failed:
Variable '${BASE_URL}' not found.</status>
</suite>
<suite id="s1-s4" name="TC 05 CALENDAR VIEW PREVIEW UN-APPROVED CAMPAIGN" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_05_CALENDAR_VIEW_PREVIEW_UN-APPROVED_CAMPAIGN.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.485" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241023 10:14:03.484" endtime="20241023 10:14:03.485"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.485" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20241023 10:14:03.485" endtime="20241023 10:14:03.485"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.485" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241023 10:14:03.485" endtime="20241023 10:14:03.485"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.485" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241023 10:14:03.485" endtime="20241023 10:14:03.485"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.486" level="FAIL">Variable '${BASE_URL}' not found.</msg>
<status status="FAIL" starttime="20241023 10:14:03.485" endtime="20241023 10:14:03.486"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>URL</arg>
<arg>${URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" starttime="20241023 10:14:03.486" endtime="20241023 10:14:03.486"/>
</kw>
<status status="FAIL" starttime="20241023 10:14:03.484" endtime="20241023 10:14:03.486"/>
</kw>
<test id="s1-s4-t1" name="Business User - Calendar View- Preview" line="37">
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="20241023 10:14:03.486" endtime="20241023 10:14:03.486">Parent suite setup failed:
Variable '${BASE_URL}' not found.</status>
</test>
<doc>Testing Future Fit APC Portal Calendar Preview Approved Campaign</doc>
<status status="FAIL" starttime="20241023 10:14:03.457" endtime="20241023 10:14:03.487">Suite setup failed:
Variable '${BASE_URL}' not found.</status>
</suite>
<suite id="s1-s5" name="TC 05 CALENDAR VIEW VERIFY THE UN APPROVED CAMPAIGNS DISPLAY COLOR" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_05_CALENDAR_VIEW_VERIFY_THE_UN_APPROVED_CAMPAIGNS_DISPLAY_COLOR.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.519" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241023 10:14:03.519" endtime="20241023 10:14:03.519"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.519" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20241023 10:14:03.519" endtime="20241023 10:14:03.519"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.519" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241023 10:14:03.519" endtime="20241023 10:14:03.519"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.519" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241023 10:14:03.519" endtime="20241023 10:14:03.519"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241023 10:14:03.520" level="FAIL">Variable '${BASE_URL}' not found.</msg>
<status status="FAIL" starttime="20241023 10:14:03.519" endtime="20241023 10:14:03.520"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>URL</arg>
<arg>${URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" starttime="20241023 10:14:03.520" endtime="20241023 10:14:03.520"/>
</kw>
<status status="FAIL" starttime="20241023 10:14:03.518" endtime="20241023 10:14:03.520"/>
</kw>
<test id="s1-s5-t1" name="Business User - Calendar View- Preview" line="34">
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="20241023 10:14:03.520" endtime="20241023 10:14:03.521">Parent suite setup failed:
Variable '${BASE_URL}' not found.</status>
</test>
<doc>Testing Future Fit APC Portal Calendar Approved Campaigns Display Color</doc>
<status status="FAIL" starttime="20241023 10:14:03.489" endtime="20241023 10:14:03.522">Suite setup failed:
Variable '${BASE_URL}' not found.</status>
</suite>
<status status="FAIL" starttime="20241023 10:14:00.808" endtime="20241023 10:14:03.523"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="5" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="5" skip="0">FFA_HEALTHCHECK</stat>
<stat pass="0" fail="5" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="0" fail="5" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
<stat pass="0" fail="1" skip="0" id="s1-s1" name="TC 05 CALENDAR VIEW NAVIGATE THE CALENDAR">Future Fit Portal.TC 05 CALENDAR VIEW NAVIGATE THE CALENDAR</stat>
<stat pass="0" fail="1" skip="0" id="s1-s2" name="TC 05 CALENDAR VIEW PREVIEW ACTIVE CAMPAIGN">Future Fit Portal.TC 05 CALENDAR VIEW PREVIEW ACTIVE CAMPAIGN</stat>
<stat pass="0" fail="1" skip="0" id="s1-s3" name="TC 05 CALENDAR VIEW PREVIEW APPROVED CAMPAIGN">Future Fit Portal.TC 05 CALENDAR VIEW PREVIEW APPROVED CAMPAIGN</stat>
<stat pass="0" fail="1" skip="0" id="s1-s4" name="TC 05 CALENDAR VIEW PREVIEW UN-APPROVED CAMPAIGN">Future Fit Portal.TC 05 CALENDAR VIEW PREVIEW UN-APPROVED CAMPAIGN</stat>
<stat pass="0" fail="1" skip="0" id="s1-s5" name="TC 05 CALENDAR VIEW VERIFY THE UN APPROVED CAMPAIGNS DISPLAY COLOR">Future Fit Portal.TC 05 CALENDAR VIEW VERIFY THE UN APPROVED CAMPAIGNS DISPLAY COLOR</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
