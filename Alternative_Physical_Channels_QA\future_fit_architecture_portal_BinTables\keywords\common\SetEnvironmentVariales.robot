*** Settings ***
#Author Name               : <PERSON>viwe
#Email Address             : <EMAIL>

Documentation  Get environment variables passed as argument from commandline

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             RequestsLibrary
Library                                             JSONLibrary
Library                                             Collections

#***********************************PROJECT RESOURCES***************************************
Library                                             ../../../common_utilities/PostExecutionUpdateV2.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

*** Variables ***
${IS_HEADLESS_BROWSER}
${BASE_URL}
${BROWSER}
${UPLOAD_TEST_STEPS}
${ROBOT_FILE_PATH}
${SUITE_DIRECTORY}
${MS_USERNAME}
${MS_PASSWORD}
${TEST_CASE_ID}
${QMETRY_API_URL}    https://testmanagementeu.qmetry.com
${API_KEY}           aG1dKCwagyGPWr2Dcl86ISWMM9w8ByVQvYJYIrll

*** Keywords ***
Set up environment variables

     Run Keyword If    '${UPLOAD_TEST_STEPS}' != ''    Set Environment Variable    UPLOAD_TEST_STEPS    ${UPLOAD_TEST_STEPS}
     ...  ELSE  Log    Environment variable ${UPLOAD_TEST_STEPS} does not exist.

     Run Keyword If    '${ROBOT_FILE_PATH}' != ''    Set Environment Variable    ROBOT_FILE_PATH    ${ROBOT_FILE_PATH}
     ...  ELSE  Log    Environment variable ${ROBOT_FILE_PATH} does not exist.

     Run Keyword If    '${IS_HEADLESS_BROWSER}' != ''    Set Environment Variable    IS_HEADLESS_BROWSER    ${IS_HEADLESS_BROWSER}
     ...  ELSE  Log    Environment variable ${IS_HEADLESS_BROWSER} does not exist.

     Run Keyword If    '${BASE_URL}' != ''    Set Environment Variable    BASE_URL    ${BASE_URL}
     ...  ELSE  Log    Environment variable ${BASE_URL} does not exist.

    Run Keyword If    '${BROWSER}' != ''    Set Environment Variable    BROWSER    ${BROWSER}
    ...  ELSE  Log    Environment variable ${BROWSER} does not exist.

    Run Keyword If    '${SUITE_DIRECTORY}' != ''    Set Environment Variable    SUITE_DIRECTORY    ${SUITE_DIRECTORY}
    ...  ELSE  Log    Environment variable called 'SUITE_DIRECTORY', does not exist.

    Run Keyword If    '${MS_USERNAME}' != ''    Set Environment Variable    MS_USERNAME    ${MS_USERNAME}
    ...  ELSE  Log    Environment variable called 'MS_USERNAME', does not exist.

    Run Keyword If    '${MS_PASSWORD}' != ''    Set Environment Variable    MS_PASSWORD    ${MS_PASSWORD}
    ...  ELSE  Log    Environment variable called 'MS_PASSWORD', does not exist.

    IF    '${TEST_CASE_ID}' != ''
          ${TEST_CASE_ID}=    Get Test Case ID    ${TEST_CASE_ID}
           Log    Test Case ID: ${TEST_CASE_ID}
    END


    Run Keyword If    '${TEST_CASE_ID}' != ''    Set Environment Variable    TEST_CASE_ID    ${TEST_CASE_ID}
    ...  ELSE  Log    Environment variable called 'TEST_CASE_ID', does not exist.


Clear environment variables
    clear testrail credentials


Get Test Case ID
    [Arguments]    ${rac_number}
    ${url}=    Set Variable    ${QMETRY_API_URL}/rest/search
    ${headers}=    Create Dictionary    apikey=${API_KEY}  project=987  Content-Type=application/json
    ${payload}=    Create Dictionary    value=${rac_number}  entityType=TC
    #${response}=      POST    ${url}    json=${payload}    headers=${headers}
    ${method}=      Set Variable    POST
    ${response} =       Send Rest Request    ${url}   method=${method}     headers=${headers}     payload=${payload}


    #${json_response}=    To Json    ${response.text}
    Log Many    ${response}
    ${resp_data_1}=    Get From Dictionary    ${response}    data
    ${resp_data_2}=    Get From Dictionary    ${resp_data_1}    data

    ${resp_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${resp_data_2}

    IF    not ${resp_contain_data}
       RETURN    ${EMPTY}
    END

    #Log Many    ${resp_data}
    ${first_row_results}=           Get From List    ${resp_data_2}    0    # Get the first row
    #Log Many    ${first_row_results}
    ${test_case_id}=    Get From Dictionary    ${first_row_results}    tcID
    RETURN    ${test_case_id}


