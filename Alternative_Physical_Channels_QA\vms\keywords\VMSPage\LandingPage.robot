*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Tests the landing page of QR Code Complaints and Compliments

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary

#***********************************PROJECT RESOURCES***************************************
*** Variables ***
${ATM_COMPLAINTS_LINK}                              id=navbarDropdown1
${QR_CODE_COMPLAINTS_AND _COMPLIMENTS_LINK}         xpath=//a[contains(text(),' QR Code Complaints and Compliments')]

${SEARCH_FIELD_INPUT}                               id~searchField
${DETAILS_BTN}                                      id=btnDetails
${UPDATE2_BTN}                                      id=btnUpdate
${CLOSE_POPUP_BTN}                                  xpath=//button[@class='close pull-right']

${REFRESH_BTN}                                      id=refresh

*** Keywords ***
The user clicks navigate to QR Code Complaints and Compliments screen link
    Log to console  --------------------------The user clicks navigate to QR Code Complaints and Compliments scre
    Click Element  ${ATM_COMPLAINTS_LINK}

    Wait Until Element Is Visible  ${QR_CODE_COMPLAINTS_AND _COMPLIMENTS_LINK}     5     QR Code Complaints and Compliments
    
    Click Element  ${QR_CODE_COMPLAINTS_AND _COMPLIMENTS_LINK}
    
    Wait Until Page Contains                                                       QR Code Complaints and Compliments

    Sleep  2s

    Capture page screenshot  QR_Code_C_C_landing_page.png

User searches for a compliant or task
    [Arguments]  ${REFERNCE_NUMBER}
    Log to console  --------------------------User searches for a compliant or task

    Sleep    9s

    Click Element                                   ${REFRESH_BTN} 

    Sleep  7s

    enter_text_to_field    ${SEARCH_FIELD_INPUT}  ${REFERNCE_NUMBER}

    Sleep  10s

    Capture page screenshot  Search_c_c_screen.png

User clciks on details link
    Log to console  --------------------------User clicks on details link
    Sleep    6s
    Click Element  ${DETAILS_BTN}

User clicks on update link
    Log to console  --------------------------User clicks on update link

    Wait Until Element Is Enabled    ${UPDATE2_BTN}  10s

    Click Element  ${UPDATE2_BTN}

User validates that update button is not availble
    [Arguments]  ${NEW_STATUS}
    
    ${is_update_button_available}=                                         Page Should Not Contain Element           id=btnUpdate  message=The Update link is available for a compliment in ${NEW_STATUS} status

    Log To Console    '${is_update_button_available}'

Status dropdown list is empty
    User clicks on update link

    Wait Until Page Contains    Update - ATM Complaints and Compliments

    ${count}=    Get Element Count    xpath=//select[@id='updSelectStatus']/option
    
    Log To Console    "Count: " ${count}

    Wait Until Element Is Enabled    id=updSelectStatus 

    Sleep    6s 

    Capture page screenshot  Update-ATMComplaints_and_Compliments_screen.png
    
    should be equal as numbers  ${count}  1
    