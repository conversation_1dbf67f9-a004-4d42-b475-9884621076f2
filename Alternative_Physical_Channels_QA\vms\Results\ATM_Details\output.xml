<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.2 on win32)" generated="20241114 10:14:06.934" rpa="false" schemaversion="4">
<suite id="s1" name="VMS Portal" source="C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-223_-_Verify_Last_Update_Date-_on_ATM_Details.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241114 10:14:08.388" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241114 10:14:08.388" endtime="20241114 10:14:08.388"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241114 10:14:08.388" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\data\ATM_Details.xml'.</msg>
<status status="PASS" starttime="20241114 10:14:08.388" endtime="20241114 10:14:08.389"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241114 10:14:08.389" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241114 10:14:08.389" endtime="20241114 10:14:08.389"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241114 10:14:08.389" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241114 10:14:08.389" endtime="20241114 10:14:08.389"/>
</kw>
<status status="PASS" starttime="20241114 10:14:08.388" endtime="20241114 10:14:08.389"/>
</kw>
<test id="s1-t1" name="Verify Update From Gasper Button Functionality" line="37">
<kw name="Verify Update From Gasper Button Functionality">
<arg>Verify Update From Gasper Button Functionality</arg>
<arg>VMS_PROD</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20241114 10:14:08.390" level="INFO">Set test documentation to:
Verify Update From Gasper Button Functionality</msg>
<status status="PASS" starttime="20241114 10:14:08.390" endtime="20241114 10:14:08.390"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241114 10:14:08.528" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20241114 10:14:08.391" endtime="20241114 10:14:08.528"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241114 10:14:08.528" endtime="20241114 10:14:08.529"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241114 10:14:08.529" endtime="20241114 10:14:08.529"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241114 10:14:08.531" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<status status="PASS" starttime="20241114 10:14:08.530" endtime="20241114 10:14:08.531"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241114 10:14:08.531" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<status status="PASS" starttime="20241114 10:14:08.531" endtime="20241114 10:14:08.531"/>
</kw>
<status status="PASS" starttime="20241114 10:14:08.531" endtime="20241114 10:14:08.531"/>
</branch>
<status status="PASS" starttime="20241114 10:14:08.531" endtime="20241114 10:14:08.531"/>
</if>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20241114 10:14:08.531" level="INFO">${handle} = msedge.exe</msg>
<status status="PASS" starttime="20241114 10:14:08.531" endtime="20241114 10:14:08.531"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241114 10:14:08.531" endtime="20241114 10:14:08.532"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20241114 10:14:08.576" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20241114 10:14:09.123" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20241114 10:14:09.123" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<status status="PASS" starttime="20241114 10:14:08.532" endtime="20241114 10:14:09.123"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20241114 10:14:09.124" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20241114 10:14:09.124" endtime="20241114 10:14:09.125"/>
</kw>
<status status="PASS" starttime="20241114 10:14:09.124" endtime="20241114 10:14:09.125"/>
</kw>
<status status="PASS" starttime="20241114 10:14:08.530" endtime="20241114 10:14:09.125"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241114 10:14:09.126" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20241114 10:14:09.125" endtime="20241114 10:14:09.126"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20241114 10:14:09.126" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20241114 10:14:09.126" endtime="20241114 10:14:09.126"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20241114 10:14:09.126" level="INFO">${browser_name} = EDGE</msg>
<status status="PASS" starttime="20241114 10:14:09.126" endtime="20241114 10:14:09.126"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241114 10:14:09.127" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<status status="PASS" starttime="20241114 10:14:09.127" endtime="20241114 10:14:09.127"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<msg timestamp="20241114 10:14:09.127" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000002387323C7D0&gt;</msg>
<status status="PASS" starttime="20241114 10:14:09.127" endtime="20241114 10:14:09.127"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20241114 10:14:09.128" endtime="20241114 10:14:09.128"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20241114 10:14:09.128" endtime="20241114 10:14:09.128"/>
</kw>
<status status="NOT RUN" starttime="20241114 10:14:09.127" endtime="20241114 10:14:09.128"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20241114 10:14:09.129" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20241114 10:14:09.128" endtime="20241114 10:14:12.623"/>
</kw>
<status status="PASS" starttime="20241114 10:14:09.128" endtime="20241114 10:14:12.624"/>
</branch>
<status status="PASS" starttime="20241114 10:14:09.127" endtime="20241114 10:14:12.624"/>
</if>
<status status="PASS" starttime="20241114 10:14:09.126" endtime="20241114 10:14:12.624"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.624" endtime="20241114 10:14:12.624"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.624" endtime="20241114 10:14:12.624"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.625" endtime="20241114 10:14:12.625"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.625" endtime="20241114 10:14:12.625"/>
</kw>
<status status="NOT RUN" starttime="20241114 10:14:12.625" endtime="20241114 10:14:12.625"/>
</branch>
<status status="NOT RUN" starttime="20241114 10:14:12.624" endtime="20241114 10:14:12.625"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.626" endtime="20241114 10:14:12.626"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.626" endtime="20241114 10:14:12.626"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.626" endtime="20241114 10:14:12.626"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.626" endtime="20241114 10:14:12.626"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.627" endtime="20241114 10:14:12.627"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.627" endtime="20241114 10:14:12.627"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.627" endtime="20241114 10:14:12.627"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.627" endtime="20241114 10:14:12.627"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.628" endtime="20241114 10:14:12.628"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.628" endtime="20241114 10:14:12.628"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20241114 10:14:12.628" endtime="20241114 10:14:12.628"/>
</kw>
<status status="NOT RUN" starttime="20241114 10:14:12.624" endtime="20241114 10:14:12.628"/>
</branch>
<status status="PASS" starttime="20241114 10:14:09.126" endtime="20241114 10:14:12.628"/>
</if>
<status status="PASS" starttime="20241114 10:14:08.530" endtime="20241114 10:14:12.628"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20241114 10:14:12.669" level="INFO">Property value fetched is:  https://vms.absa.africa/</msg>
<msg timestamp="20241114 10:14:12.670" level="INFO">${base_url} = https://vms.absa.africa/</msg>
<status status="PASS" starttime="20241114 10:14:12.629" endtime="20241114 10:14:12.670"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20241114 10:14:12.671" endtime="20241114 10:14:12.769"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20241114 10:14:12.774" level="INFO">Opening url 'https://vms.absa.africa/'</msg>
<msg timestamp="20241114 10:14:18.514" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DC230&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:22.583" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000023872DDC3B0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:26.664" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DE0C0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:30.744" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x00000238732C9400&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<status status="FAIL" starttime="20241114 10:14:12.773" endtime="20241114 10:14:30.745"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:12.770" endtime="20241114 10:14:30.745"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.745"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:12.628" endtime="20241114 10:14:30.745"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.745"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.745"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.745"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.746"/>
</iter>
<status status="NOT RUN" starttime="20241114 10:14:30.745" endtime="20241114 10:14:30.746"/>
</while>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:08.530" endtime="20241114 10:14:30.746"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:08.529" endtime="20241114 10:14:30.746"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:08.390" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="When The user clicks on the ATM Details link" library="ATMDetails">
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="And The user lands on the ATM Details pages" library="ATMDetails">
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="Then The user validates ATM Last Update" library="ATMDetails">
<status status="NOT RUN" starttime="20241114 10:14:30.746" endtime="20241114 10:14:30.746"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:08.390" endtime="20241114 10:14:30.746"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20241114 10:14:34.815" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504920&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:14:38.900" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504350&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:14:42.949" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002387FEA59D0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:14:51.109" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B479220&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:55.192" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000023872DDC1A0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:59.269" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DE630&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:03.335" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DF020&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg timestamp="20241114 10:15:03.337" level="FAIL">MaxRetryError: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4BFFE0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<status status="FAIL" starttime="20241114 10:14:30.747" endtime="20241114 10:15:03.382"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241114 10:15:03.382" endtime="20241114 10:15:03.382"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241114 10:15:03.383" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20241114 10:15:07.426" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504C20&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:15:11.515" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505310&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:15:15.549" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504D10&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:15:23.694" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B5057C0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:27.755" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505A00&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:31.806" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505C70&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:35.863" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505E80&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg timestamp="20241114 10:15:35.864" level="FAIL">MaxRetryError: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505070&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<status status="FAIL" starttime="20241114 10:15:03.382" endtime="20241114 10:15:35.878"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241114 10:15:38.879" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20241114 10:15:35.879" endtime="20241114 10:15:38.879"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<msg timestamp="20241114 10:15:42.938" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DDE80&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1</msg>
<msg timestamp="20241114 10:15:47.003" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4BFB90&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1</msg>
<msg timestamp="20241114 10:15:51.071" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DC1D0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1</msg>
<status status="PASS" starttime="20241114 10:15:38.879" endtime="20241114 10:15:59.264"/>
</kw>
<status status="FAIL" starttime="20241114 10:14:30.747" endtime="20241114 10:15:59.264">Several failures occurred:

1) MaxRetryError: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4BFFE0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))

2) MaxRetryError: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505070&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</kw>
<doc>Verify Update From Gasper Button Functionality</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" starttime="20241114 10:14:08.389" endtime="20241114 10:15:59.265">Execution terminated by signal

Also teardown failed:
Several failures occurred:

1) MaxRetryError: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4BFFE0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))

2) MaxRetryError: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505070&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</status>
</test>
<doc>Verify Update From Gasper Button Functionality</doc>
<status status="FAIL" starttime="20241114 10:14:06.958" endtime="20241114 10:15:59.266"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">ATM DETAILS</stat>
<stat pass="0" fail="1" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="0" fail="1" skip="0" id="s1" name="VMS Portal">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="20241114 10:14:06.954" level="ERROR">Taking listener 'vms\utility\PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\utility\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\utility\PostExecutionUpdateV2.py", line 3, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\utility
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg timestamp="20241114 10:14:08.385" level="ERROR">Error in file 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 11: Importing library 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\utility\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\utility\PostExecutionUpdateV2.py", line 3, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\vms\utility
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg timestamp="20241114 10:14:09.124" level="WARN">There was error during termination of process</msg>
<msg timestamp="20241114 10:14:18.514" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DC230&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:22.583" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000023872DDC3B0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:26.664" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DE0C0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:30.744" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x00000238732C9400&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg timestamp="20241114 10:14:34.815" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504920&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:14:38.900" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504350&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:14:42.949" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002387FEA59D0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:14:51.109" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B479220&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:55.192" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000023872DDC1A0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:14:59.269" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DE630&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:03.335" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DF020&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg timestamp="20241114 10:15:07.426" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504C20&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:15:11.515" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505310&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:15:15.549" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B504D10&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/elements</msg>
<msg timestamp="20241114 10:15:23.694" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B5057C0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:27.755" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505A00&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:31.806" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505C70&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot</msg>
<msg timestamp="20241114 10:15:35.863" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: HTTPConnectionPool(host='localhost', port=54585): Max retries exceeded with url: /session/4ccc5c9e31eefabd6603e4ab8f2420a1/screenshot (Caused by NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B505E80&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))</msg>
<msg timestamp="20241114 10:15:42.938" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DDE80&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1</msg>
<msg timestamp="20241114 10:15:47.003" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4BFB90&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1</msg>
<msg timestamp="20241114 10:15:51.071" level="WARN">Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x000002380B4DC1D0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/4ccc5c9e31eefabd6603e4ab8f2420a1</msg>
</errors>
</robot>
