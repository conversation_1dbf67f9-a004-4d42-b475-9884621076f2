*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Validate Search - Serial Number Coloumn- on ATM Details

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Validate Search - Serial Number Coloumn- on ATM Details
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${SEARCH_KEY}   ${COLUMN}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user searches FrontEnd for Existing ATM    ${SEARCH_KEY}

    Then The user verifies only one row is data present

    Then The user verifies that searched key appears in the the correct Column      ${COLUMN}   ${SEARCH_KEY}

| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |     ***SEARCH_KEY***   |   ***COLUMN***   |
| Validate Search - Serial Number Coloumn- on ATM Details   | Validate Search - Serial Number Coloumn- on ATM Details  | Validate Search - Serial Number Coloumn- on ATM Details |      VMS_UAT             |     43549618             |   Serial Number             |