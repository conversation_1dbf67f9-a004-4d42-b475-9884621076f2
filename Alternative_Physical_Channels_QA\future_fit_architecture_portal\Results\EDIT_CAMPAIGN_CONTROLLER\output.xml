<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 07:48:15.269" rpa="false" schemaversion="4">
<suite id="s1" name="Future Fit Portal" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_03_CAPTURE_CAMPAIGN.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:48:16.153" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:48:16.153"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:48:16.153" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:48:16.153"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:48:16.153" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:48:16.153"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:48:16.153" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:48:16.153"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:48:16.153"/>
</kw>
<test id="s1-t1" name="FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English And Afrikaans_08395" line="57">
<kw name="Create marketing campaign">
<arg>Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans</arg>
<arg>Yes</arg>
<arg>ATM</arg>
<arg>S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park</arg>
<arg>AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk</arg>
<arg>Idle</arg>
<arg>ATM</arg>
<arg>4</arg>
<arg>6</arg>
<arg>English,Afrikaans</arg>
<arg>images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg,images\\MarketingA_af_2.jpg</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_SIT</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 07:48:16.153" level="INFO">Set test documentation to:
Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans</msg>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:48:16.153"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:48:16.262" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 07:48:16.168" endtime="******** 07:48:16.262"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:16.262" endtime="******** 07:48:16.262"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:16.262" endtime="******** 07:48:16.262"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:48:16.268" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.268"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:48:16.268" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.268"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.268"/>
</branch>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.268"/>
</if>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:16.268" level="INFO">${handle} = msedge.exe</msg>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.268"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.268"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 07:48:16.310" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 07:48:16.690" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 07:48:16.690" level="INFO">${output} = SUCCESS: The process "msedge.exe" with PID 15220 has been terminated.
SUCCESS: The process "msedge.exe" with PID 2148 has been terminated.
SUCCESS: The process "msedge.exe" with PID 15416 has been ter...</msg>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.690"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:16.690"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:48:16.690" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 07:48:16.690" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 07:48:16.690" level="INFO">${browser_name} = EDGE</msg>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:48:16.690" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<msg timestamp="******** 07:48:16.690" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x000001137E173980&gt;</msg>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</kw>
<status status="NOT RUN" starttime="******** 07:48:16.690" endtime="******** 07:48:16.690"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="******** 07:48:16.690" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:18.935"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:18.935"/>
</branch>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:18.935"/>
</if>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:18.936"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.936" endtime="******** 07:48:18.937"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.937" endtime="******** 07:48:18.937"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.938" endtime="******** 07:48:18.938"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.938" endtime="******** 07:48:18.938"/>
</kw>
<status status="NOT RUN" starttime="******** 07:48:18.937" endtime="******** 07:48:18.938"/>
</branch>
<status status="NOT RUN" starttime="******** 07:48:18.937" endtime="******** 07:48:18.938"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.938" endtime="******** 07:48:18.938"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.939" endtime="******** 07:48:18.939"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.939" endtime="******** 07:48:18.939"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.939" endtime="******** 07:48:18.939"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.939" endtime="******** 07:48:18.939"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.939" endtime="******** 07:48:18.939"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.940" endtime="******** 07:48:18.940"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.940" endtime="******** 07:48:18.940"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.940" endtime="******** 07:48:18.940"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.940" endtime="******** 07:48:18.940"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 07:48:18.940" endtime="******** 07:48:18.940"/>
</kw>
<status status="NOT RUN" starttime="******** 07:48:18.936" endtime="******** 07:48:18.941"/>
</branch>
<status status="PASS" starttime="******** 07:48:16.690" endtime="******** 07:48:18.941"/>
</if>
<status status="PASS" starttime="******** 07:48:16.268" endtime="******** 07:48:18.941"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 07:48:18.950" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-sit.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 07:48:18.952" level="INFO">${base_url} = https://apc-portal.atm-marketing-sit.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 07:48:18.942" endtime="******** 07:48:18.952"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 07:48:18.952" endtime="******** 07:48:19.000"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 07:48:19.003" level="INFO">Opening url 'https://apc-portal.atm-marketing-sit.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 07:48:19.003" endtime="******** 07:48:24.192"/>
</kw>
<status status="PASS" starttime="******** 07:48:19.000" endtime="******** 07:48:24.192"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:48:34.212" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:48:24.192" endtime="******** 07:48:34.212"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:48:34.213" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:48:34.240" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 07:48:34.213" endtime="******** 07:48:34.240"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:48:34.240" endtime="******** 07:48:34.240"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:48:44.241" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:48:34.240" endtime="******** 07:48:44.241"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:48:44.241" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:48:44.253" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 07:48:44.241" endtime="******** 07:48:44.253"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:48:44.253" endtime="******** 07:48:44.255"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:48:49.256" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:48:44.255" endtime="******** 07:48:49.256"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:48:49.256" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:48:49.275" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 07:48:49.256" endtime="******** 07:48:49.275"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:48:49.275" endtime="******** 07:48:49.275"/>
</kw>
<status status="PASS" starttime="******** 07:48:18.941" endtime="******** 07:48:49.275"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:48:49.428" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-4.png"&gt;&lt;img src="selenium-screenshot-4.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:48:49.275" endtime="******** 07:48:49.428"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.262" endtime="******** 07:48:49.428"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.262" endtime="******** 07:48:49.428"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.168" endtime="******** 07:48:49.428"/>
</kw>
<kw name="When The user clicks on the Capture campaign link" library="Navigation">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks the Capture campaign link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:49.428" endtime="******** 07:48:49.443"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:48:54.446" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:48:49.443" endtime="******** 07:48:54.446"/>
</kw>
<kw name="Run Keyword Until Success" library="Navigation">
<arg>Click Element</arg>
<arg>${CAPTURE_CAMPAIGN_LINK}</arg>
<kw name="Wait Until Keyword Succeeds" library="BuiltIn">
<arg>30s</arg>
<arg>1s</arg>
<arg>${KW}</arg>
<arg>${KWARGS}</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${KWARGS}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:48:54.454" level="INFO">Clicking element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/div/mat-list-item/span/span[3]'.</msg>
<status status="PASS" starttime="******** 07:48:54.454" endtime="******** 07:48:54.575"/>
</kw>
<status status="PASS" starttime="******** 07:48:54.446" endtime="******** 07:48:54.575"/>
</kw>
<status status="PASS" starttime="******** 07:48:54.446" endtime="******** 07:48:54.575"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>Fill out Campaign Targeted</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 07:48:54.575" endtime="******** 07:48:54.590"/>
</kw>
<status status="PASS" starttime="******** 07:48:49.428" endtime="******** 07:48:54.590"/>
</kw>
<kw name="And The user fills out Campaign Targeted" library="FilloutCampaignTarget">
<arg>${IS_CAMPAIGN_TARGETED}</arg>
<arg>${CAMPAIGN_TARGET}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Targeted: ${IS_CAMPAIGN_TARGETED}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:54.592" endtime="******** 07:48:54.592"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Target: ${CAMPAIGN_TARGET}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:54.592" endtime="******** 07:48:54.592"/>
</kw>
<kw name="User select radio button" library="Navigation">
<arg>${IS_CAMPAIGN_TARGETED}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Selecting radio button ${RADIO_BUTTON_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:54.593" endtime="******** 07:48:54.593"/>
</kw>
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 07:48:54.594" endtime="******** 07:48:56.643"/>
</kw>
<status status="PASS" starttime="******** 07:48:54.594" endtime="******** 07:48:56.643"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${RADIO_BUTTON_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:56.643" level="INFO">${path_string} = //input[@value='</msg>
<status status="PASS" starttime="******** 07:48:56.643" endtime="******** 07:48:56.643"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.643" endtime="******** 07:48:56.657"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:56.657" level="INFO">${path_string} = //input[@value='Yes</msg>
<status status="PASS" starttime="******** 07:48:56.657" endtime="******** 07:48:56.657"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.657" endtime="******** 07:48:56.657"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:56.657" level="INFO">${path_string} = //input[@value='Yes']/parent::*/parent::*/parent::*</msg>
<status status="PASS" starttime="******** 07:48:56.657" endtime="******** 07:48:56.657"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.657" endtime="******** 07:48:56.657"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:48:56.657" endtime="******** 07:48:56.725"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:48:56.727" level="INFO">Clicking element 'xpath=//input[@value='Yes']/parent::*/parent::*/parent::*'.</msg>
<status status="PASS" starttime="******** 07:48:56.727" endtime="******** 07:48:56.779"/>
</kw>
<status status="PASS" starttime="******** 07:48:54.593" endtime="******** 07:48:56.779"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>User select radio button</arg>
<arg>Region</arg>
<arg>ELSE IF</arg>
<arg>'${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>User select radio button</arg>
<arg>ATM</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="User select radio button" library="Navigation">
<arg>ATM</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Selecting radio button ${RADIO_BUTTON_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.780" endtime="******** 07:48:56.781"/>
</kw>
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 07:48:56.781" endtime="******** 07:48:56.788"/>
</kw>
<status status="PASS" starttime="******** 07:48:56.781" endtime="******** 07:48:56.788"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${RADIO_BUTTON_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:56.788" level="INFO">${path_string} = //input[@value='</msg>
<status status="PASS" starttime="******** 07:48:56.788" endtime="******** 07:48:56.788"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.788" endtime="******** 07:48:56.798"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:56.798" level="INFO">${path_string} = //input[@value='ATM</msg>
<status status="PASS" starttime="******** 07:48:56.798" endtime="******** 07:48:56.798"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.798" endtime="******** 07:48:56.798"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:48:56.798" level="INFO">${path_string} = //input[@value='ATM']/parent::*/parent::*/parent::*</msg>
<status status="PASS" starttime="******** 07:48:56.798" endtime="******** 07:48:56.798"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:56.798" endtime="******** 07:48:56.798"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:48:56.798" endtime="******** 07:48:56.838"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:48:56.838" level="INFO">Clicking element 'xpath=//input[@value='ATM']/parent::*/parent::*/parent::*'.</msg>
<status status="PASS" starttime="******** 07:48:56.838" endtime="******** 07:48:57.095"/>
</kw>
<status status="PASS" starttime="******** 07:48:56.780" endtime="******** 07:48:57.095"/>
</kw>
<status status="PASS" starttime="******** 07:48:56.779" endtime="******** 07:48:57.095"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:48:59.095" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:48:57.095" endtime="******** 07:48:59.095"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>Select from dropdown</arg>
<arg>${REGIONAL_CAMPAIGN_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<arg>ELSE IF</arg>
<arg>'${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>Select ATM from dropdown</arg>
<arg>${ATM_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Select ATM from dropdown" library="Navigation">
<arg>${ATM_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:48:59.095" endtime="******** 07:48:59.105"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:01.105" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:48:59.105" endtime="******** 07:49:01.105"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:01.105" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[1]/app-campaign-targeted/div[2]/div[2]/mat-form-field/div/div[1]/div'.</msg>
<status status="PASS" starttime="******** 07:49:01.105" endtime="******** 07:49:02.124"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:07.132" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:49:02.124" endtime="******** 07:49:07.132"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_ATM_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:07.132" level="INFO">${path_string} = //span[contains(text(),</msg>
<status status="PASS" starttime="******** 07:49:07.132" endtime="******** 07:49:07.132"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:07.132" endtime="******** 07:49:07.273"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:07.276" level="INFO">${path_string} = //span[contains(text(),'S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park</msg>
<status status="PASS" starttime="******** 07:49:07.273" endtime="******** 07:49:07.276"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:07.276" endtime="******** 07:49:07.281"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_ATM_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:07.290" level="INFO">${path_string} = //span[contains(text(),'S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park')]</msg>
<status status="PASS" starttime="******** 07:49:07.281" endtime="******** 07:49:07.290"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:07.290" endtime="******** 07:49:07.293"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:49:07.293" endtime="******** 07:49:07.321"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:07.321" level="INFO">Clicking element 'xpath=//span[contains(text(),'S09005 AE OLYMPIA, 1 Olympia Road - Kempton Park')]'.</msg>
<status status="PASS" starttime="******** 07:49:07.321" endtime="******** 07:49:07.490"/>
</kw>
<status status="PASS" starttime="******** 07:48:59.095" endtime="******** 07:49:07.490"/>
</kw>
<status status="PASS" starttime="******** 07:48:59.095" endtime="******** 07:49:07.490"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>FilloutCampaignTarget.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:49:07.598" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="FilloutCampaignTarget.png"&gt;&lt;img src="FilloutCampaignTarget.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:49:07.490" endtime="******** 07:49:07.598"/>
</kw>
<status status="PASS" starttime="******** 07:48:54.591" endtime="******** 07:49:07.598"/>
</kw>
<kw name="And The user clicks on Next button" library="Navigation">
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:49:07.598" endtime="******** 07:49:07.625"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${Next_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:07.625" level="INFO">Clicking element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 07:49:07.625" endtime="******** 07:49:07.701"/>
</kw>
<status status="PASS" starttime="******** 07:49:07.598" endtime="******** 07:49:07.701"/>
</kw>
<kw name="And The user fills out Campaign" library="FilloutYourCampaign">
<arg>${CAMPAIGN_NAME}</arg>
<arg>${MARKETING_TYPE}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>${CAMPAIGN_END_DATE}</arg>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:09.702" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:49:07.702" endtime="******** 07:49:09.702"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:09.702" level="INFO">Clicking element 'xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input'.</msg>
<status status="PASS" starttime="******** 07:49:09.702" endtime="******** 07:49:09.762"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME_INPUT}</arg>
<arg>${CAMPAIGN_NAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="******** 07:49:09.762" level="INFO">Typing text 'AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk' into text field 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 07:49:09.762" endtime="******** 07:49:10.001"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Marketing Type: ${MARKETING_TYPE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:10.001" endtime="******** 07:49:10.001"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Receiver Device Type: ${RECEIVER_DEVICE_TYPE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:10.001" endtime="******** 07:49:10.001"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${RECEIVER_DEVICE_TYPE}' == 'ATM'</arg>
<arg>Select from dropdown</arg>
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>ELSE</arg>
<arg>Select from dropdown</arg>
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Select from dropdown" library="Navigation">
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:10.001" endtime="******** 07:49:10.001"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:12.002" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:49:10.001" endtime="******** 07:49:12.002"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:12.002" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="receiverDeviceType"]'.</msg>
<status status="PASS" starttime="******** 07:49:12.002" endtime="******** 07:49:12.192"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:17.196" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:49:12.192" endtime="******** 07:49:17.196"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.196" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 07:49:17.196" endtime="******** 07:49:17.196"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:17.196" endtime="******** 07:49:17.210"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.210" level="INFO">${path_string} = //span[text()=' ATM </msg>
<status status="PASS" starttime="******** 07:49:17.210" endtime="******** 07:49:17.210"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:17.210" endtime="******** 07:49:17.225"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.225" level="INFO">${path_string} = //span[text()=' ATM ']</msg>
<status status="PASS" starttime="******** 07:49:17.225" endtime="******** 07:49:17.225"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:17.225" endtime="******** 07:49:17.239"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:49:17.239" endtime="******** 07:49:17.266"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.266" level="INFO">Clicking element 'xpath=//span[text()=' ATM ']'.</msg>
<status status="PASS" starttime="******** 07:49:17.266" endtime="******** 07:49:17.312"/>
</kw>
<status status="PASS" starttime="******** 07:49:10.001" endtime="******** 07:49:17.313"/>
</kw>
<status status="PASS" starttime="******** 07:49:10.001" endtime="******** 07:49:17.313"/>
</kw>
<kw name="Select Campaign start and end date" library="FilloutYourCampaign">
<arg>${CAMPAIGN_START_DATE_DATA}</arg>
<arg>${CAMPAIGN_END_DATE_DATA}</arg>
<if>
<branch type="IF" condition="'${CAMPAIGN_START_DATE_DATA.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign start date. The date provided must be a number which represent the number of days from today.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.314" endtime="******** 07:49:17.314"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.313" endtime="******** 07:49:17.314"/>
</branch>
<branch type="ELSE IF" condition="'${CAMPAIGN_END_DATE_DATA.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign end date. The date provided must be a number which represent the number of days from today.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.315" endtime="******** 07:49:17.315"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.314" endtime="******** 07:49:17.315"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.313" endtime="******** 07:49:17.315"/>
</if>
<kw name="Check Data Type" library="GenericMethods">
<var>${start_date_data_type}</var>
<arg>${CAMPAIGN_START_DATE_DATA}</arg>
<doc>Checks if the ${object } is INTEGER, NUMBER or STRING</doc>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>not "${object}"</arg>
<arg>NONE</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Number</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Number" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to a floating point number.</doc>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<msg timestamp="******** 07:49:17.316" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 07:49:17.316" level="INFO">${value} = 4.0</msg>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isnumber}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 07:49:17.316" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'float'&gt;</msg>
<msg timestamp="******** 07:49:17.316" level="FAIL">4 != 4.0</msg>
<status status="FAIL" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<msg timestamp="******** 07:49:17.316" level="INFO">${isnumber} = False</msg>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Integer</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Integer" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to an integer number.</doc>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<msg timestamp="******** 07:49:17.316" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 07:49:17.316" level="INFO">${value} = 4</msg>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isinteger}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 07:49:17.316" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<msg timestamp="******** 07:49:17.316" level="INFO">${isinteger} = True</msg>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.316"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isnumber}</arg>
<arg>'NUMBER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isinteger}</arg>
<arg>'INTEGER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<msg timestamp="******** 07:49:17.320" level="INFO">Returning from the enclosing user keyword.</msg>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<kw name="Return From Keyword" library="BuiltIn">
<arg>'STRING'</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<msg timestamp="******** 07:49:17.320" level="INFO">${start_date_data_type} = 'INTEGER'</msg>
<status status="PASS" starttime="******** 07:49:17.316" endtime="******** 07:49:17.320"/>
</kw>
<if>
<branch type="IF" condition="${start_date_data_type}!='INTEGER'">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the start date in Integer formart!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</if>
<kw name="Check Data Type" library="GenericMethods">
<var>${start_end_data_type}</var>
<arg>${CAMPAIGN_END_DATE_DATA}</arg>
<doc>Checks if the ${object } is INTEGER, NUMBER or STRING</doc>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>not "${object}"</arg>
<arg>NONE</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Number</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Number" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to a floating point number.</doc>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<msg timestamp="******** 07:49:17.320" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 07:49:17.320" level="INFO">${value} = 6.0</msg>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isnumber}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 07:49:17.320" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'float'&gt;</msg>
<msg timestamp="******** 07:49:17.320" level="FAIL">6 != 6.0</msg>
<status status="FAIL" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<msg timestamp="******** 07:49:17.320" level="INFO">${isnumber} = False</msg>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.320"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Integer</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Integer" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to an integer number.</doc>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.326"/>
</kw>
<msg timestamp="******** 07:49:17.326" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 07:49:17.326" level="INFO">${value} = 6</msg>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.326"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isinteger}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 07:49:17.326" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="******** 07:49:17.326" endtime="******** 07:49:17.326"/>
</kw>
<msg timestamp="******** 07:49:17.326" level="INFO">${isinteger} = True</msg>
<status status="PASS" starttime="******** 07:49:17.326" endtime="******** 07:49:17.326"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isnumber}</arg>
<arg>'NUMBER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.326" endtime="******** 07:49:17.326"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isinteger}</arg>
<arg>'INTEGER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<msg timestamp="******** 07:49:17.327" level="INFO">Returning from the enclosing user keyword.</msg>
<status status="PASS" starttime="******** 07:49:17.327" endtime="******** 07:49:17.327"/>
</kw>
<kw name="Return From Keyword" library="BuiltIn">
<arg>'STRING'</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.327" endtime="******** 07:49:17.327"/>
</kw>
<msg timestamp="******** 07:49:17.327" level="INFO">${start_end_data_type} = 'INTEGER'</msg>
<status status="PASS" starttime="******** 07:49:17.320" endtime="******** 07:49:17.327"/>
</kw>
<if>
<branch type="IF" condition="${start_end_data_type}!='INTEGER'">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the end date in Integer formart!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.327" endtime="******** 07:49:17.327"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.327" endtime="******** 07:49:17.327"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.327" endtime="******** 07:49:17.327"/>
</if>
<kw name="Get Current Date" library="DateTime">
<var>${current_date}</var>
<arg>result_format=%Y-%m-%d</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="******** 07:49:17.327" level="INFO">${current_date} = 2024-10-08</msg>
<status status="PASS" starttime="******** 07:49:17.327" endtime="******** 07:49:17.327"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${campaign_required_start_date}</var>
<arg>${current_date}</arg>
<arg>${CAMPAIGN_START_DATE_DATA} days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="******** 07:49:17.335" level="INFO">${campaign_required_start_date} = 2024-10-12 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:17.327" endtime="******** 07:49:17.335"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${campaign_required_end_date}</var>
<arg>${current_date}</arg>
<arg>${CAMPAIGN_END_DATE_DATA} days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="******** 07:49:17.336" level="INFO">${campaign_required_end_date} = 2024-10-14 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:17.335" endtime="******** 07:49:17.336"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date is: ${campaign_required_start_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.337" level="INFO">Campaign Start Date is: 2024-10-12 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:17.337" endtime="******** 07:49:17.337"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date is: ${campaign_required_end_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.337" level="INFO">Campaign End Date is: 2024-10-14 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:17.337" endtime="******** 07:49:17.337"/>
</kw>
<kw name="Search for Date on the Campaign Capturing Calendar" library="FilloutYourCampaign">
<arg>${campaign_required_start_date}</arg>
<arg>${campaign_required_end_date}</arg>
<if>
<branch type="IF" condition="'${CAMPAIGN_START_DATE.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign start date.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.339" endtime="******** 07:49:17.339"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.338" endtime="******** 07:49:17.339"/>
</branch>
<branch type="ELSE IF" condition="'${CAMPAIGN_END_DATE.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign end date.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.341" endtime="******** 07:49:17.341"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.339" endtime="******** 07:49:17.341"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.338" endtime="******** 07:49:17.341"/>
</if>
<kw name="Get the number of displayed web elements" library="GenericMethods">
<var>${element_count}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${webelements}</var>
<arg>${ELEMENT_LOCATOR}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="******** 07:49:17.352" level="INFO">${webelements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="53c2a591d298cf55b46adb9bc5315f98", element="f.334F8B85802DFBC3677FB45EAA5F348B.d.9C5DAEDB5FDAD8A3019B8EBB0956E510.e.82")&gt;]</msg>
<status status="PASS" starttime="******** 07:49:17.341" endtime="******** 07:49:17.352"/>
</kw>
<if>
<branch type="IF" condition="'' == '${webelements}'">
<kw name="Fail" library="BuiltIn">
<arg>Element: '${ELEMENT_LOCATOR}' was not found</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.353" endtime="******** 07:49:17.353"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.352" endtime="******** 07:49:17.353"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.352" endtime="******** 07:49:17.353"/>
</if>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${count}</var>
<arg>${ELEMENT_LOCATOR}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${count} = 1</msg>
<status status="PASS" starttime="******** 07:49:17.353" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Element count is: ${count}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<return>
<value>${count}</value>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</return>
<msg timestamp="******** 07:49:17.360" level="INFO">${element_count} = 1</msg>
<status status="PASS" starttime="******** 07:49:17.341" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${element_count} == 0</arg>
<arg>Fail</arg>
<arg>Calendar element for selecting the campaign date was not found.</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<if>
<branch type="IF" condition="${element_count} &gt; 1">
<kw name="Fail" library="BuiltIn">
<arg>More than 1 element was located for the calendar element</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</if>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_month_year_element_path}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<arg>/descendant::*[@id='mat-calendar-button-0']</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${calendar_month_year_element_path} = xpath=//mat-card[@id='testing'] /descendant::*[@id='mat-calendar-button-0']</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_next_month_element_path}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<arg>/descendant::*[contains(@class,'mat-calendar-next-button')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${calendar_next_month_element_path} = xpath=//mat-card[@id='testing'] /descendant::*[contains(@class,'mat-calendar-next-button')]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_body_element_path}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<arg>/descendant::tbody</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${calendar_body_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_body_rows_element_path}</var>
<arg>${calendar_body_element_path}</arg>
<arg>/tr</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${calendar_body_rows_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_start_date_month_and_year}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>result_format=%b %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${campaign_required_start_date_month_and_year} = Oct 2024</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_start_date_month}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>result_format=%b</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${campaign_required_start_date_month} = Oct</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_end_date_month_and_year}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<arg>result_format=%b %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${campaign_required_end_date_month_and_year} = Oct 2024</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_end_date_month}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<arg>result_format=%b</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">${campaign_required_end_date_month} = Oct</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Create Campaign Quarters" library="GenericMethods">
<kw name="Create List" library="BuiltIn">
<var>@{q1_months}</var>
<arg>Jan</arg>
<arg>Feb</arg>
<arg>Mar</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{q1_months} = [ Jan | Feb | Mar ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>@{q2_months}</var>
<arg>Apr</arg>
<arg>May</arg>
<arg>Jun</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{q2_months} = [ Apr | May | Jun ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>@{q3_months}</var>
<arg>Jul</arg>
<arg>Aug</arg>
<arg>Sep</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{q3_months} = [ Jul | Aug | Sep ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>@{q4_months}</var>
<arg>Oct</arg>
<arg>Nov</arg>
<arg>Dec</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{q4_months} = [ Oct | Nov | Dec ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q1_MONTHS}</arg>
<arg>@{q1_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{Q1_MONTHS} = [ Jan | Feb | Mar ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q2_MONTHS}</arg>
<arg>@{q2_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{Q2_MONTHS} = [ Apr | May | Jun ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q3_MONTHS}</arg>
<arg>@{q3_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{Q3_MONTHS} = [ Jul | Aug | Sep ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q4_MONTHS}</arg>
<arg>@{q4_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 07:49:17.360" level="INFO">@{Q4_MONTHS} = [ Oct | Nov | Dec ]</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_required_start_date_month}' in ${Q1_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q1</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_start_date_month}' in ${Q2_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q2</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_start_date_month}' in ${Q3_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q3</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.360" endtime="******** 07:49:17.360"/>
</branch>
<branch type="ELSE">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q4</arg>
<doc>Converts the given item to a Unicode string.</doc>
<msg timestamp="******** 07:49:17.369" level="INFO">${campaign_start_date_quarter} = q4</msg>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.369"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.369"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.360" endtime="******** 07:49:17.369"/>
</if>
<if>
<branch type="IF" condition="'${campaign_required_end_date_month}' in ${Q1_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q1</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.369" endtime="******** 07:49:17.369"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.369" endtime="******** 07:49:17.369"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_end_date_month}' in ${Q2_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q2</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.370" endtime="******** 07:49:17.370"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.369" endtime="******** 07:49:17.370"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_end_date_month}' in ${Q3_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q3</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</branch>
<branch type="ELSE">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q4</arg>
<doc>Converts the given item to a Unicode string.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${campaign_end_date_quarter} = q4</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.369" endtime="******** 07:49:17.372"/>
</if>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${campaign_start_date_quarter}' != '${campaign_end_date_quarter}'</arg>
<arg>Fail</arg>
<arg>Campaign Start Date and End Date must fall in the same Cycle! The campaign start date falls in '${campaign_start_date_quarter}', while the end date falls in '${campaign_end_date_quarter}'</arg>
<arg>ELSE IF</arg>
<arg>'${campaign_start_date_quarter}' == '${campaign_end_date_quarter}'</arg>
<arg>Log Many</arg>
<arg>Campaign Start Date and End Date fall in the same Cycle...</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Start Date and End Date fall in the same Cycle...</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">Campaign Start Date and End Date fall in the same Cycle...</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_start_date_month_and_year: ${campaign_required_start_date_month_and_year}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">campaign_required_start_date_month_and_year: Oct 2024</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_start_date_month: ${campaign_required_start_date_month}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">campaign_required_start_date_month: Oct</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_end_date_month_and_year: ${campaign_required_end_date_month_and_year}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">campaign_required_end_date_month_and_year: Oct 2024</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_end_date_month: ${campaign_required_end_date_month}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">campaign_required_end_date_month: Oct</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Split String" library="String">
<var>${start_date_array}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>${SPACE}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${start_date_array} = ['2024-10-12', '00:00:00.000']</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Split String" library="String">
<var>${start_date_array_two}</var>
<arg>${start_date_array}[0]</arg>
<arg>separator=-</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${start_date_array_two} = ['2024', '10', '12']</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_start_day}</var>
<arg>${start_date_array_two}[2]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${campaign_start_day} = 12</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_start_day:${campaign_start_day}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">campaign_start_day:12</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Split String" library="String">
<var>${end_date_array}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<arg>${SPACE}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${end_date_array} = ['2024-10-14', '00:00:00.000']</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Split String" library="String">
<var>${end_date_array_two}</var>
<arg>${end_date_array}[0]</arg>
<arg>separator=-</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${end_date_array_two} = ['2024', '10', '14']</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_end_day}</var>
<arg>${end_date_array_two}[2]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${campaign_end_day} = 14</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_end_day:${campaign_end_day}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">campaign_end_day:14</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${campaign_required_start_date_month_and_year}</var>
<arg>${campaign_required_start_date_month_and_year}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 07:49:17.372" level="INFO">${campaign_required_start_date_month_and_year} = OCT 2024</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.372"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.401" level="INFO">${curr_month_year_displayed} = OCT 2024</msg>
<status status="PASS" starttime="******** 07:49:17.372" endtime="******** 07:49:17.401"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Current Displayed Month and Year:${curr_month_year_displayed}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.402" level="INFO">Current Displayed Month and Year:OCT 2024</msg>
<status status="PASS" starttime="******** 07:49:17.402" endtime="******** 07:49:17.402"/>
</kw>
<while condition="'${curr_month_year_displayed.strip()}' != '${campaign_required_start_date_month_and_year.strip()}'">
<iter>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${calendar_next_month_element_path}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.406" endtime="******** 07:49:17.406"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.406" endtime="******** 07:49:17.406"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.406" endtime="******** 07:49:17.406"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Month displayed:'${curr_month_year_displayed}'</arg>
<arg>Month Required:'${campaign_required_start_date_month_and_year}'</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.406" endtime="******** 07:49:17.406"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.402" endtime="******** 07:49:17.406"/>
</iter>
<status status="NOT RUN" starttime="******** 07:49:17.402" endtime="******** 07:49:17.406"/>
</while>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${rows}</var>
<arg>${calendar_body_rows_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:17.415" level="INFO">${rows} = 6</msg>
<status status="PASS" starttime="******** 07:49:17.406" endtime="******** 07:49:17.415"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:17.416" level="INFO">${bool_start_date_selected} = False</msg>
<status status="PASS" starttime="******** 07:49:17.416" endtime="******** 07:49:17.416"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_end_selected}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:17.416" level="INFO">${bool_start_end_selected} = False</msg>
<status status="PASS" starttime="******** 07:49:17.416" endtime="******** 07:49:17.416"/>
</kw>
<kw name="Get Substring" library="String">
<var>${campaign_start_day_part}</var>
<arg>${campaign_start_day.strip()}</arg>
<arg>0</arg>
<arg>1</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 07:49:17.416" level="INFO">${campaign_start_day_part} = 1</msg>
<status status="PASS" starttime="******** 07:49:17.416" endtime="******** 07:49:17.416"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_start_day.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.419" level="INFO">12</msg>
<status status="PASS" starttime="******** 07:49:17.416" endtime="******** 07:49:17.420"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day_part}' == '0'">
<kw name="Get Substring" library="String">
<var>${campaign_start_day}</var>
<arg>${campaign_start_day.strip()}</arg>
<arg>1</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.420" endtime="******** 07:49:17.420"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.420" endtime="******** 07:49:17.420"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.420" endtime="******** 07:49:17.420"/>
</if>
<kw name="Log" library="BuiltIn">
<arg>${campaign_start_day_part}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.420" level="INFO">1</msg>
<status status="PASS" starttime="******** 07:49:17.420" endtime="******** 07:49:17.420"/>
</kw>
<for flavor="IN RANGE">
<var>${row_num}</var>
<value>1</value>
<value>${rows+1}</value>
<iter>
<var name="${row_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.422" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1]</msg>
<status status="PASS" starttime="******** 07:49:17.421" endtime="******** 07:49:17.422"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.422" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td</msg>
<status status="PASS" starttime="******** 07:49:17.422" endtime="******** 07:49:17.422"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:17.432" level="INFO">${cols} = 1</msg>
<status status="PASS" starttime="******** 07:49:17.422" endtime="******** 07:49:17.432"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.432" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:17.432" endtime="******** 07:49:17.432"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.449" level="INFO">${campaign_date} = OCT</msg>
<status status="PASS" starttime="******** 07:49:17.432" endtime="******** 07:49:17.449"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.449" level="INFO">OCT</msg>
<status status="PASS" starttime="******** 07:49:17.449" endtime="******** 07:49:17.449"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.452" endtime="******** 07:49:17.452"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.452" endtime="******** 07:49:17.452"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.452" endtime="******** 07:49:17.452"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.453" endtime="******** 07:49:17.453"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.453" endtime="******** 07:49:17.453"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.453" endtime="******** 07:49:17.453"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.453" endtime="******** 07:49:17.453"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.449" endtime="******** 07:49:17.453"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.449" endtime="******** 07:49:17.453"/>
</if>
<status status="PASS" starttime="******** 07:49:17.432" endtime="******** 07:49:17.453"/>
</iter>
<status status="PASS" starttime="******** 07:49:17.432" endtime="******** 07:49:17.453"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.454" endtime="******** 07:49:17.454"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.421" endtime="******** 07:49:17.454"/>
</iter>
<iter>
<var name="${row_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.454" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2]</msg>
<status status="PASS" starttime="******** 07:49:17.454" endtime="******** 07:49:17.454"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.454" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td</msg>
<status status="PASS" starttime="******** 07:49:17.454" endtime="******** 07:49:17.454"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:17.466" level="INFO">${cols} = 6</msg>
<status status="PASS" starttime="******** 07:49:17.456" endtime="******** 07:49:17.466"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.466" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:17.466" endtime="******** 07:49:17.466"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.487" level="INFO">${campaign_date} = </msg>
<status status="PASS" starttime="******** 07:49:17.466" endtime="******** 07:49:17.487"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.488" level="INFO"/>
<status status="PASS" starttime="******** 07:49:17.487" endtime="******** 07:49:17.488"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.489" endtime="******** 07:49:17.489"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.490" endtime="******** 07:49:17.490"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.490" endtime="******** 07:49:17.490"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.490" endtime="******** 07:49:17.490"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.490" endtime="******** 07:49:17.490"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.490" endtime="******** 07:49:17.490"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.490" endtime="******** 07:49:17.490"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.488" endtime="******** 07:49:17.490"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.488" endtime="******** 07:49:17.490"/>
</if>
<status status="PASS" starttime="******** 07:49:17.466" endtime="******** 07:49:17.490"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.491" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [2]</msg>
<status status="PASS" starttime="******** 07:49:17.491" endtime="******** 07:49:17.491"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.508" level="INFO">${campaign_date} = 1</msg>
<status status="PASS" starttime="******** 07:49:17.491" endtime="******** 07:49:17.508"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.508" level="INFO">1</msg>
<status status="PASS" starttime="******** 07:49:17.508" endtime="******** 07:49:17.508"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.508" endtime="******** 07:49:17.511"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.508" endtime="******** 07:49:17.511"/>
</if>
<status status="PASS" starttime="******** 07:49:17.490" endtime="******** 07:49:17.511"/>
</iter>
<iter>
<var name="${col_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.511" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [3]</msg>
<status status="PASS" starttime="******** 07:49:17.511" endtime="******** 07:49:17.511"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.529" level="INFO">${campaign_date} = 2</msg>
<status status="PASS" starttime="******** 07:49:17.511" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.529" level="INFO">2</msg>
<status status="PASS" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</if>
<status status="PASS" starttime="******** 07:49:17.511" endtime="******** 07:49:17.529"/>
</iter>
<iter>
<var name="${col_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.529" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [4]</msg>
<status status="PASS" starttime="******** 07:49:17.529" endtime="******** 07:49:17.529"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.550" level="INFO">${campaign_date} = 3</msg>
<status status="PASS" starttime="******** 07:49:17.529" endtime="******** 07:49:17.550"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.550" level="INFO">3</msg>
<status status="PASS" starttime="******** 07:49:17.550" endtime="******** 07:49:17.550"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.552" endtime="******** 07:49:17.552"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.552" endtime="******** 07:49:17.552"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.552" endtime="******** 07:49:17.552"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.553" endtime="******** 07:49:17.553"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.553" endtime="******** 07:49:17.553"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.553" endtime="******** 07:49:17.553"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.554" endtime="******** 07:49:17.554"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.550" endtime="******** 07:49:17.554"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.550" endtime="******** 07:49:17.554"/>
</if>
<status status="PASS" starttime="******** 07:49:17.529" endtime="******** 07:49:17.554"/>
</iter>
<iter>
<var name="${col_num}">5</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.554" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [5]</msg>
<status status="PASS" starttime="******** 07:49:17.554" endtime="******** 07:49:17.554"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.572" level="INFO">${campaign_date} = 4</msg>
<status status="PASS" starttime="******** 07:49:17.554" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.572" level="INFO">4</msg>
<status status="PASS" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</if>
<status status="PASS" starttime="******** 07:49:17.554" endtime="******** 07:49:17.572"/>
</iter>
<iter>
<var name="${col_num}">6</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.572" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [6]</msg>
<status status="PASS" starttime="******** 07:49:17.572" endtime="******** 07:49:17.572"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.627" level="INFO">${campaign_date} = 5</msg>
<status status="PASS" starttime="******** 07:49:17.572" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.627" level="INFO">5</msg>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</if>
<status status="PASS" starttime="******** 07:49:17.572" endtime="******** 07:49:17.627"/>
</iter>
<status status="PASS" starttime="******** 07:49:17.466" endtime="******** 07:49:17.627"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.454" endtime="******** 07:49:17.627"/>
</iter>
<iter>
<var name="${row_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.627" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3]</msg>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.627" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td</msg>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:17.627"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:17.730" level="INFO">${cols} = 7</msg>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:17.730"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.730" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:17.730" endtime="******** 07:49:17.730"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.760" level="INFO">${campaign_date} = 6</msg>
<status status="PASS" starttime="******** 07:49:17.730" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.760" level="INFO">6</msg>
<status status="PASS" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</if>
<status status="PASS" starttime="******** 07:49:17.730" endtime="******** 07:49:17.760"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.760" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [2]</msg>
<status status="PASS" starttime="******** 07:49:17.760" endtime="******** 07:49:17.760"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.810" level="INFO">${campaign_date} = 7</msg>
<status status="PASS" starttime="******** 07:49:17.760" endtime="******** 07:49:17.810"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.812" level="INFO">7</msg>
<status status="PASS" starttime="******** 07:49:17.810" endtime="******** 07:49:17.812"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</if>
<status status="PASS" starttime="******** 07:49:17.760" endtime="******** 07:49:17.812"/>
</iter>
<iter>
<var name="${col_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.812" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [3]</msg>
<status status="PASS" starttime="******** 07:49:17.812" endtime="******** 07:49:17.812"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.830" level="INFO">${campaign_date} = 8</msg>
<status status="PASS" starttime="******** 07:49:17.812" endtime="******** 07:49:17.830"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.836" level="INFO">8</msg>
<status status="PASS" starttime="******** 07:49:17.830" endtime="******** 07:49:17.836"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.836" endtime="******** 07:49:17.838"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.836" endtime="******** 07:49:17.838"/>
</if>
<status status="PASS" starttime="******** 07:49:17.812" endtime="******** 07:49:17.838"/>
</iter>
<iter>
<var name="${col_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.838" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [4]</msg>
<status status="PASS" starttime="******** 07:49:17.838" endtime="******** 07:49:17.838"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.860" level="INFO">${campaign_date} = 9</msg>
<status status="PASS" starttime="******** 07:49:17.838" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.860" level="INFO">9</msg>
<status status="PASS" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</if>
<status status="PASS" starttime="******** 07:49:17.838" endtime="******** 07:49:17.860"/>
</iter>
<iter>
<var name="${col_num}">5</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.860" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [5]</msg>
<status status="PASS" starttime="******** 07:49:17.860" endtime="******** 07:49:17.860"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.882" level="INFO">${campaign_date} = 10</msg>
<status status="PASS" starttime="******** 07:49:17.860" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.882" level="INFO">10</msg>
<status status="PASS" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.882" endtime="******** 07:49:17.882"/>
</if>
<status status="PASS" starttime="******** 07:49:17.860" endtime="******** 07:49:17.882"/>
</iter>
<iter>
<var name="${col_num}">6</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.886" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [6]</msg>
<status status="PASS" starttime="******** 07:49:17.882" endtime="******** 07:49:17.887"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.905" level="INFO">${campaign_date} = 11</msg>
<status status="PASS" starttime="******** 07:49:17.887" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.905" level="INFO">11</msg>
<status status="PASS" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</if>
<status status="PASS" starttime="******** 07:49:17.882" endtime="******** 07:49:17.905"/>
</iter>
<iter>
<var name="${col_num}">7</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:17.905" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [7]</msg>
<status status="PASS" starttime="******** 07:49:17.905" endtime="******** 07:49:17.905"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.941" level="INFO">${campaign_date} = 12</msg>
<status status="PASS" starttime="******** 07:49:17.905" endtime="******** 07:49:17.941"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.941" level="INFO">12</msg>
<status status="PASS" starttime="******** 07:49:17.941" endtime="******** 07:49:17.941"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" starttime="******** 07:49:17.941" endtime="******** 07:49:17.962"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:17.962" level="INFO">${row_num} = 0</msg>
<status status="PASS" starttime="******** 07:49:17.962" endtime="******** 07:49:17.962"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:17.963" level="INFO">${bool_start_date_selected} = True</msg>
<status status="PASS" starttime="******** 07:49:17.962" endtime="******** 07:49:17.963"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:17.963" level="INFO">Clicking element 'xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [7]'.</msg>
<status status="PASS" starttime="******** 07:49:17.963" endtime="******** 07:49:17.998"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:17.999" level="INFO">Campaign Start Date Selected!</msg>
<status status="PASS" starttime="******** 07:49:17.999" endtime="******** 07:49:17.999"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:49:18.120" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="StartDateSelection.png"&gt;&lt;img src="StartDateSelection.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:49:17.999" endtime="******** 07:49:18.120"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 07:49:18.120" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 07:49:18.120" endtime="******** 07:49:18.120"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.941" endtime="******** 07:49:18.120"/>
</branch>
<status status="PASS" starttime="******** 07:49:17.941" endtime="******** 07:49:18.120"/>
</if>
<status status="PASS" starttime="******** 07:49:17.905" endtime="******** 07:49:18.120"/>
</iter>
<status status="PASS" starttime="******** 07:49:17.730" endtime="******** 07:49:18.120"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 07:49:18.120" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 07:49:18.120" endtime="******** 07:49:18.120"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.120" endtime="******** 07:49:18.120"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.627" endtime="******** 07:49:18.120"/>
</iter>
<status status="PASS" starttime="******** 07:49:17.420" endtime="******** 07:49:18.120"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${False}</arg>
<arg>Fail</arg>
<arg>Campaign start date: '${campaign_start_day.strip()}' was not selected!!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:18.120" endtime="******** 07:49:18.120"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${campaign_required_end_date_month_and_year}</var>
<arg>${campaign_required_end_date_month_and_year}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 07:49:18.120" level="INFO">${campaign_required_end_date_month_and_year} = OCT 2024</msg>
<status status="PASS" starttime="******** 07:49:18.120" endtime="******** 07:49:18.120"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.173" level="INFO">${curr_month_year_displayed} = OCT 2024</msg>
<status status="PASS" starttime="******** 07:49:18.120" endtime="******** 07:49:18.173"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Current Displayed Month and Year:${curr_month_year_displayed}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:18.173" endtime="******** 07:49:18.173"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Month Required:${campaign_required_end_date_month_and_year}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:18.173" endtime="******** 07:49:18.173"/>
</kw>
<while condition="'${curr_month_year_displayed.strip()}' != '${campaign_required_end_date_month_and_year.strip()}'">
<iter>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${calendar_next_month_element_path}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.175" endtime="******** 07:49:18.175"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.175" endtime="******** 07:49:18.175"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.175" endtime="******** 07:49:18.175"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Month displayed:'${curr_month_year_displayed}'</arg>
<arg>Month Required:'${campaign_required_end_date_month_and_year}'</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.175" endtime="******** 07:49:18.175"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.173" endtime="******** 07:49:18.175"/>
</iter>
<status status="NOT RUN" starttime="******** 07:49:18.173" endtime="******** 07:49:18.175"/>
</while>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${rows_new}</var>
<arg>${calendar_body_rows_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:18.185" level="INFO">${rows_new} = 6</msg>
<status status="PASS" starttime="******** 07:49:18.175" endtime="******** 07:49:18.185"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:18.186" level="INFO">${bool_end_date_selected} = False</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_end_day.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.186" level="INFO">14</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<kw name="Get Substring" library="String">
<var>${campaign_end_day_part}</var>
<arg>${campaign_end_day.strip()}</arg>
<arg>0</arg>
<arg>1</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 07:49:18.186" level="INFO">${campaign_end_day_part} = 1</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day_part}' == '0'">
<kw name="Get Substring" library="String">
<var>${campaign_end_day}</var>
<arg>${campaign_end_day.strip()}</arg>
<arg>1</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</if>
<kw name="Log" library="BuiltIn">
<arg>${campaign_end_day.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.186" level="INFO">14</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<for flavor="IN RANGE">
<var>${row_num}</var>
<value>1</value>
<value>${rows_new+1}</value>
<iter>
<var name="${row_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.186" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1]</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.186" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.186"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:18.199" level="INFO">${cols} = 1</msg>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.199"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.202" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:18.202" endtime="******** 07:49:18.202"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.214" level="INFO">${campaign_date} = OCT</msg>
<status status="PASS" starttime="******** 07:49:18.202" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.214" level="INFO">OCT</msg>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</if>
<status status="PASS" starttime="******** 07:49:18.202" endtime="******** 07:49:18.214"/>
</iter>
<status status="PASS" starttime="******** 07:49:18.199" endtime="******** 07:49:18.214"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.214"/>
</iter>
<iter>
<var name="${row_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.214" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2]</msg>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.214" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td</msg>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.214"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:18.229" level="INFO">${cols} = 6</msg>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.229"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.231" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:18.230" endtime="******** 07:49:18.231"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.248" level="INFO">${campaign_date} = </msg>
<status status="PASS" starttime="******** 07:49:18.231" endtime="******** 07:49:18.248"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.248" level="INFO"/>
<status status="PASS" starttime="******** 07:49:18.248" endtime="******** 07:49:18.248"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.250" endtime="******** 07:49:18.250"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.248" endtime="******** 07:49:18.250"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.248" endtime="******** 07:49:18.250"/>
</if>
<status status="PASS" starttime="******** 07:49:18.230" endtime="******** 07:49:18.250"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.254" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [2]</msg>
<status status="PASS" starttime="******** 07:49:18.250" endtime="******** 07:49:18.254"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.272" level="INFO">${campaign_date} = 1</msg>
<status status="PASS" starttime="******** 07:49:18.254" endtime="******** 07:49:18.272"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.272" level="INFO">1</msg>
<status status="PASS" starttime="******** 07:49:18.272" endtime="******** 07:49:18.272"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.272" endtime="******** 07:49:18.272"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.272" endtime="******** 07:49:18.272"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.272" endtime="******** 07:49:18.272"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.276" endtime="******** 07:49:18.276"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.276" endtime="******** 07:49:18.276"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.277" endtime="******** 07:49:18.277"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.277" endtime="******** 07:49:18.277"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.272" endtime="******** 07:49:18.277"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.272" endtime="******** 07:49:18.277"/>
</if>
<status status="PASS" starttime="******** 07:49:18.250" endtime="******** 07:49:18.277"/>
</iter>
<iter>
<var name="${col_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.277" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [3]</msg>
<status status="PASS" starttime="******** 07:49:18.277" endtime="******** 07:49:18.277"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.298" level="INFO">${campaign_date} = 2</msg>
<status status="PASS" starttime="******** 07:49:18.277" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.298" level="INFO">2</msg>
<status status="PASS" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</if>
<status status="PASS" starttime="******** 07:49:18.277" endtime="******** 07:49:18.298"/>
</iter>
<iter>
<var name="${col_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.298" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [4]</msg>
<status status="PASS" starttime="******** 07:49:18.298" endtime="******** 07:49:18.298"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.325" level="INFO">${campaign_date} = 3</msg>
<status status="PASS" starttime="******** 07:49:18.298" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.325" level="INFO">3</msg>
<status status="PASS" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</if>
<status status="PASS" starttime="******** 07:49:18.298" endtime="******** 07:49:18.325"/>
</iter>
<iter>
<var name="${col_num}">5</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.325" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [5]</msg>
<status status="PASS" starttime="******** 07:49:18.325" endtime="******** 07:49:18.325"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.342" level="INFO">${campaign_date} = 4</msg>
<status status="PASS" starttime="******** 07:49:18.325" endtime="******** 07:49:18.342"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.350" level="INFO">4</msg>
<status status="PASS" starttime="******** 07:49:18.350" endtime="******** 07:49:18.350"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.353" endtime="******** 07:49:18.353"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.350" endtime="******** 07:49:18.353"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.350" endtime="******** 07:49:18.353"/>
</if>
<status status="PASS" starttime="******** 07:49:18.325" endtime="******** 07:49:18.353"/>
</iter>
<iter>
<var name="${col_num}">6</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.355" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [6]</msg>
<status status="PASS" starttime="******** 07:49:18.354" endtime="******** 07:49:18.355"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.371" level="INFO">${campaign_date} = 5</msg>
<status status="PASS" starttime="******** 07:49:18.355" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.371" level="INFO">5</msg>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</if>
<status status="PASS" starttime="******** 07:49:18.354" endtime="******** 07:49:18.371"/>
</iter>
<status status="PASS" starttime="******** 07:49:18.229" endtime="******** 07:49:18.371"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.214" endtime="******** 07:49:18.371"/>
</iter>
<iter>
<var name="${row_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.371" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3]</msg>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.371" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td</msg>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.371"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:18.394" level="INFO">${cols} = 7</msg>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.394"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.396" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:18.396" endtime="******** 07:49:18.396"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.418" level="INFO">${campaign_date} = 6</msg>
<status status="PASS" starttime="******** 07:49:18.396" endtime="******** 07:49:18.418"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.418" level="INFO">6</msg>
<status status="PASS" starttime="******** 07:49:18.418" endtime="******** 07:49:18.418"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.418" endtime="******** 07:49:18.422"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.418" endtime="******** 07:49:18.422"/>
</if>
<status status="PASS" starttime="******** 07:49:18.396" endtime="******** 07:49:18.422"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.422" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [2]</msg>
<status status="PASS" starttime="******** 07:49:18.422" endtime="******** 07:49:18.422"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.441" level="INFO">${campaign_date} = 7</msg>
<status status="PASS" starttime="******** 07:49:18.422" endtime="******** 07:49:18.441"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.441" level="INFO">7</msg>
<status status="PASS" starttime="******** 07:49:18.441" endtime="******** 07:49:18.441"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.441" endtime="******** 07:49:18.450"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.441" endtime="******** 07:49:18.450"/>
</if>
<status status="PASS" starttime="******** 07:49:18.422" endtime="******** 07:49:18.450"/>
</iter>
<iter>
<var name="${col_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.450" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [3]</msg>
<status status="PASS" starttime="******** 07:49:18.450" endtime="******** 07:49:18.450"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.482" level="INFO">${campaign_date} = 8</msg>
<status status="PASS" starttime="******** 07:49:18.450" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.482" level="INFO">8</msg>
<status status="PASS" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.482"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.498" endtime="******** 07:49:18.498"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.482" endtime="******** 07:49:18.498"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.482" endtime="******** 07:49:18.498"/>
</if>
<status status="PASS" starttime="******** 07:49:18.450" endtime="******** 07:49:18.498"/>
</iter>
<iter>
<var name="${col_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.499" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [4]</msg>
<status status="PASS" starttime="******** 07:49:18.499" endtime="******** 07:49:18.499"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.520" level="INFO">${campaign_date} = 9</msg>
<status status="PASS" starttime="******** 07:49:18.499" endtime="******** 07:49:18.520"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.520" level="INFO">9</msg>
<status status="PASS" starttime="******** 07:49:18.520" endtime="******** 07:49:18.520"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.529" endtime="******** 07:49:18.529"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.529" endtime="******** 07:49:18.529"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.529" endtime="******** 07:49:18.529"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.529" endtime="******** 07:49:18.529"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.529" endtime="******** 07:49:18.529"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.534" endtime="******** 07:49:18.534"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.534" endtime="******** 07:49:18.534"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.529" endtime="******** 07:49:18.535"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.529" endtime="******** 07:49:18.535"/>
</if>
<status status="PASS" starttime="******** 07:49:18.498" endtime="******** 07:49:18.535"/>
</iter>
<iter>
<var name="${col_num}">5</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.535" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [5]</msg>
<status status="PASS" starttime="******** 07:49:18.535" endtime="******** 07:49:18.535"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.548" level="INFO">${campaign_date} = 10</msg>
<status status="PASS" starttime="******** 07:49:18.535" endtime="******** 07:49:18.548"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.548" level="INFO">10</msg>
<status status="PASS" starttime="******** 07:49:18.548" endtime="******** 07:49:18.548"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.548" endtime="******** 07:49:18.561"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.548" endtime="******** 07:49:18.561"/>
</if>
<status status="PASS" starttime="******** 07:49:18.535" endtime="******** 07:49:18.561"/>
</iter>
<iter>
<var name="${col_num}">6</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.561" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [6]</msg>
<status status="PASS" starttime="******** 07:49:18.561" endtime="******** 07:49:18.561"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.581" level="INFO">${campaign_date} = 11</msg>
<status status="PASS" starttime="******** 07:49:18.561" endtime="******** 07:49:18.581"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.581" level="INFO">11</msg>
<status status="PASS" starttime="******** 07:49:18.581" endtime="******** 07:49:18.581"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.581" endtime="******** 07:49:18.593"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.581" endtime="******** 07:49:18.593"/>
</if>
<status status="PASS" starttime="******** 07:49:18.561" endtime="******** 07:49:18.593"/>
</iter>
<iter>
<var name="${col_num}">7</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.593" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [7]</msg>
<status status="PASS" starttime="******** 07:49:18.593" endtime="******** 07:49:18.593"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.614" level="INFO">${campaign_date} = 12</msg>
<status status="PASS" starttime="******** 07:49:18.593" endtime="******** 07:49:18.614"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.614" level="INFO">12</msg>
<status status="PASS" starttime="******** 07:49:18.614" endtime="******** 07:49:18.614"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.625"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.625"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.625"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.625"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.625"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.625"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.625" endtime="******** 07:49:18.627"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.614" endtime="******** 07:49:18.627"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.614" endtime="******** 07:49:18.627"/>
</if>
<status status="PASS" starttime="******** 07:49:18.593" endtime="******** 07:49:18.627"/>
</iter>
<status status="PASS" starttime="******** 07:49:18.394" endtime="******** 07:49:18.627"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:18.628" endtime="******** 07:49:18.628"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.371" endtime="******** 07:49:18.628"/>
</iter>
<iter>
<var name="${row_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.628" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [4]</msg>
<status status="PASS" starttime="******** 07:49:18.628" endtime="******** 07:49:18.628"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.630" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [4] /td</msg>
<status status="PASS" starttime="******** 07:49:18.630" endtime="******** 07:49:18.630"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:49:18.641" level="INFO">${cols} = 7</msg>
<status status="PASS" starttime="******** 07:49:18.630" endtime="******** 07:49:18.641"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.644" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [4] /td [1]</msg>
<status status="PASS" starttime="******** 07:49:18.644" endtime="******** 07:49:18.644"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.668" level="INFO">${campaign_date} = 13</msg>
<status status="PASS" starttime="******** 07:49:18.644" endtime="******** 07:49:18.668"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.668" level="INFO">13</msg>
<status status="PASS" starttime="******** 07:49:18.668" endtime="******** 07:49:18.668"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.673" endtime="******** 07:49:18.673"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.673" endtime="******** 07:49:18.673"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.675" endtime="******** 07:49:18.675"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.675" endtime="******** 07:49:18.675"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.675" endtime="******** 07:49:18.675"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.675" endtime="******** 07:49:18.675"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 07:49:18.675" endtime="******** 07:49:18.675"/>
</kw>
<status status="NOT RUN" starttime="******** 07:49:18.668" endtime="******** 07:49:18.675"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.668" endtime="******** 07:49:18.675"/>
</if>
<status status="PASS" starttime="******** 07:49:18.644" endtime="******** 07:49:18.675"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:18.675" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [4] /td [2]</msg>
<status status="PASS" starttime="******** 07:49:18.675" endtime="******** 07:49:18.675"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.705" level="INFO">${campaign_date} = 14</msg>
<status status="PASS" starttime="******** 07:49:18.679" endtime="******** 07:49:18.705"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.707" level="INFO">14</msg>
<status status="PASS" starttime="******** 07:49:18.707" endtime="******** 07:49:18.707"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" starttime="******** 07:49:18.707" endtime="******** 07:49:18.721"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:18.721" level="INFO">${row_num} = 0</msg>
<status status="PASS" starttime="******** 07:49:18.721" endtime="******** 07:49:18.721"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:18.721" level="INFO">${bool_end_date_selected} = True</msg>
<status status="PASS" starttime="******** 07:49:18.721" endtime="******** 07:49:18.721"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:18.721" level="INFO">Clicking element 'xpath=//mat-card[@id='testing'] /descendant::tbody /tr [4] /td [2]'.</msg>
<status status="PASS" starttime="******** 07:49:18.721" endtime="******** 07:49:18.802"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:18.802" level="INFO">Campaign End Date Selected!</msg>
<status status="PASS" starttime="******** 07:49:18.802" endtime="******** 07:49:18.802"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:49:18.918" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="EndDateSelection.png"&gt;&lt;img src="EndDateSelection.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:49:18.802" endtime="******** 07:49:18.920"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 07:49:18.921" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 07:49:18.920" endtime="******** 07:49:18.921"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.707" endtime="******** 07:49:18.921"/>
</branch>
<status status="PASS" starttime="******** 07:49:18.707" endtime="******** 07:49:18.921"/>
</if>
<status status="PASS" starttime="******** 07:49:18.675" endtime="******** 07:49:18.921"/>
</iter>
<status status="PASS" starttime="******** 07:49:18.641" endtime="******** 07:49:18.921"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 07:49:18.921" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 07:49:18.921" endtime="******** 07:49:18.921"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.921" endtime="******** 07:49:18.921"/>
</kw>
<status status="PASS" starttime="******** 07:49:18.628" endtime="******** 07:49:18.921"/>
</iter>
<status status="PASS" starttime="******** 07:49:18.186" endtime="******** 07:49:18.921"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${False}</arg>
<arg>Fail</arg>
<arg>Campaign end date: '${campaign_end_day.strip()}' was not selected!!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:18.921" endtime="******** 07:49:18.921"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.337" endtime="******** 07:49:18.921"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAPTURED_CAMPAIGN_START_DATE</arg>
<arg>${campaign_required_start_date}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:49:18.923" level="INFO">Environment variable 'CAPTURED_CAMPAIGN_START_DATE' set to value '2024-10-12 00:00:00.000'.</msg>
<status status="PASS" starttime="******** 07:49:18.921" endtime="******** 07:49:18.923"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAPTURED_CAMPAIGN_END_DATE</arg>
<arg>${campaign_required_end_date}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:49:18.923" level="INFO">Environment variable 'CAPTURED_CAMPAIGN_END_DATE' set to value '2024-10-14 00:00:00.000'.</msg>
<status status="PASS" starttime="******** 07:49:18.923" endtime="******** 07:49:18.923"/>
</kw>
<status status="PASS" starttime="******** 07:49:17.313" endtime="******** 07:49:18.923"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:20.923" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:49:18.923" endtime="******** 07:49:20.923"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAPTURED_CAMPAIGN_NAME</arg>
<arg>${CAMPAIGN_NAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:49:20.923" level="INFO">Environment variable 'CAPTURED_CAMPAIGN_NAME' set to value 'AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk'.</msg>
<status status="PASS" starttime="******** 07:49:20.923" endtime="******** 07:49:20.923"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>FilloutYourCampaign.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:49:21.094" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="FilloutYourCampaign.png"&gt;&lt;img src="FilloutYourCampaign.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:49:20.923" endtime="******** 07:49:21.094"/>
</kw>
<status status="PASS" starttime="******** 07:49:07.701" endtime="******** 07:49:21.094"/>
</kw>
<kw name="And The user clicks on Next button in Fill Out your Campaign screen" library="Navigation">
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:49:21.094" endtime="******** 07:49:21.347"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:21.347" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[3]/button[2]/span[1]'.</msg>
<status status="PASS" starttime="******** 07:49:21.347" endtime="******** 07:49:21.556"/>
</kw>
<status status="PASS" starttime="******** 07:49:21.094" endtime="******** 07:49:21.556"/>
</kw>
<kw name="And The user captures marketing screen information" library="UploadMarkrtingScreen">
<arg>${CAMPAIGN_LANGUAGE}</arg>
<arg>${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:21.557" level="INFO">${counter} = 0</msg>
<status status="PASS" starttime="******** 07:49:21.557" endtime="******** 07:49:21.557"/>
</kw>
<kw name="Split String" library="String">
<var>@{campaign_laguages}</var>
<arg>${CAMPAIGN_LANGUAGE}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 07:49:21.558" level="INFO">@{campaign_laguages} = [ English | Afrikaans ]</msg>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Split String" library="String">
<var>@{campaign_images}</var>
<arg>${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 07:49:21.558" level="INFO">@{campaign_images} = [ images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg | images\MarketingA_af_2.jpg ]</msg>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${campaign_laguages_length}</var>
<arg>${campaign_laguages}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 07:49:21.558" level="INFO">Length is 2</msg>
<msg timestamp="******** 07:49:21.558" level="INFO">${campaign_laguages_length} = 2</msg>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_laguages}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:21.558" level="INFO">['English', 'Afrikaans']</msg>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${campaign_images_length}</var>
<arg>${campaign_images}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 07:49:21.558" level="INFO">Length is 2</msg>
<msg timestamp="******** 07:49:21.558" level="INFO">${campaign_images_length} = 2</msg>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_images}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:49:21.558" level="INFO">['images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg', 'images\\MarketingA_af_2.jpg']</msg>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>@{campaign_laguages}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>@{campaign_images}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.558"/>
</kw>
<for flavor="IN">
<var>${language}</var>
<value>@{campaign_laguages}</value>
<iter>
<var name="${language}">English</var>
<kw name="Log To Console" library="BuiltIn">
<arg>Language is</arg>
<arg>:</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:21.561"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${current_image}</var>
<arg>''</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:21.561" level="INFO">${current_image} = ''</msg>
<status status="PASS" starttime="******** 07:49:21.561" endtime="******** 07:49:21.561"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<arg>Fail</arg>
<arg>Please provide an image path variable for '${language.strip()}' language!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:21.561" endtime="******** 07:49:21.562"/>
</kw>
<kw name="Exit For Loop If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<doc>Stops executing the enclosing FOR loop if the ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:21.562" endtime="******** 07:49:21.562"/>
</kw>
<kw name="Select from dropdown" library="Navigation">
<arg>${LANGUAGE_DROPDOWN}</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:21.562" endtime="******** 07:49:21.562"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:23.563" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:49:21.562" endtime="******** 07:49:23.563"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:23.563" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="language"]'.</msg>
<status status="PASS" starttime="******** 07:49:23.563" endtime="******** 07:49:23.647"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:28.648" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:49:23.647" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:28.648" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:28.648" level="INFO">${path_string} = //span[text()=' English </msg>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:28.648" level="INFO">${path_string} = //span[text()=' English ']</msg>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.648"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:49:28.648" endtime="******** 07:49:28.684"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:28.684" level="INFO">Clicking element 'xpath=//span[text()=' English ']'.</msg>
<status status="PASS" starttime="******** 07:49:28.684" endtime="******** 07:49:28.731"/>
</kw>
<status status="PASS" starttime="******** 07:49:21.562" endtime="******** 07:49:28.731"/>
</kw>
<kw name="Get Path" library="Utility">
<var>${image_directory}</var>
<msg timestamp="******** 07:49:28.731" level="INFO">${image_directory} = C:\Users\<USER>\source\repos\alternative_physical_channels</msg>
<status status="PASS" starttime="******** 07:49:28.731" endtime="******** 07:49:28.731"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${final_image_path}</var>
<arg>SEPARATOR=\\</arg>
<arg>${image_directory}</arg>
<arg>future_fit_architecture_portal</arg>
<arg>${campaign_images[${counter}].strip()}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:28.733" level="INFO">${final_image_path} = C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg</msg>
<status status="PASS" starttime="******** 07:49:28.731" endtime="******** 07:49:28.733"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Image path is : ${campaign_images[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:28.733" endtime="******** 07:49:28.735"/>
</kw>
<kw name="Choose File" library="SeleniumLibrary">
<arg>xpath://input[@value='select' and @class='ng-star-inserted']</arg>
<arg>${final_image_path}</arg>
<doc>Inputs the ``file_path`` into the file input field ``locator``.</doc>
<msg timestamp="******** 07:49:28.735" level="INFO">Sending C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg to browser.</msg>
<status status="PASS" starttime="******** 07:49:28.735" endtime="******** 07:49:28.771"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:31.797" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 07:49:28.772" endtime="******** 07:49:31.797"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>UploadMarkrtingScreen.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:49:31.949" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="UploadMarkrtingScreen.png"&gt;&lt;img src="UploadMarkrtingScreen.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:49:31.797" endtime="******** 07:49:31.949"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>${counter}+1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:31.949" level="INFO">${counter} = 0+1</msg>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:31.949"/>
</kw>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:31.949"/>
</iter>
<iter>
<var name="${language}">Afrikaans</var>
<kw name="Log To Console" library="BuiltIn">
<arg>Language is</arg>
<arg>:</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:31.949"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${current_image}</var>
<arg>''</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:31.949" level="INFO">${current_image} = ''</msg>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:31.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<arg>Fail</arg>
<arg>Please provide an image path variable for '${language.strip()}' language!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:31.949"/>
</kw>
<kw name="Exit For Loop If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<doc>Stops executing the enclosing FOR loop if the ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:31.949"/>
</kw>
<kw name="Select from dropdown" library="Navigation">
<arg>${LANGUAGE_DROPDOWN}</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:31.956"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:33.963" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:49:31.956" endtime="******** 07:49:33.963"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:33.969" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="language"]'.</msg>
<status status="PASS" starttime="******** 07:49:33.969" endtime="******** 07:49:34.364"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:39.364" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:49:34.364" endtime="******** 07:49:39.364"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:39.364" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 07:49:39.364" endtime="******** 07:49:39.364"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:39.364" endtime="******** 07:49:39.430"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:39.430" level="INFO">${path_string} = //span[text()=' Afrikaans </msg>
<status status="PASS" starttime="******** 07:49:39.430" endtime="******** 07:49:39.430"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:39.430" endtime="******** 07:49:39.434"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:39.434" level="INFO">${path_string} = //span[text()=' Afrikaans ']</msg>
<status status="PASS" starttime="******** 07:49:39.434" endtime="******** 07:49:39.434"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:39.434" endtime="******** 07:49:39.434"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 07:49:39.434" endtime="******** 07:49:39.671"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:39.671" level="INFO">Clicking element 'xpath=//span[text()=' Afrikaans ']'.</msg>
<status status="PASS" starttime="******** 07:49:39.671" endtime="******** 07:49:39.797"/>
</kw>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:39.797"/>
</kw>
<kw name="Get Path" library="Utility">
<var>${image_directory}</var>
<msg timestamp="******** 07:49:39.798" level="INFO">${image_directory} = C:\Users\<USER>\source\repos\alternative_physical_channels</msg>
<status status="PASS" starttime="******** 07:49:39.798" endtime="******** 07:49:39.798"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${final_image_path}</var>
<arg>SEPARATOR=\\</arg>
<arg>${image_directory}</arg>
<arg>future_fit_architecture_portal</arg>
<arg>${campaign_images[${counter}].strip()}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:49:39.799" level="INFO">${final_image_path} = C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\MarketingA_af_2.jpg</msg>
<status status="PASS" starttime="******** 07:49:39.798" endtime="******** 07:49:39.800"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Image path is : ${campaign_images[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:39.800" endtime="******** 07:49:39.833"/>
</kw>
<kw name="Choose File" library="SeleniumLibrary">
<arg>xpath://input[@value='select' and @class='ng-star-inserted']</arg>
<arg>${final_image_path}</arg>
<doc>Inputs the ``file_path`` into the file input field ``locator``.</doc>
<msg timestamp="******** 07:49:39.833" level="INFO">Sending C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\MarketingA_af_2.jpg to browser.</msg>
<status status="PASS" starttime="******** 07:49:39.833" endtime="******** 07:49:40.142"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:43.143" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 07:49:40.142" endtime="******** 07:49:43.143"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>UploadMarkrtingScreen.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:49:43.323" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="UploadMarkrtingScreen.png"&gt;&lt;img src="UploadMarkrtingScreen.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:49:43.143" endtime="******** 07:49:43.323"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>${counter}+1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:43.323" level="INFO">${counter} = 0+1+1</msg>
<status status="PASS" starttime="******** 07:49:43.323" endtime="******** 07:49:43.323"/>
</kw>
<status status="PASS" starttime="******** 07:49:31.949" endtime="******** 07:49:43.323"/>
</iter>
<status status="PASS" starttime="******** 07:49:21.558" endtime="******** 07:49:43.323"/>
</for>
<status status="PASS" starttime="******** 07:49:21.557" endtime="******** 07:49:43.323"/>
</kw>
<kw name="And The user saves the campaign" library="UploadMarkrtingScreen">
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 07:49:43.323" endtime="******** 07:49:43.339"/>
</kw>
<status status="PASS" starttime="******** 07:49:43.323" endtime="******** 07:49:43.339"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAPTURE_CAMPAIGN_SAVE_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:49:43.339" level="INFO">Clicking element 'xpath=//span[text()='Save']/parent::button'.</msg>
<status status="PASS" starttime="******** 07:49:43.339" endtime="******** 07:49:43.676"/>
</kw>
<status status="PASS" starttime="******** 07:49:43.323" endtime="******** 07:49:43.676"/>
</kw>
<kw name="Then The captured campaign must exist on the database" library="UploadMarkrtingScreen">
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${campaign_captured_name}</var>
<arg>CAPTURED_CAMPAIGN_NAME</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:49:43.676" level="INFO">${campaign_captured_name} = AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk</msg>
<status status="PASS" starttime="******** 07:49:43.676" endtime="******** 07:49:43.676"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${campaign_start_date}</var>
<arg>CAPTURED_CAMPAIGN_START_DATE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:49:43.678" level="INFO">${campaign_start_date} = 2024-10-12 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:43.676" endtime="******** 07:49:43.678"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${campaign_end_date}</var>
<arg>CAPTURED_CAMPAIGN_END_DATE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:49:43.678" level="INFO">${campaign_end_date} = 2024-10-14 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:43.678" endtime="******** 07:49:43.678"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Name to verify on the DB is: ${campaign_captured_name}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:43.678" level="INFO">Campaign Name to verify on the DB is: AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk</msg>
<status status="PASS" starttime="******** 07:49:43.678" endtime="******** 07:49:43.678"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Start Date is: ${campaign_start_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:43.678" level="INFO">Campaign Start Date is: 2024-10-12 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:43.678" endtime="******** 07:49:43.678"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign End Date is: ${campaign_end_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:43.678" level="INFO">Campaign End Date is: 2024-10-14 00:00:00.000</msg>
<status status="PASS" starttime="******** 07:49:43.678" endtime="******** 07:49:43.678"/>
</kw>
<kw name="Replace String" library="String">
<var>${modified_campaign_start_date_one}</var>
<arg>${campaign_start_date}</arg>
<arg>.000</arg>
<arg>${SPACE}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 07:49:43.680" level="INFO">${modified_campaign_start_date_one} = 2024-10-12 00:00:00 </msg>
<status status="PASS" starttime="******** 07:49:43.680" endtime="******** 07:49:43.680"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${modified_campaign_start_date}</var>
<arg>${modified_campaign_start_date_one.strip()}</arg>
<arg>exclude_millis=yes</arg>
<arg>date_format=%Y-%m-%d %H:%M:%S</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:49:43.680" level="INFO">${modified_campaign_start_date} = 2024-10-12 00:00:00</msg>
<status status="PASS" starttime="******** 07:49:43.680" endtime="******** 07:49:43.680"/>
</kw>
<kw name="Replace String" library="String">
<var>${modified_campaign_end_date_one}</var>
<arg>${campaign_end_date}</arg>
<arg>.000</arg>
<arg>${SPACE}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 07:49:43.682" level="INFO">${modified_campaign_end_date_one} = 2024-10-14 00:00:00 </msg>
<status status="PASS" starttime="******** 07:49:43.682" endtime="******** 07:49:43.682"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${modified_campaign_end_date}</var>
<arg>${modified_campaign_end_date_one.strip()}</arg>
<arg>exclude_millis=yes</arg>
<arg>date_format=%Y-%m-%d %H:%M:%S</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:49:43.683" level="INFO">${modified_campaign_end_date} = 2024-10-14 00:00:00</msg>
<status status="PASS" starttime="******** 07:49:43.682" endtime="******** 07:49:43.683"/>
</kw>
<kw name="Replace String" library="String">
<var>${modified_campaign_end_date}</var>
<arg>${modified_campaign_end_date}</arg>
<arg>00:00:00</arg>
<arg>23:59:59</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 07:49:43.683" level="INFO">${modified_campaign_end_date} = 2024-10-14 23:59:59</msg>
<status status="PASS" starttime="******** 07:49:43.683" endtime="******** 07:49:43.683"/>
</kw>
<kw name="Replace String" library="String">
<var>${sql_query}</var>
<arg>${CAPTURED_CAMPAIGN_QUERY}</arg>
<arg>camp_name</arg>
<arg>${campaign_captured_name}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 07:49:43.684" level="INFO">${sql_query} = SELECT * FROM ATM_Marketing.Campaign where campaignName = 'AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk'</msg>
<status status="PASS" starttime="******** 07:49:43.684" endtime="******** 07:49:43.684"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${sql_query}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:49:43.684" endtime="******** 07:49:43.686"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>8s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:51.687" level="INFO">Slept 8 seconds</msg>
<status status="PASS" starttime="******** 07:49:43.686" endtime="******** 07:49:51.687"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 07:49:51.688" level="INFO">${db_type} = 'MYSQL'</msg>
<status status="PASS" starttime="******** 07:49:51.688" endtime="******** 07:49:51.688"/>
</kw>
<kw name="Execute SQL Query" library="DBUtility">
<var>${data_base_campaigns}</var>
<arg>${db_type}</arg>
<arg>${sql_query}</arg>
<arg>True</arg>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="******** 07:49:51.688" level="INFO">${return_data} = True</msg>
<status status="PASS" starttime="******** 07:49:51.688" endtime="******** 07:49:51.688"/>
</kw>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="******** 07:49:51.688" level="INFO">${return_all} = False</msg>
<status status="PASS" starttime="******** 07:49:51.688" endtime="******** 07:49:51.688"/>
</kw>
<kw name="Verify Data Using Database" library="DatabaseUtility">
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<msg timestamp="******** 07:49:52.311" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
2 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg timestamp="******** 07:49:52.311" level="INFO">${data_found} = {'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-14 23:59:59', 'campaignHistoryId': 1, 'campaignId': 'CNQ068v001Q42024', 'campaignName': 'AUTOMATION Captured - SIT - ATM_S11782...</msg>
<status status="PASS" starttime="******** 07:49:51.688" endtime="******** 07:49:52.311"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 07:49:52.311" endtime="******** 07:49:52.311"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 07:49:52.311" endtime="******** 07:49:52.313"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 07:49:52.313" endtime="******** 07:49:52.314"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" starttime="******** 07:49:52.314" endtime="******** 07:49:52.314"/>
</return>
<msg timestamp="******** 07:49:52.314" level="INFO">${data_base_campaigns} = {'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-14 23:59:59', 'campaignHistoryId': 1, 'campaignId': 'CNQ068v001Q42024', 'campaignName': 'AUTOMATION Captured - SIT - ATM_S11782...</msg>
<status status="PASS" starttime="******** 07:49:51.688" endtime="******** 07:49:52.314"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_campaign_name}</var>
<arg>${data_base_campaigns}</arg>
<arg>campaignName</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 07:49:52.315" level="INFO">${db_campaign_name} = AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk</msg>
<status status="PASS" starttime="******** 07:49:52.314" endtime="******** 07:49:52.316"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_campaign_start_date}</var>
<arg>${data_base_campaigns}</arg>
<arg>campaignStartDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 07:49:52.316" level="INFO">${db_campaign_start_date} = 2024-10-12 00:00:00</msg>
<status status="PASS" starttime="******** 07:49:52.316" endtime="******** 07:49:52.316"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_campaign_end_date}</var>
<arg>${data_base_campaigns}</arg>
<arg>campaignEndDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 07:49:52.316" level="INFO">${db_campaign_end_date} = 2024-10-14 23:59:59</msg>
<status status="PASS" starttime="******** 07:49:52.316" endtime="******** 07:49:52.316"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_is_approved}</var>
<arg>${data_base_campaigns}</arg>
<arg>isApproved</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 07:49:52.316" level="INFO">${db_is_approved} = 0</msg>
<status status="PASS" starttime="******** 07:49:52.316" endtime="******** 07:49:52.316"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_is_isactive}</var>
<arg>${data_base_campaigns}</arg>
<arg>isActive</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 07:49:52.316" level="INFO">${db_is_isactive} = 1</msg>
<status status="PASS" starttime="******** 07:49:52.316" endtime="******** 07:49:52.316"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Name from the DB is:</arg>
<arg>${db_campaign_name}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:52.316" level="INFO">Campaign Name from the DB is:</msg>
<msg timestamp="******** 07:49:52.316" level="INFO">AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk</msg>
<status status="PASS" starttime="******** 07:49:52.316" endtime="******** 07:49:52.316"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Start Date from the DB is: ${db_campaign_start_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:52.318" level="INFO">Campaign Start Date from the DB is: 2024-10-12 00:00:00</msg>
<status status="PASS" starttime="******** 07:49:52.316" endtime="******** 07:49:52.318"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign End Date from the DB is:</arg>
<arg>${db_campaign_end_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:52.318" level="INFO">Campaign End Date from the DB is:</msg>
<msg timestamp="******** 07:49:52.318" level="INFO">2024-10-14 23:59:59</msg>
<status status="PASS" starttime="******** 07:49:52.318" endtime="******** 07:49:52.318"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Active Status from the DB is:</arg>
<arg>${db_is_isactive}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:52.318" level="INFO">Campaign Active Status from the DB is:</msg>
<msg timestamp="******** 07:49:52.318" level="INFO">1</msg>
<status status="PASS" starttime="******** 07:49:52.318" endtime="******** 07:49:52.318"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Approved Status from the DB is:</arg>
<arg>${db_is_approved}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:52.319" level="INFO">Campaign Approved Status from the DB is:</msg>
<msg timestamp="******** 07:49:52.319" level="INFO">0</msg>
<status status="PASS" starttime="******** 07:49:52.319" endtime="******** 07:49:52.319"/>
</kw>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${campaign_captured_name}</arg>
<arg>${db_campaign_name}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="******** 07:49:52.319" endtime="******** 07:49:52.319"/>
</kw>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${modified_campaign_start_date}</arg>
<arg>${db_campaign_start_date}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="******** 07:49:52.319" endtime="******** 07:49:52.319"/>
</kw>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${modified_campaign_end_date}</arg>
<arg>${db_campaign_end_date}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="******** 07:49:52.319" endtime="******** 07:49:52.319"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${db_is_isactive}' != '1'</arg>
<arg>Fail</arg>
<arg>The campaign named '${campaign_captured_name}' is not active on the Database</arg>
<arg>ELSE IF</arg>
<arg>'${db_is_approved}' != '0'</arg>
<arg>Fail</arg>
<arg>The campaign named '${campaign_captured_name}' is active and also approved on the Database after it was captured.</arg>
<arg>ELSE</arg>
<arg>Log Many</arg>
<arg>The campaign named '${campaign_captured_name}', was found on the database. The start date of the campaign is</arg>
<arg>'${db_campaign_start_date}' and the end date is '${db_campaign_end_date}'.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log Many" library="BuiltIn">
<arg>The campaign named '${campaign_captured_name}', was found on the database. The start date of the campaign is</arg>
<arg>'${db_campaign_start_date}' and the end date is '${db_campaign_end_date}'.</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 07:49:52.321" level="INFO">The campaign named 'AUTOMATION Captured - SIT - ATM_S11782 - 2 image_Thabo Stk', was found on the database. The start date of the campaign is</msg>
<msg timestamp="******** 07:49:52.321" level="INFO">'2024-10-12 00:00:00' and the end date is '2024-10-14 23:59:59'.</msg>
<status status="PASS" starttime="******** 07:49:52.321" endtime="******** 07:49:52.321"/>
</kw>
<status status="PASS" starttime="******** 07:49:52.321" endtime="******** 07:49:52.321"/>
</kw>
<status status="PASS" starttime="******** 07:49:43.676" endtime="******** 07:49:52.321"/>
</kw>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:49:52.321"/>
</kw>
<kw name="End Web Test" library="Login" type="TEARDOWN">
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:49:57.322" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:49:52.321" endtime="******** 07:49:57.322"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="******** 07:49:57.322" endtime="******** 07:49:59.846"/>
</kw>
<status status="PASS" starttime="******** 07:49:52.321" endtime="******** 07:49:59.847"/>
</kw>
<doc>Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans</doc>
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 07:48:16.153" endtime="******** 07:49:59.847"/>
</test>
<doc>Create Campaign page</doc>
<status status="PASS" starttime="******** 07:48:15.396" endtime="******** 07:49:59.849"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 07:48:34.213" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:48:44.241" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:48:49.256" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
