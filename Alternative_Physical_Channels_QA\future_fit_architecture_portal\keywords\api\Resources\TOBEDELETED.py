import os
import sys
from datetime import datetime, timedelta
from robot.api.deco import keyword, library
from jsonpath_ng import parse
import numbers
import base64
import json
import hashlib
import hmac




# import common_utilities.CommonUtils


def find_project_root(start_path, marker='data'):
    print(start_path)
    current_path = start_path
    print(os.path.dirname(current_path))

    while current_path != os.path.dirname(current_path):  # Check if we are at the root
        if marker in os.listdir(current_path):
            return current_path
        current_path = os.path.dirname(current_path)
    return None


def get_variable_data_type(str_var):
    data_type = type(str_var)
    # <class 'dict'>
    data_type = str(data_type).replace('<class \'', '')
    data_type = data_type.replace('\'>', '')
    # print(data_type)
    return data_type


def get_current_date_time(str_num_of_days):
    global upload_date_field_data

    # Verify if the provided days are in correct format
    negating_char = ''
    num_of_days = ''
    if str(str_num_of_days).startswith('-'):
        negating_char = '-'
        num_of_days = str(str_num_of_days).replace("-", "")
        num_of_days = num_of_days.strip()
    else:
        num_of_days = str(str_num_of_days).strip()

    num_of_days = set_data_type_to_integer(num_of_days)

    value_is_int = isinstance(num_of_days, numbers.Integral)

    if not value_is_int:
        num_of_days = '0'

    num_of_days = str(num_of_days)

    num_of_days = negating_char + num_of_days
    num_of_days = num_of_days.strip()

    num_of_days = set_data_type_to_integer(num_of_days)

    # current date and time
    now = datetime.now()
    date2 = now + timedelta(days=num_of_days)
    date_array = str(date2).split(' ')
    json_date = date_array[0] + 'T' + date_array[1][:-3] + 'Z'
    upload_date_field_data = json_date
    return str(json_date)


def field_is_boolean_type(key):
    boolean_fields = ['isActive', 'isApproved', 'isTargetted', 'isTargetRegion', 'isUpaloadSuccessful']
    # check whether the key is in boolean_fields
    return len(list(filter(lambda x: x == key, boolean_fields))) > 0


def field_is_integer_type(key):
    integer_fields = ['marketingChannelId', 'id', 'screenId', 'duration', 'priority', 'campaignId', 'approvalId']
    # check whether the key is in boolean_fields
    return len(list(filter(lambda x: x == key, integer_fields))) > 0


def field_is_float_type(key):
    float_fields = ['version']
    # check whether the key is in boolean_fields
    return len(list(filter(lambda x: x == key, float_fields))) > 0


def is_variable_an_image(variable):
    global image_data
    variable_content = variable.lower()

    if variable_content.endswith('.jpg') or variable_content.endswith('.png') or variable_content.endswith('.gif'):
        image_data = "data:image/" + variable_content[-3:] + ";base64,"
        return True
    else:
        return False


def convert_image_to_base_64(image_file_path):
    root = 'future_fit_architecture_portal'
    print('path', root)
    full_file_path = os.path.join(root, image_file_path)
    with open(full_file_path, 'rb') as image_file:
        base64_bytes = base64.b64encode(image_file.read())
        base64_string = base64_bytes.decode()
        return base64_string


def json_object_is_an_array(str_variable):
    if not str_variable.isalnum():  # Check if all the characters in the text are not alphanumeric
        return True
    else:
        return False


def set_data_type_to_boolean(variable):
    if variable == "True":
        value = True
    else:
        value = False
    return value


def set_data_type_to_integer(variable):
    return int(variable)


def set_data_type_to_float(variable):
    return float(variable)


@library(scope='GLOBAL', auto_keywords=True)
class CreateRestPayloads:

    @keyword
    def populate_json_file_with(self, json_file_path, **kwargs):

        payload = self.load_json_file(json_file_path)

        if payload is None:
            pass
        else:
            with open(json_file_path, 'r', encoding='utf-8') as json_file:
                payload = json.load(json_file)

                for key, value in kwargs.items():
                    value = str(value).strip()
                    print("Key is '" + str(key) + "' and Value is '" + value + "'")

                    # Check if the value provided is an image path
                    if is_variable_an_image(value):
                        # Generate the image signature
                        temp_value = image_data + convert_image_to_base_64(value)
                        signature = self.generate_signature(temp_value)

                        value = temp_value + ',' + signature

                    # Check if the field value's data type must be changed from string
                    if field_is_boolean_type(key):
                        value = set_data_type_to_boolean(value)
                    elif field_is_integer_type(key):
                        value = set_data_type_to_integer(value)
                    elif (not get_variable_data_type(value) == 'str') and field_is_float_type(key):
                        value = set_data_type_to_float(value)

                    # Modify Key data if required
                    if json_object_is_an_array(key):
                        key = key.replace(":", ".")

                    if key == 'campaignStartDate' or key == 'campaignEndDate' or key == 'approvalTime':
                        value = get_current_date_time(value)
                    elif key == 'updatedDate' or key == 'requestDate' or key == 'approvalTime':  # or key == "uploadDate":
                        value = get_current_date_time('0')

                    if key == "uploadDate":
                        upload_date_field_data = value

                    key = str('$.' + key)
                    jsonpath_expr = parse(key)
                    jsonpath_expr.find(payload)
                    jsonpath_expr.update(payload, value)

            with open(json_file_path, 'w', encoding='utf-8') as json_file:
                json.dump(payload, json_file, indent=4)
                print('JSON file updated successfully')

    @keyword
    def load_json_file(self, json_file_path):
        with open(json_file_path, 'r', encoding='utf-8') as json_file:
            try:
                json_payload = json.load(json_file)
                print("Json Loaded")
                return json_payload
            except ValueError:
                print("Json file not Loaded")
                return None

    @keyword
    def get_base_url(self, environment):
        match environment.upper():
            case 'DEV':
                return "https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za"
            case 'UAT':
                return "https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za"
            case 'SIT':
                return "https://atm-marketing.atm-marketing-sit.rbb-banking.sdc-nonprod.caas.absa.co.za"

            # If an exact match is not confirmed, this last case will be used if provided
            case _:
                return "Environment value " + environment + " is not recognised. Please use the correct values."

    @keyword
    def get_service_path(self, service, path_id=None):
        match service.upper():
            case 'APPROVAL/APPROVE':
                if path_id is None:
                    return "/Approval/Approve"
                else:
                    return "/Approval/" + path_id
            case 'ATMDATA':
                return "/ATMData"
            case 'ATMMARKETING':
                return "/ATMMarketing"
            case 'ATMMARKETINGCAMPAIGN':
                if path_id is None:
                    return "/ATMMarketingCampaign"
                else:
                    return "/ATMMarketingCampaign/" + path_id
            case 'ATMMARKETINGRESULT':
                if path_id is None:
                    return "/ATMMarketingResult"
                else:
                    return "/ATMMarketingResult/" + path_id
            case 'CAMPAIGNLOOKUP':
                if path_id is None:
                    return "/CampaignLookup"
                else:
                    return "/CampaignLookup/" + path_id
            case 'DASHBOARD':
                return "/Dashboard"
            case 'GASPERDETAILS':
                if path_id is None:
                    return "/GasperDetails"
                else:
                    return "/GasperDetails/" + path_id
            case 'WEATHERFORECAST':
                return "/WeatherForecast"

            # If an exact match is not confirmed, this last case will be used.
            case _:
                return "The service path " + service + " is not recognised. Please use the correct value."

    @keyword
    def get_rest_api_headers(self, bearer_token):
        headers = {"Accept": "application/json",
                   "Content-Type": "application/json",
                   "Connection": "keep-alive",
                   "Accept-Encoding": "gzip, deflate, br",
                   "Authorization": bearer_token,
                   "Accept": "*/*"}
        return headers

    @keyword
    def value_exists_on_json_response(self, json_key, json_value, str_response=None):

        if str_response is None:
            print("Please provide the response that must be read!")
            return False
        else:

            if json_key == "uploadDate":
                json_value = upload_date_field_data

            # convert string to  object
            data = json.loads(str_response)
            modified_json_key = ''

            # Modify Key data if required
            if json_object_is_an_array(json_key):
                modified_json_key = json_key.replace(":", ".")
            else:
                modified_json_key = json_key

            modified_json_key = str('$.' + modified_json_key)
            jsonpath_expr = parse(modified_json_key)

            for match in jsonpath_expr.find(data):
                if str(match.value).strip() == str(json_value).strip():
                    print(f'Field: {json_key}', f', having a value: {json_value} was found.')
                    return True

            print(f'Field: {json_key}', f', having a value: {json_value} was NOT found!', file=sys.stderr)
            return False

    @keyword
    def generate_signature(self, message):
        keyword_instance = CommonUtils()
        key = keyword_instance.read_config_property('BUSINESS_USER_KEY')
        # print('Key value is ', key)
        hash_message = hashlib.sha256(message.encode()).digest()
        signature = hmac.new(key.encode(), hash_message, hashlib.sha256).digest()
        # print('Generated Signature ',base64.b64encode(signature).decode())
        return base64.b64encode(signature).decode()

    @keyword
    def create_base64_image_string(self, image_name):

        if is_variable_an_image(image_name):
            temp_value = image_data + convert_image_to_base_64(image_name)
            signature = self.generate_signature(temp_value)
            return temp_value + ',' + signature
        return ""

    @keyword
    def convert_to_kwargs(self, **kwargs):
        return kwargs

    @keyword
    def get_timestamp_with_milliseconds(self):
        now = datetime.now()
        return str(now.strftime('%Y-%m-%dT%H:%M:%S.%f'))  # [:-3]

    @keyword
    def is_last_char_zero(self, string):
        if string and string[-1] == '0':
            return True
        return False
