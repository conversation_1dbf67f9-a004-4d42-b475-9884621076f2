*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/controllers/DownloadBinTable_Keywords.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Download Bin Tables Negative Tests
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${DEVICE_NAME}  ${DEVICE_VERSION_NUMBER}   ${EXPECTED_STATUS_CODE}   ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to download bin table        ${BASE_URL}      ${DEVICE_NAME}        ${DEVICE_VERSION_NUMBER}
    When The service returns an expected status code                ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed               ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                                   |        *DOCUMENTATION*    		                          |         *BASE_URL*                  |     *DEVICE_NAME*      | *DEVICE_VERSION_NUMBER*     |   *EXPECTED_STATUS_CODE*   |   *EXPECTED_ERROR_MESSAGE*            |
| Download Bin Tables without specifying the 'devicename' parameter.           | Download Bin Tables Negative Tests    | Verify the downloaded bin types against the seed file.   |                                     |                        |          1                  |        400                 |   The devicename field is required.   |
| Download Bin Tables without specifying the 'deviceVersionNumber' parameter.  | Download Bin Tables Negative Tests    | Verify the downloaded bin types against the seed file.   |                                     |       S11782           |                             |        400                 |   The value '' is invalid.            |
