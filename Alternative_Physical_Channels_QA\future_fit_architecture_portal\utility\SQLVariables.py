SQL_GET_ATM_MARKETING_CAMPAIGN_TARGETED = "SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID,CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate,CH.updateDescription,CH.approvalId,C.updatedBy as campaignUpdatedBy,MS.id as marketingScreenID,MS.screenType,MS.screenNumber,MS.channelId as marketingScreenChannelID,MC.id as marketingChannelID,MC.channel as marketingChannel,MC.imageResolution as marketingChannelImageResolution,C.campaignStartDate,C.campaignEndDate,C.isActive,C.isApproved,C.version,C.isTargetted,ATMT.atmNumber, ATMT.region  FROM Campaign C Inner Join CampaignHistory CH On C.ID = CH.campaignID Inner Join MarketingScreen MS On C.marketingScreenId = MS.ID Inner Join MarketingChannel MC On C.channel = MC.id Inner Join ATMTarget ATMT On C.id = ATMT.campaignId where C.ID = campaign_ID and CH.updatedDate = 'campaign_update_date'"
SQL_GET_CAMPAIGN_TARGET_DATA = "Select count(*) as total_targets from ATMTarget where campaignId = campaign_ID"
SQL_GET_ATM_MARKETING_CAMPAIGN_UN_TARGETED = "SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID,CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate,CH.updateDescription,CH.approvalId,C.updatedBy as campaignUpdatedBy,MS.id as marketingScreenID,MS.screenType,MS.screenNumber,MS.channelId as marketingScreenChannelID,MC.id as marketingChannelID,MC.channel as marketingChannel,MC.imageResolution as marketingChannelImageResolution,C.campaignStartDate,C.campaignEndDate,C.isActive,C.isApproved,C.version,C.isTargetted  FROM Campaign C Inner Join CampaignHistory CH On C.ID = CH.campaignID Inner Join MarketingScreen MS On C.marketingScreenId = MS.ID Inner Join MarketingChannel MC On C.channel = MC.id where C.ID = campaign_ID and CH.updatedDate = 'campaign_update_date'"
SQL_GET_GASPER_DETAILS = "SELECT * FROM [MAIN_UAT].[dbo].[gasper_details] Where ID = atm_id"
SQL_GET_MARKETING_CHANNELS = "Select * from MarketingChannel where id = ID"
SQL_GET_MARKETING_SCREENS = "Select * from MarketingScreen where id = ID"
SQL_GET_MARKETING_LANGUAGES = "SELECT * FROM ATM_Marketing.Language where id = ID"
SQL_GET_MARKETING_SCHEDULE_AND_CYCLE_DETAILS = "SELECT MS.id as marketineScheduleID, MS.cycle as marketineScheduleCycle, MS.scheduleVersion,MS.isCurrentVersion,MS.updatedBy,MS.updateDate, MS.updateDescription,MS.approvalId,C.id as cycleID,C.cycleStart, C.cycleEnd, C.interval FROM MarketingSchedule MS Inner Join Cycle C On MS.cycle = C.id where scheduleVersion = sch_version"
SQL_GET_ATM_MARKETING_TARGETED = "SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID, CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate, CH.updateDescription,CH.approvalId,C.updatedBy as campaignUpdatedBy,MS.id as marketingScreenID, MS.screenType,MS.screenNumber,MS.channelId as marketingScreenChannelID, MC.id as marketingChannelID, MC.channel as marketingChannel,MC.imageResolution as marketingChannelImageResolution, C.campaignStartDate,C.campaignEndDate, MI.id as imageListID, MI.imageName, MI.languageId,Lang.language,Lang.languageCode ,MI.duration, MI.priority,MI.marketingImage,C.isActive,C.isApproved,C.version,C.isTargetted, ATMT.atmNumber, ATMT.region  FROM Campaign C Inner Join CampaignHistory CH On C.ID = CH.campaignID Inner Join MarketingScreen MS On C.marketingScreenId = MS.ID Inner Join MarketingChannel MC On C.channel = MC.id Inner Join MarketingImage MI On C.id = MI.campaignId Inner Join Language Lang on MI.languageId = Lang.id Inner Join ATMTarget ATMT On C.id = ATMT.campaignId  where C.id = campaign_ID and CH.id = campaign_Hist_ID and MI.imageName = img_name"
SQL_GET_ATM_MARKETING_UN_TARGETED = "SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID, CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate, CH.updateDescription,CH.approvalId,C.updatedBy as campaignUpdatedBy,MS.id as marketingScreenID, MS.screenType,MS.screenNumber,MS.channelId as marketingScreenChannelID, MC.id as marketingChannelID, MC.channel as marketingChannel,MC.imageResolution as marketingChannelImageResolution, C.campaignStartDate, C.campaignEndDate, MI.id as imageListID, MI.imageName, MI.languageId,Lang.language,Lang.languageCode ,MI.duration, MI.priority,MI.marketingImage,C.isActive,C.isApproved,C.version,C.isTargetted FROM Campaign C  Inner Join CampaignHistory CH On C.ID = CH.campaignID Inner Join MarketingScreen MS On C.marketingScreenId = MS.ID Inner Join MarketingChannel MC On C.channel = MC.id Inner Join MarketingImage MI On C.id = MI.campaignId Inner Join Language Lang on MI.languageId = Lang.id where C.id = campaign_ID and CH.id = campaign_Hist_ID and MI.imageName = img_name"
SQL_GET_ATM_MARKETING_RESULTS = "SELECT * FROM ATM_Marketing.MarketingResult where deviceId = atm_number and uploadDate = up_date"
SQL_GET_ATM_MARKETING_CAMPAIGN_HISTORY = "SELECT * FROM ATM_Marketing.CampaignHistory where campaignId=campaign_ID order by id desc"
SQL_GET_ATM_MARKETING_CAMPAIGN_DETAILS_BASED_ON_HISTORY = "SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID,CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate,CH.updateDescription,CH.approvalId,C.updatedBy as campaignUpdatedBy,MS.id as marketingScreenID,MS.screenType,MS.screenNumber,MS.channelId as marketingScreenChannelID, MC.id as marketingChannelID, MC.channel as marketingChannel,MC.imageResolution as marketingChannelImageResolution, C.campaignStartDate,C.campaignEndDate, MI.id as imageListID, MI.imageName, MI.languageId,Lang.language,Lang.languageCode ,MI.duration, MI.priority,MI.marketingImage,C.isActive,C.isApproved,C.version,C.isTargetted FROM Campaign C  Inner Join CampaignHistory CH On C.ID = CH.campaignID Inner Join MarketingScreen MS On C.marketingScreenId = MS.ID Inner Join MarketingChannel MC On C.channel = MC.id Inner Join MarketingImage MI On C.id = MI.campaignId Inner Join Language Lang on MI.languageId = Lang.id where C.id = campaign_ID and CH.id = campaign_history_id	order by CH.id desc"
SQL_GET_MARKETING_LANGUAGES_USING_LANGUAGE_NAME = "SELECT * FROM ATM_Marketing.Language where language = L_ANG"
