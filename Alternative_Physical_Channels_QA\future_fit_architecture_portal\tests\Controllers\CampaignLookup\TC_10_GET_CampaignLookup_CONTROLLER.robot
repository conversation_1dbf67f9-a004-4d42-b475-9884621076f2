*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFT_HEALTHCHECK CRITICAL REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot

#Run the script
#robot -d reports/controllers tests/TC_01_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No
${BROWSER}                  chrome



*** Keywords ***
CampaignLookUp
    [Arguments]        ${DOCUMENTATION}     ${DATA_FILE}  ${BASE_URL}     ${SERVICE_PATH}   ${SERVICE_PATH_ID}   ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user creates a rest session                               ${BASE_URL}
    When The user makes Get Rest Call                                   ${SERVICE_PATH}     ${SERVICE_PATH_ID}     ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                                ${EXPECTED_STATUS_CODE}  ${JSON_RESPONSE_REASON}
    Then The field value(s) returned by the GET CampaignLookup controller must correspond to the APC Database

| *** Test Cases ***                                                                                                                      | *DOCUMENTATION*                                          | *DATA_FILE* | *BASE_URL*                    | *SERVICE_PATH*     | *SERVICE_PATH_ID*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON*          |
| FFT - Controllers - Verify the details of GET CampaignLookUp Controller against the Database            | CampaignLookUp         |  Gets CampaignLookUp Result using a Business User auth   |             | APC_API_UAT_BASE_URL          | CAMPAIGNLOOKUP     |                                      | 200                              | OK                              |
