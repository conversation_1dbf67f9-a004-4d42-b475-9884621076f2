<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2025-04-01T09:24:37.145621" rpa="false" schemaversion="5">
<suite id="s1" name="Robot Interactive Console" source="c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robotframework_interactive\robot_interactive_console.robot">
<test id="s1-t1" name="Default Task/Test" line="5">
<kw name="Interpreter Main Loop" owner="MainLoop">
<status status="PASS" start="2025-04-01T09:24:37.161354" elapsed="2.255455"/>
</kw>
<status status="PASS" start="2025-04-01T09:24:37.161354" elapsed="2.256904"/>
</test>
<status status="PASS" start="2025-04-01T09:24:37.147624" elapsed="2.270634"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Robot Interactive Console">Robot Interactive Console</stat>
</suite>
</statistics>
<errors>
<msg time="2025-04-01T09:24:37.138204" level="WARN">Error in file 'c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robotframework_interactive\robot_interactive_console.robot' on line 4: Singular section headers like '*** Test Case ***' are deprecated. Use plural format like '*** Test Cases ***' instead.</msg>
</errors>
</robot>
