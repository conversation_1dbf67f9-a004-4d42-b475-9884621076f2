SQL_GET_BIN_DETAILS_AND_LINKED_BIN_TYPES_DETAILS = "SELECT BT.BinId, Bin.Number, Bin.ActionDate, BT.BinTypeId, BT.BinTypeName FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BT On BT.BinId = Bin.Id Inner Join BinDbs.BinActionTrackers BA On BA.BinId = Bin.Id Inner Join BinDbs.BinVersions BV On BV.Id = Bin.BinVersionId Where Bin.Number = 'bin_number'"
SQL_GET_CREATED_BIN_DETAILS_AND_LINKED_BIN_TYPES_DETAILS = "SELECT BA.BinId,B.Status, B.Number as Bin_Number,BBT.BinTypeId,BBT.BinTypeName,BA.ActionType,BA.ToBeActionedBy,BA.ReviewedBy,BA.ReviewedDate,BA.RejectionComment, B.ActionDate, B.CreatedBy,B.CreatedDate,B.LastModifiedBy,B.LastModifiedDate , B.<PERSON>,B.<PERSON>eted<PERSON>,B.<PERSON>,BA.ServerNumber  FROM BinDbs.Bins B Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.Id Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where B.Number in (bin_numbers) Order by BA.ReviewedDate desc"
SQL_GET_BIN_DETAILS_BASED_ON_BIN_TYPE_NAME = "SELECT BA.BinId,B.Status, B.Number as Bin_Number,BBT.BinTypeId,BBT.BinTypeName,BA.ActionType,BA.ToBeActionedBy,BA.ReviewedBy,BA.ReviewedDate,BA.RejectionComment, B.ActionDate, B.CreatedBy,B.CreatedDate,B.LastModifiedBy,B.LastModifiedDate , B.DeletedBy,B.DeletedDate,B.IsDeleted,BA.ServerNumber  FROM BinDbs.Bins B Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.Id Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where BBT.BinTypeName = 'bin_type_name' and B.Number = 'bin_number' Order by BA.ReviewedDate desc"
SQL_GET_BIN_DETAILS_BASED_ON_BIN_NUMBER = "SELECT BA.BinId,B.Status, B.Number as Bin_Number,BBT.BinTypeId,BBT.BinTypeName,BA.ActionType,BA.ToBeActionedBy,BA.ReviewedBy,BA.ReviewedDate,BA.RejectionComment, B.ActionDate, B.CreatedBy,B.CreatedDate,B.LastModifiedBy,B.LastModifiedDate , B.DeletedBy,B.DeletedDate,B.IsDeleted,BA.ServerNumber  FROM BinDbs.Bins B Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.Id Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where B.Number = 'bin_number' Order by BA.ReviewedDate desc"
SQL_GET_BIN_DETAILS_BASED_ON_BIN_TYPE_NAME_N_OUTCOME = "SELECT BA.BinId,B.Status, B.Number as Bin_Number,BBT.BinTypeId,BBT.BinTypeName,BA.ActionType,BA.ToBeActionedBy,BA.ReviewedBy,BA.ReviewedDate,BA.RejectionComment, B.ActionDate, B.CreatedBy,B.CreatedDate,B.LastModifiedBy,B.LastModifiedDate , B.DeletedBy,B.DeletedDate,B.IsDeleted,BA.ServerNumber  FROM BinDbs.Bins B Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.Id Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where BBT.BinTypeName = 'bin_type_name' and B.Number = 'bin_number' and BA.ActionType = 'bin_outcome' Order by BA.ReviewedDate desc"
SQL_GET_BINS_TO_REVIEW = "SELECT Distinct Bin.Number as BinNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (1,2) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0';"
SQL_GET_BIN_TO_APPROVE = "SELECT Distinct Bin.Number as BinNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (1) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0' ORDER BY RAND() Limit 1;"
SQL_GET_BINS_IDS_TO_REVIEW = "SELECT Distinct Bin.Id as binId, Bin.Number as binNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (1,2) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0';"
SQL_GET_BINS_IDS_APPROVED = "SELECT Distinct Bin.Id as binId, Bin.Number as binNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (0) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0' ORDER BY RAND() Limit 1;"
SQL_GET_BINS_IDS_PENDING = "SELECT Distinct Bin.Id as binId, Bin.Number as binNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (1) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0' ORDER BY RAND() Limit 1;"
SQL_GET_BIN_ID_TO_REVIEW = "SELECT Distinct Bin.Id as binId, Bin.Number as binNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (1,2) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0' ORDER BY RAND() Limit 1;"
SQL_GET_BIN_ID_TO_APPROVE = "SELECT Distinct Bin.Id as binId, Bin.Number as binNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = Bin.id Where Bin.Status in (1) and Bin.IsDeleted = '0' and BBT.IsDeleted = '0' ORDER BY RAND() Limit 1;"
SQL_GET_ALL_ACTIVE_AND_INACTIVE_BINS = "SELECT Bin.Number as BinNumber FROM BinDbs.Bins Bin ORDER BY RAND() Limit 50;"
SQL_GET_AN_ACTIVE_BIN = "SELECT Bin.Id as binId FROM BinDbs.Bins Bin Where IsDeleted=false and Status = 'bin_status' ORDER BY RAND() Limit 1;"
SQL_GET_ALL_ACTIVE_AND_INACTIVE_BINS_USED_BY_GET_BIN_BY_ID = "SELECT Bin.Number as BinNumber FROM BinDbs.Bins Bin ORDER BY RAND() Limit 50"
SQL_GET_ALL_BINS_BASED_ON_BIN_TYPE_NAME = "SELECT distinct  B.Number,BBT.BinTypeName FROM BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where BBT.BinTypeName in ('bin_type_name') and BBT.IsDeleted = 0;"
SQL_GET_BINS_TO_REVIEW_WITH_FULL_DETAILS = "WITH CTE AS (Select B.Id as Bin_ID, B.CreatedDate as capturedDate, B.CreatedBy as capturedBy, B.Number as binNumber,B.ActionDate as actionDate,B.Status as reviewStatus,BA.ActionType as outcome,BA.ToBeActionedBy as toBeActionedBy,BA.RejectionComment as rejectedComment,BA.ReviewedBy as reviewedBy,BA.ReviewedDate as reviewedDate,BBT.BinTypeId as binTypeID,BBT.BinTypeName as binType, B.IsDeleted as binNumberIsDeleted, BBT.IsDeleted as binTypeIsDeleted from BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id Inner Join BinDbs.BinTypes BT On BBT.BinTypeId = BT.id Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.id Where B.Number = 'bin_numbers'),MaxServerVersion AS (SELECT MAX(Number) AS LatestServerNumber FROM BinDbs.BinVersions) Select * From CTE Cross Join MaxServerVersion MSV Order by reviewedDate desc"
SQL_GET_BINS_TO_REVIEW_WITH_FULL_DETAILS_ALL_BIN_TYPES_DELETED = "WITH CTE AS (Select B.Id as Bin_ID, B.CreatedDate as capturedDate, B.CreatedBy as capturedBy, B.Number as binNumber,B.ActionDate as actionDate,B.Status as reviewStatus,BA.ActionType as outcome,BA.ToBeActionedBy as toBeActionedBy,BA.RejectionComment as rejectedComment,BA.ReviewedBy as reviewedBy,BA.ReviewedDate as reviewedDate,BBT.BinTypeId as binTypeID,BBT.BinTypeName as binType, B.IsDeleted as binNumberIsDeleted, BBT.IsDeleted as binTypeIsDeleted from BinDbs.Bins B Inner Join BinDbs.BinVersions BV On BV.Id = B.BinVersionId Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id Inner Join BinDbs.BinTypes BT On BBT.BinTypeId = BT.id Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.id Where B.Number = bin_numbers),MaxServerVersion AS (SELECT MAX(Number) AS LatestServerNumber FROM BinDbs.BinVersions) Select * From CTE Cross Join MaxServerVersion MSV Order by reviewedDate desc"
SQL_GET_NEW_BIN_DETAILS = "SELECT B.id as Bin_ID, B.Number as binNumber, B.ActionDate as actionDate, BBT.BinTypeId as binTypeID, BBT.BinTypeName as binType FROM BinDbs.Bins B Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.Id Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where B.Number = 'bin_number' Order by BA.ReviewedDate desc;"
SQL_GET_INACTIVE_BIN = "SELECT Bin.Number as binNumber FROM BinDbs.Bins Bin Inner Join BinDbs.BinActionTrackers BA on BA.BinId = Bin.Id Where Bin.IsDeleted = true and BA.Status = '0' and BA.ActionType = '1' and BA.ToBeActionedBy = '2' ORDER BY RAND() Limit 1;"
SQL_GET_BIN_WITH_FULL_DETAILS = "WITH CTE AS (Select B.Id as Bin_ID, B.CreatedDate as capturedDate, B.CreatedBy as capturedBy, B.Number as binNumber,B.ActionDate as actionDate,B.Status as reviewStatus,BA.ActionType as outcome,BA.ToBeActionedBy as toBeActionedBy,BA.RejectionComment as rejectedComment,BA.ReviewedBy as reviewedBy,BA.ReviewedDate as reviewedDate,BBT.BinTypeId as binTypeID,BBT.BinTypeName as binType, B.IsDeleted as binNumberIsDeleted, BBT.IsDeleted as binTypeIsDeleted from BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id  Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.id Where B.Number = 'bin_number'),MaxServerVersion AS (SELECT MAX(Number) AS LatestServerNumber FROM BinDbs.BinVersions) Select * From CTE Cross Join MaxServerVersion MSV Order by reviewedDate desc"
SQL_GET_ACTIVE_BIN_TYPES_COUNT = "SELECT count(Id) as total FROM BinDbs.BinTypes where IsDeleted ='0';"
SQL_GET_ACTIVE_BIN_TYPES = "SELECT * FROM BinDbs.BinTypes where IsDeleted ='0';"
SQL_GET_BIN_TYPE_DETAILS = "SELECT * FROM BinDbs.BinTypes where Name ='bin_type_name';"
SQL_GET_RANDOM_BIN_TYPE_DETAILS = "SELECT * FROM BinDbs.BinTypes where Name not in ('Domestic','Token','Invalid','Contactless','OnUs','domestic','token','invalid','contactless','onUs','On-Us','on-us') and IsDeleted = '0' ORDER BY RAND() Limit 1;"
SQL_GET_THE_COUNT_OF_LINKED_TO_BINS_TYPE = "SELECT * FROM BinDbs.BinTypes where Name not in ('Domestic','Token','Invalid','Contactless','OnUs','domestic','token','invalid','contactless','onUs','On-Us','on-us') and IsDeleted = '0' ORDER BY RAND() Limit 1;"

SQL_GET_BIN_TYPE_DETAILS_USING_PARTIAL_NAME = "SELECT * FROM BinDbs.BinTypes where Name like 'bin_type_name';"
SQL_GET_BIN_TYPE_DETAILS_USING_BIN_TYPE_ID = "SELECT * FROM BinDbs.BinTypes where Id ='bin_type_id';"
SQL_GET_ACTIVE_BINS_DETAILS_FOR_BIN_TYPE = "WITH DistinctBinBinTypes AS ( SELECT bbt.BinId, bbt.BinTypeId, ROW_NUMBER() OVER (PARTITION BY bbt.BinId, bbt.BinTypeId ORDER BY bbt.CreatedDate DESC) AS rn FROM BinDbs.BinBinTypes bbt WHERE bbt.IsDeleted = 0) SELECT b.Id ,b.Number AS BinNumber ,b.ActionDate ,bt.Name AS BinType FROM BinDbs.Bins b INNER JOIN DistinctBinBinTypes bbt ON b.Id = bbt.BinId AND bbt.rn = 1 INNER JOIN BinDbs.BinTypes bt ON bt.Id = bbt.BinTypeId WHERE (@BinNumber IS NULL OR TRIM(@BinNumber) = '' OR b.Number LIKE @BinNumber) AND LOWER(bt.Name) = LOWER(@BinTypeName) AND b.IsDeleted = 0 AND bt.IsDeleted = 0;"
SQL_GET_INACTIVE_BINS_DETAILS_FOR_BIN_TYPE = "SELECT * FROM BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where BBT.BinTypeName = 'bin_type_name' and B.IsDeleted = '1';"
SQL_GET_CONFIRM_DOWNLOAD_RECORD_FOR_DEVICE =  "SELECT * FROM BinDbs.DeviceVersions where DeviceName = 'device_name' order by CreatedDate desc limit 1;"
SQL_GET_DRAFT_BIN_NUMBER = "SELECT * FROM BinDbs.Bins WHERE IsNotADraft = '0' AND IsDeleted = '0' AND Status = '1';"
SQL_IS_BIN_HARD_DELETED = "SELECT * FROM BinDbs.Bins where Number= 'bin_number'"
SQL_GET_ASSIGNED_USER_ROLE = "SELECT * FROM BinDbs.BinActionTrackers where BinId= 'bin_id' order by CreatedDate desc;"
SQL_GET_BIN_ID = "SELECT * FROM BinDbs.Bins where Number= 'bin_number'"
SQL_GET_NON_DRAFT_BIN_NUMBER = "SELECT * FROM BinDbs.Bins WHERE IsNotADraft = '1' AND IsDeleted = '0' AND Status = '1' AND DeletedDate IS NULL;"
SQL_GET_BIN_WITH_FULL_DETAILS_USED_BY_GET_BIN_BY_ID = "WITH CTE AS (Select Distinct  B.Id as Bin_ID, B.CreatedDate as capturedDate, B.CreatedBy as capturedBy, B.Number as binNumber,B.ActionDate as actionDate,B.Status as reviewStatus,BA.ActionType as outcome,BA.ToBeActionedBy as toBeActionedBy,BA.RejectionComment as rejectedComment,BA.ReviewedBy as reviewedBy,BA.ReviewedDate as reviewedDate,BBT.BinTypeId as binTypeID,BBT.BinTypeName as binType, B.IsDeleted as binNumberIsDeleted, BBT.IsDeleted as binTypeIsDeleted from BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id  Inner Join BinDbs.BinActionTrackers BA On BA.BinId = B.id Where B.Number = 'bin_number'),MaxServerVersion AS (SELECT MAX(Number) AS LatestServerNumber FROM BinDbs.BinVersions) Select * From CTE Cross Join MaxServerVersion MSV Order by reviewedDate desc"

SQL_GET_LAST_ACTION_PERFORMED_ON_THE_BIN = "select * from BinDbs.BinActionTrackers where BinId = 'bin_id' order by CreatedDate  desc limit 1;	"

SQL_GET_TOTAL_NUMBER_OF_VMS_USERS = "SELECT count(*) as total_users   FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link where Enabled = 1"
SQL_GET_BIN_IS_DELETED_STATUS = "Select IsDeleted from BinDbs.Bins where Number = 'bin_number'"
SQL_GET_BIN_IS_Not_A_DRAFT_STATUS = "Select IsNotADraft from BinDbs.Bins where Number = 'bin_number'"
SQL_GET_BIN_STATUS_FIELD = "Select Status from BinDbs.Bins where Number = 'bin_number'"
SQL_BINS_RETURNED_BY_DELETE_SEARCH = "SELECT Number, IsDeleted FROM BinDbs.Bins WHERE Number IN bin_returned_by_delete_search"
SQL_GET_BIN_TYPE_IS_DELETED_STATUS = "SELECT IsDeleted FROM BinDbs.BinTypes where Id= 'bin_type_id'"
SQL_GET_BIN_TYPE_BIN_TYPE_DETAILS_USING_STATUS = "SELECT * FROM BinDbs.BinTypes where isDeleted = 'deleted_status' ORDER BY RAND() Limit 1;"

SQL_GET_BIN_NUMBER_USING_BIN_TYPE = "Select Number as binNumber from BinDbs.Bins B where B.Id = 'bin_id';"

SQL_GET_BIN_ID_USING_BIN_Number = "Select B.Id as binId from BinDbs.Bins B where B.Number = 'bin_number';"

SQL_GET_BIN_STATUS_USING_BIN_Number = "Select B.Status as binStatus from BinDbs.Bins B where B.Number = 'bin_number';"

SQL_GET_BIN_AND_ACTIVE_BIN_TYPES = "SELECT * FROM BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where B.Number = 'bin_number' and BBT.IsDeleted = false;"


SQL_GET_ALL_ACTIVE_BINS_AND_LINKED_BIN_TYPES = "SELECT * FROM BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where B.IsDeleted = false and BBT.IsDeleted = false;"
SQL_GET_ALL_ACTIVE_BINS_LINKED_TO_BIN_TYPE = "SELECT count(*) as Total_linked_bins FROM BinDbs.Bins B Inner Join BinDbs.BinBinTypes BBT On BBT.BinId = B.id where B.IsDeleted = false and BBT.IsDeleted = false and BinTypeId = 'bin_type_id'"

SQL_GET_BINS_AND_ACTIVE_BIN_TYPES_COUNT = "SELECT count(*) as total_bins FROM BinDbs.Bins;"
SQL_GET_BINS_TO_REVIEW_COUNT = "SELECT count(*) as total_bins FROM BinDbs.Bins where Status = '1';"
