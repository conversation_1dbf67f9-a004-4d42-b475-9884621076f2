*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1276




*** Keywords ***
Verify Bins displayed on View Entries Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}
    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to the next page
    Then The next page items must be displayed
    When The user navigates to the previous page
    Then The previous page items must be displayed

| *** Test Cases ***                                                                                                                            |        *DOCUMENTATION*    		         |         *BASE_URL*                  |
| Approver_Verify Pagination is Present and Allows Navigation to Additional Pages                | Verify Bins displayed on View Entries Page   | Verify bins against the database data.    |           ${EMPTY}                  |
