import json
from robot.api.deco import keyword


class ConfirmDownload:
    @keyword
    def create_confirm_bin_table_download_request(self, device_name, is_successful_handshake,
                                                  is_successful_download, download_error, handshake_error=None):

        # Handle handshakeError field
        if handshake_error is not None:
            if not isinstance(handshake_error, dict):
                # If handshake_error is passed, ensure it is a dictionary of kwargs
                handshake_error = {
                    "type": handshake_error.get("type"),
                    "title": handshake_error.get("title"),
                    "status": handshake_error.get("status"),
                    "detail": handshake_error.get("detail")
                }

        # Create the JSON object with the provided parameters
        data = {
            "deviceName": device_name,
            "isSuccessfulHandshake": is_successful_handshake,
            "handshakeError": handshake_error,
            "isSuccessfulDownload": is_successful_download,
            "downloadError": download_error
        }

        self.json_string = data
        return json.dumps(self.json_string, indent=4)

    @keyword
    def get_endpoint(self, domain):
        path = "/api/v1/bintables/deviceversions/confirmdownload"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers
