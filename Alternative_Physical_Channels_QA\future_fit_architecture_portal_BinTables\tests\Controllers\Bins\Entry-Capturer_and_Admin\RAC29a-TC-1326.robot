*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinById_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1326




*** Keywords ***
Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test
    [Arguments]        ${DOCUMENTATION}     ${BASE_URL}      ${EXPECTED_STATUS_CODE}     ${BIN_ID}     ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to search for bins using the Bin Id      ${BASE_URL}      ${BIN_ID}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed                            ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                                                                                                      |        *DOCUMENTATION*    		           |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |                *BIN_ID*                   |             *EXPECTED_ERROR_MESSAGE*                             |
| Verify the GetBinById API handles invalid BIN IDs appropriately (404 not found)                    | Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test       | Search Bin by Number on the GetBinById API   |                                     |           404               |   0193877e-54fe-791b-9ecf-e8600f2f2431    |   The bin with the specified identifier was not found.           |
