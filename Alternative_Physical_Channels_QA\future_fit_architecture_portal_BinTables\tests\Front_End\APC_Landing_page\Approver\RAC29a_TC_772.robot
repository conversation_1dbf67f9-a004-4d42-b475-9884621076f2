*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../keywords/front_end/Bin_Table_Landing_Page.robot
Resource                                            ../../../../keywords/front_end/APC_Portal_Landing_Page.robot



*** Variables ***
${SUITE NAME}               APC Landing Page 
${TEST_CASE_ID}             RAC29a-TC-772




*** Keywords ***
Verify Locked/Grayed-Out Applications Cannot Be Selected by Unauthorized Users
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture            ${BASE_URL}    
    When The user is redirected to the APC Portal Landing page 
    And the user verifies if the Marketing and Bin Table links are greyed out
    Then if the links are greyed out, the user must not be able to access ATM Marketing or Bin Tables


| *** Test Cases ***                                                                                                                                                          |        *DOCUMENTATION*     |         *BASE_URL*                  |         
| Approver_Verify Locked/Grayed-Out Applications Cannot Be Selected by Unauthorized Users  | Verify Locked/Grayed-Out Applications Cannot Be Selected by Unauthorized Users   | Page Navigation            |           ${EMPTY}                  |           
