
import urllib.parse
import requests
from urllib.parse import urlparse
import MicrosoftAuthCode
from robot.api.deco import keyword, library
import certifi


# Microsoft OAuth endpoints
TENANT_ID = "5be1f46d-495f-465b-9507-996e8c8cdcb6"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
AUTH_ENDPOINT = '/oauth2/v2.0/authorize'
TOKEN_ENDPOINT = '/oauth2/v2.0/token'

# Client (Application) credentials
CLIENT_ID = '26b303c2-5ebd-43f7-817a-7fcb50e97745'
CLIENT_SECRET = '****************************************'
REDIRECT_URI = 'https://oauth.pstmn.io/v1/callback'  # Redirect URI registered in your Azure AD app

# Authorization parameters
params = {
    'client_id': CLIENT_ID,
    'response_type': 'code',
    'redirect_uri': REDIRECT_URI,
    'scope': 'api://26b303c2-5ebd-43f7-817a-7fcb50e97745/.default',  # Add scopes as needed
    'response_mode': 'query',
    'state': '',
}

def get_MS_Authorization_Code():
    # Construct authorization URL
    auth_url = AUTHORITY + AUTH_ENDPOINT + '?' + urllib.parse.urlencode(params)

    current_url = MicrosoftAuthCode.navigate_with_edge(auth_url)


    # Extract the Authorization Code from the URL
    code = current_url.split("code=")[1]
    code_two = code.split("&state")[0]
    print('Auth Code:',code)
    return code_two
@keyword
def get_access_token():
    # After login and consent, the browser will redirect to your redirect_uri with an authorization code
    authorization_code = get_MS_Authorization_Code()

    # Exchange authorization code for access token
    token_data = {
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET,
        'code': authorization_code,
        'redirect_uri': REDIRECT_URI,
        'grant_type': 'authorization_code',
    }

    token_url = AUTHORITY + TOKEN_ENDPOINT
    token_response = requests.post(token_url, data=token_data, verify=certifi.where())

    if token_response.status_code == 200:
        token_json = token_response.json()
        access_token = token_json['access_token']
        #print(f'Access Token: {access_token}')
        #print(f'Token Type: {token_json["token_type"]}')
        #print(f'Expires In: {token_json["expires_in"]} seconds')
        #print(f'Scope: {token_json["scope"]}')
        value = token_json["token_type"] + ' ' + access_token
        return value
    else:
        print('Failed to retrieve access token.')
        print(token_response.text)
        return

#get_access_token("self")