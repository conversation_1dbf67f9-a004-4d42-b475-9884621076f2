*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Refresh Button Functionality

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Verify Refresh Button Functionality by Selecting a Random Page First
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${PAGE_NUMBER}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user presses on a random page  ${PAGE_NUMBER}

    Then The user presses Resfresh Button

    And The user verifies Refresh Button updates page to default state

| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |    ***PAGE_NUMBER***   |
| Verify Refresh Button Functionality | Verify Refresh Button Functionality by Selecting a Random Page First     | Verify Refresh Button Functionality  |      VMS_UAT             |    5   |