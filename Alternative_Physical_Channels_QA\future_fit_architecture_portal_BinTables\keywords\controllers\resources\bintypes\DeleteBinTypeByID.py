
class CreateRESTRequest:
    def __init__(self, domain, bin_id):
        self.domain = domain
        self.bin_id = bin_id

        self.params = {
            "binTypeId": self.bin_id,  # Adding a query parameter to filter the results by Bin Id
        }


    def get_endpoint(self):
        path = "/api/v1/bintables/admin/bintypes/deletebintypebyid"
        url = f"{self.domain}{path}"
        return url


    def get_params(self):
        return self.params
