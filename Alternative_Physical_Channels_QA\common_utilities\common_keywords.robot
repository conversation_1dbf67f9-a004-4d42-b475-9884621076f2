*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Common Keywords



#***********************************EXTERNAL LIBRIRIES***************************************

Library                                             SeleniumLibrary
Resource                                            ../common_utilities/Login.robot


*** Variables ***

${ATM_MARKETING_DASHBOARD_XPATH}                    xpath=//div[contains(@class, 'landing-content')]//p[contains(text(), 'ATM MARKETING')]
${BIN_TABLES_DASHBOARD_XPATH}                       xpath=//div[contains(@class, 'landing-content')]//p[contains(text(), 'BIN TABLES')]


*** Keywords ***

The user navigates to their Specified Dashboard
    [Arguments]  ${DASHBOARD}

    Run Keyword If    $DASHBOARD == 'ATM_MARKETING_DASHBOARD'
    ...    The user clicks Dashboard    ${ATM_MARKETING_DASHBOARD_XPATH}
    ...    ELSE IF    $DASHBOARD == 'BIN_TABLES'
    ...    The user clicks Dashboard    ${BIN_TABLES_DASHBOARD_XPATH}

The user clicks Dashboard
    [Arguments]  ${DASHBOARD_XPATH}
    Wait Until Element Is Visible       ${DASHBOARD_XPATH}
    Click Element    ${DASHBOARD_XPATH}
    Log to console      --------------------------The user has clicked the specified dashboard
    Capture Page Screenshot


Get Bearer Token
    ${access_token}=    get access token
    RETURN      ${access_token}