*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/Add_BinTypes_Page.robot
Resource            ../../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Verify Bin Types displayed on View Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_TYPE_NAME}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bin Type Menu
    Then The created Bin Type must be displayed on the 'View' page                       ${BIN_TYPE_NAME}
    And The created Bin Type must exist in the database            ${BIN_TYPE_NAME}    ${EMPTY}


| *** Test Cases ***                                                                                              |        *DOCUMENTATION*    		          |         *BASE_URL*                  |    *BIN_TYPE_NAME*  |
| Capturer_Display Newly Added BIN Type After Addition        | Verify Bin Types displayed on View Page   | Verify bin types against the database.    |           ${EMPTY}                  |      Token          |
