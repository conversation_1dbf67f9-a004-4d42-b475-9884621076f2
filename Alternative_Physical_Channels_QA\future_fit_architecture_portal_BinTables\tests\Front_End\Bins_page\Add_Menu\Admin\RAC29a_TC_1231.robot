*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify Bin Number List is Sorted by Captured Date Descending
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${PARTIAL_BIN_NUMBER}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The user searches with a partial Bin Number    ${PARTIAL_BIN_NUMBER}
    And The User gets the bins returned on the list
    And The user navigates to the View Menu 
    And The user Gets the Bin Captured Dates from the View Menu
    Then The user verifies that the Bin Number list is sorted by descending order

| *** Test Cases ***                                                                                                          |        *DOCUMENTATION*                                                         |         *BASE_URL*                  |   *PARTIAL_BIN_NUMBER*     |   
| Admin_BIN Number List Sorted by Captured Date Descending   | Verify Bin Number List is Sorted by Captured Date Descending   | Verifying bins returned on Bin Number field are in captured descending order   |           ${EMPTY}                  |        600                 |      
