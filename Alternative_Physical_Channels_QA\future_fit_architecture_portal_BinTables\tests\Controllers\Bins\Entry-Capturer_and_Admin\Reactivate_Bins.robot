*** Settings ***
#Author Name               : Thabo
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/ReactivateBin_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${SUITE NAME}               BIN Tables - Reactivate Bin


*** Keywords ***
Re-activate Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}    ${BIN_ID}      &{BINS_DETAILS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user gets an inactive Bin from the database
    Given The User Populates the Reactivate Bin JSON payload with                &{BINS_DETAILS}
    When The User sends the Reactivate Bin API Request                           ${BASE_URL}
    And The service returns an expected status code                              ${EXPECTED_STATUS_CODE}
    And The user verifies that the current BIN is not Deleted                    ${BIN_ID}
    Then The Reactivated Bin Number(s) details must exist on the Bin Database    &{BINS_DETAILS}

| *** Test Cases ***                                        |        *DOCUMENTATION*    		              |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |                     *BIN_ID*                       |                *BINS_DETAILS*                                                                                               |
| Reactivate deleted Bin                | Re-activate Bin   | Re-activate deleted Bin for the Bin Tables      |                                     |         200                 |                                                    |   binId1=52e1fe2d-ac08-40c8-97fb-d8d597a03cc9 | date1=2026-11-11 | binIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e       |
