<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 5.0.1 (Python 3.10.12 on win32)" generated="20250521 11:23:02.425" rpa="false" schemaversion="3">
<suite id="s1" name="Robot Interactive Console" source="c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robotframework_interactive\robot_interactive_console.robot">
<test id="s1-t1" name="Default Task/Test" line="5">
<kw name="Interpreter Main Loop" library="MainLoop">
<status status="PASS" starttime="20250521 11:23:02.458" endtime="20250521 11:23:07.252"/>
</kw>
<status status="PASS" starttime="20250521 11:23:02.457" endtime="20250521 11:23:07.252"/>
</test>
<status status="PASS" starttime="20250521 11:23:02.428" endtime="20250521 11:23:07.255"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Robot Interactive Console">Robot Interactive Console</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
