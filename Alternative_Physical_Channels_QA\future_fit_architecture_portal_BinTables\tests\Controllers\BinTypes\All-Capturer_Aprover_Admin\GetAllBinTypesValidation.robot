*** Settings ***
#Author Name               : Yaash
#Email Address             : Yaash.<PERSON><PERSON><PERSON>@absa.africa

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation                                       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetAllBinTypes2_Keyword.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Send a Get Request for GetAllBinTypes
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}    ${EXPECTED_STATUS_CODE}   
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetAllBinTypes        ${BASE_URL}      
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    And The Active Bin Types are retuned by the API Response         
    Then The Active Bin Types returned must exist on the Bin Database                  


| *** Test Cases ***                                                                                         |        *DOCUMENTATION*    		|         *BASE_URL*                  |    *EXPECTED_STATUS_CODE    |
| GetAllBinTypesValidation   | Send a Get Request for GetAllBinTypes       | Get All Bin Types               |  BIN_TABLES_API_DEV_BASE_URL        |         200                 |
