<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20241031 11:16:42.475">
   <suite name="Future Fit Portal" id="s1" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_127_Approver_Capture_Campaign.robot">
      <test name="RAC29a_TC_117_FFT_Approval_Approve_Campaign" id="s1-s1-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Approve Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:16:44.053" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:17:19.003" status="PASS" starttime="20241031 11:16:44.053"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Approve Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:17:19.003" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:17:24.679" status="PASS" starttime="20241031 11:17:19.003"/>
         </kw>
         <kw library="Selenium" name="And The clicks on the preview button to preview an un-approved campaign">
            <doc>Approve Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:17:24.679" level="INFO">And The clicks on the preview button to preview an un-approved campaign</msg>
            <status endtime="20241031 11:17:35.632" status="PASS" starttime="20241031 11:17:24.679"/>
         </kw>
         <kw library="Selenium" name="And The user approves the campaign">
            <doc>Approve Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:17:35.633" level="INFO">And The user approves the campaign</msg>
            <status endtime="20241031 11:17:40.893" status="PASS" starttime="20241031 11:17:35.633"/>
         </kw>
         <kw library="Selenium" name="And The user navigates back to the Campaign Approvals page">
            <doc>Approve Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:17:40.893" level="INFO">And The user navigates back to the Campaign Approvals page</msg>
            <status endtime="20241031 11:17:51.709" status="PASS" starttime="20241031 11:17:40.893"/>
         </kw>
         <kw library="Selenium" name="Then The user verifies that the campaign has been approved">
            <doc>Approve Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:17:51.709" level="INFO">Then The user verifies that the campaign has been approved</msg>
            <status endtime="20241031 11:17:54.035" status="PASS" starttime="20241031 11:17:51.709"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_117_FFT_Approval_Approve_Campaign</tag>
         </tags>
         <status endtime="20241031 11:18:02.491" critical="yes" status="PASS" starttime="20241031 11:16:44.053"/>
      </test>
      <test name="RAC29a_TC_118_FFT_Approval_Delete_Campaign" id="s1-s2-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Delete Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:18:02.581" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:18:31.631" status="PASS" starttime="20241031 11:18:02.581"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Delete Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:18:31.631" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:18:37.289" status="PASS" starttime="20241031 11:18:31.631"/>
         </kw>
         <kw library="Selenium" name="And The user clicks the delete icon on a campaign">
            <doc>Delete Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:18:37.290" level="INFO">And The user clicks the delete icon on a campaign</msg>
            <status endtime="20241031 11:18:45.643" status="PASS" starttime="20241031 11:18:37.290"/>
         </kw>
         <kw library="Selenium" name="And The user clicks the deactivate campaign button to confirm deletion">
            <doc>Delete Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:18:45.643" level="INFO">And The user clicks the deactivate campaign button to confirm deletion</msg>
            <status endtime="20241031 11:18:53.966" status="PASS" starttime="20241031 11:18:45.643"/>
         </kw>
         <kw library="Selenium" name="And The user navigates back to the Campaign Approvals page">
            <doc>Delete Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:18:53.966" level="INFO">And The user navigates back to the Campaign Approvals page</msg>
            <status endtime="20241031 11:19:04.740" status="PASS" starttime="20241031 11:18:53.966"/>
         </kw>
         <kw library="Selenium" name="Then The user verifies that the campaign has been deleted and removed from the front end">
            <doc>Delete Campaign</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:19:04.740" level="INFO">Then The user verifies that the campaign has been deleted and removed from the front end</msg>
            <status endtime="20241031 11:19:13.875" status="PASS" starttime="20241031 11:19:04.740"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_118_FFT_Approval_Delete_Campaign</tag>
         </tags>
         <status endtime="20241031 11:19:22.103" critical="yes" status="PASS" starttime="20241031 11:18:02.580"/>
      </test>
      <test name="RAC29a_TC_119_FFT_Approval_Search_for_Campaign_By_Campaign_Id" id="s1-s3-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Search by campaign ID</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:19:22.158" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:19:51.136" status="PASS" starttime="20241031 11:19:22.158"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Search by campaign ID</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:19:51.137" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:19:56.578" status="PASS" starttime="20241031 11:19:51.137"/>
         </kw>
         <kw library="Selenium" name="And The user inputs a campaign ID on the Search field">
            <doc>Search by campaign ID</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:19:56.578" level="INFO">And The user inputs a campaign ID on the Search field</msg>
            <status endtime="20241031 11:20:03.690" status="PASS" starttime="20241031 11:19:56.578"/>
         </kw>
         <kw library="Selenium" name="Then The user verifies the search results returned by Campaign ID">
            <doc>Search by campaign ID</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:20:03.690" level="INFO">Then The user verifies the search results returned by Campaign ID</msg>
            <status endtime="20241031 11:20:03.726" status="PASS" starttime="20241031 11:20:03.690"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_119_FFT_Approval_Search_for_Campaign_By_Campaign_Id</tag>
         </tags>
         <status endtime="20241031 11:20:10.981" critical="yes" status="PASS" starttime="20241031 11:19:22.158"/>
      </test>
      <test name="RAC29a_TC_120_FFT_Approval_Search_for_Campaign_By_Campaign_Name" id="s1-s4-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Search by campaign name</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:20:11.028" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:20:39.984" status="PASS" starttime="20241031 11:20:11.028"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Search by campaign name</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:20:39.984" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:20:45.483" status="PASS" starttime="20241031 11:20:39.984"/>
         </kw>
         <kw library="Selenium" name="And The user inputs a campaign name on the Search field">
            <doc>Search by campaign name</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:20:45.483" level="INFO">And The user inputs a campaign name on the Search field</msg>
            <status endtime="20241031 11:20:52.672" status="PASS" starttime="20241031 11:20:45.483"/>
         </kw>
         <kw library="Selenium" name="Then The user verifies the search results returned by Campaign Name">
            <doc>Search by campaign name</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:20:52.673" level="INFO">Then The user verifies the search results returned by Campaign Name</msg>
            <status endtime="20241031 11:20:52.721" status="PASS" starttime="20241031 11:20:52.673"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_120_FFT_Approval_Search_for_Campaign_By_Campaign_Name</tag>
         </tags>
         <status endtime="20241031 11:21:01.098" critical="yes" status="PASS" starttime="20241031 11:20:11.028"/>
      </test>
      <test name="RAC29a_TC_121_FFT_Approval_Search_for_Campaign_By_Approved_By" id="s1-s5-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Search by Approved By user</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:21:01.153" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:21:30.184" status="PASS" starttime="20241031 11:21:01.153"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Search by Approved By user</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:21:30.185" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:21:35.834" status="PASS" starttime="20241031 11:21:30.185"/>
         </kw>
         <kw library="Selenium" name="And The user inputs an Approved By user on the Search field">
            <doc>Search by Approved By user</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:21:35.834" level="INFO">And The user inputs an Approved By user on the Search field</msg>
            <status endtime="20241031 11:21:48.093" status="PASS" starttime="20241031 11:21:35.834"/>
         </kw>
         <kw library="Selenium" name="Then The user verifies the search results returned by the Approved By user">
            <doc>Search by Approved By user</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:21:48.093" level="INFO">Then The user verifies the search results returned by the Approved By user</msg>
            <status endtime="20241031 11:21:48.124" status="PASS" starttime="20241031 11:21:48.093"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_121_FFT_Approval_Search_for_Campaign_By_Approved_By</tag>
         </tags>
         <status endtime="20241031 11:21:55.419" critical="yes" status="PASS" starttime="20241031 11:21:01.153"/>
      </test>
      <test name="RAC29a_TC_122_Show_maximum_campaign_feature" id="s1-s10-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Approver Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:26:54.999" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:27:23.980" status="PASS" starttime="20241031 11:26:54.999"/>
         </kw>
         <kw library="Selenium" name="When The user has an active Approver Role">
            <doc>Approver Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:27:23.980" level="INFO">When The user has an active Approver Role</msg>
            <status endtime="20241031 11:27:24.013" status="PASS" starttime="20241031 11:27:23.980"/>
         </kw>
         <kw library="Selenium" name="Then The user should not be able to access Capture Campaign">
            <doc>Approver Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:27:24.013" level="INFO">Then The user should not be able to access Capture Campaign</msg>
            <status endtime="20241031 11:27:29.426" status="PASS" starttime="20241031 11:27:24.013"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_122_Show_maximum_campaign_feature</tag>
         </tags>
         <status endtime="20241031 11:27:36.731" critical="yes" status="PASS" starttime="20241031 11:26:54.997"/>
      </test>
      <test name="RAC29a_TC_123_FFT_Preview_Campaign" id="s1-s7-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Close Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:22:51.515" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:23:20.481" status="PASS" starttime="20241031 11:22:51.515"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Close Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:23:20.481" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:23:25.908" status="PASS" starttime="20241031 11:23:20.481"/>
         </kw>
         <kw library="Selenium" name="Then The user navigates to the Campaign Approvals page and previews a single campaign">
            <doc>Close Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:23:25.908" level="INFO">Then The user navigates to the Campaign Approvals page and previews a single campaign</msg>
            <status endtime="20241031 11:23:37.204" status="PASS" starttime="20241031 11:23:25.908"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_123_FFT_Preview_Campaign</tag>
         </tags>
         <status endtime="20241031 11:23:44.497" critical="yes" status="PASS" starttime="20241031 11:22:51.515"/>
      </test>
      <test name="RAC29a_TC_124_FFT_Preview_Campaign_Close_Button" id="s1-s8-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Close Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:23:44.574" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:24:13.596" status="PASS" starttime="20241031 11:23:44.574"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Close Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:24:13.596" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:24:19.042" status="PASS" starttime="20241031 11:24:13.596"/>
         </kw>
         <kw library="Selenium" name="Then The user previews and validates the Close Button on Approval Preview">
            <doc>Close Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:24:19.042" level="INFO">Then The user previews and validates the Close Button on Approval Preview</msg>
            <status endtime="20241031 11:25:12.990" status="PASS" starttime="20241031 11:24:19.042"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_124_FFT_Preview_Campaign_Close_Button</tag>
         </tags>
         <status endtime="20241031 11:25:21.325" critical="yes" status="PASS" starttime="20241031 11:23:44.573"/>
      </test>
      <test name="RAC29a_TC_125_FFT_Preview_Campaign_Next_Button" id="s1-s9-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Next Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:25:21.373" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:25:50.446" status="PASS" starttime="20241031 11:25:21.373"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to the Campaign Approvals page">
            <doc>Next Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:25:50.446" level="INFO">When The user navigates to the Campaign Approvals page</msg>
            <status endtime="20241031 11:25:55.902" status="PASS" starttime="20241031 11:25:50.446"/>
         </kw>
         <kw library="Selenium" name="Then The user previews and validates the Next Button on Approval Preview">
            <doc>Next Button on Campaign Approvals Preview</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:25:55.902" level="INFO">Then The user previews and validates the Next Button on Approval Preview</msg>
            <status endtime="20241031 11:26:47.614" status="PASS" starttime="20241031 11:25:55.902"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_125_FFT_Preview_Campaign_Next_Button</tag>
         </tags>
         <status endtime="20241031 11:26:54.915" critical="yes" status="PASS" starttime="20241031 11:25:21.373"/>
      </test>
      <test name="RAC29a_TC_122_Show_maximum_campaign_feature" id="s1-s10-t1">
         <kw library="Selenium" name="Given The user logs into Future Fit Architecture portal">
            <doc>Approver Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:26:54.999" level="INFO">Given The user logs into Future Fit Architecture portal</msg>
            <status endtime="20241031 11:27:23.980" status="PASS" starttime="20241031 11:26:54.999"/>
         </kw>
         <kw library="Selenium" name="When The user has an active Approver Role">
            <doc>Approver Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:27:23.980" level="INFO">When The user has an active Approver Role</msg>
            <status endtime="20241031 11:27:24.013" status="PASS" starttime="20241031 11:27:23.980"/>
         </kw>
         <kw library="Selenium" name="Then The user should not be able to access Capture Campaign">
            <doc>Approver Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241031 11:27:24.013" level="INFO">Then The user should not be able to access Capture Campaign</msg>
            <status endtime="20241031 11:27:29.426" status="PASS" starttime="20241031 11:27:24.013"/>
         </kw>
         <tags>
            <tag>RAC29a_TC_122_Show_maximum_campaign_feature</tag>
         </tags>
         <status endtime="20241031 11:27:36.731" critical="yes" status="PASS" starttime="20241031 11:26:54.997"/>
      </test>
      <status endtime="20241031 11:27:36.734" status="PASS" starttime="20241031 11:16:42.475"/>
   </suite>
   <statistics>
      <total>
         <stat pass="9" fail="0">Critical Tests</stat>
         <stat pass="9" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">RAC29a_TC_117_FFT_Approval_Approve_Campaign</stat>
         <stat pass="1" fail="0">RAC29a_TC_118_FFT_Approval_Delete_Campaign</stat>
         <stat pass="1" fail="0">RAC29a_TC_119_FFT_Approval_Search_for_Campaign_By_Campaign_Id</stat>
         <stat pass="1" fail="0">RAC29a_TC_120_FFT_Approval_Search_for_Campaign_By_Campaign_Name</stat>
         <stat pass="1" fail="0">RAC29a_TC_121_FFT_Approval_Search_for_Campaign_By_Approved_By</stat>
         <stat pass="1" fail="0">RAC29a_TC_122_Show_maximum_campaign_feature</stat>
         <stat pass="1" fail="0">RAC29a_TC_123_FFT_Preview_Campaign</stat>
         <stat pass="1" fail="0">RAC29a_TC_124_FFT_Preview_Campaign_Close_Button</stat>
         <stat pass="1" fail="0">RAC29a_TC_125_FFT_Preview_Campaign_Next_Button</stat>
         <stat pass="1" fail="0">RAC29a_TC_122_Show_maximum_campaign_feature</stat>
      </tag>
      <suite>
         <stat name="Future Fit Portal" pass="9" fail="0" id="s1">Future Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
