*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Contains generic methods

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             ../../../utility/Utils.py
Library                                            ../../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../../keywords/controllers/resources/CommonFunctions.py
Library                                             ../../../../common_utilities/CommonUtils.py
Library                                             SeleniumLibrary

Resource                                          ../../common/DatabaseConnector.robot


*** Variables ***
${pattern}  SEPARATOR=
...  /^[A-Z]+$/i                  # one or more alphabet "





*** Keywords ***

#Get the current schedule version from the database
#    ${db_type}=   Set Variable   'MYSQL'
#    #Get the current schedule version from the database
#    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
#    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
#    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion
#    RETURN  ${schedule_version}

Verify that values are equal
    [Arguments]     ${EXPECTED_VALUE}    ${ACTUAL_VALUE}
         Run Keyword And Continue On Failure    Should Be Equal As Strings    ${EXPECTED_VALUE}    ${ACTUAL_VALUE}
         Log Many     Comparison result  : Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}


All Variables are True
    [Arguments]    @{variables}
    #The all() function in Python takes an iterable (like a list or tuple)
    #and returns True if all elements of the iterable are true (or if the iterable is empty).
    #If any element is False, all() returns False.

    ${all_true}=    Evaluate    all(${variables})
    RETURN    ${all_true}


Write JSON data To File
    [Arguments]    ${file}    ${json_string}

    Create File    ${file}    ${json_string}


Format Timestamp for API and Database
    [Arguments]  ${timestamp}    ${use_t_separator}    #A boolean value indicating whether to use 'T' or a space separator.
    [Documentation]    This keyword formats a given timestamp to either include a 'T' or a space separator between the date and time.

    ${time_data}=       Format Timestamp    ${timestamp}        ${use_t_separator}
    RETURN    ${time_data}

Format Timestamp for API use
    [Arguments]  ${date_string}
    ${date_data}=       Format Date    ${date_string}
    RETURN    ${date_data}


Verify that the Bin Deletion Status is as expected
    [Arguments]  ${BIN_NUMBER}      ${IS_DELETED_EXPECTED_BOOLEAN}

     ${expected_deletion_status}=       Set Variable If
         ...       '${IS_DELETED_EXPECTED_BOOLEAN}' == '0'       ${False}
         ...       '${IS_DELETED_EXPECTED_BOOLEAN}' == '1'       ${True}
         ...       ${IS_DELETED_EXPECTED_BOOLEAN}

     ${actual_deletion_status}=         Get the Bin 'ISDeleted' status      ${BIN_NUMBER}
     ${verification_passed}=        Run Keyword And Return Status    Verify that values are equal    ${expected_deletion_status}     ${actual_deletion_status}
     RETURN   ${verification_passed}

Remove Quotes
    [Arguments]    ${string}
    ${no_single_quotes}=    Replace String    ${string}    '    ${EMPTY}
    ${no_quotes}=    Replace String    ${no_single_quotes}    "    ${EMPTY}
    [Return]    ${no_quotes.strip()}

Create Comma Separated String
    [Arguments]    ${items}
    ${joined_string}=    Evaluate    ', '.join(${items})    # Joins list items with a comma and space
    [Return]    ${joined_string}

Convert Dict To Kwargs
    [Arguments]     ${dict}
    ${kwargs}=    Evaluate    {k: v for k, v in ${dict}.items()}
    Log    ${kwargs}

Create JSON Payload
    [Arguments]     ${payload_type}  ${file_to_be_saved_location}  &{kw_args}
     # Create a dictionary payload where a field contains a comma-separated list (will be converted to a list of dictionaries)
    #${kwargs}=      bin3=7004520    date3=2026-11-12    binIds=a7ff7c25-057b-461b-9fa1-50d471202b52,bb679411-b69d-42b1-a6c6-8e7cdc63d6c4
    Log Many        &{kw_args}
    ${result}=    Create Payload    ${payload_type}    &{kw_args}
    ${file_to_be_saved_location}=   Set Variable        future_fit_architecture_portal_BinTables/data/UploadBin.json
    Write JSON data To File    ${file_to_be_saved_location}    ${result}

    Log Many   Generated (${payload_type}) payload:    ${result}

    RETURN   ${result}

Decode Base 64 String
    [Arguments]    ${BASE_64_STRING}
    ${decoded_string}=      decode base64       ${BASE_64_STRING}

    RETURN   ${decoded_string}

Verify if the string is in valid base64 format
    [Arguments]     ${BASE_64_STRING}

    ${is_valid_base64_string}=      is base64 string     ${BASE_64_STRING}

    RETURN      ${is_valid_base64_string}

Verify if the contents of the first xml exist in the second xml file
    [Arguments]  ${XML_FILE_A}      ${XML_FILE_B}

    ${all_contents_of_file_a_match_file_b}=     Check Values In Xml    ${XML_FILE_A}    ${XML_FILE_B}

    RETURN  ${all_contents_of_file_a_match_file_b}

Extract XML values and save them in an array
    [Arguments]  ${XML_FILE_PATH}

    ${file_contents}=     Read Values From Xml    ${XML_FILE_PATH}

    RETURN  ${file_contents}


Run Keyword Until Success
    [Arguments]         ${KW}   ${KWARGS}
    ${result}=     Wait Until Keyword Succeeds    30s    1s    ${KW}   ${KWARGS}

    RETURN  ${result}

Wait for Element to be enabled
    [Arguments]      ${ELEMENT}
    ${element_displayed}=   Set Variable    ${False}
    ${TIME_FORMAT}=     Set Variable    epoch   # Unix timestamp format
    ${TIMEOUT}=         Set Variable    60   # Timeout in seconds
    ${START_TIME}=      Get Time    format=${TIME_FORMAT}  # Capture the start time in seconds
    ${START_TIME_INT}=    Evaluate    int(${START_TIME})  # Convert to integer
    ${elapsed_time}=    Set Variable   0

    WHILE    ${elapsed_time} < ${TIMEOUT}
        ${current_time}=    Get Time    format=${TIME_FORMAT}  # Get the current time in seconds
        ${current_time_int}=    Evaluate    int(${current_time})  # Convert to integer
        ${elapsed_time}=    Evaluate    ${current_time_int} - ${START_TIME_INT}  # Calculate elapsed time
        ${element_displayed}=     Run Keyword And Return Status     GenericMethods.Run Keyword Until Success      Wait Until Element Is Enabled    ${ELEMENT}
        IF    ${element_displayed}
             RETURN  ${element_displayed}
        END
        Run Keyword If    ${elapsed_time} >= ${TIMEOUT}    Run Keyword And Continue On Failure  Fail    Timeout reached after ${TIMEOUT} seconds, the element was not found.

        #Sleep    2
        Log    Running... elapsed time: ${elapsed_time} seconds
    END

    RETURN      ${element_displayed}

Get Current Element Attributes
    [Arguments]    ${locator}
    ${attributes}=      Get Element Properties      ${locator}
    FOR  ${key}  ${value}  IN  &{attributes}
       Log  ${key}: ${value}  # Log each key-value pair
    END
    RETURN    ${attributes}

Correct Page is displayed
    [Arguments]     ${EXPECTED_TEXT}

    ${correct_page_displayed}=     Run Keyword And Return Status    SeleniumLibrary.Page Should Contain    ${EXPECTED_TEXT}

    IF    not ${correct_page_displayed}
        Capture Page Screenshot   ${EXPECTED_TEXT}_Page_not_Displayed.png
        Run Keyword And Continue On Failure    Fail   The expected text, which is '${EXPECTED_TEXT}' was not displayed on the current page.
        RETURN   ${False}
    ELSE
        Capture Page Screenshot   ${EXPECTED_TEXT}_Page_Displayed.png
        Log Many   The expected text, which is '${EXPECTED_TEXT}' was displayed on the current page.
        RETURN   ${True}
    END


Convert Month To Number
    [Arguments]    ${date}
    ${date_array}       Split String    ${date}     separator=${SPACE}
    ${month}            Get From List    ${date_array}    0
    ${month_map}        Create Dictionary    Jan=01    Feb=02    Mar=03    Apr=04    May=05    Jun=06    Jul=07    Aug=08    Sep=09    Oct=10    Nov=11    Dec=12
    ${month_number}     Get From Dictionary    ${month_map}    ${month}
    RETURN    ${month_number.strip()}

Extract Day From Date
    [Arguments]    ${date}
    ${date_array}      Split String    ${date}     separator=${SPACE}
    ${day_data}        Get From List    ${date_array}    1
    ${day_array}       Split String    ${day_data}     separator=,
    ${day_data}        Get From List    ${day_array}    0
    ${current_day_int}=    Evaluate    int('${day_data}')

    IF    ${current_day_int} < 10
         ${day_data}=      Catenate     0${day_data.strip()}
    END

    RETURN    ${day_data.strip()}

Extract Year From Date
    [Arguments]    ${date}
    ${date_array}       Split String    ${date}     separator=${SPACE}
    ${year}    Get From List    ${date_array}    2
    RETURN    ${year.strip()}


Convert Dictionary to JSON
    [Arguments]     ${my_dict}
    ${json_string}=    Evaluate    json.dumps(${my_dict})    modules=json
    Log    ${json_string}
    RETURN  ${json_string}


Compare Key-Value Pairs of Two Dictionaries
    [Arguments]     ${dict1}    ${dict2}

     FOR    ${key}    ${value}    IN    &{dict1}
        ${result}=    Run Keyword And Ignore Error    Get From Dictionary    ${dict2}    ${key}
        ${status}=    Set Variable If    '${result[0]}' == 'PASS'    ${result[1]}    None
        Run Keyword If    '${status}' == 'None'    Log    Key ${key} is missing in dict2
        Run Keyword If    '${status}' != 'None'    Run Keyword And Continue On Failure  Should Be Equal As Strings    ${value}    ${status}       msg='${key}' value: '${value}' from dict1 and '${key}' value:'${status}' from dict2 are not equal
    END

Scroll Down The Page
    Execute JavaScript    window.scrollBy(0, 1000)

Scroll Up The Page
    Execute JavaScript    window.scrollBy(0, -1000)

Remove last zero from a String
    [Arguments]  ${the_string}

    ${modified_string}=    Convert To String    ${the_string}

    ${modified_string}=    Remove Trailing Zero     ${modified_string}

    RETURN  ${modified_string}


