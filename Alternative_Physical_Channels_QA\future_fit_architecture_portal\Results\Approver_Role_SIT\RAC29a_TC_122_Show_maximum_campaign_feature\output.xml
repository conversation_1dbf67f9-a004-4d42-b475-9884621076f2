<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:01:54.769721" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_122_Show_maximum_campaign_feature.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:55.888118" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:01:55.888118" elapsed="0.001021"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:55.889139" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:01:55.889139" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:55.890493" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:01:55.889139" elapsed="0.001354"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:55.890493" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:01:55.890493" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:55.891507" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:01:55.890493" elapsed="0.001014"/>
</kw>
<status status="PASS" start="2024-10-29T12:01:55.887117" elapsed="0.004390"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_122_Show_maximum_campaign_feature" line="36">
<kw name="Validating the items per page feature on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:01:55.893726" level="INFO">Set test documentation to:
Items per page feature</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:01:55.892719" elapsed="0.001007"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:01:55.988792" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:01:55.893726" elapsed="0.095066"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:55.988792" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:55.989792" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:01:55.990813" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:01:55.990813" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:01:55.990813" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:01:55.990813" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:01:55.990813" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:01:55.991792" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:01:55.990813" elapsed="0.000979"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:01:55.991792" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:01:56.027527" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:01:56.368934" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T12:01:56.368934" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:01:55.991792" elapsed="0.377142"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:01:56.369940" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:56.369940" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:01:56.369940" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:01:55.990813" elapsed="0.379127"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:56.370939" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:01:56.370939" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:01:56.370939" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:01:56.370939" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:01:56.370939" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:01:56.370939" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:01:56.370939" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:01:56.370939" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:01:56.370939" elapsed="0.001023"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:01:56.370939" elapsed="0.001023"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:01:56.371962" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:01:56.371962" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x00000247C4424F50&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:01:56.371962" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:01:56.372940" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:01:56.372940" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:01:56.372940" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:01:56.372940" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:01:56.372940" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:01:56.372940" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:01:56.372940" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:01:56.372940" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:01:56.373957" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:01:56.373957" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:01:56.373957" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:01:56.373957" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:01:56.373957" elapsed="0.001000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:01:56.374957" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:01:56.374957" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:01:56.374957" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:01:55.893726" elapsed="33.758467"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:02:29.653173" elapsed="0.020800"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:29.678972" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="9403b5970f742d3470b715f42a129f81", element="f.59134120444E7B6AB150EB8D690E0D2D.d.49A6D17478BBF45C29DEFBB9CAF40B40.e.152")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:29.673973" elapsed="0.004999"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:29.678972" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="9403b5970f742d3470b715f42a129f81", element="f.59134120444E7B6AB150EB8D690E0D2D.d.49A6D17478BBF45C29DEFBB9CAF40B40.e.152")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:29.678972" elapsed="0.033521"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:02:29.712493" elapsed="0.007352"/>
</kw>
<status status="PASS" start="2024-10-29T12:02:29.712493" elapsed="0.008405"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:29.728866" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="9403b5970f742d3470b715f42a129f81", element="f.59134120444E7B6AB150EB8D690E0D2D.d.49A6D17478BBF45C29DEFBB9CAF40B40.e.153")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:29.720898" elapsed="0.007968"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:34.729364" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:29.728866" elapsed="5.000498"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:02:34.729364" elapsed="0.019999"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:34.749363" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="9403b5970f742d3470b715f42a129f81", element="f.59134120444E7B6AB150EB8D690E0D2D.d.49A6D17478BBF45C29DEFBB9CAF40B40.e.153")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:34.749363" elapsed="0.070326"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:02:34.819689" elapsed="1.088767"/>
</kw>
<status status="PASS" start="2024-10-29T12:02:34.819689" elapsed="1.088767"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:02:35.909581" elapsed="0.005892"/>
</kw>
<status status="PASS" start="2024-10-29T12:02:35.909581" elapsed="0.005892"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:35.925725" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:02:35.916704" elapsed="0.009021"/>
</kw>
<status status="PASS" start="2024-10-29T12:02:29.653173" elapsed="6.272552"/>
</kw>
<kw name="And The user filters campaigns with items per page feature" owner="Approvals">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:02:35.925725" elapsed="0.281451"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:02:36.207176" level="INFO">${selected_value_xpath} = //span[contains(@class, 'mat-select-min-line') and text()='10']</msg>
<var>${selected_value_xpath}</var>
<arg>//span[contains(@class, 'mat-select-min-line') and text()='10']</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:02:36.207176" elapsed="0.000000"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:02:36.207176" elapsed="0.276652"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${selected_value_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:02:36.483828" elapsed="0.014885"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:36.510492" level="INFO">${selected_value} = 10</msg>
<var>${selected_value}</var>
<arg>${selected_value_xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:36.498713" elapsed="0.011779"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${selected_value}</arg>
<arg>10</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2024-10-29T12:02:36.510492" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:02:36.510492" level="INFO">---- Default value is correctly set to 10 ----</msg>
<arg>---- Default value is correctly set to 10 ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:02:36.510492" elapsed="0.000000"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:02:36.510492" elapsed="0.274621"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:36.786568" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:36.785113" elapsed="0.032331"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:37.819006" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:36.818441" elapsed="1.000667"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:02:37.819228" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='5']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:02:37.819228" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:02:37.819228" elapsed="0.014049"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:37.833277" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='5']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:37.833277" elapsed="0.028228"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:39.861766" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:37.861505" elapsed="2.000261"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:39.869804" level="INFO">${total_row_count} = 6</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:39.861766" elapsed="0.008038"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:02:39.869804" level="INFO">${actual_row_count} = 5</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:02:39.869804" elapsed="0.001025"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:02:39.870829" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:02:39.870829" level="INFO">---- Verified 5 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:02:39.870829" elapsed="0.000000"/>
</kw>
<var name="${option}">5</var>
<status status="PASS" start="2024-10-29T12:02:36.510492" elapsed="3.360337"/>
</iter>
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:02:39.870829" elapsed="0.263413"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:40.135225" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:40.134242" elapsed="0.027620"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:41.162344" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:40.161862" elapsed="1.000482"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:02:41.165355" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='10']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:02:41.162344" elapsed="0.003011"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:02:41.165355" elapsed="0.014185"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:41.179540" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='10']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:41.179540" elapsed="0.033816"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:43.213559" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:41.213356" elapsed="2.000203"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:43.222570" level="INFO">${total_row_count} = 11</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:43.213559" elapsed="0.009011"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:02:43.222570" level="INFO">${actual_row_count} = 10</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:02:43.222570" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:02:43.222570" elapsed="0.001022"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:02:43.223592" level="INFO">---- Verified 10 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:02:43.223592" elapsed="0.000000"/>
</kw>
<var name="${option}">10</var>
<status status="PASS" start="2024-10-29T12:02:39.870829" elapsed="3.352763"/>
</iter>
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:02:43.223592" elapsed="0.270286"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:43.493878" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:43.493878" elapsed="0.026373"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:44.520748" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:43.520251" elapsed="1.000497"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:02:44.521352" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='25']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:02:44.520748" elapsed="0.000604"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:02:44.521352" elapsed="0.016820"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:44.538172" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='25']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:44.538172" elapsed="0.034927"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:46.574394" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:44.574107" elapsed="2.000287"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:46.585403" level="INFO">${total_row_count} = 17</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:46.574394" elapsed="0.011009"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:02:46.586424" level="INFO">${actual_row_count} = 16</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:02:46.586424" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:02:46.586424" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:02:46.587450" level="INFO">---- Verified 25 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:02:46.586424" elapsed="0.001026"/>
</kw>
<var name="${option}">25</var>
<status status="PASS" start="2024-10-29T12:02:43.223592" elapsed="3.363858"/>
</iter>
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:02:46.587450" elapsed="0.265913"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:46.854380" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:46.854380" elapsed="0.029492"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:47.884110" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:46.883872" elapsed="1.000238"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:02:47.886635" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='100']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:02:47.884625" elapsed="0.002010"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:02:47.886635" elapsed="0.014137"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:47.901772" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='100']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:47.900772" elapsed="0.023238"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:49.924413" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:47.924010" elapsed="2.000403"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:49.935144" level="INFO">${total_row_count} = 17</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:49.925025" elapsed="0.010119"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:02:49.936145" level="INFO">${actual_row_count} = 16</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:02:49.936145" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:02:49.936145" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:02:49.936145" level="INFO">---- Verified 100 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:02:49.936145" elapsed="0.000000"/>
</kw>
<var name="${option}">100</var>
<status status="PASS" start="2024-10-29T12:02:46.587450" elapsed="3.348695"/>
</iter>
<var>${option}</var>
<value>@{OPTIONS}</value>
<status status="PASS" start="2024-10-29T12:02:36.510492" elapsed="13.425653"/>
</for>
<status status="PASS" start="2024-10-29T12:02:35.925725" elapsed="14.010420"/>
</kw>
<arg>Items per page feature</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:01:55.892719" elapsed="54.043426"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:02:49.937241" elapsed="0.004822"/>
</kw>
<status status="PASS" start="2024-10-29T12:02:49.937241" elapsed="0.004822"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:49.942063" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:02:49.942063" elapsed="0.030544"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:52.973814" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:49.972607" elapsed="3.001207"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:02:52.974422" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:02:53.046571" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:02:53.046571" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:02:52.974422" elapsed="0.074143">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:02:55.050051" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:02:53.049714" elapsed="2.000337"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:02:55.050051" elapsed="3.185778"/>
</kw>
<status status="FAIL" start="2024-10-29T12:02:49.942063" elapsed="8.293766">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:02:49.942063" elapsed="8.293766"/>
</kw>
<status status="PASS" start="2024-10-29T12:02:49.937241" elapsed="8.298588"/>
</kw>
<doc>Items per page feature</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:01:55.891507" elapsed="62.345453"/>
</test>
<doc>Testing teh istems per page feature</doc>
<status status="PASS" start="2024-10-29T12:01:55.190920" elapsed="63.047836"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:01:54.761676" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_122_Show_maximum_campaign_feature.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:01:55.859592" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:01:56.369940" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T12:02:14.542517" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:02:24.555250" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:02:29.568400" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
