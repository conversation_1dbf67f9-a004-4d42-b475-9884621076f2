*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation                                       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../keywords/controllers/GetDownloadManagementInformation_Keywords.robot
Resource                            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
GetDownloadManagementInformation
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${SERVER_VERSION}   ${EXPECTED_STATUS_CODE}   
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetDownloadManagementInformation using the server version & bin status action    ${BASE_URL}      ${SERVER_VERSION}   
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    

| *** Test Cases ***                                                      |        *DOCUMENTATION*    		                     |         *BASE_URL*          |  *SERVER_VERSION*      |  *EXPECTED_STATUS_CODE*   |
| GetDownloadManagementInformation   | GetDownloadManagementInformation   | Testing GetDownloadManagementInformation Controller  |                             |        13              |        200                |
