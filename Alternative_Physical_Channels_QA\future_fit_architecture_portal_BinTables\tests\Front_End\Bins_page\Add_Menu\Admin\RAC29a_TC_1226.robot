*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify if the correct result is returned for the search
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${INVALID_BIN_NUMBER}    ${EXPECTED_ERROR_MESSAGE_INVALID_BIN_NUMBER}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The user searches with an invalid Bin Number    ${INVALID_BIN_NUMBER}
    Then The user verifies that the expected error message is displayed for an invalid Bin Number input    ${EXPECTED_ERROR_MESSAGE_INVALID_BIN_NUMBER}

| *** Test Cases ***                                                                                                        |        *DOCUMENTATION*               |         *BASE_URL*                  |   *INVALID_BIN_NUMBER*          |   *EXPECTED_ERROR_MESSAGE_INVALID_BIN_NUMBER*  |         
| Admin_Error Message Displayed for Invalid BIN Number Format   | Verify if the correct result is returned for the search   | Testing the partial search feature   |           ${EMPTY}                  |        %%%%%%%%%%%%             |      303020                                    |        
