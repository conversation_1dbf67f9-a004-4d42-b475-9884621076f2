<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="******** 15:53:43.771" rpa="false" schemaversion="4">
<suite id="s1" name="TC 01 POST APPROVAL CONTROLLER" source="C:\development\future-fit-architecture-portal-docker\tests\Controllers\Approval\TC_01_POST_APPROVAL_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:44.430" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value 'Yaash.<PERSON><EMAIL>'.</msg>
<status status="PASS" starttime="******** 15:53:44.430" endtime="******** 15:53:44.430"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:44.430" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="******** 15:53:44.430" endtime="******** 15:53:44.430"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:44.430" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 15:53:44.430" endtime="******** 15:53:44.430"/>
</kw>
<status status="PASS" starttime="******** 15:53:44.430" endtime="******** 15:53:44.430"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Approve Marketing Campaign with a Business Approver" line="39">
<kw name="Approve marketing campaign">
<arg>Approves a Marketing Campaign with a Business Approver</arg>
<arg>155057487</arg>
<arg>Approval</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>Approval/Approve</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>campaignId= 14408</arg>
<arg>approvalTime=0</arg>
<arg>approvedBy= Yaash Ramsahaar (ZA)</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 15:53:44.442" level="INFO">Set test documentation to:
Approves a Marketing Campaign with a Business Approver</msg>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.442"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:44.442" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057487'.</msg>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.442"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.442"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:44.442" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.442"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:44.442" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.442"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:44.442" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.442"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 15:53:44.467" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.467"/>
</kw>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:44.467"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 15:53:44.477" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 15:53:44.477" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 15:53:44.467" endtime="******** 15:53:44.477"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 15:53:44.477" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 15:53:44.477" endtime="******** 15:53:44.477"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 15:53:44.477" endtime="******** 15:53:44.477"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Session Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:44.477" level="INFO">'Session Created!'</msg>
<status status="PASS" starttime="******** 15:53:44.477" endtime="******** 15:53:44.479"/>
</kw>
<status status="PASS" starttime="******** 15:53:44.467" endtime="******** 15:53:44.479"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:44.480" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 15:53:44.480" endtime="******** 15:53:44.480"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:44.480" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 15:53:44.480" endtime="******** 15:53:44.480"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:44.480" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 15:53:44.480" endtime="******** 15:53:44.480"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 15:53:44.494" level="INFO">${payload} = {'campaignId': 14408, 'approvalTime': '2024-05-28T15:53:44.450Z', 'approvedBy': 'Yaash Ramsahaar (ZA)'}</msg>
<status status="PASS" starttime="******** 15:53:44.480" endtime="******** 15:53:44.494"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${payload}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:44.494" level="INFO">{'campaignId': 14408, 'approvalTime': '2024-05-28T15:53:44.450Z', 'approvedBy': 'Yaash Ramsahaar (ZA)'}</msg>
<status status="PASS" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 15:53:44.494" level="INFO">${end_point} = /Approval/Approve</msg>
<status status="PASS" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</kw>
<status status="NOT RUN" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 15:53:44.494" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</kw>
<status status="PASS" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</branch>
<status status="PASS" starttime="******** 15:53:44.494" endtime="******** 15:53:44.494"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval/Approve 
 path_url=/Approval/Approve 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.V0Yy64REP9-PoZQxMJ-l_GlC3Y7Qt0pkDRBKxJQzky0Sj-FQRCXdtcj9_P_uVHNSlmXyHjKV-AZkmZ2DcsNKzYNMogkspskbxT2Q4ArloJrqwYiUjOgoDXLkSrdpTjf0AjjGwHA21zzp-haHm6T2Y7zw_KybQf1tSqLibYdYRoM9UtVOaf5A6t8Qf8yDQQCsA2kiOQq2ztfuQXV8V8bnV-gHgKu1mbMTSl4Rhr2NxsAmAQxTl7k2NqPYbeXpkCe96-vZ8mnuBSRZxvpSvQxlwyA3J9qz-h-QqCUbEZELIIe4WRiiSw5kfF44YwVaH7wj2zXenrJNHtQSC0kdpqF_gQ', 'Content-Length': '103'} 
 body=b'{"campaignId": 14408, "approvalTime": "2024-05-28T15:53:44.450Z", "approvedBy": "Yaash Ramsahaar (ZA)"}' 
 </msg>
<msg timestamp="******** 15:53:45.246" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval/Approve 
 status=200, reason=OK 
 headers={'Date': 'Tue, 28 May 2024 13:53:45 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=Approved successfully 
 </msg>
<msg timestamp="******** 15:53:45.246" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 15:53:45.246" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 15:53:44.494" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">Approved successfully</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">${response.content} = Approved successfully</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<status status="PASS" starttime="******** 15:53:44.480" endtime="******** 15:53:45.246"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:53:45.246" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<status status="PASS" starttime="******** 15:53:45.246" endtime="******** 15:53:45.246"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<kw name="Log" library="BuiltIn">
<arg>Response Message : ${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:45.261" level="INFO">Response Message : Approved successfully</msg>
<status status="PASS" starttime="******** 15:53:45.261" endtime="******** 15:53:45.261"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:45.261" level="INFO"/>
<status status="PASS" starttime="******** 15:53:45.261" endtime="******** 15:53:45.261"/>
</kw>
<kw name="Should Contain" library="BuiltIn">
<arg>As Strings ${response.content}</arg>
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 15:53:45.261" endtime="******** 15:53:45.261"/>
</kw>
<status status="PASS" starttime="******** 15:53:45.261" endtime="******** 15:53:45.261"/>
</kw>
<status status="PASS" starttime="******** 15:53:44.442" endtime="******** 15:53:45.261"/>
</kw>
<doc>Approves a Marketing Campaign with a Business Approver</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 15:53:44.430" endtime="******** 15:53:45.261"/>
</test>
<test id="s1-t2" name="FFT - Contollera - Approve Marketing Campaign with a Business User" line="40">
<kw name="Approve marketing campaign">
<arg>Approves a Marketing Campaign with a Business User</arg>
<arg>155057487</arg>
<arg>Approval</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>Approval/Approve</arg>
<arg>401</arg>
<arg>Unauthorized</arg>
<arg>campaignId= 14409</arg>
<arg>approvalTime=0</arg>
<arg>approvedBy= Thabo Benjamin Setuke (ZA)</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 15:53:48.675" level="INFO">Set test documentation to:
Approves a Marketing Campaign with a Business User</msg>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.675"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:48.675" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057487'.</msg>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.675"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.675"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:48.675" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.675"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:48.675" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.675"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:48.675" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.675"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 15:53:48.695" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.695"/>
</kw>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:48.695"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 15:53:48.698" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 15:53:48.698" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 15:53:48.695" endtime="******** 15:53:48.698"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 15:53:48.698" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 15:53:48.698" endtime="******** 15:53:48.698"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 15:53:48.698" endtime="******** 15:53:48.699"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Session Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:48.699" level="INFO">'Session Created!'</msg>
<status status="PASS" starttime="******** 15:53:48.699" endtime="******** 15:53:48.699"/>
</kw>
<status status="PASS" starttime="******** 15:53:48.695" endtime="******** 15:53:48.699"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:48.700" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 15:53:48.700" endtime="******** 15:53:48.700"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:48.700" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 15:53:48.700" endtime="******** 15:53:48.700"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:53:48.700" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 15:53:48.700" endtime="******** 15:53:48.700"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 15:53:48.709" level="INFO">${payload} = {'campaignId': 14409, 'approvalTime': '2024-05-28T15:53:48.679Z', 'approvedBy': 'Thabo Benjamin Setuke (ZA)'}</msg>
<status status="PASS" starttime="******** 15:53:48.700" endtime="******** 15:53:48.709"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${payload}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:48.709" level="INFO">{'campaignId': 14409, 'approvalTime': '2024-05-28T15:53:48.679Z', 'approvedBy': 'Thabo Benjamin Setuke (ZA)'}</msg>
<status status="PASS" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 15:53:48.709" level="INFO">${end_point} = /Approval/Approve</msg>
<status status="PASS" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<msg timestamp="******** 15:53:48.709" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthY...</msg>
<status status="PASS" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</kw>
<status status="PASS" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</kw>
<status status="NOT RUN" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</branch>
<status status="PASS" starttime="******** 15:53:48.709" endtime="******** 15:53:48.709"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval/Approve 
 path_url=/Approval/Approve 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyIsImtpZCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FI26FGo9WE2UvoEncNGM1-63BboD54HmUGkgw_UF2TVGc8-C_0Wziv0KXECaaU-ExQNli2I5ZD4qgamPXa_daS9DVUmkurKQuRCSv_FV811I4ZkDCCYxVyl4G_U2Cbp82Wx9X7Xojur6lOkmu8Mo7F2YZ3gENVyF1httwzYPHFJ3qFJ6jQD7UtNDrG3D5IYZQbA06dRtu0Oo6AQq54EYMplf1SvpX0_nA6trvVsF6V9U09lbtYp6vjAYzebkWtBrNkNhojhVvZeKsgPruGCoPjPLkswCwb4dlKTC2mBeCapiHc3boy7RH0-tvLbnExqqGcW377ah9873_lUqRIBc4g', 'Content-Length': '109'} 
 body=b'{"campaignId": 14409, "approvalTime": "2024-05-28T15:53:48.679Z", "approvedBy": "Thabo Benjamin Setuke (ZA)"}' 
 </msg>
<msg timestamp="******** 15:53:50.105" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval/Approve 
 status=401, reason=Unauthorized 
 headers={'Date': 'Tue, 28 May 2024 13:53:49 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'WWW-Authenticate': 'Bearer error="invalid_token", error_description="The token expired at \'02/12/2024 07:20:36\'"', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=None 
 </msg>
<msg timestamp="******** 15:53:50.105" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 15:53:50.105" level="INFO">${response} = &lt;Response [401]&gt;</msg>
<status status="PASS" starttime="******** 15:53:48.709" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">Approved successfully</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '401'.</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Unauthorized'.</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">${response.content} = Approved successfully</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<status status="PASS" starttime="******** 15:53:48.700" endtime="******** 15:53:50.105"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">${returned_status_code} = 401</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">Response Status Code : 401</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">${returned_status_reason} = Unauthorized</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<kw name="Log" library="BuiltIn">
<arg>Response Message : ${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO">Response Message : Approved successfully</msg>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 15:53:50.105" level="INFO"/>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<kw name="Should Contain" library="BuiltIn">
<arg>As Strings ${response.content}</arg>
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<status status="PASS" starttime="******** 15:53:50.105" endtime="******** 15:53:50.105"/>
</kw>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:50.105"/>
</kw>
<doc>Approves a Marketing Campaign with a Business User</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 15:53:48.675" endtime="******** 15:53:50.105"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 15:53:43.926" endtime="******** 15:53:54.408"/>
</suite>
<statistics>
<total>
<stat pass="2" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="2" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="2" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="2" fail="0" skip="0" id="s1" name="TC 01 POST APPROVAL CONTROLLER">TC 01 POST APPROVAL CONTROLLER</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 15:53:48.675" level="ERROR">Calling method 'end_test' of listener 'C:\development\future-fit-architecture-portal-docker\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 15:53:54.398" level="ERROR">Calling method 'end_test' of listener 'C:\development\future-fit-architecture-portal-docker\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
