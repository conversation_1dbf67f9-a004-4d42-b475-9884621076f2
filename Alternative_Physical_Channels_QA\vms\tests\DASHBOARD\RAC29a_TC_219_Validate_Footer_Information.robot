*** Settings ***
#Author Name               : Ya<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Validating footer information on the Dashboard

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../../utility/DatabaseUtility.py
Library                                             ../../utility/Common_Functions.py
Library                                             ../../keywords/common/DBUtility.robot
Library                                             String


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot 
Resource                                            ../../../common_utilities/Logout.robot    
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keyword ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION} 

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}  

    And The user lands on the dashboard page

    Then The user validates footer information on the Dashboard


| *Test Case*                                                            |                *DOCUMENTATION*                   |     *TEST_ENVIRONMENT*   |        
| Validate footer information on the Dashboard | Dashboard Validation    | Validating footer information on the Dashboard   |      VMS_UAT             |
