import json
import re
from typing import Union, List

from robot.api.deco import keyword


class Reject:
    def __init__(self, **kwargs):
        """
            The constructor initializes the class with a dictionary of bin and processes it to generate
            the necessary data for the JSON request. It extracts the bin id, bin id, outcome, and rejection comment for each bin,
            regardless of how many bins are passed or their indices.

            Arguments:

            kwargs: A dictionary containing the bin data, where keys represent the bin (e.g., bin1, bin2, etc.)
            and values represent the bin number. The dictionary should also include dateX and binIdsX for each bin.

            bins = {
                'binId1': 'e57287b8-319c-4475-823d-0201589a7f74',
                'binOutcome1': 'Added',
                'rejectionComment1': 'This bin must be deleted.'
            }
            generator = Update(bins)

            """

        # Initialize bins as an empty list
        self.bins = {}

        # Loop through all bins keys and create bin data
        for key, value in kwargs.items():
            # Check if the key starts with 'bin' (e.g., 'bin1', 'bin2', ...)
            if key.upper().startswith("BINID"):
                # Extract the bin index using regex (to allow for any number format)
                match = re.match(r"binId(\d+)", key)
                if match:
                    bin_index = match.group(1)  # Extract the index as string (e.g., '1', '2', '123')

                    # Now, retrieve associated data for this bin
                    bin_outcome = kwargs.get(
                        f"outcome{bin_index}")  # Get the corresponding date (e.g., 'date1', 'date2')
                    bin_rejection_comment = kwargs.get(
                        f"rejectionComment{bin_index}")  # Get the corresponding bin outcome (e.g., 'binOutcome1', 'binOutcome2')

                    # Append bin data
                    self.bins = {
                        "binId": value,  # binId1, binId2, ... contain the bin id
                        "outcome": bin_outcome,
                        "rejectionComment": bin_rejection_comment
                    }

    def get_json_request(self):
        # Return the bins in JSON format
        return json.dumps(self.bins, indent=4)

    @keyword
    def create_bin_reject_request(self, **kwargs):
        """
        This method processes the bins and returns the JSON request.
        """
        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def get_reject_bin_endpoint(self, domain):
        path = "/api/v1/bintables/review/bins/reject"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers
