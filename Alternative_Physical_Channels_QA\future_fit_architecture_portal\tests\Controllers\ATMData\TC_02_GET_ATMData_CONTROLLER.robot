*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFT_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No
${BROWSER}                  chrome



*** Keywords ***
GET ATM DATA
    [Arguments]        ${DOCUMENTATION}     ${TESTRAIL_TESTCASE_ID}     ${DATA_FILE}  ${BASE_URL}     ${SERVICE_PATH}   ${SERVICE_PATH_ID}   ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}      &{EXPECTED_FIELDS_VALUES}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable                    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user creates a rest session                               ${BASE_URL}
    When The user makes Get Rest Call                                   ${SERVICE_PATH}     ${SERVICE_PATH_ID}     ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                                ${EXPECTED_STATUS_CODE}  ${JSON_RESPONSE_REASON}
    Then The rest service must return the response which contains       &{EXPECTED_FIELDS_VALUES}

| *** Test Cases ***                                                                                              |               *DOCUMENTATION*                    |  *TESTRAIL_TESTCASE_ID*  |      *DATA_FILE*                           | *BASE_URL*                    | *SERVICE_PATH*                    | *SERVICE_PATH_ID*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON*          | *EXPECTED_FIELDS_VALUES*          |
| FFT - Controllers - Get ATM DATA using a valid auth token       | GET ATM DATA                           |             Gets ATMs Details using valid auth   |      155057478	         |                                            | APC_API_UAT_BASE_URL          | ATMData                           |                                      | 200                              | OK                              | [*]:atmNumber=S08397 | [*]:model=31729758 |
| FFT - Controllers - Get ATM DATA using an expired auth token    | GET ATM DATA                           |             Gets ATMs Details using invalid auth |      155057478	         |                                            | APC_API_UAT_BASE_URL          | ATMData                           |                                      | 401                              | Unauthorized                    |                                   |
