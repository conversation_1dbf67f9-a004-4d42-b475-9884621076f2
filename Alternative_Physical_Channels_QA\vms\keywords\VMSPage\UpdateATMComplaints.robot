*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Update complaint status

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py

#***********************************PROJECT RESOURCES***************************************
*** Variables ***
${STATUS_DROPDOWN}                                  id~updSelectStatus                        
${COMMENT_INPUT}                                    id=txtUpdStatusComment

${CANCEL_BTN}                                       xpath=//input[@value='Cancel']
${UPDATE_BTN}                                       id=btnUpdateCall
 
${CONFIRM_MESSAGE}                                  id=ConfirmMsg
${OK_BTN}                                           id=MainContent_btnSubmitComplete

*** Keywords ***
User selects status from the dropdown
    [Arguments]  ${NEW_STATUS}
    Log to console  --------------------------User selects status from the dropdown

    Wait until Page Contains                        Update - ATM Complaints and Compliments

	Sleep  5s
    
    Capture page screenshot  Update_ATM_C_and_C_before.png
    
    #Statuses that are availble for selection in the dropdown
    Log to console  ******************Get available statuses from the dropdown

    ${dropdown_values}=  Get Text                   id=updSelectStatus

    Log to console  New status(As per the test case): ${NEW_STATUS}

    ${Result}=                                         Page Should Contain Element           xpath=//select[@id='updSelectStatus']/option[contains(text(),'${NEW_STATUS}')]  message=Status ${NEW_STATUS} could not be found in the dropdown, found statuses were ${dropdown_values}
    Run Keyword Unless  '${RESULT}'=='PASS'  Select from the drop down  ${STATUS_DROPDOWN}  ${NEW_STATUS}

    ${value}=  get text from dropdown               ${STATUS_DROPDOWN}  

    Log to console   Status currently shown in the 'Select status' dropdown: ${value} ${\n}
  
    #Validate if the status was successfully selected. 
    #If not fail the test case because it might be that the statuses are not mapped correctly
    Should Not Be Equal As Strings  ${value}  Select Status  msg=The status was not propperly selected  ignore_case=False
    
    Sleep  5s

User enters a comment
	[Arguments]    ${COMMENT}
    Log to console  --------------------------User enters a comment

    Sleep  6s

    input text  									${COMMENT_INPUT}  ${COMMENT} 

    Capture page screenshot  Update_ATM_C_and_C_after.png

Select from the drop down
    [Arguments]  ${STATUS_DROPDOWN}  ${STATUS}
    Log to console  --------------------------User selects status from the dropdown
    
    perform dropdown select             			${STATUS_DROPDOWN}  ${STATUS}

User clicks on Submit button
    Log to console  --------------------------User clicks on Submit button

    Click Element                                   id=btnUpdateCall

    