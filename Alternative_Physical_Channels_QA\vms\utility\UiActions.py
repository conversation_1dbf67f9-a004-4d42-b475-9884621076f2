from robot.libraries.BuiltIn import BuiltIn
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException, \
    ElementNotVisibleException
from selenium.webdriver.support.select import Select
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from SeleniumLibrary.base import keyword

class UiActions(object):

    @property
    def _s2l(self):
        return BuiltIn().get_library_instance('SeleniumLibrary')

    @property
    def _driver(self):
        return self._s2l._current_browser()

    @keyword
    def perform_dropdown_select(self,Objname,str_dropdownvalue):
        print("PPPPPPPPPPPP@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@PPPPPPPPPPPPPPPPPPPPP")
        print("Obj: ", Objname,"\n")
        print("Obj: ", str_dropdownvalue,"\n")
        print(str_dropdownvalue)
        arr_objectname=Objname.split("~")
        select =None
        if arr_objectname[0]=="id":
            dropdown_element=self._driver.find_element("id",arr_objectname[1])
            select = Select(dropdown_element)
            
            select.select_by_visible_text(str_dropdownvalue)
        elif arr_objectname[0]=="xpath":
            select = Select(self._driver.find_element("xpath",arr_objectname[1]))
            select.select_by_visible_text(str_dropdownvalue)
        elif arr_objectname[0]=="css":
            select = Select(self._driver.find_element("css",arr_objectname[1]))
            select.select_by_visible_text(str_dropdownvalue)
    @keyword
    def enter_text_to_field(self,obj_name,value):
        arr_objectname = obj_name.split("~")
        if (arr_objectname[0] == "name"):
            self._driver.find_element("name",arr_objectname[1]).send_keys(value)
        elif (arr_objectname[0] == "xpath"):
            self._driver.find_element("xpath",arr_objectname[1]).send_keys(value)
        elif (arr_objectname[0] == "id"):
            self._driver.find_element("id",arr_objectname[1]).send_keys(value)
    @keyword
    def get_table_row(self,locator):
        tbl_compliant_history = self._driver.find_elements("xpath","//div[@id='Complaint_Det']//*/tbody/tr")
        
        # test =  tbl_compliant_history[0].find_element(by=By.XPATH, value=".//td[1]").text 
        update_type =  tbl_compliant_history[0].find_element(by=By.XPATH, value=".//td[2]").text 
        complaint_comment =  tbl_compliant_history[0].find_element(by=By.XPATH, value=".//td[3]").text 
        complaint_status =  tbl_compliant_history[0].find_element(by=By.XPATH, value=".//td[4]").text 
        row = update_type + " | " + complaint_comment + " | " + complaint_status
        return row
            
    @keyword
    def get_text_from_dropdown(self,obj_name):
        arr_objectname = obj_name.split("~")
        if (arr_objectname[0] == "id"):
            select = Select(self._driver.find_element("id",arr_objectname[1]))
            selected_option = select.first_selected_option
            return selected_option.text
        elif (arr_objectname[0] == "xpath"):
            select = Select(self._driver.find_element("xpath",arr_objectname[1]))
            selected_option = select.first_selected_option
            return selected_option.text
        elif (arr_objectname[0] == "name"):
            select = Select(self._driver.find_element("name",arr_objectname[1]))
            selected_option = select.first_selected_option
            return selected_option.text

    @keyword
    def click_on_element(self,obj_name):
        arr_objectname = obj_name.split("~")
        if (arr_objectname[0] == "name"):
            self._driver.find_element("name",arr_objectname[1]).click()
        elif (arr_objectname[0] == "xpath"):
            self._driver.find_element("xpath",arr_objectname[1]).click()
        elif (arr_objectname[0] == "id"):
            self._driver.find_element("id",arr_objectname[1]).click()

    #***************************Enter Value into an input field***************************************************
    def enter_text(self,locator,input_value):
        arr_objectname = locator.split("~")
        input_field=""
        if arr_objectname[0] == "name":
            input_field = self._driver.find_element("name",arr_objectname[1])
        elif arr_objectname[0] == "xpath":
            input_field = self._driver.find_element("xpath",arr_objectname[1])
        elif arr_objectname[0] == "css":
            input_field = self._driver.find_element("css",arr_objectname[1])

        input_field.click()
        input_field.send_keys(input_value)

    @keyword
    def table_search(self):

        article_elements = self._driver.find_elements("xpath","//label[text()='Complaint History']//following-sibling::div//table[@class='table gs-table']/tbody/tr")
        
        rows_lst = []
        counter=1

        for article in article_elements:

            date = article.find_element("xpath",'.//td[1]').text

            update_type = article.find_element("xpath",'.//td[2]').text

            comments = article.find_element("xpath",'.//td[3]').text

            status = article.find_element("xpath",'.//td[4]').text

            updated_by = article.find_element("xpath",'.//td[5]').text

            rows_lst.insert(counter,{'date':date,'update_type':update_type,'comments':comments,'status':status,'updated_by':updated_by})
            
            print('Counter is ', counter, ' and value is ', article.text)
            counter = counter + 1
            return rows_lst


