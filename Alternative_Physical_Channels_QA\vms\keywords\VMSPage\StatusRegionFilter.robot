*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Test filtering

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Resource                                             LandingPage.robot
#***********************************PROJECT RESOURCES***************************************
*** Variables ***
${REGION_DROPDOWN}                                  id~selectRegion
${STATUS2_DROPDOWN}                                  id~selectStatus

*** Keywords ***
User filter by region
    [Arguments]  ${REGION}
    Log to console  --------------------------Filtering by region
    
    Wait Until Page Contains    QR Code Complaints and Compliments

    perform dropdown select  ${REGION_DROPDOWN}  ${REGION}
 
    Wait Until Element Is Enabled     id=btnDetails

    User clciks on details link
    
    Wait Until Element Is Visible    id=detailTitle

    User views complaint/compliment region  ${REGION}

User filter by status
    [Arguments]  ${STATUS}
    Log to console  --------------------------Filtering by status

    Wait Until Page Contains    QR Code Complaints and Compliments

    perform dropdown select  ${STATUS2_DROPDOWN}  ${STATUS}

    Wait Until Element Is Enabled     id=btnDetails

    User clciks on details link
    
    Wait Until Element Is Visible    id=detailTitle

    User views complaint/compliment status  ${STATUS}
    

User views complaint/compliment region
    [Arguments]  ${REGION}

    User validates that the task shown belongs to the selected region  ${REGION}

User views complaint/compliment status
    [Arguments]  ${STATUS}
    
    User validates that the task shown belongs to the selected status  ${STATUS}    

User validates that the task shown belongs to the selected region
    [Arguments]  ${REGION}

    Page Should Contain    ${REGION}


User validates that the task shown belongs to the selected status
    [Arguments]  ${STATUS}
    Page Should Contain    ${STATUS}