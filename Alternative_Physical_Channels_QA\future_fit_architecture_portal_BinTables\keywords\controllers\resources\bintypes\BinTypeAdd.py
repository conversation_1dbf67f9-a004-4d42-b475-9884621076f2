import requests

class BinTypeAdd:
    def __init__(self, base_url):
        self.base_url = base_url

    def create_bin_type(self, bin_type_name, bin_type_details):
        #Concatenate the base URL with the relative path
        full_url = f"{self.base_url}/api/v1/bintables/BINtypes/add"
        print(f"Final URL: {full_url}")

        #Define the data and headers (Part of the body request)
        data = {"bin_type_name": bin_type_name, "bin_type_details": bin_type_details}
        headers = {"Content-Type": "application/json"}

        #Perform the POST request
        response = requests.post(full_url, json=data, headers=headers, verify=False)

        return response