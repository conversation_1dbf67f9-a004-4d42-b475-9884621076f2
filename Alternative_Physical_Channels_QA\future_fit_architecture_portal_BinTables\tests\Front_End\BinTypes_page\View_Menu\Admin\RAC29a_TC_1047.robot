*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1047




*** Keywords ***
Search for Bins linked to a Bin Type
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TYPE_TO_VERIFY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal         ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user selects a Bin Type on the View Bin Types Page                                                      ${BIN_TYPE_TO_VERIFY}
    Then The selected Bin Type Page which contains linked Bins is be displayed                                       ${BIN_TYPE_TO_VERIFY}

| *** Test Cases ***                                                                                       |        *DOCUMENTATION*    		        |         *BASE_URL*                  |         *BIN_TYPE_TO_VERIFY*           |
| Admin_Access View BINtype Menu from BinTable Landing Page    | Search for Bins linked to a Bin Type   | Search for Bins linked to a Bin Type.   |           ${EMPTY}                  |              Domestic                  |
