*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Calendar Management Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                            RequestsLibrary
Library                                            Collections
Library                                            String
Library                                             DatabaseLibrary
Library                                          ../utility/DatabaseUtility.py
Library                                            DateTime

#***********************************PROJECT RESOURCES***************************************


*** Variables ***
${Add_Button}                          //a[@data-toggle='ajax-modal' and @data-target='#Modal' and @data-url='/ATMControl/AddCalendar']/b
${Defaulted_Date_Selection_Locator}    //*[@id='ReconDate']
${Public_Holiday_Description_Field}    //*[@id='PublicHolidayDescription']
${Public_Holiday_Description}          Testing Adding a Public Holiday Flow
${SAVE_ACTION_BUTTON}                  //button[@data-save='modal'] 
${SQL_ADD_NEW_PUBLIC_HOLIDAY}          SELECT TOP (1000) [Id] ,[ReconDate] ,[PublicHolidayDescription] ,[IsActive] ,[CreatedDate] ,[UpdatedDate] ,[CreatedUserId] ,[UpdatedUserId] FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_T_Calendars] Order By CreatedDate Desc
${CLOSE_BUTTON}                        //*[@type='button'][text()[normalize-space(.)='Close']]

*** Keywords ***
the user clicks the add button on the ATM Control Calendar Management
    Sleep    2s
    Click Element    ${Add_Button}
    Sleep    3s
    Page Should Contain    Public Holiday Description

The user inputs a date and Public Holiday Description
    ${Public_Holiday_Date}=    Get Value    ${Defaulted_Date_Selection_Locator}
    Log    The Date for this Public Holiday Entry is:${Public_Holiday_Date}
    Set Global Variable    ${Public_Holiday_Date}

    Input Text    ${Public_Holiday_Description_Field}    ${Public_Holiday_Description}
    Sleep    3s

The user clicks the save button 
    Click Element    ${SAVE_ACTION_BUTTON}
    Sleep    5s

The user clicks the Close button to cancel 
    Click Element    ${CLOSE_BUTTON}
    Sleep    5s

The user verifies that the flow is cancelled and they are redirected to the ATM Control Calendar Management page
    Page Should Contain    ATM Control Calendar Management

The User verifies that the Public Holiday was saved to the database
    #DATABASE VALIDATION
    ${Database_Added_New_Public_Holiday}=    DatabaseUtility.Execute Sql Query        ${SQL_ADD_NEW_PUBLIC_HOLIDAY}   
    Log To Console    ${Database_Added_New_Public_Holiday}

    #Extract database data
    ${New_Public_Holiday}=    Get From List    ${Database_Added_New_Public_Holiday}    0

    ${DATABASE_ADDED_PUBLIC_HOLIDAY_DATE}=    Get From Dictionary    ${New_Public_Holiday}    ReconDate
    Log    Public Holiday Date on Database is:${DATABASE_ADDED_PUBLIC_HOLIDAY_DATE}
    ${Trimmed_Public_Holiday_Date_From_DB}=    Evaluate    str("${DATABASE_ADDED_PUBLIC_HOLIDAY_DATE}").split('.')[0]
    ${Formatted_Public_Holiday_Date_From_DB}=    Convert Date    ${Trimmed_Public_Holiday_Date_From_DB}    result_format=%Y-%m-%d
    Log To Console    Formatted Database Public Holiday Date: ${Formatted_Public_Holiday_Date_From_DB}

    ${DATABASE_ADDED_PUBLIC_HOLIDAY_DESCRIPTION}=    Get From Dictionary    ${New_Public_Holiday}    PublicHolidayDescription
    Log    Public Holiday Description on Database is:${DATABASE_ADDED_PUBLIC_HOLIDAY_DESCRIPTION}
    
    ${DATABASE_ADDED_PUBLIC_HOLIDAY_ACTIVE_STATUS}=    Get From Dictionary    ${New_Public_Holiday}    IsActive
    Log    Public Holiday Active Status on Database is:${DATABASE_ADDED_PUBLIC_HOLIDAY_ACTIVE_STATUS}

    Should Be Equal    ${Public_Holiday_Date}    ${Formatted_Public_Holiday_Date_From_DB}
    Should Be Equal    ${Public_Holiday_Description}    ${DATABASE_ADDED_PUBLIC_HOLIDAY_DESCRIPTION}
    Should Be True     ${DATABASE_ADDED_PUBLIC_HOLIDAY_ACTIVE_STATUS}

    Log    The public Holiday was sucessfully saved to the database...