<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="******** 12:53:18.846" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\development\future-fit-architecture-portal-docker\tests\TC_01_POST_APPROVE_CAMPAIGN_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Approve Marketing Campaign with a Business Approver" line="43">
<kw name="Approve marketing campaign">
<arg>Approves a Marketing Campaign with a Business Approver</arg>
<arg>155057487</arg>
<arg>Approval</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>Approval</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>campaignId= 14210</arg>
<arg>approvalTime=0</arg>
<arg>approvedBy= Thabo Benjamin Setuke (ZA)</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">Set test documentation to:
Approves a Marketing Campaign with a Business Approver</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057487'.</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:19.327" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.327"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 12:53:19.364" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.364"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.364"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:53:19.364" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:53:19.364" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:53:19.364" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:19.364" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:19.364" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:19.364" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:19.364" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.364"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 12:53:19.379" level="INFO">${payload} = {'campaignId': 14210, 'approvalTime': '2024-03-12T12:53:19.347Z', 'approvedBy': 'Thabo Benjamin Setuke (ZA)'}</msg>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.379"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 12:53:19.379" level="INFO">${end_point} = /Approval</msg>
<status status="PASS" starttime="******** 12:53:19.379" endtime="******** 12:53:19.379"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:53:19.379" endtime="******** 12:53:19.379"/>
</kw>
<status status="NOT RUN" starttime="******** 12:53:19.379" endtime="******** 12:53:19.379"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 12:53:19.379" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvO...</msg>
<status status="PASS" starttime="******** 12:53:19.379" endtime="******** 12:53:19.379"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.379" endtime="******** 12:53:19.379"/>
</branch>
<status status="PASS" starttime="******** 12:53:19.379" endtime="******** 12:53:19.379"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:53:19.767" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 path_url=/Approval 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSIsImtpZCI6IlhSdmtvOFA3QTNVYVdTblU3Yk05blQwTWpoQSJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ROe4HCono7xXRXYqdWg8H4gHjxLOQ5AE374-nP-umUwM1DiPlPvrBEGXQgqU_ufhQrUMeXdAhRmys9EG8YoRopccViOVA6pKpLr_pPPrwQ8vEfwuU4wa2M3TXGZJsq4ALIGmIHlt-5jc5z43OcpluhFD03qEu9SBqQNpTBGN2zcfhzJZClywqHoKe2W79Bh0VbFnJekwIusU_UVQ9bnSQWJN_fftuWHvunj4NvCZdeeHOGd24lcPy5-rvxWCPOumzViTe9dS5dldXXKZJWtkkMjdp6uLLVLOnszPiDH2bVPsEEdcSQZN7PQnRJsa0X0zshsWVMATHLzmKjuXPW1GkA', 'Content-Length': '109'} 
 body=b'{"campaignId": 14210, "approvalTime": "2024-03-12T12:53:19.347Z", "approvedBy": "Thabo Benjamin Setuke (ZA)"}' 
 </msg>
<msg timestamp="******** 12:53:19.767" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 status=200, reason=OK 
 headers={'Date': 'Tue, 12 Mar 2024 10:53:19 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=Approved successfully 
 </msg>
<msg timestamp="******** 12:53:19.767" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:53:19.782" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 12:53:19.379" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">Approved successfully</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">${response.content} = Approved successfully</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.364" endtime="******** 12:53:19.782"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<kw name="Log" library="BuiltIn">
<arg>Response Message : ${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO">Response Message : Approved successfully</msg>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:19.782" level="INFO"/>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<kw name="Should Contain" library="BuiltIn">
<arg>As Strings ${response.content}</arg>
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.782" endtime="******** 12:53:19.782"/>
</kw>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.782"/>
</kw>
<doc>Approves a Marketing Campaign with a Business Approver</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 12:53:19.327" endtime="******** 12:53:19.782"/>
</test>
<test id="s1-t2" name="FFT - Contollera - Approve Marketing Campaign with a Business User" line="44">
<kw name="Approve marketing campaign">
<arg>Approves a Marketing Campaign with a Business User</arg>
<arg>155057487</arg>
<arg>Approval</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>Approval</arg>
<arg>401</arg>
<arg>Unauthorized</arg>
<arg>campaignId= 14210</arg>
<arg>approvalTime=0</arg>
<arg>approvedBy= Thabo Benjamin Setuke (ZA)</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 12:53:22.461" level="INFO">Set test documentation to:
Approves a Marketing Campaign with a Business User</msg>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.461"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:22.461" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057487'.</msg>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.461"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.461"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:22.461" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.461"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:22.461" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.461"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:22.461" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.461"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 12:53:22.495" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.495"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.495"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 12:53:22.498" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 12:53:22.498" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 12:53:22.495" endtime="******** 12:53:22.498"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 12:53:22.498" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 12:53:22.498" endtime="******** 12:53:22.498"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 12:53:22.498" endtime="******** 12:53:22.498"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:22.498" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 12:53:22.498" endtime="******** 12:53:22.498"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.495" endtime="******** 12:53:22.498"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:22.499" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 12:53:22.499" endtime="******** 12:53:22.499"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:22.499" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 12:53:22.499" endtime="******** 12:53:22.499"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 12:53:22.500" level="INFO">${path} = data/Approval.json</msg>
<status status="PASS" starttime="******** 12:53:22.499" endtime="******** 12:53:22.500"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 12:53:22.510" level="INFO">${payload} = {'campaignId': 14210, 'approvalTime': '2024-03-12T12:53:22.480Z', 'approvedBy': 'Thabo Benjamin Setuke (ZA)'}</msg>
<status status="PASS" starttime="******** 12:53:22.500" endtime="******** 12:53:22.510"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 12:53:22.510" level="INFO">${end_point} = /Approval</msg>
<status status="PASS" starttime="******** 12:53:22.510" endtime="******** 12:53:22.510"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<msg timestamp="******** 12:53:22.511" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthY...</msg>
<status status="PASS" starttime="******** 12:53:22.511" endtime="******** 12:53:22.511"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.511" endtime="******** 12:53:22.511"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 12:53:22.511" endtime="******** 12:53:22.511"/>
</kw>
<status status="NOT RUN" starttime="******** 12:53:22.511" endtime="******** 12:53:22.511"/>
</branch>
<status status="PASS" starttime="******** 12:53:22.510" endtime="******** 12:53:22.511"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 12:53:22.684" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 path_url=/Approval 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyIsImtpZCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FI26FGo9WE2UvoEncNGM1-63BboD54HmUGkgw_UF2TVGc8-C_0Wziv0KXECaaU-ExQNli2I5ZD4qgamPXa_daS9DVUmkurKQuRCSv_FV811I4ZkDCCYxVyl4G_U2Cbp82Wx9X7Xojur6lOkmu8Mo7F2YZ3gENVyF1httwzYPHFJ3qFJ6jQD7UtNDrG3D5IYZQbA06dRtu0Oo6AQq54EYMplf1SvpX0_nA6trvVsF6V9U09lbtYp6vjAYzebkWtBrNkNhojhVvZeKsgPruGCoPjPLkswCwb4dlKTC2mBeCapiHc3boy7RH0-tvLbnExqqGcW377ah9873_lUqRIBc4g', 'Content-Length': '109'} 
 body=b'{"campaignId": 14210, "approvalTime": "2024-03-12T12:53:22.480Z", "approvedBy": "Thabo Benjamin Setuke (ZA)"}' 
 </msg>
<msg timestamp="******** 12:53:22.684" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Approval 
 status=401, reason=Unauthorized 
 headers={'Date': 'Tue, 12 Mar 2024 10:53:22 GMT', 'Content-Length': '0', 'Connection': 'keep-alive', 'WWW-Authenticate': 'Bearer error="invalid_token", error_description="The token expired at \'02/12/2024 07:20:36\'"', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body=None 
 </msg>
<msg timestamp="******** 12:53:22.684" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1099: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings
  warnings.warn(</msg>
<msg timestamp="******** 12:53:22.686" level="INFO">${response} = &lt;Response [401]&gt;</msg>
<status status="PASS" starttime="******** 12:53:22.511" endtime="******** 12:53:22.686"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:22.687" level="INFO">Approved successfully</msg>
<status status="PASS" starttime="******** 12:53:22.687" endtime="******** 12:53:22.687"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:22.689" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '401'.</msg>
<status status="PASS" starttime="******** 12:53:22.687" endtime="******** 12:53:22.689"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 12:53:22.691" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Unauthorized'.</msg>
<status status="PASS" starttime="******** 12:53:22.689" endtime="******** 12:53:22.691"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 12:53:22.691" level="INFO">${response.content} = Approved successfully</msg>
<status status="PASS" starttime="******** 12:53:22.691" endtime="******** 12:53:22.691"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.499" endtime="******** 12:53:22.692"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:53:22.693" level="INFO">${returned_status_code} = 401</msg>
<status status="PASS" starttime="******** 12:53:22.692" endtime="******** 12:53:22.693"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:22.693" level="INFO">Response Status Code : 401</msg>
<status status="PASS" starttime="******** 12:53:22.693" endtime="******** 12:53:22.693"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 12:53:22.693" endtime="******** 12:53:22.693"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 12:53:22.694" endtime="******** 12:53:22.694"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 12:53:22.695" level="INFO">${returned_status_reason} = Unauthorized</msg>
<status status="PASS" starttime="******** 12:53:22.694" endtime="******** 12:53:22.695"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 12:53:22.695" endtime="******** 12:53:22.695"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.692" endtime="******** 12:53:22.695"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<kw name="Log" library="BuiltIn">
<arg>Response Message : ${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:22.696" level="INFO">Response Message : Approved successfully</msg>
<status status="PASS" starttime="******** 12:53:22.696" endtime="******** 12:53:22.696"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 12:53:22.697" level="INFO"/>
<status status="PASS" starttime="******** 12:53:22.696" endtime="******** 12:53:22.697"/>
</kw>
<kw name="Should Contain" library="BuiltIn">
<arg>As Strings ${response.content}</arg>
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 12:53:22.697" endtime="******** 12:53:22.697"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.695" endtime="******** 12:53:22.697"/>
</kw>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.697"/>
</kw>
<doc>Approves a Marketing Campaign with a Business User</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 12:53:22.461" endtime="******** 12:53:22.698"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 12:53:18.961" endtime="******** 12:53:25.438"/>
</suite>
<statistics>
<total>
<stat pass="2" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="2" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="2" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="2" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
