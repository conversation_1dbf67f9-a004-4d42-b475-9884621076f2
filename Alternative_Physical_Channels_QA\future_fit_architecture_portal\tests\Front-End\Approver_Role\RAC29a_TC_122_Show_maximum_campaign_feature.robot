*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing teh istems per page feature

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../common_utilities/Login.robot
Resource                                            ../../../../common_utilities/Logout.robot
Resource                                            ../../../Keywords/atm_marketing/Dashboard.robot
Resource                                            ../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/atm_marketing/NegativeScenarios.robot
Resource                                            ../../../keywords/common/Navigation.robot
Resource                                            ../../../keywords/atm_marketing/Approvals.robot

*** Keyword ***
Validating the items per page feature on Campaign Approvals
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    When The user navigates to the Campaign Approvals page   

    And The user filters campaigns with items per page feature
    
| *Test Cases*                                                                                                  |      *DOCUMENTATION*      |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   | 
| RAC29a_TC_122_Show_maximum_campaign_feature   |  Validating the items per page feature on Campaign Approvals  |  Items per page feature   |    BUSINESS_APPROVER       |     APC_UAT             |