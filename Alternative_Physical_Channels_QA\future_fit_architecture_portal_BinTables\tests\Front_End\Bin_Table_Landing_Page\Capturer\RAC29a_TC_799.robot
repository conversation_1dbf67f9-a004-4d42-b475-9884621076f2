*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../keywords/front_end/Bin_Table_Landing_Page.robot


*** Variables ***
${SUITE NAME}               BIN Table Landing Page 




*** Keywords ***
Verify BINtypes Panel is Accessible
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The user clicks on the Bin Type Menu 
    Then The user verfies that the Bin Type menu is Accessible


| *** Test Cases ***                                                                          |        *DOCUMENTATION*     |         *BASE_URL*                  |         
| Capturer_Verify BINtypes Panel is Accessible        | Verify BINtypes Panel is Accessible   | Menu Navigation            |           ${EMPTY}                  |           
