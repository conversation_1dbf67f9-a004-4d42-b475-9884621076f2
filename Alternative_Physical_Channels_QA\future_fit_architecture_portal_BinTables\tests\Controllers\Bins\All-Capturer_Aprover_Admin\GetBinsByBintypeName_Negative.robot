*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinsByBintypeName_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot

#Run the script
#robot -d reports/controllers tests/TC_01_GET_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_TYPE_NAME}   ${BIN_NUMBER}  ${EXPECTED_STATUS_CODE}   ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetBinsByBintypeName                 ${BASE_URL}      ${BIN_TYPE_NAME}      ${BIN_NUMBER}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed                           ${EXPECTED_ERROR_MESSAGE}


| *** Test Cases ***                                                                                                                                                                 |        *DOCUMENTATION*    		                                       |         *BASE_URL*                  |     *BIN_TYPE_NAME*      |     *BIN_NUMBER*      |    *EXPECTED_STATUS_CODE*   |     *EXPECTED_ERROR_MESSAGE*                 |
| Search for the details of the Bin Numbered '333333' without supplying the 'binTypeName' parameter.  | Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller   | Search Bin by bin type name using the getbinsbybintypename Controller   |                                     |                          |       333333          |           400               |   Bin type name must not be null or empty.   |
