*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/EOD_Control_Dashboard.robot

*** Variables ***

*** Keywords ***
Validates that the user can access an account for a Cost Centre
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page 

    And the user navigates to the EOD Control Dashboard Menu

    Then the user verifies that the cost centres can be expanded to display the accounts 


| *Test Cases*                                                                                                                                     |      *DOCUMENTATION*    | *TEST_ENVIRONMENT*   |
| 	Verify that the user can successfully access an account for a cost centre	| Validates that the user can access an account for a cost centre  |    Account Information  |    MTGLA_UAT         | 
