*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    UPDATE STATUS NEG SCENARIO
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Test vms negetive scenariost

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../keywords/qrcode_cc/UpdateATMComplaints.robot 
Resource                                            ../keywords/qrcode_cc/LandingPage.robot
Resource                                            ../keywords/qrcode_cc/ViewATMComplaintsAndCompliments.robot
Resource                                            ../keywords/common/common_keywords.robot
Resource                                            ../keywords/common/Login.robot
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
#LOCAL VARIABLES
${SUCCESS_MESSAGE_TEXT}                             Complaint/ Compliment Updated

#For update function this field must be empty
${ASSIGNED_TO_VALUE}                              

*** Keyword ***
Update ATM complaints status
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${FUNCTION}  ${Tag}  ${REFERNCE_NO}  ${NEW_STATUS}  ${CURRENT_STATUS}  ${COMMENT}  ${ACTION}
    Set Test Documentation  ${DOCUMENTATION}
   
    Log to console  Function: ${FUNCTION} New Status: ${NEW_STATUS} Current Status: ${CURRENT_STATUS} Comment: ${COMMENT}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe
   
    And The user clicks navigate to QR Code Complaints and Compliments screen link
   
    And User searches for a compliant or task  ${REFERNCE_NO}
   
    Sleep  2s
    Run Keyword If    '${ACTION}' == 'EMPTY STATUS'
    ...    Status dropdown list is empty
    ...  ELSE
    ...    User validates that update button is not availble  ${NEW_STATUS}

    # And User validates that update button is not availble  ${NEW_STATUS}

    And User logs out

| *Test Case*                                                                                                                               |                                  *DOCUMENTATION*                                                         |  *TESTRAIL_TESTCASE_ID*  |    *FUNCTION*       |    *Tag*         |  *REFERNCE_NO*| *NEW_STATUS*        | *CURRENT_STATUS* |                  *COMMENT*                                        |       *FUNCTION*              |           
| ATM Complaints Lifecycle - Negative Test - On Closed to Archived                                    | Update ATM complaints status        |      Negative Test - ATM Complaints Lifecycle - On Closed to Archived                                    |    101597689              |     UPDATE STATUS   | Update status    |  ACR0000361 |        Archived     |    Closed        | Negative Test - ATM Complaints Lifecycle - On Closed to Archived   |         EMPTY
| ATM Complaints Lifecycle - On Cancel to Closed                                                      |  Update ATM complaints status       |     ATM Complaints Lifecycle - On Cancel to Closed                                                       |    101597694              |     UPDATE STATUS   | Update status    |  ACR0000138 |        Closed       | Cancel           | ATM Complaints Lifecycle - On Cancel to Closed                     |         EMPTY
| ATM Complaints Lifecycle - Negative Test - ATM Complaints Lifecycle - New to In progress             | Update ATM complaints status       |     ATM Complaints Lifecycle - Negative Test - ATM Complaints Lifecycle - New to In progress             |    101597700              |     UPDATE STATUS   | Update status    |  ACR0000581 |        Closed       | Cancel           | ATM Complaints Lifecycle - On Cancel to Closed                     |          EMPTY STATUS
| ATM Complaints Lifecycle - Negative Test - ATM Complaints Lifecycle - New to On Hold                 | Update ATM complaints status       |     ATM Complaints Lifecycle - Negative Test - ATM Complaints Lifecycle - New to On Hold                 |    101597699              |     UPDATE STATUS   | Update status    |  ACR0000580 |        Closed       | Cancel           | ATM Complaints Lifecycle - On Cancel to Closed                     |          EMPTY STATUS
| ATM Complaints Lifecycle - Negative Test - ATM Complaints Lifecycle - New to Cancel                 | Update ATM complaints status        |     ATM Complaints Lifecycle - Negative Test - ATM Complaints Lifecycle - New to Cancel                  |    101597698              |     UPDATE STATUS   | Update status    |  ACR0000579 |        Closed       | Cancel           | ATM Complaints Lifecycle - On Cancel to Closed                     |          EMPTY STATUS

