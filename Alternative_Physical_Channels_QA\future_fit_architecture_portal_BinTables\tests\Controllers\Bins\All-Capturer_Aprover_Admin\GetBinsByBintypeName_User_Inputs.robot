*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinsByBintypeName_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_TYPE_NAME}   ${BIN_NUMBER}  ${EXPECTED_STATUS_CODE}   ${BIN_ID}  ${BIN_TYPE}  ${ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetBinsByBintypeName                 ${BASE_URL}      ${BIN_TYPE_NAME}      ${BIN_NUMBER}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    And The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${BIN_NUMBER}   ${BIN_TYPE}   ${ACTION_DATE}
    Then The Bin Number details must exist on the Bin Database                  ${BIN_ID}   ${BIN_NUMBER}   ${BIN_TYPE}   ${ACTION_DATE}


| *** Test Cases ***                                                                                                                                                          |        *DOCUMENTATION*    		                                        |         *BASE_URL*                  |     *BIN_TYPE_NAME*      |     *BIN_NUMBER*      |    *EXPECTED_STATUS_CODE*   |                *BIN_ID*                   |      *BIN_TYPE*       |       *ACTION_DATE*            |
| Search for the details of the Bin Numbered '333333' using the Bin Type Name: 'Contactless'.  | Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller   | Search Bin by bin type name using the getbinsbybintypename Controller   |                                     |       Contactless        |       333333          |           200               |   5171bf7c-1c96-493f-8f4d-e1747b15f385    |       Contactless     |       2024-10-31               |
| Search for the details of the Bin Numbered '333333' using the Bin Type Name: 'Domestic'.     | Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller   | Search Bin by bin type name using the getbinsbybintypename Controller   |                                     |       Domestic           |       333333          |           200               |   5171bf7c-1c96-493f-8f4d-e1747b15f385    |       Domestic        |       2024-10-31               |
| Search for the details of the Bin Numbered '7004532' using the Bin Type Name: 'Invalid'.     | Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller   | Search Bin by bin type name using the getbinsbybintypename Controller   |                                     |       Invalid            |       7004532         |           200               |   117944e3-d3e3-4d36-a6a4-7216ccb2ee6b    |       Invalid         |       2026-11-12               |
| Search for the details of the Bin Numbered '517224' using the Bin Type Name: 'On-Us'.        | Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller   | Search Bin by bin type name using the getbinsbybintypename Controller   |                                     |       On-Us              |       517224          |           200               |   0225f1dc-e26b-472d-9967-aa3ff384dd8d    |       On-Us           |       2025-01-28               |
| Search for the details of the Bin Numbered '654321' using the Bin Type Name: 'Token'.        | Search for a Bin using a Bin Number on the GetBinsByBintypeName Controller   | Search Bin by bin type name using the getbinsbybintypename Controller   |                                     |       Token              |       654321          |           200               |   54384896-8796-49bd-99ee-4a609a79f862    |       Token           |       2024-11-21               |
