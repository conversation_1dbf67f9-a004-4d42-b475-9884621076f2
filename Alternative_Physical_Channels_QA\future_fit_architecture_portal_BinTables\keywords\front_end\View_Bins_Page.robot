*** Settings ***
#Author Name               : <PERSON>hab<PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/front_end/Add_Bins_Page.robot


*** Variables ***
${BIN_TYPES_BTN}                                    xpath=//p[contains(text(),'BIN TYPES')]
${BINS_TABLE}                                       xpath=//mat-table[contains(@class, 'mat-table') and contains(@class, 'cdk-table') and contains(@class, 'mat-sort')]
${PAGINATOR_RANGE_LABEL}                            xpath=//div[@class='mat-paginator-range-label']
${PAGINATOR_ICON}                                   xpath=//button[contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-paginator-navigation-nex') and contains(@class, 'mat-icon-button')]
${PREV_PAGE_PAGINATOR_ICON}                         xpath=//button[contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-paginator-navigation-previous') and contains(@class, 'mat-icon-button')]
${FILTER_LIST}                                      xpath=//mat-select[@role='combobox']
${FILTER_LIST_5_ITEMS}                              xpath=//span[contains(text(),' 5 ')]
${FILTER_LIST_10_ITEMS}                             xpath=//span[contains(text(),' 10 ')]
${FILTER_LIST_25_ITEMS}                             xpath=//span[contains(text(),' 25 ')]
${FILTER_LIST_100_ITEMS}                            xpath=//span[contains(text(),' 100 ')]
${VIEW_BINS_BTN}                                    xpath=//span[contains(text(),'View')]
${SEARCH_INPUT}                                     xpath=//input[contains(@class, 'search-input') and contains(@class, 'ng-pristine') and contains(@class, 'ng-valid') and contains(@class, 'ng-touched')]
${REVIEW_BINS_BTN}                                  xpath=//span[contains(text(),'Review')]
${EXISTING_BIN_NUMBER}                              xpath=//mat-row[mat-cell[contains(@class, 'cdk-column-outcome') and normalize-space(text())='Added']]/mat-cell[contains(@class, 'cdk-column-binNumber')]


*** Keywords ***
The Bin Number does not exist in the database
    [Arguments]     ${BIN_NUMBER_TO_VERIFY}

    ${BIN_NUMBER_TO_VERIFY}=      Remove Quotes       ${BIN_NUMBER_TO_VERIFY}

    #This is the boolean value that checks if all bins must be verified
    ${verify_all_bins}=        Set Variable If
         ...       '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}'     ${True}
         ...       '${BIN_NUMBER_TO_VERIFY}' == ''             ${True}
         ...       '${BIN_NUMBER_TO_VERIFY}' != '${EMPTY}'     ${False}
         ...       '${BIN_NUMBER_TO_VERIFY}' != ''             ${False}

    ${db_bin_details}=    Get Bin details using Bin Number   ${BIN_NUMBER_TO_VERIFY}
    ${bin_details_do_not_exist_in_db}=   Run Keyword And Return Status    Should Be Empty    ${db_bin_details}

    Run Keyword If    ${bin_details_do_not_exist_in_db}
    ...    Log Many   The bin numbered: '${BIN_NUMBER_TO_VERIFY}' does not exist in the database.
    ...   ELSE
    ...    Fail   The bin numbered: '${BIN_NUMBER_TO_VERIFY}' exists in the database.


The Bin Number exists in the database
    [Arguments]     ${BIN_NUMBER_TO_VERIFY}

    ${BIN_NUMBER_TO_VERIFY}=      Remove Quotes       ${BIN_NUMBER_TO_VERIFY}

    #This is the boolean value that checks if all bins must be verified
    ${verify_all_bins}=        Set Variable If
         ...       '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}'     ${True}
         ...       '${BIN_NUMBER_TO_VERIFY}' == ''             ${True}
         ...       '${BIN_NUMBER_TO_VERIFY}' != '${EMPTY}'     ${False}
         ...       '${BIN_NUMBER_TO_VERIFY}' != ''             ${False}

    ${db_bin_details}=    Get Bin details using Bin Number   ${BIN_NUMBER_TO_VERIFY}
    ${bin_details_exist_in_db}=   Run Keyword And Return Status    Should Not Be Empty    ${db_bin_details}

    Run Keyword If    ${bin_details_exist_in_db}
    ...    Log Many   The bin numbered: '${BIN_NUMBER_TO_VERIFY}' exists in the database.
    ...   ELSE
    ...    Fail   The bin numbered: '${BIN_NUMBER_TO_VERIFY}' does not exist in the database.


The user gets the details of a soft-deleted bin type from the database

    #Get the Bin Type from the database
    ${db_bin_type_results}=     Get a Bin Type from the database using 'isDeleted' status    0

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no soft-deleted Bin Types in the database.

    IF    not ${db_results_contain_data}
        Set Global Variable    ${SOFT_DELETED_BIN_TYPE_DETAILS}         ${EMPTY}
    ELSE
        Set Global Variable    ${SOFT_DELETED_BIN_TYPE_DETAILS}         ${db_bin_type_results}
    END


The user gets the details of a soft-deleted bins for a bin type from the database
    [Arguments]     ${BIN_TYPE_TO_VERIFY}

     ${string_to_validate}=      Remove Quotes       ${BIN_TYPE_TO_VERIFY}

    #This is the boolean value that checks if the bin type name is provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${string_to_validate}' == '${EMPTY}'     ${False}
         ...       '${string_to_validate}' == ''             ${False}
         ...       '${string_to_validate}' != '${EMPTY}'     ${True}
         ...       '${string_to_validate}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type!

    #Get the Bin Type from the database
    ${db_bin_type_results}=     Get all in-active Bins associated with the Bin Type from the Database using the Bin Type Name    ${BIN_TYPE_TO_VERIFY}

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no soft-deleted Bins for Bin Type '${BIN_TYPE_TO_VERIFY}' in the database.

    IF    not ${db_results_contain_data}
        Set Global Variable    ${SOFT_DELETED_BINS_FOR_BIN_TYPE_DETAILS}         ${EMPTY}
    ELSE
        Set Global Variable    ${SOFT_DELETED_BINS_FOR_BIN_TYPE_DETAILS}         ${db_bin_type_results}
    END

The View Entries page must display Bins that match with the database data
    [Arguments]     ${BIN_NUMBER_TO_VERIFY}
    ${BIN_NUMBER_TO_VERIFY}=      Remove Quotes       ${BIN_NUMBER_TO_VERIFY}

    #This is the boolean value that checks if all bins must be verified
    ${verify_all_bins}=        Set Variable If
         ...       '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}'     ${True}
         ...       '${BIN_NUMBER_TO_VERIFY}' == ''             ${True}
         ...       '${BIN_NUMBER_TO_VERIFY}' != '${EMPTY}'     ${False}
         ...       '${BIN_NUMBER_TO_VERIFY}' != ''             ${False}

    Log  ${verify_all_bins}

    #Split the Bin Types names using a comma
    ${bin_numbers_list}=         Split String    ${BIN_NUMBER_TO_VERIFY}    separator=,
    Log  ${bin_numbers_list}



    
    #To be run if all bin types displayed on the page must be verified against the DB
    IF    ${verify_all_bins}

         Verify the bins displayed on the View Entries page against the active bins in the database     ${BIN_NUMBER_TO_VERIFY}
    ELSE
        #Verify specific Bin Types
        FOR    ${element}    IN    @{bin_numbers_list}
            ${user_bin_number_found}=     Set Variable    ${False}
            Log    Current Bin Type to verify is: '${element}'
            Verify the bins displayed on the View Entries page against the active bins in the database   ${BIN_NUMBER_TO_VERIFY}
        END
    END




Verify the bins displayed on the View Entries page against the active bins in the database
    [Arguments]     ${BIN_NUMBER_TO_VERIFY}
    #Verify that the current page is 'View Entries'
    ${correct_page_displayed}=      Correct Page is displayed    View Entries

    IF    not ${correct_page_displayed}
        RETURN
    END

    ${db_active_bins_count}=    Get the count on active Bins from the Database
    ${db_count_first_item}=    Get From List    ${db_active_bins_count}    0
    ${number_of_rows_returned_from_db}=    Get Column Data By Name       ${db_count_first_item}       total_bins

    Run Keyword If    '${number_of_rows_returned_from_db}' == '0'
    ...    Run Keyword And Warn On Failure    Fail   The are no active Bins on the database.

    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The Bins table is not displayed on the 'View Entries' page.

    #Filter the table to show 100 items
     ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            SeleniumLibrary.Click Element    ${FILTER_LIST}
            Sleep    3s
        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_For_BINS_Not_Displayed.png
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Run Keyword And Warn On Failure    Fail    There are no Bins displayed on the 'View Entries' page.
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${bins_displayed_on_page_equal_db_total}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    ${number_of_rows_returned_from_db}

    IF    '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}' or '${BIN_NUMBER_TO_VERIFY}' == ''
        Run Keyword If    not ${bins_displayed_on_page_equal_db_total}
        ...    Run Keyword And Warn On Failure    Fail   The total number of Bins that are displayed on the View Entries page is not the same as the DB Bins total. The DB total is '${number_of_rows_returned_from_db}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
        ...  ELSE
        ...    Log Many    The total number of active Bins that are displayed on the View Entries page is the same as the total number of active DB Bins. The DB total is '${number_of_rows_returned_from_db}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
    END


     ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed on the View Entries page!
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    ${record_counter}=    Set Variable  0
    WHILE    '${disabled_attribute}' == 'False' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${bins_table_rows_element_xpath}=       Catenate  ${BINS_TABLE}/mat-row

        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${bins_table_rows_element_xpath}
        Log  Number of rows is : ${linked_bins_table_rows_total}
        #Loop through each row on the current page
        #Verify the details of all displayed BINS against that database
        ${user_bin_number_found}=    Set Variable  ${False}
        IF    '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}' or '${BIN_NUMBER_TO_VERIFY}' == ''
            FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
                   ${curr_row_bin_ele}=           Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[1]
                   ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                   ${curr_row_bin_type_ele}=           Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[2]
                   ${bin_type}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
                   ${curr_row_bin_action_date_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[3]
                   ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
                   ${curr_row_bin_captured_date_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[4]
                   ${bin_captured_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_captured_date_ele}
                   ${curr_row_bin_captured_by_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[5]
                   ${bin_captured_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_captured_by_ele}
                   ${curr_row_bin_outcome_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[6]
                   ${bin_outcome}=         SeleniumLibrary.Get Text    ${curr_row_bin_outcome_ele}
                   ${bin_outcome_number}=         Get Bin Outcome Number   ${bin_outcome}
                   ${curr_row_bin_review_date_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[7]
                   ${bin_review_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_review_date_ele}
                   IF    '${bin_review_date}' == '${EMPTY}'
                       ${bin_review_date}=      Set Variable   None
                   ELSE
                       ${bin_review_date}=      Convert to Date    ${bin_review_date}
                       ${curr_row_value_array}=   Split String    ${bin_review_date.strip()}       separator=${SPACE}
                       ${bin_review_date}=   Set Variable    ${curr_row_value_array}[0]
                   END
                   Log   bin_review_date: ${bin_review_date}
                   ${curr_row_bin_reviewed_by_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[8]
                   ${bin_reviewed_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_reviewed_by_ele}
                   IF    '${bin_reviewed_by}' == '${EMPTY}'
                       ${bin_reviewed_by}=      Set Variable    None

                   END
                   ${curr_row_bin_status_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[9]
                   ${bin_status}=         SeleniumLibrary.Get Text    ${curr_row_bin_status_ele}
                   ${bin_status_number}=         Get Bin Status Number   ${bin_status}

                   ${curr_row_bin_rejected_comment_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[10]
                   ${bin_rejected_comment}=         SeleniumLibrary.Get Text    ${curr_row_bin_rejected_comment_ele}
                   IF    '${bin_rejected_comment}' == '${EMPTY}'
                       ${bin_rejected_comment}=      Set Variable    None
                   END

                   #Check how many Bin Types are linked to the current bin as per the front end display
                   ${bin_type_array}=       Split String    ${bin_type}     separator=,
                   #Loop through all associated Bin Types
                   FOR    ${bin_type_element}    IN    @{bin_type_array}
                        ${bin_type_element}=    Set Variable   ${bin_type_element.strip()}
                        Log    ${bin_type_element}
                        #Get the bin mathing the current displayed bin from the database
                        ${bins_details_for_bin_type_db_results}=     Get Bin details based on bin type name and outcome     ${bin_number}       ${bin_type_element}     ${bin_outcome_number}

                       # Ensure the results are not empty
                        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bins_details_for_bin_type_db_results}
                        Run Keyword If    not ${db_results_contain_data}
                        ...    Run Keyword And Continue On Failure    Fail   The Bin Number: '${bin_number}' that has the outcome of '${bin_outcome}', and is linked to Bin Type: '${bin_type_element}', was not found in the database.

                        IF    not ${db_results_contain_data}
                            CONTINUE
                        END
                       ${bins_details_for_bin_type_db_results_rows}=        Get Length      ${bins_details_for_bin_type_db_results}
                       #Verify that the Bin's DB details matches the front end
                        IF    ${db_results_contain_data}
                            ${bin_action_date_verified}=   Set Variable  ${False}
                            ${bin_captured_date_verified}=   Set Variable  ${False}
                            ${bin_captured_by_verified}=   Set Variable  ${False}
                            ${bin_review_date_verified}=   Set Variable  ${False}
                            ${bin_reviewed_by_verified}=   Set Variable  ${False}
                            ${bin_status_number_verified}=   Set Variable  ${False}
                            ${bin_rejected_comment_verified}=   Set Variable  ${False}

                             #Loop through allresults returned by the database
                             FOR    ${db_row}    IN    @{bins_details_for_bin_type_db_results}
                                #${db_data_first_item}=    Get From List    ${bins_details_for_bin_type_db_results}    0
                                ${db_bin_id}=                   Get Column Data By Name       ${db_row}       BinId
                                ${db_bin_isDeleted_status}=     Get Column Data By Name       ${db_row}       IsDeleted
                                ${db_outcome}=           Get Column Data By Name    ${db_row}       ActionType
                                ${db_outcome_text}=      Get Bin Outcome Text       ${db_outcome}
                                ${db_action_date}=       Get Column Data By Name    ${db_row}       ActionDate
                                ${bin_action_date}=      Convert to Date    ${bin_action_date}
                                ${expected_date_array}=       Split String    ${bin_action_date}       separator=${SPACE}
                                ${bin_action_date}=      Set Variable        ${expected_date_array}[0]
                                ${db_captured_date}=       Get Column Data By Name    ${db_row}       CreatedDate
                                ${db_captured_date}=       Convert to String    ${db_captured_date}
                                ${curr_row_value_array}=   Split String    ${db_captured_date.strip()}       separator=${SPACE}
                                ${db_captured_date}=   Set Variable    ${curr_row_value_array}[0]
                                ${bin_captured_date}=      Convert to Date    ${bin_captured_date}
                                ${expected_date_array}=       Split String    ${bin_captured_date}       separator=${SPACE}
                                ${bin_captured_date}=      Set Variable        ${expected_date_array}[0]
                                ${db_captured_by}=        Get Column Data By Name    ${db_row}       CreatedBy
                                IF    '${db_captured_by}' != 'None'
                                    ${db_captured_by}=      Convert to String   ${db_captured_by}
                                    ${db_captured_by}=      Set Variable     ${db_captured_by.strip()}
                                END
                                ${db_review_date}=       Get Column Data By Name    ${db_row}       ReviewedDate
                                IF    '${db_review_date}' != 'None' and '${db_review_date}' != '${EMPTY}'
                                    ${db_review_date}=       Convert to String    ${db_review_date}
                                    ${curr_row_value_array}=   Split String    ${db_review_date.strip()}       separator=${SPACE}
                                    ${db_review_date}=   Set Variable    ${curr_row_value_array}[0]
                                ELSE
                                    IF    '${db_review_date}' == '${EMPTY}'
                                        ${db_review_date}=  Set Variable   None
                                    END
                                END
                                ${db_reviewed_by}=        Get Column Data By Name    ${db_row}       ReviewedBy
                                IF    '${db_reviewed_by}' != 'None' and '${db_reviewed_by}' != '${EMPTY}'
                                    ${db_reviewed_by}=      Convert to String   ${db_reviewed_by}
                                    ${db_reviewed_by}=      Set Variable     ${db_reviewed_by.strip()}
                                ELSE
                                    IF    '${db_reviewed_by}' == '${EMPTY}'
                                        ${db_reviewed_by}=  Set Variable   None
                                    END
                                END
                                ${db_status}=        Get Column Data By Name    ${db_row}       Status
                                ${db_rejected_comment}=        Get Column Data By Name    ${db_row}       RejectionComment
                                IF    '${db_rejected_comment}' != 'None' and '${db_rejected_comment}' != '${EMPTY}'
                                    ${db_rejected_comment}=      Convert to String   ${db_rejected_comment}
                                    ${db_rejected_comment}=      Set Variable     ${db_rejected_comment.strip()}
                                ELSE
                                    IF    '${db_rejected_comment}' == '${EMPTY}'
                                        ${db_rejected_comment}=  Set Variable   None
                                    END
                                END
                                ${bin_action_date_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_action_date}    ${bin_action_date}     msg=The db_action_date value '${db_action_date}' is not the same as the action_date displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_action_date}'
                                ${bin_captured_date_verified}=      Run Keyword And Return Status     Should Be Equal As Strings    ${db_captured_date}    ${bin_captured_date}     msg=The db_captured_date value '${db_captured_date}' is not the same as the captured_date displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_captured_date}'
                                ${bin_captured_by_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_captured_by}    ${bin_captured_by}     msg=The db_captured_by value '${db_captured_by}' is not the same as the captured_by displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_captured_by}'
                                ${bin_review_date_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_review_date}    ${bin_review_date}     msg=The db_review_date value '${db_review_date}' is not the same as the review_date displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_review_date}'
                                ${bin_reviewed_by_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_reviewed_by}    ${bin_reviewed_by}     msg=The db_reviewed_by value '${db_reviewed_by}' is not the same as the reviewed_by displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_review_date}'
                                ${bin_status_number_verified}=      Run Keyword And Return Status     Should Be Equal As Strings    ${db_status}    ${bin_status_number}     msg=The db_status value '${db_status}' is not the same as the status displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_status_number}'
                                ${bin_rejected_comment_verified}=   Run Keyword And Return Status     Should Be Equal As Strings    ${db_rejected_comment}    ${bin_rejected_comment}     msg=The db_rejected_comment value '${db_rejected_comment}' is not the same as the rejected_comment displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_rejected_comment}'

                                ${all_verification_passed}=         All Variables are True   ${bin_action_date_verified}        ${bin_captured_date_verified}       ${bin_captured_by_verified}     ${bin_review_date_verified}     ${bin_reviewed_by_verified}     ${bin_status_number_verified}       ${bin_rejected_comment_verified}

                                IF    ${all_verification_passed}
                                    #If the bin is showing as approved on the front end
                                    IF    '${bin_status_number}' == '0'
                                        IF    '${bin_outcome.strip()}' == 'Deleted'
                                             #Verify that the Bin Number is soft-deleted in the database
                                            Run Keyword If    '${db_bin_isDeleted_status}' == '1'
                                            ...    Log Many   The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                            ...  ELSE
                                            ...    Run Keyword And Continue On Failure    Fail    The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is not soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                        ELSE
                                             #Verify that the Bin Number is not soft-deleted in the database
                                            Run Keyword If    '${db_bin_isDeleted_status}' == '0'
                                            ...    Log Many   The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is not soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                            ...  ELSE
                                            ...    Run Keyword And Continue On Failure    Fail    The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                        END
                                    ELSE
                                        IF    '${bin_outcome.strip()}' == 'Deleted'
                                             #Verify that the Bin Number is not soft-deleted in the database if the current action is not yet approved
                                            Run Keyword If    '${db_bin_isDeleted_status}' == '1'
                                            ...    Run Keyword And Continue On Failure    Fail   The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                            ...  ELSE
                                            ...    Log Many    The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is not yet soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                        END

                                    END
                                    Exit for loop
                                END
                             END

                             Run Keyword If    not ${bin_action_date_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_action_date: '${bin_action_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_action_date: '${bin_action_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_captured_date_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_captured_date: '${bin_captured_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_captured_date: '${bin_captured_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_captured_by_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_captured_by: '${bin_captured_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_captured_by: '${bin_captured_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_review_date_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_review_date: '${bin_review_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_review_date: '${bin_review_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_reviewed_by_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_reviewed_by: '${bin_reviewed_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_reviewed_by: '${bin_reviewed_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_status_number_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_status: '${bin_status}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_status: '${bin_status}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_rejected_comment_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_rejected_comment: '${bin_rejected_comment}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_rejected_comment: '${bin_rejected_comment}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.
                        END
                   END
                   ${record_counter}=    Evaluate    ${record_counter} + 1
                   Log    The counter value is ${record_counter}
            END
        ELSE
            FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
               ${curr_row_bin_ele}=           Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[1]
               ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}

               IF    '${BIN_NUMBER_TO_VERIFY}' == '${bin_number}'
                   Log Many     The provided Bin Number: '${bin_number}' was found on the View Entries page and it will be verified against the DB.
                   ${user_bin_number_found}=    Set Variable  ${True}
                   ${curr_row_bin_type_ele}=           Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[2]
                   ${bin_type}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
                   ${curr_row_bin_action_date_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[3]
                   ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
                   ${curr_row_bin_captured_date_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[4]
                   ${bin_captured_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_captured_date_ele}
                   ${curr_row_bin_captured_by_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[5]
                   ${bin_captured_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_captured_by_ele}
                   ${curr_row_bin_outcome_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[6]
                   ${bin_outcome}=         SeleniumLibrary.Get Text    ${curr_row_bin_outcome_ele}
                   ${bin_outcome_number}=         Get Bin Outcome Number   ${bin_outcome}
                   ${curr_row_bin_review_date_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[7]
                   ${bin_review_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_review_date_ele}
                   IF    '${bin_review_date}' == '${EMPTY}'
                       ${bin_review_date}=      Set Variable   None
                   ELSE
                       ${bin_review_date}=      Convert to Date    ${bin_review_date}
                       ${curr_row_value_array}=   Split String    ${bin_review_date.strip()}       separator=${SPACE}
                       ${bin_review_date}=   Set Variable    ${curr_row_value_array}[0]
                   END
                   Log   bin_review_date: ${bin_review_date}
                   ${curr_row_bin_reviewed_by_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[8]
                   ${bin_reviewed_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_reviewed_by_ele}
                   IF    '${bin_reviewed_by}' == '${EMPTY}'
                       ${bin_reviewed_by}=      Set Variable    None

                   END
                   ${curr_row_bin_status_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[9]
                   ${bin_status}=         SeleniumLibrary.Get Text    ${curr_row_bin_status_ele}
                   ${bin_status_number}=         Get Bin Status Number   ${bin_status}

                   ${curr_row_bin_rejected_comment_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[10]
                   ${bin_rejected_comment}=         SeleniumLibrary.Get Text    ${curr_row_bin_rejected_comment_ele}
                   IF    '${bin_rejected_comment}' == '${EMPTY}'
                       ${bin_rejected_comment}=      Set Variable    None
                   END

                   #Check how many Bin Types are linked to the current bin as per the front end display
                   ${bin_type_array}=       Split String    ${bin_type}     separator=,
                   #Loop through all associated Bin Types
                   FOR    ${bin_type_element}    IN    @{bin_type_array}
                        ${bin_type_element}=    Set Variable   ${bin_type_element.strip()}
                        Log    ${bin_type_element}
                        #Get the bin mathing the current displayed bin from the database
                        ${bins_details_for_bin_type_db_results}=     Get Bin details based on bin type name and outcome     ${bin_number}       ${bin_type_element}     ${bin_outcome_number}

                       # Ensure the results are not empty
                        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bins_details_for_bin_type_db_results}
                        Run Keyword If    not ${db_results_contain_data}
                        ...    Run Keyword And Continue On Failure    Fail   The Bin Number: '${bin_number}' that has the outcome of '${bin_outcome}', and is linked to Bin Type: '${bin_type_element}', was not found in the database.

                        IF    not ${db_results_contain_data}
                            CONTINUE
                        END
                       ${bins_details_for_bin_type_db_results_rows}=        Get Length      ${bins_details_for_bin_type_db_results}
                       #Verify that the Bin's DB details matches the front end
                        IF    ${db_results_contain_data}
                            ${bin_action_date_verified}=   Set Variable  ${False}
                            ${bin_captured_date_verified}=   Set Variable  ${False}
                            ${bin_captured_by_verified}=   Set Variable  ${False}
                            ${bin_review_date_verified}=   Set Variable  ${False}
                            ${bin_reviewed_by_verified}=   Set Variable  ${False}
                            ${bin_status_number_verified}=   Set Variable  ${False}
                            ${bin_rejected_comment_verified}=   Set Variable  ${False}

                             #Loop through allresults returned by the database
                             FOR    ${db_row}    IN    @{bins_details_for_bin_type_db_results}
                                #${db_data_first_item}=    Get From List    ${bins_details_for_bin_type_db_results}    0
                                ${db_bin_id}=                   Get Column Data By Name       ${db_row}       BinId
                                ${db_bin_isDeleted_status}=     Get Column Data By Name       ${db_row}       IsDeleted
                                ${db_outcome}=           Get Column Data By Name    ${db_row}       ActionType
                                ${db_outcome_text}=      Get Bin Outcome Text       ${db_outcome}
                                ${db_action_date}=       Get Column Data By Name    ${db_row}       ActionDate
                                ${bin_action_date}=      Convert to Date    ${bin_action_date}
                                ${expected_date_array}=       Split String    ${bin_action_date}       separator=${SPACE}
                                ${bin_action_date}=      Set Variable        ${expected_date_array}[0]
                                ${db_captured_date}=       Get Column Data By Name    ${db_row}       CreatedDate
                                ${db_captured_date}=       Convert to String    ${db_captured_date}
                                ${curr_row_value_array}=   Split String    ${db_captured_date.strip()}       separator=${SPACE}
                                ${db_captured_date}=   Set Variable    ${curr_row_value_array}[0]
                                ${bin_captured_date}=      Convert to Date    ${bin_captured_date}
                                ${expected_date_array}=       Split String    ${bin_captured_date}       separator=${SPACE}
                                ${bin_captured_date}=      Set Variable        ${expected_date_array}[0]
                                ${db_captured_by}=        Get Column Data By Name    ${db_row}       CreatedBy
                                IF    '${db_captured_by}' != 'None'
                                    ${db_captured_by}=      Convert to String   ${db_captured_by}
                                    ${db_captured_by}=      Set Variable     ${db_captured_by.strip()}
                                END
                                ${db_review_date}=       Get Column Data By Name    ${db_row}       ReviewedDate
                                IF    '${db_review_date}' != 'None' and '${db_review_date}' != '${EMPTY}'
                                    ${db_review_date}=       Convert to String    ${db_review_date}
                                    ${curr_row_value_array}=   Split String    ${db_review_date.strip()}       separator=${SPACE}
                                    ${db_review_date}=   Set Variable    ${curr_row_value_array}[0]
                                ELSE
                                    IF    '${db_review_date}' == '${EMPTY}'
                                        ${db_review_date}=  Set Variable   None
                                    END
                                END
                                ${db_reviewed_by}=        Get Column Data By Name    ${db_row}       ReviewedBy
                                IF    '${db_reviewed_by}' != 'None' and '${db_reviewed_by}' != '${EMPTY}'
                                    ${db_reviewed_by}=      Convert to String   ${db_reviewed_by}
                                    ${db_reviewed_by}=      Set Variable     ${db_reviewed_by.strip()}
                                ELSE
                                    IF    '${db_reviewed_by}' == '${EMPTY}'
                                        ${db_reviewed_by}=  Set Variable   None
                                    END
                                END
                                ${db_status}=        Get Column Data By Name    ${db_row}       Status
                                ${db_rejected_comment}=        Get Column Data By Name    ${db_row}       RejectionComment
                                IF    '${db_rejected_comment}' != 'None' and '${db_rejected_comment}' != '${EMPTY}'
                                    ${db_rejected_comment}=      Convert to String   ${db_rejected_comment}
                                    ${db_rejected_comment}=      Set Variable     ${db_rejected_comment.strip()}
                                ELSE
                                    IF    '${db_rejected_comment}' == '${EMPTY}'
                                        ${db_rejected_comment}=  Set Variable   None
                                    END
                                END
                                ${bin_action_date_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_action_date}    ${bin_action_date}     msg=The db_action_date value '${db_action_date}' is not the same as the action_date displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_action_date}'
                                ${bin_captured_date_verified}=      Run Keyword And Return Status     Should Be Equal As Strings    ${db_captured_date}    ${bin_captured_date}     msg=The db_captured_date value '${db_captured_date}' is not the same as the captured_date displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_captured_date}'
                                ${bin_captured_by_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_captured_by}    ${bin_captured_by}     msg=The db_captured_by value '${db_captured_by}' is not the same as the captured_by displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_captured_by}'
                                ${bin_review_date_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_review_date}    ${bin_review_date}     msg=The db_review_date value '${db_review_date}' is not the same as the review_date displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_review_date}'
                                ${bin_reviewed_by_verified}=        Run Keyword And Return Status     Should Be Equal As Strings    ${db_reviewed_by}    ${bin_reviewed_by}     msg=The db_reviewed_by value '${db_reviewed_by}' is not the same as the reviewed_by displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_review_date}'
                                ${bin_status_number_verified}=      Run Keyword And Return Status     Should Be Equal As Strings    ${db_status}    ${bin_status_number}     msg=The db_status value '${db_status}' is not the same as the status displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_status_number}'
                                ${bin_rejected_comment_verified}=   Run Keyword And Return Status     Should Be Equal As Strings    ${db_rejected_comment}    ${bin_rejected_comment}     msg=The db_rejected_comment value '${db_rejected_comment}' is not the same as the rejected_comment displayed on the front End for Bin Number '${bin_number}'. The value displayed on the FE is '${bin_rejected_comment}'

                                ${all_verification_passed}=         All Variables are True   ${bin_action_date_verified}        ${bin_captured_date_verified}       ${bin_captured_by_verified}     ${bin_review_date_verified}     ${bin_reviewed_by_verified}     ${bin_status_number_verified}       ${bin_rejected_comment_verified}

                                IF    ${all_verification_passed}
                                    #If the bin is showing as approved on the front end
                                    IF    '${bin_status_number}' == '0'
                                        IF    '${bin_outcome.strip()}' == 'Deleted'
                                             #Verify that the Bin Number is soft-deleted in the database
                                            Run Keyword If    '${db_bin_isDeleted_status}' == '1'
                                            ...    Log Many   The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                            ...  ELSE
                                            ...    Run Keyword And Continue On Failure    Fail    The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is not soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                        ELSE
                                             #Verify that the Bin Number is not soft-deleted in the database
                                            Run Keyword If    '${db_bin_isDeleted_status}' == '0'
                                            ...    Log Many   The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is not soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                            ...  ELSE
                                            ...    Run Keyword And Continue On Failure    Fail    The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                        END
                                    ELSE
                                        IF    '${bin_outcome.strip()}' == 'Deleted'
                                             #Verify that the Bin Number is not soft-deleted in the database if the current action is not yet approved
                                            Run Keyword If    '${db_bin_isDeleted_status}' == '1'
                                            ...    Run Keyword And Continue On Failure    Fail   The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                            ...  ELSE
                                            ...    Log Many    The '${bin_status}' Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', that is displayed on the front end is not yet soft-deleted in the database. The outcome of the bin displayed on the FE is '${bin_outcome.strip()}'.
                                        END

                                    END
                                    Exit for loop
                                END
                             END

                             Run Keyword If    not ${bin_action_date_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_action_date: '${bin_action_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_action_date: '${bin_action_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_captured_date_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_captured_date: '${bin_captured_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_captured_date: '${bin_captured_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_captured_by_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_captured_by: '${bin_captured_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_captured_by: '${bin_captured_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_review_date_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_review_date: '${bin_review_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_review_date: '${bin_review_date}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_reviewed_by_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_reviewed_by: '${bin_reviewed_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_reviewed_by: '${bin_reviewed_by}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_status_number_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_status: '${bin_status}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_status: '${bin_status}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.

                             Run Keyword If    not ${bin_rejected_comment_verified}
                              ...    Run Keyword And Continue On Failure    Fail   The bin_rejected_comment: '${bin_rejected_comment}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was not found in the database.
                              ...  ELSE
                              ...    Log Many    The bin_rejected_comment: '${bin_rejected_comment}' for the Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_element}', was found in the database.
                        END
                   END
                   Exit For Loop
               END

               ${record_counter}=    Evaluate    ${record_counter} + 1
               Log    The counter value is ${record_counter}
            END

        END

        ${random_word}=     Generate random word
        Capture Page Screenshot   ${random_word}_Page_${record_counter}_verification.png

        IF  ${user_bin_number_found}
             RETURN
        END
        ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
        ${attr}=                    Get Current Element Attributes    ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
        Log Many    next_page_disabled_status:   '${disabled_attribute}'

       #Go to the next page if the navigator is enabled
        IF    '${disabled_attribute}' != 'True' or '${record_counter}' != '${total_number_of_items_on_page}'

           #Click on the 'Next' button
           SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
           Sleep    2s
        ELSE

            IF    '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}' or '${BIN_NUMBER_TO_VERIFY}' == ''
                #Verify that the last page is displayed
                ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
                Log Many  ${paginator_range_data}
                ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of

                ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
                ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
                ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}

                Run Keyword If    '${total_number_of_items_on_page.strip()}' == '${total_number_of_items_on_current_page.strip()}'
                ...    Log Many     All '${total_number_of_items_on_current_page.strip()}' Bins displayed  have been verified against the database.
                ...  ELSE
                ...    Fail     The next page navigator icon is disabled, hence not all fron end Bins could be verified against the database. The total number of Bins loaded on the Front End is  '${total_number_of_items_on_current_page.strip()}' while the total number of Bins verified against the database is  '${total_number_of_items_on_current_page.strip()}'.
            ELSE
                IF    not ${user_bin_number_found}
                   Run Keyword And Continue on Failure  Fail     The provided Bin Number: '${BIN_NUMBER_TO_VERIFY}' was not found on the View Entries page.

                END
            END

            BREAK
        END
    END





The search results must be empty if the bin does not exist in the database
    [Arguments]     ${BIN_NUMBER_TO_VERIFY}
    #Verify that the current page is 'View Entries'
    ${correct_page_displayed}=      Correct Page is displayed    View Entries

    IF    not ${correct_page_displayed}
        RETURN
    END

    ${db_active_bins_count}=    Get the count on active Bins from the Database
    ${db_count_first_item}=    Get From List    ${db_active_bins_count}    0
    ${number_of_rows_returned_from_db}=    Get Column Data By Name       ${db_count_first_item}       total_bins

    Run Keyword If    '${number_of_rows_returned_from_db}' == '0'
    ...    Run Keyword And Warn On Failure    Fail   The are no active Bins on the database.

    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The Bins table is not displayed on the 'View Entries' page.

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #Verify that no BINS are displayed after the search text is populated
    IF    "${number_of_items_on_current_page_array}" != "['0 ']"
        Capture Page Screenshot   Bin_Results_Displayed.png
        Run Keyword And Continue On Failure    Fail    There are Bins displayed on the 'View Entries' page even though the incorrect Bin number was used to search. The Bin number used to search is '${BIN_NUMBER_TO_VERIFY}'.
    END

    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_page}

    ${number_of_bins_displayed_on_page_is_zero}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    0     msg=The search results were returned when searching for Bin Number '${BIN_NUMBER_TO_VERIFY}'.

    IF    '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}' or '${BIN_NUMBER_TO_VERIFY}' == ''
        Run Keyword If    not ${number_of_bins_displayed_on_page_is_zero}
        ...    Run Keyword And Continue On Failure    Fail   The search results were returned when searching for incorrect Bin Number '${BIN_NUMBER_TO_VERIFY}'. The total number of bins displayed on the Front End is '${total_number_of_items_on_page}'.
        ...  ELSE
        ...    Log Many    There were no results returned when an incorrect Bin Number was used to search. The Bin Number '${BIN_NUMBER_TO_VERIFY}' was used when searching for BINS.
    END

    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}

    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed on the View Entries page!
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The paginator icon was disabled because no search results were found for Bin Number '${BIN_NUMBER_TO_VERIFY}'.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail      The paginator icon was enabled even though the search results were not returned for Bin Number '${BIN_NUMBER_TO_VERIFY}'.

    #Verify that the results rows were not returned
    ${bins_table_rows_element_xpath}=       Catenate  ${BINS_TABLE}/mat-row
    ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${bins_table_rows_element_xpath}
    Log  Number of rows is : ${linked_bins_table_rows_total}

    Run Keyword If    '${linked_bins_table_rows_total}' == '0'
    ...    Log Many  The Bin results rows were not displayed because no search results were found for Bin Number '${BIN_NUMBER_TO_VERIFY}'.
    ...  ELSE
    ...    Fail   The Bin results rows were displayed even though the search results were not returned for Bin Number '${BIN_NUMBER_TO_VERIFY}'.


The user gets the number of Bins displayed on the current page
    Scroll Down The Page

    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The Bins table is not displayed on the 'View Entries' page.

    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_page}

    Set Global Variable     ${TOTAL_BINS_ON_PAGE}      ${total_number_of_items_on_page}

The total number of BINS displayed on the page must be the same as the initial total
    Scroll Down The Page
    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The Bins table is not displayed on the 'View Entries' page.

    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_page}

    #Verify that the total number for BINS displayed is the same as the intial count
    Run Keyword If    '${TOTAL_BINS_ON_PAGE}' != '${total_number_of_items_on_page}'
    ...    Fail   The total number of Bins displayed on the page has changed. The initial total was '${TOTAL_BINS_ON_PAGE}', and the current total is '${total_number_of_items_on_page}'
    ...  ELSE
    ...    Log Many   The total number of Bins displayed on the page has not changed. The initial total was '${TOTAL_BINS_ON_PAGE}', and the current total is '${total_number_of_items_on_page}'

The user populates the search text
    [Arguments]     ${SEARCH_TEXT}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SEARCH_TEXT}      msg=Search Criteria cannot be empty!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Run Keyword And Continue On Failure  Fail     Please provide the search Text to be used!

    Log Many    Search data provided :)
    Sleep    3s
    SeleniumLibrary.Input Text    ${SEARCH_INPUT}    ${SEARCH_TEXT}
    Capture Page Screenshot   ${SEARCH_TEXT}_populated.png

The user populates the search text and presses the Enter button
    [Arguments]     ${SEARCH_TEXT}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SEARCH_TEXT}      msg=Search Criteria cannot be empty!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Run Keyword And Continue On Failure  Fail     Please provide the search Text to be used!

    Log Many    Search data provided :)
    Sleep    3s
    SeleniumLibrary.Input Text    ${SEARCH_INPUT}    ${SEARCH_TEXT}
    Press Key   ${SEARCH_INPUT}      \\13   #Press the 'Enter' button
    Capture Page Screenshot   ${SEARCH_TEXT}_populated.png

The user must be able to click and focus on the 'Search' input
    Scroll Up The Page
    #Click on the 'SEARCH_INPUT' button
     SeleniumLibrary.Click Element    ${SEARCH_INPUT}
     Capture Page Screenshot   clicked_only.png

The user clicks the 'Search' input and press Enter button
    Scroll Up The Page
    #Click on the 'SEARCH_INPUT' button
     SeleniumLibrary.Click Element    ${SEARCH_INPUT}
    Press Key   ${SEARCH_INPUT}      \\13   #Press the 'Enter' button
    Capture Page Screenshot   clicked_n_press_enter.png

The returned Bin Numbers results must contain the characters of the search-data that was used to search
    [Arguments]     ${SEARCH_TEXT}   ${BIN_NUM}   ${BIN_TYPE}   ${ACTION_DATE}   ${CAPTURED_DATE}   ${CAPTURED_BY}   ${OUTCOME}   ${REVIEW_DATE}   ${REVIEWED_BY}   ${REVIEW_STATUS}   ${REJECTED_COMMENT}

    #Check which columns must be verified
    ${bin_number_must_be_verified}=        Set Variable If
         ...       '${BIN_NUM}' == '${EMPTY}'     ${False}
         ...       '${BIN_NUM}' != '${EMPTY}'     ${True}
    ${bin_number_verified}=        Set Variable If
         ...       '${BIN_NUM}' == '${EMPTY}'     ${True}
         ...       '${BIN_NUM}' != '${EMPTY}'     ${False}


    ${bin_type_must_be_verified}=        Set Variable If
         ...       '${BIN_TYPE}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE}' != '${EMPTY}'     ${True}
    ${bin_type_verified}=        Set Variable If
         ...       '${BIN_TYPE}' == '${EMPTY}'     ${True}
         ...       '${BIN_TYPE}' != '${EMPTY}'     ${False}

    ${action_date_must_be_verified}=        Set Variable If
         ...       '${ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${ACTION_DATE}' != '${EMPTY}'     ${True}
    ${action_date_verified}=        Set Variable If
         ...       '${ACTION_DATE}' == '${EMPTY}'     ${True}
         ...       '${ACTION_DATE}' != '${EMPTY}'     ${False}


    ${captured_date_must_be_verified}=        Set Variable If
         ...       '${CAPTURED_DATE}' == '${EMPTY}'     ${False}
         ...       '${CAPTURED_DATE}' != '${EMPTY}'     ${True}
    ${captured_date_verified}=        Set Variable If
         ...       '${CAPTURED_DATE}' == '${EMPTY}'     ${True}
         ...       '${CAPTURED_DATE}' != '${EMPTY}'     ${False}


    ${captured_by_must_be_verified}=        Set Variable If
         ...       '${CAPTURED_BY}' == '${EMPTY}'     ${False}
         ...       '${CAPTURED_BY}' != '${EMPTY}'     ${True}
    ${captured_by_verified}=        Set Variable If
         ...       '${CAPTURED_BY}' == '${EMPTY}'     ${True}
         ...       '${CAPTURED_BY}' != '${EMPTY}'     ${False}


    ${outcome_must_be_verified}=        Set Variable If
         ...       '${OUTCOME}' == '${EMPTY}'     ${False}
         ...       '${OUTCOME}' != '${EMPTY}'     ${True}
    ${outcome_verified}=        Set Variable If
         ...       '${OUTCOME}' == '${EMPTY}'     ${True}
         ...       '${OUTCOME}' != '${EMPTY}'     ${False}


    ${review_date_must_be_verified}=        Set Variable If
         ...       '${REVIEW_DATE}' == '${EMPTY}'     ${False}
         ...       '${REVIEW_DATE}' != '${EMPTY}'     ${True}
    ${review_date_verified}=        Set Variable If
         ...       '${REVIEW_DATE}' == '${EMPTY}'     ${True}
         ...       '${REVIEW_DATE}' != '${EMPTY}'     ${False}


    ${reviewd_by_must_be_verified}=        Set Variable If
         ...       '${REVIEWED_BY}' == '${EMPTY}'     ${False}
         ...       '${REVIEWED_BY}' != '${EMPTY}'     ${True}
    ${reviewd_by_verified}=        Set Variable If
         ...       '${REVIEWED_BY}' == '${EMPTY}'     ${True}
         ...       '${REVIEWED_BY}' != '${EMPTY}'     ${False}


    ${review_status_must_be_verified}=        Set Variable If
         ...       '${REVIEW_STATUS}' == '${EMPTY}'     ${False}
         ...       '${REVIEW_STATUS}' != '${EMPTY}'     ${True}
    ${review_status_verified}=        Set Variable If
         ...       '${REVIEW_STATUS}' == '${EMPTY}'     ${True}
         ...       '${REVIEW_STATUS}' != '${EMPTY}'     ${False}


    ${rejected_comment_must_be_verified}=        Set Variable If
         ...       '${REJECTED_COMMENT}' == '${EMPTY}'     ${False}
         ...       '${REJECTED_COMMENT}' != '${EMPTY}'     ${True}
    ${rejected_comment_verified}=        Set Variable If
         ...       '${REJECTED_COMMENT}' == '${EMPTY}'     ${True}
         ...       '${REJECTED_COMMENT}' != '${EMPTY}'     ${False}



    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The Bins table is not displayed on the 'View Entries' page.

    #Filter the table to show 100 items
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed for the search data: '${SEARCH_TEXT}'.
        Capture Page Screenshot   ${SEARCH_TEXT}_Bins_Not_Displayed.png
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}
    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}

    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed!
        RETURN
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    ${record_counter}=    Set Variable  0
    Capture Page Screenshot   Search_results.png
    WHILE    '${next_page_disabled_status}' == 'None' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${BINS_TABLE}/mat-row
        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}
        Run Keyword If    '${linked_bins_table_rows_total}' == '0'
        ...    Fail  The search for '${SEARCH_TEXT}' did not return results.

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[1]
           ${displayed_bin_number}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}

           #Verify only columns that must be verified
           IF    ${bin_number_must_be_verified}
                ${bin_number_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_number}    ${BIN_NUM}
           END
           IF    ${bin_type_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[2]
                ${displayed_bin_type}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                #Check how many Bin Types are linked to the current bin as per the front end display
                ${displayed_bin_type_array}=       Split String    ${displayed_bin_type}     separator=,
                ${user_bin_type_array}=       Split String    ${BIN_TYPE}     separator=,
                ${all_user_bin_types_found}=    Set Variable    ${True}
                #Loop through the user provided bin types and verify that they are all displayed
                FOR    ${user_bin_type_element}    IN    @{user_bin_type_array}
                    ${bin_type_element}=    Set Variable   ${user_bin_type_element.strip()}
                    Log    ${bin_type_element}
                    #Loop through the list of displayed bin types and search for the current user's bin type
                    FOR    ${displayed_bin_type_element}    IN    @{displayed_bin_type_array}
                        ${bin_type_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_type_element.strip()}    ${bin_type_element}
                        IF    ${bin_type_verified}
                            Exit for loop
                        END
                    END
                    IF    not ${bin_type_verified}
                        ${all_user_bin_types_found}=    Set Variable    ${False}
                    END
                END
                IF    not ${all_user_bin_types_found}
                      ${bin_type_verified}=    Set Variable    ${False}
                END
           END

           IF    ${action_date_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[3]
                ${displayed_bin_action_date}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${action_date_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_action_date}    ${ACTION_DATE}
           END
           IF    ${captured_date_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[4]
                ${displayed_bin_captured_date}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${captured_date_verified}=      Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_captured_date}    ${CAPTURED_DATE}
           END
           IF    ${captured_by_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[5]
                ${displayed_bin_captured_by}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${captured_by_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_captured_by}    ${CAPTURED_BY}
           END
           IF    ${outcome_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[6]
                ${displayed_bin_outcome}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${outcome_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_outcome}    ${OUTCOME}
           END
           IF    ${review_date_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[7]
                ${displayed_bin_review_date}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${review_date_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_review_date}    ${REVIEW_DATE}
           END
           IF    ${reviewd_by_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[8]
                ${displayed_bin_reviewed_by}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${reviewd_by_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_reviewed_by}    ${REVIEWED_BY}
           END
           IF    ${review_status_must_be_verified}
                ${curr_row_bin_ele}=          Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[9]
                ${displayed_bin_status}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${review_status_verified}=    Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_status}    ${REVIEW_STATUS}
           END
           IF    ${rejected_comment_must_be_verified}
                ${curr_row_bin_ele}=                 Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[10]
                ${displayed_bin_rejected_comment}=      SeleniumLibrary.Get Text    ${curr_row_bin_ele}
                ${rejected_comment_verified}=       Run Keyword And Return Status     Should Be Equal As Strings    ${displayed_bin_rejected_comment}    ${REVIEW_STATUS}
           END

           ${verification_passed}=      All Variables are True      ${bin_number_verified}      ${bin_type_verified}    ${action_date_verified}     ${captured_date_verified}       ${captured_by_verified}     ${outcome_verified}     ${review_date_verified}     ${reviewd_by_verified}      ${review_status_verified}       ${rejected_comment_verified}
           IF    ${verification_passed}
                Exit For Loop
           END

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END
        IF    ${verification_passed}
            Log Many    The Bin Number: '${displayed_bin_number}', linked to Bin Type(s): '${BIN_TYPE}', that matches the search text: '${SEARCH_TEXT}' was found on the View Entries page.
            RETURN
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled

        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
        IF    '${next_page_disabled_status}' != 'None' or '${record_counter}' == '${total_number_of_items_on_page}'
            IF   not ${verification_passed}
                Fail    The Bin Number: '${displayed_bin_number}', linked to Bin Type(s): '${BIN_TYPE}', that matches the search text: '${SEARCH_TEXT}' was not found on the View Entries page.
            END
            RETURN
        ELSE
            IF    '${next_page_disabled_status}' == 'None'
                     #Click on the 'Next' button
                     SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                     Sleep    4s
            END
        END
    END


The user sorts the Bins results
    [Arguments]     ${SORT_BY}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SORT_BY}      msg=The column name data that must be used to sort is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name that must be used for sorting BINS!

    Log Many    Sort data provided :)
    ${provided_data_is_valid}=   table column name is valid      ${SORT_BY}

    Run Keyword If    not ${provided_data_is_valid}
    ...    Fail  The column name: '${SORT_BY}' is not valid for the View Entries table sorting.

    Capture Page Screenshot   ${SORT_BY}_header_sort.png

    Sort the search results based on column name   ${SORT_BY}

    Capture Page Screenshot   ${SORT_BY}_header_sorted.png

The user sorts the Bins results for the second time
    [Arguments]     ${SORT_BY}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SORT_BY}      msg=The column name data that must be used to sort is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name that must be used for sorting BINS!

    Log Many    Sort data provided :)
    ${provided_data_is_valid}=   table column name is valid      ${SORT_BY}

    Run Keyword If    not ${provided_data_is_valid}
    ...    Fail  The column name: '${SORT_BY}' is not valid for the View Entries table sorting.

    Capture Page Screenshot   ${SORT_BY}_header_sort.png

    Sort the search results based on column name   ${SORT_BY}

    Capture Page Screenshot   ${SORT_BY}_header_sorted.png

The user sorts the Bins results for the third time
    [Arguments]     ${SORT_BY}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SORT_BY}      msg=The column name data that must be used to sort is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name that must be used for sorting BINS!

    Log Many    Sort data provided :)
    ${provided_data_is_valid}=   table column name is valid      ${SORT_BY}

    Run Keyword If    not ${provided_data_is_valid}
    ...    Fail  The column name: '${SORT_BY}' is not valid for the View Entries table sorting.

    Capture Page Screenshot   ${SORT_BY}_header_sort.png

    Sort the search results based on column name   ${SORT_BY}

    Capture Page Screenshot   ${SORT_BY}_header_sorted.png

Sort the search results based on column name
    [Arguments]     ${SORT_BY}

    Scroll Element Into View    ${VIEW_BINS_BTN}
    SeleniumLibrary.Click Element    ${VIEW_BINS_BTN}
    Sleep   3s


    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The Bins table is not displayed on the 'View Entries' page.

    #Loop through the displayed Bins table column names and search for the columns that must be clicked
    ${linked_bins_table_header_element_xpath}=       Catenate  ${BINS_TABLE}/mat-header-row
    ${linked_bins_table_header_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_header_element_xpath}
    Run Keyword If    '${linked_bins_table_header_total}' == '0'
    ...    Fail  The table header is not displayed for the View Entries page.

    ${linked_bins_table_cols_element_xpath}=       Catenate  ${linked_bins_table_header_element_xpath}/mat-header-cell
    ${linked_bins_table_cols_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_cols_element_xpath}
    Run Keyword If    '${linked_bins_table_cols_total}' == '0'
    ...    Fail  The View Entries table header does not have column names.

    #Read the column names elements
    ${column_elements}=     SeleniumLibrary.Get Webelements     ${linked_bins_table_cols_element_xpath}
    ${provided_data}=   Set Variable     ${SORT_BY.strip()}
    ${provided_data}=   Convert To Lower Case    ${provided_data}
    ${col_clicked}=    Set Variable   ${False}
    FOR    ${column_element}    IN    @{column_elements}
        ${column_text}=     SeleniumLibrary.Get Text        ${column_element}
        Log  Col Name: '${column_text}'
        ${column_text}=   Convert To Lower Case    ${column_text.strip()}
        IF    '${column_text.strip()}' == '${provided_data}'
             Log Many   The column named: '${SORT_BY}' was found and clicked.
             Scroll Element Into View    ${column_element}
             SeleniumLibrary.Click Element    ${column_element}
             ${col_clicked}=    Set Variable   ${True}
             Exit For Loop
        END
    END

    IF    not ${col_clicked}
         Run Keyword And Continue On Failure  Fail   The column named: '${SORT_BY}' was not found hence it could not be clicked.
    END


The expected columns must be displayed on the View Entries table
    [Arguments]     ${COL_NAMES}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${COL_NAMES}      msg=The column name data that must be verified is not given!
    #This is the boolean value that checks if the data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name(s) that must be verified!

    ${col_names_array}=     Split String     ${COL_NAMES}   separator=,

    FOR    ${element}    IN    @{col_names_array}
        Log    ${element}
        ${provided_data_is_valid}=   table column name is valid      ${element}
        Run Keyword If    ${provided_data_is_valid}
        ...    Verify that the column name is displayed on the View Entries table    ${element}
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail   The column name: '${element}' is not valid for the View Entries table.
    END
    Capture Page Screenshot   ${element}_header_sort.png



Verify that the column name is displayed on the View Entries table
    [Arguments]     ${SORT_BY}

    Scroll Element Into View    ${VIEW_BINS_BTN}
    SeleniumLibrary.Click Element    ${VIEW_BINS_BTN}
    Sleep   3s


    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The Bins table is not displayed on the 'View Entries' page.

    #Loop through the displayed Bins table column names and search for the columns that must be clicked
    ${linked_bins_table_header_element_xpath}=       Catenate  ${BINS_TABLE}/mat-header-row
    ${linked_bins_table_header_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_header_element_xpath}
    Run Keyword If    '${linked_bins_table_header_total}' == '0'
    ...    Fail  The table header is not displayed for the View Entries page.

    ${linked_bins_table_cols_element_xpath}=       Catenate  ${linked_bins_table_header_element_xpath}/mat-header-cell
    ${linked_bins_table_cols_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_cols_element_xpath}
    Run Keyword If    '${linked_bins_table_cols_total}' == '0'
    ...    Fail  The View Entries table header does not have column names.

    #Read the column names elements
    ${column_elements}=     SeleniumLibrary.Get Webelements     ${linked_bins_table_cols_element_xpath}
    ${provided_data}=   Set Variable     ${SORT_BY.strip()}
    ${provided_data}=   Convert To Lower Case    ${provided_data}
    ${col_found}=    Set Variable   ${False}
    FOR    ${column_element}    IN    @{column_elements}
        ${column_text}=     SeleniumLibrary.Get Text        ${column_element}
        Log  Col Name: '${column_text}'
        ${column_text}=   Convert To Lower Case    ${column_text.strip()}
        IF    '${column_text.strip()}' == '${provided_data}'
             Log Many   The column named: '${SORT_BY}' was found and clicked.
             Scroll Element Into View    ${column_element}
             ${col_found}=    Set Variable   ${True}
             Exit For Loop
        END
    END

    Run Keyword If    ${col_found}
    ...    Log Many   The column named: '${SORT_BY}' was found on the View Entries table.
    ...  ELSE
    ...    Run Keyword And Continue On Failure  Fail   The column named: '${SORT_BY}' was not found on the View Entries table.


table column name is valid
    [Arguments]     ${SORT_BY}

    ${data_is_valid}=   Set Variable     ${False}
    ${provided_data}=   Set Variable     ${SORT_BY.strip()}
    ${provided_data}=   Convert To Lower Case    ${provided_data}
    ${data_is_valid}=        Set Variable If
         ...       '${provided_data}' == 'bin number'      ${True}
         ...       '${provided_data}' == 'binnumber'       ${True}
         ...       '${provided_data}' == 'bin type'      ${True}
         ...       '${provided_data}' == 'bintype'       ${True}
         ...       '${provided_data}' == 'action date'     ${True}
         ...       '${provided_data}' == 'actiondate'      ${True}
         ...       '${provided_data}' == 'captured date'   ${True}
         ...       '${provided_data}' == 'captureddate'    ${True}
         ...       '${provided_data}' == 'captured by'     ${True}
         ...       '${provided_data}' == 'outcome'         ${True}
         ...       '${provided_data}' == 'capturedby'      ${True}
         ...       '${provided_data}' == 'review date'     ${True}
         ...       '${provided_data}' == 'reviewdate'      ${True}
         ...       '${provided_data}' == 'reviewed by'     ${True}
         ...       '${provided_data}' == 'reviewedby'      ${True}
         ...       '${provided_data}' == 'review status'   ${True}
         ...       '${provided_data}' == 'reviewstatus'    ${True}
         ...       '${provided_data}' == 'rejected comment'   ${True}
         ...       '${provided_data}' == 'rejectedcomment'   ${True}

    RETURN      ${data_is_valid}



get the column number that must be verified
    [Arguments]     ${SORT_BY}


    ${provided_data}=   Set Variable     ${SORT_BY.strip()}
    ${provided_data}=   Convert To Lower Case    ${provided_data}
    ${col_number}=        Set Variable If
         ...       '${provided_data}' == 'bin number'      1
         ...       '${provided_data}' == 'binnumber'       1
         ...       '${provided_data}' == 'bin type'      2
         ...       '${provided_data}' == 'bintype'       2
         ...       '${provided_data}' == 'action date'     3
         ...       '${provided_data}' == 'actiondate'      3
         ...       '${provided_data}' == 'captured date'   4
         ...       '${provided_data}' == 'captureddate'    4
         ...       '${provided_data}' == 'captured by'     5
         ...       '${provided_data}' == 'capturedby'      5
         ...       '${provided_data}' == 'outcome'         6
         ...       '${provided_data}' == 'review date'     7
         ...       '${provided_data}' == 'reviewdate'      7
         ...       '${provided_data}' == 'reviewed by'     8
         ...       '${provided_data}' == 'reviewedby'      8
         ...       '${provided_data}' == 'review status'   9
         ...       '${provided_data}' == 'reviewstatus'    9
         ...       '${provided_data}' == 'rejected comment'   10
         ...       '${provided_data}' == 'rejectedcomment'   10

    RETURN      ${col_number}


The returned BINS must be sorted in ascending order
    [Arguments]     ${COLUMN_NAME_TO_VERIFY}


    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${COLUMN_NAME_TO_VERIFY}      msg=The column name data that must be used to sort verification is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name that was used for sorting BINS!

    Log Many    Sort data provided :)
    ${provided_data_is_valid}=   table column name is valid      ${COLUMN_NAME_TO_VERIFY}
    Run Keyword If    not ${provided_data_is_valid}
    ...    Fail  The column name: '${COLUMN_NAME_TO_VERIFY}' is not valid for the View Entries table sorting.

    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The Bins table is not displayed on the 'View Entries' page.

    #Filter the table to show 100 items
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}

        ELSE
            Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed on the page.
        Capture Page Screenshot   Bins_Not_Displayed.png
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}

    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Fail    The 'Next Page' paginator icon is not displayed!
        RETURN
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    ${record_counter}=    Set Variable  0
    ${values}=    Create List
    ${col_index_to_verify}=     get the column number that must be verified     ${COLUMN_NAME_TO_VERIFY.strip()}

    WHILE    '${next_page_disabled_status}' == 'None' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
       ${linked_bins_table_rows_element_text}=       Catenate  ${BINS_TABLE}/mat-row
        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}
        Run Keyword If    '${linked_bins_table_rows_total}' == '0'
        ...    Fail  The search did not return results.
        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${BINS_TABLE}/mat-row[${index}]/mat-cell[${col_index_to_verify}]
           ${cell_text}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           Log  cell_text: '${cell_text}'
           Append To List    ${values}    ${cell_text}
           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
        IF    '${next_page_disabled_status}' != 'None' or '${record_counter}' == '${total_number_of_items_on_page}'
                ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
                Log Many  ${paginator_range_data}
                ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
                ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
                ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
                ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
                Log Many    ${total_number_of_items_on_current_page}
                Log Many    ${total_number_of_items_on_page}
                Run Keyword If    '${total_number_of_items_on_current_page}' == '${record_counter}'
                ...    Log Many     All ${total_number_of_items_on_current_page} Bins have been verified successfully.
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  Not all Bins have been verified. The total number of Bins on the page is '${total_number_of_items_on_current_page}', but the total number of bins that have been verified is  '${record_counter}'.


                #Verify that records have been sorted in ascending order
                ${ascending}=    Data is sorted in ascending order    ${values}

                Run Keyword If    ${ascending}
                ...    Log Many    The table column: '${COLUMN_NAME_TO_VERIFY.strip()}' is in ascending order. The data is populated as ${values}.
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  The table column: '${COLUMN_NAME_TO_VERIFY.strip()}' is not in ascending order. The data is populated as ${values}.

                ${random_word}=     Generate random word
                Capture Page Screenshot   data_sorted_${random_word}_by_${COLUMN_NAME_TO_VERIFY}.png
                RETURN
        ELSE
            IF    '${next_page_disabled_status}' == 'None'
                     #Click on the 'Next' button
                     SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                     Sleep    4s
            END
        END
    END



The user must be able to filter the Bins displayed on the page
    [Arguments]     ${FILTER_NUMBER_TO_USE}


    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${FILTER_NUMBER_TO_USE}      msg=The column name data that must be used to sort verification is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the number that must used to filter BINS! The number can either be 5, 10, 25 or 100.

    Log Many    Filter data provided :)
    ${FILTER_NUMBER_TO_USE}=    Set Variable    ${FILTER_NUMBER_TO_USE.strip()}

    Run Keyword If    '${FILTER_NUMBER_TO_USE}' != '5' and '${FILTER_NUMBER_TO_USE}' != '10' and '${FILTER_NUMBER_TO_USE}' != '25' and '${FILTER_NUMBER_TO_USE}' != '100'
    ...    Fail     Please provide the correct number that must used to filter BINS! The number can either be 5, 10, 25 or 100.

    #Decide which filter number to click
    ${filter_element}=       Set Variable If
         ...       '${FILTER_NUMBER_TO_USE}' == '5'     ${FILTER_LIST_5_ITEMS}
         ...       '${FILTER_NUMBER_TO_USE}' == '10'    ${FILTER_LIST_10_ITEMS}
         ...       '${FILTER_NUMBER_TO_USE}' == '25'    ${FILTER_LIST_25_ITEMS}
         ...       '${FILTER_NUMBER_TO_USE}' == '100'   ${FILTER_LIST_100_ITEMS}



    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The linked Bins table is not displayed on the 'View Entries' page.

    #Filter the table to show 100 items
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}

        ELSE
            Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${filter_element}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed on the page.
        Capture Page Screenshot   Bins_Data_Not_Displayed.png
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Evaluate    int('${total_number_of_items_on_current_page.strip()}')
    ${total_number_of_items_on_page}=           Evaluate    int('${total_number_of_items_on_page.strip()}')
    ${FILTER_NUMBER_TO_USE}=                    Evaluate    int('${FILTER_NUMBER_TO_USE}')
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    #Verify that the total number of Bins displayed on the current page are as per the user's chosen number for filtering

    IF    ${total_number_of_items_on_current_page} < ${FILTER_NUMBER_TO_USE}
        IF    ${total_number_of_items_on_page} < ${FILTER_NUMBER_TO_USE}
            Log Many     The number of Bins displayed on the page is '${total_number_of_items_on_current_page}' which is the total number of BINS linked to the current Bin Type.
        ELSE
            Run Keyword And Continue On Failure    Fail  The number of Bins displayed on the page is '${total_number_of_items_on_current_page}' which is less than the filtered number: '${FILTER_NUMBER_TO_USE}'.
        END
    ELSE
        IF    ${total_number_of_items_on_current_page} == ${FILTER_NUMBER_TO_USE}
            Log Many     The number of Bins displayed on the page is '${FILTER_NUMBER_TO_USE}', this is the same as the number that was used to filer results.
        ELSE
            Run Keyword And Continue On Failure    Fail  The number of BINS displayed on the current page is greater than the filtered number. The number used to filter BINS is '${FILTER_NUMBER_TO_USE}', and the number of BINS displayed on the page is '${total_number_of_items_on_current_page}'.
        END
    END

    Capture Page Screenshot   bins_data_filtered_by_${FILTER_NUMBER_TO_USE}.png

The user scrolls to the bottom of the page
    Scroll Down The Page

The pagination icon should be visible

    Capture Page Screenshot   scroll_to_the_bottom_of_page.png
    ${paginator_element_displayed}=  Run Keyword And Return Status         SeleniumLibrary.Wait Until Element is Visible    ${PAGINATOR_ICON}
    Run Keyword If    ${paginator_element_displayed}
    ...    Log Many  The paginator icon was displayed on the View Entries page.
    ...  ELSE
    ...    Fail     The paginator icon was not displayed on the View Entries page.

The user scrolls to the top of the page
    Scroll Up The Page


The search field should be visible

    Capture Page Screenshot   scroll_to_the_top_of_page.png
    ${search_element_displayed}=  Run Keyword And Return Status         SeleniumLibrary.Wait Until Element is Visible    ${SEARCH_INPUT}
    Run Keyword If    ${search_element_displayed}
    ...    Log Many  The 'Search' input was displayed on the View Entries page.
    ...  ELSE
    ...    Fail     The 'Search' input was not displayed on the View Entries page.

The user navigates to the next page
    Scroll Down The Page
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Fail    There are no Bins displayed on the 'View Entries' page.
        RETURN
    END

    #Save the records count, for the bins displayed on the current page
    ${first_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[0]
    ${first_record_number_on_current_page}=   Set Variable    ${first_record_number_on_current_page.strip()}
    ${last_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${last_record_number_on_current_page}=   Set Variable    ${last_record_number_on_current_page.strip()}

    Set Global Variable     ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}    ${first_record_number_on_current_page}
    Set Global Variable     ${LAST_RECORD_ON_PREVIOUS_SCREEN}    ${last_record_number_on_current_page}

    #Verify whether the user can navigate to the next page
    ${int_total_number_of_items_on_page}=    Convert To Integer    ${total_number_of_items_on_page}
    Run Keyword If    ${int_total_number_of_items_on_page} > 5
    ...    Log Many     There are ${int_total_number_of_items_on_page} Bins displayed on the View Entries Table, hence navigation to the next page is possible.
    ...  ELSE
    ...    Fail         There are ${int_total_number_of_items_on_page} Bins displayed on the View Entries Table, hence navigation to the next page is not possible.

    #Filter the table to show 5 items per page
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}
    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}
        ELSE
            Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END
    SeleniumLibrary.Click Element    ${FILTER_LIST_5_ITEMS}
    Sleep    3s


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Fail    The 'Next Page' paginator icon is not displayed on the View Entries page!
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    
    Run Keyword If    ${disabled_attribute} == 'True'
    ...    Fail    The 'Next Page' paginator icon is disabled hence it cannot be clicked!
    ...  ELSE
    ...    Log Many    The 'Next Page' paginator icon is enabled hence it will be clicked.

    Capture Page Screenshot   next_btn_not_clicked.png

    SeleniumLibrary.Click Element  ${PAGINATOR_ICON}
    Sleep    3s

    Capture Page Screenshot   next_btn_clicked.png

Get Number of records on current page

    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Fail    There are no Bins displayed on the 'View Entries' page.
        RETURN
    END

    #Save the records count, for the bins displayed on the current page
    ${first_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[0]
    ${first_record_number_on_current_page}=   Set Variable    ${first_record_number_on_current_page.strip()}
    ${last_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${last_record_number_on_current_page}=   Set Variable    ${last_record_number_on_current_page.strip()}

    RETURN  ${first_record_number_on_current_page},${last_record_number_on_current_page}


The next page items must be displayed
    Scroll Down The Page
    ${total_number_of_items_on_page_array}=         Get Number of records on current page
     #Save the records count, for the bins displayed on the current page
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}   separator=,
    ${first_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[0]
    ${first_record_number_on_current_page}=   Set Variable    ${first_record_number_on_current_page.strip()}
    ${last_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${last_record_number_on_current_page}=   Set Variable    ${last_record_number_on_current_page.strip()}

    #Verify that the user is on the next page
    ${first_record_number_on_current_page}=    Convert To Integer    ${first_record_number_on_current_page}
    ${last_record_number_on_current_page}=    Convert To Integer    ${last_record_number_on_current_page}
    ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}=    Convert To Integer    ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}
    ${LAST_RECORD_ON_PREVIOUS_SCREEN}=    Convert To Integer    ${LAST_RECORD_ON_PREVIOUS_SCREEN}

    Capture Page Screenshot   currs_page_items.png

    Run Keyword If    ${first_record_number_on_current_page} > ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}
    ...    Log Many  The next page's records are displayed.
    ...  ELSE
    ...    Fail     The next page's records are not displayed. The first item number displayed on the current page is '${first_record_number_on_current_page}', and the last item displayed is  '${last_record_number_on_current_page}'.


The user navigates to the previous page
    Scroll Down The Page
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Fail    There are no Bins displayed on the 'View Entries' page.
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    #Verify whether the user can navigate to the next page
    ${int_total_number_of_items_on_page}=    Convert To Integer    ${total_number_of_items_on_page}
    Run Keyword If    ${int_total_number_of_items_on_page} > 5
    ...    Log Many     There are ${int_total_number_of_items_on_page} Bins displayed on the View Entries Table, hence navigation to the next page is possible.
    ...  ELSE
    ...    Fail         There are ${int_total_number_of_items_on_page} Bins displayed on the View Entries Table, hence navigation to the next page is not possible.


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PREV_PAGE_PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Fail    The 'Previous Page' paginator icon is not displayed on the View Entries page!
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PREV_PAGE_PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'

    Run Keyword If    ${disabled_attribute} == 'True'
    ...    Fail    The 'Previous Page' paginator icon is disabled hence it cannot be clicked!
    ...  ELSE
    ...    Log Many    The 'Previous Page' paginator icon is enabled hence it will be clicked.

    Capture Page Screenshot   prev_btn_not_clicked.png

    SeleniumLibrary.Click Element  ${PREV_PAGE_PAGINATOR_ICON}
    Sleep    3s

    Capture Page Screenshot   prev_btn_clicked.png


The previous page items must be displayed
    Sleep   4s
    Scroll Down The Page
    ${total_number_of_items_on_page_array}=         Get Number of records on current page
     #Save the records count, for the bins displayed on the current page
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}   separator=,
    ${first_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[0]
    ${first_record_number_on_current_page}=   Set Variable    ${first_record_number_on_current_page.strip()}
    ${last_record_number_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${last_record_number_on_current_page}=   Set Variable    ${last_record_number_on_current_page.strip()}

    #Verify that the user is on the next page
    ${first_record_number_on_current_page}=    Convert To Integer    ${first_record_number_on_current_page}
    ${last_record_number_on_current_page}=    Convert To Integer    ${last_record_number_on_current_page}
    ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}=    Convert To Integer    ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}
    ${LAST_RECORD_ON_PREVIOUS_SCREEN}=    Convert To Integer    ${LAST_RECORD_ON_PREVIOUS_SCREEN}

    Capture Page Screenshot   prevs_page_items.png

    Run Keyword If    ${first_record_number_on_current_page} == ${INITIAL_RECORD_ON_PREVIOUS_SCREEN}
    ...    Log Many  The previous page's records are displayed.
    ...  ELSE
    ...    Fail     The previous page's records are not displayed. The first item number displayed on the current page is '${first_record_number_on_current_page}', and the last item displayed is  '${last_record_number_on_current_page}'. The expected number for the first item on page is '${INITIAL_RECORD_ON_PREVIOUS_SCREEN}', while expected number for the last item is '${LAST_RECORD_ON_PREVIOUS_SCREEN}'.

The modified bin number must be displayed at the top of the Bins results on the View Entries Page
    [Arguments]     ${BIN_NUMBER_TO_VERIFY}
    #Verify that the current page is 'View Entries'
    ${correct_page_displayed}=      Correct Page is displayed    View Entries
    IF    not ${correct_page_displayed}
        RETURN
    END
    ${USER_BIN_NUMBER_TO_VERIFY}=      Remove Quotes       ${BIN_NUMBER_TO_VERIFY}
    #This is the boolean value that checks if the bin number is provided
    ${user_bin_number_is_provided}=        Set Variable If
         ...       '${USER_BIN_NUMBER_TO_VERIFY}' == '${EMPTY}'     ${False}
         ...       '${USER_BIN_NUMBER_TO_VERIFY}' == ''             ${False}
         ...       '${USER_BIN_NUMBER_TO_VERIFY}' != '${EMPTY}'     ${True}
         ...       '${USER_BIN_NUMBER_TO_VERIFY}' != ''             ${True}


    Run Keyword If    not ${user_bin_number_is_provided}
    ...    Fail   Please provide the bin number that must be verified.

    ${db_active_bins_count}=    Get the count on active Bins from the Database
    ${db_count_first_item}=    Get From List    ${db_active_bins_count}    0
    ${number_of_rows_returned_from_db}=    Get Column Data By Name       ${db_count_first_item}       total_bins

    Run Keyword If    '${number_of_rows_returned_from_db}' == '0'
    ...    Run Keyword And Warn On Failure    Fail   The are no active Bins on the database.

    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The Bins table is not displayed on the 'View Entries' page.
    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Run Keyword And Warn On Failure    Fail    There are no Bins displayed on the 'View Entries' page.
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${bins_displayed_on_page_equal_db_total}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    ${number_of_rows_returned_from_db}

    IF    '${BIN_NUMBER_TO_VERIFY}' == '${EMPTY}' or '${BIN_NUMBER_TO_VERIFY}' == ''
        Run Keyword If    not ${bins_displayed_on_page_equal_db_total}
        ...    Run Keyword And Warn On Failure    Fail   The total number of Bins that are displayed on the View Entries page is not the same as the DB Bins total. The DB total is '${number_of_rows_returned_from_db}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
        ...  ELSE
        ...    Log Many    The total number of active Bins that are displayed on the View Entries page is the same as the total number of active DB Bins. The DB total is '${number_of_rows_returned_from_db}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
    END
    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}
    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed on the View Entries page!
        RETURN
    END

    #Get the total number of rows for Bins displayed on the page
    ${bins_table_rows_element_xpath}=       Catenate  ${BINS_TABLE}/mat-row
    ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${bins_table_rows_element_xpath}
    Log  Number of rows is : ${linked_bins_table_rows_total}

    #Get the first Bin Number displayed on the page
    ${curr_row_bin_ele}=           Catenate  ${BINS_TABLE}/mat-row[1]/mat-cell[1]
    ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
    ${bin_number}=      Set Variable    ${bin_number.strip()}
    ${BIN_NUMBER_TO_VERIFY}=      Set Variable    ${BIN_NUMBER_TO_VERIFY.strip()}

    #Verify that the first Bin Number displyed is the same as the modified Bin
    ${modified_bin_is_first_on_the_table}=     Run Keyword And Return Status       Should Be Equal As Strings    ${BIN_NUMBER_TO_VERIFY}    ${bin_number}

    Capture Page Screenshot   Bins_results_Displayed.png
    Run Keyword If    not ${modified_bin_is_first_on_the_table}
    ...    Fail   The bin number '${BIN_NUMBER_TO_VERIFY}' is not displayed as the first bin on the page. The first bin on the page is '${bin_number}'.
    ...  ELSE
    ...    Log Many   The bin number '${BIN_NUMBER_TO_VERIFY}' is displayed as the first bin on the page.


The user retrieves an existing Bin Number from the View Menu
    ${EXISTING_BIN_NUMBER_RETRIEVAL}=    Get Text    ${EXISTING_BIN_NUMBER}
    Log    Existing Bin Number Retrieved is:${EXISTING_BIN_NUMBER_RETRIEVAL}
    Sleep    3s
    Set Global Variable    ${GLOBAL_EXISTING_BIN_NUMBER}    ${EXISTING_BIN_NUMBER_RETRIEVAL}

The user Gets the Bin Captured Dates from the View Menu
    # Retrieve the global bin list
    @{bin_list}=    Get Variable Value    ${GLOBAL_BIN_LIST}

    # Initialize list to store matched bin dates
        @{matched_dates}=    Create List

    FOR    ${bin}    IN    @{bin_list}
        # Click on the search bar and enter the bin number
        Click Element    ${SEARCH_INPUT}   # Update the XPath for the search bar if needed
        Input Text    ${SEARCH_INPUT}    ${bin}
        Sleep    5s

        # Wait for the search results to update
        Wait Until Element Is Visible    xpath=//*[text()[normalize-space(.)='${bin}']]    timeout=5s
    
        # Capture the created date for the searched bin
        ${bin_date}=    Get Text    xpath=//mat-cell[contains(@class, 'mat-column-capturedDate')]
    
        # Log and store the date
        Log    Captured date for bin ${bin}: ${bin_date}
        Append To List    ${matched_dates}    ${bin_date}
    END
    Log Many    ${matched_dates}
    Set Global Variable    ${GLOBAL_MATCHED_DATES}    ${matched_dates}

The user navigates to the View Menu 
    ${VIEW_MENU}=    Get WebElement    xpath=//*[text()[normalize-space(.)='View']]
    Click Element    ${VIEW_MENU}
    Sleep    3s