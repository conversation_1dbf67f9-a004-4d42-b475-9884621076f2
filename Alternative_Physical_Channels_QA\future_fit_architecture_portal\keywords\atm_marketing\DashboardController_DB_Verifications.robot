*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             RequestsLibrary
Library                                             JSONLibrary
Library                                             String
Library                                            ../../utility/APCAccessToken.py

#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***
Verify dashboard controller field on the database
    ${field_to_verify}=   Get Environment Variable    FIELD_TO_VERIFY
    Run Keyword If    '${field_to_verify}' == 'totalCampaigns'
    ...    Verify that the number of Campaigns displayed on the controller is the same as the ones saved in the Database
    ...  ELSE IF    '${field_to_verify}' == 'latestScheduleCount'
    ...    Verify that the number of latestScheduleCount displayed on the controller is the same as the ones saved in the Database
    ...  ELSE IF    '${field_to_verify}' == 'failedUploadSchedulesCount'
    ...    Verify that the number of failedUploadSchedulesCount displayed on the controller is the same as the ones saved in the Database
    ...  ELSE IF    '${field_to_verify}' == 'currentVersion'
    ...    Verify that the currentVersion displayed on the controller is the same as the ones saved in the Database
    ...  ELSE IF    '${field_to_verify}' == 'atmScheduleResultVersions'
    ...    Verify that the atmScheduleResultVersions displayed on the controller is the same as the ones saved in the Database




Verify that the number of Campaigns displayed on the controller is the same as the ones saved in the Database

   ${total_campaigns_available_on_DB}=          Get the number of campaigns available for the current schedule
   ${totalCampaigns_from_api}=   Get Environment Variable    totalCampaigns
   Log  DB Value: '${total_campaigns_available_on_DB}'
   Log  API Value: '${totalCampaigns_from_api}'
   
   Should Be Equal As Strings    ${total_campaigns_available_on_DB}    ${totalCampaigns_from_api}


Verify that the number of latestScheduleCount displayed on the controller is the same as the ones saved in the Database

   ${latestScheduleCount_available_on_DB}=     Get the latest schedule request count from the database
   ${latestScheduleCount_from_api}=   Get Environment Variable    latestScheduleCount
   Log  DB Value: '${latestScheduleCount_available_on_DB}'
   Log  API Value: '${latestScheduleCount_from_api}'

   Should Be Equal As Strings    ${latestScheduleCount_available_on_DB}    ${latestScheduleCount_from_api}


Verify that the number of failedUploadSchedulesCount displayed on the controller is the same as the ones saved in the Database

   ${failedUploadSchedulesCount_available_on_DB}=     Get the latest schedule request count for failed requests from the database
   ${failedUploadSchedulesCount_from_api}=   Get Environment Variable    failedUploadSchedulesCount
   Log  DB Value: '${failedUploadSchedulesCount_available_on_DB}'
   Log  API Value: '${failedUploadSchedulesCount_from_api}'

   Should Be Equal As Strings    ${failedUploadSchedulesCount_available_on_DB}    ${failedUploadSchedulesCount_from_api}


Verify that the currentVersion displayed on the controller is the same as the ones saved in the Database

   ${currentVersion_active_on_DB}=     Get the currentVersion from the database
   ${currentVersion_from_api}=   Get Environment Variable    currentVersion
   Log  DB Value: '${currentVersion_active_on_DB}'
   Log  API Value: '${currentVersion_from_api}'

   Should Be Equal As Strings    ${currentVersion_from_api}    ${currentVersion_active_on_DB}

Verify that the atmScheduleResultVersions displayed on the controller is the same as the ones saved in the Database

   #${currentVersion_active_on_DB}=     Get the currentVersion from the database
   ${atmScheduleResultVersions_from_api}=   Get Environment Variable    atmScheduleResultVersions
   #Log  DB Value: '${currentVersion_active_on_DB}'
   Log  API Value: '${atmScheduleResultVersions_from_api}'
   Log To Console  API Value: '${atmScheduleResultVersions_from_api}'

    ${new_string}=   Replace String    ${atmScheduleResultVersions_from_api}    '    "
    ${dict_value}=      Convert String to Dictionary    ${new_string}
    #Loop through the ATMs dictionary
    FOR    ${item}    IN    @{dict_value}
        #Loop through the current item and get the key and value details
        Log       '#########################'
        @{item_dict}=       Get Dictionary Items    ${item}
        FOR   ${key}  ${value}  IN  @{item_dict}
            Log Many   ${key} = ${value}
        END
        Log       '#########################'

    END

Get the number of campaigns available for the current schedule

    ${db_type}=   Set Variable   'MYSQL'
    #Get the current schedule version from the database
    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion

    #Get the number of active campaigns for the current schedule
    ${total_campaigns_query}   Set Variable     SELECT count(*) AS total FROM ATM_Marketing.Campaign where version in ('${schedule_version}') and isActive = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${total_campaigns_query}    True
    ${total_number_of_campaigns}=    Get From Dictionary    ${data_base_current_schedule}    total
    RETURN   ${total_number_of_campaigns}


Get the latest schedule request count from the database

    ${db_type}=   Set Variable   'MYSQL'
    #Get the current schedule version from the database
    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion

    #Get the number of active campaigns for the current schedule
    ${schedule_request_query}   Set Variable     SELECT count(*) as total_requests FROM ATM_Marketing.MarketingResult where scheduleVersion = '${schedule_version}' and isUploadSuccessful is true
    ${data_base_schedule_request}=      Execute SQL Query  ${db_type}  ${schedule_request_query}    True
    ${total_number_of_schedule_request}=    Get From Dictionary    ${data_base_schedule_request}    total_requests
    RETURN   ${total_number_of_schedule_request}


Get the latest schedule request count for failed requests from the database

    ${db_type}=   Set Variable   'MYSQL'
    #Get the current schedule version from the database
    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion

    #Get the number of active campaigns for the current schedule
    ${schedule_request_query}   Set Variable     SELECT count(*) as total_requests FROM ATM_Marketing.MarketingResult where scheduleVersion = '${schedule_version}' and isUploadSuccessful is false
    ${data_base_schedule_request}=      Execute SQL Query  ${db_type}  ${schedule_request_query}    True
    ${total_number_of_schedule_request}=    Get From Dictionary    ${data_base_schedule_request}    total_requests
    RETURN   ${total_number_of_schedule_request}

Get the currentVersion from the database

    ${db_type}=   Set Variable   'MYSQL'
    #Get the current schedule version from the database
    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion
    RETURN   ${schedule_version}


Convert Dict To Kwargs
    [Arguments]     ${my_dict}
    &{kwargs}=      dict(${my_dict})
    RETURN  ${kwargs}