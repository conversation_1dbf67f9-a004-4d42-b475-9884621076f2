*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFT_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_GET_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
ATM marketing campaigns download
    [Arguments]        ${DOCUMENTATION}    ${DATA_FILE}    ${BASE_URL}    ${SERVICE_PATH}    ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}    &{KW_ARGS}
    Set Test Documentation  ${DOCUMENTATION}

    #Replace the schedule version with the current version
    ${current_schedule_version}=        Get current schedule version
    ${key}=     Set Variable    version
    Log Many     ${KW_ARGS}
    ${KW_ARGS}=     Modify Kwargs Value     ${key}      ${current_schedule_version}     &{KW_ARGS}
    Log Many     ${KW_ARGS}

    Given The user prepares a json payload                      ${SUITE_NAME}       ${DATA_FILE}    &{KW_ARGS}
    When The user creates a rest session                        ${BASE_URL}
    And The user makes Post Rest Call                           ${SERVICE_PATH}     ${DATA_FILE}    ${EXPECTED_STATUS_CODE}
    And The service returns http status                         ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The field value(s) returned by the POST ATMMarketing controller must correspond to the APC Database

| *** Test Cases ***                                                                                | *DOCUMENTATION*                |      *DATA_FILE*                | *BASE_URL*           | *SERVICE_PATH* | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON* |  *KW_ARGS*                                    |
| FFT - Controllers - Download Marketing Campaign(s)   | ATM marketing campaigns download    | Download Marketing Campaign(s) |   ATMMarketing                  | APC_API_UAT_BASE_URL | ATMMarketing   | 200                              | OK                     |  id=0 | atmNumber=S09005 | version=test       |
