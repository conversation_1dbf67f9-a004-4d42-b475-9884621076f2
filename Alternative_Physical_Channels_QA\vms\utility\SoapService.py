from asyncio.windows_events import NULL
from requests_pkcs12 import post,get
import csv

response=NULL
class SoapService:
    response=object

    def __init__(self):
        print('SoapService instance instantiated ')

    # -----------------GET THE BASE UR AS PASSED FROM THE DATA FILE-------------------------------------
    def get_base_url(self):
        return self.base_url

    #------------------GET THE PATH OF THE SERVICE AS PASSED FROM THE DATA FILE-------------------------
    def get_service_path(self):
        return self.service_path

    # --------------------------MAKE A SOAP CALL----------------------------------------------
    def make_soap_request(self,data_file,test_case_name, request_method):
        data_reader=self.get_data_from_csv(data_file)
        
        for row in data_reader:
            if row[0].strip(' ').upper() == test_case_name.strip(' ').upper():              
                print('------------------------ Test Case Name: ' + test_case_name )
                self.set_headers(row)
                header=self.get_headers()
                url=row[1]
                cert_path=row[4]
                cert_password=row[5]
                payload=row[6]

                if request_method.strip(' ').upper() == 'POST':
                    self.response = post(url, data=payload, headers=header, verify=False, pkcs12_filename=cert_path,pkcs12_password=cert_password)    
                else:
                    self.response = post(url, headers=header, verify=False, pkcs12_filename=cert_path,pkcs12_password=cert_password)    

                print(f'------------------------ Response Body: {self.response.text}')
                break

    #---------------------SETUP JSON FILE FOR THE PAYLOAD AND ---------------------------------------
    def make_rest_request(self,data_file,test_case_name,request_method):
        data_reader=self.get_data_from_csv(data_file)
        
        for row in data_reader:
            if row[0].strip(' ').upper() == test_case_name.strip(' ').upper():              
                print('------------------------ Test Case Name: ' + test_case_name )

                #Write the request t a json file
                if request_method.strip() == 'POST':
                    f = open('data/' + test_case_name + '.json',"w")
                    f.write(row[6])
                    f.close()
                self.set_headers(row)                
                self.base_url=row[1]
                self.service_path=row[2]
                break

    #--------------------------------RETURN LIST OF HEADERS--------------------------------------
    def get_headers(self):
        return self.headers

    #---------------------ADD HEADERS FROM THE DATA FILE TO A DICTIONARY--------------------------
    def set_headers(self,row):
        headers={}
        cols=row[3].split(",")
        for col in cols:
            newCol=col.split('==')
            headers[newCol[0].strip(' ')]=newCol[1].strip(' ') 
            self.headers=headers
        
    #----------------------READ THE DATA FILE AND REATURN THE WHOLE ROW---------------------------
    def get_data_from_csv(self,data_file):    
        a_stream = open(data_file, "r")                        
        reader = csv.reader(a_stream)
        next(reader)
        return reader

    #------------------------------GET RETURNED STATUS CODE------------------------------------------------------
    def get_response_status_code(self):
        return self.response.status_code
    
    #GET RETURNED RESPONSE
    def get_response_body(self):
        return self.response.text
