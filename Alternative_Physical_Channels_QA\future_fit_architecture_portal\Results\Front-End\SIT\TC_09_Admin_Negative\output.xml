<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="20240312 17:34:21.082" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\development\future-fit-architecture-portal-docker\tests\Front-End\TC_09_Admin_Negative.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 17:34:21.394" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="20240312 17:34:21.393" endtime="20240312 17:34:21.394"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 17:34:21.394" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="20240312 17:34:21.394" endtime="20240312 17:34:21.394"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 17:34:21.394" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20240312 17:34:21.394" endtime="20240312 17:34:21.394"/>
</kw>
<status status="PASS" starttime="20240312 17:34:21.393" endtime="20240312 17:34:21.394"/>
</kw>
<test id="s1-t1" name="FFT - Admin - Capture Campaign" line="42">
<kw name="Validates Admin User Negative Tests">
<arg>Validate that the BA cannot capture a campaign</arg>
<arg>T155057387</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<arg>B-Approver not permitted to capture a campaign</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240312 17:34:21.395" level="INFO">Set test documentation to:
Validate that the BA cannot capture a campaign</msg>
<status status="PASS" starttime="20240312 17:34:21.394" endtime="20240312 17:34:21.395"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 17:34:21.395" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057387'.</msg>
<status status="PASS" starttime="20240312 17:34:21.395" endtime="20240312 17:34:21.395"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:34:21.448" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:34:21.395" endtime="20240312 17:34:21.448"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:34:21.448" endtime="20240312 17:34:21.448"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:34:21.449" endtime="20240312 17:34:21.449"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:34:21.450" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:34:21.450" endtime="20240312 17:34:21.450"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:34:21.450" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:34:21.450" endtime="20240312 17:34:21.450"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:34:21.450" endtime="20240312 17:34:21.450"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:34:21.490" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:34:21.660" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:34:21.660" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:34:21.451" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:34:21.660" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<status status="PASS" starttime="20240312 17:34:21.449" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:34:21.660" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001CED93DC260&gt;</msg>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:34:21.660" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:34:21.660" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:34:21.660" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:34:21.660" endtime="20240312 17:34:21.660"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:34:21.660" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>