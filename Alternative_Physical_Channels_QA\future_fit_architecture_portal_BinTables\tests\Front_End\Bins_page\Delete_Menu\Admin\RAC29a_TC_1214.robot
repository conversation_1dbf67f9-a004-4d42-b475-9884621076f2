*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Setup                                          The User gets a Non-draft bin for deletion process
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-1214




*** Keywords ***
Verify Non-Draft Bin Deletion Status: Outcome=1 (Pending), IsDeleted=0
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The Bin Number is active in the Database                         ${BIN_NAME}   
    When The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    And The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates the Bin details of the bin to be deleted        ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    And The user must be able to delete the bin
    And The Bin 'Status' field should update to true (1) on the database to indicate that the deletion is pending approval    ${BIN_NAME}
    Then The Bin 'IsDeleted' field must remain false (0) on the database as the bin deletion is pending approval    ${BIN_nAME}

| *** Test Cases ***                                                                                                                                                                                                                  |        *DOCUMENTATION*        |         *BASE_URL*             |         *BIN_NAME*              |         *BIN_TYPE_NAME*          |         *BIN_ACTION_DATE*        |
| Admin_Verify that deleted Bins outcome is mapped as 1 on the database while the IsDeleted column is mapped as 0 (Before the Delete Outcome is approved)	 | Verify Non-Draft Bin Deletion Status: Outcome=1 (Pending), IsDeleted=0 |       Delete a Bin Number.    |           ${EMPTY}             | ${global_non_draft_bin_number}  |         Domestic                 |         3/21/2025                |
