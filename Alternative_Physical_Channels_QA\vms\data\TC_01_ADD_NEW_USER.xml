<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240902 08:56:23.630">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_01_ADD_NEW_USER.robot">
      <test name="Create a VMS user with a 'Browse' Role Test Case" id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240902 08:56:26.027" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240902 08:57:06.623" status="PASS" starttime="20240902 08:56:26.027"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management and Adds a User">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240902 08:57:06.623" level="INFO">When The user navigates to Admin - User Management and Adds a User</msg>
            <status endtime="20240902 08:57:24.871" status="PASS" starttime="20240902 08:57:06.623"/>
         </kw>
         <kw library="Selenium" name="Then The created user must be added to the system">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240902 08:57:24.872" level="INFO">Then The created user must be added to the system</msg>
            <status endtime="20240902 08:57:30.916" status="PASS" starttime="20240902 08:57:24.872"/>
         </kw>
         <tags>
            <tag>Create a VMS user with a 'Browse' Role Test Case</tag>
         </tags>
         <status endtime="20240902 08:57:43.945" critical="yes" status="PASS" starttime="20240902 08:56:26.026"/>
      </test>
      <status endtime="20240902 08:57:43.949" status="PASS" starttime="20240902 08:56:23.630"/>
   </suite>
   <statistics>
      <total>
         <stat pass="1" fail="0">Critical Tests</stat>
         <stat pass="1" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">Create a VMS user with a 'Browse' Role Test Case</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="1" fail="0" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
