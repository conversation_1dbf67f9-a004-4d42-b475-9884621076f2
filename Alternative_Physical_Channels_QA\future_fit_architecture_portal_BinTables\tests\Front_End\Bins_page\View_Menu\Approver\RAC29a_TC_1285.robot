*** Settings ***
#Author Name               : Thab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/Review_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1285




*** Keywords ***
Verify and review Bins displayed on Review Bins Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TO_REVIEW}     ${SELECT_BINS}
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_TO_REVIEW}' == '${EMPTY}' or '${BIN_TO_REVIEW}' == ''
         ${BIN_TO_REVIEW}=   Get a Bin to be approved from the Database
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                    ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Review' Bins tab
    And The user verifies that the Bins details displayed on page matches the database     ${BIN_TO_REVIEW}     ${SELECT_BINS}
    And The user approves the bins
    And The bins(s) must have the status of approved in the database                      ${BIN_TO_REVIEW}
    Then The modified bin number must be displayed at the top of the Bins results on the View Entries Page      ${BIN_TO_REVIEW}


| *** Test Cases ***                                                                                                                   |        *DOCUMENTATION*    		                                    |         *BASE_URL*                 |         *BIN_TO_REVIEW*                |         *SELECT_BINS*           |
| Approver_Verify BINTable Displays BIN Changes in Descending Date Order      | Verify and review Bins displayed on Review Bins Page   | Verifies bins against the database data and approves the Bin(s).   |           ${EMPTY}                 |         ${EMPTY}                       |            True                 |
