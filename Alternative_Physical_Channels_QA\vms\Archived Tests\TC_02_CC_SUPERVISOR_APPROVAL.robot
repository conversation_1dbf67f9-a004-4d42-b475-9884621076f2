*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    SUPERVISOR APPROVAL
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Test status update functionality of ATM Complaints & Complement with supervisor approval

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../Keywords/qrcode_cc/LandingPage.robot   
Resource                                            ../keywords/qrcode_cc/UpdateATMComplaints.robot 
Resource                                            ../keywords/qrcode_cc/ViewATMComplaintsAndCompliments.robot
Resource                                            ../keywords/common/common_keywords.robot
Resource                                            ../keywords/qrcode_cc/KW_SUPERVISOR_APPROVAL.robot
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
#For update function this field must be empty
${ASSIGNED_TO_VALUE}                              

*** Keyword ***
Approve or reject cancellation
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${FUNCTION}  ${Tag}  ${REFERNCE_NO}  ${NEW_STATUS}  ${CURRENT_STATUS}  ${COMMENT}  ${UPDATE_SUCCESS_MESSAGE_TEXT}  ${APPROVAL_SUCCESS_MESSAGE_TEXT}  ${ACTION}
    Set Test Documentation  ${DOCUMENTATION}
 
    Log to console  Function: ${FUNCTION} New Status: ${NEW_STATUS} Current Status: ${CURRENT_STATUS} Comment: ${COMMENT}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe
   
    And The user clicks navigate to QR Code Complaints and Compliments screen link
   
    And User searches for a compliant or task  ${REFERNCE_NO}
   
    Sleep  2s
    
    And User clicks on update link

    And User selects status from the dropdown  ${NEW_STATUS}

    And User enters a comment  ${COMMENT} 

    And User clicks on Submit button

    And User confirms action  ${UPDATE_SUCCESS_MESSAGE_TEXT}

    #Start with approval process
    And User searches for a compliant or task  ${REFERNCE_NO}
   
    Sleep  2s
    
    And User clicks on update link

    User attends to complaint in Awaitng Approval  ${NEW_STATUS}  ${ACTION}

    And User confirms action  ${APPROVAL_SUCCESS_MESSAGE_TEXT}

    #Now check if the status is updated accordingly

    And User searches for a compliant or task  ${REFERNCE_NO}

    ${NEW_STATUS} =	Set Variable If	'${ACTION}' == 'REJECT'  ${CURRENT_STATUS}  ${NEW_STATUS}  

    And User views status after rejecting or rejecting  ${NEW_STATUS}

    And User logs out

| *Test Case*                                                                                             |                                  *DOCUMENTATION*                                                                     |  *TESTRAIL_TESTCASE_ID*  |      *FUNCTION*        |    *Tag*         |  *REFERNCE_NO*|    *NEW_STATUS*     |   *CURRENT_STATUS* |                  *COMMENT*                        |*UPDATE_SUCCESS_MESSAGE_TEXT*  |        *APPROVAL_SUCCESS_MESSAGE_TEXT*                     |   *ACTION*  |
| ATM CC - Negative test - Supervisor rejects compliant in Awaiting Approval        | Approve or reject cancellation           |                ATM CC - Negative test - Supervisor rejects compliant in Awaiting Approval       |       101597684           |      UPDATE STATUS     | Update status    |  ACR0000113   |      Canceled       |     Assigned       | Negative test - Supervisor rejects compliant in Awaiting Approval    | Complaint/ Compliment Updated |   ATM Complaint cancellation has been rejected             |    REJECT
| ATM CC - Supervisor approves compliant in Awaiting Approval                       | Approve or reject cancellation           |                ATM CC - Supervisor approves compliant in Awaiting Approval                      |       101597685           |      UPDATE STATUS     | Update status    |  ACR0000113   |      Canceled       |     Assigned       | Supervisor approves compliant in Awaiting Approval                   | Complaint/ Compliment Updated |   ATM Complaint cancellation has been approved             |    APPROVE

