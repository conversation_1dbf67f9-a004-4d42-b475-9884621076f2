*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Testing Camapaign Approval

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../common_utilities/Login.robot
Resource                                            ../../../../common_utilities/Logout.robot
Resource                                            ../../../Keywords/atm_marketing/Dashboard.robot
Resource                                            ../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/atm_marketing/NegativeScenarios.robot
Resource                                            ../../../keywords/common/Navigation.robot
Resource                                            ../../../keywords/atm_marketing/Approvals.robot

*** Keyword ***
Validating the Approval function on Campaign Approvals
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    When The user navigates to the Campaign Approvals page   

    And The clicks on the preview button to preview an un-approved campaign

    And The user approves the campaign

    And The user navigates back to the Campaign Approvals page

    Then The user verifies that the campaign has been approved

    
| *Test Cases*                                                                                             |      *DOCUMENTATION*     |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   | 
| RAC29a_TC_117_FFT_Approval_Approve_Campaign   |  Validating the Approval function on Campaign Approvals  |  Approve Campaign   |    BUSINESS_APPROVER       |     APC_UAT             |