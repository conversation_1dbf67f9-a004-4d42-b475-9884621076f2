*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Get environment variables passed as argument from commandline

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Library                                             ../../../common_utilities/PostExecutionUpdateV2.py



*** Variables ***
${IS_HEADLESS_BROWSER}
${BASE_URL}
${BROWSER}
${UPLOAD_TEST_STEPS}
${ROBOT_FILE_PATH}
${SUITE_DIRECTORY}
${MS_USERNAME}
${MS_PASSWORD}

*** Keywords ***
Set up environment variables

     Run Keyword If    '${UPLOAD_TEST_STEPS}' != ''    Set Environment Variable    UPLOAD_TEST_STEPS    ${UPLOAD_TEST_STEPS}
     ...  ELSE  Log    Environment variable ${UPLOAD_TEST_STEPS} does not exist.

     Run Keyword If    '${ROBOT_FILE_PATH}' != ''    Set Environment Variable    ROBOT_FILE_PATH    ${ROBOT_FILE_PATH}
     ...  ELSE  Log    Environment variable ${ROBOT_FILE_PATH} does not exist.

     Run Keyword If    '${IS_HEADLESS_BROWSER}' != ''    Set Environment Variable    IS_HEADLESS_BROWSER    ${IS_HEADLESS_BROWSER}
     ...  ELSE  Log    Environment variable ${IS_HEADLESS_BROWSER} does not exist.

     Run Keyword If    '${BASE_URL}' != ''    Set Environment Variable    BASE_URL    ${BASE_URL}
     ...  ELSE  Log    Environment variable ${BASE_URL} does not exist.

    Run Keyword If    '${BROWSER}' != ''    Set Environment Variable    BROWSER    ${BROWSER}
    ...  ELSE  Log    Environment variable ${BROWSER} does not exist.

    Run Keyword If    '${SUITE_DIRECTORY}' != ''    Set Environment Variable    SUITE_DIRECTORY    ${SUITE_DIRECTORY}
    ...  ELSE  Log    Environment variable called 'SUITE_DIRECTORY', does not exist.

    Run Keyword If    '${MS_USERNAME}' != ''    Set Environment Variable    MS_USERNAME    ${SUITE_DIRECTORY}
    ...  ELSE  Log    Environment variable called 'MS_USERNAME', does not exist.

    Run Keyword If    '${MS_PASSWORD}' != ''    Set Environment Variable    MS_PASSWORD    ${SUITE_DIRECTORY}
    ...  ELSE  Log    Environment variable called 'MS_PASSWORD', does not exist.






Clear environment variables
    clear testrail credentials
  