*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Setup                                          The User gets a draft bin number for deletion process    
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-1205




*** Keywords ***
Delete a Bin Number and confirm user is redirected to the view page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The Bin Number is active in the Database                         ${BIN_NAME}
    When The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    And The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates the Bin details of the bin to be deleted        ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    And The user must be able to delete the bin
    Then The Bin must be hard-deleted    ${BIN_NAME}
    Then The user confirms redirection to the View Menu page    

| *** Test Cases ***                                                                                                                                                        |        *DOCUMENTATION*                                                      |         *BASE_URL*             |         *BIN_NAME*          |         *BIN_TYPE_NAME*          |         *BIN_ACTION_DATE*        |
| 	Admin_Verify that the user is redirected to the view screen after confirming the bin deletion   | Delete a Bin Number and confirm user is redirected to the view page   |       User verfies the redirection to the View page after delete process    |           ${EMPTY}             | ${global_draft_bin_number}  |         OnUs                     |         3/21/2025                |
