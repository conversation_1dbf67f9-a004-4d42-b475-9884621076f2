*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Documentation  Test the Calendar view page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             XML
Library                                             Collections
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/DBUtility.robot


*** Variables ***
${CALENDAR_VIEW_LINK}                               xpath=//span[contains(text(),'Calendar View')]/parent::span/parent::*
${CAMPAIGN_VALENDAR_TABLE}                          css=body > app-root > app-sidenav > mat-sidenav-container > mat-sidenav-content > app-marketing-logs-view > full-calendar > div.fc-header-toolbar.fc-toolbar.fc-toolbar-ltr

${SEARCH_FIELD_INPUT}                               id~searchField
${DETAILS_BTN}                                      id=btnDetails
${UPDATE2_BTN}                                      id=btnUpdate
${CLOSE_POPUP_BTN}                                  xpath=//button[@class='close pull-right']

${REFRESH_BTN}                                      id=refresh
${ATM_MARKETING_DASHBOARD}                          xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/mat-list-item[1]/span
${INSTALLED_SCHEDULE_VERSION}                       xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[5]/mat-card/mat-card-header/div/mat-card-title
${CAMPAIGN_ON_THE_DASHBOARD}                        xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[1]/mat-card/mat-card-content/p/mat-card-subtitle
${ATMS_LATEST_SCHEDULE}                             xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[2]/mat-card/mat-card-content/p/mat-card-subtitle
${FAILED_UPLOAD_SCHEDULES}                          xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[3]/mat-card/mat-card-content/p/mat-card-subtitle
${DEVICES_ATM}                                      xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[4]/mat-card/mat-card-content/p/mat-card-subtitle
${CURRENT_VERSION}                                  xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[5]/mat-card/mat-card-content/p/mat-card-subtitle
${ATMS_WITH_INSTALLED_VERSIONS_BY_REGION}           aria-label=//*[@aria-label="ATMs With Installed Versions By Region"]

${CAPTURE_CAMPAIGN}                                 xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[3]/span

${CAMPAIGN_PREVIEW}                                 xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[4]/span
${CAMPAIGN_NUMBERS}                                 xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[1]/mat-card/mat-card-header/div/mat-card-title
${ATMS_LATEST_SCHEDULE_NUMBERS}                     xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[2]/mat-card/mat-card-header/div/mat-card-title
${FAILED_UPLOAD_SCHEDULES_NUMBERS}                  xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[3]/mat-card/mat-card-header/div/mat-card-title
${DEVICES_ATM_NUMBERS}                              xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[4]/mat-card/mat-card-header/div/mat-card-title
*** Keywords ***

The user navigates to the dashboard
    Log to console    -----------------------------The user views the dashboard
    Sleep    10s

    Page Should Contain Element                     ${ATM_MARKETING_DASHBOARD} 
    Page Should Contain Element                     ${CALENDAR_VIEW_LINK}


    
    Click Element    ${ATM_MARKETING_DASHBOARD}

    Sleep    10s

    Page Should Contain     Installed Schedule Version

    ${number_0f_devices}=    Get Text    ${DEVICES_ATM_NUMBERS}
    Log To Console    ${number_0f_devices}
    ${number_of_ATMS_with_latest_schedule}=    Get Text    ${ATMS_LATEST_SCHEDULE_NUMBERS}
    Log To Console    ${number_of_ATMS_with_latest_schedule}
    ${number_of_ATMS_that_failed_to_download_schedule}=    Get Text    ${FAILED_UPLOAD_SCHEDULES_NUMBERS}
    Log To Console    ${number_of_ATMS_that_failed_to_download_schedule}

    Capture page screenshot  Dashboard.png

Reads the dashboard details for current schedule version and active campaigns displayed

    #Get the data that is currently displayed on the page for current version
    ${installed_schedule_version_number}=   Get Text   ${INSTALLED_SCHEDULE_VERSION}

    #Get the total number of active campaigns
    ${number_of_active_campaigns}=    Get Text    ${CAMPAIGN_NUMBERS}

    #Save the values on environment variables
    Set Environment Variable    FRONT_END_SCHEDULE_VERSION        ${installed_schedule_version_number}
    Set Environment Variable    FRONT_END_NUMBER_OF_CAMPAIGNS     ${number_of_active_campaigns}

Reads the database details for current schedule version and active campaigns displayed

    ${db_type}=   Set Variable   'MYSQL'
    #Get the current schedule version from the database
    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion

   #Get the number of active campaigns belonging to the current schedule from the database
    ${active_campaigns}   Set Variable     SELECT count(*) as total_campaigns FROM ATM_Marketing.Campaign where version = '${schedule_version}' and isActive = true;
    ${data_base_active_campaigns}=      Execute SQL Query  ${db_type}  ${active_campaigns}    True
    ${total_number_of_campaigns}=    Get From Dictionary    ${data_base_active_campaigns}    total_campaigns

    #Save the values on environment variables
    Set Environment Variable    DATABASE_SCHEDULE_VERSION        ${schedule_version}
    Set Environment Variable    DATABASE_NUMBER_OF_CAMPAIGNS     ${total_number_of_campaigns}


The Database details must be the same as Front End details for schedule version and total campaigns

    ${front_end_schedule_version}=   Get Environment Variable    FRONT_END_SCHEDULE_VERSION
    ${front_end_total_campaigns}=    Get Environment Variable    FRONT_END_NUMBER_OF_CAMPAIGNS

    ${database_schedule_version}=   Get Environment Variable    DATABASE_SCHEDULE_VERSION
    ${database_total_campaigns}=    Get Environment Variable    DATABASE_NUMBER_OF_CAMPAIGNS

    #Verify that the schedule version from the database is the same as the displayed schedule version
    Should Be Equal As Strings    ${front_end_schedule_version}    ${database_schedule_version}

    #Verify that the number of campaigns from the database is the same as the displayed number of campaigns
    Should Be Equal As Strings    ${front_end_total_campaigns}     ${database_total_campaigns}

