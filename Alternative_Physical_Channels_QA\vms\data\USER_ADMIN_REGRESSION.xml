<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240912 10:21:13.104">
   <suite name="VMS Portal" id="s1" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_EMAIL_MANAGEMENT\RAC29a_TC_331_Validate_Rows_per_Page_filter_Email_Management.robot">
      <test name="Filter the number of Vendor Emails displayed to show 1 email per page." id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:13.854" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240912 10:21:30.669" status="PASS" starttime="20240912 10:21:13.854"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - Email Management">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:30.669" level="INFO">When The user navigates to Admin - Email Management</msg>
            <status endtime="20240912 10:21:31.458" status="PASS" starttime="20240912 10:21:30.669"/>
         </kw>
         <kw library="Selenium" name="And The user filters the number of Vendor Email to be displayed using rows per page filter">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:31.458" level="INFO">And The user filters the number of Vendor Email to be displayed using rows per page filter</msg>
            <status endtime="20240912 10:21:31.656" status="PASS" starttime="20240912 10:21:31.458"/>
         </kw>
         <kw library="Selenium" name="Then The number of rows returned on the page must be the same as the number that was used for filtering">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:31.656" level="INFO">Then The number of rows returned on the page must be the same as the number that was used for filtering</msg>
            <status endtime="20240912 10:21:32.074" status="PASS" starttime="20240912 10:21:31.656"/>
         </kw>
         <tags>
            <tag>Filter the number of Vendor Emails displayed to show 1 email per page.</tag>
         </tags>
         <status endtime="20240912 10:21:37.958" critical="yes" status="PASS" starttime="20240912 10:21:13.854"/>
      </test>
      <test name="Filter the number of Vendor Emails displayed to show 5 emails per page." id="s1-t2">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:37.958" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240912 10:21:53.228" status="PASS" starttime="20240912 10:21:37.958"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - Email Management">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:53.228" level="INFO">When The user navigates to Admin - Email Management</msg>
            <status endtime="20240912 10:21:53.943" status="PASS" starttime="20240912 10:21:53.228"/>
         </kw>
         <kw library="Selenium" name="And The user filters the number of Vendor Email to be displayed using rows per page filter">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:53.943" level="INFO">And The user filters the number of Vendor Email to be displayed using rows per page filter</msg>
            <status endtime="20240912 10:21:54.154" status="PASS" starttime="20240912 10:21:53.943"/>
         </kw>
         <kw library="Selenium" name="Then The number of rows returned on the page must be the same as the number that was used for filtering">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:21:54.154" level="INFO">Then The number of rows returned on the page must be the same as the number that was used for filtering</msg>
            <status endtime="20240912 10:21:54.659" status="PASS" starttime="20240912 10:21:54.154"/>
         </kw>
         <tags>
            <tag>Filter the number of Vendor Emails displayed to show 5 emails per page.</tag>
         </tags>
         <status endtime="20240912 10:22:00.308" critical="yes" status="PASS" starttime="20240912 10:21:37.958"/>
      </test>
      <test name="Filter the number of Vendor Emails displayed to show 10 emails per page." id="s1-t3">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:00.308" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240912 10:22:21.927" status="PASS" starttime="20240912 10:22:00.308"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - Email Management">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:21.927" level="INFO">When The user navigates to Admin - Email Management</msg>
            <status endtime="20240912 10:22:22.882" status="PASS" starttime="20240912 10:22:21.927"/>
         </kw>
         <kw library="Selenium" name="And The user filters the number of Vendor Email to be displayed using rows per page filter">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:22.882" level="INFO">And The user filters the number of Vendor Email to be displayed using rows per page filter</msg>
            <status endtime="20240912 10:22:23.107" status="PASS" starttime="20240912 10:22:22.882"/>
         </kw>
         <kw library="Selenium" name="Then The number of rows returned on the page must be the same as the number that was used for filtering">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:23.107" level="INFO">Then The number of rows returned on the page must be the same as the number that was used for filtering</msg>
            <status endtime="20240912 10:22:23.670" status="PASS" starttime="20240912 10:22:23.107"/>
         </kw>
         <tags>
            <tag>Filter the number of Vendor Emails displayed to show 10 emails per page.</tag>
         </tags>
         <status endtime="20240912 10:22:29.292" critical="yes" status="PASS" starttime="20240912 10:22:00.308"/>
      </test>
      <test name="Filter the number of Vendor Emails displayed to show 15 emails per page." id="s1-t4">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:29.296" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240912 10:22:46.810" status="PASS" starttime="20240912 10:22:29.296"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - Email Management">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:46.810" level="INFO">When The user navigates to Admin - Email Management</msg>
            <status endtime="20240912 10:22:47.899" status="PASS" starttime="20240912 10:22:46.810"/>
         </kw>
         <kw library="Selenium" name="And The user filters the number of Vendor Email to be displayed using rows per page filter">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:47.900" level="INFO">And The user filters the number of Vendor Email to be displayed using rows per page filter</msg>
            <status endtime="20240912 10:22:48.099" status="PASS" starttime="20240912 10:22:47.900"/>
         </kw>
         <kw library="Selenium" name="Then The number of rows returned on the page must be the same as the number that was used for filtering">
            <doc>Filter nummber of rows to be displayed per page</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240912 10:22:48.100" level="INFO">Then The number of rows returned on the page must be the same as the number that was used for filtering</msg>
            <status endtime="20240912 10:22:48.575" status="PASS" starttime="20240912 10:22:48.100"/>
         </kw>
         <tags>
            <tag>Filter the number of Vendor Emails displayed to show 15 emails per page.</tag>
         </tags>
         <status endtime="20240912 10:22:54.831" critical="yes" status="PASS" starttime="20240912 10:22:29.294"/>
      </test>
      <status endtime="20240912 10:22:54.834" status="PASS" starttime="20240912 10:21:13.104"/>
   </suite>
   <statistics>
      <total>
         <stat pass="4" fail="0">Critical Tests</stat>
         <stat pass="4" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">Filter the number of Vendor Emails displayed to show 1 email per page.</stat>
         <stat pass="1" fail="0">Filter the number of Vendor Emails displayed to show 5 emails per page.</stat>
         <stat pass="1" fail="0">Filter the number of Vendor Emails displayed to show 10 emails per page.</stat>
         <stat pass="1" fail="0">Filter the number of Vendor Emails displayed to show 15 emails per page.</stat>
      </tag>
      <suite>
         <stat name="VMS Portal" pass="4" fail="0" id="s1">VMS Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
