*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/controllers/Confirm_BinTable_Download_Keywords.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Execute the 'Confirm Bin Tables Download' API
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}   ${EXPECTED_STATUS_CODE}  ${EXPECTED_ERROR_MSG}   ${DEVICE_NAME}  ${IS_SUCCESSFUL_HANDSHAKE}   ${IS_SUCCESSFUL_DOWNLOAD}   ${DOWNLOAD_ERROR}   &{HANDSHAKE_ERROR}
    Set Test Documentation  ${DOCUMENTATION}

    ${is_successful_download_scenario}=       Get From Dictionary    ${HANDSHAKE_ERROR}    error     default=Key not found

    IF    '${is_successful_download_scenario}' != 'Key not found'
        ${HANDSHAKE_ERROR}=    Set Variable   ${None}
    END

    Given The User Populates the Confirm Bin Table Download JSON payload         ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}
    When The User sends a POST Request to confirm the download of bin table      ${BASE_URL}
    And The service returns the expected status code                             ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed                            ${EXPECTED_ERROR_MSG}


| *** Test Cases ***                                                                                                                                               |        *DOCUMENTATION*    		        |         *BASE_URL*                  |  *EXPECTED_STATUS_CODE*  |                                              *EXPECTED_ERROR_MSG*                                                                                           |     *DEVICE_NAME*        | *IS_SUCCESSFUL_HANDSHAKE*   |   *IS_SUCCESSFUL_DOWNLOAD* |   *DOWNLOAD_ERROR*                           |   *HANDSHAKE_ERROR*                                                                                                     |
| Send Confirm Bin Table download request using the device that has not yet downloaded Bin Tables.              | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          404             |         There are no bins to download.                                                                                                                   |         S12345         |          ${False}           |        ${False}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
| Send Confirm Bin Table download request using and empty string for the device name field.                     | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          400             |         'Device Name' must not be empty.                                                                                                                 |         ${EMPTY}       |          ${False}           |        ${False}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
| Send Confirm Bin Table download request using and empty string for the 'isSuccessfulHandshake' field.         | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          400             |         The JSON value could not be converted to BinTables.Controllers.DeviceVersions.ConfirmBinTablesDownloadRequest. Path: $.isSuccessfulHandshake     |         S08397         |          ${EMPTY}           |        ${False}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
| Send Confirm Bin Table download request using a non-boolean value for the 'isSuccessfulHandshake' field.      | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          400             |         The JSON value could not be converted to BinTables.Controllers.DeviceVersions.ConfirmBinTablesDownloadRequest. Path: $.isSuccessfulHandshake     |         S08397         |          False              |        ${False}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
| Send Confirm Bin Table download request using and empty string for the 'handshakeError -status' field.        | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          400             |         The JSON value could not be converted to BinTables.Domain.Abstractions.Error. Path: $.handshakeError.status                                      |         S08397         |          ${False}           |        ${False}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=${EMPTY}  | detail=Thabo unit test                          |
| Send Confirm Bin Table download request using and empty string for the 'isSuccessfulDownload' field.          | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          400             |         The JSON value could not be converted to BinTables.Controllers.DeviceVersions.ConfirmBinTablesDownloadRequest. Path: $.isSuccessfulDownload      |         S08397         |          ${False}           |        ${EMPTY}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
| Send Confirm Bin Table download request using a non-boolean value for the 'isSuccessfulDownload' field.       | Execute the 'Confirm Bin Tables Download' API    | Verify the ConfirmDownload controller.    |                                     |          400             |         The JSON value could not be converted to BinTables.Controllers.DeviceVersions.ConfirmBinTablesDownloadRequest. Path: $.isSuccessfulDownload      |         S08397         |          ${False}           |        False               |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
