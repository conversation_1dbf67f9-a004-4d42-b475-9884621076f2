*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinsByBintypeName_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for all Bins to ve reviewed using a Bin Number on the GetBinsByBintypeName Controller
    [Arguments]        ${DOCUMENTATION}    ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User gets all the Bin numbers(based on Bin Type Name) created in the Database
    When The User sends a Get Requests for GetBinsByBintypeName Controller using all available bin types
    And The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then All the Bin numbers' details returned by the GetBinsByBintypeName Controller must be the same as the details of the bins queried from the Database

| *** Test Cases ***                                                                                                                                                                                          |        *DOCUMENTATION*    		                 |    *EXPECTED_STATUS_CODE*   |
| Get all Bins from the database, and verify the Bins' details against GetBinsByBintypeName controller   | Search for all Bins to ve reviewed using a Bin Number on the GetBinsByBintypeName Controller       | Search Bin by Number on the GetBinsToReview API  |           200               |
