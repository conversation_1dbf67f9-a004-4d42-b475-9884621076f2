*** Settings ***
#Author Name               : THab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS User Creation - Negative Testing
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${USER_NAME}       ${NAME}      ${USER_ROLE}        ${EXPECTED_ERROR}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And The user Adds a new VMS User while leaving some fields blank    ${USER_NAME}       ${NAME}      ${USER_ROLE}
    Then The exepcted error message must be displayed   ${EXPECTED_ERROR}




| *Test Case*                                                                                                                                |      *DOCUMENTATION*                                                     | *TEST_ENVIRONMENT*   |  *USER_NAME*    |  *NAME*                          |  *USER_ROLE*      |    *EXPECTED_ERROR*              |
| Add New User- Blank Fields- User Management 'Browse' Role Test Case: Do not populate the 'Username' field         | VMS User Creation - Negative Testing     | Create a VMS user with 'Browse' Role without populating the Username.    |    VMS_UAT           |                 |  Automation User Browser         |    Browse         |   Please fill out this field.    |
# | Add New User- Blank Fields- User Management 'Browse' Role Test Case: Do not populate the 'Name' field.             | VMS User Creation - Negative Testing     | Create a VMS user with 'Browse' Role without populating the Name.        |    VMS_UAT           |   AB0541to      |                                  |    Browse         |   Please fill out this field.    |
# | Create a VMS user with a 'Browse' Role Test Case, do not select the 'Role'.                     | VMS User Creation - Negative Testing     | Create a VMS user with 'Browse' Role without populating the Username.    |    VMS_UAT           |   AB0541to      |  Automation User Browser         |                   |   No validation message found    |
