import json
import re


from robot.api.deco import keyword


class Approve:
    def __init__(self, **kwargs):
        """
            The constructor initializes the class with a dictionary of bins and processes them to generate
            the necessary data for the JSON request. It extracts the bin id and outcome for each bin,
            regardless of how many bins are passed or their indices.

            Arguments:

            kwargs: A dictionary containing the bin data, where keys represent the bin (e.g., bin1, bin2, etc.)
            and values represent the bin number. The dictionary should also include outcomeX for each bin.

            bins = {
                'binId1': 'uuid1',
                'outcome1': 'outcome data',
            }
            generator = Approve(bins)

            """

        # Initialize bins as an empty list
        self.bins = []

        # Loop through all bins keys and create bin data
        for key, value in kwargs.items():

            # Check if the key starts with 'bin' (e.g., 'bin1', 'bin2', ...)
            if key.startswith("binId"):
                # Extract the bin index using regex (to allow for any number format)
                match = re.match(r"binId(\d+)", key)
                if match:
                    bin_id_index = match.group(1)  # Extract the index as string (e.g., '1', '2', '123')

                    # Now, retrieve associated data for this bin
                    bin_outcome = kwargs.get(f"outcome{bin_id_index}")  # Get the corresponding outcome (e.g., 'outcome1', 'outcome2')

                    # Append bin data
                    self.bins.append({
                        "binId": value,  # binId1, binId2, ... contain the bin uuid
                        "outcome": bin_outcome
                    })
                    #if bin_outcome:
                        #print('bin outcome provided.')
                        #self.bins.append({
                         #   "binId": value,  # binId1, binId2, ... contain the bin uuid
                        #    "outcome": bin_outcome
                        #})


    def get_json_request(self):
        # Return the bins in JSON format
        return json.dumps(self.bins, indent=4)

    @keyword
    def create_bin_request(self, **kwargs):
        """
        This method processes the bins and returns the JSON request.
        """
        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def create_bin_request(self, **kwargs):
        """
        This method processes the bins and returns the JSON request.
        """
        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def get_endpoint(self, domain):
        path = "/api/v1/bintables/review/bins/approve"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers





