server-api: 2025-06-18 09:55:00 UTC pid: 2960 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.2.others.api']

server-api: 2025-06-18 09:55:00 UTC pid: 2960 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-18 09:55:00 UTC pid: 2960 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-18 09:55:00 UTC pid: 2960 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 2960

server-api: 2025-06-18 09:55:03 UTC pid: 2960 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-18 09:55:03 UTC pid: 2960 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-18 09:55:03 UTC pid: 2960 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-18 10:02:20 UTC pid: 2960 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'c:\\common\\DBUtility.robot', 'c:\\Alternative\\common\\DBUtility.robot', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common\\DBUtility.robot', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\common\\DBUtility.robot', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\FIS_API\\common\\DBUtility.robot', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

