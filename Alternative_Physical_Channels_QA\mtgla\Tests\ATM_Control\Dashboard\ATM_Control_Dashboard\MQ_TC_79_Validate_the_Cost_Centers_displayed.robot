*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../keywords/Common/Login.robot
Resource                                            ../../../../keywords/Common/HomePage.robot
Resource                                            ../../../../keywords/Common/Navigation.robot
Resource                                            ../../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../../keywords/Common/SetEnvironmentVariales.robot

*** Variables ***

*** Keywords ***
 Validates the cost centres displayed on the ATM Control Dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page 

    And the user navigates to the ATM Control Dashboard

    Then the user verifies the cost centres displayed on the ATM Control Dashboard    


| *Test Cases*                                                                                                          |      *DOCUMENTATION*                                                 | *TEST_ENVIRONMENT*   |
| MQ_TC_79_Validate_the_Cost_Centers_displayed    | Validates the cost centres displayed on the ATM Control Dashboard   |    User verfies cost centres displayed on the ATM Control Dashbaord  |    MTGLA_UAT         | 
