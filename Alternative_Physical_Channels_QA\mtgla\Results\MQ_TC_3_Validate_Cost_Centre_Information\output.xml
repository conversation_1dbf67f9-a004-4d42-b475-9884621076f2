<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2025-01-22T08:10:54.311884" rpa="false" schemaversion="5">
<suite id="s1" name="MQ TC 39 Validate the Close Button when adding a public holiday" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\mtgla\tests\ATM_Control\Calendar_Management\MQ_TC_39_Validate_the_Close_Button_when_adding_a_public_holiday.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.169862" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'No'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-01-22T08:10:56.169862" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.169862" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\mtgla'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-01-22T08:10:56.169862" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.169862" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-01-22T08:10:56.169862" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.170865" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-01-22T08:10:56.169862" elapsed="0.001003"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.170865" level="INFO">Environment variable 'CRED_U' set to value '//CORP%5CSVC-MTGLA_UAT'.</msg>
<arg>CRED_U</arg>
<arg>${base64_string_U}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-01-22T08:10:56.170865" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.170865" level="INFO">Environment variable 'CRED_P' set to value '%24vc%2A_MT6L%40_U%26T'.</msg>
<arg>CRED_P</arg>
<arg>${base64_string_P}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-01-22T08:10:56.170865" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-01-22T08:10:56.168864" elapsed="0.002001"/>
</kw>
<test id="s1-t1" name="MQ_TC_39_Validate_the_Close_Button_when_adding_a_public_holiday" line="45">
<kw name="Validates the Close Button on Calendar Management">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-01-22T08:10:56.171762" level="INFO">Set test documentation to:
User can cancel add public holiday flow</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-01-22T08:10:56.171762" elapsed="0.000000"/>
</kw>
<kw name="Given the user logs into the MTGLA Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-01-22T08:10:56.262442" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-01-22T08:10:56.171762" elapsed="0.090680"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-01-22T08:10:56.262442" elapsed="0.001073"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----The user logs into the MTGLA Web Application----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-01-22T08:10:56.263515" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-01-22T08:10:56.264032" level="INFO">${base_u} = https:////CORP%5CSVC-MTGLA_UAT:%24vc%2A_MT6L%40_U%26T@zaurnbmweb0126</msg>
<var>${base_u}</var>
<arg>https://${base64_string_U}:${base64_string_P}@zaurnbmweb0126</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-01-22T08:10:56.264032" elapsed="0.000000"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-01-22T08:10:56.265038" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-01-22T08:10:56.265038" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:56.265038" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-01-22T08:10:56.265038" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-01-22T08:10:56.265038" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-01-22T08:10:56.265038" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-01-22T08:10:56.265038" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-01-22T08:10:56.265038" elapsed="0.000999"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-01-22T08:10:56.300742" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2025-01-22T08:10:57.127061" level="INFO">${rc_code} = 0</msg>
<msg time="2025-01-22T08:10:57.127061" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 32772 has been terminated.
SUCCESS: The process "chrome.exe" with PID 17124 has been terminated.
SUCCESS: The process "chrome.exe" with PID 33424 has been te...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-01-22T08:10:56.266037" elapsed="0.861024"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-01-22T08:10:57.127061" elapsed="0.001206"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-01-22T08:10:56.265038" elapsed="0.863229"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:57.128267" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-01-22T08:10:57.128267" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-01-22T08:10:57.128267" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-01-22T08:10:57.128267" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-01-22T08:10:57.128267" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-01-22T08:10:57.128267" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.129569" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-01-22T08:10:57.129569" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>${base_u}</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<kw name="Execute Javascript" owner="SeleniumLibrary">
<arg>location.reload(true)</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-01-22T08:10:57.129569" elapsed="0.001008"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>${base_u}</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<kw name="Execute Javascript" owner="SeleniumLibrary">
<arg>location.reload(true)</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-01-22T08:10:57.130577" elapsed="0.001001"/>
</branch>
<status status="NOT RUN" start="2025-01-22T08:10:57.129569" elapsed="0.002009"/>
</if>
<status status="NOT RUN" start="2025-01-22T08:10:57.128267" elapsed="0.003311"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-01-22T08:10:57.131578" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-01-22T08:10:57.131578" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-01-22T08:10:57.131578" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000022A5142FBC0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-01-22T08:10:57.131578" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.132577" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-01-22T08:10:57.132577" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-01-22T08:10:57.131578" elapsed="0.000999"/>
</branch>
<status status="PASS" start="2025-01-22T08:10:57.131578" elapsed="0.000999"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-01-22T08:10:57.132577" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-01-22T08:10:57.132577" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-01-22T08:10:57.132577" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-01-22T08:10:57.132577" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-01-22T08:10:57.133893" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-01-22T08:10:57.133893" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-01-22T08:10:57.133893" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-01-22T08:10:57.133893" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-01-22T08:10:57.133893" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-01-22T08:10:57.134900" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-01-22T08:10:57.133893" elapsed="0.001007"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2025-01-22T08:10:57.134900" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>