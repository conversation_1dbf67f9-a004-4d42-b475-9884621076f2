<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="20240903 12:48:53.904" rpa="false" schemaversion="4">
<suite id="s1" name="Login" source="C:\development\MTGLA-QA\tests\Login.robot">
<test id="s1-t1" name="Login MTGLA System" line="30">
<kw name="Validates MTGLA Login">
<arg>Login</arg>
<arg>MTGLA_UAT</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240903 12:48:54.376" level="INFO">Set test documentation to:
Login</msg>
<status status="PASS" starttime="20240903 12:48:54.376" endtime="20240903 12:48:54.376"/>
</kw>
<kw name="Given The user logs into the MTGLA Web Application" library="Login">
<kw name="Set Log Level" library="BuiltIn">
<arg>NONE</arg>
<doc>Sets the log threshold to the specified level and returns the old level.</doc>
<status status="PASS" starttime="20240903 12:48:54.377" endtime="20240903 12:48:54.377"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dencryptedData_P}</var>
<arg>base64.b64decode('${base64_string_P}').decode("utf-8")</arg>
<arg>modules=base64</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" starttime="20240903 12:48:54.377" endtime="20240903 12:48:54.377"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dencryptedData_U}</var>
<arg>base64.b64decode('${base64_string_U}').decode("utf-8")</arg>
<arg>modules=base64</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" starttime="20240903 12:48:54.377" endtime="20240903 12:48:54.377"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>https://${dencryptedData_U}:${dencryptedData_P}@zaurnbmweb0126</arg>
<arg>chrome</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" starttime="20240903 12:48:54.377" endtime="20240903 12:49:36.626"/>
</kw>
<kw name="Set Log Level" library="BuiltIn">
<arg>INFO</arg>
<doc>Sets the log threshold to the specified level and returns the old level.</doc>
<msg timestamp="20240903 12:49:36.627" level="INFO">Log level changed from NONE to INFO.</msg>
<status status="PASS" starttime="20240903 12:49:36.627" endtime="20240903 12:49:36.627"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>15s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240903 12:49:51.627" level="INFO">Slept 15 seconds</msg>
<status status="PASS" starttime="20240903 12:49:36.627" endtime="20240903 12:49:51.627"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20240903 12:49:51.627" endtime="20240903 12:49:51.670"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Mismatch GL Automation</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240903 12:49:51.683" level="INFO">Current page contains text 'Mismatch GL Automation'.</msg>
<status status="PASS" starttime="20240903 12:49:51.670" endtime="20240903 12:49:51.683"/>
</kw>
<status status="PASS" starttime="20240903 12:48:54.376" endtime="20240903 12:49:51.683"/>
</kw>
<status status="PASS" starttime="20240903 12:48:54.376" endtime="20240903 12:49:51.683"/>
</kw>
<doc>Login</doc>
<tag>Login</tag>
<tag>MTGLA HEALTHCHECK</tag>
<status status="PASS" starttime="20240903 12:48:54.375" endtime="20240903 12:49:51.684"/>
</test>
<doc>MTGLA Login</doc>
<status status="PASS" starttime="20240903 12:48:54.070" endtime="20240903 12:49:51.685"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">Login</stat>
<stat pass="1" fail="0" skip="0">MTGLA HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Login">Login</stat>
</suite>
</statistics>
<errors>
<msg timestamp="20240903 12:48:54.374" level="ERROR">Error in file 'C:\development\MTGLA-QA\tests\Login.robot' on line 15: Invalid resource file extension '.py'. Supported extensions are '.json', '.resource', '.rest', '.robot', '.rsrc', '.rst', '.tsv' and '.txt'.</msg>
<msg timestamp="20240903 12:48:55.879" level="WARN">The chromedriver version (127.0.6533.72) detected in PATH at C:\Bin\chromedriver.exe might not be compatible with the detected chrome version (128.0.6613.114); currently, chromedriver 128.0.6613.119 is recommended for chrome 128.*, so it is advised to delete the driver in PATH and retry</msg>
</errors>
</robot>
