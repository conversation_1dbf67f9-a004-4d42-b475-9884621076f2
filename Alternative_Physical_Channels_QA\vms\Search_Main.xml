<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20250505 14:11:10.333">
   <suite name="VMS Portal" id="s1" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ADMIN_USER_MANAGEMENT\RAC29a_TC_342_Validate_Add_New_User_User_Management.robot">
      <test name="Create a VMS user with a 'Browse' Role Test Case" id="s1-t1">
         <tags>
            <tag>Create a VMS user with a 'Browse' Role Test Case</tag>
         </tags>
         <status endtime="20250505 14:12:21.241" critical="yes" status="FAIL" starttime="20250505 14:11:13.636"/>
      </test>
      <test name="Create a VMS user with a 'User' Role Test Case" id="s1-t2">
         <tags>
            <tag>Create a VMS user with a 'User' Role Test Case</tag>
         </tags>
         <status endtime="20250505 14:13:32.153" critical="yes" status="FAIL" starttime="20250505 14:12:21.244"/>
      </test>
      <test name="Create a VMS user with a 'Supervisor' Role Test Case" id="s1-t3">
         <tags>
            <tag>Create a VMS user with a 'Supervisor' Role Test Case</tag>
         </tags>
         <status endtime="20250505 14:14:39.784" critical="yes" status="FAIL" starttime="20250505 14:13:32.157"/>
      </test>
      <test name="Create a VMS user with a 'Administrator' Role Test Case" id="s1-t4">
         <tags>
            <tag>Create a VMS user with a 'Administrator' Role Test Case</tag>
         </tags>
         <status endtime="20250505 14:16:11.865" critical="yes" status="FAIL" starttime="20250505 14:14:39.789"/>
      </test>
      <status endtime="20250505 14:16:11.871" status="FAIL" starttime="20250505 14:11:10.333"/>
   </suite>
   <statistics>
      <total>
         <stat pass="0" fail="4">Critical Tests</stat>
         <stat pass="0" fail="4">All Tests</stat>
      </total>
      <tag>
         <stat pass="0" fail="1">Create a VMS user with a 'Browse' Role Test Case</stat>
         <stat pass="0" fail="1">Create a VMS user with a 'User' Role Test Case</stat>
         <stat pass="0" fail="1">Create a VMS user with a 'Supervisor' Role Test Case</stat>
         <stat pass="0" fail="1">Create a VMS user with a 'Administrator' Role Test Case</stat>
      </tag>
      <suite>
         <stat name="VMS Portal" pass="0" fail="4" id="s1">VMS Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
