import os
from pathlib import Path
import requests
import requests
import json
from datetime import datetime
from requests_toolbelt.multipart.encoder import <PERSON>part<PERSON>ncoder


def get_project_root(parent_folder_name):
    current_dir = Path(__file__).resolve()  # Get the absolute path of the current script

    # Traverse upward through parent directories until we find the folder name
    while current_dir != current_dir.parent:
        if current_dir.name == parent_folder_name:
            return current_dir  # Return the project root once the parent folder is found
        current_dir = current_dir.parent

    return None  # Return None if the parent folder is not found




def attach_test_logs_using_run_id(test_case_run_id, file_path, service_url, api_key):
    try:
        current_date = datetime.now().strftime("%Y-%m-%d-%H%M%S")

        with open(file_path, 'rb') as f:
            out_file_array = f.read()

        if out_file_array:
            m = MultipartEncoder(
                fields={
                    'desc': f'Attached on {current_date}',
                    'type': 'TCR',
                    'entityId': str(test_case_run_id),
                    'file[]': (file_path)
                }
            )

            headers = {
                'apikey': api_key,
                'project': '987',
                'Content-Type': m.content_type
            }

            response = requests.post(f'{service_url}/rest/attachments/testLog', headers=headers, data=m)

            print('response: ', response)
            if response.ok:
                print(f"File uploaded successfully. {response.status_code} - {response.text}")
                print(f"File uploaded successfully. {response.status_code} - {response.json()}")


                response_data = response.json()
                data = response_data.get('data')
                if data:
                    return int(data[0].get('id'))
                else:
                    print("No data found in response")
                    return 0
            else:
                print("Request failed with status code:", response.status_code)
                return 0
        else:
            print(f"{file_path} file does not exist")
            return 0

    except Exception as ex:
        print(f"Error in attaching file - {file_path}")
        print(ex)
        return 0


def upload_robot_log(api_key, entity_id, file_path, file_type="TCR"):
    url = "https://testmanagementeu.qmetry.com/rest/attachments/testLog"  # Replace with your Qmetry instance URL
    headers = {
        "apiKey": f"{api_key}",  # Using the API key for authentication
        "project": "987"
    }

    # Prepare the files to upload (the 'file[]' parameter expects a file)
    files = {
        'file[]': open(file_path, 'rb')  # Open the log file in binary read mode
    }

    # Prepare additional form data (the other required parameters)
    data = {
        'entityId': entity_id,
        'type': file_type  # You can adjust 'type' as needed, default is "text/html"
    }

    # Send the POST request to upload the file
    response = requests.post(url, headers=headers, files=files, data=data)

    # Close the file after uploading
    #files['file[]'].close()

    # Check the response from the server
    if response.status_code == 200:
        print("File uploaded successfully.")
        return response.json()  # You can process the response as needed
    else:
        print(f"Failed to upload file: {response.status_code} - {response.text}")
        return None


def upload_log(api_key, entity_id, file_path, file_type='TCR'):
    url = 'https://testmanagementeu.qmetry.com/rest/attachments/testLog'
    headers = {
        'apiKey': api_key,
        "project": "987"
    }
    files = {
        'entityId': (None, entity_id),
        'type': (None, file_type),
        'file[]': open(file_path, 'rb')
    }

    response = requests.post(url, headers=headers, files=files)

    if response.status_code == 200:
        print('Upload successful!')
    else:
        print(f'Upload failed with status code: {response.status_code}')
        print(f'Response: {response.text}')
def get_test_case_run_id(test_suite_run_id):
    obj_test_case_run_id = {
        "limit": 200,
        "page": 1,
        "start": 0,
        "tsrID": test_suite_run_id
    }

    service_url = "https://testmanagementeu.qmetry.com"
    api_key = "aG1dKCwagyGPWr2Dcl86ISWMM9w8ByVQvYJYIrll"
    headers = {
        'apiKey': api_key,
        "project": "987",
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(f"{service_url}/rest/execution/list/tcr", headers=headers,
                                 json=json.dumps(obj_test_case_run_id))
        return response.text
    except Exception as e:
        print(e)
        return None


def get_request_header():
    # Define your request headers here if needed
    return {
        'apiKey': api_key,
        "project": "987",
        "Content-Type": "application/json"
    }
# Example usage:

print(get_test_case_run_id(78236))

# Example usage

project_root = get_project_root("alternative_physical_channels")  # Pass the name of the parent folder you want to find
if project_root:
    print(f"Project root path: {project_root}")
else:
    print("Parent folder not found.")
# Replace with your QMetry instance URL and API Key

qmetry_url = "https://testmanagementeu.qmetry.com"
api_key = "aG1dKCwagyGPWr2Dcl86ISWMM9w8ByVQvYJYIrll"

# Path to your log file and the test suite ID you want to associate it with
log_file_path = os.path.join(project_root, 'future_fit_architecture_portal_BinTables/Results/BinsControllers/log.html')
entity_id = 828431


test_case_run_id = 828431
file_path = log_file_path
service_url = qmetry_url

# api_key = "your_api_key_here"
# entity_id = "your_entity_id_here"
# file_path = log_file_path
#upload_robot_log(api_key, entity_id, file_path)
#upload_log(api_key, entity_id, file_path)

#attachment_id = attach_test_logs_using_run_id(test_case_run_id, file_path, service_url, api_key)
#print("Attachment ID:", attachment_id)
