*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Contains generic methods

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            ../../keywords/api/Resources/CreateCampaignAPI.py
Library                                            Collections
Library                                            String
Library                                            DateTime
Library                                            OperatingSystem
Library                                             JSONLibrary
*** Keywords ***

Generate Campaign JSON Request
    [Arguments]         ${IMAGES}       ${LAST_UPDATE}       ${TARGET_DATA}       ${CAMPAIGN_NAME}       ${CAMPAIGN_CREATED_BY}       ${CAMPAIGN_START_DATE}       ${CAMPAIGN_END_DATE}       ${MARKETING_CHANNEL_ID}       ${CAMPAIGN_ID}       ${CAMPAIGN_CAMPAIGNID}       ${SCREEN_ID}       ${CAMPAIGN_UPDATED_BY}       ${CAMPAIGN_IS_ACTIVE}       ${CAMPAIGN_IS_APPROVED}       ${VERSION}       ${CAMPAIGN_IS_TARGETED}


    #Verify that the provided varibles are of the correct data type
    ${integer_validation}=       Validate Integer    ${MARKETING_CHANNEL_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for MARKETING_CHANNEL_ID, which is '${MARKETING_CHANNEL_ID}', is not an integer.
    ${integer_validation}=       Validate Integer    ${CAMPAIGN_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for CAMPAIGN_ID, which is '${CAMPAIGN_ID}', is not an integer.
    ${integer_validation}=       Validate Integer    ${SCREEN_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for SCREEN_ID, which is '${SCREEN_ID}', is not an integer.
    ${result}=  Run Keyword And Return Status    Is Boolean    ${CAMPAIGN_IS_ACTIVE}
    Run Keyword If    '${result}' == 'False'    Fail    The provided value for CAMPAIGN_IS_ACTIVE, is not a Boolean.
    ${result}=  Run Keyword And Return Status    Is Boolean    ${CAMPAIGN_IS_APPROVED}
    Run Keyword If    '${result}' == 'False'    Fail    The provided value for CAMPAIGN_IS_APPROVED, is not a Boolean.
    ${result}=  Run Keyword And Return Status    Is Boolean    ${CAMPAIGN_IS_TARGETED}
    Run Keyword If    '${result}' == 'False'    Fail    The provided value for CAMPAIGN_IS_TARGETED, is not a Boolean.

    ${json_request}=    Create Campaign Request    ${CAMPAIGN_NAME}    ${CAMPAIGN_CREATED_BY}    ${CAMPAIGN_START_DATE}    ${CAMPAIGN_END_DATE}    ${MARKETING_CHANNEL_ID}    ${CAMPAIGN_ID}    ${CAMPAIGN_CAMPAIGNID}    ${SCREEN_ID}    ${CAMPAIGN_UPDATED_BY}    ${CAMPAIGN_IS_ACTIVE}    ${CAMPAIGN_IS_APPROVED}    ${VERSION}    ${CAMPAIGN_IS_TARGETED}    ${IMAGES}    ${LAST_UPDATE}    ${TARGET_DATA}

    RETURN    ${json_request}


Create the imageList Object
    [Arguments]         ${IMAGES}    ${IMAGE_ID}     ${MARKETING_IMAGE_BASE64_STRING}     ${MARKETING_IMAGE_LANGUAGE_ID}     ${MARKETING_IMAGE_LANGUAGE_NAME}     ${IMAGE_NAME}     ${IMAGE_DURATION}     ${IMAGE_PRIORITY}
    #{images}  this is a List to which the image objects will be appended
    #The rest of the variables are the objects belonging to the image


    #Verify that the provided varibles are of the correct data type
    ${integer_validation}=       Validate Integer    ${IMAGE_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for IMAGE_ID, which is '${IMAGE_ID}', is not an integer.

    ${image_string_array}=      Split String    ${MARKETING_IMAGE_BASE64_STRING}        ,
    ${actual_length}=    Get Length    ${image_string_array}

    ${result}=  Run Keyword And Return Status    Should Be Equal    '${actual_length}'    '3'
    Run Keyword If    '${result}' == 'False'    Fail    The provided value for MARKETING_IMAGE_BASE64_STRING, is not a valid Base64 format.

    ${image_string}=    Set Variable    ${image_string_array[1]}
    ${result}=    Is Base64 Encoded    ${image_string}

    Run Keyword If    '${result}' == 'False'    Fail    The provided value for MARKETING_IMAGE_BASE64_STRING, is not a valid Base64 format.

    ${integer_validation}=       Validate Integer    ${MARKETING_IMAGE_LANGUAGE_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for MARKETING_IMAGE_LANGUAGE_ID, which is '${MARKETING_IMAGE_LANGUAGE_ID}', is not an integer.

    ${integer_validation}=       Validate Integer    ${IMAGE_DURATION}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for IMAGE_DURATION, which is '${IMAGE_DURATION}', is not an integer.

    ${integer_validation}=       Validate Integer    ${IMAGE_PRIORITY}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for IMAGE_PRIORITY, which is '${IMAGE_PRIORITY}', is not an integer.

    Append To List    ${IMAGES}    {"id": ${IMAGE_ID}, "marketingImage": "${MARKETING_IMAGE_BASE64_STRING}", "language": {"id": ${MARKETING_IMAGE_LANGUAGE_ID}, "language": "${MARKETING_IMAGE_LANGUAGE_NAME}"}, "imageName": "${IMAGE_NAME}", "duration": ${IMAGE_DURATION}, "priority": ${IMAGE_PRIORITY}}

    RETURN      ${IMAGES}

Create the lastUpdate object
    [Arguments]         ${LAST_UPDATE_ID}    ${CAMPAIGN_ID}     ${APPROVAL_ID}

    #Verify that the provided varibles are of the correct data type
    ${integer_validation}=       Validate Integer    ${LAST_UPDATE_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for LAST_UPDATE_ID, which is '${LAST_UPDATE_ID}', is not an integer.

    ${integer_validation}=       Validate Integer    ${CAMPAIGN_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for CAMPAIGN_ID, which is '${CAMPAIGN_ID}', is not an integer.

    ${integer_validation}=       Validate Integer    ${APPROVAL_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for APPROVAL_ID, which is '${APPROVAL_ID}', is not an integer.

    ${LAST_UPDATE_ID}=      Convert To Integer    ${LAST_UPDATE_ID}
    ${CAMPAIGN_ID}=      Convert To Integer    ${CAMPAIGN_ID}
    ${APPROVAL_ID}=      Convert To Integer    ${APPROVAL_ID}

    ${update_date}=    Get Current Date In ISO Format
    ${last_update}=    Create Dictionary    id=${LAST_UPDATE_ID}    campaignId=${CAMPAIGN_ID}    updatedDate=${update_date}    updatedBy=    updateDescription=    approvalId=${APPROVAL_ID}

     RETURN     ${last_update}


Create the targetData object
    [Arguments]         ${IS_TARGET_REGION}     ${TARGET_REGION_OR_ATM}

    ${result}=  Run Keyword And Return Status    Is Boolean    ${IS_TARGET_REGION}
    Run Keyword If    '${result}' == 'False'    Fail    The provided value for IS_TARGET_REGION, is not a Boolean.

    ${target_data}=    Create Dictionary    isTargetRegion=${IS_TARGET_REGION}    targetRegionOrAtm=${TARGET_REGION_OR_ATM}

    RETURN  ${target_data}


Get Current Date In ISO Format
    ${current_time} =    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S.000Z
    RETURN    ${current_time}

Add days to String date
    [Arguments]     ${DATE_STRING}      ${DAYS}

    ${current}    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S.000Z

     # Convert string to datetime
    ${date}=    Convert Date	${DATE_STRING}    result_format=%Y-%m-%dT%H:%M:%S.000Z
    # Add day(s)
    ${new_date} =	Add Time To Date	${date}	${DAYS} days
    # Convert back to string in the same format
    ${date_2}=    Convert Date	${new_date}    result_format=%Y-%m-%dT%H:%M:%S.000Z
    ${new_date_string}=    Convert To String    ${date_2}

    RETURN      ${new_date_string}


Write JSON data To File
    [Arguments]    ${file}    ${json_string}

    Create File    ${file}    ${json_string}