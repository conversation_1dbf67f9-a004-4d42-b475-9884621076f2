*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       SLA Status Per Main Vendor- This Year Validation 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../utility/DatabaseUtility.py
Library                                             ../../utility/Common_Functions.py
Library                                             ../../keywords/common/DBUtility.robot
Library                                             String


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot 
Resource                                            ../../../common_utilities/Logout.robot    
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keyword ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION} 

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}  

    When The user lands on the dashboard page

    And The user reads the dashboard details for SLA Status Per Main Vendor for this year

    And The user reads the database details for SLA Status Per Main Vendor for this year

    Then The Database details must be the same as Front End details for SLA Status Per Main Vendor for this year

| *Test Case*                                                                 |                *DOCUMENTATION*                       |     *TEST_ENVIRONMENT*   |        
| Validate SLA Status per Main Vendor- This Year | Dashboard Validation    | Validates SLA Status Per Main Vendor for this year   |      VMS_UAT             |