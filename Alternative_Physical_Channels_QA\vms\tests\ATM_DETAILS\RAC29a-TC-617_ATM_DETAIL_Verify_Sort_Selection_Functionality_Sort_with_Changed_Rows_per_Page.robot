*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Sort Selection Functionality: Sort with Changed Rows per Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Verify Sort Selection Functionality: Sort with Changed Rows per Page
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${NUM_ROWS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user selects number of rows from the Rows per Page Menu    ${NUM_ROWS}

    And The user reads number of ATM rows on Frontend

    Then The user verifies the number of rows displayed is equal to the number of rows selected


| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |      ***NUM_ROWS***   |
| Verify Sort Selection Functionality: Sort with Changed Rows per Page | Verify Sort Selection Functionality: Sort with Changed Rows per Page | Verify Sort Selection Functionality: Sort with Changed Rows per Page |      VMS_UAT             |  5  |