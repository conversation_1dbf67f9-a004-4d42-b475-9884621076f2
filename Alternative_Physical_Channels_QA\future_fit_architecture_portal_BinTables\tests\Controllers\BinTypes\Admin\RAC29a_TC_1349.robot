*** Settings ***
# Author Name               : Thab<PERSON>
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/DeleteBinTypebyId_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
Delete Bin Type by ID
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}  ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}    ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Get the Bin Type ID from the database  ${CURR_BIN_TYPE_NAME}
    ${CURR_BIN_TYPE_NAME}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_NAME}
    ${CURR_BIN_TYPE_DESC}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_DESCRIPTION}
    ${BIN_TYPE_ID}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_ID}

    Given The User sends a DELETE Request to delete a Bin Type by ID         ${BASE_URL}  ${BIN_TYPE_ID}
    When The service returns an expected status code                         ${EXPECTED_STATUS_CODE}
    Then The bin type must still be active in the database                   ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}

| *** Test Cases ***                                                                                                           |             *DOCUMENTATION*    		     |    *BASE_URL*                  | *CURR_BIN_TYPE_NAME*      | *CURR_BIN_TYPE_DESC*            |   *EXPECTED_STATUS_CODE*
| Verify the DeleteBINtypeById  API by deleting a Bin Type that contains Bins- Not allowed           | Delete Bin Type by ID   | Delete Bin Type by ID and verify response   |                                |     Domestic              | ${EMPTY}                        |       404
