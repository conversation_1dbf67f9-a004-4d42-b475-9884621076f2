*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1032




*** Keywords ***
Verify Bin Types displayed on View Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_TYPE_TO_VERIFY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal  ${BASE_URL}
    When The User clicks Bin Type Menu
    Then The Bin Types displayed on the 'View' page must exist in the database and must be active
    Then The bins linked to the Bin Type(s) must match with the database data    ${BIN_TYPE_TO_VERIFY}

| *** Test Cases ***                                                                                          |        *DOCUMENTATION*    		          |         *BASE_URL*               |         *BIN_TYPE_TO_VERIFY*       |
| Capturer_Select and View Details for a Specific BIN Type        | Verify Bin Types displayed on View Page   | Verify bin types against the database.    |           ${EMPTY}               |           OnUs                     |
