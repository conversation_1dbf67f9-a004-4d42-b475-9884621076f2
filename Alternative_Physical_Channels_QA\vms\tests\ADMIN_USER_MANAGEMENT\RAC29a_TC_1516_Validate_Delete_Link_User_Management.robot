*** Settings ***
#Author Name               : T<PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS User Delete
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${USER_NAME}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And Searches for existing user  ${USER_NAME}
    And Deletes the user from the VMS Application   ${USER_NAME}
    And The user navigates to Admin - User Management
    Then The user should not be found when you are searching for it on VMS     ${USER_NAME}



| *Test Case*                                                                           |      *DOCUMENTATION*                                | *TEST_ENVIRONMENT*   |  *USER_NAME*    |
# | Validate Delete Link- User Management: Delete the VMS user that has a role of 'User'.          | VMS User Delete             | Deletes a VMS user that has a 'User' Role           |    VMS_UAT           |    AB0540to     |
# | Validate Delete Link- User Management: Delete the VMS user that has a role of 'Browse'.        | VMS User Delete             | Deletes a VMS user that has a 'Browse' Role         |    VMS_UAT           |    AB0541to     |
| Validate Delete Link- User Management: Delete the VMS user that has a role of 'Administrator'. | VMS User Delete             | Deletes a VMS user that has an 'Administrator' Role |    VMS_UAT           |    AB0542to     |
# | Validate Delete Link- User Management: Delete the VMS user that has a role of 'Supervisor'.    | VMS User Delete             | Deletes a VMS user that has a 'Supervisor' Role     |    VMS_UAT           |    AB0543to     |
