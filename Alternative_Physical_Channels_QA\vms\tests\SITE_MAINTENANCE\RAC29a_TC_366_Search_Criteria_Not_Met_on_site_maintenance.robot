*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    Site Maintenance
Suite Setup                                         Set up environment variables
Documentation                                       Search Criteria Not Met on Site Maintenance 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot 
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/VMSPage/Main.robot
Resource                                            ../../keywords/VMSPage/Site_Maintenance.robot

*Variables*


*** Keywords ***
Search Criteria Not Met on Site Maintenance
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    And The user lands on the dashboard page

    And The user navigates to the Site Maintenance Page 

    When The user performs a search with invalid criteria

    Then The user should see no search results

The user performs a search with invalid criteria
    # Implementation for searching with invalid criteria
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    InvalidSiteData123

The user should see no search results
    # Implementation for verifying no search results appear
    Sleep    1s
    # Check that the table is empty or contains a no results message
    ${table_rows}=    Get Element Count    xpath=//*[@id="root"]/div/table/tbody/tr
    # If there are no rows or only one row with a message, the search has no results
    Run Keyword If    ${table_rows} == 0    Log    No search results found - empty table
    ...    ELSE    Check For No Results Message
    
Check For No Results Message
    # Check if the single row contains a no results message or is empty
    ${row_content}=    Get Text    xpath=//*[@id="root"]/div/table/tbody/tr
    ${isEmpty}=    Run Keyword And Return Status    Should Be Empty    ${row_content}
    ${hasNoResults}=    Run Keyword And Return Status    Should Contain    ${row_content}    No matching    
    Run Keyword If    ${isEmpty} or ${hasNoResults}    Log    No search results found - message shown
    ...    ELSE    Fail    Search results were found when none expected

| *Test Case*                                                                                   |     *DOCUMENTATION*                          |     *TEST_ENVIRONMENT*   |        
| Search Criteria Not Met- on site maintenance  | Search Criteria Not Met on Site Maintenance     | Search Criteria Not Met on Site Maintenance  |      VMS_UAT             |