*** Settings ***
#Suite Setup                                         Connect To Database Using Custom Params    pymssql    '${DBHost}', '${DBUser}', '${DBPass}', '${DBName}'
#Suite Teardown                                      Disconnect From Database
Library                                             DatabaseLibrary
Library                                             OperatingSystem
Library                                             Collections
Library                                             String

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/DBUtility.robot


*** Variables ***
${DBHost}         XZAPBCC1SQL1004
${DBName}         MAIN_UAT
${DBPass}         Pa$$w0rd
${DBUser}         apl

*** Keywords ***
Verify Query - Get results as a list of dictionaries
    [Tags]    db    smoke
    ${output} =    Query    SELECT * FROM [MAIN_UAT].[dbo].[gasper_details];    \    True
    Log To Console    ${output}

Verify DB using python class
    ${db_type_1}=   Set Variable   'MSSQL'
    ${db_type}=   Convert To String     ${db_type_1}

    ${gasper_details_query}   Set Variable      SELECT * FROM [MAIN_UAT].[dbo].[gasper_details]

    ${gasper_details}=      Execute SQL Query  ${db_type}  ${gasper_details_query}    True

    ${total_number_of_campaigns}=    Get From Dictionary    ${gasper_details}    ADDRESS


