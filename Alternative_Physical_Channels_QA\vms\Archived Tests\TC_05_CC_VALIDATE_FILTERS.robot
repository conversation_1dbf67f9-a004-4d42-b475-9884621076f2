*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    FILTERS
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Test status update functionality of ATM Complaints & Complement

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../keywords/qrcode_cc/UpdateATMComplaints.robot 
Resource                                            ../keywords/qrcode_cc/ViewATMComplaintsAndCompliments.robot
Resource                                            ../keywords/qrcode_cc/StatusRegionFilter.robot
Resource                                            ../keywords/common/common_keywords.robot
Resource                                            ../keywords/common/Login.robot
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
#LOCAL VARIABLES
${SUCCESS_MESSAGE_TEXT}                             Complaint/ Compliment Updated

#For update function this field must be empty
${ASSIGNED_TO_VALUE}                              

*** Keyword ***
Validate filters
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${STATUS}  ${REGION}  ${TEST_CASE}
    Set Test Documentation  ${DOCUMENTATION}
   
    Log to console  REGION: ${REGION} Status: ${STATUS} 

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe
   
    And The user clicks navigate to QR Code Complaints and Compliments screen link

    Run Keyword If	'${TEST_CASE}' == "REGION FILTER"  User filter by region  ${REGION}
    ...  ELSE IF  '${TEST_CASE}' =="STATUS_FILTER"  	User filter by status  ${STATUS}

    And Close the popup screen

    And User logs out


| *Test Case*                                                                         |            *DOCUMENTATION*                                  |  *TESTRAIL_TESTCASE_ID*  |   *STATUS*                 |                  *REGION*                        |      *TEST_CASE*    |
| ATM CC - Validate filter - region         | Validate filters                        |      ATM CC - Validate filter - region                              |    101597695              |                            |   Eastern Cape                                   |   REGION FILTER
| ATM CC - Validate filter - status         | Validate filters                        |      ATM CC - Validate filter - region                              |    101597696              |     All                    |                                                  | STATUS_FILTER
