from typing import List, Union


class CreateRESTRequest:
    def __init__(self, domain, server_version):
        self.domain = domain
        self.server_version = server_version

        self.params = {
            "serverVersion": self.server_version,
        }

    def get_endpoint(self):
        path = "/api/v1/bintables/all/managementinformation/getdownloadmanagementinformation"
        url = f"{self.domain}{path}"
        return url

    def get_params(self):
        return self.params

