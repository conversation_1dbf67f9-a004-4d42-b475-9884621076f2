<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240607 09:45:32.323">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\ATMData\TC_02_GET_ATMData_CONTROLLER.robot">
      <test name="FFT - Controllers - Get ATM DATA using a valid auth token" id="s1-t1">
         <tags>
            <tag>FFT - Controllers - Get ATM DATA using a valid auth token</tag>
         </tags>
         <status endtime="20240607 09:45:35.677" critical="yes" status="FAIL" starttime="20240607 09:45:33.926"/>
      </test>
      <test name="FFT - Controllers - Get ATM DATA using an expired auth token" id="s1-t2">
         <tags>
            <tag>FFT - Controllers - Get ATM DATA using an expired auth token</tag>
         </tags>
         <status endtime="20240607 09:45:36.115" critical="yes" status="PASS" starttime="20240607 09:45:35.679"/>
      </test>
      <status endtime="20240607 09:45:36.117" status="FAIL" starttime="20240607 09:45:32.323"/>
   </suite>
   <statistics>
      <total>
         <stat pass="1" fail="1">Critical Tests</stat>
         <stat pass="1" fail="1">All Tests</stat>
      </total>
      <tag>
         <stat pass="0" fail="1">FFT - Controllers - Get ATM DATA using a valid auth token</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get ATM DATA using an expired auth token</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="1" fail="1" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
