*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Edit_or_Delete_BinTypes_Page.robot
Resource            ../../../../keywords/front_end/View_BinTypes_Page.robot

Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Edit Bin Types displayed on the Edit/Delete page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bin Type Menu
    Then The user must not be able to add, edit or delete a Bin Type without Admin Access
    And The user navigates to 'Edit/Delete' Bin Type tab

| *** Test Cases ***                                                                                                             |        *DOCUMENTATION*    		                   |         *BASE_URL*                  |
| Admin_Access Edit BINtype Functionality as Administrator                  | Edit Bin Types displayed on the Edit/Delete page   | Edit Bin Types displayed on the Edit/Delete page.   |           ${EMPTY}                  |
