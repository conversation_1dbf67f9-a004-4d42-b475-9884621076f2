<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240731 14:10:58.118">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\CampaignLookup\TC_10_GET_CampaignLookup_CONTROLLER.robot">
      <test name="FFT - Controllers - Verify the details of GET CampaignLookUp Controller against the Database" id="s1-t1">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Gets CampaignLookUp Result using a Business User auth</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240731 14:10:58.934" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20240731 14:10:59.321" status="PASS" starttime="20240731 14:10:58.934"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Gets CampaignLookUp Result using a Business User auth</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240731 14:10:59.321" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20240731 14:11:44.680" status="PASS" starttime="20240731 14:10:59.321"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Gets CampaignLookUp Result using a Business User auth</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240731 14:11:44.680" level="INFO">And The service returns http status</msg>
            <status endtime="20240731 14:11:44.683" status="PASS" starttime="20240731 14:11:44.680"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) returned by the GET CampaignLookup controller must correspond to the APC Database">
            <doc>Gets CampaignLookUp Result using a Business User auth</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240731 14:11:44.683" level="INFO">Then The field value(s) returned by the GET CampaignLookup controller must correspond to the APC Database</msg>
            <status endtime="20240731 14:11:58.915" status="PASS" starttime="20240731 14:11:44.683"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Verify the details of GET CampaignLookUp Controller against the Database</tag>
         </tags>
         <status endtime="20240731 14:11:58.915" critical="yes" status="PASS" starttime="20240731 14:10:58.933"/>
      </test>
      <status endtime="20240731 14:11:58.915" status="PASS" starttime="20240731 14:10:58.118"/>
   </suite>
   <statistics>
      <total>
         <stat pass="1" fail="0">Critical Tests</stat>
         <stat pass="1" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">FFT - Controllers - Verify the details of GET CampaignLookUp Controller against the Database</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="1" fail="0" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
