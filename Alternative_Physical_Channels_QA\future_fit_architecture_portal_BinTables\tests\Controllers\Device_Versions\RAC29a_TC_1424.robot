*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/controllers/DownloadBinTable_Keywords.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1424




*** Keywords ***
Download Bin Tables and verify that all the Bins that are on the seed file have been downloaded
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${DEVICE_NAME}  ${DEVICE_VERSION_NUMBER}   ${EXPECTED_STATUS_CODE}   ${BIN_TYPE_TO_VALIDATE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to download bin table                                                ${BASE_URL}      ${DEVICE_NAME}        ${DEVICE_VERSION_NUMBER}
    When The service returns an expected status code                                                        ${EXPECTED_STATUS_CODE}
    And The downloaded Bin(s) numbers returned by the Controller must exist on the Bin Table Master file   ${BIN_TYPE_TO_VALIDATE}
    Then The downloaded Bins must have the correct review status and associated to the correct bin type on the database     ${BIN_TYPE_TO_VALIDATE}

| *** Test Cases ***                                                                                                                                                            |        *DOCUMENTATION*    		                                    |         *BASE_URL*                  |     *DEVICE_NAME*      | *DEVICE_VERSION_NUMBER*     |   *EXPECTED_STATUS_CODE*   |   *BIN_TYPE_TO_VALIDATE*    |
| Validate the downloaded On-Us bin types against the seed file and database.           | Download Bin Tables and verify that all the Bins that are on the seed file have been downloaded    | Verify the downloaded bin types against the seed file.   |                                     |       S11782           |          0                  |        200                 |           OnUS             |
