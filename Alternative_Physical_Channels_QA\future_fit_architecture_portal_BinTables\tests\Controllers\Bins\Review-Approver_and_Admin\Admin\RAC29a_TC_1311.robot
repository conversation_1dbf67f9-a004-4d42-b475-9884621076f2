*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../../keywords/controllers/RejectBin_Keywords.robot
Resource                            ../../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Reject the pending Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}    ${BIN_REJECTION_COMMENTS}   ${BIN_STATUS}    ${EXPECTED_STATUS_CODE}   ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user gets a pending Bin from the database that can be rejected
    When The User Populates the Reject Bin JSON payload with    ${BIN_ID}   ${BIN_OUTCOME}    ${BIN_REJECTION_COMMENTS}
    And The User executes the Reject Bin API Request    ${BASE_URL}
    And The Reject Bin controller returns an expected status code     ${EXPECTED_STATUS_CODE}
    Then The expected Error must be displayed     ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                  |                   *DOCUMENTATION*                      |         *BASE_URL*         |          *BIN_REJECTION_COMMENTS*              |          *BIN_STATUS*         |       *EXPECTED_STATUS_CODE*  |       *EXPECTED_ERROR_MESSAGE*                                                               |
| Verify that the Reject API fails when no rejection reason is provided   | Reject the pending Bin    | Reject a pending Bin that is assigned to the approver  |                            |                                                |            Pending            |               500             |      Value cannot be null. (Parameter 'The Rejection Comment must not be null or empty.')    |
