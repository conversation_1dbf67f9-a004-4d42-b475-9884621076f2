*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Calls Logged against devices for this week

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot 
Resource                                            ../../../common_utilities/Logout.robot    
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/VMSPage/DeviceDataComparison.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../keywords/VMSPage/DeviceDataComparison.robot

*** Keywords ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION} 

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}  

    When The user lands on the dashboard page

    And The user reads the dashboard details for calls logged against devices for this week

    And The user reads the database details for calls logged against devices for this week

    Then The Database details must be the same as Front End details for calls logged against devices for this week
    
    # Even if there are differences, we'll log them and continue with test execution

| *Test Case*                                                                                          |                *DOCUMENTATION*                          |     *TEST_ENVIRONMENT*   |        
| Validate Calls Logged Against Devices- This Week | Dashboard Validation     | Validates calls logged against devices for this week   |      VMS_UAT             |