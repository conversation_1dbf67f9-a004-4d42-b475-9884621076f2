*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Documentation  View complaint/compliment details

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                            String

#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${MAIN_MENU}                                        //*[@href='Main.aspx']                        
${ADD_NEW_CALL}                                     //*[@id='btnAdd']  
${ADD_ATM_NUMBER_Dropdown}                          //*[@id='select2-MainContent_ddlAddAtmNumber-container']   
${ADD_NEW_VENDOR}                                   //*[@id='btnAddEmail']   
${SELECT_ATM_DROPDOWN}                              xpath~//*[@id='MainContent_ddlAddAtmNumber']  
${SELECT_VENDOR}                                    //*[@id='MainContent_ddlAddVendor']
${SELECT_VENDOR_DROPDOWN}                           xpath~//*[@id='MainContent_ddlAddVendor']
${ATM_NAME}                                         //*[@id='MainContent_txtAdd_ATMName']
${SELECT_DEVICE}                                    xpath~//*[@id='MainContent_ddlAdd_Device']
${EPP_SERIAL}                                       //*[@id='MainContent_txtAdd_EppSerial']
${ERROR_DESCRIPTION}                                //*[@id='MainContent_txtAdd_ErrorDesc']
${VENDOR_REF}                                       //*[@id='MainContent_txtAdd_VendorRef']
${CUSTODIAN}                                        //*[@id='MainContent_txtAdd_Custodian']
${INVOICE_NO}                                       //*[@id='MainContent_txtAdd_InvoiceNo']
${COST_CENTRE}                                      //*[@id='MainContent_txtAdd_CostCentre']
${COMMENTS}                                         //*[@id='MainContent_txtAdd_Comments']
${CELLPHONE}                                        //*[@id="MainContent_txtAdd_Cellphone"]
${CANCEL}                                           //*[@id="addCallModal"]/div/div/form/div[3]/div[2]/input[1]
${SLA}                                              //*[@id="MainContent_lblZoneSLA_Add"]

${div_class_gs_table_ele}    xpath=//*[contains(@class,'gs-table')]
${MAIN_ROW_COUNT_ELE}        XPATH=//*[contains(@class,'col-md-6')]/span


*** Keywords ***
The user clicks navigate to main 
    Log to console  --------------------------The user clicks navigate to main screen

    Click Element  ${MAIN_MENU}

    Sleep    5s

    Wait Until Element Is Visible  ${ADD_NEW_CALL}          5     Main 
    
    Wait Until Page Contains                                                       Main


The user gets front end data 
    FOR    ${Table_Headers}    IN    ${div_class_gs_table_ele}
    
        ${Table_Headers}   Get Text     ${Table_Headers}
        
        Log    ${Table_Headers}
            

    END

    Log Many    ${Table_Headers}

Keyword
    Sleep  5s

    Capture page screenshot  new_call_page.png

    perform dropdown select             			${SELECT_ATM_DROPDOWN}    ${SELECT_AN_ATM}

    Sleep    5s

    Run Keyword If  '${SELECT_AN_ATM}' == "S08003 - THE GLEN 1"  The user validates vendor for wincor and ncr

    Run Keyword If  '${SELECT_AN_ATM}' == "T5581F14 - Absa Building 1st Floor"  The user validates vendor for vetera    ${CELLPHONE_TEXT}

    Sleep    5s

    Input Text    ${EPP_SERIAL}    ${EPP_SERIAL_TEXT}   

    perform dropdown select             			${SELECT_VENDOR_DROPDOWN}    ${VENDOR_DROPDOWN}

    Sleep    5s

    perform dropdown select             			${SELECT_DEVICE}    ${DEVICE_DROPDOWN}

    Sleep    3S

    Input Text    ${ERROR_DESCRIPTION}     ${ERROR_DESCRIPTION_TEXT} 

    Input Text    ${VENDOR_REF}    ${VENDOR_REF_TEXT} 

    Input Text    ${CUSTODIAN}     ${CUSTODIAN_TEXT} 

    Input Text    ${INVOICE_NO}     ${INVOICE_NO_TEXT} 

    Input Text    ${COST_CENTRE}    ${COST_CENTRE_TEXT} 

    Input Text    ${COMMENTS}    ${COMMENTS_TEXT} 

    Sleep    3s

    Click Element    ${CANCEL} 

    Sleep    4S

The user validates vendor for wincor and ncr
    Log to console  --------------------------The user validates vendor for wincor and ncr

    #check SLA
    ${SLA}=                                GET TEXT    ${SLA}
    Log To Console    ${SLA}

    Should Contain  ${SLA}  4 hour SLA                msg=SLA does not match

     #remove new line
    ${VENDOR}=                                 GET TEXT  ${SELECT_VENDOR} 
    Log to console    ${VENDOR} 

    ${VENDOR_TEXT}=    Replace String    ${VENDOR}    \n    ${SPACE}

    Log to console    ${VENDOR_TEXT} 
    ${VENDOR_COMPARE}=    Set Variable   Select a vendor BMS BYTES
    Log to console    ${VENDOR_COMPARE}

    Should Contain  ${VENDOR_TEXT}  ${VENDOR_COMPARE}                msg=vendor does not match

The user validates vendor for vetera   
    [Arguments]    ${CELLPHONE_TEXT}
    Log to console  --------------------------The user validates vendor for vetera 

    #Check SLA
    ${SLA}=                                GET TEXT    ${SLA}
    Log To Console    ${SLA}

    Should Contain  ${SLA}  No SLA                msg=SLA does not match

     #remove new line
    ${VENDOR}=                                 GET TEXT  ${SELECT_VENDOR} 
    Log to console    ${VENDOR} 

    ${VENDOR_TEXT}=    Replace String    ${VENDOR}    \n    ${SPACE}

    Log to console    ${VENDOR_TEXT} 
    ${VENDOR_COMPARE}=    Set Variable   Select a vendor Gijima GPT                                            
    Log to console    ${VENDOR_COMPARE}

    Should Contain  ${VENDOR_TEXT}  ${VENDOR_COMPARE}                msg=vendor does not match

    Input Text    ${CELLPHONE}    ${CELLPHONE_TEXT} 
    









    


    

