*** Settings ***
#Author Name               : Yaash
#Email Address             : Yaash.<PERSON><PERSON><PERSON>@absa.africa

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../keywords/controllers/GetListOfServerVersions_Keywords.robot
Resource                            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***




*** Keywords ***
GetListOfServerVersions
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}   
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetListOfServerVersions        ${BASE_URL}      
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}     
    Then The Server Version List details must exist on the Bin Database                  


| *** Test Cases ***                                  |        *DOCUMENTATION*    		            |         *BASE_URL*                  |     *EXPECTED_STATUS_CODE*   |                
| GetListOfServerVersions   | GetListOfServerVersions | Search Bin by Number on the GetAllBins API  |                                     |         200                  |   
