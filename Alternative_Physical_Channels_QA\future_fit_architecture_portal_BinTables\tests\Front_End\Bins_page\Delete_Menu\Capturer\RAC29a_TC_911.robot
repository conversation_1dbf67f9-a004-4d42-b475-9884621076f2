*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-911




*** Keywords ***
Verifies that the Action Date does not permit past dates 
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    And The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The user verifies that no past dates are allowed for Action Date

| *** Test Cases ***                                                                                                                        |        *DOCUMENTATION*                |         *BASE_URL*             |         
| Capturer_Action Date for Delete Flow Cannot Be Set to a date in the Past   | Verifies that the Action Date does not permit past dates     |       Verifying Delete Action Date    |           ${EMPTY}             |           
    