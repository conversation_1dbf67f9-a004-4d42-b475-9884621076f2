*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables database connector keywords
#***********************************EXTERNAL LIBRIRIES***************************************
Library                     Collections
Library                     String
Library                     ../../../common_utilities/Database_Library.py
Library                     ../../../common_utilities/CommonUtils.py
Variables                   ../../utility/SQLVariables.py

#***********************************PROJECT RESOURCES***************************************


#***********************************PROJECT VARIABLES***************************************

*** Variables ***
${DB_TYPE}          mysql      # or 'mssql'
${DB_HOST}          oss-vip-02186.corp.dsarena.com
${DB_NAME}          appdb
${DB_USER}          app_account
${DB_PASSWORD}      CcQZglNU0E

${MS_DB_TYPE}          mssql      # or 'mssql'
${MS_DB_HOST}          XZAPBCC1SQL1004
${MS_DB_NAME}          VMS_UAT
${MS_DB_USER}          apl
${MS_DB_PASSWORD}      Pa$$w0rd

#*** Test Cases ***


*** Keywords ***



Execute Select Query
    [Arguments]    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}
    ${results}=    Execute Select Statement    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}
    Return From Keyword    ${results}

Execute Select Query with parameters
    [Arguments]    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}   ${params}
    ${results}=    Execute Select Statement with parameters   ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}   ${params}
    Return From Keyword    ${results}

Execute Select Query VMS
    [Arguments]    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}
    ${results}=    Execute Select Statement    ${db_type}    ${host}    ${database}    ${username}    ${password}    ${query}
    Return From Keyword    ${results}

Get Column Data By Name
    [Arguments]     ${results_row}  ${col_name}

    ${col_data}=    Get From Dictionary    ${results_row}    ${col_name}
    Log    Column data: ${col_data}
    RETURN  ${col_data}

Get the Bin details including the linked Bin Types from the Database
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_TO_REVIEW_WITH_FULL_DETAILS}

    ${my_query}=  Replace String      ${my_query}       bin_numbers     ${BIN_NUMBERS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD


    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the last action tracker details for the Bin
    [Arguments]     ${BIN_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_LAST_ACTION_PERFORMED_ON_THE_BIN}

    ${my_query}=  Replace String      ${my_query}       bin_id     ${BIN_ID}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin details for the newly created bin
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_NEW_BIN_DETAILS}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBERS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin details for Bins to be Reviwed from the Database
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_TO_REVIEW_WITH_FULL_DETAILS}

    ${my_query}=  Replace String      ${my_query}       bin_numbers     ${BIN_NUMBERS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the Bin details for Bins to be Reviwed from the Database where all bin types have been deleted
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_TO_REVIEW_WITH_FULL_DETAILS_ALL_BIN_TYPES_DELETED}

    ${my_query}=  Replace String      ${my_query}       bin_numbers     ${BIN_NUMBERS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}



Get the Bin details from the Database
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_WITH_FULL_DETAILS}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBERS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the DeviceVersions details from the Database
    [Arguments]     ${DEVICE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${DEVICE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_CONFIRM_DOWNLOAD_RECORD_FOR_DEVICE}

    ${my_query}=  Replace String      ${my_query}       device_name     ${DEVICE_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the Bin details and active Bin Types from the Database
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_AND_ACTIVE_BIN_TYPES}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBERS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the Bin details and linked Bin Types for all BINS from the Database

    ${DB_TYPE}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ALL_ACTIVE_BINS_AND_LINKED_BIN_TYPES}
    Log Many    ${my_query}

    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin ID using the Bin Number from the Database
    [Arguments]     ${BIN_NUMBER}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBER}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_ID_USING_BIN_Number}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBER}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin status using the Bin Number from the Database
    [Arguments]     ${BIN_NUMBER}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBER}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_STATUS_USING_BIN_Number}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBER}

    Log Many    ${my_query}

    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the Bin details from the Database used by GetBinByID controller
    [Arguments]     ${BIN_NUMBERS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBERS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_WITH_FULL_DETAILS_USED_BY_GET_BIN_BY_ID}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBERS}

    Log Many    ${my_query}

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    Log Many    ${results}
    RETURN      ${results}

Get the Bins to be reviewed from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_TO_REVIEW}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin to be approved from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_TO_APPROVE}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bins(bin id's) to be reviewed from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_IDS_TO_REVIEW}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get an approved bin from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_IDS_APPROVED}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get a pending bin from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_IDS_PENDING}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get a Bin(bin id) to be reviewed from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_ID_TO_REVIEW}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get a Bin(bin id) to be awaiting approval from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_ID_TO_APPROVE}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get all the active and inactive Bins from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ALL_ACTIVE_AND_INACTIVE_BINS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the in-active Bin number from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_INACTIVE_BIN}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get an active Bin from the Database
    [Arguments]  ${required_bin_status}

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_AN_ACTIVE_BIN}
    ${my_query}=  Replace String      ${my_query}       bin_status     ${required_bin_status}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD


    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get all the active and inactive Bins from the Database used by GetBinByID controller

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ALL_ACTIVE_AND_INACTIVE_BINS_USED_BY_GET_BIN_BY_ID}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get all the active and inactive Bins from the Database based on Bin Type Name
    [Arguments]    ${BIN_TYPE_NAME}

     #Verify that all parameters are supplied
    Run Keyword If    "${BIN_TYPE_NAME}" == "${EMPTY}"
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ALL_BINS_BASED_ON_BIN_TYPE_NAME}
    ${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get Bin details based on bin type name
    [Arguments]     ${BIN_NUMBER}    ${BIN_TYPE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBER}' == '${EMPTY}' or '${BIN_TYPE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!


    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_DETAILS_BASED_ON_BIN_TYPE_NAME}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBER}
    ${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get Bin details using Bin Number
    [Arguments]     ${BIN_NUMBER}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBER}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!


    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_DETAILS_BASED_ON_BIN_NUMBER}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBER}


    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get Bin details based on bin type name and outcome
    [Arguments]     ${BIN_NUMBER}    ${BIN_TYPE_NAME}       ${BIN_OUTCOME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NUMBER}' == '${EMPTY}' or '${BIN_TYPE_NAME}' == '${EMPTY}' or '${BIN_OUTCOME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!


    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_DETAILS_BASED_ON_BIN_TYPE_NAME_N_OUTCOME}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NUMBER}
    ${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}
    ${my_query}=  Replace String      ${my_query}       bin_outcome     ${BIN_OUTCOME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the Bin 'ISDeleted' status
    [Arguments]     ${BIN_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_IS_DELETED_STATUS}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log Many    ${results}
    ${results_len}=     Get Length    ${results}
    Should Be True    ${results_len} > 0       msg=The bin number '${BIN_NAME}' does not exist in the DB.
    ${bin_dict}=      Get From List    ${results}    0
    ${bin_status}=      Get From Dictionary    ${bin_dict}    IsDeleted
    ${results}=   Check If One Or Zero   ${bin_status}
    Log Many    ${results}

    RETURN      ${results}

Get Draft Bin Number
    ${db_type}=   Set Variable   MYSQL
    ${my_query}=   Set Variable    ${SQL_GET_DRAFT_BIN_NUMBER}

    Log    Executing query: ${my_query}

    ${DB_HOST}=    Read Config Property    BIN_DB_HOST
    ${DB_NAME}=    Read Config Property    BIN_DB_NAME
    ${DB_USER}=    Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=    Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${db_type}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    ${length}=    Evaluate    len(${results})
    
    Run Keyword If    ${length} == 0  Log    There are no Draft bins existing in the database!    WARN
    Run Keyword If    ${length} == 0    Skip

    Log    Draft bin(s) found, proceeding with the test.

    ${bin_dict}=    Get From List    ${results}    0
    ${draft_bin_number}=    Get From Dictionary    ${bin_dict}    Number

    Log    Draft bin number: ${draft_bin_number}

    RETURN    ${draft_bin_number}

Get Non-Draft Bin Number
    ${db_type}=   Set Variable   MYSQL
    ${my_query}=   Set Variable    ${SQL_GET_NON_DRAFT_BIN_NUMBER}

    Log    Executing query: ${my_query}

    ${DB_HOST}=    Read Config Property    BIN_DB_HOST
    ${DB_NAME}=    Read Config Property    BIN_DB_NAME
    ${DB_USER}=    Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=    Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${db_type}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    ${length}=    Evaluate    len(${results})
    
    Run Keyword If    ${length} == 0  Log    There are no Non-Draft bins existing in the database!    WARN
    Run Keyword If    ${length} == 0    Skip

    Log    Non-Draft bin(s) found, proceeding with the test.

    ${bin_dict}=    Get From List    ${results}    0
    ${non_draft_bin_number}=    Get From Dictionary    ${bin_dict}    Number

    Log    Non-Draft bin number: ${non_draft_bin_number}

    RETURN    ${non_draft_bin_number}

Verify if hard deleted bin exists
    [Arguments]    ${BIN_NAME}
    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_IS_BIN_HARD_DELETED}
    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NAME}
    
    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD


    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log    Results from DB: ${results}
    [Return]    ${results}

    ${length}=    Evaluate    len(${results}) if ${results} is not None else 0

Get Bin Id
    [Arguments]    ${BIN_NAME}
    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_ID}
    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NAME}
    
    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    ${bin_dict}=      Get From List    ${results}    0
    ${bin_id}=      Get From Dictionary    ${bin_dict}    Id
    
    Log Many    ${bin_id}
    Set Global Variable    ${database_bin_id}     ${bin_id}

    RETURN      ${bin_id}

Retrieve Delete Request Routing Details
    [Arguments]    ${database_bin_id}
    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ASSIGNED_USER_ROLE}
    ${my_query}=  Replace String      ${my_query}       bin_id     ${database_bin_id}
    
    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    ${bin_dict}=      Get From List    ${results}    0
    ${user_role}=      Get From Dictionary    ${bin_dict}    ToBeActionedBy
    
    Log Many    ${user_role}
    Set Global Variable    ${database_user_role}     ${user_role}

    RETURN      ${user_role}

Get the Bin 'IsNotADraft' status
    [Arguments]     ${BIN_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_IS_Not_A_DRAFT_STATUS}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log Many    ${results}
    ${results_len}=     Get Length    ${results}
    Should Be True    ${results_len} > 0       msg=The bin number '${BIN_NAME}' does not exist in the DB.
    ${bin_dict}=      Get From List    ${results}    0
    ${bin_is_draft}=      Get From Dictionary    ${bin_dict}    IsNotADraft
    ${results}=   Check If One Or Zero   ${bin_is_draft}
    Log Many    ${results}

    RETURN      ${results}

Is Bin Hard Deleted 
    [Arguments]     ${BIN_NAME}
    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

Get the Bin 'Status' field
    [Arguments]     ${BIN_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_STATUS_FIELD}

    ${my_query}=  Replace String      ${my_query}       bin_number     ${BIN_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log Many    ${results}
    ${results_len}=     Get Length    ${results}
    Should Be True    ${results_len} > 0       msg=The bin number '${BIN_NAME}' does not exist in the DB.
    ${bin_dict}=      Get From List    ${results}    0
    ${bin_status}=      Get From Dictionary    ${bin_dict}    Status
    ${results}=   Check If One Or Zero   ${bin_status}
    Log Many    ${results}

    RETURN      ${results}

Get the Bins returned by Delete search
    [Arguments]     ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_BINS_RETURNED_BY_DELETE_SEARCH}

    ${my_query}=  Replace String      ${my_query}       bin_returned_by_delete_search     ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log Many    ${results}

    RETURN      ${results}

Get the Bin Type 'ISDeleted' status
    [Arguments]     ${BIN_TYPE_ID_DETAILS}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_ID_DETAILS}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_TYPE_IS_DELETED_STATUS}

    ${my_query}=  Replace String      ${my_query}       bin_type_id     ${BIN_TYPE_ID_DETAILS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log Many    ${results}
    ${bin_dict}=      Get From List    ${results}    0
    ${bin_status}=      Get From Dictionary    ${bin_dict}    IsDeleted
    ${results}=   Check If One Or Zero   ${bin_status}
    Log Many    ${results}
    RETURN      ${results}


Get the Bin Number using the Bin Id
    [Arguments]     ${BIN_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_NUMBER_USING_BIN_TYPE}
    ${my_query}=  Replace String      ${my_query}       bin_id     ${BIN_ID}
    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}
    Log Many    ${results}
    RETURN      ${results}

Get the count on active Bin Type from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ACTIVE_BIN_TYPES_COUNT}

    Log Many    ${my_query}

    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the count on active Bins from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_AND_ACTIVE_BIN_TYPES_COUNT}

    Log Many    ${my_query}

    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get all the active Bin Types from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ACTIVE_BIN_TYPES}

    Log Many    ${my_query}

    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}



Get the count of Bins to review from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BINS_TO_REVIEW_COUNT}

    Log Many    ${my_query}

    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get the Bin Type details from the Database using the Bin Type Name
    [Arguments]     ${BIN_TYPE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_TYPE_DETAILS}

    ${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the random Bin Type details from the Database

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_RANDOM_BIN_TYPE_DETAILS}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the count of all Bins Linked to a Bin Type
    [Arguments]     ${BIN_TYPE_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ALL_ACTIVE_BINS_LINKED_TO_BIN_TYPE}
    ${my_query}=  Replace String      ${my_query}       bin_type_id     ${BIN_TYPE_ID}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin Type details from the Database using the partial Bin Type Name
    [Arguments]     ${BIN_TYPE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_TYPE_DETAILS_USING_PARTIAL_NAME}

    ${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}

Get the Bin Type details from the Database using the Bin Type Id
    [Arguments]     ${BIN_TYPE_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_TYPE_DETAILS_USING_BIN_TYPE_ID}

    ${my_query}=  Replace String      ${my_query}       bin_type_id     ${BIN_TYPE_ID}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get all active Bins details associated with the Bin Type from the Database using the Bin Type Name
    [Arguments]     ${BIN_TYPE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ACTIVE_BINS_DETAILS_FOR_BIN_TYPE}

    #${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}

    ${params}=   Set Variable   SET @BinTypeName = '${BIN_TYPE_NAME}';,SET @BinNumber = '';
    #${param2}=   Set Variable
    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query with parameters    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}    ${params}

    Log Many    ${results}
    RETURN      ${results}


Get all in-active Bins associated with the Bin Type from the Database using the Bin Type Name
    [Arguments]     ${BIN_TYPE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${BIN_TYPE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_INACTIVE_BINS_DETAILS_FOR_BIN_TYPE}

    ${my_query}=  Replace String      ${my_query}       bin_type_name     ${BIN_TYPE_NAME}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Get a Bin Type from the database using 'isDeleted' status
    [Arguments]     ${IS_DELETED_NUMBER}

    #Verify that all parameters are supplied
    Run Keyword If    '${IS_DELETED_NUMBER}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_BIN_TYPE_BIN_TYPE_DETAILS_USING_STATUS}

    ${my_query}=  Replace String      ${my_query}       deleted_status     ${IS_DELETED_NUMBER}

    Log Many    ${my_query}
    ${DB_HOST}=         Read Config Property    BIN_DB_HOST
    ${DB_NAME}=         Read Config Property    BIN_DB_NAME
    ${DB_USER}=         Read Config Property    BIN_DB_USER
    ${DB_PASSWORD}=     Read Config Property    BIN_DB_PASSWORD

    ${results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${results}
    RETURN      ${results}


Check If One Or Zero
    [Arguments]    ${value}

    ${result}=       Set Variable If
         ...       '${value}' == '0'       ${False}
         ...       '${value}' == '1'       ${True}
    IF    '${result}' == '${EMPTY}'
        RETURN    ${value}
    END

    RETURN    ${result}

Get Bin Status Text
    [Arguments]  ${STATUS_NUMBER}
     ${var}=       Set Variable If
         ...       '${STATUS_NUMBER}' == '0'       Approved
         ...       '${STATUS_NUMBER}' == '1'       Pending
         ...       '${STATUS_NUMBER}' == '2'       Rejected

     RETURN  ${var}

Get Bin Status Number
    [Arguments]  ${STATUS_NUMBER}
     ${var}=       Set Variable If
         ...       '${STATUS_NUMBER}' == 'Approved'     0
         ...       '${STATUS_NUMBER}' == 'Pending'      1
         ...       '${STATUS_NUMBER}' == 'Rejected'     2

     RETURN  ${var}

Get Bin Outcome Text
    [Arguments]  ${OUTCOME_NUMBER}
     ${var}=       Set Variable If
         ...       '${OUTCOME_NUMBER}' == '0'       Added
         ...       '${OUTCOME_NUMBER}' == '1'       Deleted
     RETURN  ${var}

Get Bin Outcome Number
    [Arguments]  ${OUTCOME_TEXT}
     ${var}=       Set Variable If
         ...       '${OUTCOME_TEXT}' == 'Added'     0
         ...       '${OUTCOME_TEXT}' == 'Deleted'   1
     RETURN  ${var}

Get Bin To Be Actioned By Text
    [Arguments]  ${TO_BE_ACTIONED_BY_NUMBER}
     ${var}=       Set Variable If
         ...       '${TO_BE_ACTIONED_BY_NUMBER}' == '0'       Approver
         ...       '${TO_BE_ACTIONED_BY_NUMBER}' == '1'       Capturer
         ...       '${TO_BE_ACTIONED_BY_NUMBER}' == '2'       Nobody
     RETURN  ${var}


Get Row By Column Value From DB results
    [Arguments]  ${db_results}  ${column_name}  ${value_to_find}
    FOR  ${row}  IN  @{db_results}
      ${col_data}=      Set Variable    ${row[${column_name}]}
      Run Keyword If  "${col_data}" == "${value_to_find}"  Return From Keyword  ${row}
    END
    Run Keyword And Continue On Failure     Fail  No row found with ${column_name} = ${value_to_find}
    RETURN  None

Get Rows By Column Values
    [Arguments]    ${data}    ${conditions}
    # Initialize an empty list to store the matching rows
    ${matching_rows}=    Create List

    # Loop through each row in the data
    FOR    ${row}    IN    @{data}
        # Assume the row matches the condition
        ${matching_row_found}=    Set Variable    ${True}
        ${keys}=    Evaluate    list(${conditions}.keys())    # Get the keys as a list

        # Loop through each condition and check if the row satisfies it
        FOR    ${key}    IN    @{keys}
            ${expected_value}=    Get From Dictionary    ${conditions}    ${key}
            ${expected_value}=   Convert to String      ${expected_value}
            Log    Key: ${key}, Value: ${expected_value}

            ${is_valid_date}=    Check If Valid Date    ${expected_value}
            IF    ${is_valid_date}
                ${converted_date}=      Convert to Date    ${expected_value}
                ${expected_date_array}=       Split String    ${converted_date}       separator=${SPACE}
                ${expected_value}=      Set Variable        ${expected_date_array}[0]
                 Log Many        expected_value: ${expected_value}
                # Extract column name from the current row's results
                ${curr_row_value}=   Set Variable    ${row[${key}]}
                ${curr_row_value}=   Convert to String      ${curr_row_value}
                ${curr_row_value_array}=   Split String    ${curr_row_value.strip()}       separator=${SPACE}
                ${curr_row_value}=   Set Variable    ${curr_row_value_array}[0]
            ELSE
               # Extract column name from the current row's results
                ${curr_row_value}=   Set Variable    ${row[${key}]}
                ${curr_row_value}=   Convert to String      ${curr_row_value}
            END


            # Check if the row's column matches the expected value
            IF    "${curr_row_value.strip()}" != "${expected_value.strip()}"
                 ${matching_row_found}=   Set Variable    ${False}
            ELSE
                 ${matching_row_found}=   Set Variable    ${True}
            END
            Exit For Loop If    not ${matching_row_found}
        END
        # If the row matches all conditions, add it to the matching rows list
        Run Keyword If    ${matching_row_found}    Append To List    ${matching_rows}    ${row}
        ${matching_rows_len}=   Get Length    ${matching_rows}
        Exit For Loop If    ${matching_row_found}

    END
    # Return the list of matching rows
    RETURN    ${matching_rows}

Get Row By Column Value From DB results and warn if no row found
    [Arguments]  ${db_results}  ${column_name}  ${value_to_find}
    FOR  ${row}  IN  @{db_results}
      ${col_data}=      Set Variable    ${row[${column_name}]}
      Run Keyword If  "${col_data}" == "${value_to_find}"  Return From Keyword  ${row}
    END
    Run Keyword And Warn On Failure     Fail  No row found with ${column_name} = ${value_to_find}
    RETURN  None

