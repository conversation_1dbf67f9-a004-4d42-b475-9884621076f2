import json
import re
from typing import Union, List

from robot.api.deco import keyword


class Update:
    def __init__(self, **kwargs):
        """
            The constructor initializes the class with a dictionary of bins and processes them to generate
            the necessary data for the JSON request. It extracts the bin id, bin number, action date, outcome, and bin type IDs for each bin,
            regardless of how many bins are passed or their indices.

            Arguments:

            kwargs: A dictionary containing the bin data, where keys represent the bin (e.g., bin1, bin2, etc.)
            and values represent the bin number. The dictionary should also include dateX and binIdsX for each bin.

            bins = {
                'binId1': 'e57287b8-319c-4475-823d-0201589a7f74',
                'bin1': '12345',
                'date1': '2024-11-11',
                'binTypeIds1': ['uuid1', 'uuid2'],
                'binOutcome1': 'Added',
                'binId2': 'e57287b8-319c-4475-823d-0201589a7f74',
                'bin2': '67890',
                'date2': '2024-11-12',
                'binTypeIds2': ['uuid3', 'uuid4'],
                'binOutcome2': 'Deleted'
            }
            generator = Update(bins)

            """

        # Initialize bins as an empty list
        self.bins = {}

        # Loop through all bins keys and create bin data
        for key, value in kwargs.items():
            # Check if the key starts with 'bin' (e.g., 'bin1', 'bin2', ...)
            if key.startswith("bin"):
                # Extract the bin index using regex (to allow for any number format)
                match = re.match(r"bin(\d+)", key)
                if match:
                    bin_index = match.group(1)  # Extract the index as string (e.g., '1', '2', '123')

                    # Now, retrieve associated data for this bin
                    bin_id = kwargs.get(f"binId{bin_index}")  # Get the corresponding bin id (e.g., 'binId1', 'binId2')
                    bin_date = kwargs.get(f"date{bin_index}")  # Get the corresponding date (e.g., 'date1', 'date2')
                    bin_Type_ids = kwargs.get(
                        f"binTypeIds{bin_index}")  # Get the corresponding bin Type IDs (e.g., 'binTypeIds1', 'binTypeIds2')
                    bin_outcome = kwargs.get(
                        f"binOutcome{bin_index}")  # Get the corresponding bin outcome (e.g., 'binOutcome1', 'binOutcome2')

                    # Make sure bin_ids is a list (in case it was passed as a string)
                    if isinstance(bin_Type_ids, str):
                        bin_Type_ids = bin_Type_ids.strip("[]").split(",")  # Convert the string to a list

                    # Append bin data if all required info is valid
                    if bin_date and isinstance(bin_Type_ids, list):
                        self.bins = {
                            "binId": bin_id,
                            "binNumber": value,  # bin1, bin2, ... contain the bin number
                            "actionDate": bin_date,
                            "binTypeIds": bin_Type_ids,
                            "outcome": bin_outcome
                        }

    def get_json_request(self):
        # Return the bins in JSON format
        return json.dumps(self.bins, indent=4)

    @keyword
    def create_bin_update_request(self, **kwargs):
        """
        This method processes the bins and returns the JSON request.
        """
        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def get_endpoint(self, domain):
        path = "/api/v1/bintables/entry/bins/update"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers

