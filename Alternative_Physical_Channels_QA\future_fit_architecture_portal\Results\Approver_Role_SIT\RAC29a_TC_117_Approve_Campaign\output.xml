<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:16:00.056856" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_117_Approve_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.141743" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:16:01.141743" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.142728" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:16:01.142728" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.142728" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:16:01.142728" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.142728" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:16:01.142728" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.142728" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:16:01.142728" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:01.141743" elapsed="0.000985"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_117_FFT_Approval_Approve_Campaign" line="43">
<kw name="Validating the Approval function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.143726" level="INFO">Set test documentation to:
Approve Campaign</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:16:01.143726" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.234733" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:16:01.143726" elapsed="0.091007"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:16:01.234733" elapsed="0.000999"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:16:01.235732" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.237823" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:16:01.237823" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:16:01.237823" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:16:01.237823" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:16:01.237823" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.237823" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:16:01.237823" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:16:01.237823" elapsed="0.001001"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.268717" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:16:01.644465" level="INFO">${rc_code} = 0</msg>
<msg time="2024-10-29T12:16:01.644465" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 17204 has been terminated.
SUCCESS: The process "chrome.exe" with PID 30644 has been terminated.
SUCCESS: The process "chrome.exe" with PID 2352 has been ter...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:16:01.238824" elapsed="0.406148"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:16:01.644972" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:16:01.236732" elapsed="0.408240"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.644972" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:16:01.644972" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:16:01.646479" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:16:01.646479" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:16:01.646479" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:16:01.646479" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:16:01.648501" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.001013"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:16:01.647488" elapsed="0.001013"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:16:01.646479" elapsed="0.002022"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.648501" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:16:01.648501" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.649495" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000027F33CA40B0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:16:01.648501" elapsed="0.000994"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:16:01.650003" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:16:01.650003" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:16:01.649495" elapsed="0.000508"/>
</branch>
<status status="PASS" start="2024-10-29T12:16:01.649495" elapsed="0.000508"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.651010" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:16:01.650003" elapsed="0.001007"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:16:01.651010" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:16:01.651010" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:16:01.651010" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:16:01.651010" elapsed="0.001392"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:16:01.652402" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:16:01.652402" elapsed="0.001009"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:16:01.653411" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.653411" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:16:01.653411" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:16:01.653411" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:16:01.143726" elapsed="33.761939"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:16:34.905665" elapsed="0.019256"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:34.930257" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.D7551D844B78B2E8D238C43FFD384466.e.152")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:34.924921" elapsed="0.005336"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:34.930257" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.D7551D844B78B2E8D238C43FFD384466.e.152")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:34.930257" elapsed="0.028908"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:16:34.960210" elapsed="0.006952"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:34.959165" elapsed="0.007997"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:34.976213" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.D7551D844B78B2E8D238C43FFD384466.e.153")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:34.968162" elapsed="0.008051"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:16:39.976488" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:16:34.976213" elapsed="5.000275"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:16:39.976488" elapsed="0.017770"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:39.994258" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.D7551D844B78B2E8D238C43FFD384466.e.153")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:39.994258" elapsed="0.082966"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:16:40.078223" elapsed="0.446931"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:40.078223" elapsed="0.446931"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:16:40.525154" elapsed="0.003997"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:40.525154" elapsed="0.004997"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:40.539149" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:16:40.530151" elapsed="0.008998"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:34.905665" elapsed="5.633484"/>
</kw>
<kw name="And The clicks on the preview button to preview an un-approved campaign" owner="Approvals">
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${dropdown_xpath}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:40.539149" elapsed="0.011909"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:40.551058" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${dropdown_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:40.551058" elapsed="0.030177"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:40.581235" level="INFO">Clicking element '//span[contains(text(), '100')]'.</msg>
<arg>${option_100_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:40.581235" elapsed="0.039368"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=${UNAPPROVED_CAMPAIGN_ROW_XPATH}/td[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:16:40.620603" elapsed="0.280875"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:16:42.901707" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:16:40.901478" elapsed="2.000229"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:42.917751" level="INFO">${CAMPAIGN_ID} = CNQ012v001Q42024</msg>
<var>${CAMPAIGN_ID}</var>
<arg>xpath=${UNAPPROVED_CAMPAIGN_ROW_XPATH}/td[2]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:42.901707" elapsed="0.016044"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:16:42.917751" level="INFO">Campaign ID: CNQ012v001Q42024</msg>
<arg>Campaign ID: ${CAMPAIGN_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:16:42.917751" elapsed="0.000000"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:16:42.918773" level="INFO">${CAMPAIGN_ID} = CNQ012v001Q42024</msg>
<arg>${CAMPAIGN_ID}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:16:42.917751" elapsed="0.001022"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${Un-Approved_Campaign}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T12:16:42.918773" elapsed="0.277375"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:16:48.196214" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:16:43.196148" elapsed="5.000066"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:48.197976" level="INFO">Clicking element '//tr[@role='row' and td[6]//mat-chip[contains(@class, 'not-approved')]]//fa-icon[@mattooltip='Preview']'.</msg>
<arg>${Un-Approved_Campaign}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:48.196850" elapsed="0.069082"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:16:48.265932" elapsed="1.404166"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:49.671075" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:49.670098" elapsed="0.052288"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:16:51.722880" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:16:49.722386" elapsed="2.000494"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:40.539149" elapsed="11.184246"/>
</kw>
<kw name="And The user approves the campaign" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${APPROVE_CAMPAIGN_BUTTON_XPATH}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:16:51.724409" elapsed="0.015424"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:16:56.741386" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:16:51.739833" elapsed="5.001553"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:16:56.741386" level="INFO">Clicking element '//button[contains(@class, 'mat-button') and contains(span/text(), 'Approve Campaign')]'.</msg>
<arg>${APPROVE_CAMPAIGN_BUTTON_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:16:56.741386" elapsed="0.054683"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:51.723395" elapsed="5.072674"/>
</kw>
<kw name="And The user navigates back to the Campaign Approvals page" owner="Navigation">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:17:01.796301" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:16:56.796069" elapsed="5.000232"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:01.807171" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.D7551D844B78B2E8D238C43FFD384466.e.153")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:01.796301" elapsed="0.010870"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:17:01.807171" elapsed="0.010090"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:01.817261" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.D7551D844B78B2E8D238C43FFD384466.e.153")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:01.817261" elapsed="0.047295"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:17:01.864556" elapsed="0.439816"/>
</kw>
<status status="PASS" start="2024-10-29T12:17:01.864556" elapsed="0.439816"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:17:07.304849" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:17:02.304372" elapsed="5.000477"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:07.317015" level="INFO">Current page contains text 'APPROVALS'.</msg>
<arg>APPROVALS</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:17:07.305356" elapsed="0.011659"/>
</kw>
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:07.318018" level="INFO">Executing JavaScript:
location.reload(true)
Without any arguments.</msg>
<arg>location.reload(true)</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-29T12:17:07.317015" elapsed="0.540115"/>
</kw>
<status status="PASS" start="2024-10-29T12:16:56.796069" elapsed="11.061061"/>
</kw>
<kw name="Then The user verifies that the campaign has been approved" owner="Approvals">
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:07.869148" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.ABE8C1A10BFEB17149D20686556D3E2C.e.238")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:07.858131" elapsed="0.011017"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:07.870129" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.ABE8C1A10BFEB17149D20686556D3E2C.e.238")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:07.870129" elapsed="0.282312"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:08.152441" level="INFO">Typing text 'CNQ012v001Q42024' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.ABE8C1A10BFEB17149D20686556D3E2C.e.238")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${CAMPAIGN_ID}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:08.152441" elapsed="0.067555"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.220350" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:17:08.219996" elapsed="2.000354"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:10.229490" level="INFO">${result_elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="05e28fb2e87bddaecf3d553833d228e4", element="f.B3D5E40AC5F835C5FEC03FE50E2F7911.d.ABE8C1A10BFEB17149D20686556D3E2C.e.281")&gt;]</msg>
<var>${result_elements}</var>
<arg>//tr[@role='row' and td[2][normalize-space(text())='${CAMPAIGN_ID}']]</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:10.220350" elapsed="0.009140"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.229490" level="INFO">Length is 1.</msg>
<msg time="2024-10-29T12:17:10.230865" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${result_elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2024-10-29T12:17:10.229490" elapsed="0.001375"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Campaign Status" owner="Approvals">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:10.242704" level="INFO">${status} = 15149 CNQ012v001Q42024 SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk Yaash Ramsahar (ZA) Oct 29, 2024
true</msg>
<var>${status}</var>
<arg>//tr[@role='row' and td[2][normalize-space(text())='${CAMPAIGN_ID}']]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:10.230865" elapsed="0.011839"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.242704" level="INFO">Campaign Approval Status: 15149 CNQ012v001Q42024 SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk Yaash Ramsahar (ZA) Oct 29, 2024
true</msg>
<arg>Campaign Approval Status: ${status}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:10.242704" elapsed="0.000000"/>
</kw>
<kw name="Split String" owner="String">
<msg time="2024-10-29T12:17:10.242704" level="INFO">${status_list} = ['15149', 'CNQ012v001Q42024', 'SIT_Cycle_4_Test', 'Un_-Targeted_English', 'Thabo_Setuk', 'Yaash', 'Ramsahar', '(ZA)', 'Oct', '29,', '2024', 'true']</msg>
<var>${status_list}</var>
<arg>${status}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<status status="PASS" start="2024-10-29T12:17:10.242704" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2024-10-29T12:17:10.242704" level="INFO">${approval_status} = true</msg>
<var>${approval_status}</var>
<arg>${status_list}</arg>
<arg>-1</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2024-10-29T12:17:10.242704" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.243726" level="INFO">Campaign Approved with status: true</msg>
<arg>Campaign Approved with status: ${approval_status}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:10.243726" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${approval_status.strip()}</arg>
<arg>true</arg>
<arg>Campaign approval status is false after approval, failing the test.</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2024-10-29T12:17:10.243726" elapsed="0.000988"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.244714" level="INFO">${approval_status} = true</msg>
<arg>${approval_status}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:17:10.244714" elapsed="0.000000"/>
</kw>
<arg>${result_elements}[0]</arg>
<status status="PASS" start="2024-10-29T12:17:10.230865" elapsed="0.013849"/>
</kw>
<arg>${result_count} == 1</arg>
<arg>Verify Campaign Status</arg>
<arg>${result_elements}[0]</arg>
<arg>ELSE IF</arg>
<arg>${result_count} &gt; 1</arg>
<arg>Log</arg>
<arg>Duplicate campaign found with ID: ${CAMPAIGN_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>No campaigns found with ID: ${CAMPAIGN_ID}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:17:10.230865" elapsed="0.013849"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.244714" level="INFO">----Campaign: CNQ012v001Q42024 successfully approved on front end----</msg>
<arg>----Campaign: ${CAMPAIGN_ID} successfully approved on front end----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:10.244714" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Campaign: ${CAMPAIGN_ID} successfully approved on front end----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:17:10.244714" elapsed="0.000997"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.245711" level="INFO">----Approval status set to: true----</msg>
<arg>----Approval status set to: ${approval_status}----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:10.245711" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:17:10.245711" level="INFO">----Approval status set to: true----</msg>
<arg>----Approval status set to: ${approval_status}----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:17:10.245711" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:17:07.858131" elapsed="2.387580"/>
</kw>
<arg>Approve Campaign</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:16:01.143726" elapsed="69.102983"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:17:10.246709" elapsed="0.005083"/>
</kw>
<status status="PASS" start="2024-10-29T12:17:10.246709" elapsed="0.005083"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:10.251792" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:17:10.251792" elapsed="0.033618"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:17:13.285950" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:17:10.285410" elapsed="3.000540"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:17:13.286565" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:17:13.405393" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:17:13.406294" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:17:13.286565" elapsed="0.121736">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:17:15.408504" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:17:13.408301" elapsed="2.000203"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:17:15.408504" elapsed="2.255623"/>
</kw>
<status status="FAIL" start="2024-10-29T12:17:10.251792" elapsed="7.412335">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:17:10.251792" elapsed="7.412335"/>
</kw>
<status status="PASS" start="2024-10-29T12:17:10.246709" elapsed="7.417418"/>
</kw>
<doc>Approve Campaign</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:16:01.142728" elapsed="76.522347"/>
</test>
<doc>Testing Camapaign Approval</doc>
<status status="PASS" start="2024-10-29T12:16:00.481230" elapsed="77.184861"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:16:00.048115" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_117_Approve_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:16:01.114797" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:16:19.779890" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:16:29.794259" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:16:34.804521" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
