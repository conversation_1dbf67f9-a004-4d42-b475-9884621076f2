*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        FFT_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for the Post ATM Marketing Result controller 

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_GET_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
Post ATM Marketing Result
    [Arguments]        ${DOCUMENTATION}    ${DATA_FILE}    ${BASE_URL}    ${SERVICE_PATH}    ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}    &{KW_ARGS}
    Set Test Documentation  ${DOCUMENTATION}

    #Replace the schedule version with the current version
    ${current_schedule_version}=        Get current schedule version
    ${key}=     Set Variable    scheduleVersion
    Log Many     ${KW_ARGS}
    ${KW_ARGS}=     Modify Kwargs Value     ${key}      ${current_schedule_version}     &{KW_ARGS}
    Log Many     ${KW_ARGS}

    #Replace the upload date with the current date
    ${current_date}=        Get Current Timestamp With Milliseconds
    ${key}=     Set Variable    uploadDate
    Log Many     ${KW_ARGS}
    ${KW_ARGS}=     Modify Kwargs Value     ${key}      ${current_date}     &{KW_ARGS}
    Log Many     ${KW_ARGS}

    #Add the schedule version to the upload description message

    ${key}=     Set Variable    resultDescription
    ${message_description}=        Catenate     ${KW_ARGS['resultDescription']}    ${current_schedule_version}
    Log Many     ${KW_ARGS}
    ${KW_ARGS}=     Modify Kwargs Value     ${key}      ${message_description}     &{KW_ARGS}
    Log Many     ${KW_ARGS}

    Given The user prepares a json payload                             ${SUITE_NAME}       ${DATA_FILE}    &{KW_ARGS}
    When The user creates a rest session                               ${BASE_URL}
    And The user makes Post Rest Call                                  ${SERVICE_PATH}     ${DATA_FILE}    ${EXPECTED_STATUS_CODE}
    And The service returns http status                                ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The field value(s) returned by the POST ATMMarketing Results controller must correspond to the APC Database

| *** Test Cases ***                                                                                                                                                     |               *DOCUMENTATION*               |      *DATA_FILE*      | *BASE_URL*                    | *SERVICE_PATH*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON* | *KW_ARGS*                                                                                                                                                                                                                          |
| FFT - Controllers - Post ATM Marketing Result and verify the response against the APC Database   | Post ATM Marketing Result                                    | Post ATM Marketing Result DB Verifications  |   ATMMarketingResult  | APC_API_UAT_BASE_URL          | ATMMARKETINGRESULT                | 201                              | Created                |  uploadResult=Successful | resultDescription=Successfully implemented marketing schedule  | isUpaloadSuccessful=True            |
