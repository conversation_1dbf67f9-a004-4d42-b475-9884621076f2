<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="******** 16:39:07.124" rpa="false" schemaversion="4">
<suite id="s1" name="TC 01 AUTHENRICATION AUTHORISATION" source="C:\development\future-fit-architecture-portal-docker\tests\Front-End\TC_01_AUTHENRICATION_AUTHORISATION.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:39:07.755" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 16:39:07.755" endtime="******** 16:39:07.755"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:39:07.756" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="******** 16:39:07.756" endtime="******** 16:39:07.756"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:39:07.756" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 16:39:07.756" endtime="******** 16:39:07.756"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.755" endtime="******** 16:39:07.756"/>
</kw>
<test id="s1-t1" name="Login &amp; Logout - BA" line="36">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Logout</arg>
<arg>155057390</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_DEV</arg>
<arg>*</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 16:39:07.757" level="INFO">Set test documentation to:
Login &amp; Logout</msg>
<status status="PASS" starttime="******** 16:39:07.757" endtime="******** 16:39:07.757"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:39:07.757" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057390'.</msg>
<status status="PASS" starttime="******** 16:39:07.757" endtime="******** 16:39:07.757"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:39:07.863" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 16:39:07.758" endtime="******** 16:39:07.863"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:39:07.864" endtime="******** 16:39:07.864"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:39:07.864" endtime="******** 16:39:07.865"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:39:07.866" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 16:39:07.866" endtime="******** 16:39:07.866"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 16:39:07.866" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 16:39:07.866" endtime="******** 16:39:07.866"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:39:07.867" endtime="******** 16:39:07.867"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 16:39:07.973" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 16:39:09.085" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 16:39:09.085" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 18348 has been terminated.
SUCCESS: The process "chrome.exe" with PID 23912 has been terminated.
SUCCESS: The process "chrome.exe" with PID 10332 has been te...</msg>
<status status="PASS" starttime="******** 16:39:07.867" endtime="******** 16:39:09.085"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:39:09.086" endtime="******** 16:39:09.086"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.865" endtime="******** 16:39:09.086"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:39:09.087" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000016C322DF680&gt;</msg>
<status status="PASS" starttime="******** 16:39:09.086" endtime="******** 16:39:09.087"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 16:39:09.087" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 16:39:09.087" endtime="******** 16:39:09.087"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:39:09.087" endtime="******** 16:39:09.088"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 16:39:09.088" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="******** 16:39:09.088" endtime="******** 16:39:09.088"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:39:09.088" endtime="******** 16:39:09.088"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:39:09.088" endtime="******** 16:39:09.089"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:39:09.089" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 16:39:09.089" endtime="******** 16:39:09.089"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 16:39:09.089" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 16:39:09.089" endtime="******** 16:39:24.444"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.865" endtime="******** 16:39:24.444"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 16:39:24.469" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 16:39:24.469" level="INFO">${base_url} = https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 16:39:24.444" endtime="******** 16:39:24.469"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 16:39:24.469" endtime="******** 16:39:24.509"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 16:39:24.511" level="INFO">Opening url 'https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 16:39:24.511" endtime="******** 16:39:52.412"/>
</kw>
<status status="PASS" starttime="******** 16:39:24.509" endtime="******** 16:39:52.412"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:40:02.414" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:39:52.413" endtime="******** 16:40:02.414"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:40:02.414" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:02.435" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 16:40:02.414" endtime="******** 16:40:02.435"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:40:02.435" endtime="******** 16:40:02.435"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:40:12.435" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:40:02.435" endtime="******** 16:40:12.435"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:40:12.435" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:12.457" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 16:40:12.435" endtime="******** 16:40:12.457"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:40:12.457" endtime="******** 16:40:12.457"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:40:17.457" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 16:40:12.457" endtime="******** 16:40:17.457"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:40:17.457" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:17.487" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 16:40:17.457" endtime="******** 16:40:17.487"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:40:17.487" endtime="******** 16:40:17.487"/>
</kw>
<status status="PASS" starttime="******** 16:39:24.444" endtime="******** 16:40:17.487"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 16:40:17.583" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-5.png"&gt;&lt;img src="selenium-screenshot-5.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 16:40:17.487" endtime="******** 16:40:17.583"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.865" endtime="******** 16:40:17.583"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.865" endtime="******** 16:40:17.583"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.757" endtime="******** 16:40:17.583"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.587"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.587"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.587"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.587"/>
</kw>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.587"/>
</kw>
<kw name="Then User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.596"/>
</kw>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.596"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:40:17.596" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 16:40:17.721" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-6.png"&gt;&lt;img src="selenium-screenshot-6.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 16:40:17.721" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 16:40:17.596" endtime="******** 16:40:17.793"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 16:40:17.793" endtime="******** 16:40:17.794"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 16:40:17.794" endtime="******** 16:40:17.794"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 16:40:17.794" endtime="******** 16:40:17.794"/>
</kw>
<status status="FAIL" starttime="******** 16:40:17.596" endtime="******** 16:40:17.794"/>
</kw>
<status status="PASS" starttime="******** 16:40:17.596" endtime="******** 16:40:17.794"/>
</kw>
<status status="PASS" starttime="******** 16:40:17.587" endtime="******** 16:40:17.794"/>
</kw>
<status status="PASS" starttime="******** 16:39:07.757" endtime="******** 16:40:17.795"/>
</kw>
<doc>Login &amp; Logout</doc>
<tag>FFT HEALTHCHECK</tag>
<status status="PASS" starttime="******** 16:39:07.756" endtime="******** 16:40:17.795"/>
</test>
<test id="s1-t2" name="Login- BA- Calendar" line="37">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Validate Calendar</arg>
<arg>155057392</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_DEV</arg>
<arg>Login- BA- Calendar</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 16:40:18.595" level="INFO">Set test documentation to:
Login &amp; Validate Calendar</msg>
<status status="PASS" starttime="******** 16:40:18.595" endtime="******** 16:40:18.595"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:40:18.596" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057392'.</msg>
<status status="PASS" starttime="******** 16:40:18.595" endtime="******** 16:40:18.596"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:40:18.598" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 16:40:18.598" endtime="******** 16:40:18.598"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:40:18.599" endtime="******** 16:40:18.600"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:40:18.600" endtime="******** 16:40:18.601"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:40:18.603" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 16:40:18.603" endtime="******** 16:40:18.603"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 16:40:18.604" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 16:40:18.603" endtime="******** 16:40:18.604"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:40:18.604" endtime="******** 16:40:18.605"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 16:40:18.725" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 16:40:19.304" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 16:40:19.304" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 3700 has been terminated.
SUCCESS: The process "chrome.exe" with PID 24232 has been terminated.
SUCCESS: The process "chrome.exe" with PID 10992 has been ter...</msg>
<status status="PASS" starttime="******** 16:40:18.605" endtime="******** 16:40:19.304"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:40:19.304" endtime="******** 16:40:19.304"/>
</kw>
<status status="PASS" starttime="******** 16:40:18.602" endtime="******** 16:40:19.304"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:40:19.307" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000016C322DF0B0&gt;</msg>
<status status="PASS" starttime="******** 16:40:19.304" endtime="******** 16:40:19.307"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 16:40:19.308" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 16:40:19.307" endtime="******** 16:40:19.308"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:40:19.308" endtime="******** 16:40:19.308"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 16:40:19.309" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="******** 16:40:19.309" endtime="******** 16:40:19.309"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:40:19.309" endtime="******** 16:40:19.309"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:40:19.309" endtime="******** 16:40:19.309"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:40:19.309" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 16:40:19.309" endtime="******** 16:40:19.309"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 16:40:19.309" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 16:40:19.309" endtime="******** 16:40:25.024"/>
</kw>
<status status="PASS" starttime="******** 16:40:18.602" endtime="******** 16:40:25.024"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 16:40:25.027" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 16:40:25.028" level="INFO">${base_url} = https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 16:40:25.025" endtime="******** 16:40:25.028"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 16:40:25.028" endtime="******** 16:40:25.032"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 16:40:25.036" level="INFO">Opening url 'https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 16:40:25.035" endtime="******** 16:40:46.263"/>
</kw>
<status status="PASS" starttime="******** 16:40:25.032" endtime="******** 16:40:46.263"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:40:56.264" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:40:46.263" endtime="******** 16:40:56.264"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:40:56.264" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:56.292" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 16:40:56.264" endtime="******** 16:40:56.293"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:40:56.293" endtime="******** 16:40:56.293"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:41:06.295" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:40:56.293" endtime="******** 16:41:06.295"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:41:06.295" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:41:06.300" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 16:41:06.295" endtime="******** 16:41:06.300"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:41:06.300" endtime="******** 16:41:06.300"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:41:11.300" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 16:41:06.300" endtime="******** 16:41:11.300"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:41:11.300" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:41:11.321" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 16:41:11.300" endtime="******** 16:41:11.321"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:41:11.322" endtime="******** 16:41:11.322"/>
</kw>
<status status="PASS" starttime="******** 16:40:25.024" endtime="******** 16:41:11.322"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 16:41:11.433" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-7.png"&gt;&lt;img src="selenium-screenshot-7.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 16:41:11.322" endtime="******** 16:41:11.433"/>
</kw>
<status status="PASS" starttime="******** 16:40:18.602" endtime="******** 16:41:11.433"/>
</kw>
<status status="PASS" starttime="******** 16:40:18.601" endtime="******** 16:41:11.433"/>
</kw>
<status status="PASS" starttime="******** 16:40:18.596" endtime="******** 16:41:11.433"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that the user can access the Calendar View" library="Authentication_Authorisation">
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:41:11.437" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 16:41:11.435" endtime="******** 16:41:11.550"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Greyed out Campaigns needs to be approved*</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 16:41:11.570" level="INFO">Current page contains text 'Greyed out Campaigns needs to be approved*'.</msg>
<status status="PASS" starttime="******** 16:41:11.550" endtime="******** 16:41:11.571"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.435" endtime="******** 16:41:11.571"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.434" endtime="******** 16:41:11.571"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:41:11.571" endtime="******** 16:41:11.572"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:41:11.572" endtime="******** 16:41:11.572"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:41:11.572" endtime="******** 16:41:11.573"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.434" endtime="******** 16:41:11.573"/>
</kw>
<kw name="Then User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 16:41:11.574" endtime="******** 16:41:11.598"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.574" endtime="******** 16:41:11.598"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:41:11.600" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 16:41:11.758" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-8.png"&gt;&lt;img src="selenium-screenshot-8.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 16:41:11.758" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 16:41:11.599" endtime="******** 16:41:11.760"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 16:41:11.760" endtime="******** 16:41:11.760"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 16:41:11.761" endtime="******** 16:41:11.761"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 16:41:11.761" endtime="******** 16:41:11.761"/>
</kw>
<status status="FAIL" starttime="******** 16:41:11.599" endtime="******** 16:41:11.761"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.598" endtime="******** 16:41:11.761"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.573" endtime="******** 16:41:11.761"/>
</kw>
<status status="PASS" starttime="******** 16:40:18.594" endtime="******** 16:41:11.761"/>
</kw>
<doc>Login &amp; Validate Calendar</doc>
<tag>FFT HEALTHCHECK</tag>
<status status="PASS" starttime="******** 16:40:18.593" endtime="******** 16:41:11.762"/>
</test>
<test id="s1-t3" name="Login- BA- Dashboard" line="38">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Validate Dashboard</arg>
<arg>155057391</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_DEV</arg>
<arg>Login- BA- Dashboard</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 16:41:11.979" level="INFO">Set test documentation to:
Login &amp; Validate Dashboard</msg>
<status status="PASS" starttime="******** 16:41:11.979" endtime="******** 16:41:11.979"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:41:11.980" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057391'.</msg>
<status status="PASS" starttime="******** 16:41:11.979" endtime="******** 16:41:11.980"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:41:11.981" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 16:41:11.981" endtime="******** 16:41:11.981"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:41:11.981" endtime="******** 16:41:11.983"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:41:11.983" endtime="******** 16:41:11.984"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:41:11.989" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 16:41:11.986" endtime="******** 16:41:11.989"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 16:41:11.990" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 16:41:11.989" endtime="******** 16:41:11.990"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:41:11.990" endtime="******** 16:41:11.991"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 16:41:12.155" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 16:41:13.138" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 16:41:13.138" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 26380 has been terminated.
SUCCESS: The process "chrome.exe" with PID 8860 has been terminated.
ERROR: The process "chrome.exe" with PID 5472 could not be te...</msg>
<status status="PASS" starttime="******** 16:41:11.991" endtime="******** 16:41:13.138"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:41:13.139" endtime="******** 16:41:13.139"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.986" endtime="******** 16:41:13.139"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:41:13.140" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000016C322DC0B0&gt;</msg>
<status status="PASS" starttime="******** 16:41:13.140" endtime="******** 16:41:13.140"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 16:41:13.141" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 16:41:13.141" endtime="******** 16:41:13.141"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:41:13.141" endtime="******** 16:41:13.142"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 16:41:13.142" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="******** 16:41:13.143" endtime="******** 16:41:13.143"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:41:13.143" endtime="******** 16:41:13.143"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:41:13.144" endtime="******** 16:41:13.144"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:41:13.145" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 16:41:13.144" endtime="******** 16:41:13.145"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 16:41:13.145" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 16:41:13.146" endtime="******** 16:41:19.647"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.985" endtime="******** 16:41:19.647"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 16:41:19.649" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 16:41:19.649" level="INFO">${base_url} = https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 16:41:19.648" endtime="******** 16:41:19.649"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 16:41:19.649" endtime="******** 16:41:19.653"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 16:41:19.655" level="INFO">Opening url 'https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 16:41:19.655" endtime="******** 16:41:41.571"/>
</kw>
<status status="PASS" starttime="******** 16:41:19.653" endtime="******** 16:41:41.571"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:41:51.571" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:41:41.571" endtime="******** 16:41:51.571"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:41:51.571" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:41:51.591" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 16:41:51.571" endtime="******** 16:41:51.591"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:41:51.591" endtime="******** 16:41:51.592"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:42:01.593" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:41:51.592" endtime="******** 16:42:01.593"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:42:01.593" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:42:01.617" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 16:42:01.593" endtime="******** 16:42:01.617"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:42:01.617" endtime="******** 16:42:01.617"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:42:06.618" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 16:42:01.617" endtime="******** 16:42:06.618"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:42:06.618" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:42:06.638" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 16:42:06.618" endtime="******** 16:42:06.638"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:42:06.638" endtime="******** 16:42:06.638"/>
</kw>
<status status="PASS" starttime="******** 16:41:19.647" endtime="******** 16:42:06.638"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 16:42:06.731" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-9.png"&gt;&lt;img src="selenium-screenshot-9.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 16:42:06.638" endtime="******** 16:42:06.731"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.985" endtime="******** 16:42:06.731"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.984" endtime="******** 16:42:06.731"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.980" endtime="******** 16:42:06.731"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:42:06.731" endtime="******** 16:42:06.731"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that the user can access the Dashboard" library="Authentication_Authorisation">
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[1]/span</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:42:06.746" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[1]/span'.</msg>
<status status="PASS" starttime="******** 16:42:06.746" endtime="******** 16:42:06.867"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Installed Schedule Version</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 16:42:06.887" level="INFO">Current page contains text 'Installed Schedule Version'.</msg>
<status status="PASS" starttime="******** 16:42:06.867" endtime="******** 16:42:06.887"/>
</kw>
<status status="PASS" starttime="******** 16:42:06.746" endtime="******** 16:42:06.887"/>
</kw>
<status status="PASS" starttime="******** 16:42:06.731" endtime="******** 16:42:06.887"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:42:06.887" endtime="******** 16:42:06.887"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:42:06.887" endtime="******** 16:42:06.887"/>
</kw>
<status status="PASS" starttime="******** 16:42:06.731" endtime="******** 16:42:06.887"/>
</kw>
<kw name="Then User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 16:42:06.887" endtime="******** 16:42:10.708"/>
</kw>
<status status="PASS" starttime="******** 16:42:06.887" endtime="******** 16:42:10.708"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:42:10.708" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 16:42:10.899" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-10.png"&gt;&lt;img src="selenium-screenshot-10.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 16:42:10.899" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 16:42:10.708" endtime="******** 16:42:10.899"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 16:42:10.899" endtime="******** 16:42:10.899"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 16:42:10.899" endtime="******** 16:42:10.899"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 16:42:10.899" endtime="******** 16:42:10.899"/>
</kw>
<status status="FAIL" starttime="******** 16:42:10.708" endtime="******** 16:42:10.899"/>
</kw>
<status status="PASS" starttime="******** 16:42:10.708" endtime="******** 16:42:10.899"/>
</kw>
<status status="PASS" starttime="******** 16:42:06.887" endtime="******** 16:42:10.899"/>
</kw>
<status status="PASS" starttime="******** 16:41:11.978" endtime="******** 16:42:10.899"/>
</kw>
<doc>Login &amp; Validate Dashboard</doc>
<tag>FFT HEALTHCHECK</tag>
<status status="PASS" starttime="******** 16:41:11.977" endtime="******** 16:42:10.899"/>
</test>
<test id="s1-t4" name="Login- BA- Approval" line="39">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Validate Approvals</arg>
<arg>155057393</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_DEV</arg>
<arg>Login- BA- Approval</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 16:42:11.215" level="INFO">Set test documentation to:
Login &amp; Validate Approvals</msg>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:42:11.215"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 16:42:11.215" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057393'.</msg>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:42:11.215"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:42:11.215" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:42:11.215"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:42:11.215"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:11.231"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:42:11.231" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:11.231"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 16:42:11.231" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:11.231"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:11.231"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 16:42:11.910" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 16:42:12.701" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 16:42:12.701" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 27476 has been terminated.
SUCCESS: The process "chrome.exe" with PID 14468 has been terminated.
SUCCESS: The process "chrome.exe" with PID 27392 has been te...</msg>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:42:12.701" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000016C323BCE30&gt;</msg>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 16:42:12.701" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 16:42:12.701" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 16:42:12.701" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:12.701"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 16:42:12.701" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 16:42:12.701" endtime="******** 16:42:18.964"/>
</kw>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:42:18.964"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 16:42:18.977" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 16:42:18.977" level="INFO">${base_url} = https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 16:42:18.965" endtime="******** 16:42:18.977"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 16:42:18.977" endtime="******** 16:42:18.981"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 16:42:18.985" level="INFO">Opening url 'https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 16:42:18.984" endtime="******** 16:42:42.398"/>
</kw>
<status status="PASS" starttime="******** 16:42:18.981" endtime="******** 16:42:42.398"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:42:52.399" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:42:42.398" endtime="******** 16:42:52.399"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:42:52.399" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:42:52.418" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 16:42:52.399" endtime="******** 16:42:52.418"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:42:52.418" endtime="******** 16:42:52.418"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:43:02.418" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 16:42:52.418" endtime="******** 16:43:02.418"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:43:02.418" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:43:02.429" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 16:43:02.418" endtime="******** 16:43:02.429"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:43:02.429" endtime="******** 16:43:02.430"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:43:07.430" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 16:43:02.430" endtime="******** 16:43:07.430"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 16:43:07.431" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:43:07.459" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 16:43:07.431" endtime="******** 16:43:07.459"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 16:43:07.459" endtime="******** 16:43:07.460"/>
</kw>
<status status="PASS" starttime="******** 16:42:18.965" endtime="******** 16:43:07.460"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 16:43:07.563" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-11.png"&gt;&lt;img src="selenium-screenshot-11.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 16:43:07.460" endtime="******** 16:43:07.563"/>
</kw>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:43:07.563"/>
</kw>
<status status="PASS" starttime="******** 16:42:11.231" endtime="******** 16:43:07.563"/>
</kw>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:43:07.563"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:43:07.565" endtime="******** 16:43:07.565"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:43:07.565" endtime="******** 16:43:07.566"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that the user can access approvals under admin" library="Authentication_Authorisation">
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/mat-expansion-panel-header/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:43:07.567" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/mat-expansion-panel-header/span[1]'.</msg>
<status status="PASS" starttime="******** 16:43:07.567" endtime="******** 16:43:07.693"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:43:09.694" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 16:43:07.693" endtime="******** 16:43:09.694"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/div/div/mat-nav-list/mat-list-item/span/span[3]</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:43:09.695" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/div/div/mat-nav-list/mat-list-item/span/span[3]'.</msg>
<status status="PASS" starttime="******** 16:43:09.694" endtime="******** 16:43:09.867"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 16:43:12.869" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 16:43:09.867" endtime="******** 16:43:12.869"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Approved By</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 16:43:12.900" level="INFO">Current page contains text 'Approved By'.</msg>
<status status="PASS" starttime="******** 16:43:12.870" endtime="******** 16:43:12.901"/>
</kw>
<status status="PASS" starttime="******** 16:43:07.567" endtime="******** 16:43:12.901"/>
</kw>
<status status="PASS" starttime="******** 16:43:07.566" endtime="******** 16:43:12.901"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 16:43:12.901" endtime="******** 16:43:12.902"/>
</kw>
<status status="PASS" starttime="******** 16:43:07.563" endtime="******** 16:43:12.902"/>
</kw>
<kw name="Then User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 16:43:12.903" endtime="******** 16:43:12.921"/>
</kw>
<status status="PASS" starttime="******** 16:43:12.902" endtime="******** 16:43:12.921"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 16:43:12.923" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 16:43:13.058" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-12.png"&gt;&lt;img src="selenium-screenshot-12.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 16:43:13.059" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 16:43:12.922" endtime="******** 16:43:13.060"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 16:43:13.061" endtime="******** 16:43:13.061"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 16:43:13.061" endtime="******** 16:43:13.061"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 16:43:13.061" endtime="******** 16:43:13.061"/>
</kw>
<status status="FAIL" starttime="******** 16:43:12.922" endtime="******** 16:43:13.061"/>
</kw>
<status status="PASS" starttime="******** 16:43:12.922" endtime="******** 16:43:13.061"/>
</kw>
<status status="PASS" starttime="******** 16:43:12.902" endtime="******** 16:43:13.062"/>
</kw>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:43:13.062"/>
</kw>
<doc>Login &amp; Validate Approvals</doc>
<tag>FFT HEALTHCHECK</tag>
<status status="PASS" starttime="******** 16:42:11.215" endtime="******** 16:43:13.063"/>
</test>
<doc>Testing future fit Authentication &amp; Authorisation</doc>
<status status="PASS" starttime="******** 16:39:07.307" endtime="******** 16:43:13.173"/>
</suite>
<statistics>
<total>
<stat pass="4" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="4" fail="0" skip="0">FFT HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="4" fail="0" skip="0" id="s1" name="TC 01 AUTHENRICATION AUTHORISATION">TC 01 AUTHENRICATION AUTHORISATION</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 16:39:18.282" level="WARN">The chromedriver version (124.0.6367.78) detected in PATH at C:\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 16:40:02.414" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:12.435" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:17.457" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:40:18.588" level="ERROR">Calling method 'end_test' of listener 'C:\development\future-fit-architecture-portal-docker\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 16:40:21.798" level="WARN">The chromedriver version (124.0.6367.78) detected in PATH at C:\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 16:40:56.264" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:41:06.295" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:41:11.300" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:41:11.971" level="ERROR">Calling method 'end_test' of listener 'C:\development\future-fit-architecture-portal-docker\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 16:41:15.787" level="WARN">The chromedriver version (124.0.6367.78) detected in PATH at C:\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 16:41:51.571" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:42:01.593" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:42:06.618" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:42:11.215" level="ERROR">Calling method 'end_test' of listener 'C:\development\future-fit-architecture-portal-docker\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 16:42:15.402" level="WARN">The chromedriver version (124.0.6367.78) detected in PATH at C:\Bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.76); currently, chromedriver 125.0.6422.76 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 16:42:52.399" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:43:02.418" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:43:07.431" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 16:43:13.168" level="ERROR">Calling method 'end_test' of listener 'C:\development\future-fit-architecture-portal-docker\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
