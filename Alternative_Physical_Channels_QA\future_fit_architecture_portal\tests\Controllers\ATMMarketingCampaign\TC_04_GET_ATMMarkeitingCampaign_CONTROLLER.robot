*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
GET marketing campaigns approvals
    [Arguments]        ${DOCUMENTATION}     ${TESTRAIL_TESTCASE_ID}     ${DATA_FILE}  ${BASE_URL}     ${SERVICE_PATH}   ${SERVICE_PATH_ID}   ${EXPECTED_STATUS_CODE}     ${JSON_RESPONSE_REASON}     &{EXPECTED_FIELDS_VALUES}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable                    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user creates a rest session                                ${BASE_URL}
    When The user makes Get Rest Call                                   ${SERVICE_PATH}     ${SERVICE_PATH_ID}     ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                                ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The rest service must return the response which contains      &{EXPECTED_FIELDS_VALUES}

| *** Test Cases ***                                                                                                                             |               *DOCUMENTATION*                                                          |  *TESTRAIL_TESTCASE_ID*  |      *DATA_FILE*                            | *BASE_URL*                    | *SERVICE_PATH*              | *SERVICE_PATH_ID*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON* | *EXPECTED_FIELDS_VALUES*                  |
#| FFT - Controllers - Get All Marketing Campaigns approvals using an Approver          | GET marketing campaigns approvals                |             Gets all Marketing Campaigns - valid token                                 |      155057476	          |                                             | APC_API_UAT_BASE_URL          | ATMMarketingCampaign        |                                      | 200                              | OK                     |          |                                |
#| FFT - Controllers - Get Marketing Campaigns approval by ID using an Approver         | GET marketing campaigns approvals                |             Gets a Marketing Campaign using the Campaign ID - valid token              |      155057480	          |                                             | APC_API_UAT_BASE_URL          | ATMMarketingCampaign        | 14238                                | 200                              | OK                     |          |                                |
| FFT - Controllers - Get All Marketing Campaigns approvals using a Business User      | GET marketing campaigns approvals                |             Gets all Marketing Campaigns - Business User                               |      155057476	          |                                             | APC_API_UAT_BASE_URL          | ATMMarketingCampaign        |                                      | 200                              | OK                     |          |                                |
| FFT - Controllers - Get Marketing Campaigns approval by ID using a Business User     | GET marketing campaigns approvals                |             Gets a Marketing Campaign using the Campaign ID - Business User            |      155057480	          |                                             | APC_API_UAT_BASE_URL          | ATMMarketingCampaign        | 14141                                | 200                              | OK                     |          |                                |

