*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/GetBinsByBintypeName.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot
#***********************************PROJECT VARIABLES***************************************
** Variables ***
${DATABASE_RESULTS}
${TOTAL_NUMBER_OF_BINS_FROM_DB}         0

*** Keywords ***
The User gets all the Bin numbers created in the Database


    ${database_query_executed}=         Run Keyword And Return Status    Should Be True   "${DATABASE_RESULTS}" != "${EMPTY}"

    IF    ${database_query_executed}
         Log Many     Database query has already been executed...
         RETURN
    END

    ${db_results}=     Get all the active and inactive Bins from the Database

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for bins awaiting review did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Save the database results in a global variable
    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Log Many    ${DATABASE_RESULTS}


The User gets all the Bin numbers(based on Bin Type Name) created in the Database



    ${bin_type_dict}=   Create List       Contactless    Domestic   Invalid    OnUs','On-Us   Token

    FOR    ${type_name}    IN    @{bin_type_dict}

        ${db_results}=     Get all the active and inactive Bins from the Database based on Bin Type Name        ${type_name}

        # Ensure the results are not empty
        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

        IF    not ${dr_results_contain_data}
             Log Many   Database SQL for bins that are linked to the Bin Type Name: '${type_name}' did not results!
             CONTINUE
        END

        ${num_rows}=    Get Length    ${db_results}

        ${TOTAL_NUMBER_OF_BINS_FROM_DB}=        Evaluate    ${num_rows} + ${TOTAL_NUMBER_OF_BINS_FROM_DB}

        Set Global Variable    ${TOTAL_NUMBER_OF_BINS_FROM_DB}        ${TOTAL_NUMBER_OF_BINS_FROM_DB}
       # Check that results is a list using Evaluate
        ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
        Should Be True    ${is_list}
    END

    #Save the total number of BINS from the database in an environment variable

    ${bin_Types}=   Set Variable  Contactless','Domestic','Invalid','OnUs','On-Us','Token
    ${db_results}=     Get all the active and inactive Bins from the Database based on Bin Type Name        ${bin_Types}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    IF    not ${dr_results_contain_data}
         Fail   Database SQL for bins that are linked to the Bin Type Name: '${type_name}' did not results!
    END

    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Set Global Variable    ${DATABASE_TOTAL_BINS}        ${TOTAL_NUMBER_OF_BINS_FROM_DB}

     ${num_rows}=    Get Length    ${DATABASE_RESULTS}

    Log Many    ${DATABASE_RESULTS}


All the Bin numbers' details returned by the GetBinsByBintypeName Controller must be the same as the details of the bins queried from the Database

    #Verify that the number of records returned by the database
    #is the same as the total number of Bins returned by the GetBinsByBintypeName
    #Controller

    ${total_bins_from_api}=    Evaluate    ${API_TOTAL_NUMBER_OF_CONTACTLESS_BINS} + ${API_TOTAL_NUMBER_OF_DOMESTIC_BINS} + ${API_TOTAL_NUMBER_OF_INVALID_BINS} + ${API_TOTAL_NUMBER_OF_ON_US_BINS} + ${API_TOTAL_NUMBER_OF_TOKEN_BINS}

    Run Keyword If    '${DATABASE_TOTAL_BINS}' != '${total_bins_from_api}'
    ...    Fail     Total number of Bins fetched from the database is not equal the total returned by the API! The database total is '${DATABASE_TOTAL_BINS}', and the API total is '${total_bins_from_api}'!

    #Loop through the DB Results and verify that each Bin that exists in the DB
     #was returned by GetBinsByBintypeName Controller
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       Number
        ${bin_type_name_data}=               Get Column Data By Name       ${row}       BinTypeName

        #Get the details for the current Bin from the database
        ${db_results}=     Get Bin details based on bin type name    ${binNumber_data}          ${bin_type_name_data}


         # Ensure the results are not empty
        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

        Run Keyword If    not ${dr_results_contain_data}
        ...     Fail    Database query for bin number: '${binNumber_data}' that has bin type name: '${bin_type_name_data}' returned no results.

        #Loop Through the review records details for the current BIN NUMBER and verify them against the Controller
        FOR    ${db_results_row}    IN    @{db_results}


            ${BIN_ID}=          Get Column Data By Name       ${db_results_row}       BinId
            ${BIN_NUMBER}=      Get Column Data By Name       ${db_results_row}       Bin_Number
            ${ACTION_DATE}=     Get Column Data By Name       ${db_results_row}       ActionDate
            ${BIN_TYPE}=        Get Column Data By Name       ${db_results_row}       BinTypeName

            #Verify the current DB record against the API results
            ${bin_type_dict}=   Create List       Contactless    Domestic   Invalid    OnUs','On-Us   Token
            
            ${rest_inst_var}=       Set Variable If
             ...       '${BIN_TYPE}' == 'Contactless'       ${CONTACTLESS_REST_RESPONSE_INSTANCE}
             ...       '${BIN_TYPE}' == 'Domestic'          ${DOMESTIC_REST_RESPONSE_INSTANCE}
             ...       '${BIN_TYPE}' == 'Invalid'           ${INVALID_REST_RESPONSE_INSTANCE}
             ...       '${BIN_TYPE}' == 'OnUs' or '${BIN_TYPE}' == 'On-Us'           ${ON_US_REST_RESPONSE_INSTANCE}
             ...       '${BIN_TYPE}' == 'Token'             ${TOKEN_REST_RESPONSE_INSTANCE}

            Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${rest_inst_var}


            #Verify against the Controller results
            The expected Bin Number details are retuned by the API Response     ${BIN_ID}   ${binNumber_data}   ${BIN_TYPE}    ${ACTION_DATE}
        END
    END



The User sends a Get Requests for GetBinsByBintypeName Controller using all available bin types

    #Get all 'Contactless' Bins
    The User sends a Get Request for GetBinsByBintypeName                 ${EMPTY}      Contactless      ${EMPTY}

    #Save the contactless Bins API response details
    Set Global Variable    ${CONTACTLESS_REST_RESPONSE_INSTANCE}        ${REST_RESPONSE_INSTANCE}
    Set Global Variable    ${API_TOTAL_NUMBER_OF_CONTACTLESS_BINS}        ${TOTAL_NUMBER_OF_BINS}

    #Get all 'Domestic' Bins
    The User sends a Get Request for GetBinsByBintypeName                 ${EMPTY}      Domestic      ${EMPTY}

    #Save the contactless Bins API response details
    Set Global Variable    ${DOMESTIC_REST_RESPONSE_INSTANCE}        ${REST_RESPONSE_INSTANCE}
    Set Global Variable    ${API_TOTAL_NUMBER_OF_DOMESTIC_BINS}        ${TOTAL_NUMBER_OF_BINS}

    #Get all 'Invalid' Bins
    The User sends a Get Request for GetBinsByBintypeName                 ${EMPTY}      Invalid      ${EMPTY}

    #Save the contactless Bins API response details
    Set Global Variable    ${INVALID_REST_RESPONSE_INSTANCE}        ${REST_RESPONSE_INSTANCE}
    Set Global Variable    ${API_TOTAL_NUMBER_OF_INVALID_BINS}        ${TOTAL_NUMBER_OF_BINS}

    #Get all 'On-Us' Bins
    The User sends a Get Request for GetBinsByBintypeName                 ${EMPTY}      On-Us      ${EMPTY}

    #Save the contactless Bins API response details
    Set Global Variable    ${ON_US_REST_RESPONSE_INSTANCE}        ${REST_RESPONSE_INSTANCE}
    Set Global Variable    ${API_TOTAL_NUMBER_OF_ON_US_BINS}        ${TOTAL_NUMBER_OF_BINS}

    #Get all 'Token' Bins
    The User sends a Get Request for GetBinsByBintypeName                 ${EMPTY}      Token      ${EMPTY}

    #Save the contactless Bins API response details
    Set Global Variable    ${TOKEN_REST_RESPONSE_INSTANCE}        ${REST_RESPONSE_INSTANCE}
    Set Global Variable    ${API_TOTAL_NUMBER_OF_TOKEN_BINS}        ${TOTAL_NUMBER_OF_BINS}


The User sends a Get Request for GetBinsByBintypeName
    [Arguments]     ${BASE_URL}     ${BIN_TYPE_NAME}     ${BIN_NUMBER}


    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetBinsByBintypeName Instance    ${base_url}     ${BIN_TYPE_NAME}    ${BIN_NUMBER}     #Create an instance of   GetBinsByBintypeName

    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   headers=${headers}   method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'GetBinsByBintypeName' REST API call failed, the returned status is '${status_code}'


Verify that the API retruned an empty response

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    #Verify that the response is empty
    ${verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '[]'     '${bin_object_instace}'

    Run Keyword If    not ${verification_passed}
    ...    Fail     The API did not return an empty response!



The expected Bin Number details are retuned by the API Response

    [Arguments]     ${BIN_ID}   ${BIN_NUMBER}   ${BIN_TYPE}    ${ACTION_DATE}


    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}

    ${binType_must_be_verified}=    Set Variable If  '${BIN_TYPE}' != '${EMPTY}'     ${True}     ${False}
    ${binType_verification_passed}=    Set Variable If  ${binType_must_be_verified}     ${False}     ${True}

    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    Log     '################################################'

    #Loop through all returned Bins to verify data
    FOR    ${index}    ${element}    IN ENUMERATE    @{bin_object_instace}
        Log    ${index}: ${element}
        ${binNumber_data}=        Get the Bin Number Details     ${element}
        Log    '${binNumber_data}'
        #IF the BIN Number is found then verify its details
        IF    '${binNumber_data}' == '${BIN_NUMBER}'
            Log     '################################################'
            ${binNumber_verification_passed}=      Set Variable    ${True}
            ${binNumber_verified}=      Set Variable    ${True}
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$

            ${binId_data}=         Get the Bin Response field data     ${element}     get_id
            #Check if the Bin ID must be verified against the user's data.
            IF    ${binID_must_be_verified}
                ${binID_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_ID}'     '${binId_data}'
            END

            ${bin_binType_data}=    Get the Bin Response field data     ${element}     get_bin_type
            ${bin_binType_data_array}=      Split String    ${bin_binType_data}         separator=,
            #Check if the Bin Type must be verified against the user's data
            IF    ${binType_must_be_verified}
                IF    '${BIN_TYPE}' == 'OnUs'
                 ${BIN_TYPE}=   Set Variable    On-Us
                END
                ${bin_binType_data_array}=   Split String    ${bin_binType_data.strip()}    separator=,
                FOR    ${bin_binType_element}    IN    @{bin_binType_data_array}
                    Log    ${bin_binType_element}
                    ${binType_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_TYPE}'     '${bin_binType_element.strip()}'
                END

            END


            ${binActionDate_data}=    Get the Bin Response field data     ${element}     get_action_date
            #Check if the Action Date must be verified against the user's data
            IF    ${actionDate_must_be_verified}
                ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
            END

            #The loop will be exited if all the parameters have been verified against the response
            Run Keyword If    ${binNumber_verified}
            ...    Exit For Loop
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
        END
    END

    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the API response.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binType_must_be_verified}


        Run Keyword If    ${binType_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

#Keywords to read error response fields
Get Error details data
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}

Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_detail}'     '${EXPECTED_ERROR_MESSAGE}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       binTypeName

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${EXPECTED_ERROR_MESSAGE}' in '${api_error_message_fields}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Upload' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.


#The below keywords are for Database Verifications


The Bin Number details must exist on the Bin Database
    [Arguments]     ${BIN_ID}   ${BIN_NUMBER}   ${BIN_TYPE}   ${ACTION_DATE}

    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}

    ${binType_must_be_verified}=    Set Variable If  '${BIN_TYPE}' != '${EMPTY}'     ${True}     ${False}
    ${binType_verification_passed}=    Set Variable If  ${binType_must_be_verified}     ${False}     ${True}

    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${db_results}=     Get the Bin details for Bins to be Reviwed from the Database    ${BIN_NUMBER}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin number: '${BIN_NUMBER}' returned no results

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Loop through the returned DB results and verify them against the user's supplied parameters
    ${num_rows}=    Get Length    ${db_results}
    #Loop Through the review records details for the current BIN NUMBER and verify them against the Controller
    #The below variables are used to detect and filter out the duplicate columns
    ${bin_type_already_read_value}=       Set Variable      ${EMPTY}
    ${tobeActionedBy_Value}=    Set Variable       ${EMPTY}
    FOR    ${row}    IN    @{db_results}
        ${binNumber_verification_passed}=      Set Variable    ${True}
        ${binNumber_verified}=      Set Variable    ${True}

        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType
        IF    '${bin_type_already_read_value}' == '${EMPTY}'
            ${bin_type_already_read_value}=   Set Variable     ${bin_binType_data}
        ELSE
             IF    '${bin_type_already_read_value}' == '${bin_binType_data}'
                 CONTINUE
             END
        END

        ${bin_toActionedBy_data}=        Get Column Data By Name       ${row}       toBeActionedBy
        ${bin_toActionedBy_data}=        Get Bin To Be Actioned By Text     ${bin_toActionedBy_data}

        IF    '${tobeActionedBy_Value}' == '${EMPTY}'
            ${tobeActionedBy_Value}=   Set Variable     ${bin_toActionedBy_data}
        ELSE
             IF    '${tobeActionedBy_Value}' != '${bin_toActionedBy_data}'
                 CONTINUE
             END
        END


        #Verify the bin details
        ${binID_data}=                   Get Column Data By Name       ${row}       Bin_ID

        ${binActionDate_data}=           Get Column Data By Name       ${row}       actionDate

        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType

        #Check if the Bin ID must be verified against the user's data.
        IF    ${binID_must_be_verified}
            ${binID_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_ID}'     '${binID_data}'
        END

        #Check if the Action Date must be verified against the user's data
        IF    ${actionDate_must_be_verified}
            ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
        END


        #Check if the binType must be verified against the user's data
        IF    ${binType_must_be_verified}

            IF    '${BIN_TYPE}' == 'On-Us' or '${BIN_TYPE}' == 'ON-US'
                 ${BIN_TYPE}=       Set Variable    OnUs
            END

            ${binType_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_TYPE}'     '${bin_binType_data}'
        END

        #The loop will be exited if all the parameters have been verified against the response
        Run Keyword If    ${binType_verification_passed}
        ...    Exit For Loop
        #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
    END


    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the Database.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the Database.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binType_must_be_verified}
        Run Keyword If    ${binType_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the BIN_TYPE value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



# The below keywords are used to interact with the API POJOS

Create GetBinsByBintypeName Instance
    [Arguments]    ${BASE_URL}  ${BIN_TYPE_NAME}  ${BIN_NUMBER}
    ${instance}=    Evaluate    GetBinsByBintypeName.CreateRESTRequest('${BASE_URL}','${BIN_TYPE_NAME}','${BIN_NUMBER}')    modules=GetBinsByBintypeName
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords
Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal

    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}

   # Count the number of BINS returned
    ${bin_object_instace}=      Get the Bin Details
    ${item_count}=    Get Length    ${bin_object_instace}
    Log Many    ${item_count}
    Set Global Variable    ${TOTAL_NUMBER_OF_BINS}        ${item_count}

Get Response Status Code
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}


#Keyword to read successful response
Get the Bin Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bins_by_bin_type_name_details
    RETURN    ${result}


Get the Bin Response field data
     [Arguments]     ${BINS_INSTNANCE}   ${METHOD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    ${METHOD_NAME}
    RETURN    ${result}

Get the Bin Number Details
     [Arguments]     ${BINS_INSTNANCE}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_bin_number
    RETURN    ${result}



