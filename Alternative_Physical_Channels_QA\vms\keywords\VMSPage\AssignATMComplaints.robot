*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>
Documentation  Assign re-assaign a complaint to a consultant

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/VMSPage/LandingPage.robot
Resource                                            ../../keywords/common/common_keywords.robot

*** Variables ***
${ASSIGN_TO_DROPDOWN}                               id~assignConsultant     
${REASSIGN_BTN}                                     id=btnAssign   
${CONFIRM_MESSAGE}                                  id=ConfirmMsg
${SUCCESS_MESSAGE_TEXT}                             Complain<PERSON> has been successfully assigned to
${OK_BTN}                                           id=MainContent_btnSubmitComplete

*** Keywords ***
User re-assigns a compliant
	[Arguments]    ${ASSIGN_TO_USER} 

    Log to console  --------------------------Starting to Assign

    User clicks on update link

    Wait until Page Contains                        Update - ATM Complaints and Compliments

	Sleep  3s

    Capture page screenshot  Assign_ATM_C_and_C_before.png

    perform dropdown select             			${ASSIGN_TO_DROPDOWN}  ${ASSIGN_TO_USER}

    Click Element                                   ${REASSIGN_BTN}

    Capture page screenshot  Assign_ATM_C_and_C_after.png

    User confirms action                            ${SUCCESS_MESSAGE_TEXT}     
