*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/Delete.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                           ApproveBin_Keywords.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************

*** Variables ***



*** Keywords ***
The user gets a bin that is not deleted from the database
    [Arguments]  ${required_bin_status}

    ${bin_counter}=    Set Variable    1
    &{BINS_TO_DELETE}=  Create Dictionary
    &{BINS_TO_APPROVE}=  Create Dictionary
    ${required_bin_status_number}=     Get Bin Status Number    ${required_bin_status}

    #Get the Bin ID for the active bin from the database
    ${db_results}=     Get an active Bin from the Database   ${required_bin_status_number}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...    Fail    Database query for retrieving an active bin's Id with the review status of '${required_bin_status}' returned no results.

    IF    not ${dr_results_contain_data}
        RETURN
    END

    ${first_row_results}=             Get From List    ${db_results}    0    # Get the first row
    ${binId_value}=    Get Column Data By Name       ${first_row_results}       binId
    Log Many    The value of the binId column is: ${binId_value}

    #Get the status of the current bin to verify if it is Pending (Only pending Bins can be rejected)

     #Get the Database details for the current Bin
    ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${binId_value}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin id: '${binId_value}' returned no results!

     ${first_row_results}=             Get From List    ${action_tracker_db_results}    0    # Get the first row

    #Verify the bin details
    ${bin_outcome_data}=            Get Column Data By Name       ${first_row_results}       ActionType
    ${bin_outcome_text}=            Get Bin Outcome Text       ${bin_outcome_data}
    ${bin_reject_comment_data}=     Get Column Data By Name       ${first_row_results}       RejectionComment
    ${bin_status_data}=             Get Column Data By Name       ${first_row_results}       Status
    ${bin_status_text}=           Get Bin Status Text     ${bin_status_data}

    IF   '${bin_status_text}' == '${required_bin_status}'
        ${binId_key}=    Set Variable    binId${bin_counter}
        ${binOutcome_key}=    Set Variable    outcome${bin_counter}

        #Save the pending Bin details to a dictionary
        Set To Dictionary    ${BINS_TO_DELETE}    ${binId_key}=${binId_value}
        Set To Dictionary    ${BINS_TO_APPROVE}    ${binId_key}=${binId_value}      ${binOutcome_key}=Deleted
        Log  Bin with the Id: '${binId_value}' will be deleted.
    ELSE
        Fail  Bin with the Id: '${binId_value}' cannot be deleted because its current review status is not the same as the expected status. Thi bin review status is '${bin_status_text}'.
    END

    #Fail the test if there are no bins to reject
    Run Keyword If    "${BINS_TO_DELETE}" == "{}"
    ...    Fail     There are no bins to delete on the DB!

    Log Many    BINS that will be deleted using the Reject Controller:   '${BINS_TO_DELETE}'

    Set Global Variable    ${BINS_TO_DELETE}     ${BINS_TO_DELETE}
    Set Global Variable    ${BINS_TO_APPROVE}     ${BINS_TO_APPROVE}

The user deletes the Bin retrieved from the database using the Delete Bin Controller

    ${dict_length}=    Get Length    ${BINS_TO_DELETE}
    ${bin_id_data}=    Get From Dictionary    ${BINS_TO_DELETE}    binId1
    Log    binId1=${bin_id_data}

    #Reject each bin using the Reject Bin controller
    The user verifies that the current BIN is not Deleted   ${bin_id_data}
    The User executes the Delete Bin API Request    ${EMPTY}    ${bin_id_data}
    The Delete Bin controller returns an expected status code     200

    #Approve the Bin deletion
    ${bin_number_data}=         Get the Bin Number using the Bin Id     ${bin_id_data}
    The User Populates the Approve Bin JSON payload with data    &{BINS_TO_APPROVE}
    The User sends an API request to Approve the Bin(s)       ${EMPTY}
    The service returns an expected status code     200


The user verifies that the current BIN is not Deleted
    [Arguments]     ${BIN_ID}

    # Ensure the bins are not empty
    ${dict_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${BIN_ID}
    Run Keyword If    not ${dict_contain_data}
    ...     Fail    Please provide the Bin ID that must be queried from the DB.

    #Get the bin number for the provided bin id
    ${db_results}=     Get the Bin Number using the Bin Id     ${BIN_ID}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin id: '${BIN_ID}' returned no results
     #Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}
    ${num_rows}=    Get Length    ${db_results}

    Run Keyword If    ${num_rows} > 1
    ...    Fail     Database query for bin id: '${BIN_ID}' returned ${num_rows} bins.

    FOR    ${row}    IN    @{db_results}
       ${bin_number}=             Get Column Data By Name       ${row}       binNumber
    END
    ${bin_is_active}=       Verify that the Bin Deletion Status is as expected      ${bin_number}        ${False}
    Run Keyword If    ${bin_is_active}
    ...    Log Many    The BIN Number: '${bin_number}' is not deleted on the database.
    ...  ELSE
    ...    Fail    The BIN Number: '${bin_number}' is deleted on the database hence it cannot be edited.

The the BIN's isDeleted status must be the same as the expected status
    [Arguments]     ${BIN_ID}    ${EXPECTED_IS_DELETED_STATUS}



    #IF the Bin ID is not provided then look for the bin ID that has been saved in the env variable
    IF    '${BIN_ID}' == '${EMPTY}' or '${BIN_ID}' == 'None'
       ${BIN_ID}=       Get From Dictionary    ${BINS_TO_DELETE}    binId1
    END

    #Verify that all parameters have been provided
    Run Keyword If    '${BIN_ID}' == '${EMPTY}' or '${BIN_ID}' == 'None'
    ...    Fail  Please provide the Bin Id for the Bin that must be/has been deleted!
    ...  ELSE IF    '${EXPECTED_IS_DELETED_STATUS}' == '${EMPTY}' or '${EXPECTED_IS_DELETED_STATUS}' == 'None'
    ...    Fail  Please provide the expected Bin 'isDeleted' status for the Bin that must be/has been deleted!

    #Get the bin number for the provided bin id
    ${db_results}=     Get the Bin Number using the Bin Id     ${BIN_ID}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin id: '${BIN_ID}' returned no results
     #Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}
    ${num_rows}=    Get Length    ${db_results}

    Run Keyword If    ${num_rows} > 1
    ...    Fail     Database query for bin id: '${BIN_ID}' returned ${num_rows} bins.

    ${first_row_results}=             Get From List    ${db_results}    0    # Get the first row
    ${bin_number}=    Get Column Data By Name       ${first_row_results}       binNumber

    ${expected_is_deleted_status_text}=    Check If One Or Zero   ${EXPECTED_IS_DELETED_STATUS}
    ${actual_bin_is_deleted_status}=       Get the Bin 'ISDeleted' status      ${bin_number}

    Run Keyword If    '${actual_bin_is_deleted_status}' == '${expected_is_deleted_status_text}'
    ...    Log Many    The database 'isDeleted' status for BIN Number: '${bin_number}' is as per user expected status.
    ...  ELSE
    ...    Fail    The database 'isDeleted' status for BIN Number: '${bin_number}' is not the same as the expected status. The database status is  '${actual_bin_is_deleted_status}', while the expected status is '${expected_is_deleted_status_text}'.



Create Delete Bin Instance
    [Arguments]    ${BASE_URL}  ${BIN_ID}
    ${instance}=    Evaluate    Delete.CreateRESTRequest('${BASE_URL}','${BIN_ID}')    modules=Delete
    RETURN    ${instance}
Get Delete Bin Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_delete_bin_endpoint
    RETURN    ${result}

Get Delete Bin Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_delete_bin_params
    RETURN    ${result}

The User executes the Delete Bin API Request
    [Arguments]     ${BASE_URL}    ${BIN_ID}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}

     #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create Delete Bin Instance    ${base_url}      ${BIN_ID}     #Create an instance of   Delete Bin

    ${endpoint}=    Get Delete Bin Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Delete Bin Parameters   ${instance}  #intialize the parameters
    Log Many    ${params}


    ${method}=          Set Variable   DELETE
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     params=${params}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance


The Delete Bin controller returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'Upload' REST API call failed, the returned status is '${status_code}'


The expected Error must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     "${api_error_message_detail}"     "${EXPECTED_ERROR_MESSAGE}"
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       binId

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             ${error_msg_two_verification}=    Set Variable If  "${EXPECTED_ERROR_MESSAGE}" in "${api_error_message_fields}"     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Reject' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.


Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}



#Keywords to read error response fields
Get Error details data
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}


Get Response Status Code
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}


Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}