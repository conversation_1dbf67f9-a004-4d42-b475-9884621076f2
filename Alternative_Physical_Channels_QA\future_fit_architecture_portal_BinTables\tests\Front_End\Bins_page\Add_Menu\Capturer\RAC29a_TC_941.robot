*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify that the Add a Bin Button is Disabled for empty fields
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_NAME}' == '${EMPTY}' or '${BIN_NAME}' == ''
         ${random_word}=     Generate random bin name
         ${BIN_NAME}=   Set Variable     ${random_word}
         ${BIN_NAME}=    Get Substring    ${BIN_NAME}    0    12
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The user allows the Bin Details to be empty    ${BIN_NAME}    ${BIN_TYPE_NAME}     ${BIN_ACTION_DATE}
    Then The user verifies that the 'Add Bin' Button is disabled 

| *** Test Cases ***                                                                                                               |        *DOCUMENTATION*       |         *BASE_URL*                  |         *BIN_NAME*          |         *BIN_TYPE_NAME*        |         *BIN_ACTION_DATE*        |
| Capturer_Add Bin Button Disabled for Empty or Invalid Fields   | Verify that the Add a Bin Button is Disabled for empty fields   | Testing the Add Bin Button   |           ${EMPTY}                  |            ${EMPTY}         |         Domestic               |         06-28-2027               |