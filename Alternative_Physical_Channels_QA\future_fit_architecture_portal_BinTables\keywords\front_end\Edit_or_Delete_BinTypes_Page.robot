*** Settings ***
#Author Name               : <PERSON>hab<PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/front_end/Add_BinTypes_Page.robot


*** Variables ***
${EDIT_DELETE_BIN_TYPE_BTN}                       xpath=//span[contains(text(),'Edit / Delete')]
${BIN_TYPE_NAME_INPUT}                            xpath=//input[contains(@class, 'mat-input-element') and contains(@placeholder, 'Enter Bintype Name')]
${BIN_TYPE_DESC_INPUT}                            xpath=//input[contains(@class, 'mat-input-element') and contains(@placeholder, 'Start typing..')]
${SAVE_EDITED_BIN_TYPE_BTN}                       xpath=//button[contains(@class, 'mat-stroked-button') and contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-button-base')]
${SAVE_BIN_DIALOG}                                xpath=//mat-dialog-content[contains(@class, 'mat-dialog-content') and contains(@class, 'mat-typography')]
${SAVE_BIN_TYPE_TO_DB_BTN}                        xpath=//button[contains(@class, 'btn-save') and contains(@class, 'mat-button-base')]
${CANCEL_SAVE_BIN_TYPE_TO_DB_BTN}                 xpath=//button[contains(@class, 'btn-close') and contains(@class, 'mat-button-base')]
${VIEW_BIN_TYPES_BTN}                             xpath=//span[contains(text(),'View')]
${BIN_TYPES_TABLE}                                xpath=//table[contains(@class, 'mat-table') and contains(@class, 'cdk-table mat-sort')]
${PAGINATOR_RANGE_LABEL}                          xpath=//div[@class='mat-paginator-range-label']
${PAGINATOR_ICON}                                 xpath=//button[contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-paginator-navigation-next') and contains(@class, 'mat-icon-button')]
${FILTER_LIST}                                    xpath=//mat-select[@role='combobox']
${FILTER_LIST_5_ITEMS}                            xpath=//span[contains(text(),' 5 ')]
${FILTER_LIST_10_ITEMS}                           xpath=//span[contains(text(),' 10 ')]
${FILTER_LIST_25_ITEMS}                           xpath=//span[contains(text(),' 25 ')]
${FILTER_LIST_100_ITEMS}                          xpath=//span[contains(text(),' 100 ')]
${CANCEL_DELETE_BIN_TYPE_BTN}                     xpath=//button[contains(@class, 'valid-button') and contains(@class, 'mat-button-base')]/span[contains(text(),'Okay')]
${80_PERCENT_ZOOM_LEVEL}              0.8  # Set zoom level (1.0 = 100%, 0.8 = 80%, 1.2 = 120%)
${100_PERCENT_ZOOM_LEVEL}             1.0  # Set zoom level (1.0 = 100%, 0.8 = 80%, 1.2 = 120%)


*** Keywords ***
The user navigates to 'Edit/Delete' Bin Type tab

    ${add_bin_type_btn_displayed}=     Wait for Element to be enabled    ${EDIT_DELETE_BIN_TYPE_BTN}

    Run Keyword If    not ${add_bin_type_btn_displayed}
    ...    Fail   There 'Edit/Delete' button is not displayed on the Bin Types page.

     #Click on the bin type to Verify
    SeleniumLibrary.Click Element    ${EDIT_DELETE_BIN_TYPE_BTN}
    Sleep    4s
    Execute JavaScript    document.body.style.zoom = ${80_PERCENT_ZOOM_LEVEL}
    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    BINTYPES
    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END


The bin types displayed on the page must exist in the database
    #Verify that the current page is BINTYPES
    ${correct_page_displayed}=      Correct Page is displayed    BINTYPES

    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Get the total number of actibe Bin Types from the database
    ${total_bin_types_db_results}=     Get the count on active Bin Type from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${total_bin_types_db_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Log    There are no active Bin Types on the database.

    ${first_row_results}=             Get From List    ${total_bin_types_db_results}    0    # Get the first row
    ${DB_TOTAL_ACTIVE_BINS}=       Get Column Data By Name       ${first_row_results}       total


    #Get the active Bin Types details from the database
    ${db_bin_types_details_results}=     Get all the active Bin Types from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_types_details_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no active Bin Types on the database..

    IF    not ${db_results_contain_data}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${bin_type_table_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPES_TABLE}
    ${number_of_elements_found}=    Get Length     ${bin_type_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The Bin Types table is not displayed on the 'Edit/Delete' Bin Type page.

    #Filter the table to show 100 items
     ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            SeleniumLibrary.Click Element    ${FILTER_LIST}
            Sleep    3s
        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bin Types total displayed on the Front End is the same
    #as active Bin Types on the DB.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        IF  '${DB_TOTAL_ACTIVE_BINS}' == '0'
                Run Keyword And Warn On Failure    Fail    There are no Bin Types displayed on the page because the DB has not active bin types saved..
            ELSE
                Run Keyword And Continue On Failure    Fail  There are no Bin Types displayed on the page, but the DB has '${DB_TOTAL_ACTIVE_BINS}' active Bin Types.
        END

        Capture Page Screenshot   Bin_Types_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${bins_displayed_on_page_equal_db_total}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    ${DB_TOTAL_ACTIVE_BINS}

    Run Keyword If    not ${bins_displayed_on_page_equal_db_total}
    ...    Run Keyword And Continue On Failure    Fail   The total number of Bin Types displayed on the page is not the same as the DB Bins for the same Bin Type. The DB total is '${DB_TOTAL_ACTIVE_BINS}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
    ...  ELSE
    ...    Log Many    he total number of Bin Types displayed on the page is the same as the DB active Bin Types. The DB total is '${DB_TOTAL_ACTIVE_BINS}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.


     ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   EDIT_BinType_Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed on the 'Edit/Detele Bin Type page!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    ${record_counter}=    Set Variable  0
    WHILE    '${disabled_attribute}' == 'False' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${bin_types__table_rows_element_text}=       Catenate  ${BIN_TYPES_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${bin_types__table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END


        ${bin_types_table_rows_total}=           SeleniumLibrary.Get Element Count    ${bin_types__table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${bin_types_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_type_name}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_type_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[2]
           ${bin_type_description}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
           ${curr_row_bin_type_captured_date_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[3]
           ${bin_type_captured_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_captured_date_ele}
           ${date1_normalized}=        Convert Date    ${bin_type_captured_date.strip()}    date_format=%b %d, %Y
           ${curr_row_bin_type_captured_by_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[4]
           ${bin_type_captured_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_captured_by_ele}

           ${db_row_data}=     Get Row By Column Value From DB results     ${db_bin_types_details_results}     'Name'      ${bin_type_name}

           ${bin_type_is_saved_in_the_db}=     Run Keyword And Return Status       Should Not Be Equal As Strings    None    ${db_row_data}

           Run Keyword If    not ${bin_type_is_saved_in_the_db}
           ...    Run Keyword And Continue On Failure    Fail   The active Bin Type: '${bin_type_name}' was not found in the database.

           Log Many    ${db_row_data}

           IF    ${bin_type_is_saved_in_the_db}
                ${db_bin_type_name}=    Get Column Data By Name       ${db_row_data}       Name
                ${db_bin_type_desc}=    Get Column Data By Name       ${db_row_data}       Description
                ${db_bin_type_captured_by}=    Get Column Data By Name       ${db_row_data}       CreatedBy
                ${db_bin_type_captured_date}=    Get Column Data By Name       ${db_row_data}       CreatedDate
                ${db_bin_type_captured_date_string}=       Convert To String    ${db_bin_type_captured_date}
                ${db_bin_type_captured_date_array}=        Split String    ${db_bin_type_captured_date_string}      separator=${SPACE}
                ${db_bin_type_captured_string}=     Set Variable    ${db_bin_type_captured_date_array}[0]
                ${db_bin_type_captured_string}=     Set Variable     ${db_bin_type_captured_string.strip()}

                ${date2_normalized}=      Convert Date    ${db_bin_type_captured_string}    date_format=%Y-%m-%d
                ${db_bin_type_isDeleted}=    Get Column Data By Name       ${db_row_data}       IsDeleted

                Run Keyword And Continue On Failure     Should Be Equal As Strings    0    ${db_bin_type_isDeleted}                                    msg=The Bin Type is deleted in the DB but it is displayed on the front end!     strip_spaces=True
                Run Keyword And Continue On Failure     Should Be Equal As Strings    ${bin_type_name}    ${db_bin_type_name}                          msg=Bin Type name displayed on the front end is not the same as the name saved in the DB!     strip_spaces=True
                Run Keyword And Continue On Failure     Should Be Equal As Strings    ${bin_type_description}    ${db_bin_type_desc}                   msg=Bin Type description displayed on the front end is not the same as the description saved in the DB!     strip_spaces=True
                Run Keyword And Continue On Failure     Should Be Equal As Strings    ${bin_type_captured_by}    ${db_bin_type_captured_by}            msg=Bin Type Captured By displayed on the front end is not the same as the data saved in the DB!     strip_spaces=True
                Run Keyword And Continue On Failure     Should Be Equal As Strings    ${date1_normalized}    ${date2_normalized}                       msg=Bin Type Captured date displayed on the front end is not the same as the date saved in the DB!     strip_spaces=True
           END

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled

        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'



        IF    '${next_page_disabled_status}' == 'true' and '${record_counter}' == '${total_number_of_items_on_page}'

                ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
                Log Many  ${paginator_range_data}
                ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
                ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
                ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
                ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
                Log Many    ${total_number_of_items_on_current_page}
                Log Many    ${total_number_of_items_on_page}

                Run Keyword If    '${total_number_of_items_on_current_page}' == '${DB_TOTAL_ACTIVE_BINS}'
                ...    Log Many     All ${DB_TOTAL_ACTIVE_BINS} Bin Types have been verified successfully against the database.
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  Not all Bin Types have been verified against the DB. The total number of active Bin Types in the DB is '${DB_TOTAL_ACTIVE_BINS}', but the bins that are displayed on the View Linked Bins page is '${total_number_of_items_on_current_page}'.

                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN

             ELSE
                IF    '${next_page_disabled_status}' == 'None'
                         #Click on the 'Next' button
                         SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                         Sleep    4s
                END

        END
    END

    #${view_button_is_displayed}=     Run Keyword And Return Status     GenericMethods.Run Keyword Until Success      Wait Until Element Is Enabled    ${VIEW_BIN_TYPES_BTN}
    ${view_button_is_displayed}=     Wait for Element to be enabled    ${VIEW_BIN_TYPES_BTN}

    #Navigate to the Bin Types View Menu
    IF   ${view_button_is_displayed}
         Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
         SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
         Sleep    4s
    END

The user edits a Bin Type
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}     ${NEW_BIN_TYPE_NAME}     ${NEW_BIN_TYPE_DESC}       ${TEST_TYPE}

     #This is the boolean value that checks if the bin type name is provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    IF  ${bin_type_name_provided} == ${False}
         ${BIN_TYPE_NAME}=      Set Variable     ${CREATED_BIN_TYPE_NAME}
    END

        Run Keyword If    ${bin_type_name_provided} == ${False}
    ...    Fail  Please provide the name of the Bin Type that must be edited!

    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_NAME}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}       msg=The Bin Type named '${BIN_TYPE_NAME}' does not exist in the database, hence it cannot be edited

     Run Keyword If    not ${db_results_contain_data}
    ...  Fail  There is no bin type callled '${BIN_TYPE_NAME}' in the database.

    IF    ${db_results_contain_data}
        ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
        ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
        ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
        ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

        Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
        ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is active.
        ...  ELSE
        ...    Run Keyword And Continue On Failure  Fail  The bin type name: '${db_bin_type_name}' from the DB is not active.

    END


    #Verify that the current page is BINTYPES
    ${correct_page_displayed}=      Correct Page is displayed    BINTYPES

    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Get the total number of actibe Bin Types from the database
    ${total_bin_types_db_results}=     Get the count on active Bin Type from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${total_bin_types_db_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Log    There are no active Bin Types on the database.

    ${first_row_results}=             Get From List    ${total_bin_types_db_results}    0    # Get the first row
    ${DB_TOTAL_ACTIVE_BINS}=       Get Column Data By Name       ${first_row_results}       total


    #Get the active Bin Types details from the database
    ${db_bin_types_details_results}=     Get all the active Bin Types from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_types_details_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Continue On Failure    Fail    There are no active Bin Types on the database..

    IF    not ${db_results_contain_data}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${bin_type_table_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPES_TABLE}
    ${number_of_elements_found}=    Get Length     ${bin_type_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The Bin Types table is not displayed on the 'Edit/Delete' Bin Type page.

    #Filter the table to show 100 items
     ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            SeleniumLibrary.Click Element    ${FILTER_LIST}
            Sleep    3s
        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bin Types total displayed on the Front End is the same
    #as active Bin Types on the DB.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        IF  '${DB_TOTAL_ACTIVE_BINS}' == '0'
                Log Many    There are no Bin Types displayed on the page because the DB has not active bin types saved..
            ELSE
                Run Keyword And Continue On Failure    Fail  There are no Bin Types displayed on the page, but the DB has '${DB_TOTAL_ACTIVE_BINS}' active Bin Types.
        END

        Capture Page Screenshot   Bin_Types_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${bins_displayed_on_page_equal_db_total}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    ${DB_TOTAL_ACTIVE_BINS}

    Run Keyword If    not ${bins_displayed_on_page_equal_db_total}
    ...    Run Keyword And Continue On Failure    Fail   The total number of Bin Types displayed on the page is not the same as the DB Bins for the same Bin Type. The DB total is '${DB_TOTAL_ACTIVE_BINS}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
    ...  ELSE
    ...    Log Many    he total number of Bin Types displayed on the page is the same as the DB active Bin Types. The DB total is '${DB_TOTAL_ACTIVE_BINS}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.


     ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   EDIT_BinType_Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed on the 'Edit/Detele Bin Type page!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    ${record_counter}=    Set Variable  0
    ${bin_type_found_on_page}=      Set Variable  ${False}
    WHILE    '${disabled_attribute}' == 'False' or '${bin_type_found_on_page}' == '${False}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${bin_types__table_rows_element_text}=       Catenate  ${BIN_TYPES_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${bin_types__table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END

        ${bin_types_table_rows_total}=           SeleniumLibrary.Get Element Count    ${bin_types__table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${bin_types_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[1]
           ${fe_bin_type_name}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_type_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[2]
           ${fe_bin_type_description}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
           ${curr_row_bin_type_captured_date_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[3]
           ${fe_bin_type_captured_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_captured_date_ele}
           ${date1_normalized}=        Convert Date    ${fe_bin_type_captured_date.strip()}    date_format=%b %d, %Y
           ${curr_row_bin_type_captured_by_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[4]
           ${fe_bin_type_captured_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_captured_by_ele}

           IF    '${fe_bin_type_name}' == '${BIN_TYPE_NAME}'
                IF    ${db_bin_type_name_is_deleted_boolean}
                    Fail  The bin type name: '${db_bin_type_name}' is not active in the database but it is displayed on the Front End.
                ELSE
                    ${bin_type_found_on_page}=      Set Variable  ${True}
                    ${curr_row_bin_edit_ele}=       Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[5]/button[1]
                    #Select and edit The Bin Type
                    SeleniumLibrary.Click Element    ${curr_row_bin_edit_ele}
                    Edit the Bin Type       ${NEW_BIN_TYPE_NAME}    ${NEW_BIN_TYPE_DESC}        ${TEST_TYPE}
                END

                BREAK
           END

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        IF    ${bin_type_found_on_page}
             Log Many      The Bin Type: '${BIN_TYPE_NAME}' has been edited successfully
             BREAK
        END

        ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
        ${attr}=                    Get Current Element Attributes    ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
        Log Many    next_page_disabled_status:   '${disabled_attribute}'

        IF    '${disabled_attribute}' == 'true'

                Fail    The Bin Type: '${BIN_TYPE_NAME}' was not found on the page, hence it could not be edited!
                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN
             ELSE
                #Click on the 'Next' button
                 SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                 Sleep    4s
        END
    END

    IF    '${TEST_TYPE}' == 'POSITIVE'
        ${view_button_is_displayed}=     Wait for Element to be enabled    ${VIEW_BIN_TYPES_BTN}

        #Navigate to the Bin Types View Menu
        IF   ${view_button_is_displayed}
             Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
             SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
             Sleep    4s
        END
    END



The user deletes a Bin Type
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

     #This is the boolean value that checks if the bin type name is provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    IF  ${bin_type_name_provided} == ${False}
         ${BIN_TYPE_NAME}=      Set Variable     ${CREATED_BIN_TYPE_NAME}
    END

    Run Keyword If    ${bin_type_name_provided} == ${False}
    ...    Fail  Please provide the name of the Bin Type that must be deleted!



    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_NAME}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}       msg=The Bin Type named '${BIN_TYPE_NAME}' does not exist in the database, hence it cannot be edited

     Run Keyword If    not ${db_results_contain_data}
    ...  Fail  There is no bin type callled '${BIN_TYPE_NAME}' in the database.

    IF    ${db_results_contain_data}
        ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
        ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
        ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
        ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

        Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
        ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is active.
        ...  ELSE
        ...    Run Keyword And Continue On Failure  Fail  The bin type name: '${db_bin_type_name}' from the DB is not active.

    END


    #Verify that the current page is BINTYPES
    ${correct_page_displayed}=      Correct Page is displayed    BINTYPES

    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Get the total number of actibe Bin Types from the database
    ${total_bin_types_db_results}=     Get the count on active Bin Type from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${total_bin_types_db_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Log    There are no active Bin Types on the database.

    ${first_row_results}=             Get From List    ${total_bin_types_db_results}    0    # Get the first row
    ${DB_TOTAL_ACTIVE_BINS}=       Get Column Data By Name       ${first_row_results}       total


    #Get the active Bin Types details from the database
    ${db_bin_types_details_results}=     Get all the active Bin Types from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_types_details_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Continue On Failure    Fail    There are no active Bin Types on the database..

    IF    not ${db_results_contain_data}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${bin_type_table_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPES_TABLE}
    ${number_of_elements_found}=    Get Length     ${bin_type_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The Bin Types table is not displayed on the 'Edit/Delete' Bin Type page.

    #Filter the table to show 100 items
     ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            SeleniumLibrary.Click Element    ${FILTER_LIST}
            Sleep    3s
        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    Scroll Up The Page

    #Verify that the Bin Types total displayed on the Front End is the same
    #as active Bin Types on the DB.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        IF  '${DB_TOTAL_ACTIVE_BINS}' == '0'
                Log Many    There are no Bin Types displayed on the page because the DB has not active bin types saved..
            ELSE
                Run Keyword And Continue On Failure    Fail  There are no Bin Types displayed on the page, but the DB has '${DB_TOTAL_ACTIVE_BINS}' active Bin Types.
        END

        Capture Page Screenshot   Bin_Types_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${bins_displayed_on_page_equal_db_total}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    ${DB_TOTAL_ACTIVE_BINS}

    Run Keyword If    not ${bins_displayed_on_page_equal_db_total}
    ...    Run Keyword And Continue On Failure    Fail   The total number of Bin Types displayed on the page is not the same as the DB Bins for the same Bin Type. The DB total is '${DB_TOTAL_ACTIVE_BINS}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
    ...  ELSE
    ...    Log Many    he total number of Bin Types displayed on the page is the same as the DB active Bin Types. The DB total is '${DB_TOTAL_ACTIVE_BINS}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.


     ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   EDIT_BinType_Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed on the 'Edit/Detele Bin Type page!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    ${record_counter}=    Set Variable  0
    ${bin_type_found_on_page}=      Set Variable  ${False}
    WHILE    '${disabled_attribute}' == 'False' or '${bin_type_found_on_page}' == '${False}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${bin_types__table_rows_element_text}=       Catenate  ${BIN_TYPES_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${bin_types__table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END

        ${bin_types_table_rows_total}=           SeleniumLibrary.Get Element Count    ${bin_types__table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${bin_types_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[1]
           ${fe_bin_type_name}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_type_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[2]
           ${fe_bin_type_description}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
           ${curr_row_bin_type_captured_date_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[3]
           ${fe_bin_type_captured_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_captured_date_ele}
           ${date1_normalized}=        Convert Date    ${fe_bin_type_captured_date.strip()}    date_format=%b %d, %Y
           ${curr_row_bin_type_captured_by_ele}=           Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[4]
           ${fe_bin_type_captured_by}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_captured_by_ele}

           IF    '${fe_bin_type_name}' == '${BIN_TYPE_NAME}'
                IF    ${db_bin_type_name_is_deleted_boolean}
                    Fail  The bin type name: '${db_bin_type_name}' is not active in the database bu it is displayed on the Front End.
                ELSE
                    ${bin_type_found_on_page}=      Set Variable  ${True}
                    ${curr_row_bin_delete_ele}=       Catenate  ${BIN_TYPES_TABLE}/tbody/tr[${index}]/td[5]/button[2]
                    #Select and edit The Bin Type
                    SeleniumLibrary.Click Element    ${curr_row_bin_delete_ele}


                    #Verify that the delete bin  dialog is displayed
                    Capture Page Screenshot   ${BIN_TYPE_NAME}_delete_confirmation.png
                    ${add_bin_type_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/p
                    ${add_bin_type_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_confirmation_msg_xpath}

                    Run Keyword If    '${add_bin_type_confirmation_msg.strip()}' == 'You are about to DELETE BIN Type:'
                    ...    Log Many  The 'Delete Bin Type' confirmation message is displayed.
                    ...  ELSE
                    ...    Fail  The 'Delete Bin Type' confirmation message is not displayed.


                    ${add_bin_type_name_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/ul/li
                    ${add_bin_type_name_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_name_confirmation_msg_xpath}

                    Run Keyword If    '${add_bin_type_name_confirmation_msg}' == '${BIN_TYPE_NAME}'
                    ...    Log Many  The bin type name: '${BIN_TYPE_NAME}' is displayed correctly on the dialog.
                    ...  ELSE
                    ...    Fail  The bin type name: '${BIN_TYPE_NAME}' is not displayed correctly on the dialog.

                    Capture Page Screenshot   Delete_${BIN_TYPE_NAME}_saved_to_db.png

                    #Save the Bin Type to Database
                    SeleniumLibrary.Click Element    ${SAVE_BIN_TYPE_TO_DB_BTN}
                    Capture Page Screenshot   Delete_${BIN_TYPE_NAME}_saved_to_db_2.png

                END

                BREAK
           END

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        IF    ${bin_type_found_on_page}
             Log Many      The Bin Type: '${BIN_TYPE_NAME}' has been deleted successfully.
             BREAK
        END

        ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
        ${attr}=                    Get Current Element Attributes    ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
        Log Many    next_page_disabled_status:   '${disabled_attribute}'

        IF    '${disabled_attribute}' == 'true'

                Fail    The Bin Type: '${BIN_TYPE_NAME}' was not found on the page, hence it could not be deleted!
                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN
             ELSE
                #Click on the 'Next' button
                 SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                 Sleep    4s
        END
    END






Edit the Bin Type
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}     ${TEST_TYPE}

    ${correct_page_displayed}=      Correct Page is displayed    EDIT BINTYPE

    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be edited!


    Capture Page Screenshot   ${BIN_TYPE_NAME}_details_not_populated.png


    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME_INPUT}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Fail   The   BIN_TYPE_NAME_INPUT is not displayed on the 'Add Bin Type' page.

    #Verify that the 'Edit Bin Type' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_EDITED_BIN_TYPE_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Edit Bin Type' button is disabled before the bin type is edited.
    ...  ELSE
    ...    Fail  The 'Edit Bin Type' button on the Edit Bin Type page is not disabled as expected.

    #Populate the Bin Type details
    SeleniumLibrary.Input Text    ${BIN_TYPE_NAME_INPUT}    ${BIN_TYPE_NAME}


    Capture Page Screenshot   Edit_${BIN_TYPE_NAME}_details_populated.png


    IF    '${TEST_TYPE}' == 'POSITIVE'
        Sleep    4s
        SeleniumLibrary.Input Text    ${BIN_TYPE_DESC_INPUT}    ${BIN_TYPE_DESCRIPTION}
        Press Key    ${BIN_TYPE_DESC_INPUT}    \\9    # Simulate pressing TAB after Bin Type description input
        Sleep    4s
        Capture Page Screenshot   Edit_${BIN_TYPE_NAME}_populated_.png
        #Verify that the 'Edit Bin Type' button is disabled
        ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_EDITED_BIN_TYPE_BTN}
        ${attr}=        Get Current Element Attributes     ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

        Log Many    attr:   ${disabled_attribute}

        Run Keyword If    '${disabled_attribute}' == 'False'
        ...    Log Many  The 'Edit Bin Type' button is enabled after the bin type is edited.
        ...  ELSE
        ...    Fail  The 'Edit Bin Type' button on the Edit Bin Type page is not enabled as expected, this is after the Bin Type has been edited.

        #Click to add the Bin Type
        SeleniumLibrary.Click Element  ${SAVE_EDITED_BIN_TYPE_BTN}

        #Verify that the save bin  dialog is displayed
        Capture Page Screenshot   ${BIN_TYPE_NAME}_Confirmation_details_populated.png
        ${add_bin_type_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/p
        ${add_bin_type_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_confirmation_msg_xpath}

        Run Keyword If    '${add_bin_type_confirmation_msg.strip()}' == 'You have edited BINTYPE:'
        ...    Log Many  The 'Edit Bin Type' confirmation message is displayed.
        ...  ELSE
        ...    Fail  The 'Edit Bin Type' confirmation message is not displayed.


        ${add_bin_type_name_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/ul/li
        ${add_bin_type_name_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_name_confirmation_msg_xpath}

        Run Keyword If    '${add_bin_type_name_confirmation_msg}' == '${BIN_TYPE_NAME}'
        ...    Log Many  The bin type name: '${BIN_TYPE_NAME}' is displayed correctly on the dialog.
        ...  ELSE
        ...    Fail  The bin type name: '${BIN_TYPE_NAME}' is not displayed correctly on the dialog.

        #Save the Bin Type to Database
        SeleniumLibrary.Click Element    ${SAVE_BIN_TYPE_TO_DB_BTN}
    END

    Capture Page Screenshot   Edit_${BIN_TYPE_NAME}_saved_to_db.png



The deleted bin must be de-activated on the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    ${bin_type_name_provided} == ${False}
    ...    Fail  Please provide the name of the Bin Type that must be deleted!


    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_TO_ADD}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no bin type callled '${BIN_TYPE_TO_ADD}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    #Verify that the Bin Type details are correct
    Run Keyword If    '${db_bin_type_name}' == '${BIN_TYPE_NAME}'
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is the same as the provided Bin Type name.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The bin type name: '${db_bin_type_name}' from the DB is not the same as the provided Bin Type. The provided Bin Type name is '${BIN_TYPE_NAME}'.


    Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
    ...    Fail  The bin type name: '${db_bin_type_name}' is still active in the database.
    ...  ELSE
    ...    Log Many  The bin type name: '${db_bin_type_name}' is not active in the database. The soft-delete was successful.


The edited Bin Type must exist in the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    The Created Bin Type Should Exist In The Database     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}


The error message must be displayed
    Capture Page Screenshot   _details_populated.png
    Sleep    3s
    #Save the Bin Type to Database
    SeleniumLibrary.Click Element    ${BIN_TYPE_DESC_INPUT}
   #Verify that the error message is displayed and the 'Add Bin Type' button is not enabled
   ${error_msg_is_displayed}=    Run Keyword And Return Status   SeleniumLibrary.Element Should Be Visible   ${DUPLICATE_ERR_MSG}

   IF    ${error_msg_is_displayed}
       Log Many  The error message is displayed.
       ${error_message_text}=       SeleniumLibrary.Get Text    ${DUPLICATE_ERR_MSG}
       Should Be Equal As Strings    Duplicate Found!    ${error_message_text}      msg=The expected error message, which is 'Duplicate Found!', was not displayed.        strip_spaces=True

       #Verify that the 'Add Bin Type' button is disabled
        ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_TYPE_BTN}
        ${attr}=        Get Current Element Attributes     ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

        Log Many    attr:   ${disabled_attribute}

        Run Keyword If    '${disabled_attribute}' == 'True'
        ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type name populated is a lready existing in the database.
        ...  ELSE
        ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details populated exist in the database.

   ELSE
      Fail  The error message is not displayed.
   END




The user reads and stores the Bin Type details from the DB prior to editing that bin type
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    ${bin_type_name_provided} == ${False}
    ...    Fail  Please provide the name of the Bin Type that must be deleted!


    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_TO_ADD}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no bin type callled '${BIN_TYPE_TO_ADD}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_description}=                 Get Column Data By Name       ${first_row_results}       Description
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    Set Global Variable    ${DATABASE_BIN_TYPE_NAME}               ${db_bin_type_name}
    Set Global Variable    ${DATABASE_BIN_TYPE_DESCRIPTION}        ${db_bin_type_description}


The invalid changes to an existing Bin Type must not be saved to the database

    The Created Bin Type Should Exist In The Database     ${DATABASE_BIN_TYPE_NAME}    ${DATABASE_BIN_TYPE_DESCRIPTION}


Get the random Bin Type to delete from the DB

    ${db_bins_count}=   Set Variable    1
    ${db_bin_type_count_details}=         Get the count on active Bin Type from the Database
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_count_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    The are no active bin types found in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_count_details}    0    # Get the first row
    ${db_bin_type_count}=                        Get Column Data By Name       ${first_row_results}       total
    ${list_of_bin_types_already_read}=      Create List
    ${list_length}=     Get Length   ${list_of_bin_types_already_read}
    WHILE    '${list_length}' != '${db_bin_type_count}'

        ${db_bin_type_details}=         Get the random Bin Type details from the Database
        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

         Run Keyword If    not ${db_results_contain_data}
        ...     Fail    The random bin type was not found in the database.

        ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
        ${db_bin_type_id}=                        Get Column Data By Name       ${first_row_results}       Id
        ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
        ${db_bin_type_description}=                 Get Column Data By Name       ${first_row_results}       Description
        ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
        ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

        ${db_bin_type_details}=         Get the count of all Bins Linked to a Bin Type      ${db_bin_type_id}
        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}
        ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
        ${db_bins_count}=                        Get Column Data By Name       ${first_row_results}       Total_linked_bins

        IF    '${db_bins_count}' != '0'
           Append To List      ${list_of_bin_types_already_read}       ${db_bin_type_name}
           ${list_length}=     Get Length   ${list_of_bin_types_already_read}
        ELSE
            Exit For Loop
        END


    END

    #If the bin type was found
    IF    '${db_bins_count}' == '0'
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_NAME}               ${db_bin_type_name}
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_DESCRIPTION}        ${db_bin_type_description}
    ELSE
        Fail    There is no Bin Type that can be deleted, all bin types are linked to Bins.
    END



The expected error message must be displayed
    [Arguments]     ${BIN_TYPE_NAME_TEXT}
    Capture Page Screenshot   _details_populated.png
    Sleep    3s

   #Verify that the error message is displayed
   ${error_msg_is_displayed}=    Run Keyword And Return Status   SeleniumLibrary.Element Should Be Visible   ${SAVE_BIN_DIALOG}

   IF    ${error_msg_is_displayed}
       Log Many  The error message is displayed.
       ${error_message_text}=       SeleniumLibrary.Get Text    ${SAVE_BIN_DIALOG}/ul/li
       ${expected_error_text}=      Set Variable    You cannot delete ${BIN_TYPE_NAME_TEXT} as there are BIN Numbers linked to it.

        Run Keyword If    '${error_message_text.strip()}' == '${expected_error_text}'
        ...    Log Many  The error message: '${expected_error_text}', was displayed on the page.
        ...  ELSE
        ...    Run Keyword And Continue on Failure      Fail  The error message: '${expected_error_text}', was not displayed on the page.

        #Click the 'Okay' button
        SeleniumLibrary.Click Element  ${CANCEL_DELETE_BIN_TYPE_BTN}
   ELSE
        Fail  The error message is not displayed.
   END


