*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for getting all ATM Marketing Campaigns using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
GET APC marketing campaigns
    [Arguments]        ${DOCUMENTATION}    ${DATA_FILE}  ${BASE_URL}     ${SERVICE_PATH}   ${SERVICE_PATH_ID}   ${EXPECTED_STATUS_CODE}     ${JSON_RESPONSE_REASON}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user creates a rest session                                ${BASE_URL}
    When The user makes Get Rest Call                                    ${SERVICE_PATH}     ${SERVICE_PATH_ID}     ${DATA_FILE}     ${EXPECTED_STATUS_CODE}
    And The service returns http status                                  ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The field value(s) returned by the GET ATMMarketingCampaign controller must correspond to the APC Database

| *** Test Cases ***                                                                                                                                                        |               *DOCUMENTATION*                    |   *DATA_FILE*   | *BASE_URL*                    | *SERVICE_PATH*              | *SERVICE_PATH_ID*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON* |
| FFT - Controllers - Get All APC Marketing Campaigns and verify the returned campaigns against the DataBase      | GET APC marketing campaigns                      |             Verify APC Campaigns using the DB    |                 | APC_API_UAT_BASE_URL          | ATMMarketingCampaign        |                                      | 200                              | OK                     |

