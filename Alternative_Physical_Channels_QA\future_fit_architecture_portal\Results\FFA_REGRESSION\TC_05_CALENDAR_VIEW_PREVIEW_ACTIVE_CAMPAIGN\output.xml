<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="20241017 10:15:47.954" rpa="false" schemaversion="4">
<suite id="s1" name="Future Fit Portal" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Controllers\ATMMarketingCampaign\TC_08_PUT_EDIT_MarketingCampaignController.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241017 10:15:48.743" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241017 10:15:48.743" endtime="20241017 10:15:48.743"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\EDIT_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<status status="PASS" starttime="20241017 10:15:48.743" endtime="20241017 10:15:48.759"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Change all editable fields of the Campaign" line="55">
<kw name="Edit UnTargeted Campaign">
<arg>Change the Campaign Name</arg>
<arg>future_fit_architecture_portal/data/Campaign_EDIT.json</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMMarketingCampaign</arg>
<arg>15027</arg>
<arg>Edited using Automated Script</arg>
<arg>ATM</arg>
<arg>2</arg>
<arg>8</arg>
<arg>images/MarketingA_en_7_sot.jpg</arg>
<arg>images/MarketingB_en_7_zul.jpg</arg>
<arg>English</arg>
<arg>Afrikaans</arg>
<arg>0</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>Campaign has been edited</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">Set test documentation to:
Change the Campaign Name</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>${CAMP_IMAGES}</var>
<arg>${CAMPAIGN_IMAGE1}</arg>
<arg>${CAMPAIGN_IMAGE_2}</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">${CAMP_IMAGES} = ['images/MarketingA_en_7_sot.jpg', 'images/MarketingB_en_7_zul.jpg']</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${list_string}</var>
<arg>','.join(${CAMP_IMAGES})</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">${list_string} = images/MarketingA_en_7_sot.jpg,images/MarketingB_en_7_zul.jpg</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAMP_IMAGES</arg>
<arg>${list_string}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">Environment variable 'CAMP_IMAGES' set to value 'images/MarketingA_en_7_sot.jpg,images/MarketingB_en_7_zul.jpg'.</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>${CAMP_LNGS}</var>
<arg>${CAMPAIGN_LANGUAGE_1}</arg>
<arg>${CAMPAIGN_LANGUAGE_2}</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">${CAMP_LNGS} = ['English', 'Afrikaans']</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${list_string}</var>
<arg>','.join(${CAMP_LNGS})</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">${list_string} = English,Afrikaans</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAMP_LNGS</arg>
<arg>${list_string}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241017 10:15:48.759" level="INFO">Environment variable 'CAMP_LNGS' set to value 'English,Afrikaans'.</msg>
<status status="PASS" starttime="20241017 10:15:48.759" endtime="20241017 10:15:48.759"/>
</kw>
<kw name="Given The user creates JSON request for Edit Campaign" library="RestCalls">
<arg>${CAMPAIGN_ID}</arg>
<arg>${CAMPAIGN_NAME}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>${CAMPAIGN_END_DATE}</arg>
<kw name="Get Campaign History details for the Campaign to be edited" library="PUTATMMarketingCampaign_DB_Verifications">
<var>${campaign_details}</var>
<arg>${CAMPAIGN_ID}</arg>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${CAMPAIGN_ID}</arg>
<msg timestamp="20241017 10:15:48.762" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for CAMPAIGN_ID, which is '${CAMPAIGN_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Get the untargted campaign history details from the database" library="PUTATMMarketingCampaign_DB_Verifications">
<var>${campaign_history_details}</var>
<arg>${CAMPAIGN_ID}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_ID}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Please make sure that values of all parameters are provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:48.762" level="INFO">${db_type} = 'MYSQL'</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_history_query}</var>
<arg>${SQL_GET_ATM_MARKETING_CAMPAIGN_HISTORY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:48.762" level="INFO">${campaign_history_query} = SELECT * FROM ATM_Marketing.CampaignHistory where campaignId=campaign_ID order by id desc</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Replace String" library="String">
<var>${campaign_history_query}</var>
<arg>${campaign_history_query}</arg>
<arg>campaign_ID</arg>
<arg>'${CAMPAIGN_ID}'</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="20241017 10:15:48.762" level="INFO">${campaign_history_query} = SELECT * FROM ATM_Marketing.CampaignHistory where campaignId='15027' order by id desc</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Execute SQL Query" library="DBUtility">
<var>${data_base_campaign_history_details}</var>
<arg>${db_type}</arg>
<arg>${campaign_history_query}</arg>
<arg>True</arg>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:48.762" level="INFO">${return_data} = True</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:48.762" level="INFO">${return_all} = False</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:48.762"/>
</kw>
<kw name="Verify Data Using Database" library="DatabaseUtility">
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<msg timestamp="20241017 10:15:49.313" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg timestamp="20241017 10:15:49.313" level="INFO">${data_found} = {'approvalId': 1, 'campaignId': 15027, 'id': 92, 'updateDescription': 'Create new campaign UAT_Cycle_3_exe -09005_Targted_Eng and Afr Thabo_Setuke_', 'updatedBy': 'Thabo Benjamin Setuke (ZA)', 'update...</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:49.313"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:49.313" endtime="20241017 10:15:49.313"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:49.328" endtime="20241017 10:15:49.328"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:49.328" endtime="20241017 10:15:49.328"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" starttime="20241017 10:15:49.328" endtime="20241017 10:15:49.328"/>
</return>
<msg timestamp="20241017 10:15:49.328" level="INFO">${data_base_campaign_history_details} = {'approvalId': 1, 'campaignId': 15027, 'id': 92, 'updateDescription': 'Create new campaign UAT_Cycle_3_exe -09005_Targted_Eng and Afr Thabo_Setuke_', 'updatedBy': 'Thabo Benjamin Setuke (ZA)', 'update...</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:49.328"/>
</kw>
<return>
<value>${data_base_campaign_history_details}</value>
<status status="PASS" starttime="20241017 10:15:49.328" endtime="20241017 10:15:49.328"/>
</return>
<msg timestamp="20241017 10:15:49.329" level="INFO">${campaign_history_details} = {'approvalId': 1, 'campaignId': 15027, 'id': 92, 'updateDescription': 'Create new campaign UAT_Cycle_3_exe -09005_Targted_Eng and Afr Thabo_Setuke_', 'updatedBy': 'Thabo Benjamin Setuke (ZA)', 'update...</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:49.329"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_history_id}</var>
<arg>${campaign_history_details}</arg>
<arg>id</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:49.329" level="INFO">${campaign_history_id} = 92</msg>
<status status="PASS" starttime="20241017 10:15:49.329" endtime="20241017 10:15:49.329"/>
</kw>
<kw name="Get the untargted campaign details based on the update history from the database" library="PUTATMMarketingCampaign_DB_Verifications">
<var>${campaign_details}</var>
<arg>${CAMPAIGN_ID}</arg>
<arg>${campaign_history_id}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_ID}' == '${EMPTY}' or '${CAMPAIGN_HISTORY_ID}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Please make sure that values of all parameters are provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:49.329" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:49.330" level="INFO">${db_type} = 'MYSQL'</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_targted_query}</var>
<arg>${SQL_GET_ATM_MARKETING_CAMPAIGN_DETAILS_BASED_ON_HISTORY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:49.330" level="INFO">${campaign_targted_query} = SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID,CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate,CH.updateDescription,CH.approvalI...</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Replace String" library="String">
<var>${campaign_targted_query}</var>
<arg>${campaign_targted_query}</arg>
<arg>campaign_ID</arg>
<arg>'${CAMPAIGN_ID}'</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="20241017 10:15:49.330" level="INFO">${campaign_targted_query} = SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID,CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate,CH.updateDescription,CH.approvalI...</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Replace String" library="String">
<var>${campaign_targted_query}</var>
<arg>${campaign_targted_query}</arg>
<arg>campaign_history_id</arg>
<arg>'${CAMPAIGN_HISTORY_ID}'</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="20241017 10:15:49.330" level="INFO">${campaign_targted_query} = SELECT C.id, C.campaignId,C.campaignName,C.campaignBy, CH.id as campaignHistoryID,CH.updatedBy as campaignHistoryUpdatedBy,CH.updatedDate as campaignHistoryUpdateDate,CH.updateDescription,CH.approvalI...</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Execute SQL Query" library="DBUtility">
<var>${data_base_campaign_targeted_campaign_details}</var>
<arg>${db_type}</arg>
<arg>${campaign_targted_query}</arg>
<arg>True</arg>
<arg>True</arg>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:49.330" level="INFO">${return_data} = True</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:49.330" level="INFO">${return_all} = True</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:49.330"/>
</kw>
<kw name="Verify Data Using Database" library="DatabaseUtility">
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<msg timestamp="20241017 10:15:51.426" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
2 is the total number of records returned by the query executed.
Returning all records....</msg>
<msg timestamp="20241017 10:15:51.428" level="INFO">${data_found} = [{'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHi...</msg>
<status status="PASS" starttime="20241017 10:15:49.332" endtime="20241017 10:15:51.428"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:51.428" endtime="20241017 10:15:51.428"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:51.429" endtime="20241017 10:15:51.429"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:51.429" endtime="20241017 10:15:51.429"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" starttime="20241017 10:15:51.429" endtime="20241017 10:15:51.429"/>
</return>
<msg timestamp="20241017 10:15:51.429" level="INFO">${data_base_campaign_targeted_campaign_details} = [{'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHi...</msg>
<status status="PASS" starttime="20241017 10:15:49.330" endtime="20241017 10:15:51.429"/>
</kw>
<return>
<value>${data_base_campaign_targeted_campaign_details}</value>
<status status="PASS" starttime="20241017 10:15:51.429" endtime="20241017 10:15:51.429"/>
</return>
<msg timestamp="20241017 10:15:51.431" level="INFO">${campaign_details} = [{'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHi...</msg>
<status status="PASS" starttime="20241017 10:15:49.329" endtime="20241017 10:15:51.431"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Not be Empty</arg>
<arg>'${campaign_details}'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Not Be Empty" library="BuiltIn">
<arg>'${campaign_details}'</arg>
<doc>Verifies that the given item is not empty.</doc>
<msg timestamp="20241017 10:15:51.431" level="INFO">Length is 339913</msg>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</kw>
<msg timestamp="20241017 10:15:51.431" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The campaign with the ID: ${CAMPAIGN_ID}, does not exist on the DB. Hence it could not be edited.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</kw>
<return>
<value>${campaign_details}</value>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</return>
<msg timestamp="20241017 10:15:51.431" level="INFO">${campaign_details} = [{'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHi...</msg>
<status status="PASS" starttime="20241017 10:15:48.762" endtime="20241017 10:15:51.431"/>
</kw>
<kw name="Get Current Date In ISO Format" library="ATMMarketing_JSON_Requests">
<var>${today_date}</var>
<kw name="Get Current Date" library="DateTime">
<var>${current_time}</var>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="20241017 10:15:51.431" level="INFO">${current_time} = 2024-10-17T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</kw>
<return>
<value>${current_time}</value>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</return>
<msg timestamp="20241017 10:15:51.431" level="INFO">${today_date} = 2024-10-17T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.431" endtime="20241017 10:15:51.431"/>
</kw>
<if>
<branch type="IF" condition="'${CAMPAIGN_START_DATE}' != '${EMPTY}'">
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<msg timestamp="20241017 10:15:51.436" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for CAMPAIGN_START_DATE, which is '${CAMPAIGN_START_DATE}', is not an integer. The CAMPAIGN_START_DATE must be specified as a number of days from today; e.g if the campaign must start tomorrow then you must specify the data for CAMPAIGN_START_DATE as 1</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Add days to String date" library="ATMMarketing_JSON_Requests">
<var>${start_date_string}</var>
<arg>${today_date}</arg>
<arg>${CAMPAIGN_START_DATE}</arg>
<kw name="Get Current Date" library="DateTime">
<var>${current}</var>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${current} = 2024-10-17T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${date}</var>
<arg>${DATE_STRING}</arg>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${date} = 2024-10-17T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${new_date}</var>
<arg>${date}</arg>
<arg>${DAYS} days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${new_date} = 2024-10-19 10:15:51.000</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${date_2}</var>
<arg>${new_date}</arg>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${date_2} = 2024-10-19T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Convert To String" library="BuiltIn">
<var>${new_date_string}</var>
<arg>${date_2}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${new_date_string} = 2024-10-19T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<return>
<value>${new_date_string}</value>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</return>
<msg timestamp="20241017 10:15:51.436" level="INFO">${start_date_string} = 2024-10-19T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Split String" library="String">
<var>${start_date_string_array}</var>
<arg>${start_date_string}</arg>
<arg>T</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${start_date_string_array} = ['2024-10-19', '10:15:51.000Z']</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${start_date_string}</var>
<arg>${start_date_string_array}[0]T00:00:00.000Z</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${start_date_string} = 2024-10-19T00:00:00.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</branch>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</if>
<if>
<branch type="IF" condition="'${CAMPAIGN_END_DATE}' != '${EMPTY}'">
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<msg timestamp="20241017 10:15:51.436" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for CAMPAIGN_END_DATE, which is '${CAMPAIGN_END_DATE}', is not an integer. The CAMPAIGN_END_DATE must be specified as a number of days from today; e.g if the campaign must expire tomorrow then you must specify the data for CAMPAIGN_START_DATE as 1</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Add days to String date" library="ATMMarketing_JSON_Requests">
<var>${end_date_string}</var>
<arg>${today_date}</arg>
<arg>${CAMPAIGN_END_DATE}</arg>
<kw name="Get Current Date" library="DateTime">
<var>${current}</var>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${current} = 2024-10-17T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${date}</var>
<arg>${DATE_STRING}</arg>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${date} = 2024-10-17T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${new_date}</var>
<arg>${date}</arg>
<arg>${DAYS} days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${new_date} = 2024-10-25 10:15:51.000</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${date_2}</var>
<arg>${new_date}</arg>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${date_2} = 2024-10-25T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Convert To String" library="BuiltIn">
<var>${new_date_string}</var>
<arg>${date_2}</arg>
<doc>Converts the given item to a Unicode string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${new_date_string} = 2024-10-25T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<return>
<value>${new_date_string}</value>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</return>
<msg timestamp="20241017 10:15:51.436" level="INFO">${end_date_string} = 2024-10-25T10:15:51.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Split String" library="String">
<var>${end_date_string_array}</var>
<arg>${end_date_string}</arg>
<arg>T</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${end_date_string_array} = ['2024-10-25', '10:15:51.000Z']</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${end_date_string}</var>
<arg>${end_date_string_array}[0]T00:00:00.000Z</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${end_date_string} = 2024-10-25T00:00:00.000Z</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</branch>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</if>
<kw name="Evaluate" library="BuiltIn">
<var>${type}</var>
<arg>type($campaign_details)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${type} = &lt;class 'list'&gt;</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>${images}</var>
<doc>Returns a list containing given items.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${images} = []</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_counter}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${image_counter} = 0</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<if>
<branch type="IF" condition="&quot;${type}&quot; == &quot;&lt;class 'dict'&gt;&quot;">
<kw name="Log" library="BuiltIn">
<arg>A campaign has 1 image</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${length}</var>
<arg>1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Up the image List Object" library="RestCalls">
<var>${images}</var>
<arg>${images}</arg>
<arg>${CAMPAIGN_ID}</arg>
<arg>${image_counter}</arg>
<arg>${length}</arg>
<status status="NOT RUN" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_fields}</var>
<arg>${campaign_details}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</branch>
<branch type="ELSE IF" condition="&quot;${type}&quot; == &quot;&lt;class 'list'&gt;&quot;">
<kw name="Get number of records returned by query" library="DBUtility">
<var>${length}</var>
<arg>${campaign_details}</arg>
<kw name="Count Dictionaries" library="DatabaseUtility">
<var>${dict_count}</var>
<arg>@{SQL_RECORDS}</arg>
<msg timestamp="20241017 10:15:51.436" level="INFO">${dict_count} = 2</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<return>
<value>${dict_count}</value>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</return>
<msg timestamp="20241017 10:15:51.436" level="INFO">${length} = 2</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>A campaign has ${length} images</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">A campaign has 2 images</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<for flavor="IN">
<var>${item}</var>
<value>@{campaign_details}</value>
<iter>
<var name="${item}">{'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHis...</var>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_fields}</var>
<arg>${item}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${campaign_fields} = {'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHis...</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Up the image List Object" library="RestCalls">
<var>${images}</var>
<arg>${images}</arg>
<arg>${CAMPAIGN_ID}</arg>
<arg>${image_counter}</arg>
<arg>${length}</arg>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_COUNTER}</arg>
<msg timestamp="20241017 10:15:51.436" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_COUNTER, which is '${IMAGE_COUNTER}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${IMAGE_NAMES}</var>
<arg>CAMP_IMAGES</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${IMAGE_NAMES} = images/MarketingA_en_7_sot.jpg,images/MarketingB_en_7_zul.jpg</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Split String" library="String">
<var>${image_list}</var>
<arg>${IMAGE_NAMES}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${image_list} = ['images/MarketingA_en_7_sot.jpg', 'images/MarketingB_en_7_zul.jpg']</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${number_of_images_to_upload}</var>
<arg>${image_list}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">Length is 2</msg>
<msg timestamp="20241017 10:15:51.436" level="INFO">${number_of_images_to_upload} = 2</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${LANGUAGES}</var>
<arg>CAMP_LNGS</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${LANGUAGES} = English,Afrikaans</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Split String" library="String">
<var>${language_list}</var>
<arg>${LANGUAGES}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${language_list} = ['English', 'Afrikaans']</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${number_of_image_laguages_provided}</var>
<arg>${language_list}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">Length is 2</msg>
<msg timestamp="20241017 10:15:51.436" level="INFO">${number_of_image_laguages_provided} = 2</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${number_of_images_to_upload}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal" library="BuiltIn">
<arg>'${number_of_images_to_upload}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<msg timestamp="20241017 10:15:51.436" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The campaign with the ID: ${CAMPAIGN_ID} has ${TOTAL_NUMBER_OF_IMAGES} images. Please provide the same number of images before the campaign could be edited!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${number_of_image_laguages_provided}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal" library="BuiltIn">
<arg>'${number_of_image_laguages_provided}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<msg timestamp="20241017 10:15:51.436" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The campaign with the ID: ${CAMPAIGN_ID} has ${TOTAL_NUMBER_OF_IMAGES} languages. Please provide the same number of languages before the campaign could be edited!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_id}</var>
<arg>${image_counter}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${image_id} = 0</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${user_provided_imageName}</var>
<arg>${image_list}[${image_counter}]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${user_provided_imageName} = images/MarketingA_en_7_sot.jpg</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Split String" library="String">
<var>${user_image_array}</var>
<arg>${user_provided_imageName}</arg>
<arg>/</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${user_image_array} = ['images', 'MarketingA_en_7_sot.jpg']</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${actual_image_name}</var>
<arg>${user_image_array[1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.436" level="INFO">${actual_image_name} = MarketingA_en_7_sot.jpg</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.436"/>
</kw>
<kw name="Create Base64 Image String" library="CreateRestPayloads">
<var>${base64_image}</var>
<arg>${user_provided_imageName}</arg>
<msg timestamp="20241017 10:15:51.451" level="INFO">path future_fit_architecture_portal
Property value fetched is:  4f3fcaf9-08bb-4af0-8736-aa1acc218567</msg>
<msg timestamp="20241017 10:15:51.451" level="INFO">${base64_image} = data:image/jpg;base64,/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAAAA...</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Split String" library="String">
<var>${image_string_array}</var>
<arg>${base64_image}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${image_string_array} = ['data:image/jpg;base64', '/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAA...</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_string}</var>
<arg>${image_string_array[1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${image_string} = /9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAAAAAAAPbWAAEAAAAA0y1IUCAg...</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Is Base64 Encoded" library="CreateCampaignAPI">
<var>${result}</var>
<arg>${image_string}</arg>
<doc>Check if the given string is Base64 encoded.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The image named '${user_provided_imageName}', could not be converted to Base64 string. Please ensure that the image path and extention are valid</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${user_provided_languageName}</var>
<arg>${language_list}[${image_counter}]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${user_provided_languageName} = English</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Get the language details from the database" library="PUTATMMarketingCampaign_DB_Verifications">
<var>${db_language_details}</var>
<arg>${user_provided_languageName}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${LANGUAGE}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Please make sure that values of all parameters are provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${db_type} = 'MYSQL'</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_language_query}</var>
<arg>${SQL_GET_MARKETING_LANGUAGES_USING_LANGUAGE_NAME}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${campaign_language_query} = SELECT * FROM ATM_Marketing.Language where language = L_ANG</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Replace String" library="String">
<var>${campaign_language_query}</var>
<arg>${campaign_language_query}</arg>
<arg>L_ANG</arg>
<arg>'${LANGUAGE}'</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${campaign_language_query} = SELECT * FROM ATM_Marketing.Language where language = 'English'</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Execute SQL Query" library="DBUtility">
<var>${data_base_campaign_language_details}</var>
<arg>${db_type}</arg>
<arg>${campaign_language_query}</arg>
<arg>True</arg>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${return_data} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:51.451" level="INFO">${return_all} = False</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.451"/>
</kw>
<kw name="Verify Data Using Database" library="DatabaseUtility">
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<msg timestamp="20241017 10:15:51.944" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg timestamp="20241017 10:15:51.944" level="INFO">${data_found} = {'id': 1, 'language': 'English', 'languageCode': 'en'}</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.944"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:51.944" endtime="20241017 10:15:51.944"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:51.944" endtime="20241017 10:15:51.944"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:51.944" endtime="20241017 10:15:51.946"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" starttime="20241017 10:15:51.946" endtime="20241017 10:15:51.946"/>
</return>
<msg timestamp="20241017 10:15:51.946" level="INFO">${data_base_campaign_language_details} = {'id': 1, 'language': 'English', 'languageCode': 'en'}</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.946"/>
</kw>
<return>
<value>${data_base_campaign_language_details}</value>
<status status="PASS" starttime="20241017 10:15:51.946" endtime="20241017 10:15:51.946"/>
</return>
<msg timestamp="20241017 10:15:51.946" level="INFO">${db_language_details} = {'id': 1, 'language': 'English', 'languageCode': 'en'}</msg>
<status status="PASS" starttime="20241017 10:15:51.451" endtime="20241017 10:15:51.946"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Not Be Equal</arg>
<arg>"${db_language_details}"</arg>
<arg>"'Failed'"</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Not Be Equal" library="BuiltIn">
<arg>"${db_language_details}"</arg>
<arg>"'Failed'"</arg>
<doc>Fails if the given objects are equal.</doc>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<msg timestamp="20241017 10:15:51.947" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_LANGUAGE which is '${user_provided_languageName}', is not valid.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${language_id}</var>
<arg>${db_language_details}</arg>
<arg>id</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:51.947" level="INFO">${language_id} = 1</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${duaration}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.947" level="INFO">${duaration} = 0</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${priority}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.947" level="INFO">${priority} = 0</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Create the imageList Object" library="ATMMarketing_JSON_Requests">
<var>${images}</var>
<arg>${IMAGE_LIST_OBJECT}</arg>
<arg>${image_id}</arg>
<arg>${base64_image}</arg>
<arg>${language_id}</arg>
<arg>${user_provided_languageName}</arg>
<arg>${actual_image_name}</arg>
<arg>${duaration}</arg>
<arg>${priority}</arg>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_ID}</arg>
<msg timestamp="20241017 10:15:51.947" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_ID, which is '${IMAGE_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.947"/>
</kw>
<kw name="Split String" library="String">
<var>${image_string_array}</var>
<arg>${MARKETING_IMAGE_BASE64_STRING}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${image_string_array} = ['data:image/jpg;base64', '/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAA...</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${actual_length}</var>
<arg>${image_string_array}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">Length is 3</msg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${actual_length} = 3</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${actual_length}'</arg>
<arg>'3'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal" library="BuiltIn">
<arg>'${actual_length}'</arg>
<arg>'3'</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<msg timestamp="20241017 10:15:51.949" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_IMAGE_BASE64_STRING, is not a valid Base64 format.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_string}</var>
<arg>${image_string_array[1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${image_string} = /9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQAAAAASUVDIHNSR0IAAAAAAAAAAAAAAAAAAPbWAAEAAAAA0y1IUCAg...</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Is Base64 Encoded" library="CreateCampaignAPI">
<var>${result}</var>
<arg>${image_string}</arg>
<doc>Check if the given string is Base64 encoded.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_IMAGE_BASE64_STRING, is not a valid Base64 format.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${MARKETING_IMAGE_LANGUAGE_ID}</arg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_IMAGE_LANGUAGE_ID, which is '${MARKETING_IMAGE_LANGUAGE_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_DURATION}</arg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_DURATION, which is '${IMAGE_DURATION}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_PRIORITY}</arg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_PRIORITY, which is '${IMAGE_PRIORITY}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Append To List" library="Collections">
<arg>${IMAGES}</arg>
<arg>{"id": ${IMAGE_ID}, "marketingImage": "${MARKETING_IMAGE_BASE64_STRING}", "language": {"id": ${MARKETING_IMAGE_LANGUAGE_ID}, "language": "${MARKETING_IMAGE_LANGUAGE_NAME}"}, "imageName": "${IMAGE_NAME}", "duration": ${IMAGE_DURATION}, "priority": ${IMAGE_PRIORITY}}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<return>
<value>${IMAGES}</value>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</return>
<msg timestamp="20241017 10:15:51.949" level="INFO">${images} = ['{"id": 0, "marketingImage": "data:image/jpg;base64,/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQ...</msg>
<status status="PASS" starttime="20241017 10:15:51.947" endtime="20241017 10:15:51.949"/>
</kw>
<return>
<value>${images}</value>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</return>
<msg timestamp="20241017 10:15:51.949" level="INFO">${images} = ['{"id": 0, "marketingImage": "data:image/jpg;base64,/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQ...</msg>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${image_counter}</var>
<arg>${image_counter} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${image_counter} = 1</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:51.949"/>
</iter>
<iter>
<var name="${item}">{'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHis...</var>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_fields}</var>
<arg>${item}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${campaign_fields} = {'approvalId': 1, 'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2024-10-25 23:59:59', 'campaignHistoryID': 92, 'campaignHistoryUpdateDate': '2024-10-17 07:47:15.760472', 'campaignHis...</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Set Up the image List Object" library="RestCalls">
<var>${images}</var>
<arg>${images}</arg>
<arg>${CAMPAIGN_ID}</arg>
<arg>${image_counter}</arg>
<arg>${length}</arg>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_COUNTER}</arg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_COUNTER, which is '${IMAGE_COUNTER}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${IMAGE_NAMES}</var>
<arg>CAMP_IMAGES</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${IMAGE_NAMES} = images/MarketingA_en_7_sot.jpg,images/MarketingB_en_7_zul.jpg</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Split String" library="String">
<var>${image_list}</var>
<arg>${IMAGE_NAMES}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${image_list} = ['images/MarketingA_en_7_sot.jpg', 'images/MarketingB_en_7_zul.jpg']</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${number_of_images_to_upload}</var>
<arg>${image_list}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">Length is 2</msg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${number_of_images_to_upload} = 2</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${LANGUAGES}</var>
<arg>CAMP_LNGS</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${LANGUAGES} = English,Afrikaans</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Split String" library="String">
<var>${language_list}</var>
<arg>${LANGUAGES}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">${language_list} = ['English', 'Afrikaans']</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${number_of_image_laguages_provided}</var>
<arg>${language_list}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20241017 10:15:51.949" level="INFO">Length is 2</msg>
<msg timestamp="20241017 10:15:51.949" level="INFO">${number_of_image_laguages_provided} = 2</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.949"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${number_of_images_to_upload}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal" library="BuiltIn">
<arg>'${number_of_images_to_upload}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<msg timestamp="20241017 10:15:51.958" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The campaign with the ID: ${CAMPAIGN_ID} has ${TOTAL_NUMBER_OF_IMAGES} images. Please provide the same number of images before the campaign could be edited!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${number_of_image_laguages_provided}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal" library="BuiltIn">
<arg>'${number_of_image_laguages_provided}'</arg>
<arg>'${TOTAL_NUMBER_OF_IMAGES}'</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<msg timestamp="20241017 10:15:51.958" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The campaign with the ID: ${CAMPAIGN_ID} has ${TOTAL_NUMBER_OF_IMAGES} languages. Please provide the same number of languages before the campaign could be edited!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_id}</var>
<arg>${image_counter}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.958" level="INFO">${image_id} = 1</msg>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${user_provided_imageName}</var>
<arg>${image_list}[${image_counter}]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.958" level="INFO">${user_provided_imageName} = images/MarketingB_en_7_zul.jpg</msg>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Split String" library="String">
<var>${user_image_array}</var>
<arg>${user_provided_imageName}</arg>
<arg>/</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.958" level="INFO">${user_image_array} = ['images', 'MarketingB_en_7_zul.jpg']</msg>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${actual_image_name}</var>
<arg>${user_image_array[1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.958" level="INFO">${actual_image_name} = MarketingB_en_7_zul.jpg</msg>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.958"/>
</kw>
<kw name="Create Base64 Image String" library="CreateRestPayloads">
<var>${base64_image}</var>
<arg>${user_provided_imageName}</arg>
<msg timestamp="20241017 10:15:51.963" level="INFO">path future_fit_architecture_portal
Property value fetched is:  4f3fcaf9-08bb-4af0-8736-aa1acc218567</msg>
<msg timestamp="20241017 10:15:51.963" level="INFO">${base64_image} = data:image/jpg;base64,/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+ICQElDQ19QUk9GSUxFAAEBAAACMEFEQkUCEAAAbW50clJHQiBYWVogB88ABgADAAAAAAAAYWNzcEFQUEwAAAAAbm...</msg>
<status status="PASS" starttime="20241017 10:15:51.958" endtime="20241017 10:15:51.963"/>
</kw>
<kw name="Split String" library="String">
<var>${image_string_array}</var>
<arg>${base64_image}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:51.963" level="INFO">${image_string_array} = ['data:image/jpg;base64', '/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+ICQElDQ19QUk9GSUxFAAEBAAACMEFEQkUCEAAAbW50clJHQiBYWVogB88ABgADAAAAAAAAYWNzcEFQUEwAA...</msg>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:51.963"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_string}</var>
<arg>${image_string_array[1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.963" level="INFO">${image_string} = /9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+ICQElDQ19QUk9GSUxFAAEBAAACMEFEQkUCEAAAbW50clJHQiBYWVogB88ABgADAAAAAAAAYWNzcEFQUEwAAAAAbm9uZQAAAAAAAAAAAAAAAAAA...</msg>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:51.963"/>
</kw>
<kw name="Is Base64 Encoded" library="CreateCampaignAPI">
<var>${result}</var>
<arg>${image_string}</arg>
<doc>Check if the given string is Base64 encoded.</doc>
<msg timestamp="20241017 10:15:51.963" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:51.963"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The image named '${user_provided_imageName}', could not be converted to Base64 string. Please ensure that the image path and extention are valid</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:51.963"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${user_provided_languageName}</var>
<arg>${language_list}[${image_counter}]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.963" level="INFO">${user_provided_languageName} = Afrikaans</msg>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:51.963"/>
</kw>
<kw name="Get the language details from the database" library="PUTATMMarketingCampaign_DB_Verifications">
<var>${db_language_details}</var>
<arg>${user_provided_languageName}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${LANGUAGE}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Please make sure that values of all parameters are provided!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:51.968"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.968" level="INFO">${db_type} = 'MYSQL'</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:51.968"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_language_query}</var>
<arg>${SQL_GET_MARKETING_LANGUAGES_USING_LANGUAGE_NAME}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:51.968" level="INFO">${campaign_language_query} = SELECT * FROM ATM_Marketing.Language where language = L_ANG</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:51.968"/>
</kw>
<kw name="Replace String" library="String">
<var>${campaign_language_query}</var>
<arg>${campaign_language_query}</arg>
<arg>L_ANG</arg>
<arg>'${LANGUAGE}'</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="20241017 10:15:51.968" level="INFO">${campaign_language_query} = SELECT * FROM ATM_Marketing.Language where language = 'Afrikaans'</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:51.968"/>
</kw>
<kw name="Execute SQL Query" library="DBUtility">
<var>${data_base_campaign_language_details}</var>
<arg>${db_type}</arg>
<arg>${campaign_language_query}</arg>
<arg>True</arg>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:51.968" level="INFO">${return_data} = True</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:51.968"/>
</kw>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="20241017 10:15:51.968" level="INFO">${return_all} = False</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:51.968"/>
</kw>
<kw name="Verify Data Using Database" library="DatabaseUtility">
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<msg timestamp="20241017 10:15:52.376" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg timestamp="20241017 10:15:52.376" level="INFO">${data_found} = {'id': 2, 'language': 'Afrikaans', 'languageCode': 'af'}</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:52.376"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:52.378" endtime="20241017 10:15:52.378"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:52.378" endtime="20241017 10:15:52.379"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="20241017 10:15:52.379" endtime="20241017 10:15:52.379"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" starttime="20241017 10:15:52.379" endtime="20241017 10:15:52.379"/>
</return>
<msg timestamp="20241017 10:15:52.379" level="INFO">${data_base_campaign_language_details} = {'id': 2, 'language': 'Afrikaans', 'languageCode': 'af'}</msg>
<status status="PASS" starttime="20241017 10:15:51.968" endtime="20241017 10:15:52.379"/>
</kw>
<return>
<value>${data_base_campaign_language_details}</value>
<status status="PASS" starttime="20241017 10:15:52.379" endtime="20241017 10:15:52.379"/>
</return>
<msg timestamp="20241017 10:15:52.379" level="INFO">${db_language_details} = {'id': 2, 'language': 'Afrikaans', 'languageCode': 'af'}</msg>
<status status="PASS" starttime="20241017 10:15:51.963" endtime="20241017 10:15:52.379"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Not Be Equal</arg>
<arg>"${db_language_details}"</arg>
<arg>"'Failed'"</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Not Be Equal" library="BuiltIn">
<arg>"${db_language_details}"</arg>
<arg>"'Failed'"</arg>
<doc>Fails if the given objects are equal.</doc>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<msg timestamp="20241017 10:15:52.381" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_LANGUAGE which is '${user_provided_languageName}', is not valid.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${language_id}</var>
<arg>${db_language_details}</arg>
<arg>id</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.381" level="INFO">${language_id} = 2</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${duaration}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.381" level="INFO">${duaration} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${priority}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.381" level="INFO">${priority} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Create the imageList Object" library="ATMMarketing_JSON_Requests">
<var>${images}</var>
<arg>${IMAGE_LIST_OBJECT}</arg>
<arg>${image_id}</arg>
<arg>${base64_image}</arg>
<arg>${language_id}</arg>
<arg>${user_provided_languageName}</arg>
<arg>${actual_image_name}</arg>
<arg>${duaration}</arg>
<arg>${priority}</arg>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_ID}</arg>
<msg timestamp="20241017 10:15:52.381" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_ID, which is '${IMAGE_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.381"/>
</kw>
<kw name="Split String" library="String">
<var>${image_string_array}</var>
<arg>${MARKETING_IMAGE_BASE64_STRING}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="20241017 10:15:52.387" level="INFO">${image_string_array} = ['data:image/jpg;base64', '/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+ICQElDQ19QUk9GSUxFAAEBAAACMEFEQkUCEAAAbW50clJHQiBYWVogB88ABgADAAAAAAAAYWNzcEFQUEwAA...</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.387"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${actual_length}</var>
<arg>${image_string_array}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20241017 10:15:52.387" level="INFO">Length is 3</msg>
<msg timestamp="20241017 10:15:52.387" level="INFO">${actual_length} = 3</msg>
<status status="PASS" starttime="20241017 10:15:52.387" endtime="20241017 10:15:52.387"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Should Be Equal</arg>
<arg>'${actual_length}'</arg>
<arg>'3'</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal" library="BuiltIn">
<arg>'${actual_length}'</arg>
<arg>'3'</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="20241017 10:15:52.387" endtime="20241017 10:15:52.387"/>
</kw>
<msg timestamp="20241017 10:15:52.387" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.387" endtime="20241017 10:15:52.387"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_IMAGE_BASE64_STRING, is not a valid Base64 format.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.387" endtime="20241017 10:15:52.387"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${image_string}</var>
<arg>${image_string_array[1]}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.387" level="INFO">${image_string} = /9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+ICQElDQ19QUk9GSUxFAAEBAAACMEFEQkUCEAAAbW50clJHQiBYWVogB88ABgADAAAAAAAAYWNzcEFQUEwAAAAAbm9uZQAAAAAAAAAAAAAAAAAA...</msg>
<status status="PASS" starttime="20241017 10:15:52.387" endtime="20241017 10:15:52.387"/>
</kw>
<kw name="Is Base64 Encoded" library="CreateCampaignAPI">
<var>${result}</var>
<arg>${image_string}</arg>
<doc>Check if the given string is Base64 encoded.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.387" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_IMAGE_BASE64_STRING, is not a valid Base64 format.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${MARKETING_IMAGE_LANGUAGE_ID}</arg>
<msg timestamp="20241017 10:15:52.392" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for MARKETING_IMAGE_LANGUAGE_ID, which is '${MARKETING_IMAGE_LANGUAGE_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_DURATION}</arg>
<msg timestamp="20241017 10:15:52.392" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_DURATION, which is '${IMAGE_DURATION}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${IMAGE_PRIORITY}</arg>
<msg timestamp="20241017 10:15:52.392" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for IMAGE_PRIORITY, which is '${IMAGE_PRIORITY}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Append To List" library="Collections">
<arg>${IMAGES}</arg>
<arg>{"id": ${IMAGE_ID}, "marketingImage": "${MARKETING_IMAGE_BASE64_STRING}", "language": {"id": ${MARKETING_IMAGE_LANGUAGE_ID}, "language": "${MARKETING_IMAGE_LANGUAGE_NAME}"}, "imageName": "${IMAGE_NAME}", "duration": ${IMAGE_DURATION}, "priority": ${IMAGE_PRIORITY}}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<return>
<value>${IMAGES}</value>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</return>
<msg timestamp="20241017 10:15:52.392" level="INFO">${images} = ['{"id": 0, "marketingImage": "data:image/jpg;base64,/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQ...</msg>
<status status="PASS" starttime="20241017 10:15:52.381" endtime="20241017 10:15:52.392"/>
</kw>
<return>
<value>${images}</value>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</return>
<msg timestamp="20241017 10:15:52.392" level="INFO">${images} = ['{"id": 0, "marketingImage": "data:image/jpg;base64,/9j/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEAAQBIAAAAAQAB/+IMWElDQ19QUk9GSUxFAAEBAAAMSExpbm8CEAAAbW50clJHQiBYWVogB84AAgAJAAYAMQAAYWNzcE1TRlQ...</msg>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${image_counter}</var>
<arg>${image_counter} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${image_counter} = 2</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<status status="PASS" starttime="20241017 10:15:51.949" endtime="20241017 10:15:52.392"/>
</iter>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:52.392"/>
</for>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:52.392"/>
</branch>
<status status="PASS" starttime="20241017 10:15:51.436" endtime="20241017 10:15:52.392"/>
</if>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_isApproved}</var>
<arg>${campaign_fields}</arg>
<arg>isApproved</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${campaign_isApproved} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Set Variable If" library="BuiltIn">
<var>${campaign_isApproved_boolean}</var>
<arg>${campaign_isApproved} == 1</arg>
<arg>True</arg>
<arg>False</arg>
<doc>Sets variable based on the given condition.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${campaign_isApproved_boolean} = False</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${campaign_isApproved_boolean}</arg>
<arg>Fail</arg>
<arg>The campaign: '${CAMPAIGN_ID}' is approved hence it cannot be edited.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${last_update_id}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${last_update_id} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${last_update_campaignId}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${last_update_campaignId} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${last_update_updatedDate}</var>
<arg>${campaign_fields}</arg>
<arg>campaignHistoryUpdateDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${last_update_updatedDate} = 2024-10-17 07:47:15.760472</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${last_update_approvalId}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${last_update_approvalId} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Create the lastUpdate object" library="ATMMarketing_JSON_Requests">
<var>${last_update}</var>
<arg>${last_update_id}</arg>
<arg>${last_update_campaignId}</arg>
<arg>${last_update_approvalId}</arg>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${LAST_UPDATE_ID}</arg>
<msg timestamp="20241017 10:15:52.392" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for LAST_UPDATE_ID, which is '${LAST_UPDATE_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${CAMPAIGN_ID}</arg>
<msg timestamp="20241017 10:15:52.392" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for CAMPAIGN_ID, which is '${CAMPAIGN_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Validate Integer" library="CreateCampaignAPI">
<var>${integer_validation}</var>
<arg>${APPROVAL_ID}</arg>
<msg timestamp="20241017 10:15:52.392" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value for APPROVAL_ID, which is '${APPROVAL_ID}', is not an integer.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Convert To Integer" library="BuiltIn">
<var>${LAST_UPDATE_ID}</var>
<arg>${LAST_UPDATE_ID}</arg>
<doc>Converts the given item to an integer number.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${LAST_UPDATE_ID} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Convert To Integer" library="BuiltIn">
<var>${CAMPAIGN_ID}</var>
<arg>${CAMPAIGN_ID}</arg>
<doc>Converts the given item to an integer number.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${CAMPAIGN_ID} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Convert To Integer" library="BuiltIn">
<var>${APPROVAL_ID}</var>
<arg>${APPROVAL_ID}</arg>
<doc>Converts the given item to an integer number.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${APPROVAL_ID} = 0</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Get Current Date In ISO Format" library="ATMMarketing_JSON_Requests">
<var>${update_date}</var>
<kw name="Get Current Date" library="DateTime">
<var>${current_time}</var>
<arg>result_format=%Y-%m-%dT%H:%M:%S.000Z</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${current_time} = 2024-10-17T10:15:52.000Z</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<return>
<value>${current_time}</value>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</return>
<msg timestamp="20241017 10:15:52.392" level="INFO">${update_date} = 2024-10-17T10:15:52.000Z</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${last_update}</var>
<arg>id=${LAST_UPDATE_ID}</arg>
<arg>campaignId=${CAMPAIGN_ID}</arg>
<arg>updatedDate=${update_date}</arg>
<arg>updatedBy=</arg>
<arg>updateDescription=</arg>
<arg>approvalId=${APPROVAL_ID}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${last_update} = {'id': 0, 'campaignId': 0, 'updatedDate': '2024-10-17T10:15:52.000Z', 'updatedBy': '', 'updateDescription': '', 'approvalId': 0}</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<return>
<value>${last_update}</value>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</return>
<msg timestamp="20241017 10:15:52.392" level="INFO">${last_update} = {'id': 0, 'campaignId': 0, 'updatedDate': '2024-10-17T10:15:52.000Z', 'updatedBy': '', 'updateDescription': '', 'approvalId': 0}</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${targetData_isTargetRegion}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${targetData_isTargetRegion} = False</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${targetData_targetRegionOrAtm}</var>
<arg>${EMPTY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.392" level="INFO">${targetData_targetRegionOrAtm} = </msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<kw name="Create the targetData object" library="ATMMarketing_JSON_Requests">
<var>${targetData}</var>
<arg>${targetData_isTargetRegion}</arg>
<arg>${targetData_targetRegionOrAtm}</arg>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${result}</var>
<arg>Is Boolean</arg>
<arg>${IS_TARGET_REGION}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Is Boolean" library="CreateCampaignAPI">
<arg>${IS_TARGET_REGION}</arg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.392"/>
</kw>
<msg timestamp="20241017 10:15:52.403" level="INFO">${result} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${result}' == 'False'</arg>
<arg>Fail</arg>
<arg>The provided value for IS_TARGET_REGION, is not a Boolean.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${target_data}</var>
<arg>isTargetRegion=${IS_TARGET_REGION}</arg>
<arg>targetRegionOrAtm=${TARGET_REGION_OR_ATM}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${target_data} = {'isTargetRegion': False, 'targetRegionOrAtm': ''}</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<return>
<value>${target_data}</value>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</return>
<msg timestamp="20241017 10:15:52.403" level="INFO">${targetData} = {'isTargetRegion': False, 'targetRegionOrAtm': ''}</msg>
<status status="PASS" starttime="20241017 10:15:52.392" endtime="20241017 10:15:52.403"/>
</kw>
<if>
<branch type="IF" condition="'${CAMPAIGN_NAME}' == '${EMPTY}'">
<kw name="Get From Dictionary" library="Collections">
<var>${campaignName}</var>
<arg>${campaign_fields}</arg>
<arg>campaignName</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${campaignName}</var>
<arg>${CAMPAIGN_NAME}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaignName} = Edited using Automated Script</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</if>
<if>
<branch type="IF" condition="'${CAMPAIGN_START_DATE}' == '${EMPTY}'">
<kw name="Get From Dictionary" library="Collections">
<var>${start_date_string}</var>
<arg>${campaign_fields}</arg>
<arg>campaignStartDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Split String" library="String">
<var>${start_date_string_array}</var>
<arg>${start_date_string}</arg>
<arg>${SPACE}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${start_date_string}</var>
<arg>${start_date_string_array}[0]T00:00:00.000Z</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</if>
<if>
<branch type="IF" condition="'${CAMPAIGN_END_DATE}' == '${EMPTY}'">
<kw name="Get From Dictionary" library="Collections">
<var>${end_date_string}</var>
<arg>${campaign_fields}</arg>
<arg>campaignEndDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Split String" library="String">
<var>${end_date_string_array}</var>
<arg>${end_date_string}</arg>
<arg>${SPACE}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${end_date_string}</var>
<arg>${end_date_string_array}[0]T00:00:00.000Z</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</if>
<if>
<branch type="IF" condition="'${CAMPAIGN_RECEIVER_DEVICE_TYPE}' == '${EMPTY}'">
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_marketingChannelID}</var>
<arg>${campaign_fields}</arg>
<arg>marketingChannelID</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${_marketingChannelID}</var>
<arg>${CAMPAIGN_RECEIVER_DEVICE_TYPE}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${_marketingChannelID} = ATM</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${condition1}</var>
<arg>'${_marketingChannelID}' == 'ATM'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${condition1} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${condition2}</var>
<arg>'${_marketingChannelID}' == 'SSK'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${condition2} = False</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${condition3}</var>
<arg>'${_marketingChannelID}' == 'BCD'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${condition3} = False</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<if>
<branch type="IF" condition="${condition1}">
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_marketingChannelID}</var>
<arg>1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_marketingChannelID} = 1</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<branch type="ELSE">
<if>
<branch type="IF" condition="${condition2}">
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_marketingChannelID}</var>
<arg>2</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_marketingChannelID}</var>
<arg>3</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</if>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</if>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</branch>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</if>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_id}</var>
<arg>${campaign_fields}</arg>
<arg>id</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_id} = 15027</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_createdBy}</var>
<arg>${campaign_fields}</arg>
<arg>campaignBy</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_createdBy} = Thabo Benjamin Setuke (ZA)</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_campaignId}</var>
<arg>${campaign_fields}</arg>
<arg>campaignId</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_campaignId} = CNQ270v001Q42024</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_screenId}</var>
<arg>${campaign_fields}</arg>
<arg>marketingScreenID</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_screenId} = 2</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_updatedBy}</var>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_updatedBy} = </msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_isActive}</var>
<arg>${campaign_fields}</arg>
<arg>isActive</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_isActive} = 1</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Set Variable If" library="BuiltIn">
<var>${campaign_isActive_boolean}</var>
<arg>${campaign_isActive} == 1</arg>
<arg>True</arg>
<arg>False</arg>
<doc>Sets variable based on the given condition.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_isActive_boolean} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_version}</var>
<arg>1.0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_version} = 1.0</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${campaign_isTargetted}</var>
<arg>${campaign_fields}</arg>
<arg>isTargetted</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_isTargetted} = 1</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Set Variable If" library="BuiltIn">
<var>${campaign_isTargetted_boolean}</var>
<arg>${campaign_isTargetted} == 1</arg>
<arg>True</arg>
<arg>False</arg>
<doc>Sets variable based on the given condition.</doc>
<msg timestamp="20241017 10:15:52.403" level="INFO">${campaign_isTargetted_boolean} = True</msg>
<status status="PASS" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${campaign_isTargetted_boolean}</arg>
<arg>Fail</arg>
<arg>The campaign: '${CAMPAIGN_ID}' is targeted hence it cannot be edited.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Fail" library="BuiltIn">
<arg>The campaign: '${CAMPAIGN_ID}' is targeted hence it cannot be edited.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<msg timestamp="20241017 10:15:52.403" level="FAIL">The campaign: '15027' is targeted hence it cannot be edited.</msg>
<status status="FAIL" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="FAIL" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Generate Campaign JSON Request" library="ATMMarketing_JSON_Requests">
<var>${json_payload}</var>
<arg>${images}</arg>
<arg>${last_update}</arg>
<arg>${targetData}</arg>
<arg>${campaignName}</arg>
<arg>${CAMPAIGN_CREATED_BY}</arg>
<arg>${start_date_string}</arg>
<arg>${end_date_string}</arg>
<arg>${campaign_marketingChannelID}</arg>
<arg>${campaign_id}</arg>
<arg>${campaign_campaignId}</arg>
<arg>${campaign_screenId}</arg>
<arg>${campaign_updatedBy}</arg>
<arg>${campaign_isActive_boolean}</arg>
<arg>${campaign_isApproved_boolean}</arg>
<arg>${campaign_version}</arg>
<arg>${campaign_isTargetted_boolean}</arg>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${JSON_FILE}</var>
<arg>future_fit_architecture_portal/data/Campaign_EDIT.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Write JSON data To File" library="ATMMarketing_JSON_Requests">
<arg>${JSON_FILE}</arg>
<arg>${json_payload}</arg>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAMP_LNGS</arg>
<arg>${JSON_FILE}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="FAIL" starttime="20241017 10:15:48.762" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="And The user makes Put Rest Call with JSON payload" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<status status="NOT RUN" starttime="20241017 10:15:52.403" endtime="20241017 10:15:52.403"/>
</kw>
<status status="FAIL" starttime="20241017 10:15:48.759" endtime="20241017 10:15:52.403"/>
</kw>
<doc>Change the Campaign Name</doc>
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="20241017 10:15:48.759" endtime="20241017 10:15:52.403">The campaign: '15027' is targeted hence it cannot be edited.</status>
</test>
<doc>This is the test suite for deactivating an ATM Marketing Campaign using the Controller</doc>
<status status="FAIL" starttime="20241017 10:15:48.128" endtime="20241017 10:15:52.413"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">FFA_HEALTHCHECK</stat>
<stat pass="0" fail="1" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="0" fail="1" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
