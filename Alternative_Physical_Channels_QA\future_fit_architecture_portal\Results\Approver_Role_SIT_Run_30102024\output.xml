<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-30T14:56:01.293899" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role">
<suite id="s1-s1" name="RAC29a TC 117 Approve Campaign" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_117_Approve_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:03.416653" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:56:03.416653" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:03.417652" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:56:03.416653" elapsed="0.000999"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:03.417652" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:56:03.417652" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:03.417652" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:56:03.417652" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:03.417652" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:56:03.417652" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:03.415659" elapsed="0.001993"/>
</kw>
<test id="s1-s1-t1" name="RAC29a_TC_117_FFT_Approval_Approve_Campaign" line="43">
<kw name="Validating the Approval function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T14:56:03.418654" level="INFO">Set test documentation to:
Approve Campaign</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T14:56:03.418654" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:56:03.508444" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:56:03.419652" elapsed="0.088792"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:56:03.508444" elapsed="0.000999"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:56:03.509443" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:56:03.510450" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:56:03.510450" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:56:03.510450" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:56:03.510450" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:56:03.510450" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T14:56:03.511454" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T14:56:03.511454" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:56:03.511454" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T14:56:03.543968" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T14:56:04.250478" level="INFO">${rc_code} = 0</msg>
<msg time="2024-10-30T14:56:04.250478" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 23460 has been terminated.
SUCCESS: The process "chrome.exe" with PID 31780 has been terminated.
SUCCESS: The process "chrome.exe" with PID 32972 has been te...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T14:56:03.511454" elapsed="0.739024"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T14:56:04.251476" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T14:56:03.510450" elapsed="0.741026"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:04.252476" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T14:56:04.251476" elapsed="0.001000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:56:04.252476" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:56:04.252476" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:56:04.252476" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:56:04.252476" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:56:04.254527" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:56:04.254527" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.001053"/>
</if>
<status status="NOT RUN" start="2024-10-30T14:56:04.253474" elapsed="0.001053"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T14:56:04.254527" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T14:56:04.254527" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:56:04.255523" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E820200B0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:56:04.254527" elapsed="0.000996"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:56:04.255523" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T14:56:04.255523" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:56:04.255523" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:56:04.255523" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T14:56:04.256476" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T14:56:04.255523" elapsed="0.000953"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:56:04.256476" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:56:04.256476" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:56:04.257477" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:56:04.257477" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:56:04.257477" elapsed="0.001001"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:56:04.258478" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:56:04.258478" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:56:04.259474" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:56:04.258478" elapsed="0.000996"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T14:56:04.259474" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T14:56:03.418654" elapsed="37.359922"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T14:56:40.778576" elapsed="0.070724"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:40.854241" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.0AF44D03ACC78E1252012998D4E07C95.e.57")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:40.849300" elapsed="0.004941"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:40.854241" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.0AF44D03ACC78E1252012998D4E07C95.e.57")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:40.854241" elapsed="0.026372"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:56:40.881617" elapsed="0.007860"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:40.881617" elapsed="0.007860"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:40.902859" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.0AF44D03ACC78E1252012998D4E07C95.e.58")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:40.889477" elapsed="0.013382"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:56:45.903063" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:56:40.902859" elapsed="5.000204"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T14:56:45.903063" elapsed="0.013526"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:45.918072" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.0AF44D03ACC78E1252012998D4E07C95.e.58")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:45.916589" elapsed="0.076836"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:56:45.994763" elapsed="0.645995"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:45.994763" elapsed="0.645995"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:56:46.642234" elapsed="0.003619"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:46.642234" elapsed="0.003619"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:46.655147" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T14:56:46.645853" elapsed="0.009294"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:40.778576" elapsed="5.876571"/>
</kw>
<kw name="And The clicks on the preview button to preview an un-approved campaign" owner="Approvals">
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${dropdown_xpath}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:46.656163" elapsed="0.013852"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:46.670015" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${dropdown_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:46.670015" elapsed="0.031262"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:46.701277" level="INFO">Clicking element '//span[contains(text(), '100')]'.</msg>
<arg>${option_100_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:46.701277" elapsed="0.044247"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=${UNAPPROVED_CAMPAIGN_ROW_XPATH}/td[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T14:56:46.746530" elapsed="0.288045"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:56:49.034991" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:56:47.034575" elapsed="2.000416"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:49.055245" level="INFO">${CAMPAIGN_ID} = CNQ048v001Q42024</msg>
<var>${CAMPAIGN_ID}</var>
<arg>xpath=${UNAPPROVED_CAMPAIGN_ROW_XPATH}/td[2]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:49.034991" elapsed="0.020254"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:56:49.055245" level="INFO">Campaign ID: CNQ048v001Q42024</msg>
<arg>Campaign ID: ${CAMPAIGN_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:56:49.055245" elapsed="0.000000"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T14:56:49.055245" level="INFO">${CAMPAIGN_ID} = CNQ048v001Q42024</msg>
<arg>${CAMPAIGN_ID}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T14:56:49.055245" elapsed="0.000000"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${Un-Approved_Campaign}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T14:56:49.056263" elapsed="0.277458"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:56:54.333900" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:56:49.333721" elapsed="5.000179"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:54.333900" level="INFO">Clicking element '//tr[@role='row' and td[6]//mat-chip[contains(@class, 'not-approved')]]//fa-icon[@mattooltip='Preview']'.</msg>
<arg>${Un-Approved_Campaign}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:54.333900" elapsed="0.107124"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:57.230118" level="INFO">Suppressing StaleElementReferenceException from Selenium.</msg>
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:56:54.441024" elapsed="3.002104"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T14:56:57.443128" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:56:57.443128" elapsed="0.047574"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:56:59.492459" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:56:57.492022" elapsed="2.000437"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:46.655147" elapsed="12.837312"/>
</kw>
<kw name="And The user approves the campaign" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${APPROVE_CAMPAIGN_BUTTON_XPATH}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T14:56:59.492459" elapsed="0.013065"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:57:04.505599" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:56:59.505524" elapsed="5.000075"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:04.505599" level="INFO">Clicking element '//button[contains(@class, 'mat-button') and contains(span/text(), 'Approve Campaign')]'.</msg>
<arg>${APPROVE_CAMPAIGN_BUTTON_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:04.505599" elapsed="0.103094"/>
</kw>
<status status="PASS" start="2024-10-30T14:56:59.492459" elapsed="5.116234"/>
</kw>
<kw name="And The user navigates back to the Campaign Approvals page" owner="Navigation">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:57:09.610197" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:57:04.609694" elapsed="5.000503"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:09.618254" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.0AF44D03ACC78E1252012998D4E07C95.e.58")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:09.610197" elapsed="0.008057"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T14:57:09.618254" elapsed="0.011147"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:09.630407" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.0AF44D03ACC78E1252012998D4E07C95.e.58")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:09.629401" elapsed="0.055492"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:57:09.685891" elapsed="0.227068"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:09.685891" elapsed="0.227068"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:57:14.914023" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:57:09.912959" elapsed="5.001064"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:14.922957" level="INFO">Current page contains text 'APPROVALS'.</msg>
<arg>APPROVALS</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T14:57:14.914023" elapsed="0.008934"/>
</kw>
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:14.922957" level="INFO">Executing JavaScript:
location.reload(true)
Without any arguments.</msg>
<arg>location.reload(true)</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-30T14:57:14.922957" elapsed="0.376089"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:04.609694" elapsed="10.689352"/>
</kw>
<kw name="Then The user verifies that the campaign has been approved" owner="Approvals">
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:15.311076" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.4BA0CBD1E2B583D3476E64339B987978.e.144")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:15.300045" elapsed="0.011031"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:15.311076" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.4BA0CBD1E2B583D3476E64339B987978.e.144")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:15.311076" elapsed="0.725868"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:16.037944" level="INFO">Typing text 'CNQ048v001Q42024' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.4BA0CBD1E2B583D3476E64339B987978.e.144")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${CAMPAIGN_ID}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:16.036944" elapsed="0.065962"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.103098" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:57:16.102906" elapsed="2.000192"/>
</kw>
<kw name="Get WebElements" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:18.111950" level="INFO">${result_elements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="dac0aa823ae2faae251b8261e365c955", element="f.369D74A8DFC63E4B7DB0C592D34329F1.d.4BA0CBD1E2B583D3476E64339B987978.e.186")&gt;]</msg>
<var>${result_elements}</var>
<arg>//tr[@role='row' and td[2][normalize-space(text())='${CAMPAIGN_ID}']]</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:18.103098" elapsed="0.008852"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.111950" level="INFO">Length is 1.</msg>
<msg time="2024-10-30T14:57:18.111950" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${result_elements}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2024-10-30T14:57:18.111950" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Verify Campaign Status" owner="Approvals">
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:18.130363" level="INFO">${status} = 15204 CNQ048v001Q42024 SIT 1 Dup Camp 4 Yaash Ramsahar (ZA) Oct 30, 2024
true</msg>
<var>${status}</var>
<arg>//tr[@role='row' and td[2][normalize-space(text())='${CAMPAIGN_ID}']]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:18.113222" elapsed="0.017141"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.130363" level="INFO">Campaign Approval Status: 15204 CNQ048v001Q42024 SIT 1 Dup Camp 4 Yaash Ramsahar (ZA) Oct 30, 2024
true</msg>
<arg>Campaign Approval Status: ${status}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:18.130363" elapsed="0.000000"/>
</kw>
<kw name="Split String" owner="String">
<msg time="2024-10-30T14:57:18.130363" level="INFO">${status_list} = ['15204', 'CNQ048v001Q42024', 'SIT', '1', 'Dup', 'Camp', '4', 'Yaash', 'Ramsahar', '(ZA)', 'Oct', '30,', '2024', 'true']</msg>
<var>${status_list}</var>
<arg>${status}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<status status="PASS" start="2024-10-30T14:57:18.130363" elapsed="0.000000"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2024-10-30T14:57:18.131387" level="INFO">${approval_status} = true</msg>
<var>${approval_status}</var>
<arg>${status_list}</arg>
<arg>-1</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2024-10-30T14:57:18.131387" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.131387" level="INFO">Campaign Approved with status: true</msg>
<arg>Campaign Approved with status: ${approval_status}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:18.131387" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${approval_status.strip()}</arg>
<arg>true</arg>
<arg>Campaign approval status is false after approval, failing the test.</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2024-10-30T14:57:18.131387" elapsed="0.000982"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.133254" level="INFO">${approval_status} = true</msg>
<arg>${approval_status}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T14:57:18.132369" elapsed="0.000885"/>
</kw>
<arg>${result_elements}[0]</arg>
<status status="PASS" start="2024-10-30T14:57:18.111950" elapsed="0.021304"/>
</kw>
<arg>${result_count} == 1</arg>
<arg>Verify Campaign Status</arg>
<arg>${result_elements}[0]</arg>
<arg>ELSE IF</arg>
<arg>${result_count} &gt; 1</arg>
<arg>Log</arg>
<arg>Duplicate campaign found with ID: ${CAMPAIGN_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>No campaigns found with ID: ${CAMPAIGN_ID}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T14:57:18.111950" elapsed="0.021304"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.133254" level="INFO">----Campaign: CNQ048v001Q42024 successfully approved on front end----</msg>
<arg>----Campaign: ${CAMPAIGN_ID} successfully approved on front end----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:18.133254" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Campaign: ${CAMPAIGN_ID} successfully approved on front end----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:57:18.133254" elapsed="0.001006"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.134260" level="INFO">----Approval status set to: true----</msg>
<arg>----Approval status set to: ${approval_status}----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:18.134260" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:57:18.134260" level="INFO">----Approval status set to: true----</msg>
<arg>----Approval status set to: ${approval_status}----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:18.134260" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:15.299046" elapsed="2.835214"/>
</kw>
<arg>Approve Campaign</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T14:56:03.418654" elapsed="74.715606"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:57:18.135272" elapsed="0.005069"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:18.135272" elapsed="0.005069"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:18.141357" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:18.140341" elapsed="0.041001"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:57:21.182806" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:57:18.181342" elapsed="3.001464"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:21.182806" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T14:57:21.308402" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T14:57:21.308402" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T14:57:21.182806" elapsed="0.173446">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:57:23.357833" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:57:21.356252" elapsed="2.001581"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T14:57:23.357833" elapsed="2.272826"/>
</kw>
<status status="FAIL" start="2024-10-30T14:57:18.140341" elapsed="7.490318">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T14:57:18.140341" elapsed="7.490318"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:18.134260" elapsed="7.496399"/>
</kw>
<doc>Approve Campaign</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T14:56:03.417652" elapsed="82.213007"/>
</test>
<doc>Testing Camapaign Approval</doc>
<status status="PASS" start="2024-10-30T14:56:01.787477" elapsed="83.844655"/>
</suite>
<suite id="s1-s2" name="RAC29a TC 118 Delete Campaign" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_118_Delete_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.682525" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:57:25.682525" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.684021" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:57:25.684021" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.684021" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:57:25.684021" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.684021" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:57:25.684021" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.684021" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:57:25.684021" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:25.682525" elapsed="0.001496"/>
</kw>
<test id="s1-s2-t1" name="RAC29a_TC_118_FFT_Approval_Delete_Campaign" line="43">
<kw name="Validating the Deletion function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.685525" level="INFO">Set test documentation to:
Delete Campaign</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T14:57:25.685525" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.685525" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:57:25.685525" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:57:25.685525" elapsed="0.001006"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:57:25.686531" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.687666" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:57:25.687666" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:57:25.687666" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:57:25.687666" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:57:25.687666" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.688700" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T14:57:25.687666" elapsed="0.001034"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:57:25.688700" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.713410" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T14:57:25.973076" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T14:57:25.974082" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T14:57:25.688700" elapsed="0.285382"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.974082" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:25.974082" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T14:57:25.974082" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T14:57:25.687666" elapsed="0.286416"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.975467" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:57:25.975467" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:57:25.975467" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:57:25.976799" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.001332"/>
</branch>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.001332"/>
</if>
<status status="NOT RUN" start="2024-10-30T14:57:25.975467" elapsed="0.001332"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.976799" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T14:57:25.976799" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.976799" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E82022DB0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:57:25.976799" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:57:25.976799" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T14:57:25.977811" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:57:25.976799" elapsed="0.001012"/>
</branch>
<status status="PASS" start="2024-10-30T14:57:25.976799" elapsed="0.001012"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.977811" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T14:57:25.977811" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:57:25.977811" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:57:25.977811" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:57:25.977811" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:57:25.977811" elapsed="0.001003"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:57:25.978814" elapsed="0.000282"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:57:25.979096" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:57:25.979096" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.979096" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:57:25.979096" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T14:57:25.979096" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T14:57:25.685525" elapsed="32.728604"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T14:57:58.414129" elapsed="0.017435"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:58.435562" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.C4A264E517BC8599272832FB85C75DB6.e.128")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:58.431564" elapsed="0.003998"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:58.435562" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.C4A264E517BC8599272832FB85C75DB6.e.128")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:58.435562" elapsed="0.025708"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:57:58.462131" elapsed="0.007984"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:58.462131" elapsed="0.007984"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:57:58.477098" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.C4A264E517BC8599272832FB85C75DB6.e.129")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:57:58.470115" elapsed="0.006983"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:03.477433" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:57:58.477098" elapsed="5.000335"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T14:58:03.477433" elapsed="0.015259"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:03.493698" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.C4A264E517BC8599272832FB85C75DB6.e.129")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:03.493698" elapsed="0.093295"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:58:03.588383" elapsed="0.446932"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:03.586993" elapsed="0.448322"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:58:04.036319" elapsed="0.007872"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:04.035315" elapsed="0.009877"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:04.059468" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T14:58:04.045192" elapsed="0.014276"/>
</kw>
<status status="PASS" start="2024-10-30T14:57:58.414129" elapsed="5.645339"/>
</kw>
<kw name="And The user clicks the delete icon on a campaign" owner="Approvals">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:09.061143" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:04.060478" elapsed="5.000665"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${first_campaign_id}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T14:58:09.061143" elapsed="0.013841"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:09.090991" level="INFO">${DELETED_CAMPAIGN_ID} = CNQ048v001Q42024</msg>
<var>${DELETED_CAMPAIGN_ID}</var>
<arg>${first_campaign_id}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:09.074984" elapsed="0.016007"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T14:58:09.091930" level="INFO">${DELETED_CAMPAIGN_ID} = CNQ048v001Q42024</msg>
<arg>${DELETED_CAMPAIGN_ID}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T14:58:09.091930" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:58:09.091930" level="INFO">Campaign ID to be deactivated: CNQ048v001Q42024</msg>
<arg>Campaign ID to be deactivated: ${DELETED_CAMPAIGN_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:58:09.091930" elapsed="0.000000"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${Delete_Campaign_Button}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T14:58:09.091930" elapsed="0.286509"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:09.378439" level="INFO">Clicking element '//td[@role='cell' and contains(@class, 'mat-column-approve')]//fa-icon[@mattooltip='Delete']'.</msg>
<arg>${Delete_Campaign_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:09.378439" elapsed="0.036885"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:12.415540" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:09.415324" elapsed="3.000216"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:04.059468" elapsed="8.356072"/>
</kw>
<kw name="And The user clicks the deactivate campaign button to confirm deletion" owner="Approvals">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${Deactivate_Campaign_Button}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T14:58:12.416182" elapsed="0.270517"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:15.687291" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:12.686699" elapsed="3.000592"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:15.687291" level="INFO">Clicking element '//button[contains(@class, 'approve') and contains(., 'Deactivate Campaign')]'.</msg>
<arg>${Deactivate_Campaign_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:15.687291" elapsed="0.030161"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:20.717824" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:15.717452" elapsed="5.000372"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:12.415540" elapsed="8.302284"/>
</kw>
<kw name="And The user navigates back to the Campaign Approvals page" owner="Navigation">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:25.718258" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:20.717824" elapsed="5.000434"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:25.722898" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.C4A264E517BC8599272832FB85C75DB6.e.129")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:25.718258" elapsed="0.004640"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T14:58:25.724393" elapsed="0.013483"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:25.737876" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.C4A264E517BC8599272832FB85C75DB6.e.129")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:25.737876" elapsed="0.059610"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:58:25.797486" elapsed="0.435147"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:25.797486" elapsed="0.435147"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:31.234537" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:26.234056" elapsed="5.000481"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:31.245245" level="INFO">Current page contains text 'APPROVALS'.</msg>
<arg>APPROVALS</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T14:58:31.234537" elapsed="0.010708"/>
</kw>
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:31.246229" level="INFO">Executing JavaScript:
location.reload(true)
Without any arguments.</msg>
<arg>location.reload(true)</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-30T14:58:31.246229" elapsed="0.343145"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:20.717824" elapsed="10.871550"/>
</kw>
<kw name="Then The user verifies that the campaign has been deleted and removed from the front end" owner="Approvals">
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:31.601305" level="INFO">${Search_Bar} = &lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.78399B564D7851ED026C2763FA2846CE.e.192")&gt;</msg>
<var>${Search_Bar}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:31.590397" elapsed="0.010908"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:36.602811" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:31.602307" elapsed="5.000504"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:36.603373" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.78399B564D7851ED026C2763FA2846CE.e.192")&gt;'.</msg>
<arg>${Search_Bar}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:36.602811" elapsed="0.027649"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:38.630919" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:36.630460" elapsed="2.000459"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:38.631463" level="INFO">Typing text 'CNQ048v001Q42024' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="90b9b3dc0624a84276d736dee15445ee", element="f.59BF5B70B008BB23E419AEE85A4F2859.d.78399B564D7851ED026C2763FA2846CE.e.192")&gt;'.</msg>
<arg>${Search_Bar}</arg>
<arg>${DELETED_CAMPAIGN_ID}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:38.630919" elapsed="0.054236"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:40.685552" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:38.685155" elapsed="2.000397"/>
</kw>
<kw name="Wait Until Keyword Succeeds" owner="BuiltIn">
<kw name="Element Should Not Be Visible" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:40.693198" level="INFO">Element 'xpath=//tr[@role='row' and td[2][normalize-space(text())='CNQ048v001Q42024']]' did not exist.</msg>
<arg>xpath=//tr[@role='row' and td[2][normalize-space(text())='${DELETED_CAMPAIGN_ID}']]</arg>
<doc>Verifies that the element identified by ``locator`` is NOT visible.</doc>
<status status="PASS" start="2024-10-30T14:58:40.686089" elapsed="0.007109"/>
</kw>
<arg>5x</arg>
<arg>2s</arg>
<arg>Element Should Not Be Visible</arg>
<arg>xpath=//tr[@role='row' and td[2][normalize-space(text())='${DELETED_CAMPAIGN_ID}']]</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<status status="PASS" start="2024-10-30T14:58:40.685552" elapsed="0.007646"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:58:40.694512" level="INFO">Campaign successfully deleted. No results found for ID: CNQ048v001Q42024.</msg>
<arg>Campaign successfully deleted. No results found for ID: ${DELETED_CAMPAIGN_ID}.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:58:40.694512" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:31.590397" elapsed="9.104115"/>
</kw>
<arg>Delete Campaign</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T14:57:25.684021" elapsed="75.010491"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:58:40.695527" elapsed="0.002955"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:40.694512" elapsed="0.003970"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:40.699887" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:58:40.699887" elapsed="0.032657"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:43.734196" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:40.732544" elapsed="3.001652"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:58:43.734734" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T14:58:43.818029" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-4.png"&gt;&lt;img src="selenium-screenshot-4.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T14:58:43.818029" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T14:58:43.734734" elapsed="0.084296">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:58:45.819118" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:58:43.819030" elapsed="2.000088"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T14:58:45.819118" elapsed="2.224125"/>
</kw>
<status status="FAIL" start="2024-10-30T14:58:40.699887" elapsed="7.343356">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T14:58:40.699887" elapsed="7.343356"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:40.694512" elapsed="7.348731"/>
</kw>
<doc>Delete Campaign</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T14:57:25.684021" elapsed="82.359222"/>
</test>
<doc>Testing Camapaign Deletion</doc>
<status status="PASS" start="2024-10-30T14:57:25.632132" elapsed="82.412194"/>
</suite>
<suite id="s1-s3" name="RAC29a TC 119 Search for Campaign By Campaign Id" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_119_Search_for_Campaign_By_Campaign_Id.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.094991" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:58:48.094991" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.096407" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:58:48.094991" elapsed="0.001416"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.096407" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:58:48.096407" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.096407" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:58:48.096407" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.096407" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:58:48.096407" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:58:48.094991" elapsed="0.001416"/>
</kw>
<test id="s1-s3-t1" name="RAC29a_TC_119_FFT_Approval_Search_for_Campaign_By_Campaign_Id" line="38">
<kw name="Validating the Search function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.097411" level="INFO">Set test documentation to:
Search by campaign ID</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T14:58:48.097411" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.098417" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:58:48.097411" elapsed="0.001006"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:58:48.098417" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:58:48.098417" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.099898" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:58:48.099898" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:58:48.099898" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:58:48.099898" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:58:48.099898" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.099898" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T14:58:48.099898" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:58:48.101118" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.130609" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T14:58:48.435249" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T14:58:48.435249" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T14:58:48.101118" elapsed="0.334131"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.435249" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:58:48.435249" elapsed="0.001005"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T14:58:48.435249" elapsed="0.001005"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T14:58:48.099898" elapsed="0.336356"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.436254" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T14:58:48.436254" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:58:48.436254" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:58:48.436254" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:58:48.436254" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:58:48.436254" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.001043"/>
</branch>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.001043"/>
</if>
<status status="NOT RUN" start="2024-10-30T14:58:48.437253" elapsed="0.001043"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.438296" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T14:58:48.438296" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.438296" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E8208AA80&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:58:48.438296" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:58:48.438296" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T14:58:48.438296" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:58:48.438296" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:58:48.438296" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.439341" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T14:58:48.438296" elapsed="0.001045"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:58:48.439341" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:58:48.439341" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:58:48.439341" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:58:48.439341" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:58:48.439341" elapsed="0.000979"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:58:48.440320" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:58:48.440320" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.440320" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:58:48.440320" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T14:58:48.440320" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T14:58:48.097411" elapsed="31.827786"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T14:59:19.925197" elapsed="0.017773"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:19.949082" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.127")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:19.942970" elapsed="0.007141"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:19.950111" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.127")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:19.950111" elapsed="0.031716"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:59:19.982824" elapsed="0.008589"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:19.981827" elapsed="0.009586"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:19.998829" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.128")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:19.992654" elapsed="0.006175"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:59:25.000742" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:59:20.000266" elapsed="5.000476"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T14:59:25.000742" elapsed="0.015735"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:25.016477" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.128")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:25.016477" elapsed="0.082862"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:59:25.100333" elapsed="0.441442"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:25.099339" elapsed="0.442436"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:59:25.541775" elapsed="0.004944"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:25.541775" elapsed="0.004944"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:25.556768" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T14:59:25.546719" elapsed="0.010049"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:19.925197" elapsed="5.631571"/>
</kw>
<kw name="And The user inputs a campaign ID on the Search field" owner="Approvals">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:59:30.557000" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:59:25.556768" elapsed="5.000232"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:30.572162" level="INFO">${Camapaign_ID} = CNQ002v001Q12025</msg>
<var>${Camapaign_ID}</var>
<arg>xpath=//tr[@role='row']/td[2]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:30.557000" elapsed="0.015162"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:59:30.572162" level="INFO">Campaign Name retrieved: CNQ002v001Q12025</msg>
<arg>Campaign Name retrieved: ${Camapaign_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:59:30.572162" elapsed="0.000000"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:30.579148" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.136")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:30.572162" elapsed="0.006986"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:30.579148" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.136")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:30.579148" elapsed="0.027236"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:30.606384" level="INFO">Typing text 'CNQ002v001Q12025' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.136")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${Camapaign_ID}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:30.606384" elapsed="0.065623"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:59:32.673188" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:59:30.672007" elapsed="2.001181"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T14:59:32.673188" level="INFO">${Camapaign_ID} = CNQ002v001Q12025</msg>
<arg>${Camapaign_ID}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T14:59:32.673188" elapsed="0.000000"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T14:59:32.673837" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="734a8caa758e8ebd1e338c383edb356d", element="f.669A967956464B71CEE3C610B41D5B3A.d.44D34CC00BC252D5CE5AC792B66B6827.e.136")&gt;</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T14:59:32.673837" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:25.556768" elapsed="7.117069"/>
</kw>
<kw name="Then The user verifies the search results returned by Campaign ID" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_RESULT_XPATH}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T14:59:32.673837" elapsed="0.012323"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:32.698135" level="INFO">${search_results} = CNQ002v001Q12025</msg>
<var>${search_results}</var>
<arg>xpath=//td[contains(text(), '${Camapaign_ID}')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:32.686160" elapsed="0.011975"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${search_results}</arg>
<arg>${Camapaign_ID}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T14:59:32.698135" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:32.699131" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:59:32.703925" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${SEARCH_RESULT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:32.699131" elapsed="0.004794"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${result_count} &gt; 0</arg>
<arg>msg=No search results found!</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T14:59:32.704945" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:59:32.704945" level="INFO">----Search completed successfully using campaign ID: CNQ002v001Q12025.----</msg>
<arg>----Search completed successfully using campaign ID: ${Camapaign_ID}.----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:59:32.704945" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Search completed successfully using campaign ID: ${Camapaign_ID}.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:59:32.704945" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:32.673837" elapsed="0.031108"/>
</kw>
<arg>Search by campaign ID</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T14:58:48.097411" elapsed="44.607534"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T14:59:32.705930" elapsed="0.005934"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:32.705930" elapsed="0.005934"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:32.712905" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T14:59:32.712905" elapsed="0.034967"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:59:35.748396" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:59:32.747872" elapsed="3.000524"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T14:59:35.748396" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T14:59:35.813781" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-6.png"&gt;&lt;img src="selenium-screenshot-6.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T14:59:35.813781" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T14:59:35.748396" elapsed="0.066463">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T14:59:37.815172" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T14:59:35.814859" elapsed="2.000313"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T14:59:37.815172" elapsed="2.222915"/>
</kw>
<status status="FAIL" start="2024-10-30T14:59:32.711864" elapsed="7.326223">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T14:59:32.711864" elapsed="7.326223"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:32.705930" elapsed="7.332157"/>
</kw>
<doc>Search by campaign ID</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T14:58:48.096407" elapsed="51.942710"/>
</test>
<doc>Testing the Search Funtion on Campaign Approvals: Search by Campaign ID</doc>
<status status="PASS" start="2024-10-30T14:58:48.045335" elapsed="51.994719"/>
</suite>
<suite id="s1-s4" name="RAC29a TC 120 Search for Campaign By Campaign Name" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_120_Search_for_Campaign_By_Campaign_Name.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.095149" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:59:40.095149" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.095149" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:59:40.095149" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.095149" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:59:40.095149" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.095149" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:59:40.095149" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.095149" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T14:59:40.095149" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T14:59:40.094082" elapsed="0.001067"/>
</kw>
<test id="s1-s4-t1" name="RAC29a_TC_120_FFT_Approval_Search_for_Campaign_By_Campaign_Name" line="38">
<kw name="Validating the Search function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.096145" level="INFO">Set test documentation to:
Search by campaign name</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T14:59:40.096145" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.097141" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:59:40.097141" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:59:40.097141" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:59:40.097141" elapsed="0.001001"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.099140" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:59:40.099140" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:59:40.099140" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:59:40.099140" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:59:40.099140" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.099140" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T14:59:40.099140" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T14:59:40.100153" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.121637" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T14:59:40.421128" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T14:59:40.421128" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T14:59:40.100153" elapsed="0.320975"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.422132" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:59:40.422132" elapsed="0.000998"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T14:59:40.422132" elapsed="0.000998"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T14:59:40.098142" elapsed="0.324988"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.423130" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T14:59:40.423130" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:59:40.423130" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:59:40.423130" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T14:59:40.423130" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T14:59:40.423130" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T14:59:40.424125" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-30T14:59:40.423130" elapsed="0.000995"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.425130" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T14:59:40.424125" elapsed="0.001005"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.425130" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E82076690&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:59:40.425130" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T14:59:40.425130" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T14:59:40.425130" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T14:59:40.425130" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T14:59:40.425130" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.426128" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T14:59:40.425130" elapsed="0.000998"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:59:40.426128" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T14:59:40.426128" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T14:59:40.426128" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:59:40.426128" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:59:40.426128" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:59:40.427129" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T14:59:40.427129" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.427129" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T14:59:40.427129" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T14:59:40.427129" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T14:59:40.096145" elapsed="31.907553"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:00:12.003698" elapsed="0.016046"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:12.025281" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.127")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:12.019744" elapsed="0.005537"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:12.025281" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.127")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:12.025281" elapsed="0.026649"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:00:12.052931" elapsed="0.008602"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:12.051930" elapsed="0.009603"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:12.069594" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.128")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:12.061533" elapsed="0.008061"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:00:17.069801" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:00:12.069594" elapsed="5.000207"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:00:17.069801" elapsed="0.013050"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:17.082851" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.128")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:17.082851" elapsed="0.075716"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:00:17.158567" elapsed="0.438482"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:17.158567" elapsed="0.438482"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:00:17.598080" elapsed="0.004168"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:17.598080" elapsed="0.004168"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:17.611703" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:00:17.602248" elapsed="0.009455"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:12.003698" elapsed="5.608005"/>
</kw>
<kw name="And The user inputs a campaign name on the Search field" owner="Approvals">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:00:22.613155" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:00:17.612702" elapsed="5.000453"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:22.628612" level="INFO">${Campaign_Name} = SIT 1 Dup Camp 3</msg>
<var>${Campaign_Name}</var>
<arg>xpath=//td[contains(@class, 'mat-column-campaignName')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:22.613155" elapsed="0.015457"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:00:22.629626" level="INFO">Campaign Name retrieved: SIT 1 Dup Camp 3</msg>
<arg>Campaign Name retrieved: ${Campaign_Name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:00:22.628612" elapsed="0.001014"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:22.635583" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.136")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:22.629626" elapsed="0.005957"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:22.635583" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.136")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:22.635583" elapsed="0.025133"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:22.661715" level="INFO">Typing text 'SIT 1 Dup Camp 3' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.136")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${Campaign_Name}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:22.661715" elapsed="0.059002"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:00:24.721873" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:00:22.721716" elapsed="2.000157"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T15:00:24.722474" level="INFO">${Campaign_Name} = SIT 1 Dup Camp 3</msg>
<arg>${Campaign_Name}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T15:00:24.721873" elapsed="0.000601"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T15:00:24.722474" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="ff490c11a3b4e5cdec1402414937c98b", element="f.9C6BE31799DFC9036F896FA700866DD4.d.6F8DC960DB657335AA31959064102BE5.e.136")&gt;</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T15:00:24.722474" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:17.611703" elapsed="7.110771"/>
</kw>
<kw name="Then The user verifies the search results returned by Campaign Name" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_RESULT_XPATH}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:00:24.722474" elapsed="0.014244"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:24.751033" level="INFO">${search_results} = SIT 1 Dup Camp 3</msg>
<var>${search_results}</var>
<arg>xpath=//td[contains(text(), '${Campaign_Name}')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:24.736718" elapsed="0.014315"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${search_results}</arg>
<arg>${Campaign_Name}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:00:24.751033" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:24.751033" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:00:24.756737" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${SEARCH_RESULT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:24.751033" elapsed="0.005704"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${result_count} &gt; 0</arg>
<arg>msg=No search results found!</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T15:00:24.756737" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:00:24.756737" level="INFO">----Search completed successfully using campaign name: SIT 1 Dup Camp 3.----</msg>
<arg>----Search completed successfully using campaign name: ${Campaign_Name}.----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:00:24.756737" elapsed="0.001006"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Search completed successfully using campaign name: ${Campaign_Name}.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:00:24.757743" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:24.722474" elapsed="0.035269"/>
</kw>
<arg>Search by campaign name</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T14:59:40.096145" elapsed="44.661598"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:00:24.758680" elapsed="0.004750"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:24.758680" elapsed="0.004750"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:24.764469" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:00:24.763430" elapsed="0.033027"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:00:27.797484" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:00:24.797366" elapsed="3.000118"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:00:27.797484" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:00:27.860781" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-8.png"&gt;&lt;img src="selenium-screenshot-8.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:00:27.860781" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:00:27.797484" elapsed="0.064294">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:00:29.863219" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:00:27.861778" elapsed="2.001441"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:00:29.863219" elapsed="2.223152"/>
</kw>
<status status="FAIL" start="2024-10-30T15:00:24.763430" elapsed="7.322941">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:00:24.763430" elapsed="7.322941"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:24.757743" elapsed="7.328628"/>
</kw>
<doc>Search by campaign name</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T14:59:40.096145" elapsed="51.990226"/>
</test>
<doc>Testing the Search Funtion on Campaign Approvals: Search by Campaign Name</doc>
<status status="PASS" start="2024-10-30T14:59:40.041055" elapsed="52.045316"/>
</suite>
<suite id="s1-s5" name="RAC29a TC 121 Search for Campaign By Approved By" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_121_Search_for_Campaign_By_Approved_By.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.137148" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:00:32.137148" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.137148" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:00:32.137148" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.137148" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:00:32.137148" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.138170" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:00:32.137148" elapsed="0.001022"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.138170" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:00:32.138170" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:00:32.137148" elapsed="0.001022"/>
</kw>
<test id="s1-s5-t1" name="RAC29a_TC_121_FFT_Approval_Search_for_Campaign_By_Approved_By" line="38">
<kw name="Validating the Search function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.139147" level="INFO">Set test documentation to:
Search by Approved By user</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T15:00:32.138170" elapsed="0.000977"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.139147" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:00:32.139147" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:00:32.139147" elapsed="0.001000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:00:32.140147" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.141202" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:00:32.141202" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:00:32.141202" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:00:32.141202" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:00:32.141202" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.142206" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T15:00:32.141202" elapsed="0.001004"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:00:32.142206" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.162635" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T15:00:32.439662" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T15:00:32.439662" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T15:00:32.142206" elapsed="0.297456"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.440673" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:00:32.440673" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:00:32.439662" elapsed="0.001011"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T15:00:32.141202" elapsed="0.299471"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.441668" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T15:00:32.440673" elapsed="0.000995"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:00:32.441668" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:00:32.441668" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:00:32.441668" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:00:32.441668" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:00:32.441668" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:00:32.441668" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:00:32.441668" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:00:32.442666" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:00:32.441668" elapsed="0.000998"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:00:32.442666" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:00:32.442666" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T15:00:32.441668" elapsed="0.000998"/>
</if>
<status status="NOT RUN" start="2024-10-30T15:00:32.441668" elapsed="0.000998"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.442666" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:00:32.442666" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.442666" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E820AD6D0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:00:32.442666" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:00:32.442666" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T15:00:32.443666" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:00:32.442666" elapsed="0.001000"/>
</branch>
<status status="PASS" start="2024-10-30T15:00:32.442666" elapsed="0.001000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.443666" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T15:00:32.443666" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:00:32.443666" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:00:32.443666" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:00:32.443666" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:00:32.443666" elapsed="0.001000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:00:32.444666" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:00:32.444666" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:00:32.444666" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.444666" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:00:32.444666" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T15:00:32.445666" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T15:00:32.139147" elapsed="31.564710"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:01:03.705207" elapsed="0.016414"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:03.727621" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.126")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:03.722621" elapsed="0.005000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:03.727621" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.126")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:03.727621" elapsed="0.026248"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:01:03.753869" elapsed="0.008373"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:03.753869" elapsed="0.008373"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:03.768271" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.127")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:03.762242" elapsed="0.006029"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:08.768412" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:03.768271" elapsed="5.000141"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:01:08.768412" elapsed="0.013268"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:08.783160" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.127")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:08.783160" elapsed="0.078921"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:01:08.862081" elapsed="0.439148"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:08.862081" elapsed="0.439148"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:01:09.301229" elapsed="0.004944"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:09.301229" elapsed="0.004944"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:09.314968" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:01:09.306173" elapsed="0.008795"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:03.705207" elapsed="5.609761"/>
</kw>
<kw name="And The user inputs an Approved By user on the Search field" owner="Approvals">
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${dropdown_xpath}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:09.314968" elapsed="0.011642"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:09.326610" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${dropdown_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:09.326610" elapsed="0.032066"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:09.359545" level="INFO">Clicking element '//span[contains(text(), '100')]'.</msg>
<arg>${option_100_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:09.358676" elapsed="0.044679"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:14.403785" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:09.403355" elapsed="5.000430"/>
</kw>
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${APPROVED_BY_XPATH}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:14.403785" elapsed="0.012245"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:14.428781" level="INFO">${Approved_By_User} = Yaash Ramsahar (ZA)</msg>
<var>${Approved_By_User}</var>
<arg>${APPROVED_BY_XPATH}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:14.416030" elapsed="0.012751"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:01:14.428781" level="INFO">Campaign Name retrieved: Yaash Ramsahar (ZA)</msg>
<arg>Campaign Name retrieved: ${Approved_By_User}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:01:14.428781" elapsed="0.000000"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:14.435366" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.135")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:14.428781" elapsed="0.006585"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:16.436912" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:14.436343" elapsed="2.000569"/>
</kw>
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${Search_Bar_XPATH}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:16.436912" elapsed="0.008007"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:19.445382" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:16.444919" elapsed="3.000463"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:19.445926" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.135")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:19.445382" elapsed="0.024702"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:19.470084" level="INFO">Typing text 'Yaash Ramsahar (ZA)' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.135")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${Approved_By_User}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:19.470084" elapsed="0.059421"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:21.529926" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:19.529505" elapsed="2.000421"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T15:01:21.529926" level="INFO">${Approved_By_User} = Yaash Ramsahar (ZA)</msg>
<arg>${Approved_By_User}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T15:01:21.529926" elapsed="0.000000"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-30T15:01:21.529926" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="812dc3112c6f69fb2243476615a45118", element="f.56CC04DA03ABD446D7A4110E18185EC5.d.630F5A65B78978E91EE69DD2B28E66CE.e.135")&gt;</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-30T15:01:21.529926" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:09.314968" elapsed="12.214958"/>
</kw>
<kw name="Then The user verifies the search results returned by the Approved By user" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_RESULT_XPATH}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:01:21.531039" elapsed="0.013895"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:21.556412" level="INFO">${search_results} = Yaash Ramsahar (ZA)</msg>
<var>${search_results}</var>
<arg>xpath=//tr[contains(@class, 'mat-row')]//td[contains(@class, 'mat-column-ApprovedBy') and normalize-space(text())='${Approved_By_User}']</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:21.544934" elapsed="0.011478"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${search_results}</arg>
<arg>${Approved_By_User}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:01:21.556412" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:21.556412" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:01:21.561460" level="INFO">${result_count} = 3</msg>
<var>${result_count}</var>
<arg>${SEARCH_RESULT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:21.556412" elapsed="0.005048"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${result_count} &gt; 0</arg>
<arg>msg=No search results found!</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T15:01:21.561460" elapsed="0.001004"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:01:21.562464" level="INFO">----Search completed successfully using campaign name: Yaash Ramsahar (ZA).----</msg>
<arg>----Search completed successfully using campaign name: ${Approved_By_User}.----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:01:21.562464" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Search completed successfully using campaign name: ${Approved_By_User}.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:01:21.562464" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:21.529926" elapsed="0.032538"/>
</kw>
<arg>Search by Approved By user</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T15:00:32.138170" elapsed="49.424294"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:01:21.563891" elapsed="0.005563"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:21.562464" elapsed="0.006990"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:21.570461" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:01:21.569454" elapsed="0.037802"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:24.607940" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:21.607256" elapsed="3.000684"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:01:24.607940" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:01:24.674034" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-10.png"&gt;&lt;img src="selenium-screenshot-10.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:01:24.674034" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:01:24.607940" elapsed="0.066094">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:01:26.675484" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:01:24.675377" elapsed="2.000107"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:01:26.675484" elapsed="2.209124"/>
</kw>
<status status="FAIL" start="2024-10-30T15:01:21.569454" elapsed="7.315154">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:01:21.569454" elapsed="7.315154"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:21.562464" elapsed="7.322144"/>
</kw>
<doc>Search by Approved By user</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T15:00:32.138170" elapsed="56.746438"/>
</test>
<doc>Testing the Search Funtion on Campaign Approvals: Search by Approved By user</doc>
<status status="PASS" start="2024-10-30T15:00:32.087854" elapsed="56.797752"/>
</suite>
<suite id="s1-s6" name="RAC29a TC 122 Show maximum campaign feature" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_122_Show_maximum_campaign_feature.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:28.940451" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:01:28.940451" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:28.940451" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:01:28.940451" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:28.940451" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:01:28.940451" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:28.940451" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:01:28.940451" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:28.941452" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:01:28.941452" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:01:28.939455" elapsed="0.001997"/>
</kw>
<test id="s1-s6-t1" name="RAC29a_TC_122_Show_maximum_campaign_feature" line="36">
<kw name="Validating the items per page feature on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T15:01:28.942455" level="INFO">Set test documentation to:
Items per page feature</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T15:01:28.941452" elapsed="0.001003"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:01:28.942455" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:01:28.942455" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:01:28.942455" elapsed="0.001000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:01:28.943455" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:01:28.944484" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:01:28.944484" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:01:28.944484" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:01:28.944484" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:01:28.944484" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T15:01:28.944484" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T15:01:28.944484" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:01:28.944484" elapsed="0.000972"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T15:01:28.960062" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T15:01:29.244617" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T15:01:29.244617" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T15:01:28.945456" elapsed="0.299161"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:01:29.245611" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:01:29.245611" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:01:29.244617" elapsed="0.001993"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T15:01:28.944484" elapsed="0.302126"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:29.246610" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T15:01:29.246610" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:01:29.246610" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:01:29.246610" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:01:29.246610" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:01:29.246610" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T15:01:29.247622" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-30T15:01:29.246610" elapsed="0.001012"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:01:29.247622" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:01:29.247622" elapsed="0.000989"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:01:29.248611" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E820AF0E0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:01:29.248611" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:01:29.248611" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T15:01:29.248611" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:01:29.248611" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:01:29.248611" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T15:01:29.248611" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T15:01:29.248611" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:01:29.248611" elapsed="0.001003"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:01:29.249614" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:01:29.249614" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:01:29.249614" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:01:29.249614" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:01:29.249614" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:01:29.249614" elapsed="0.000995"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:01:29.250609" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:01:29.250609" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T15:01:29.250609" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T15:01:28.942455" elapsed="31.939439"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:02:00.881894" elapsed="0.019444"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:00.907992" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="f2dcfb58ecf27e09e1dfb470ae015443", element="f.0E1FF3064B56BBABC123926CC88ADAFA.d.73F48EC76B49DA850229C7597F73769C.e.127")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:00.902487" elapsed="0.005505"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:00.907992" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="f2dcfb58ecf27e09e1dfb470ae015443", element="f.0E1FF3064B56BBABC123926CC88ADAFA.d.73F48EC76B49DA850229C7597F73769C.e.127")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:00.907992" elapsed="0.033883"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:02:00.942893" elapsed="0.008005"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:00.941875" elapsed="0.009023"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:00.959795" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="f2dcfb58ecf27e09e1dfb470ae015443", element="f.0E1FF3064B56BBABC123926CC88ADAFA.d.73F48EC76B49DA850229C7597F73769C.e.128")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:00.950898" elapsed="0.008897"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:05.960876" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:00.960797" elapsed="5.000079"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:02:05.960876" elapsed="0.015696"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:05.976572" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="f2dcfb58ecf27e09e1dfb470ae015443", element="f.0E1FF3064B56BBABC123926CC88ADAFA.d.73F48EC76B49DA850229C7597F73769C.e.128")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:05.976572" elapsed="0.074952"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:02:06.052672" elapsed="0.436157"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:06.052672" elapsed="0.436157"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:02:06.488829" elapsed="0.005370"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:06.488829" elapsed="0.005370"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:06.503691" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:02:06.494199" elapsed="0.009492"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:00.881894" elapsed="5.621797"/>
</kw>
<kw name="And The user filters campaigns with items per page feature" owner="Approvals">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:02:06.504692" elapsed="0.292166"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:02:06.796858" level="INFO">${selected_value_xpath} = //span[contains(@class, 'mat-select-min-line') and text()='10']</msg>
<var>${selected_value_xpath}</var>
<arg>//span[contains(@class, 'mat-select-min-line') and text()='10']</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:02:06.796858" elapsed="0.000000"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:02:06.796858" elapsed="0.272415"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${selected_value_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:02:07.069273" elapsed="0.013059"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:07.094586" level="INFO">${selected_value} = 10</msg>
<var>${selected_value}</var>
<arg>${selected_value_xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:07.082332" elapsed="0.012254"/>
</kw>
<kw name="Should Be Equal As Strings" owner="BuiltIn">
<arg>${selected_value}</arg>
<arg>10</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" start="2024-10-30T15:02:07.094586" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:02:07.094586" level="INFO">---- Default value is correctly set to 10 ----</msg>
<arg>---- Default value is correctly set to 10 ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:07.094586" elapsed="0.000000"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:02:07.094586" elapsed="0.264333"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:07.358919" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:07.358919" elapsed="0.029649"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:08.388825" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:07.388568" elapsed="1.000257"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:02:08.389330" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='5']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:02:08.388825" elapsed="0.000505"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:02:08.389330" elapsed="0.012385"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:08.401715" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='5']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:08.401715" elapsed="0.030687"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:10.432660" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:08.432402" elapsed="2.000258"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:10.439768" level="INFO">${total_row_count} = 6</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:10.432660" elapsed="0.007108"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:10.439768" level="INFO">${actual_row_count} = 5</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:10.439768" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T15:02:10.439768" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:02:10.440762" level="INFO">---- Verified 5 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:10.440762" elapsed="0.000000"/>
</kw>
<var name="${option}">5</var>
<status status="PASS" start="2024-10-30T15:02:07.094586" elapsed="3.346176"/>
</iter>
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:02:10.440762" elapsed="0.266071"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:10.707976" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:10.707976" elapsed="0.029410"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:11.738830" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:10.738591" elapsed="1.000239"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:02:11.739836" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='10']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:02:11.738830" elapsed="0.001006"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:02:11.739836" elapsed="0.015183"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:11.755019" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='10']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:11.755019" elapsed="0.038976"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:13.795175" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:11.793995" elapsed="2.001180"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:13.801184" level="INFO">${total_row_count} = 11</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:13.795175" elapsed="0.006009"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:13.801184" level="INFO">${actual_row_count} = 10</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:13.801184" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T15:02:13.801184" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:02:13.802487" level="INFO">---- Verified 10 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:13.801184" elapsed="0.001303"/>
</kw>
<var name="${option}">10</var>
<status status="PASS" start="2024-10-30T15:02:10.440762" elapsed="3.361725"/>
</iter>
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:02:13.802487" elapsed="0.270823"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:14.073310" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:14.073310" elapsed="0.027497"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:15.100998" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:14.100807" elapsed="1.000191"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:02:15.102028" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='25']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:02:15.100998" elapsed="0.001030"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:02:15.102028" elapsed="0.013993"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:15.117019" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='25']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:15.116021" elapsed="0.043473"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:17.159764" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:15.159494" elapsed="2.000270"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:17.165543" level="INFO">${total_row_count} = 24</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:17.159764" elapsed="0.005779"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:17.165543" level="INFO">${actual_row_count} = 23</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:17.165543" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T15:02:17.166686" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:02:17.166686" level="INFO">---- Verified 25 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:17.166686" elapsed="0.000000"/>
</kw>
<var name="${option}">25</var>
<status status="PASS" start="2024-10-30T15:02:13.802487" elapsed="3.364199"/>
</iter>
<iter>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>${DROPDOWN_XPATH}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:02:17.166686" elapsed="0.279301"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:17.445987" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${DROPDOWN_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:17.445987" elapsed="0.023108"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:18.470584" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:17.469095" elapsed="1.001489"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:02:18.471635" level="INFO">${option_xpath} = //mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='100']</msg>
<var>${option_xpath}</var>
<arg>${OPTION_BASE_XPATH.format(${option})}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:02:18.470584" elapsed="0.001051"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${option_xpath}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:02:18.471635" elapsed="0.011581"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:18.484334" level="INFO">Clicking element '//mat-option//span[contains(@class, 'mat-option-text') and normalize-space(text())='100']'.</msg>
<arg>${option_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:18.483216" elapsed="0.026813"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:20.511468" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:18.510029" elapsed="2.001439"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:20.516363" level="INFO">${total_row_count} = 24</msg>
<var>${total_row_count}</var>
<arg>${ROW_COUNT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:20.511468" elapsed="0.004895"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:20.517544" level="INFO">${actual_row_count} = 23</msg>
<var>${actual_row_count}</var>
<arg>${total_row_count} - 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:20.517544" elapsed="0.000000"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${actual_row_count} &lt;= ${option}</arg>
<arg>Expected ${option} items but found ${actual_row_count}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-30T15:02:20.517544" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:02:20.518049" level="INFO">---- Verified 100 items per page successfully ----</msg>
<arg>---- Verified ${option} items per page successfully ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:20.518049" elapsed="0.000000"/>
</kw>
<var name="${option}">100</var>
<status status="PASS" start="2024-10-30T15:02:17.166686" elapsed="3.351363"/>
</iter>
<var>${option}</var>
<value>@{OPTIONS}</value>
<status status="PASS" start="2024-10-30T15:02:07.094586" elapsed="13.423463"/>
</for>
<status status="PASS" start="2024-10-30T15:02:06.504692" elapsed="14.013357"/>
</kw>
<arg>Items per page feature</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T15:01:28.941452" elapsed="51.577100"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:02:20.518552" elapsed="0.003893"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:20.518552" elapsed="0.003893"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:20.523527" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:02:20.523527" elapsed="0.027782"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:23.552998" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:20.552739" elapsed="3.000259"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:02:23.553532" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:02:23.623752" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-12.png"&gt;&lt;img src="selenium-screenshot-12.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:02:23.623752" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:02:23.552998" elapsed="0.071751">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:02:25.624844" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:02:23.624749" elapsed="2.000095"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:02:25.624844" elapsed="2.199057"/>
</kw>
<status status="FAIL" start="2024-10-30T15:02:20.522445" elapsed="7.301456">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:02:20.522445" elapsed="7.301456"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:20.518552" elapsed="7.305349"/>
</kw>
<doc>Items per page feature</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T15:01:28.941452" elapsed="58.882449"/>
</test>
<doc>Testing teh istems per page feature</doc>
<status status="PASS" start="2024-10-30T15:01:28.886539" elapsed="58.938362"/>
</suite>
<suite id="s1-s7" name="RAC29a TC 123 Preview Campaign" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_123_Preview_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:27.873327" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:02:27.873327" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:27.873327" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:02:27.873327" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:27.873327" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:02:27.873327" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:27.874648" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:02:27.874648" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:27.874648" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:02:27.874648" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:02:27.873327" elapsed="0.001321"/>
</kw>
<test id="s1-s7-t1" name="RAC29a_TC_123_FFT_Preview_Campaign" line="36">
<kw name="Validating the Preview function on Approval Page preview">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T15:02:27.874648" level="INFO">Set test documentation to:
Close Button on Campaign Approvals Preview</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T15:02:27.874648" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:27.876142" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:27.876142" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:02:27.876142" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:02:27.876142" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:27.878465" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:27.878465" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:02:27.878465" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:02:27.878465" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:02:27.878465" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T15:02:27.878465" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T15:02:27.878465" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:02:27.878465" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T15:02:27.901060" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T15:02:28.200313" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T15:02:28.200313" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T15:02:27.878465" elapsed="0.321848"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:02:28.201314" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:28.201314" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:02:28.201314" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T15:02:27.877454" elapsed="0.323860"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:28.202316" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T15:02:28.202316" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:02:28.202316" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:02:28.202316" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:02:28.202316" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:02:28.202316" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:02:28.202316" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-30T15:02:28.202316" elapsed="0.001000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:02:28.203316" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:02:28.203316" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:28.203316" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E825C7D40&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:28.203316" elapsed="0.000999"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:02:28.204315" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T15:02:28.204315" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:02:28.204315" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:02:28.204315" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T15:02:28.204315" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T15:02:28.204315" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:02:28.204315" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:02:28.204315" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:02:28.205319" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:02:28.205319" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:02:28.205319" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:02:28.205319" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:02:28.205319" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:02:28.205319" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:02:28.205319" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T15:02:28.206315" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T15:02:27.874648" elapsed="32.318355"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:05.371406" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-14.png"&gt;&lt;img src="selenium-screenshot-14.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:03:05.371406" level="FAIL">Element 'xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]' not visible after 5 seconds.</msg>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2024-10-30T15:03:00.193003" elapsed="5.189380">Element 'xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]' not visible after 5 seconds.</status>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.382383" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.382383" elapsed="0.000000"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<status status="NOT RUN" start="2024-10-30T15:03:05.382383" elapsed="0.000000"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2024-10-30T15:03:00.193003" elapsed="5.190380">Element 'xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]' not visible after 5 seconds.</status>
</kw>
<kw name="Then The user navigates to the Campaign Approvals page and previews a single campaign" owner="Approvals">
<status status="NOT RUN" start="2024-10-30T15:03:05.383383" elapsed="0.000000"/>
</kw>
<arg>Close Button on Campaign Approvals Preview</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="FAIL" start="2024-10-30T15:02:27.874648" elapsed="37.508735">Element 'xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]' not visible after 5 seconds.</status>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:03:05.384518" elapsed="0.005988"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:05.384518" elapsed="0.005988"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:05.391506" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<msg time="2024-10-30T15:03:05.455291" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-15.png"&gt;&lt;img src="selenium-screenshot-15.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:03:05.455291" level="FAIL">Element with locator 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]' not found.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:03:05.391506" elapsed="0.064894">Element with locator 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:03:08.456668" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:03:05.456400" elapsed="3.000268"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:08.456668" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:03:08.519884" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-16.png"&gt;&lt;img src="selenium-screenshot-16.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:03:08.519884" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:03:08.456668" elapsed="0.063216">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:03:10.521315" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:03:08.519884" elapsed="2.001431"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:03:10.521315" elapsed="2.179714"/>
</kw>
<status status="FAIL" start="2024-10-30T15:03:05.391506" elapsed="7.309523">Several failures occurred:

1) Element with locator 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]' not found.

2) Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:03:05.391506" elapsed="7.309523"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:05.384518" elapsed="7.316511"/>
</kw>
<doc>Close Button on Campaign Approvals Preview</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="FAIL" start="2024-10-30T15:02:27.874648" elapsed="44.826381">Element 'xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]' not visible after 5 seconds.</status>
</test>
<doc>Testing the Preview function on Campaign Approvals Page</doc>
<status status="FAIL" start="2024-10-30T15:02:27.826362" elapsed="44.875652"/>
</suite>
<suite id="s1-s8" name="RAC29a TC 124 Preview Campaign Close Button" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_124_Preview_Campaign_Close_Button.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:12.751465" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:03:12.750444" elapsed="0.001021"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:12.751465" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:03:12.751465" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:12.751465" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:03:12.751465" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:12.751465" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:03:12.751465" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:12.752459" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:03:12.752459" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:12.750444" elapsed="0.002015"/>
</kw>
<test id="s1-s8-t1" name="RAC29a_TC_124_FFT_Preview_Campaign_Close_Button" line="36">
<kw name="Validating the Close Button on Approval Page preview">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T15:03:12.752459" level="INFO">Set test documentation to:
Close Button on Campaign Approvals Preview</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T15:03:12.752459" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:12.753461" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:12.753461" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:03:12.753461" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:03:12.753461" elapsed="0.000981"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:12.755580" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:12.755580" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:12.756613" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:03:12.755580" elapsed="0.001033"/>
</branch>
<status status="PASS" start="2024-10-30T15:03:12.755580" elapsed="0.001033"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T15:03:12.756613" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T15:03:12.756613" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:03:12.756613" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T15:03:12.775422" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T15:03:13.030724" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T15:03:13.030724" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T15:03:12.756613" elapsed="0.274111"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:03:13.030724" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:03:13.030724" elapsed="0.001113"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:03:13.030724" elapsed="0.001113"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T15:03:12.755580" elapsed="0.276257"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:13.031837" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T15:03:13.031837" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:03:13.031837" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:03:13.031837" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:03:13.032835" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:03:13.032835" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:13.033726" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000891"/>
</branch>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000891"/>
</if>
<status status="NOT RUN" start="2024-10-30T15:03:13.032835" elapsed="0.000891"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:03:13.033726" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:03:13.033726" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:13.033726" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E825DD640&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:13.033726" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:13.033726" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T15:03:13.033726" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:03:13.033726" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:03:13.033726" elapsed="0.001002"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T15:03:13.034728" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T15:03:13.034728" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:03:13.034728" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:03:13.034728" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:03:13.034728" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:03:13.034728" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:03:13.034728" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:03:13.035837" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:03:13.035837" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:13.035837" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:13.035837" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T15:03:13.035837" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T15:03:12.752459" elapsed="32.511350"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:03:45.263809" elapsed="0.015766"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:45.284551" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="e12efbe9ebeb0050862a06baca4a76f8", element="f.7B962B174C2F05DE079E2D93A688C741.d.75D4F239C6A4B53552FDFE42A102696E.e.64")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:45.279575" elapsed="0.004976"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:45.284551" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="e12efbe9ebeb0050862a06baca4a76f8", element="f.7B962B174C2F05DE079E2D93A688C741.d.75D4F239C6A4B53552FDFE42A102696E.e.64")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:45.284551" elapsed="0.026013"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:03:45.310564" elapsed="0.008381"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:45.310564" elapsed="0.008381"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:45.326936" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="e12efbe9ebeb0050862a06baca4a76f8", element="f.7B962B174C2F05DE079E2D93A688C741.d.75D4F239C6A4B53552FDFE42A102696E.e.65")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:45.319935" elapsed="0.007001"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:03:50.327437" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:03:45.326936" elapsed="5.000501"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:03:50.327437" elapsed="0.014041"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:50.341478" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="e12efbe9ebeb0050862a06baca4a76f8", element="f.7B962B174C2F05DE079E2D93A688C741.d.75D4F239C6A4B53552FDFE42A102696E.e.65")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:50.341478" elapsed="0.085144"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:03:50.426622" elapsed="0.228621"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:50.426622" elapsed="0.228621"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:03:50.656242" elapsed="0.006985"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:50.655243" elapsed="0.007984"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:50.680315" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:03:50.664229" elapsed="0.016086"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:45.263809" elapsed="5.416506"/>
</kw>
<kw name="Then The user previews and validates the Close Button on Approval Preview" owner="Approvals">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:03:50.680857" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:03:50.680857" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:03:50.680857" level="INFO">${TOTAL_CAMPAIGNS} = 0</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:03:50.680857" elapsed="0.000000"/>
</kw>
<while condition="${more_pages} == True">
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:50.689902" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:50.681902" elapsed="0.008000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:50.690932" level="INFO">${TOTAL_CAMPAIGNS} = 10</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:50.690932" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:50.690932" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:50.690932" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:03:50.691914" level="INFO">Current page campaigns: 10 | Total so far: 10</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:03:50.690932" elapsed="0.000982"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:50.692922" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:50.691914" elapsed="0.071640"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:03:50.764505" elapsed="0.312167"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:03:51.076672" elapsed="0.013743"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:03:56.090629" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:03:51.090415" elapsed="5.000214"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:56.090629" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:56.090629" elapsed="0.044601"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CLOSE BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:03:56.135230" elapsed="0.001371"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-30T15:03:50.691914" elapsed="5.444687"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-30T15:03:50.691914" elapsed="5.444687"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:03:56.136601" elapsed="0.018654"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-30T15:03:56.172433" level="INFO">${is_disabled} = None</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:03:56.156255" elapsed="0.016178"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:03:56.173433" level="INFO">${is_enabled} = True</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:03:56.173433" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:03:56.173433" level="INFO">Next page button enabled: True</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:03:56.173433" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-30T15:03:56.173433" elapsed="0.000999"/>
</return>
<msg time="2024-10-30T15:03:56.174432" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-30T15:03:56.136601" elapsed="0.037831"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:03:56.174432" elapsed="0.016524"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:01.192304" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:03:56.191957" elapsed="5.000347"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:01.192304" elapsed="0.284488"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:01.476792" elapsed="0.284329"/>
</kw>
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:01.476792" elapsed="0.284329"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Next Page Button" owner="Approvals">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:01.762595" level="INFO">Executing JavaScript:
document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()
Without any arguments.</msg>
<arg>document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:01.762595" elapsed="0.016155"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:01.762595" elapsed="0.016155"/>
</kw>
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:01.761121" elapsed="0.017629"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:03.780143" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:01.779883" elapsed="2.000260"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:50.680857" elapsed="13.099286"/>
</iter>
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:03.785098" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:03.780143" elapsed="0.004955"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:03.786472" level="INFO">${TOTAL_CAMPAIGNS} = 20</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:03.786472" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:03.786472" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:03.786472" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:03.786472" level="INFO">Current page campaigns: 10 | Total so far: 20</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:03.786472" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:03.787478" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:03.786472" elapsed="0.193887"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:03.980359" elapsed="0.309855"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:04:04.291707" elapsed="0.011991"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:09.304157" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:04.303698" elapsed="5.000459"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:09.304157" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:09.304157" elapsed="0.053368"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CLOSE BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:09.357525" elapsed="0.000000"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-30T15:04:03.786472" elapsed="5.572059"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-30T15:04:03.786472" elapsed="5.572059"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:04:09.358531" elapsed="0.013906"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:09.384430" level="INFO">${is_disabled} = None</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:09.372437" elapsed="0.011993"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:09.385412" level="INFO">${is_enabled} = True</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:09.385412" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:09.385412" level="INFO">Next page button enabled: True</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:09.385412" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-30T15:04:09.385412" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:04:09.386413" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-30T15:04:09.358531" elapsed="0.027882"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:04:09.386413" elapsed="0.013080"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:14.400551" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:09.400459" elapsed="5.000092"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:14.400551" elapsed="0.290414"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:14.690965" elapsed="0.274684"/>
</kw>
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:14.690965" elapsed="0.275843"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Next Page Button" owner="Approvals">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:14.966808" level="INFO">Executing JavaScript:
document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()
Without any arguments.</msg>
<arg>document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:14.966808" elapsed="0.010922"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:14.966808" elapsed="0.010922"/>
</kw>
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:14.966808" elapsed="0.010922"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:16.977765" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:14.977730" elapsed="2.000035"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:03.780143" elapsed="13.197622"/>
</iter>
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:16.983608" level="INFO">${campaigns} = 3</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:16.977765" elapsed="0.005843"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:16.984637" level="INFO">${TOTAL_CAMPAIGNS} = 23</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:16.984637" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:16.984637" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:16.984637" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:16.984637" level="INFO">Current page campaigns: 3 | Total so far: 23</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:16.984637" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:16.985607" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:16.984637" elapsed="0.331732"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:17.316369" elapsed="0.302489"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:04:17.618858" elapsed="0.012420"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:22.631546" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:17.631278" elapsed="5.000268"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:22.631546" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:22.631546" elapsed="0.041522"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----CLOSE BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:22.673068" elapsed="0.001009"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-30T15:04:16.984637" elapsed="5.689440"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-30T15:04:16.984637" elapsed="5.689440"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:04:22.674077" elapsed="0.013209"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:22.698285" level="INFO">${is_disabled} = true</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:22.687286" elapsed="0.010999"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:22.698285" level="INFO">${is_enabled} = False</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:22.698285" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:22.699274" level="INFO">Next page button enabled: False</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:22.698285" elapsed="0.000989"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-30T15:04:22.699274" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:04:22.699274" level="INFO">${more_pages} = False</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-30T15:04:22.674077" elapsed="0.025197"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:04:22.699274" elapsed="0.011423"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:27.711157" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:22.710697" elapsed="5.000460"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:04:27.711157" elapsed="0.269156"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:27.981320" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:27.981320" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:29.981590" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:27.981320" elapsed="2.000270"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:16.977765" elapsed="13.003825"/>
</iter>
<status status="PASS" start="2024-10-30T15:03:50.680857" elapsed="39.300733"/>
</while>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:29.981590" level="INFO">Total campaigns identified on the front-end: 23</msg>
<arg>Total campaigns identified on the front-end: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:29.981590" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:29.981590" elapsed="0.001138"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:04:29.982728" level="INFO">${db_type} = 'MYSQL'</msg>
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:04:29.982728" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:04:29.982728" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY} = SELECT COUNT(*) FROM ATM_Marketing.Campaign WHERE isActive = '1';</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:04:29.982728" elapsed="0.000000"/>
</kw>
<kw name="Execute SQL Query" owner="DBUtility">
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-30T15:04:29.983603" level="INFO">${return_data} = True</msg>
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-30T15:04:29.983603" elapsed="0.000000"/>
</kw>
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-30T15:04:29.983603" level="INFO">${return_all} = False</msg>
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-30T15:04:29.983603" elapsed="0.000000"/>
</kw>
<kw name="Verify Data Using Database" owner="DatabaseUtility">
<msg time="2024-10-30T15:04:30.422584" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg time="2024-10-30T15:04:30.422584" level="INFO">${data_found} = {'COUNT(*)': 23}</msg>
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<status status="PASS" start="2024-10-30T15:04:29.983603" elapsed="0.438981"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:04:30.422584" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:04:30.422584" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:04:30.423584" elapsed="0.000000"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" start="2024-10-30T15:04:30.423584" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:04:30.423584" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = {'COUNT(*)': 23}</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${db_type}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<arg>True</arg>
<status status="PASS" start="2024-10-30T15:04:29.983603" elapsed="0.439981"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2024-10-30T15:04:30.424585" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = 23</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<arg>COUNT(*)</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2024-10-30T15:04:30.424585" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:30.424585" level="INFO">Total campaigns on the approval list (database): 23</msg>
<arg>Total campaigns on the approval list (database): ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:30.424585" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns on the database approval list: ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:30.424585" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:30.424585" level="INFO">Test completed. The Close button on the approval preview functions as expected.</msg>
<arg>Test completed. The Close button on the approval preview functions as expected.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:30.424585" elapsed="0.000999"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Test completed. The Close button on the approval preview functions as expected.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:30.425584" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${TOTAL_CAMPAIGNS}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2024-10-30T15:04:30.425584" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----The front-end and database campaign counts match----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:30.425584" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:03:50.680857" elapsed="39.744727"/>
</kw>
<arg>Close Button on Campaign Approvals Preview</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T15:03:12.752459" elapsed="77.673125"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:04:30.427134" elapsed="0.008225"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:30.426601" elapsed="0.008758"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:30.435359" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:04:30.435359" elapsed="0.035660"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:33.471462" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:30.471019" elapsed="3.000443"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:04:33.471462" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:04:33.528564" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-18.png"&gt;&lt;img src="selenium-screenshot-18.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:04:33.528564" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:04:33.471462" elapsed="0.058607">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:04:35.530209" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:04:33.530069" elapsed="2.000140"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:04:35.530209" elapsed="2.207227"/>
</kw>
<status status="FAIL" start="2024-10-30T15:04:30.435359" elapsed="7.302077">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:04:30.435359" elapsed="7.302077"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:30.426601" elapsed="7.310835"/>
</kw>
<doc>Close Button on Campaign Approvals Preview</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T15:03:12.752459" elapsed="84.984977"/>
</test>
<doc>Testing the Close Button on Campaign Approvals Preview</doc>
<status status="PASS" start="2024-10-30T15:03:12.703029" elapsed="85.035331"/>
</suite>
<suite id="s1-s9" name="RAC29a TC 125 Preview Campaign Next Button" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_125_Preview_Campaign_Next_Button.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:37.787358" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:04:37.787358" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:37.787358" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:04:37.787358" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:37.788336" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:04:37.787358" elapsed="0.000978"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:37.788336" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:04:37.788336" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:37.788336" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:04:37.788336" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:04:37.787358" elapsed="0.000978"/>
</kw>
<test id="s1-s9-t1" name="RAC29a_TC_125_FFT_Preview_Campaign_Next_Button" line="36">
<kw name="Validating the Next Button on Approval Page preview">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T15:04:37.789432" level="INFO">Set test documentation to:
Next Button on Campaign Approvals Preview</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T15:04:37.788336" elapsed="0.001096"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:37.789432" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:37.789432" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:37.789432" elapsed="0.001000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:37.790432" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:37.791440" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:37.791440" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:04:37.791440" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:04:37.791440" elapsed="0.000998"/>
</branch>
<status status="PASS" start="2024-10-30T15:04:37.791440" elapsed="0.000998"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T15:04:37.792438" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T15:04:37.792438" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:04:37.792438" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T15:04:37.811702" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T15:04:38.108800" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T15:04:38.108800" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T15:04:37.792438" elapsed="0.316362"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:04:38.110275" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:38.110275" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:04:38.110275" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T15:04:37.791440" elapsed="0.318835"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:38.110275" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T15:04:38.110275" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:04:38.110275" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:04:38.110275" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:04:38.111667" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-30T15:04:38.111667" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:04:38.112673" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:04:38.112673" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:38.112673" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E82631BB0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:38.112673" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:04:38.112673" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T15:04:38.112673" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:04:38.112673" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:04:38.112673" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T15:04:38.113672" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T15:04:38.112673" elapsed="0.000999"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:38.113672" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:04:38.113672" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:04:38.113672" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:38.113672" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:38.113672" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:38.114672" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:04:38.114672" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:04:38.114672" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:04:38.114672" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T15:04:38.114672" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T15:04:37.789432" elapsed="31.667563"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:09.457995" elapsed="0.015535"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:09.478892" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="264a7c8429291e3da1b26475b6a110fd", element="f.FA308C33AAD68B89D727ABEF4AB96E13.d.9A4B25008DA7FD131B9177E9CDEF9724.e.129")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:09.473530" elapsed="0.005362"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:09.478892" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="264a7c8429291e3da1b26475b6a110fd", element="f.FA308C33AAD68B89D727ABEF4AB96E13.d.9A4B25008DA7FD131B9177E9CDEF9724.e.129")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:09.478892" elapsed="0.025688"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:09.504580" elapsed="0.009738"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:09.504580" elapsed="0.009738"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:09.521338" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="264a7c8429291e3da1b26475b6a110fd", element="f.FA308C33AAD68B89D727ABEF4AB96E13.d.9A4B25008DA7FD131B9177E9CDEF9724.e.130")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:09.514318" elapsed="0.007020"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:14.522429" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:09.521338" elapsed="5.001091"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:14.522429" elapsed="0.013396"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:14.535825" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="264a7c8429291e3da1b26475b6a110fd", element="f.FA308C33AAD68B89D727ABEF4AB96E13.d.9A4B25008DA7FD131B9177E9CDEF9724.e.130")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:14.535825" elapsed="0.083208"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:14.619033" elapsed="0.442470"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:14.619033" elapsed="0.442470"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:15.062502" elapsed="0.004357"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:15.061503" elapsed="0.005356"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:15.077491" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:05:15.066859" elapsed="0.011926"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:09.456995" elapsed="5.621790"/>
</kw>
<kw name="Then The user previews and validates the Next Button on Approval Preview" owner="Approvals">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:05:15.078785" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:05:15.078785" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:05:15.078785" level="INFO">${TOTAL_CAMPAIGNS} = 0</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:05:15.078785" elapsed="0.000000"/>
</kw>
<while condition="${more_pages} == True">
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:15.087790" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:15.078785" elapsed="0.009005"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:15.087790" level="INFO">${TOTAL_CAMPAIGNS} = 10</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:15.087790" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:15.087790" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:15.087790" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:15.087790" level="INFO">Current page campaigns: 10 | Total so far: 10</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:15.087790" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:15.088831" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:15.088831" elapsed="0.069705"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:15.159520" elapsed="1.800313"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:15.159520" elapsed="1.800313"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:16.959833" elapsed="0.019521"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:16.980371" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:16.980371" elapsed="0.060102"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----NEXT BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:17.040473" elapsed="0.001153"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:19.041964" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:17.041626" elapsed="2.000338"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:19.051731" level="INFO">Current page contains text 'Language:'.</msg>
<arg>Language:</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:05:19.041964" elapsed="0.009767"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:19.051731" elapsed="0.004600"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:19.051731" elapsed="0.004600"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:19.056331" elapsed="0.011112"/>
</kw>
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:19.067443" elapsed="0.016512"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:19.083955" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:19.083955" elapsed="0.039589"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:21.123938" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:19.123544" elapsed="2.000394"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:21.123938" elapsed="0.276891"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:21.400829" elapsed="0.013344"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:21.414173" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:21.414173" elapsed="0.043897"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-30T15:05:15.088831" elapsed="6.369239"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-30T15:05:15.087790" elapsed="6.370280"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:21.459076" elapsed="0.014889"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:21.487120" level="INFO">${is_disabled} = None</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:21.473965" elapsed="0.013155"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:21.487120" level="INFO">${is_enabled} = True</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:21.487120" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:21.487120" level="INFO">Next page button enabled: True</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:21.487120" elapsed="0.001005"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-30T15:05:21.488125" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:05:21.488125" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:21.458070" elapsed="0.030055"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:21.488125" elapsed="0.012242"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:26.501762" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:21.501424" elapsed="5.000338"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:26.501762" elapsed="0.284264"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:26.787436" elapsed="0.272704"/>
</kw>
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:05:26.787436" elapsed="0.272704"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Next Page Button" owner="Approvals">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:27.061236" level="INFO">Executing JavaScript:
document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()
Without any arguments.</msg>
<arg>document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-30T15:05:27.060140" elapsed="0.017449"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:27.060140" elapsed="0.017449"/>
</kw>
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:05:27.060140" elapsed="0.017449"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:29.077823" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:27.077589" elapsed="2.000234"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:15.078785" elapsed="13.999038"/>
</iter>
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:29.085335" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:29.077823" elapsed="0.007512"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:29.086651" level="INFO">${TOTAL_CAMPAIGNS} = 20</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:29.085335" elapsed="0.001316"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:29.086651" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:29.086651" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:29.086651" level="INFO">Current page campaigns: 10 | Total so far: 20</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:29.086651" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:29.087685" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:29.086651" elapsed="0.191038"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:29.277689" elapsed="2.331466"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:29.277689" elapsed="2.331466"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:31.610154" elapsed="0.010023"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:31.620177" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:31.620177" elapsed="0.039775"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----NEXT BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:31.660952" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:33.661046" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:31.660952" elapsed="2.000094"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:33.667870" level="INFO">Current page contains text 'Language:'.</msg>
<arg>Language:</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:05:33.661046" elapsed="0.006824"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:33.669373" elapsed="0.003248"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:33.667870" elapsed="0.004751"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:33.672621" elapsed="0.008562"/>
</kw>
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:33.682612" elapsed="0.011610"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:33.694222" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:33.694222" elapsed="0.034603"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:35.729183" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:33.728825" elapsed="2.000358"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:35.729183" elapsed="0.264538"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:35.993721" elapsed="0.010503"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:36.004224" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:36.004224" elapsed="0.041770"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-30T15:05:29.086651" elapsed="6.959343"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-30T15:05:29.086651" elapsed="6.959343"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:36.046974" elapsed="0.014001"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:36.076280" level="INFO">${is_disabled} = None</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:36.061975" elapsed="0.014305"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:36.076280" level="INFO">${is_enabled} = True</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:36.076280" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:36.077297" level="INFO">Next page button enabled: True</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:36.077297" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-30T15:05:36.077297" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:05:36.077297" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:36.046974" elapsed="0.030323"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:36.077297" elapsed="0.014992"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:41.092481" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:36.092289" elapsed="5.000192"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:41.092481" elapsed="0.279769"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:41.372250" elapsed="0.288629"/>
</kw>
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:05:41.372250" elapsed="0.288629"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Next Page Button" owner="Approvals">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:41.662967" level="INFO">Executing JavaScript:
document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()
Without any arguments.</msg>
<arg>document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-30T15:05:41.661960" elapsed="0.011021"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:41.660879" elapsed="0.012102"/>
</kw>
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:05:41.660879" elapsed="0.012102"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:43.673051" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:41.672981" elapsed="2.000070"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:29.077823" elapsed="14.595228"/>
</iter>
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:43.679784" level="INFO">${campaigns} = 3</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:43.673051" elapsed="0.006733"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:43.679784" level="INFO">${TOTAL_CAMPAIGNS} = 23</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:43.679784" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:43.679784" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:43.679784" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:43.680812" level="INFO">Current page campaigns: 3 | Total so far: 23</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:43.679784" elapsed="0.001028"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:43.680812" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:43.680812" elapsed="0.345932"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:44.027744" elapsed="1.676832"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:44.026744" elapsed="1.677832"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:45.704576" elapsed="0.010734"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:45.716669" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:45.716669" elapsed="0.041745"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----NEXT BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:45.758414" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:47.759828" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:45.758414" elapsed="2.001414"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:47.768359" level="INFO">Current page contains text 'Language:'.</msg>
<arg>Language:</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:05:47.759828" elapsed="0.008531"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:47.769739" elapsed="0.004004"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:47.768359" elapsed="0.005384"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:47.773743" elapsed="0.010733"/>
</kw>
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:47.785563" elapsed="0.014038"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:47.799601" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:47.799601" elapsed="0.037117"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:49.836898" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:47.836718" elapsed="2.000180"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:49.836898" elapsed="0.269638"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:50.106536" elapsed="0.010957"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:50.118488" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:50.118488" elapsed="0.040033"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-30T15:05:43.680812" elapsed="6.477709"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-30T15:05:43.680812" elapsed="6.477709"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:50.159532" elapsed="0.013053"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:50.184077" level="INFO">${is_disabled} = true</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:50.172585" elapsed="0.011492"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:05:50.185059" level="INFO">${is_enabled} = False</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:05:50.185059" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:50.185059" level="INFO">Next page button enabled: False</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:50.185059" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-30T15:05:50.185059" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:05:50.185059" level="INFO">${more_pages} = False</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-30T15:05:50.158521" elapsed="0.026538"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:05:50.185059" elapsed="0.014579"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:55.200154" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:50.199638" elapsed="5.000516"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-30T15:05:55.200154" elapsed="0.266697"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:05:55.467867" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:05:55.467867" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.468062" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:55.467867" elapsed="2.000195"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:43.673051" elapsed="13.795011"/>
</iter>
<status status="PASS" start="2024-10-30T15:05:15.078785" elapsed="42.389277"/>
</while>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.488078" level="INFO">Total campaigns identified on the front-end Approval List: 23</msg>
<arg>Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:57.488078" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:57.488078" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.488078" level="INFO">${db_type} = 'MYSQL'</msg>
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:05:57.488078" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.488078" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY} = SELECT COUNT(*) FROM ATM_Marketing.Campaign WHERE isActive = '1';</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:05:57.488078" elapsed="0.000000"/>
</kw>
<kw name="Execute SQL Query" owner="DBUtility">
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.489483" level="INFO">${return_data} = True</msg>
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-30T15:05:57.489483" elapsed="0.000000"/>
</kw>
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.489483" level="INFO">${return_all} = False</msg>
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-30T15:05:57.489483" elapsed="0.000000"/>
</kw>
<kw name="Verify Data Using Database" owner="DatabaseUtility">
<msg time="2024-10-30T15:05:57.971449" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg time="2024-10-30T15:05:57.971449" level="INFO">${data_found} = {'COUNT(*)': 23}</msg>
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<status status="PASS" start="2024-10-30T15:05:57.489483" elapsed="0.481966"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:05:57.971449" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:05:57.971449" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-30T15:05:57.971449" elapsed="0.000983"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" start="2024-10-30T15:05:57.972432" elapsed="0.000000"/>
</return>
<msg time="2024-10-30T15:05:57.972432" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = {'COUNT(*)': 23}</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${db_type}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<arg>True</arg>
<status status="PASS" start="2024-10-30T15:05:57.488078" elapsed="0.484354"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2024-10-30T15:05:57.973435" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = 23</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<arg>COUNT(*)</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2024-10-30T15:05:57.972432" elapsed="0.001003"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.973435" level="INFO">Total campaigns on the approval list (database): 23</msg>
<arg>Total campaigns on the approval list (database): ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:57.973435" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns on the database approval list: ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:57.973435" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:05:57.974436" level="INFO">Test completed. The Next button on the approval preview functions as expected.</msg>
<arg>Test completed. The Next button on the approval preview functions as expected.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:05:57.974436" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Test completed. The Next button on the approval preview functions as expected.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:57.974436" elapsed="0.000901"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${TOTAL_CAMPAIGNS}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2024-10-30T15:05:57.975337" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----The front-end and database campaign counts on Approval List match----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:05:57.975337" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:15.078785" elapsed="42.896552"/>
</kw>
<arg>Next Button on Campaign Approvals Preview</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T15:04:37.788336" elapsed="80.187001"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:05:57.976449" elapsed="0.005554"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:57.976449" elapsed="0.005554"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:05:57.982003" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:05:57.982003" elapsed="0.034220"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:06:01.016612" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:05:58.016223" elapsed="3.000389"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:01.016612" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:06:01.084878" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-20.png"&gt;&lt;img src="selenium-screenshot-20.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:06:01.084878" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:06:01.016612" elapsed="0.069280">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:06:03.086302" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:06:01.085892" elapsed="2.000410"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:06:03.086302" elapsed="2.212196"/>
</kw>
<status status="FAIL" start="2024-10-30T15:05:57.982003" elapsed="7.316495">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:05:57.982003" elapsed="7.316495"/>
</kw>
<status status="PASS" start="2024-10-30T15:05:57.976449" elapsed="7.322049"/>
</kw>
<doc>Next Button on Campaign Approvals Preview</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T15:04:37.788336" elapsed="87.510162"/>
</test>
<doc>Testing the Next Button on Campaign Approvals Preview</doc>
<status status="PASS" start="2024-10-30T15:04:37.739405" elapsed="87.560076"/>
</suite>
<suite id="s1-s10" name="RAC29a TC 127 Approver Capture Campaign" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_127_Approver_Capture_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.348689" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:06:05.348689" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.348689" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:06:05.348689" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.348689" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:06:05.348689" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.348689" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:06:05.348689" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.348689" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-30T15:06:05.348689" elapsed="0.001017"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:05.347708" elapsed="0.001998"/>
</kw>
<test id="s1-s10-t1" name="RAC29a_TC_122_Show_maximum_campaign_feature" line="37">
<kw name="Validating the Approver Role">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.349706" level="INFO">Set test documentation to:
Approver Role</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-30T15:06:05.349706" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.350692" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:06:05.350692" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:06:05.350692" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:06:05.350692" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.352196" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:06:05.352196" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:06:05.353219" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:06:05.352196" elapsed="0.001023"/>
</branch>
<status status="PASS" start="2024-10-30T15:06:05.352196" elapsed="0.001023"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.353219" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-30T15:06:05.353219" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:06:05.353219" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.377943" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T15:06:05.638940" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T15:06:05.638940" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-30T15:06:05.353219" elapsed="0.285721"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.640350" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:06:05.640350" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-30T15:06:05.638940" elapsed="0.001410"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T15:06:05.352196" elapsed="0.288154"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.640350" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T15:06:05.640350" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:06:05.640350" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:06:05.640350" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T15:06:05.641694" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T15:06:05.641694" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.001007"/>
</if>
<status status="NOT RUN" start="2024-10-30T15:06:05.641694" elapsed="0.001007"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.642701" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.642701" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000023E825DDA30&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T15:06:05.642701" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T15:06:05.642701" elapsed="0.001000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.643701" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-30T15:06:05.643701" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:06:05.643701" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T15:06:05.643701" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:06:05.643701" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:06:05.643701" elapsed="0.001000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:06:05.644701" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:06:05.644701" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-30T15:06:05.644701" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.644701" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T15:06:05.644701" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-30T15:06:05.644701" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-30T15:06:05.349706" elapsed="31.694222"/>
</kw>
<kw name="When The user has an active Approver Role" owner="Approvals">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:37.060186" level="INFO">Element 'xpath=//span[contains(@class, 'menu-item') and text()='Admin']' is displayed.</msg>
<arg>xpath=//span[contains(@class, 'menu-item') and text()='Admin']</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:06:37.043928" elapsed="0.016258"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:37.072549" level="INFO">Current page contains text 'Admin'.</msg>
<arg>Admin</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-30T15:06:37.060186" elapsed="0.012363"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:37.043928" elapsed="0.028621"/>
</kw>
<kw name="Then The user should not be able to access Capture Campaign" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-30T15:06:37.072549" elapsed="0.015251"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:37.092801" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="3e91638ba59996c8f696f510ef270875", element="f.3746058CAC66669329EC087232561097.d.5671FBFF21DA03BCED41276E3B0A5C5A.e.138")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:06:37.087800" elapsed="0.005001"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:37.092801" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="3e91638ba59996c8f696f510ef270875", element="f.3746058CAC66669329EC087232561097.d.5671FBFF21DA03BCED41276E3B0A5C5A.e.138")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:06:37.092801" elapsed="0.027308"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:06:37.120109" elapsed="0.007478"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:37.120109" elapsed="0.007478"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:37.135078" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="3e91638ba59996c8f696f510ef270875", element="f.3746058CAC66669329EC087232561097.d.5671FBFF21DA03BCED41276E3B0A5C5A.e.139")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:06:37.127587" elapsed="0.007491"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:06:42.135621" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:06:37.135078" elapsed="5.000543"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-30T15:06:42.135621" elapsed="0.017133"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:42.152754" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="3e91638ba59996c8f696f510ef270875", element="f.3746058CAC66669329EC087232561097.d.5671FBFF21DA03BCED41276E3B0A5C5A.e.139")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:06:42.152754" elapsed="0.080066"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:06:42.232820" elapsed="0.433220"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:42.232820" elapsed="0.433220"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T15:06:42.667534" level="INFO">---- User does not have access to Capture Campaign as an Approver ----</msg>
<arg>---- User does not have access to Capture Campaign as an Approver ----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T15:06:42.667534" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>---- User does not have access to Capture Campaign as an Approver ----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T15:06:42.667534" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:37.072549" elapsed="5.594985"/>
</kw>
<arg>Approver Role</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-30T15:06:05.349706" elapsed="37.317828"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-30T15:06:42.668551" elapsed="0.004641"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:42.668551" elapsed="0.004641"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:42.673192" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-30T15:06:42.673192" elapsed="0.032584"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:06:45.707311" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:06:42.706800" elapsed="3.000511"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-30T15:06:45.707311" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-30T15:06:45.772721" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-22.png"&gt;&lt;img src="selenium-screenshot-22.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-30T15:06:45.772721" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-30T15:06:45.707311" elapsed="0.066402">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-30T15:06:47.773967" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-30T15:06:45.773713" elapsed="2.000254"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-30T15:06:47.773967" elapsed="2.220943"/>
</kw>
<status status="FAIL" start="2024-10-30T15:06:42.673192" elapsed="7.322591">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-30T15:06:42.673192" elapsed="7.322591"/>
</kw>
<status status="PASS" start="2024-10-30T15:06:42.668551" elapsed="7.327232"/>
</kw>
<doc>Approver Role</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-30T15:06:05.349706" elapsed="44.646077"/>
</test>
<doc>Verifying that the approver role does not permit users to capture campaigns</doc>
<status status="PASS" start="2024-10-30T15:06:05.300951" elapsed="44.695828"/>
</suite>
<status status="FAIL" start="2024-10-30T14:56:01.767477" elapsed="648.232417"/>
</suite>
<statistics>
<total>
<stat pass="9" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="9" fail="1" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="9" fail="1" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
<stat pass="1" fail="0" skip="0" id="s1-s1" name="RAC29a TC 117 Approve Campaign">Future Fit Portal.RAC29a TC 117 Approve Campaign</stat>
<stat pass="1" fail="0" skip="0" id="s1-s2" name="RAC29a TC 118 Delete Campaign">Future Fit Portal.RAC29a TC 118 Delete Campaign</stat>
<stat pass="1" fail="0" skip="0" id="s1-s3" name="RAC29a TC 119 Search for Campaign By Campaign Id">Future Fit Portal.RAC29a TC 119 Search for Campaign By Campaign Id</stat>
<stat pass="1" fail="0" skip="0" id="s1-s4" name="RAC29a TC 120 Search for Campaign By Campaign Name">Future Fit Portal.RAC29a TC 120 Search for Campaign By Campaign Name</stat>
<stat pass="1" fail="0" skip="0" id="s1-s5" name="RAC29a TC 121 Search for Campaign By Approved By">Future Fit Portal.RAC29a TC 121 Search for Campaign By Approved By</stat>
<stat pass="1" fail="0" skip="0" id="s1-s6" name="RAC29a TC 122 Show maximum campaign feature">Future Fit Portal.RAC29a TC 122 Show maximum campaign feature</stat>
<stat pass="0" fail="1" skip="0" id="s1-s7" name="RAC29a TC 123 Preview Campaign">Future Fit Portal.RAC29a TC 123 Preview Campaign</stat>
<stat pass="1" fail="0" skip="0" id="s1-s8" name="RAC29a TC 124 Preview Campaign Close Button">Future Fit Portal.RAC29a TC 124 Preview Campaign Close Button</stat>
<stat pass="1" fail="0" skip="0" id="s1-s9" name="RAC29a TC 125 Preview Campaign Next Button">Future Fit Portal.RAC29a TC 125 Preview Campaign Next Button</stat>
<stat pass="1" fail="0" skip="0" id="s1-s10" name="RAC29a TC 127 Approver Capture Campaign">Future Fit Portal.RAC29a TC 127 Approver Capture Campaign</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-30T14:56:01.177897" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_117_Approve_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.189394" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_118_Delete_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.197394" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_119_Search_for_Campaign_By_Campaign_Id.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.208023" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_120_Search_for_Campaign_By_Campaign_Name.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.221199" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_121_Search_for_Campaign_By_Approved_By.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.234026" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_122_Show_maximum_campaign_feature.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.249077" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_123_Preview_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.262593" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_124_Preview_Campaign_Close_Button.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.273389" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_125_Preview_Campaign_Next_Button.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:01.284419" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_127_Approver_Capture_Campaign.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-30T14:56:03.378030" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-30T14:56:25.679350" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:56:35.690000" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:56:40.696114" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:57:25.974082" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T14:57:43.308435" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:57:53.316512" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:57:58.322408" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:58:48.435249" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T14:59:04.833811" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:59:14.844708" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:59:19.852244" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:59:32.699131" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T14:59:40.422132" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T14:59:56.894280" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:00:06.903970" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:00:11.911044" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:00:24.751033" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:00:32.440673" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T15:00:48.619325" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:00:58.628252" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:01:03.635114" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:01:21.556412" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:01:29.245611" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T15:01:45.769284" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:01:55.778404" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:02:00.785718" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:02:28.201314" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T15:02:45.109862" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:02:55.118852" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:03:00.126159" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:03:13.030724" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T15:03:30.156380" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:03:40.164220" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:03:45.171104" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:04:38.110275" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T15:04:54.358704" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:05:04.368673" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:05:09.374383" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:06:05.640350" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-30T15:06:21.940285" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:06:31.948357" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:06:36.955802" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-30T15:06:59.198490" level="ERROR">Calling method 'end_suite' of listener 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' failed: SSLError: HTTPSConnectionPool(host='testmanagementeu.qmetry.com', port=443): Max retries exceeded with url: /rest/import/createandscheduletestresults/1 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))</msg>
</errors>
</robot>
