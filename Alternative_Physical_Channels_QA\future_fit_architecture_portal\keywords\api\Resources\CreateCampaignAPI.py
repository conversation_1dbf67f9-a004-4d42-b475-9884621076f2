import base64
import json
import re

from robot.api.deco import keyword
from dataclasses import dataclass, field
from typing import List, Dict, Any

#Each nested structure of the JSON Request is represented by its own @dataclass.
#The CampaignAPI class contains all the necessary fields and a method to convert the instance to a JSON string.

@dataclass
class Language:
    id: int
    language: str

@dataclass
class Image:
    id: int
    marketingImage: str
    language: Language
    imageName: str
    duration: int
    priority: int

@dataclass
class LastUpdate:
    id: int
    campaignId: int
    updatedDate: str
    updatedBy: str
    updateDescription: str
    approvalId: int

@dataclass
class TargetData:
    isTargetRegion: bool
    targetRegionOrAtm: str

@dataclass
class CampaignRequest:
    campaignName: str
    campaignBy: str
    campaignStartDate: str
    campaignEndDate: str
    marketingChannelId: int
    id: int
    campaignId: str
    screenId: int
    imageList: List[Image]
    lastUpdate: LastUpdate
    updatedBy: str
    isActive: bool
    isApproved: bool
    version: str
    isTargetted: bool
    targetData: TargetData

    def to_json(self) -> str:
        return json.dumps(self, default=lambda o: o.__dict__, indent=2)

class CreateCampaignAPI:
    @keyword
    def create_campaign_request(self, campaign_name: str, campaign_by: str,
                                 campaign_start_date: str, campaign_end_date: str,
                                 marketing_channel_id: int, campaign_id: int, campaign_campaignID: str,
                                 screen_id: int, updated_by: str,
                                 is_active: bool, is_approved: bool,
                                 version: str, is_targetted: bool,
                                 images: List[Dict[str, Any]],
                                 last_update: Dict[str, Any],
                                 target_data: Dict[str, Any]) -> str:

        image_list = [
            Image(
                id=image['id'],
                marketingImage=image.get('marketingImage', ""),
                language=Language(id=image['language']['id'], language=image['language']['language']),
                imageName=image['imageName'],
                duration=image['duration'],
                priority=image['priority']
            ) for image in images
        ]

        campaign_request = CampaignRequest(
            campaignName=campaign_name,
            campaignBy=campaign_by,
            campaignStartDate=campaign_start_date,
            campaignEndDate=campaign_end_date,
            marketingChannelId=marketing_channel_id,
            id=campaign_id,
            campaignId=campaign_campaignID,
            screenId=screen_id,
            imageList=image_list,
            lastUpdate=LastUpdate(**last_update),
            updatedBy=updated_by,
            isActive=is_active,
            isApproved=is_approved,
            version=version,
            isTargetted=is_targetted,
            targetData=TargetData(**target_data)
        )

        return campaign_request.to_json()



    @keyword
    def is_base64_encoded(self, string: str) -> bool:
        """Check if the given string is Base64 encoded."""
        # Check if the string matches Base64 format
        base64_pattern = r'^[A-Za-z0-9+/=]*$'
        if re.match(base64_pattern, string) is None:
            print('first fail')
            return False

        # Check if the string can be decoded properly
        try:
            decoded_bytes = base64.b64decode(string, validate=True)
            return True
        except Exception:
            print('second fail')
            return False

    @keyword
    def is_boolean(self, value):
        return isinstance(value, bool)