*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/GetBinById.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                           ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***


*** Keywords ***
The User gets all the active and inactive Bins from the Database

    ${db_results}=     Get all the active and inactive Bins from the Database used by GetBinByID controller

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for bins awaiting review did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Save the database results in a global variable
    Set Global Variable    ${DATABASE_RESULTS}        ${db_results}

    Log Many    ${DATABASE_RESULTS}


Get the active or inactive Bin from the Database

    ${db_results}=     Get an active Bin from the Database    0

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for retrieving a bin id did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}


     ${first_row_results}=           Get From List    ${db_results}    0    # Get the first row
    ${bin_id}=    Get From Dictionary    ${first_row_results}    binId

    ${db_results}=     Get the Bin Number using the Bin Id    ${bin_id}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for retrieving a bin number did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}


     ${first_row_results}=           Get From List    ${db_results}    0    # Get the first row
    ${bin_number}=    Get From Dictionary    ${first_row_results}    binNumber


    ${bin_details_db_results}=     Get the Bin details from the Database used by GetBinByID controller    ${bin_number}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bin_details_db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fatal Error    Database SQL for retrieving a bin number details did not results!

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

   ${counter}=  Set Variable  0
   ${bin_type_id_list}=     Create List
   ${bin_type_name_list}=     Create List

   FOR    ${db_row}    IN    @{bin_details_db_results}
       Log    ${db_row}
       IF    '${counter}' == '0'
           ${bin_created_date_data}=             Get Column Data By Name       ${db_row}       capturedDate
           ${bin_created_by_data}=             Get Column Data By Name       ${db_row}       capturedBy
           ${bin_action_date_data}=             Get Column Data By Name       ${db_row}       actionDate
           ${bin_review_status_data}=             Get Column Data By Name       ${db_row}       reviewStatus
           ${bin_review_status_text}=             Get Bin Status Text       ${bin_review_status_data}
           ${bin_outcome_data}=             Get Column Data By Name       ${db_row}       outcome
           ${bin_outcome_text}=            Get Bin Outcome Text     ${bin_outcome_data}
           ${bin_to_be_actioned_by_data}=             Get Column Data By Name       ${db_row}       toBeActionedBy
           ${bin_rejection_comment_data}=             Get Column Data By Name       ${db_row}       rejectedComment
           ${bin_reviewed_by_data}=             Get Column Data By Name       ${db_row}       reviewedBy
           ${bin_reviewed_date_data}=             Get Column Data By Name       ${db_row}       reviewedDate
           ${bin_latest_server_number_data}=             Get Column Data By Name       ${db_row}       LatestServerNumber
           ${bin_type_id_data}=             Get Column Data By Name       ${db_row}       binTypeID
           Append To List    ${bin_type_id_list}        ${bin_type_id_data}
           ${bin_type_name_data}=             Get Column Data By Name       ${db_row}       binType
           Append To List    ${bin_type_name_list}        ${bin_type_name_data}
            ${counter}=  Set Variable  1
       ELSE
           ${bin_type_id_data}=             Get Column Data By Name       ${db_row}         binTypeID
           ${bin_type_name_data}=           Get Column Data By Name       ${db_row}       binType
           Append To List    ${bin_type_id_list}        ${bin_type_id_data}
           Append To List    ${bin_type_name_list}      ${bin_type_name_data}
       END
   END

   &{BIN_DICTIONARY} =    Create Dictionary    BIN_NUMBER=${bin_number}    BIN_ID=${bin_id}    CAPTURED_DATE=${bin_created_date_data}    CAPTURED_BY=${bin_created_by_data}    ACTION_DATE=${bin_action_date_data}    REVIEW_STATUS=${bin_review_status_text}    OUTCOME=${bin_outcome_text}    TO_BE_ACTIONED_BY=None    REJECTED_COMMENT=${bin_rejection_comment_data}    REVIEWED_BY=${bin_reviewed_by_data}    REVIEWED_DATE=${bin_reviewed_date_data}    LATEST_SERVER_NUMBER=${bin_latest_server_number_data}    BIN_TYPE_IDS=${bin_type_id_list}    BIN_TYPE_NAMES=${bin_type_name_list}

    Log Many  &{BIN_DICTIONARY}
    RETURN   &{BIN_DICTIONARY}



The user sends a Get Request to search for all database retrieved bins using the Bin Id, the bin details must be retruned by the getbinbyid controller
    [Arguments]     ${BASE_URL}


    #Loop though all the bins fetched from the database,
    #send a getbinbyid request for each bean and verify that the returned
    #results are the same as those from the DB.
    ${num_rows}=    Get Length    ${DATABASE_RESULTS}

    Log     '${num_rows}' database BINS will be verfied against the API

    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       BinNumber

        #The below bin number is not run because it has a different Status on the Action Tracker table when compared to the controller
        IF    '${binNumber_data}' == '333333'
            CONTINUE
        END
        #Get the review details for the current Bin from the database
        ${db_results}=     Get the Bin details from the Database used by GetBinByID controller    ${binNumber_data}



        # Ensure the results are not empty
        ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

        Run Keyword If    not ${dr_results_contain_data}
        ...     Fail    Database query for bin number: '${binNumber_data}' returned no results

        #Loop Through the review records details for the current BIN NUMBER and verify them against the Controller
        #The below variables are used to detect and filter out the duplicate columns
        ${bin_type_already_read_value}=       Set Variable      ${EMPTY}
        ${bin_type_list}=      Create List
        ${tobeActionedBy_Value}=    Set Variable    ${EMPTY}
        FOR    ${db_results_row}    IN    @{db_results}

            Log Many    ${db_results_row}

            ${BIN_TYPE}=       Get Column Data By Name       ${db_results_row}       binType

            ${bin_is_deleted}=       Get Column Data By Name       ${db_results_row}       binNumberIsDeleted

            #Do not verify the bin on the API if it has been deleted (soft-deleted) on the DB
            Run Keyword If    '${bin_is_deleted}'=='1'
            ...    Continue For Loop

            IF    '${bin_type_already_read_value}' == '${EMPTY}'
                ${bin_type_already_read_value}=   Set Variable     ${BIN_TYPE}
                Append To List    ${bin_type_list}      ${BIN_TYPE}
            ELSE
                 ${duplicate_found}=     Set Variable  ${False}
                FOR    ${type_name}    IN    @{bin_type_list}
                    IF    '${type_name}' == '${BIN_TYPE}'
                        ${duplicate_found}=     Set Variable  ${True}
                        Exit For Loop
                    END
                END

                IF    ${duplicate_found}
                        CONTINUE
                    ELSE
                        Append To List    ${bin_type_list}      ${BIN_TYPE}
                END
            END

            #If the bin type is deleted do not verify it on the API response
            ${binTypeIsDeleted_data}=   Get Column Data By Name       ${db_results_row}       binTypeIsDeleted
            Run Keyword If    '${binTypeIsDeleted_data}'=='1'
            ...    Continue For Loop

            ${BIN_ID}=  Get Column Data By Name       ${db_results}       Bin_ID

            ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${BIN_ID.strip()}
            FOR    ${row}    IN    @{action_tracker_db_results}
                ${REVIEW_STATUS}=       Get Column Data By Name       ${row}       Status
                ${REVIEW_STATUS}=       Get Bin Status Text           ${REVIEW_STATUS}
                ${OUTCOME}=             Get Column Data By Name       ${row}       ActionType
                ${OUTCOME}=             Get Bin Outcome Text          ${OUTCOME}
                ${REJECTED_COMMENT}=    Get Column Data By Name       ${row}       RejectionComment
                ${REVIEWED_BY}=         Get Column Data By Name       ${row}       ReviewedBy
                ${REVIEWED_DATE}=       Get Column Data By Name       ${row}       ReviewedDate

            END

            #${TO_BE_ACTIONED_BY}=   Get Column Data By Name       ${db_results_row}       toBeActionedBy
            #${TO_BE_ACTIONED_BY}=   Get Bin To Be Actioned By Text     ${TO_BE_ACTIONED_BY}

            ${TO_BE_ACTIONED_BY}=       Set Variable        ${EMPTY}

            #IF    '${tobeActionedBy_Value}' == '${EMPTY}'
            #    ${tobeActionedBy_Value}=   Set Variable     ${TO_BE_ACTIONED_BY}
            #ELSE
            #     IF    '${tobeActionedBy_Value}' != '${TO_BE_ACTIONED_BY}'
            #         CONTINUE
             #    END
            #END





            ${CAPTURED_DATE}=  Get Column Data By Name       ${db_results_row}       capturedDate
            IF    '${CAPTURED_DATE}' != 'None'
                ${CAPTURED_DATE}=        Format Timestamp for API and Database      ${CAPTURED_DATE}       ${False}
            END
            ${CAPTURED_BY}=         Get Column Data By Name       ${db_results_row}       capturedBy
            ${ACTION_DATE}=         Get Column Data By Name       ${db_results_row}       actionDate


            IF    '${REVIEWED_DATE}' != 'None'
                ${REVIEWED_DATE}=        Format Timestamp for API and Database      ${REVIEWED_DATE}       ${False}
            END
            ${LATEST_SERVER_NUMBER}=       Get Column Data By Name       ${db_results_row}       LatestServerNumber

            ${BIN_TYPE_NAME}=         Get Column Data By Name       ${db_results_row}       binType
            ${bin_type_data}=       Convert To Upper Case     ${BIN_TYPE_NAME}
            IF    '${bin_type_data}' == 'ONUS'
                ${BIN_TYPE_NAME}=       Set Variable    On-Us
            END
            ${BIN_TYPE_ID}=         Get Column Data By Name       ${db_results_row}       binTypeID



            #Send the GET BIN By Id request for the current bin
            The User sends a Get Request to search for bins using the Bin Id      ${BASE_URL}      ${BIN_ID}

            #Verify that the controller returned the 200 response code
            The service returns an expected status code     200

            #Verify that the contoller returned the correct details as per the Database info
            The expected Bin Number details are retuned by the API Response         ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${binNumber_data}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_ID}    ${BIN_TYPE_NAME}
        END
   END


The User sends a Get Request to search for bins using the Bin Id
    [Arguments]     ${BASE_URL}     ${BIN_ID}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetBinById Instance    ${base_url}     ${BIN_ID}    #Create an instance of

    #${rest_request}=        Build Rest Request       ${base_url}     ${BIN_NUMBER}     ${ACTION}  #Instantiate the CreateRequest class
    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${REST_HEADERS}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method}     params=${params}   headers=${REST_HEADERS}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'GetBinById' REST API call failed, the returned status is '${status_code}'



The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_detail}'     '${EXPECTED_ERROR_MESSAGE}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       binid

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  "${EXPECTED_ERROR_MESSAGE}" in "${api_error_message_fields}"     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'GetBinById' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.

Verify that the API retruned an empty response

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    #Verify that the response is empty
    ${verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '[]'     '${bin_object_instace}'

    Run Keyword If    not ${verification_passed}
    ...    Fail     The API did not return an empty response!


The expected Bin Number details are retuned by the API Response

    [Arguments]     ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}     ${BIN_TYPE_NAMES}


    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${captured_date_must_be_verified}=    Set Variable If  '${CAPTURED_DATE}' != '${EMPTY}' and '${CAPTURED_DATE}' != 'None'     ${True}     ${False}
    ${captured_date_verification_passed}=    Set Variable If  ${captured_date_must_be_verified}     ${False}     ${True}

    ${captured_by_must_be_verified}=    Set Variable If  '${CAPTURED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${captured_by_verification_passed}=    Set Variable If  ${captured_by_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}


    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${review_status_must_be_verified}=    Set Variable     ${True}
    #Set Variable If  '${REVIEW_STATUS}' != '${EMPTY}'     ${True}     ${False}
    ${review_status_verification_passed}=    Set Variable If  ${review_status_must_be_verified}     ${False}     ${True}

    ${outcome_must_be_verified}=    Set Variable     ${True}
    #Set Variable If  '${OUTCOME}' != '${EMPTY}'     ${True}     ${False}
    ${outcome_verification_passed}=    Set Variable If  ${outcome_must_be_verified}     ${False}     ${True}

    ${toActionedBy_must_be_verified}=    Set Variable If  '${TO_BE_ACTIONED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${toActionedBy_verification_passed}=    Set Variable If  ${toActionedBy_must_be_verified}     ${False}     ${True}

    ${rejected_comment_must_be_verified}=    Set Variable     ${True}
    #Set Variable If  '${REJECTED_COMMENT}' != '${EMPTY}'     ${True}     ${False}
    Set Variable If  ${rejected_comment_must_be_verified}     ${False}     ${True}

    ${reviewedBy_must_be_verified}=    Set Variable     ${True}
    #Set Variable If  '${REVIEWED_BY}' != '${EMPTY}' and '${REVIEWED_BY}' != 'None'     ${True}     ${False}
    ${reviewedBy_verification_passed}=    Set Variable If  ${reviewedBy_must_be_verified}     ${False}     ${True}

    ${reviewed_date_must_be_verified}=    Set Variable     ${True}
    #Set Variable If  '${REVIEWED_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${reviewed_date_verification_passed}=    Set Variable If  ${reviewed_date_must_be_verified}     ${False}     ${True}

    ${latestServerNumber_must_be_verified}=    Set Variable If  '${LATEST_SERVER_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${latestServerNumber_verification_passed}=    Set Variable If  ${latestServerNumber_must_be_verified}     ${False}     ${True}

    ${binTypeId_must_be_verified}=    Set Variable If  '${BIN_TYPE_IDS}' != '${EMPTY}'     ${True}     ${False}
    ${binTypeId_verification_passed}=    Set Variable If  ${binTypeId_must_be_verified}     ${False}     ${True}

    ${binTypeName_must_be_verified}=    Set Variable If  '${BIN_TYPE_NAMES}' != '${EMPTY}'     ${True}     ${False}
    ${binTypeName_verification_passed}=    Set Variable If  ${binTypeName_must_be_verified}     ${False}     ${True}

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    Log     '################################################'


    ${users_binTypeId_data_array}=      Split String    ${BIN_TYPE_IDS}         separator=,
    ${users_binTypeName_data_array}=      Split String    ${BIN_TYPE_NAMES}         separator=,
    ${user_bin_typeIds_that_passed_Verification}=     Create List
    ${user_bin_typeNames_that_passed_Verification}=     Create List

    #Loop through all returned Bins to verify data
    FOR    ${index}    ${element}    IN ENUMERATE    @{bin_object_instace}
        Log    ${index}: ${element}
        ${binNumber_data}=        Get the Bin Response field data     ${element}    get_bin_number
        Log    '${binNumber_data}'
        #IF the BIN Number is found then verify its details
        IF    '${binNumber_data}' == '${BIN_NUMBER}'
            Log     '################################################'
            ${binNumber_verification_passed}=      Set Variable    ${True}
            ${binNumber_verified}=      Set Variable    ${True}
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$

            ${binId_data}=         Get the Bin Response field data     ${element}     get_id
            #Check if the Bin ID must be verified against the user's data.
            IF    ${binID_must_be_verified}
                ${binID_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_ID}'     '${binId_data}'
            END


            ${bin_capturedDate_data}=            Get the Bin Response field data     ${element}     get_captured_date
            ${bin_capturedDate_data}=        Format Timestamp for API and Database      ${bin_capturedDate_data}       ${False}

            #Check if the Bin captured_date must be verified against the user's data.
            IF    ${captured_date_must_be_verified}
                ${captured_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_DATE}'     '${bin_capturedDate_data}'
            END


            ${bin_capturedBy_data}=            Get the Bin Response field data     ${element}     get_captured_by
            #Check if the Bin captured_by must be verified against the user's data.
            IF    ${captured_by_must_be_verified}
                ${captured_by_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_BY}'     '${bin_capturedBy_data}'
            END

            ${binActionDate_data}=    Get the Bin Response field data     ${element}     get_action_date
            #Check if the Action Date must be verified against the user's data
            IF    ${actionDate_must_be_verified}
                ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
            END

            ${bin_review_status_data}=    Get the Bin Response field data     ${element}     get_review_status
            #Check if the Action Date must be verified against the user's data
            IF    ${review_status_must_be_verified}
                ${review_status_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEW_STATUS}'     '${bin_review_status_data}'
            END


            ${bin_outcome_data}=    Get the Bin Response field data     ${element}     get_outcome
            #Check if the outcome must be verified against the user's data
            IF    ${outcome_must_be_verified}
                ${outcome_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${OUTCOME}'     '${bin_outcome_data}'
            END


            ${bin_toActionedBy_data}=    Get the Bin Response field data     ${element}     get_to_be_actioned_by
            #Check if the toActionedBy must be verified against the user's data
            IF    ${toActionedBy_must_be_verified}
                ${toActionedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${TO_BE_ACTIONED_BY}'     '${bin_toActionedBy_data}'
            END

            ${bin_rejected_comment_data}=    Get the Bin Response field data     ${element}     get_rejected_comment
            #Check if the toActionedBy must be verified against the user's data
            IF    ${rejected_comment_must_be_verified}
                ${rejected_comment_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REJECTED_COMMENT}'     '${bin_rejected_comment_data}'
            END

            ${bin_reviewedBy_data}=    Get the Bin Response field data     ${element}     get_reviewed_by
            #Check if the toActionedBy must be verified against the user's data
            IF    ${reviewedBy_must_be_verified}
                ${reviewedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_BY}'     '${bin_reviewedBy_data}'
            END


            ${bin_reviewed_date_data}=    Get the Bin Response field data     ${element}     get_reviewed_date

            IF    '${bin_reviewed_date_data}' != 'None'
                ${bin_reviewed_date_data}=        Format Timestamp for API and Database      ${bin_reviewed_date_data}       ${False}
            END

            #Check if the toActionedBy must be verified against the user's data
            IF    ${reviewed_date_must_be_verified}
                ${reviewed_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_DATE}'     '${bin_reviewed_date_data}'
            END


             ${bin_latestServerNumber_data}=    Get the Bin Response field data     ${element}     get_latest_server_number
            #Check if the toActionedBy must be verified against the user's data
            IF    ${latestServerNumber_must_be_verified}
                ${latestServerNumber_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${LATEST_SERVER_NUMBER}'     '${bin_latestServerNumber_data}'
            END


            ${binTypes_instance}=    Get the Bin Response field data     ${element}     get_bin_types

            FOR   ${binType_index}    ${binType_element}    IN ENUMERATE    @{binTypes_instance}
                Log    ${binType_index}: ${binType_element}
                Log     '################################################'


                ${binTypeId_data}=      Get the Bin Response field data     ${binType_element}          get_bin_type_id
                Log    '${binTypeId_data}'


                ${binType_data}=        Get the Bin Response field data     ${binType_element}          get_bin_type
                Log    '${binType_data}'


                IF  ${binTypeId_must_be_verified}
                    ${binTypeId_verification_passed}=     Set Variable    ${False}
                    FOR    ${type_id}    IN    @{users_binTypeId_data_array}
                        #Check if the Bin Type ID must be verified against the user's data.
                        ${binTypeId_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${type_id.strip()}'     '${binTypeId_data}'
                        Run Keyword If    ${binTypeId_verification_passed}
                        ...    Exit For Loop
                    END

                    IF    ${binTypeId_verification_passed} == ${True}
                        Append To List    ${user_bin_typeIds_that_passed_Verification}      ${binTypeId_data.strip()}
                    END
                END

                IF    ${binTypeName_must_be_verified}
                        ${binTypeName_verification_passed}=     Set Variable    ${False}
                        FOR    ${type_name}    IN    @{users_binTypeName_data_array}
                           #Check if the Bin Type ID must be verified against the user's data.
                            ${binTypeName_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${type_name.strip()}'     '${binType_data}'
                            Run Keyword If    ${binTypeName_verification_passed}
                            ...    Exit For Loop
                        END

                        IF    ${binTypeName_verification_passed} == ${True}
                            Append To List    ${user_bin_typeNames_that_passed_Verification}      ${type_name.strip()}
                        END
                END


                Log     '################################################'
            END
            Log     '################################################'
            #The loop will be exited if all the parameters have been verified against the response
            Run Keyword If    ${binNumber_verified}
            ...    Exit For Loop
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
        END
    END

    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the API response.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${captured_date_must_be_verified}
        Run Keyword If    ${captured_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${captured_by_must_be_verified}
        Run Keyword If    ${captured_by_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${review_status_must_be_verified}
        Run Keyword If    ${review_status_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${outcome_must_be_verified}
        Run Keyword If    ${outcome_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${toActionedBy_must_be_verified}
        Run Keyword If    ${toActionedBy_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The TO_BE_ACTIONED_BY value: '${TO_BE_ACTIONED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the TO_BE_ACTIONED_BY value: '${OUTCOME}', for BIN Number: '${TO_BE_ACTIONED_BY}' was successful.
    END


    IF  ${rejected_comment_must_be_verified}
        Run Keyword If    ${rejected_comment_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${reviewedBy_must_be_verified}
        Run Keyword If    ${reviewedBy_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${reviewed_date_must_be_verified}
        Run Keyword If    ${reviewed_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${latestServerNumber_must_be_verified}
        Run Keyword If    ${latestServerNumber_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
        ...  ELSE
        ...    Log    Verification for the LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${binTypeId_must_be_verified}

        #Verify that all user provided Bin Types were verified
        ${individual_bin_verification_failed}=      Set Variable   ${False}
        ${user_bin_typeIds_not_found}=   Create List
        ${user_bin_type_Ids_that_passed_Verification_string}=    Create Comma Separated String    ${user_bin_typeIds_that_passed_Verification}

        FOR    ${item}    IN    @{users_binTypeId_data_array}
            Run Keyword If    '${item.strip()}' not in '${user_bin_type_Ids_that_passed_Verification_string}'    Append To List    ${user_bin_typeIds_not_found}    ${item}
        END
        Log Many        @{user_bin_typeIds_not_found}
        ${length}=    Get Length    ${user_bin_typeIds_not_found}

        IF    ${length} > 0
              ${individual_bin_verification_failed}=      Set Variable   ${True}
              ${bin_types_string}=    Create Comma Separated String    ${user_bin_typeIds_not_found}
        END

        Run Keyword If    ${individual_bin_verification_failed} == ${True}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE value(s): '${bin_types_string}', for BIN Number: '${BIN_NUMBER}' was not found on the API Response.
        ...  ELSE
        ...    Log    Verification for the BIN_TYPE value: '${BIN_TYPE_IDS}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${binTypeName_must_be_verified}

        #Verify that all user provided Bin Types were verified
        ${individual_bin_verification_failed}=      Set Variable   ${False}
        ${user_bin_typeNames_not_found}=   Create List
        ${user_bin_type_Names_that_passed_Verification_string}=    Create Comma Separated String    ${user_bin_typeNames_that_passed_Verification}

        FOR    ${item}    IN    @{users_binTypeName_data_array}
            Run Keyword If    '${item.strip()}' not in '${user_bin_type_Names_that_passed_Verification_string}'    Append To List    ${user_bin_typeNames_not_found}    ${item}
        END
        Log Many        @{user_bin_typeNames_not_found}
        ${length}=    Get Length    ${user_bin_typeNames_not_found}

        IF    ${length} > 0
              ${individual_bin_verification_failed}=      Set Variable   ${True}
              ${bin_types_string}=    Create Comma Separated String    ${user_bin_typeNames_not_found}
        END

        Run Keyword If    ${individual_bin_verification_failed} == ${True}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE_NAME value(s): '${bin_types_string}', for BIN Number: '${BIN_NUMBER}' was not found on the API Response.
        ...  ELSE
        ...    Log    Verification for the BIN_TYPE_NAME value(s): '${BIN_TYPE_NAMES}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



#The below keywords are for Database Verifications
The Bin Number details must exist on the Bin Database
    [Arguments]     ${BIN_ID}   ${CAPTURED_DATE}    ${CAPTURED_BY}  ${BIN_NUMBER}   ${ACTION_DATE}  ${REVIEW_STATUS}  ${OUTCOME}  ${TO_BE_ACTIONED_BY}  ${REJECTED_COMMENT}  ${REVIEWED_BY}  ${REVIEWED_DATE}  ${LATEST_SERVER_NUMBER}  ${BIN_TYPE_IDS}     ${BIN_TYPE_NAMES}

    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${captured_date_must_be_verified}=    Set Variable If  '${CAPTURED_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${captured_date_verification_passed}=    Set Variable If  ${captured_date_must_be_verified}     ${False}     ${True}

    ${captured_by_must_be_verified}=    Set Variable If  '${CAPTURED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${captured_by_verification_passed}=    Set Variable If  ${captured_by_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}


    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${review_status_must_be_verified}=    Set Variable If  '${REVIEW_STATUS}' != '${EMPTY}'     ${True}     ${False}
    ${review_status_verification_passed}=    Set Variable If  ${review_status_must_be_verified}     ${False}     ${True}

    ${outcome_must_be_verified}=    Set Variable If  '${OUTCOME}' != '${EMPTY}'     ${True}     ${False}
    ${outcome_verification_passed}=    Set Variable If  ${outcome_must_be_verified}     ${False}     ${True}

    ${toActionedBy_must_be_verified}=    Set Variable If  '${TO_BE_ACTIONED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${toActionedBy_verification_passed}=    Set Variable If  ${toActionedBy_must_be_verified}     ${False}     ${True}

    ${rejected_comment_must_be_verified}=    Set Variable If  '${REJECTED_COMMENT}' != '${EMPTY}'     ${True}     ${False}
    ${rejected_comment_verification_passed}=    Set Variable If  ${rejected_comment_must_be_verified}     ${False}     ${True}

    ${reviewedBy_must_be_verified}=    Set Variable If  '${REVIEWED_BY}' != '${EMPTY}'     ${True}     ${False}
    ${reviewedBy_verification_passed}=    Set Variable If  ${reviewedBy_must_be_verified}     ${False}     ${True}

    ${reviewed_date_must_be_verified}=    Set Variable If  '${REVIEWED_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${reviewed_date_verification_passed}=    Set Variable If  ${reviewed_date_must_be_verified}     ${False}     ${True}

    ${latestServerNumber_must_be_verified}=    Set Variable If  '${LATEST_SERVER_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${latestServerNumber_verification_passed}=    Set Variable If  ${latestServerNumber_must_be_verified}     ${False}     ${True}

    ${binTypeId_must_be_verified}=    Set Variable If  '${BIN_TYPE_IDS}' != '${EMPTY}'     ${True}     ${False}
    ${binTypeId_verification_passed}=    Set Variable If  ${binTypeId_must_be_verified}     ${False}     ${True}

    ${binTypeName_must_be_verified}=    Set Variable If  '${BIN_TYPE_NAMES}' != '${EMPTY}'     ${True}     ${False}
    ${binTypeName_verification_passed}=    Set Variable If  ${binTypeName_must_be_verified}     ${False}     ${True}

    ${db_results}=     Get the Bin details from the Database    ${BIN_NUMBER}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin number: '${BIN_NUMBER}' returned no results

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Loop through the returned DB results and verify them against the user's supplied parameters
    ${num_rows}=    Get Length    ${db_results}
    #Loop Through the review records details for the current BIN NUMBER and verify them against the Controller
    #The below variables are used to detect and filter out the duplicate columns
    ${bin_type_already_read_value}=       Set Variable      ${EMPTY}
    ${tobeActionedBy_Value}=    Set Variable       ${EMPTY}
    ${verified_bin_types}=      Create Dictionary

    ${users_binTypeId_data_array}=      Split String    ${BIN_TYPE_IDS}         separator=,
    ${users_binTypeName_data_array}=      Split String    ${BIN_TYPE_NAMES}         separator=,
    ${user_bin_typeIds_that_passed_Verification}=     Create List
    ${user_bin_typeNames_that_passed_Verification}=     Create List

    FOR    ${row}    IN    @{db_results}
        ${binNumber_verification_passed}=      Set Variable    ${True}
        ${binNumber_verified}=      Set Variable    ${True}

        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType
        ${binTypeIsDeleted_data}=        Get Column Data By Name       ${row}       binTypeIsDeleted

        Log Many    ${verified_bin_types}
        ${duplicate_bin_type}=      Set Variable    ${False}
        IF    '${bin_type_already_read_value}' == '${EMPTY}'
            ${bin_type_already_read_value}=   Set Variable     ${bin_binType_data}
            Set To Dictionary   ${verified_bin_types}       ${bin_binType_data}=${binTypeIsDeleted_data}
        ELSE
            FOR    ${key}    IN    @{verified_bin_types}
                ${value}=    Get From Dictionary    ${verified_bin_types}    ${key}
                Log    ${key}=${value}
                IF    '${key}' == '${bin_binType_data}' and '${value}' == '${binTypeIsDeleted_data}'
                    ${duplicate_bin_type}=      Set Variable    ${True}
                    Exit For Loop
                END
            END
        END

        IF  '${duplicate_bin_type}' == '${True}'
            CONTINUE
        ELSE
            Set To Dictionary   ${verified_bin_types}       ${bin_binType_data}=${binTypeIsDeleted_data}
        END

        ${bin_toActionedBy_data}=        Get Column Data By Name       ${row}       toBeActionedBy
        ${bin_toActionedBy_data}=        Get Bin To Be Actioned By Text     ${bin_toActionedBy_data}

        IF    '${tobeActionedBy_Value}' == '${EMPTY}'
            ${tobeActionedBy_Value}=   Set Variable     ${bin_toActionedBy_data}
        ELSE
             IF    '${tobeActionedBy_Value}' != '${bin_toActionedBy_data}'
                 CONTINUE
             END
        END


        #Verify the bin details
        ${binID_data}=                   Get Column Data By Name       ${row}       Bin_ID
        ${bin_capturedBy_data}=          Get Column Data By Name       ${row}       capturedBy
        ${bin_capturedDate_data}=        Get Column Data By Name       ${row}       capturedDate

        IF    '${bin_capturedDate_data}' != 'None'
            ${bin_capturedDate_data}=        Format Timestamp for API and Database      ${bin_capturedDate_data}       ${False}
        END

        ${binActionDate_data}=           Get Column Data By Name       ${row}       actionDate
        ${bin_review_status_data}=       Get Column Data By Name       ${row}       reviewStatus
        ${bin_review_status_data}=       Get Bin Status Text     ${bin_review_status_data}
        ${bin_outcome_data}=             Get Column Data By Name       ${row}       outcome
        ${bin_outcome_data}=             Get Bin Outcome Text    ${bin_outcome_data}
        ${bin_rejected_comment_data}=    Get Column Data By Name       ${row}       rejectedComment
        ${bin_reviewedBy_data}=          Get Column Data By Name       ${row}       reviewedBy
        ${bin_reviewed_date_data}=       Get Column Data By Name       ${row}       reviewedDate

        IF    '${bin_reviewed_date_data}' != 'None'
            ${bin_reviewed_date_data}=       Format Timestamp for API and Database      ${bin_reviewed_date_data}       ${False}
        END

        ${bin_latestServerNumber_data}=  Get Column Data By Name       ${row}       LatestServerNumber

        #Check if the Bin ID must be verified against the user's data.
        IF    ${binID_must_be_verified}
            ${binID_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${BIN_ID}'     '${binID_data}'
        END


        #Check if the Bin captured_date must be verified against the user's data.
        IF    ${captured_date_must_be_verified}

            ${captured_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_DATE}'     '${bin_capturedDate_data}'
        END

        #Check if the Bin captured_by must be verified against the user's data.
        IF    ${captured_by_must_be_verified}
            ${captured_by_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${CAPTURED_BY}'     '${bin_capturedBy_data}'
        END

        #Check if the Action Date must be verified against the user's data
        IF    ${actionDate_must_be_verified}
            ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
        END

        #Check if the Action Date must be verified against the user's data
        IF    ${review_status_must_be_verified}
            ${review_status_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEW_STATUS}'     '${bin_review_status_data}'
        END

        #Check if the outcome must be verified against the user's data
        IF    ${outcome_must_be_verified}
            ${outcome_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${OUTCOME}'     '${bin_outcome_data}'
        END


        #Check if the toActionedBy must be verified against the user's data
        #IF    ${toActionedBy_must_be_verified}
        #    ${toActionedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${TO_BE_ACTIONED_BY}'     '${bin_toActionedBy_data}'
        #END

        #Check if the toActionedBy must be verified against the user's data
        IF    ${rejected_comment_must_be_verified}
            ${rejected_comment_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REJECTED_COMMENT}'     '${bin_rejected_comment_data}'
        END

        #Check if the toActionedBy must be verified against the user's data
        IF    ${reviewedBy_must_be_verified}
            ${reviewedBy_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_BY}'     '${bin_reviewedBy_data}'
        END
        #Check if the toActionedBy must be verified against the user's data
        IF    ${reviewed_date_must_be_verified}
            ${reviewed_date_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${REVIEWED_DATE}'     '${bin_reviewed_date_data}'
        END
        #Check if the toActionedBy must be verified against the user's data
        IF    ${latestServerNumber_must_be_verified}
            ${latestServerNumber_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${LATEST_SERVER_NUMBER}'     '${bin_latestServerNumber_data}'
        END

        #Check if the bin types must be verified against the user's data

        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType
        ${bin_binTypeID_data}=             Get Column Data By Name       ${row}     binTypeID

        IF    ${binTypeName_must_be_verified}

            FOR    ${user_bin_type_name_element}    IN    @{users_binTypeName_data_array}
                ${binTypeName_verification_passed}=   Set Variable      ${False}
                IF    '${user_bin_type_name_element.strip()}' == 'OnUs'
                 ${user_bin_type_element}=   Set Variable    On-Us
                END
                #Search for the user provided bin type on the DB returned bin
                ${binTypeName_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${user_bin_type_name_element.strip()}'     '${bin_binType_data.strip()}'


                #Check if the user's bin type was found
                IF  ${binTypeName_verification_passed}
                    ${user_bin_types_names_that_passed_Verification_string}=    Create Comma Separated String    ${user_bin_typeNames_that_passed_Verification}
                    ${verified_bin_types_names_length}=    Get Length    ${user_bin_types_names_that_passed_Verification_string}
                    IF    '${verified_bin_types_names_length}' > '0'
                        IF    '${user_bin_type_name_element.strip()}' not in '${user_bin_types_names_that_passed_Verification_string}'
                             Append To List    ${user_bin_typeNames_that_passed_Verification}        ${user_bin_type_name_element.strip()}
                        END
                    ELSE
                        Append To List    ${user_bin_typeNames_that_passed_Verification}        ${user_bin_type_name_element.strip()}
                    END
                     Exit For Loop
                END
            END
        END

        IF    ${binTypeId_must_be_verified}

            FOR    ${user_bin_type_id_element}    IN    @{users_binTypeId_data_array}
                ${binTypeId_verification_passed}=   Set Variable      ${False}

                #Search for the user provided bin type id on the DB returned bin type id
                ${binTypeId_verification_passed}=      Run Keyword And Return Status    Verify that values are equal     '${user_bin_type_id_element.strip()}'     '${bin_binTypeID_data.strip()}'


                #Check if the user's bin type was found
                IF  ${binTypeId_verification_passed}
                    ${user_bin_types_ids_that_passed_Verification_string}=    Create Comma Separated String    ${user_bin_typeIds_that_passed_Verification}
                    ${verified_bin_types_ids_length}=    Get Length    ${user_bin_types_ids_that_passed_Verification_string}
                    IF    '${verified_bin_types_ids_length}' > '0'
                        IF    '${user_bin_type_id_element.strip()}' not in '${user_bin_types_ids_that_passed_Verification_string}'
                             Append To List    ${user_bin_typeIds_that_passed_Verification}        ${user_bin_type_id_element.strip()}
                        END
                    ELSE
                        Append To List    ${user_bin_typeIds_that_passed_Verification}        ${user_bin_type_id_element.strip()}
                    END
                     Exit For Loop
                END
            END
        END

        #The loop will be exited if all the parameters have been verified against the response
        ${verified_bin_type_names_length}=    Get Length    ${user_bin_typeNames_that_passed_Verification}
        ${user_bin_type_names_length}=    Get Length    ${users_binTypeName_data_array}

        ${verified_bin_type_ids_length}=    Get Length    ${user_bin_typeIds_that_passed_Verification}
        ${user_bin_type_ids_length}=    Get Length    ${users_binTypeId_data_array}

        Run Keyword If    '${verified_bin_type_names_length}' == '${user_bin_type_names_length}' and '${verified_bin_type_ids_length}' == '${user_bin_type_ids_length}'
        ...    Exit For Loop
    END


    #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the Database.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the Database.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${captured_date_must_be_verified}
        Run Keyword If    ${captured_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the CAPTURED DATE: '${CAPTURED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${captured_by_must_be_verified}
        Run Keyword If    ${captured_by_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the CAPTURED BY: '${CAPTURED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${review_status_must_be_verified}
        Run Keyword If    ${review_status_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Review Status value: '${REVIEW_STATUS}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${outcome_must_be_verified}
        Run Keyword If    ${outcome_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the Outcome value: '${OUTCOME}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    #IF  ${toActionedBy_must_be_verified}
    #    Run Keyword If    ${toActionedBy_verification_passed} == ${False}
    #    ...    Run Keyword And Continue On Failure  Fail  The TO_BE_ACTIONED_BY value: '${TO_BE_ACTIONED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
    #    ...  ELSE
    #    ...    Log    Database verification for the TO_BE_ACTIONED_BY value: '${OUTCOME}', for BIN Number: '${TO_BE_ACTIONED_BY}' was successful.
    #END


    IF  ${rejected_comment_must_be_verified}
        Run Keyword If    ${rejected_comment_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the REJECTED_COMMENT value: '${REJECTED_COMMENT}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${reviewedBy_must_be_verified}
        Run Keyword If    ${reviewedBy_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the REVIEWED_BY value: '${REVIEWED_BY}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${reviewed_date_must_be_verified}
        Run Keyword If    ${reviewed_date_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the REVIEWED_DATE value: '${REVIEWED_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${latestServerNumber_must_be_verified}
        Run Keyword If    ${latestServerNumber_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Database verification for the LATEST_SERVER_NUMBER value: '${LATEST_SERVER_NUMBER}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binTypeName_must_be_verified}

        #Verify that all user provided Bin Types were verified
        ${individual_bin_verification_failed}=      Set Variable   ${False}
        ${user_bin_types_not_found}=   Create List
        ${user_bin_type_names_that_passed_Verification_string}=    Create Comma Separated String    ${user_bin_typeNames_that_passed_Verification}

        FOR    ${item}    IN    @{users_binTypeName_data_array}
            Run Keyword If    '${item.strip()}' not in '${user_bin_type_names_that_passed_Verification_string}'    Append To List    ${user_bin_types_not_found}    ${item}
        END
        Log Many        @{user_bin_types_not_found}
        ${length}=    Get Length    ${user_bin_types_not_found}

        IF    ${length} > 0
              ${individual_bin_verification_failed}=      Set Variable   ${True}
              ${bin_types_string}=    Create Comma Separated String    ${user_bin_types_not_found}
        END

        Run Keyword If    ${individual_bin_verification_failed} == ${True}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE value(s): '${bin_types_string}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Verification for the BIN_TYPE_NAME value(s): '${BIN_TYPE_NAMES}', for BIN Number: '${BIN_NUMBER}' was successful.
    END


    IF  ${binTypeId_must_be_verified}

        #Verify that all user provided Bin Types were verified
        ${individual_bin_verification_failed}=      Set Variable   ${False}
        ${user_bin_type_ids_not_found}=   Create List
        ${user_bin_type_ids_that_passed_Verification_string}=    Create Comma Separated String    ${user_bin_typeIds_that_passed_Verification}

        FOR    ${item}    IN    @{users_binTypeId_data_array}
            Run Keyword If    '${item.strip()}' not in '${user_bin_type_ids_that_passed_Verification_string}'    Append To List    ${user_bin_type_ids_not_found}    ${item}
        END
        Log Many        @{user_bin_type_ids_not_found}
        ${length}=    Get Length    ${user_bin_type_ids_not_found}

        IF    ${length} > 0
              ${individual_bin_verification_failed}=      Set Variable   ${True}
              ${bin_types_string}=    Create Comma Separated String    ${user_bin_type_ids_not_found}
        END

        Run Keyword If    ${individual_bin_verification_failed} == ${True}
        ...    Run Keyword And Continue On Failure  Fail  The BIN_TYPE_ID value(s): '${bin_types_string}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    Verification for the BIN_TYPE_ID value(s): '${BIN_TYPE_IDS}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



# The below keywords are used to interact with the API POJOS
Create GetBinById Instance
    [Arguments]    ${BASE_URL}  ${BIN_ID}
    ${instance}=    Evaluate    GetBinById.CreateRESTRequest('${BASE_URL}','${BIN_ID}')    modules=GetBinById
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords

Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}



Get Response Status Code
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

#Keywords to read error response fields
Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keyword to read successful response
Get the Bin Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bin_by_id_details
    RETURN    ${result}

Get the Bin Response field data
     [Arguments]     ${BINS_INSTNANCE}   ${METHOD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    ${METHOD_NAME}
    RETURN    ${result}





