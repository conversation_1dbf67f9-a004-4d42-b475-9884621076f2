*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Validate Delete Link functionality in Email Management

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/EmailManagement.robot
Resource                                            ../../keywords/common/DatabaseConnector.robot

*** Variables ***


*** Keywords ***
Validate Delete Link-Email Management
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - Email Management
    And Delete Random Email From Database

*** Test Cases ***
| Validate Delete Link- Email Management: Delete a random existing vendor email. |
| | [Documentation] | Validate the delete functionality for vendor emails in Email Management. |
| | Validate Delete Link-Email Management | Validate the delete functionality for vendor emails in Email Management. | VMS_UAT |
