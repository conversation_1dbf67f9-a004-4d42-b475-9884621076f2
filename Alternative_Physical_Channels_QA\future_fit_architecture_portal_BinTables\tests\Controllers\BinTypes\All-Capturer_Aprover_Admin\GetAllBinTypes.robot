*** Settings ***
# Author Name               : Thabo
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/GetAllBinTypes_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
GET Request to get all Bin Types
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}   ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    # Step 1: Send DELETE request to the API
    Given The User sends a GET Request to get all Bin Types   ${BASE_URL}

    # Step 2: Check for expected status code
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}

| *** Test Cases ***                                     |              *DOCUMENTATION*    	|    *BASE_URL*                  |   *EXPECTED_STATUS_CODE*
| GET all Bin Types   | GET Request to get all Bin Types | GET Request to get all Bin Types |                                 |       200
