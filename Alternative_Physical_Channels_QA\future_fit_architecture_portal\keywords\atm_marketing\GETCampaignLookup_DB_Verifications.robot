*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             String
Variables                                          ../../utility/SQLVariables.py
#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***
Verify GET CampaignLookup controller fields on the database
    [Arguments]     ${json_response}
    Set Environment Variable    FIELD_FOUND        ${False}


    ${new_string}=   Convert To String    ${json_response}
    ${dict_value}=      Convert String to Dictionary    ${new_string}

    #Verify the marketingChannels details
    Set Environment Variable    FIELD_TO_VERIFY        marketingChannels
    Set Environment Variable    FIELD_FOUND        ${False}
    ${marketingChannels_field_value}=   Get field value from JSON    ${dict_value}        marketingChannels
    ${marketingChannels_field_value}=   Replace String      ${marketingChannels_field_value}    '   "
    ${list_of_dicts}=    Convert String to Dictionary           ${marketingChannels_field_value}

    FOR     ${dict}    IN    @{list_of_dicts}
        ${marketing_channel_id}=      Get From Dictionary    ${dict}    id
        ${marketing_channel_name}=      Get From Dictionary    ${dict}    channel
        ${marketing_channel_imageResolution}=      Get From Dictionary    ${dict}    imageResolution

        #Verify the details against the database
        ${db_campaign_lookup_data}=        Get the CampaignLookup details from the database      ${marketing_channel_id}      ${SQL_GET_MARKETING_CHANNELS}
        ${db_channel_id}=    Get From Dictionary    ${db_campaign_lookup_data}    id
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_channel_id}    ${db_channel_id}
        Log Many     Comparison result for 'marketing_channel_id' : Controller Results = ${marketing_channel_id}, Database Results = ${db_channel_id}

        ${db_channel_name}=    Get From Dictionary    ${db_campaign_lookup_data}    channel
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_channel_name}    ${db_channel_name.strip()}
        Log Many     Comparison result for 'marketing_channel_name' : Controller Results = ${marketing_channel_name}, Database Results = ${db_channel_name.strip()}

        ${db_channel_imageResolution}=    Get From Dictionary    ${db_campaign_lookup_data}    imageResolution
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_channel_imageResolution}    ${db_channel_imageResolution.strip()}
        Log Many     Comparison result for 'marketing_channel_imageResolution' : Controller Results = ${marketing_channel_imageResolution}, Database Results = ${db_channel_imageResolution.strip()}

    END


    #Verify the marketingScreens details
    Set Environment Variable    FIELD_TO_VERIFY        marketingScreens
    Set Environment Variable    FIELD_FOUND        ${False}
    ${marketingScreens_field_value}=   Get field value from JSON    ${dict_value}        marketingScreens
    ${marketingScreens_field_value}=   Replace String      ${marketingScreens_field_value}    '   "
    ${list_of_dicts}=    Convert String to Dictionary           ${marketingScreens_field_value}

    FOR     ${dict}    IN    @{list_of_dicts}
        ${marketing_screens_id}=      Get From Dictionary    ${dict}    id
        ${marketing_screens_type}=      Get From Dictionary    ${dict}    screenType
        ${marketing_screens_number}=      Get From Dictionary    ${dict}    screenNumber
        ${marketing_screens_channelID}=      Get From Dictionary    ${dict}    channelId
        #Verify the details against the database
        ${db_campaign_lookup_data}=        Get the CampaignLookup details from the database      ${marketing_screens_id}      ${SQL_GET_MARKETING_SCREENS}
        ${db_screen_id}=    Get From Dictionary    ${db_campaign_lookup_data}    id
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_screens_id}    ${db_screen_id}
        Log Many     Comparison result for 'marketing_screens_id' : Controller Results = ${marketing_screens_id}, Database Results = ${db_screen_id}

        ${db_screen_type}=    Get From Dictionary    ${db_campaign_lookup_data}    screenType
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_screens_type}    ${db_screen_type.strip()}
        Log Many     Comparison result for 'marketing_screens_type' : Controller Results = ${marketing_screens_type}, Database Results = ${db_screen_type.strip()}

        ${db_screen_number}=    Get From Dictionary    ${db_campaign_lookup_data}    screenNumber
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_screens_number}    ${db_screen_number.strip()}
        Log Many     Comparison result for 'marketing_screens_number' : Controller Results = ${marketing_screens_number}, Database Results = ${db_screen_number.strip()}

        ${db_screen_channelID}=    Get From Dictionary    ${db_campaign_lookup_data}    channelId
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_screens_channelID}    ${db_screen_channelID}
        Log Many     Comparison result for 'marketing_screens_channelID' : Controller Results = ${marketing_screens_channelID}, Database Results = ${db_screen_channelID}

    END

    #Verify the languages details
    Set Environment Variable    FIELD_TO_VERIFY        languages
    Set Environment Variable    FIELD_FOUND        ${False}
    ${languages_field_value}=   Get field value from JSON    ${dict_value}        languages
    ${languages_field_value}=   Replace String      ${languages_field_value}    '   "
    ${list_of_dicts}=    Convert String to Dictionary           ${languages_field_value}

    FOR     ${dict}    IN    @{list_of_dicts}
        ${language_id}=      Get From Dictionary    ${dict}    id
        ${language}=      Get From Dictionary    ${dict}    language
        ${language_code}=      Get From Dictionary    ${dict}    languageCode

        #Verify the details against the database
        ${db_campaign_lookup_data}=        Get the CampaignLookup details from the database      ${language_id}      ${SQL_GET_MARKETING_LANGUAGES}
        ${db_language_id}=    Get From Dictionary    ${db_campaign_lookup_data}    id
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${language_id}    ${db_language_id}
        Log Many     Comparison result for 'language_id' : Controller Results = ${language_id}, Database Results = ${db_language_id}

        ${db_language}=    Get From Dictionary    ${db_campaign_lookup_data}    language
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${language}    ${db_language.strip()}
        Log Many     Comparison result for 'language' : Controller Results = ${language}, Database Results = ${db_language.strip()}

        ${db_language_code}=    Get From Dictionary    ${db_campaign_lookup_data}    languageCode
        Run Keyword And Continue On Failure    Should Be Equal As Strings    ${language_code}    ${db_language_code.strip()}
        Log Many     Comparison result for 'language_code' : Controller Results = ${language_code}, Database Results = ${db_language_code.strip()}

    END


Get the CampaignLookup details from the database
    [Arguments]     ${ID}   ${QUERY}

    #Verify that all parameters are supplied
    Run Keyword If    '${ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'

    ${marketing_channels_details_query}   Set Variable     ${QUERY}
    ${marketing_channels_details_query}=  Replace String      ${marketing_channels_details_query}       ID     '${ID}'
    ${marketing_channels_details}=      Execute SQL Query and Continue if no records found  ${db_type}  ${marketing_channels_details_query}    True

    RETURN   ${marketing_channels_details}






