*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  View complaint/compliment details

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py

#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${STATU_DROPDOWN}                                   id~updSelectStatus                        
${COMPLAINT_REF_TXT}                              xpath=//label[@id='MainContent_lblRefNo']                                                     
${REASON_TXT}                                     id=MainContent_lblReason
${COMMENT_TXT}                                    id=MainContent_lblComment
${CUSTOMER_NAME_TXT}                              id=MainContent_lblCustomerName
${CUSTOMER_SURNAME_TXT}                           id=MainContent_lblCustomerSurname
${CUSTOMER_CELLPHONE_TXT}                         id=MainContent_lblCustomerCellphone
${CUSTOMER_EMAIL_TXT}                             id=MainContent_lblCustomerEmail
${ATM_NUMBER_TXT}                                 id=MainContent_lblAtmNumber
${ATM_LOCATION_TXT}                               id=MainContent_lblAtmLocation
${CITY_TXT}                                       id=MainContent_lblCity
${REGION_TXT}                                     id=MainContent_lblProvince
${SERIAL_NUMBER_TXT}                              id=MainContent_lblSerialNo
${MODEL_TXT}                                      id=MainContent_lblModel
${ZONE_TXT}                                       id=MainContent_lblZone
${INSTITUTION_TXT}                                id=MainContent_lblInstitution
${COMPLAINT_HISTORY_TXT}                          xpath=//label[contains(text(),'Complaint History')]
${ASSIGNED_TO_TXT}                                id=MainContent_lblAssignedTo

${COMPLAINT_HISTORY}                              xpath=//label[contains(text(),'Complaint History')]
${CANCEL_BTN}                                     xpath=//input[@value='Cancel']

*** Keywords ***
Close the popup screen
    Click Element  ${CLOSE_POPUP_BTN}

User clicks on COMPLAINT HISTORY link
    Click Element                                   ${COMPLAINT_HISTORY} 

User views complaint/compliment details
    Log to console  --------------------------User views complaint/compliment details
    # [Arguments]    ${COMPLAIN_REF_VALUE}  ${REASON_VALUE}  ${COMMENT_VALUE}  ${CUSTOMER_NAME_VALUE}  ${CUSTOMER_SURNAME_VALUE}  ${CUSTOMER_CELLPHONE_VALUE}  ${CUSTOMER_EMAIL_VALUE}  ${ATM_NUMBER_VALUE}  ${ATM_LOCATION_VALUE}  ${CITY_VALUE}  ${REGION_VALUE}  ${SERIAL_NUMBER_VALUE}  ${MODEL_VALUE}  ${ZONE_VALUE}  ${INSTITUTION_VALUE}  ${ASSIGNED_TO_VALUE}  ${NEW_STATUS} 
    [Arguments]    ${COMMENT_VALUE}  ${ASSIGNED_TO_VALUE}  ${NEW_STATUS}    ${REFERNCE_NO}    
    Log to console  Starting with viewing complaints detail 

    User clciks on details link

    Sleep  9s
    
    User clicks on COMPLAINT HISTORY link
    ${row}=             get table row  //div[@id='Complaint_Det']//*/tbody/tr
    ${row2}=             get table row    //div[@id='Complaint_Det']//*/tbody/tr[2]

    # VALIDATE ASSIGNED TO ONLY IF THERE IS A VALUE TO COMPARE AGAINST
    Log to console  --------------------------Starting with viewing complaints detail
    Log to console  Assigned to consultant: ${ASSIGNED_TO_VALUE} 

    Run Keyword If	'${ASSIGNED_TO_VALUE}' !="${EMPTY}" and '${NEW_STATUS}' != 'Assign'  Validate assiged-to consultant  ${ASSIGNED_TO_VALUE}  ${row}	
    Run Keyword If  '${NEW_STATUS}' !="${EMPTY}" and '${NEW_STATUS}' != 'Assigned' and '${NEW_STATUS}' != 'Resolved'   Validate that the status was successfully chanaged  ${NEW_STATUS}  ${row}  ${COMMENT_VALUE}  ${NEW_STATUS}
    Run Keyword If	'${NEW_STATUS}' !="${EMPTY}" and '${NEW_STATUS}'== 'Resolved'	Validate that the status was successfully changed to Resolved   ${ASSIGNED_TO_VALUE}  ${row}  ${REFERNCE_NO}  ${NEW_STATUS}  ${row2}  ${COMMENT_VALUE}
    Sleep  9s

    Close the popup screen

    Sleep  5s
Validate assiged-to consultant
    Log to console  --------------------------Validate assiged-to consultant
    [Arguments]    ${ASSIGNED_TO_VALUE}  ${row}

    ${assigned_to}=                                 GET TEXT  ${ASSIGNED_TO_TXT}

    Log to console  Actual Assigned to: ${assigned_to}

    Log to console  Expected Assigned to: ${ASSIGNED_TO_VALUE}

    Should Contain  ${ASSIGNED_TO_VALUE}  ${assigned_to}

    Log to console  Row is: ${row}

    Log to console  History top row: ${row}

    Should Contain	${row}	Re-Assign to Consultant                                         msg=History table is not updated with 'Re-Assign to Consultant' in the Update Type column

    Should Contain	${row}	Re-Assign to Consultant                                         msg=History table is not updated with 'Re-Assign to Consultant' in the Comments column

    Should Contain	${row}	Assigned	                                                    msg=History table is not updated with 'Assigned' in the Status column


Validate that the status was successfully chanaged
    [Arguments]    ${ASSIGNED_TO_VALUE}  ${row}  ${COMMENT_VALUE}  ${NEW_STATUS}

    Log to console  --------------------------Validating status on Details screen

    Page Should Contain Element                     xpath=//div[@id='second' and contains(text(),'${ASSIGNED_TO_VALUE}')]

    Log to console  Row is: ${row}

    Log to console  History top row: ${row}

    Should Contain	${row}	Status Update	                                                msg=History table is not updated with 'Status Update' in the Update Type column

    Should Contain	${row}	${COMMENT_VALUE}	                                            msg=History table is not updated with '${COMMENT_VALUE}' in the Comments column

    Should Contain	${row}	${NEW_STATUS}	                                                msg=History table is not updated with '${NEW_STATUS}' in the Status column

Validate that the status was successfully changed to Resolved

  [Arguments]    ${ASSIGNED_TO_VALUE}  ${row}  ${REFERNCE_NO}  ${NEW_STATUS}  ${row2}  ${COMMENT_VALUE}

    Log to console  --------------------------Validating status on Details screen

    Page Should Contain Element                     xpath=//div[@id='second' and contains(text(),'${ASSIGNED_TO_VALUE}')]

//variable
    ${SMS_Comment}=    Set Variable    Absa: Thank you for logging an ATM complaint. Ref ${REFERNCE_NO} has been resolved. Thank you for using our ATM. Auth FSP	


    Log to console  Row is: ${row}

    Log to console  History top row: ${row}

    Should Contain	${row}	SMS Notification	                                            msg=History table is not updated with 'SMS Notification' in the Update Type column

    Should Contain	${row}	                                                                msg=History table is not updated with '${REFERNCE_NO}' in the Comments column

    Should Contain	${row}	${NEW_STATUS}	                                                msg=History table is not updated with '${NEW_STATUS}' in the Status column


    Sleep    9s
    #validate the second row
    Log to console  second Row is: ${row2}

    Log to console  second History top row: ${row2}

    Should Contain	${row2}	Status Update	                                                 msg=History table is not updated with 'Status Update' in the Update Type column

    Should Contain	${row2}	${COMMENT_VALUE}	                                             msg=History table is not updated with '${COMMENT_VALUE}' in the Comments column

    Should Contain	${row2}	${NEW_STATUS}	                                                 msg=History table is not updated with '${NEW_STATUS}' in the Status column

      #validation after changing the status to look for sms on Resolved status
    Log to console    ------------------------ Validate SMS NOTIFICATION
       

    Sleep    9s

    #Get Text for update type
    ${UPDATE_TYPE} =    Get Text    //*[@id="Complaint_Det"]/div/table/tbody/tr[1]/td[2]
    Log to console    -----------------  
    Log to console      ${UPDATE_TYPE} 

    #Get text for update by
    ${UPDATE_BY} =    Get Text    //*[@id="Complaint_Det"]/div/table/tbody/tr[1]/td[5]
    Log to console    -----------------  
    Log to console      ${UPDATE_BY}

    #validate sms notification and system 
    ${UPDATE_TYPE_status_update}=    Set Variable    Status Update
    Log to console   ${UPDATE_TYPE_status_update} 


    Should Contain	${UPDATE_TYPE}    ${UPDATE_TYPE_status_update}                        msg=Update type is not equal to System update
    Should Contain	${UPDATE_BY}    ${APPLICATION_USERNAME}                               msg=Update by is not equal to the ab number


    #Validate second row on the table
    #Get Text for update type
    ${UPDATE_TYPE_2} =    Get Text    //*[@id="Complaint_Det"]/div/table/tbody/tr[2]/td[2]
    Log to console    -----------------  
    Log to console      ${UPDATE_TYPE_2} 

    #Get text for update by
    ${UPDATE_BY_2} =    Get Text    //*[@id="Complaint_Det"]/div/table/tbody/tr[2]/td[5]
    Log to console    -----------------  
    Log to console      ${UPDATE_BY_2}

    #validate status update and updated by
     ${SMS_Notification}=    Set Variable    SMS Notification
    ${System}=    Set Variable    System

    Log to console    ${SMS_Notification}
    Log to console    ${System}

   # Should Contain	${UPDATE_TYPE_2}    ${UPDATE_TYPE_status_update}                        msg=Update type is not equal to SMS Notification
    Should Contain	${UPDATE_BY_2}    ${System}                                   msg=Update by is not equal to System


    Log to console    ------------------------ SMS NOTIFICATION 