import os
from jproperties import Properties
import mysql
from robot.api.deco import keyword, library
from mysql.connector import <PERSON>rror
import pyodbc
import json
import os
import sys
from datetime import datetime, timedelta
from robot.api.deco import keyword, library
from jsonpath_ng import parse
import numbers
import base64
import json
import hashlib
import hmac

from random_word import RandomWords
from robot.libraries.BuiltIn import BuiltIn
from selenium.webdriver.remote.webelement import WebElement
from dateutil import parser
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.webdriver import WebDriver
import pyautogui
import time


def find_project_root(start_path, marker='data'):
    print(start_path)
    current_path = start_path
    print(os.path.dirname(current_path))

    while current_path != os.path.dirname(current_path):  # Check if we are at the root
        if marker in os.listdir(current_path):
            return current_path
        current_path = os.path.dirname(current_path)
    return None


def get_variable_data_type(str_var):
    data_type = type(str_var)
    # <class 'dict'>
    data_type = str(data_type).replace('<class \'', '')
    data_type = data_type.replace('\'>', '')
    # print(data_type)
    return data_type


def get_current_date_time(str_num_of_days):
    global upload_date_field_data

    # Verify if the provided days are in correct format
    negating_char = ''
    num_of_days = ''
    if str(str_num_of_days).startswith('-'):
        negating_char = '-'
        num_of_days = str(str_num_of_days).replace("-", "")
        num_of_days = num_of_days.strip()
    else:
        num_of_days = str(str_num_of_days).strip()

    num_of_days = set_data_type_to_integer(num_of_days)

    value_is_int = isinstance(num_of_days, numbers.Integral)

    if not value_is_int:
        num_of_days = '0'

    num_of_days = str(num_of_days)

    num_of_days = negating_char + num_of_days
    num_of_days = num_of_days.strip()

    num_of_days = set_data_type_to_integer(num_of_days)

    # current date and time
    now = datetime.now()
    date2 = now + timedelta(days=num_of_days)
    date_array = str(date2).split(' ')
    json_date = date_array[0] + 'T' + date_array[1][:-3] + 'Z'
    upload_date_field_data = json_date
    return str(json_date)


def field_is_boolean_type(key):
    boolean_fields = ['isActive', 'isApproved', 'isTargetted', 'isTargetRegion', 'isUpaloadSuccessful']
    # check whether the key is in boolean_fields
    return len(list(filter(lambda x: x == key, boolean_fields))) > 0


def field_is_integer_type(key):
    integer_fields = ['marketingChannelId', 'id', 'screenId', 'duration', 'priority', 'campaignId', 'approvalId']
    # check whether the key is in boolean_fields
    return len(list(filter(lambda x: x == key, integer_fields))) > 0


def field_is_float_type(key):
    float_fields = ['version']
    # check whether the key is in boolean_fields
    return len(list(filter(lambda x: x == key, float_fields))) > 0


def is_variable_an_image(variable):
    global image_data
    variable_content = variable.lower()

    if variable_content.endswith('.jpg') or variable_content.endswith('.png') or variable_content.endswith('.gif'):
        image_data = "data:image/" + variable_content[-3:] + ";base64,"
        return True
    else:
        return False


def convert_image_to_base_64(image_file_path):
    root = 'future_fit_architecture_portal'
    print('path', root)
    full_file_path = os.path.join(root, image_file_path)
    with open(full_file_path, 'rb') as image_file:
        base64_bytes = base64.b64encode(image_file.read())
        base64_string = base64_bytes.decode()
        return base64_string


def json_object_is_an_array(str_variable):
    if not str_variable.isalnum():  # Check if all the characters in the text are not alphanumeric
        return True
    else:
        return False


def set_data_type_to_boolean(variable):
    if variable == "True":
        value = True
    else:
        value = False
    return value


def set_data_type_to_integer(variable):
    return int(variable)


def set_data_type_to_float(variable):
    return float(variable)


def parse_date(date_string, date_format):
    # List of allowed date formats within this function
    allowed_formats = [
        "%Y-%m-%d",  # 2025-01-20
        "%m/%d/%Y",  # 01/20/2025
        "%d-%m-%Y",  # 20-01-2025
        "%d/%m/%Y",  # 20/01/2025
        "%Y/%m/%d",  # 2025/01/20
        "%d-%b-%Y",  # 20-Jan-2025
        "%d %b %Y",  # 20 Jan 2025
        "%b %d, %Y",  # Jan 20, 2025
        "%B %d, %Y",  # January 20, 2025
        "%Y-%m-%d %H:%M:%S",  # 2025-01-20 14:30:00
        "%m-%d-%Y",  # 1-20-2025 (single digit month)
        "%d-%m-%Y",  # 20-1-2025 (single digit month)
    ]

    # If the format provided by the user is not in the allowed formats, fail
    if date_format not in allowed_formats:
        raise ValueError(f"Unsupported date format: {date_format}. Please use one of the allowed formats.")

    try:
        # Parse the input date string to a datetime object
        parsed_date = parser.parse(date_string)

        # Convert the datetime object to the desired output format
        return parsed_date.strftime(date_format)
    except (ValueError, TypeError) as e:
        # Handle cases where the date parsing fails
        print(f"Error parsing date: {str(e)}")
        return None  # Return None if parsing fails


def parse_value(value):
    """
    Parse the value into an appropriate comparable format.
    """
    # Try to parse as a date (assuming the format is Month Day, Year e.g., 'Jan 24, 2025')
    try:
        return datetime.strptime(value, "%b %d, %Y")
    except ValueError:
        pass  # If it's not a date, continue to next check

    # Try to parse as a number
    try:
        return float(value)  # Convert to float to handle both integers and floating point numbers
    except ValueError:
        pass  # If it's not a number, continue to next check

    # If it's not a number or date, treat it as a string
    return value


@library(scope='GLOBAL', auto_keywords=True)
class CommonUtils:
    global db_connection

    @keyword
    def verify_data_using_database(self, db_type, sql_select_query, return_data=False, return_all_records=False,
                                   **kwargs):
        global db_connection
        keyword_instance = CommonUtils()
        try:

            data_found = False

            match db_type:
                case "'MYSQL'":
                    print('connecting to MYSQL...')
                    db_host = keyword_instance.read_config_property('DB_HOST')
                    db_schema = keyword_instance.read_config_property('DB_SCHEMA')
                    db_user = keyword_instance.read_config_property('DB_User')
                    db_pwd = keyword_instance.read_config_property('DB_PWD')
                    db_connection = mysql.connector.connect(host=db_host,
                                                            database=db_schema,
                                                            user=db_user,
                                                            password=db_pwd)
                    if db_connection.is_connected():
                        print('connected to MSSQL...')
                        db_info = db_connection.get_server_info()
                        print("Connected to MySQL Server version ", db_info)
                        cursor = db_connection.cursor()
                        cursor.execute("select database();")

                        record = cursor.fetchone()
                        print("You're connected to database: ", record)

                        cursor.execute(sql_select_query)
                        record = cursor.fetchall()

                        # Verify that the query returns Results
                        if len(record) == 0:
                            print('Query executed did not return Results!')
                            return 'Failed'
                        else:
                            print(len(record), 'is the total number of records returned by the query executed.')

                        cursor.execute(sql_select_query)
                        # build an array of key, value objects using the rows and columns of sql Results returned
                        rows = [x for x in cursor]
                        cols = [x[0] for x in cursor.description]
                        records = []

                        for row in rows:
                            record = {}
                            for prop, val in zip(cols, row):
                                record[prop] = val
                            records.append(record)

                        # Create a JSON string representation of array of SQL Results.
                        records_json = json.dumps(records, indent=4, sort_keys=True, default=str)

                        # Convert JSON data to a Python object
                        json_data = json.loads(records_json)

                        # The data will be verified against the database, the returned value will either be an SQL
                        # record or the boolean value
                        verification_failed = False
                        current_record = {}

                        if len(kwargs) == 0:
                            if return_all_records:
                                print('Returning all records....')
                                return json_data
                            else:
                                print('Returning 1 record....')
                                return json_data[0]

                        for data_key, data_value in kwargs.items():
                            value_found = False

                            for item in json_data:

                                for key, value in item.items():

                                    if str(data_key).strip() == str(key).strip():
                                        if str(data_value).strip() == str(value).strip():
                                            value_found = True
                                            current_record = item
                                            break
                            if value_found:
                                print({data_key}, '=', {data_value}, 'WAS FOUND ON THE RETURNED RESULTS.')
                                data_found = True
                            else:
                                print({data_key}, '=', {data_value}, 'WAS NOT FOUND ON THE RETURNED RESULTS!')
                                data_found = False
                                verification_failed = True

                        if not data_found or verification_failed:
                            # Check if data needs to be returned to the user, or if it must only be verified without
                            # being returned
                            if return_data:  # if data must be returned from the database
                                if return_all_records:
                                    print('Returning all records....')
                                    return json_data
                                else:
                                    print('Returning 1 record....')
                                    return current_record
                            else:
                                return 'Failed'
                        else:

                            # Check if data needs to be returned to the user, or if it must only be verified without
                            # being returned
                            if return_data:  # if data must be returned from the database
                                if return_all_records:
                                    print('Returning all records....')
                                    return json_data
                                else:
                                    print('Returning 1 record....')
                                    return current_record
                            else:
                                return 'Passed'

                case "'MSSQL'":
                    print('connecting to MSSQL...')
                    server = keyword_instance.read_config_property('MS_DB_HOST')
                    database = keyword_instance.read_config_property('MS_DB_SCHEMA')
                    username = keyword_instance.read_config_property('MS_DB_User')
                    password = keyword_instance.read_config_property('MS_DB_PWD')
                    db_connection = pyodbc.connect(
                        'DRIVER={SQL Server};SERVER=' + server + ';DATABASE=' + database + ';UID=' + username + ';PWD=' + password)
                    # cursor = db_connection.cursor()
                    # cursor.execute(sql_select_query)

                    # db_info = db_connection.get_server_info()
                    # print("Connected to MySQL Server version ", db_info)
                    cursor = db_connection.cursor()
                    # cursor.execute("select database();")

                    # record = cursor.fetchone()
                    # print("You're connected to database: ", record)

                    cursor.execute(sql_select_query)
                    record = cursor.fetchall()

                    # Verify that the query returns Results
                    if len(record) == 0:
                        print('Query executed did not return Results!')
                        return 'Failed'
                    else:
                        print(len(record), 'is the total number of records returned by the query executed.')

                    cursor.execute(sql_select_query)
                    # build an array of key, value objects using the rows and columns of sql Results returned
                    rows = [x for x in cursor]
                    cols = [x[0] for x in cursor.description]
                    records = []

                    for row in rows:
                        record = {}
                        for prop, val in zip(cols, row):
                            record[prop] = val
                        records.append(record)

                    # Create a JSON string representation of array of SQL Results.
                    records_json = json.dumps(records, indent=4, sort_keys=True, default=str)

                    # Convert JSON data to a Python object
                    json_data = json.loads(records_json)

                    # The data will be verified against the database, the returned value will either be an SQL record or
                    # the boolean value
                    verification_failed = False
                    current_record = {}

                    if len(kwargs) == 0:
                        if return_all_records:
                            print('Returning all records....')
                            return json_data
                        else:
                            print('Returning 1 record....')
                            return json_data[0]

                    for data_key, data_value in kwargs.items():
                        value_found = False

                        for item in json_data:

                            for key, value in item.items():

                                if str(data_key).strip() == str(key).strip():
                                    if str(data_value).strip() == str(value).strip():
                                        value_found = True
                                        current_record = item
                                        break
                        if value_found:
                            print({data_key}, '=', {data_value}, 'WAS FOUND ON THE RETURNED RESULTS.')
                            data_found = True
                        else:
                            print({data_key}, '=', {data_value}, 'WAS NOT FOUND ON THE RETURNED RESULTS!')
                            data_found = False
                            verification_failed = True

                    if not data_found or verification_failed:
                        # Check if data needs to be returned to the user, or if it must only be verified without
                        # being returned
                        if return_data:  # if data must be returned from the database
                            if return_all_records:
                                print('Returning all records....')
                                return json_data
                            else:
                                print('Returning 1 record....')
                                return current_record
                        else:
                            return 'Failed'
                    else:

                        # Check if data needs to be returned to the user, or if it must only be verified without
                        # being returned
                        if return_data:  # if data must be returned from the database
                            if return_all_records:
                                print('Returning all records....')
                                return json_data
                            else:
                                print('Returning 1 record....')
                                return current_record
                        else:
                            return 'Passed'
                case _:
                    print('No match found for DB:', db_type)
        except Error as e:
            print("Error while connecting to MySQL", e)
        finally:
            if db_type.upper == 'MYSQL' and db_connection.is_connected():
                cursor.close()
                db_connection.close()
                print("MySQL connection is closed")
        return 'Failed'

    @keyword
    def count_dictionaries(self, my_list):
        return len([item for item in my_list if isinstance(item, dict)])

    @keyword
    def read_config_property(self, str_property_name):
        configs = Properties()

        current_directory = os.path.dirname(os.path.realpath(__file__))
        file_name = 'app-config.properties'
        configs_file_path = os.path.join(current_directory, file_name)

        with open(configs_file_path, 'rb') as config_file:
            configs.load(config_file)
            try:
                if configs[str_property_name] is not None:
                    property_retrieved = configs.get(str_property_name).data
                    print("Property value fetched is: ", property_retrieved)
                    return property_retrieved
                else:
                    print("Property named: " + str_property_name + " does not exist on file!")
                    return None
            except KeyError as ke:
                print(f'{ke}, lookup key was: ' + str(str_property_name))
                return None

    @keyword
    def generate_random_word(self):
        r = RandomWords()
        generated_word = r.get_random_word()
        generated_word = generated_word.capitalize()
        word_to_append = "bin type"
        result = f"{generated_word} {word_to_append}"
        return result

    @keyword
    def get_element_properties(self, element: WebElement):
        # Create a dictionary to store both attributes and properties
        properties = {}

        # Retrieve the attributes using get_attribute
        attributes = element.get_property('attributes')  # This retrieves attributes from the element
        for attr in attributes:
            properties[attr['name']] = attr['value']

        # List of properties to retrieve (add more properties as needed)
        property_list = [
            'value',  # e.g., value of an input field
            'innerHTML',  # e.g., inner HTML content of the element
            'innerText',  # e.g., text content of the element
            'outerHTML',  # full HTML of the element (including the tag itself)
            'textContent',  # text inside the element
            'className',  # class of the element
            'style',  # style properties applied
            'id',  # id of the element
            'disabled',  # id of the element
        ]

        # Retrieve properties and add to the dictionary
        for prop in property_list:
            try:
                properties[prop] = element.get_property(prop)
            except Exception as e:
                properties[prop] = f"Error retrieving {prop}: {str(e)}"

        # Return the properties dictionary
        return properties

    @keyword
    def check_if_valid_date(self, date_string):
        # List of common date formats
        date_formats = [
            "%Y-%m-%d",  # 2025-01-20
            "%m/%d/%Y",  # 01/20/2025
            "%d-%m-%Y",  # 20-01-2025
            "%d/%m/%Y",  # 20/01/2025
            "%Y/%m/%d",  # 2025/01/20
            "%d-%b-%Y",  # 20-Jan-2025
            "%d %b %Y",  # 20 Jan 2025
            "%b %d, %Y",  # Jan 20, 2025
            "%B %d, %Y",  # January 20, 2025
            "%Y-%m-%d %H:%M:%S",  # 2025-01-20 14:30:00
            "%m-%d-%Y",  # 1-20-2025 (single digit month)
            "%d-%m-%Y",  # 20-1-2025 (single digit month)
        ]

        for date_format in date_formats:
            try:
                # Try to parse the date with the current format
                datetime.strptime(date_string, date_format)
                return True
            except ValueError:
                # If parsing fails, continue checking with the next format
                continue

        # If no format matches, return False
        return False

    @keyword
    def convert_to_date(self, date_string):
        # Convert date string to desired format
        try:
            print('date_string is ', date_string)
            parsed_date = datetime.strptime(date_string, "%b %d, %Y")
            return parsed_date.strftime("%Y-%m-%d")
        except ValueError:
            print('ValueError', ValueError)
            return date_string  # Return None if the date is invalid

    @keyword
    def extract_date_component(self, date_string, component):
        # List of common date formats to try parsing
        date_formats = [
            "%Y-%m-%d",  # 2025-01-20
            "%m/%d/%Y",  # 01/20/2025
            "%d-%m-%Y",  # 20-01-2025
            "%d/%m/%Y",  # 20/01/2025
            "%Y/%m/%d",  # 2025/01/20
            "%d-%b-%Y",  # 20-Jan-2025
            "%d %b %Y",  # 20 Jan 2025
            "%b %d, %Y",  # Jan 20, 2025
            "%B %d, %Y",  # January 20, 2025
            "%Y-%m-%d %H:%M:%S",  # 2025-01-20 14:30:00
            "%m-%d-%Y",  # 1-20-2025 (single digit month)
            "%d-%m-%Y",  # 20-1-2025 (single digit month)
        ]

        # Try parsing the date with each format
        for date_format in date_formats:
            try:
                parsed_date = datetime.strptime(date_string, date_format)
                # Extract and return the specified component
                if component.lower() == 'day':
                    return parsed_date.day
                elif component.lower() == 'month':
                    # Return the month as a three-letter abbreviation
                    return parsed_date.strftime("%b").upper()  # e.g., 'JAN', 'FEB'
                elif component.lower() == 'year':
                    return parsed_date.year
                else:
                    return f"Invalid component: {component}. Use 'day', 'month', or 'year'."
            except ValueError:
                # If parsing fails with this format, continue checking the next format
                continue

        return "Invalid date string."

    @keyword
    def are_dates_equal(self, date_string1, date_string2, date_format):
        # Parse both date strings using the selected format
        print(date_string1, ' ', date_string2)
        print(date_format)
        date1 = parse_date(date_string1, date_format)
        date2 = parse_date(date_string2, date_format)
        print(date1, ' ', date2)
        if date1 and date2:
            # Compare the two parsed dates
            return date1 == date2
        else:
            return False  # If any of the dates is invalid, return False

    @keyword
    def data_is_sorted_in_ascending_order(self, data):
        """
        Verifies if a list of mixed data (numbers, alphanumeric strings, and dates) is sorted in ascending order.

        Args:
        - data (list): List of text values to be verified as sorted.

        Returns:
        - bool: True if the list is sorted in ascending order, otherwise False.
        """
        # Parse and compare each value in the list
        print('============== Verifying Ascending Order ==============')
        for i in range(len(data) - 1):
            # Parse current and next item
            current_value = parse_value(data[i])
            next_value = parse_value(data[i + 1])
            print(f"current_value: {current_value}, next_value: {next_value}")

            # Compare parsed values to check if the list is in ascending order

            # Check if both values are of the same type before comparing
            if isinstance(current_value, (float, int)) and isinstance(next_value, (float, int)):
                if current_value > next_value:
                    return False  # If a pair is out of order, return False
            elif isinstance(current_value, str) and isinstance(next_value, str):
                if current_value > next_value:
                    return False  # If a pair is out of order, return False
            elif isinstance(current_value, datetime) and isinstance(next_value, datetime):
                if current_value > next_value:
                    return False  # If a pair is out of order, return False
            else:
                # If types differ, compare as strings (forcing all comparisons to the same type)
                if str(current_value) > str(next_value):
                    return False  # If a pair is out of order, return False

        return True  # If no pair was out of order, return True

    @keyword
    def data_is_sorted_in_descending_order(self, data):
        """
        Verifies if a list of mixed data (numbers, alphanumeric strings, and dates) is sorted in descending order.

        Args:
        - data (list): List of text values to be verified as sorted.

        Returns:
        - bool: True if the list is sorted in descending order, otherwise False.
        """
        # Parse and compare each value in the list
        print('============== Verifying Descending Order ==============')
        for i in range(len(data) - 1):
            # Parse current and next item
            current_value = parse_value(data[i])
            next_value = parse_value(data[i + 1])
            print(f"current_value: {current_value}, next_value: {next_value}")

            # Check if both values are of the same type before comparing
            if isinstance(current_value, (float, int)) and isinstance(next_value, (float, int)):
                if current_value < next_value:
                    return False  # If a pair is out of order, return False
            elif isinstance(current_value, str) and isinstance(next_value, str):
                if current_value < next_value:
                    return False  # If a pair is out of order, return False
            elif isinstance(current_value, datetime) and isinstance(next_value, datetime):
                if current_value < next_value:
                    return False  # If a pair is out of order, return False
            else:
                # If types differ, compare as strings (forcing all comparisons to the same type)
                if str(current_value) < str(next_value):
                    return False  # If a pair is out of order, return False

        return True  # If no pair was out of order, return True

    @keyword
    def data_is_unsorted(self, data):
        """
        Verifies that the list of mixed data is neither sorted in ascending nor descending order.

        Args:
        - data (list): List of text values to be verified as unsorted.

        Returns:
        - bool: True if the list is unsorted, otherwise False.
        """
        if not self.data_is_sorted_in_ascending_order(data) and not self.data_is_sorted_in_descending_order(data):
            return True  # The data is neither in ascending nor descending order
        return False  # The data is either ascending or descending

    @keyword
    def generate_random_bin_name(self):
        r = RandomWords()
        generated_word = r.get_random_word()
        generated_word = generated_word.capitalize()
        word_to_append = "Bin"
        result = f"{generated_word}{word_to_append}"
        return result

    def generate_random_campaign_name(self):
        r = RandomWords()
        generated_word = r.get_random_word()
        generated_word = generated_word.capitalize()
        word_to_append = "Campaign"
        result = f"{generated_word}{word_to_append}"
        return result

    @keyword
    def is_boolean_string(self, value):
        # Check if value is one of the accepted boolean-like values
        if value in [True, False, 'True', 'False']:
            return True
        return False

    @keyword
    def click_checkbox(self, element: WebElement, is_mat_checkbox=False):
        """
        Clicks a checkbox element on the page.

        :param element: The WebDriver element representing the checkbox
        :param is_mat_checkbox: Boolean flag for mat-checkbox handling (default: False)
        """
        try:
            # If it's a mat-checkbox, find the inner span element
            if is_mat_checkbox:
                # For mat-checkboxes, the checkbox element is usually wrapped inside a span
                checkbox_element = element.find_element(By.TAG_NAME, './/span')
            else:
                # Otherwise, use the passed element directly
                checkbox_element = element

            # Click the checkbox element
            checkbox_element.click()
            print("Checkbox clicked successfully.")

        except Exception as e:
            # Raising an appropriate exception for Robot Framework to handle
            raise RuntimeError(f"Error clicking the checkbox: {str(e)}")

    @keyword
    def execute_cdp_command(self, driver: WebDriver, cmd, params=None):
        """Execute a CDP command."""
        response = driver.execute_cdp_cmd(cmd, params or {})
        return response

    @keyword
    def get_local_storage_keys_and_values(self, key_name=None):
        driver = BuiltIn().get_library_instance('SeleniumLibrary').driver
        local_storage_keys = driver.execute_cdp_cmd('Runtime.evaluate', {
            'expression': 'Object.keys(window.localStorage)',
            'returnByValue': True
        })
        keys_and_values = {}
        for key in local_storage_keys['result']['value']:
            local_storage_value = driver.execute_cdp_cmd('Runtime.evaluate', {
                'expression': f'window.localStorage.getItem("{key}")',
                'returnByValue': True
            })
            keys_and_values[key] = local_storage_value['result']['value']

        if key_name:
            filtered_keys_and_values = {key: value for key, value in keys_and_values.items() if key_name in key}
            return filtered_keys_and_values

        return keys_and_values
    @keyword
    def get_local_storage_details(self, driver: WebDriver):
        """Get all local storage key-value pairs."""
        script = """
           var storage = window.localStorage;
           var items = {};
           for (var i = 0; i < storage.length; i++) {
               var key = storage.key(i);
               items[key] = storage.getItem(key);
           }
           return items;
           """
        return driver.execute_script(script)

    @keyword
    def validate_integer(self, number):
        try:
            # Attempt to convert the parameter to an integer
            int_value = int(number)

            return "valid integer"
        except ValueError:
            return "integer not valid"


    @keyword
    def populate_json_file_with(self, json_file_path, **kwargs):

        payload = self.load_json_file(json_file_path)

        if payload is None:
            pass
        else:
            with open(json_file_path, 'r', encoding='utf-8') as json_file:
                payload = json.load(json_file)

                for key, value in kwargs.items():
                    value = str(value).strip()
                    print("Key is '" + str(key) + "' and Value is '" + value + "'")

                    # Check if the value provided is an image path
                    if is_variable_an_image(value):
                        # Generate the image signature
                        temp_value = image_data + convert_image_to_base_64(value)
                        signature = self.generate_signature(temp_value)

                        value = temp_value + ',' + signature

                    # Check if the field value's data type must be changed from string
                    if field_is_boolean_type(key):
                        value = set_data_type_to_boolean(value)
                    elif field_is_integer_type(key):
                        value = set_data_type_to_integer(value)
                    elif (not get_variable_data_type(value) == 'str') and field_is_float_type(key):
                        value = set_data_type_to_float(value)

                    # Modify Key data if required
                    if json_object_is_an_array(key):
                        key = key.replace(":", ".")

                    if key == 'campaignStartDate' or key == 'campaignEndDate' or key == 'approvalTime':
                        value = get_current_date_time(value)
                    elif key == 'updatedDate' or key == 'requestDate' or key == 'approvalTime':  # or key == "uploadDate":
                        value = get_current_date_time('0')

                    if key == "uploadDate":
                        upload_date_field_data = value

                    key = str('$.' + key)
                    jsonpath_expr = parse(key)
                    jsonpath_expr.find(payload)
                    jsonpath_expr.update(payload, value)

            with open(json_file_path, 'w', encoding='utf-8') as json_file:
                json.dump(payload, json_file, indent=4)
                print('JSON file updated successfully')

    @keyword
    def load_json_file(self, json_file_path):
        with open(json_file_path, 'r', encoding='utf-8') as json_file:
            try:
                json_payload = json.load(json_file)
                print("Json Loaded")
                return json_payload
            except ValueError:
                print("Json file not Loaded")
                return None

    @keyword
    def get_base_url(self, environment):
        match environment.upper():
            case 'DEV':
                return "https://atm-marketing.atm-marketing-dev.rbb-banking.sdc-nonprod.caas.absa.co.za"
            case 'UAT':
                return "https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za"
            case 'SIT':
                return "https://atm-marketing.atm-marketing-sit.rbb-banking.sdc-nonprod.caas.absa.co.za"

            # If an exact match is not confirmed, this last case will be used if provided
            case _:
                return "Environment value " + environment + " is not recognised. Please use the correct values."

    @keyword
    def get_service_path(self, service, path_id=None):
        match service.upper():
            case 'APPROVAL/APPROVE':
                if path_id is None:
                    return "/Approval/Approve"
                else:
                    return "/Approval/" + path_id
            case 'ATMDATA':
                return "/ATMData"
            case 'ATMMARKETING':
                return "/ATMMarketing"
            case 'ATMMARKETINGCAMPAIGN':
                if path_id is None:
                    return "/ATMMarketingCampaign"
                else:
                    return "/ATMMarketingCampaign/" + path_id
            case 'ATMMARKETINGRESULT':
                if path_id is None:
                    return "/ATMMarketingResult"
                else:
                    return "/ATMMarketingResult/" + path_id
            case 'CAMPAIGNLOOKUP':
                if path_id is None:
                    return "/CampaignLookup"
                else:
                    return "/CampaignLookup/" + path_id
            case 'DASHBOARD':
                return "/Dashboard"
            case 'GASPERDETAILS':
                if path_id is None:
                    return "/GasperDetails"
                else:
                    return "/GasperDetails/" + path_id
            case 'WEATHERFORECAST':
                return "/WeatherForecast"

            # If an exact match is not confirmed, this last case will be used.
            case _:
                return "The service path " + service + " is not recognised. Please use the correct value."


    # Function to handle the "Save As" dialog
    @keyword
    def handle_save_as_dialog(self, file_path=None):
        time.sleep(2)  # Wait for the dialog to appear
        if file_path is not None:
          pyautogui.write(file_path)  # Type the file path
        pyautogui.press('enter')  # Press Enter to save

    @keyword
    def get_rest_api_headers(self, bearer_token):
        headers = {"Accept": "application/json",
                   "Content-Type": "application/json",
                   "Connection": "keep-alive",
                   "Accept-Encoding": "gzip, deflate, br",
                   "Authorization": bearer_token,
                   "Accept": "*/*"}
        return headers

    @keyword
    def value_exists_on_json_response(self, json_key, json_value, str_response=None):

        if str_response is None:
            print("Please provide the response that must be read!")
            return False
        else:

            if json_key == "uploadDate":
                json_value = upload_date_field_data

            # convert string to  object
            data = json.loads(str_response)
            modified_json_key = ''

            # Modify Key data if required
            if json_object_is_an_array(json_key):
                modified_json_key = json_key.replace(":", ".")
            else:
                modified_json_key = json_key

            modified_json_key = str('$.' + modified_json_key)
            jsonpath_expr = parse(modified_json_key)

            for match in jsonpath_expr.find(data):
                if str(match.value).strip() == str(json_value).strip():
                    print(f'Field: {json_key}', f', having a value: {json_value} was found.')
                    return True

            print(f'Field: {json_key}', f', having a value: {json_value} was NOT found!', file=sys.stderr)
            return False

    @keyword
    def generate_signature(self, message):
        keyword_instance = CommonUtils()
        key = keyword_instance.read_config_property('BUSINESS_USER_KEY')
        # print('Key value is ', key)
        hash_message = hashlib.sha256(message.encode()).digest()
        signature = hmac.new(key.encode(), hash_message, hashlib.sha256).digest()
        # print('Generated Signature ',base64.b64encode(signature).decode())
        return base64.b64encode(signature).decode()

    @keyword
    def create_base64_image_string(self, image_name):

        if is_variable_an_image(image_name):
            temp_value = image_data + convert_image_to_base_64(image_name)
            signature = self.generate_signature(temp_value)
            return temp_value + ',' + signature
        return ""

    @keyword
    def convert_to_kwargs(self, **kwargs):
        return kwargs

    @keyword
    def get_timestamp_with_milliseconds(self):
        now = datetime.now()
        return str(now.strftime('%Y-%m-%dT%H:%M:%S.%f'))  # [:-3]

    @keyword
    def is_last_char_zero(self, string):
        if string and string[-1] == '0':
            return True
        return False