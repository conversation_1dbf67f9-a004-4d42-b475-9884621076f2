*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Supervisor Approval

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py

#***********************************PROJECT RESOURCES***************************************
*** Variables ***
${REJECT_BTN}                                       xpath=//button[@class='close pull-right btn approvalButton' and contains(text(),'Reject')]
${APPROVE_BTN}                                      xpath=//button[@class='close pull-right btn approvalButton' and contains(text(),'Approve')]
${STATUS_TXT}                                       xpath=//div[@id='second' and contains(text(),'Awaiting Approval')]
*** Keywords ***
User attends to complaint in Awaitng Approval
    [Arguments]  ${NEW_STATUS}  ${ACTION}

    Wait until Page Contains                        Update - ATM Complaints and Compliments

	Sleep  3s
    
    Capture page screenshot  Approve_Reject_ATM_C_and_C_before.png    

    #PERFORM REQUIRED ACTION (APPROVE/REJECT)
    Run Keyword If	'${ACTION}' == "REJECT"  Supervisor rejects the complaint cancellation
    ...  ELSE IF  '${ACTION}' =="APPROVE"  	Supervisor approves the complaint cancellation

Supervisor rejects the complaint cancellation
	[Arguments]
    Log to console  --------------------------Reject

    Sleep  2s

    Click Element                                   ${REJECT_BTN}

    Sleep  2s

    Capture page screenshot  Approve_Reject_ATM_C_and_C_after.png

Supervisor approves the complaint cancellation
	[Arguments]
    Log to console  --------------------------Reject

    Sleep  2s

    Click Element                                   ${APPROVE_BTN}

    Sleep  2s

    Capture page screenshot  Approve_Reject_ATM_C_and_C_after.png     

User views status after rejecting or rejecting
	[Arguments]  ${NEW_STATUS}
    Log to console  --------------------------View status

    Page Should Contain Element                     xpath=//div[@id='second' and contains(text(),'${NEW_STATUS}')]
