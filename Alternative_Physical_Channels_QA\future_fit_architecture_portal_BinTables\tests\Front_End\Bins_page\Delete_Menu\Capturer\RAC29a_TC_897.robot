*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Setup                                          The User gets a draft bin number for deletion process    
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-897




*** Keywords ***
Search for BIN Number to Delete
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_NAME}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The user searches for a Bin Number to be deleted         ${BIN_NAME}    
    Then The user verifies that the Bin search returned results    ${BIN_NAME}
    Then The delete bin search results should exist in the database    ${GLOBAL_SEARCH_RESULTS_FOR_DELETE_BINS}

| *** Test Cases ***                                                                          |        *DOCUMENTATION*                  |         *BASE_URL*             |         *BIN_NAME*                      |        
| Capturer_Search for BIN Number to Delete            | Search for BIN Number to Delete       |      Search a Bin Number to delete.     |           ${EMPTY}             |         ${global_draft_bin_number}      |         