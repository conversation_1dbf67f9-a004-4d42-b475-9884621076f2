*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Sort for Bins linked to a Bin Type
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TYPE_TO_VERIFY}    ${SORT_DATA}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal         ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user selects a Bin Type on the View Bin Types Page                        ${BIN_TYPE_TO_VERIFY}
    And The selected Bin Type Page which contains linked Bins is be displayed         ${BIN_TYPE_TO_VERIFY}
    And The user sorts the Bins results                                               ${SORT_DATA}
    Then The returned BINS must be sorted in ascending order                          ${SORT_DATA}
    When The user sorts the Bins results                                              ${SORT_DATA}
    Then The returned BINS must be sorted in desceding order                          ${SORT_DATA}
    When The user sorts the Bins results                                              ${SORT_DATA}
    Then The returned BINS must neither be sorted in ascending nor desceding order    ${SORT_DATA}

| *** Test Cases ***                                                                                        |        *DOCUMENTATION*    		|         *BASE_URL*                  |         *BIN_TYPE_TO_VERIFY*          |         *SORT_DATA*          |
| Sort Invalid Bins using the Bin Number.                        | Sort for Bins linked to a Bin Type       | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |              Invalid                  |           Bin Number         |
| Sort Invalid Bins using the Action Date.                       | Sort for Bins linked to a Bin Type       | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |              Invalid                  |           Action Date        |
