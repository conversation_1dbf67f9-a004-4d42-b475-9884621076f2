*** Settings ***
# Author Name               : Thabo
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/GetBinTypesById_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID
${TEST_CASE_ID}             RAC29a-TC-1345


*** Keywords ***
Get all Bin Types by Id
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}  ${BIN_TYPE_ID}    ${EXPECTED_ERROR_MESSAGE}      ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Set Global Variable    ${GLOBAL_BIN_TYPE_ID}    ${BIN_TYPE_ID}

    Given The User sends a request to get a Bin Type by Id   ${BASE_URL}      ${BIN_TYPE_ID}
    When The service returns an expected status code         ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed        ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                                                                           |    *DOCUMENTATION*    	   |    *BASE_URL*       | *BIN_TYPE_ID*                                  |   *EXPECTED_ERROR_MESSAGE*                                                 |   *EXPECTED_STATUS_CODE*    |
| Verify the GetBINtypeById API response when a non-existent BIN type ID is used to retrieve details (Bin Type View)            | Get all Bin Types by Id         | Get all Bin Types by Id   |                  | 5c90d3d6-2bec-418f-aace-905caabd312e           |       The binType with the specified identifier was not found.              |   404                       |
