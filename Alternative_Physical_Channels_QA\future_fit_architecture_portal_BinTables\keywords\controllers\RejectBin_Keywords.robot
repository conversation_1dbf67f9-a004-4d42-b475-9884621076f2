*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/bins/Reject.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          GetBinsToReview_Keywords.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************

*** Variables ***



*** Keywords ***
The user gets all pending Bins from the database that can be rejected

    #Get all the Bins that are under review
    The User gets all the Bin numbers that must be reviewed from the Database

    Log Many    BINS From the database:   '${DATABASE_RESULTS}'
    ${bin_counter}=    Set Variable    1
    &{BINS_TO_REJECT}=  Create Dictionary
    ${required_bin_status}=     Set Variable    Pending
    #Loop through all the Bins under review and get the ones that are not deleted and are in a Pending status
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       BinNumber

        #Verify that the Bin is not deleted
        ${bin_is_active}=       Verify that the Bin Deletion Status is as expected      ${binNumber_data}        ${False}

        IF  ${bin_is_active}

            #Get the Bin ID for the current Bin from the database
            ${db_results}=     Get the Bin ID using the Bin Number from the Database    ${binNumber_data}
            # Ensure the results are not empty
            ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
            Run Keyword If    not ${dr_results_contain_data}
            ...     Fail    Database query for bin number: '${binNumber_data}' returned no results
            ${first_row_results}=             Get From List    ${db_results}    0    # Get the first row
            ${binId_value}=    Get Column Data By Name       ${first_row_results}       binId
            Log Many    The value of the binId column is: ${binId_value}

            #Get the status of the current bin to verify if it is Pending (Only pending Bins can be rejected)

             #Get the Database details for the current Bin
            ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${binId_value}
            # Ensure the results are not empty
            ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
            Run Keyword If    not ${dr_results_contain_data}
            ...     Fail    Database query for bin id: '${binId_value}' returned no results!

             ${first_row_results}=             Get From List    ${action_tracker_db_results}    0    # Get the first row


            #Verify the bin details
            ${bin_outcome_data}=            Get Column Data By Name       ${first_row_results}       ActionType
            ${bin_outcome_text}=            Get Bin Outcome Text       ${bin_outcome_data}
            ${bin_reject_comment_data}=     Get Column Data By Name       ${first_row_results}       RejectionComment
            ${bin_status_data}=             Get Column Data By Name       ${first_row_results}       Status
            ${bin_status_text}=         Get Bin Status Text     ${bin_status_data}

            IF   '${bin_status_text}' == '${required_bin_status}'
                ${binId_key}=    Set Variable    binId${bin_counter}
                ${outcome_key}=    Set Variable    outcome${bin_counter}
                ${rejectionComment_key}=    Set Variable    rejectionComment${bin_counter}

                #Save the pending Bin details to a dictionary
                Set To Dictionary    ${BINS_TO_REJECT}    ${binId_key}=${binId_value}       ${outcome_key}=${bin_outcome_text}     ${rejectionComment_key}=Rejected using the automation script. the outcome of the bin must not be ${bin_outcome_text}
                ${bin_counter}=    Evaluate    ${bin_counter} + 1
                Log  Bin Number: '${binNumber_data}' can be rejected.
            ELSE
                Log Many  Bin Number: '${binNumber_data}' cannot be rejected because its current status is '${bin_outcome_text}'.
            END

        END
    END

    #Fail the test if there are no bins to reject
    Run Keyword If    "${BINS_TO_REJECT}" == "{}"
    ...    Fail     There are no bins to reject on the DB!

    Log Many    BINS that will be rejected using the Reject Controller:   '${BINS_TO_REJECT}'

    Set Global Variable    ${BINS_TO_REJECT}     ${BINS_TO_REJECT}


The user gets a pending Bin from the database that can be rejected

    #Get all the Bins that are under review
    The User gets a Bin number that must be reviewed from the Database

    Log Many    BINS From the database:   '${DATABASE_RESULTS}'
    ${bin_counter}=    Set Variable    1
    &{BINS_TO_REJECT}=  Create Dictionary
    ${required_bin_status}=     Set Variable    Pending
    #Loop through all the Bins under review and get the ones that are not deleted and are in a Pending status
    FOR    ${row}    IN    @{DATABASE_RESULTS}

        ${binNumber_data}=                   Get Column Data By Name       ${row}       BinNumber

        #Verify that the Bin is not deleted
        ${bin_is_active}=       Verify that the Bin Deletion Status is as expected      ${binNumber_data}        ${False}

        IF  ${bin_is_active}

            #Get the Bin ID for the current Bin from the database
            ${db_results}=     Get the Bin ID using the Bin Number from the Database    ${binNumber_data}
            # Ensure the results are not empty
            ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
            Run Keyword If    not ${dr_results_contain_data}
            ...     Fail    Database query for bin number: '${binNumber_data}' returned no results
            ${first_row_results}=             Get From List    ${db_results}    0    # Get the first row
            ${binId_value}=    Get Column Data By Name       ${first_row_results}       binId
            Log Many    The value of the binId column is: ${binId_value}

            #Get the status of the current bin to verify if it is Pending (Only pending Bins can be rejected)

             #Get the Database details for the current Bin
            ${action_tracker_db_results}=     Get the last action tracker details for the Bin    ${binId_value}
            # Ensure the results are not empty
            ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
            Run Keyword If    not ${dr_results_contain_data}
            ...     Fail    Database query for bin id: '${binId_value}' returned no results!

             ${first_row_results}=             Get From List    ${action_tracker_db_results}    0    # Get the first row


            #Verify the bin details
            ${bin_outcome_data}=            Get Column Data By Name       ${first_row_results}       ActionType
            ${bin_outcome_text}=            Get Bin Outcome Text       ${bin_outcome_data}
            ${bin_reject_comment_data}=     Get Column Data By Name       ${first_row_results}       RejectionComment
            ${bin_status_data}=             Get Column Data By Name       ${first_row_results}       Status
            ${bin_status_text}=         Get Bin Status Text     ${bin_status_data}

            IF   '${bin_status_text}' == '${required_bin_status}'
                ${binId_key}=    Set Variable    binId${bin_counter}
                ${outcome_key}=    Set Variable    outcome${bin_counter}
                ${rejectionComment_key}=    Set Variable    rejectionComment${bin_counter}

                #Save the pending Bin details to a dictionary
                Set To Dictionary    ${BINS_TO_REJECT}    ${binId_key}=${binId_value}       ${outcome_key}=${bin_outcome_text}     ${rejectionComment_key}=Rejected using the automation script. the outcome of the bin must not be ${bin_outcome_text}
                ${bin_counter}=    Evaluate    ${bin_counter} + 1
                Log  Bin Number: '${binNumber_data}' can be rejected.
            ELSE
                Log Many  Bin Number: '${binNumber_data}' cannot be rejected because its current status is '${bin_outcome_text}'.
            END

        END
    END

    #Fail the test if there are no bins to reject
    Run Keyword If    "${BINS_TO_REJECT}" == "{}"
    ...    Fail     There are no bins to reject on the DB!

    Log Many    BINS that will be rejected using the Reject Controller:   '${BINS_TO_REJECT}'

    Set Global Variable    ${BINS_TO_REJECT}     ${BINS_TO_REJECT}
    Set Global Variable    ${BIN_ID}     ${binId_value}
    Set Global Variable    ${BIN_OUTCOME}     ${bin_outcome_text}

The user rejects all pending Bins retrieved from the database

    ${dict_length}=    Get Length    ${BINS_TO_REJECT}    # Get the length of the dictionary
    ${bin_counter}=    Set Variable    1
    FOR    ${i}    IN RANGE    0    ${dict_length}    3    # Loop with a step of 3
        ${bin_id_data}=    Get From Dictionary    ${BINS_TO_REJECT}    binId${bin_counter}  # Access dictionary item by  bin_counter index
        Log    binId${bin_counter}=${bin_id_data}
        ${bin_outcome_data}=    Get From Dictionary    ${BINS_TO_REJECT}    outcome${bin_counter}  # Access dictionary item by bin_counter index
        Log    outcome${bin_counter}=${bin_outcome_data}
        ${bin_reject_comment_data}=    Get From Dictionary    ${BINS_TO_REJECT}    rejectionComment${bin_counter}  # Access dictionary item by bin_counter index
        Log    rejectionComment${bin_counter}=${bin_reject_comment_data}

        #Reject each bin using the Reject Bin controller
        The user verifies that the current BIN is not Deleted   ${bin_id_data}
        The User Populates the Reject Bin JSON payload with    ${bin_id_data}   ${bin_outcome_data}    ${bin_reject_comment_data}
        The User executes the Reject Bin API Request    ${EMPTY}
        The Reject Bin controller returns an expected status code     200

        ${bin_counter}=    Evaluate    ${bin_counter} + 1
    END

All rejected bins must be showing as expcted on the database
    [Arguments]  ${REJECTED_BINS_EXPECTED_STATUS}
    ${dict_length}=    Get Length    ${BINS_TO_REJECT}    # Get the length of the dictionary
    ${bin_counter}=    Set Variable    1
    FOR    ${i}    IN RANGE    0    ${dict_length}    3    # Loop with a step of 3
        ${bin_id_data}=    Get From Dictionary    ${BINS_TO_REJECT}    binId${bin_counter}  # Access dictionary item by  bin_counter index
        Log    binId${bin_counter}=${bin_id_data}
        ${bin_outcome_data}=    Get From Dictionary    ${BINS_TO_REJECT}    outcome${bin_counter}  # Access dictionary item by bin_counter index
        Log    outcome${bin_counter}=${bin_outcome_data}
        ${bin_reject_comment_data}=    Get From Dictionary    ${BINS_TO_REJECT}    rejectionComment${bin_counter}  # Access dictionary item by bin_counter index
        Log    rejectionComment${bin_counter}=${bin_reject_comment_data}

        #Verify that the Bin has been rejected on the Database
        The Bin details are reflecting as expected on the database    ${bin_id_data}   ${bin_outcome_data}    ${bin_reject_comment_data}    ${REJECTED_BINS_EXPECTED_STATUS}
        ${bin_counter}=    Evaluate    ${bin_counter} + 1
    END

The user verifies that the current BIN is not Deleted
    [Arguments]     ${BIN_ID}

    # Ensure the bins are not empty
    ${dict_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${BIN_ID}
    Run Keyword If    not ${dict_contain_data}
    ...     Fail    Please provide the Bin ID that must be queried from the DB.

    #Get the bin number for the provided bin id
    ${db_results}=     Get the Bin Number using the Bin Id     ${BIN_ID}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin id: '${BIN_ID}' returned no results
     #Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}
    ${num_rows}=    Get Length    ${db_results}

    Run Keyword If    ${num_rows} > 1
    ...    Fail     Database query for bin id: '${BIN_ID}' returned ${num_rows} bins.

    FOR    ${row}    IN    @{db_results}
       ${bin_number}=             Get Column Data By Name       ${row}       binNumber
    END
    ${bin_is_active}=       Verify that the Bin Deletion Status is as expected      ${bin_number}        ${False}
    Run Keyword If    ${bin_is_active}
    ...    Log Many    The BIN Number: '${bin_number}' is not deleted on the database.
    ...  ELSE
    ...    Fail    The BIN Number: '${bin_number}' is deleted on the database hence it cannot be edited.

The user verifies that the current BIN's isDeleted status is as expected
    [Arguments]     ${BIN_ID}    ${EXPECTED_IS_DELETED_STATUS}

    # Ensure the bins are not empty
    ${dict_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${BIN_ID}
    Run Keyword If    not ${dict_contain_data}
    ...     Fail    Please provide the Bin ID that must be queried from the DB.

    #Get the bin number for the provided bin id
    ${db_results}=     Get the Bin Number using the Bin Id     ${BIN_ID}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin id: '${BIN_ID}' returned no results
     #Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}
    ${num_rows}=    Get Length    ${db_results}

    Run Keyword If    ${num_rows} > 1
    ...    Fail     Database query for bin id: '${BIN_ID}' returned ${num_rows} bins.

    FOR    ${row}    IN    @{db_results}
       ${bin_number}=             Get Column Data By Name       ${row}       binNumber
    END
    ${actual_bin_is_deleted_status}=       Get the Bin 'ISDeleted' status      ${bin_number}

    Run Keyword If    ${actual_bin_is_deleted_status} == ${EXPECTED_IS_DELETED_STATUS}
    ...    Log Many    The database 'isDeleted' status for BIN Number: '${bin_number}' is as per user expected status.
    ...  ELSE
    ...    Fail    The database 'isDeleted' status for BIN Number: '${bin_number}' is not the same as the expected status. The database status is  '${actual_bin_is_deleted_status}', while the expected status is '${EXPECTED_IS_DELETED_STATUS}'.


The User Populates the Reject Bin JSON payload with
    [Arguments]     ${BIN_ID}   ${BIN_OUTCOME}   ${BIN_REJECTION_COMMENTS}

    &{BINS_DETAILS}=  Create Dictionary  binId1=${BIN_ID}  outcome1=${BIN_OUTCOME}  rejectionComment1=${BIN_REJECTION_COMMENTS}

    ${json_payload}=    Create Bin Rejection Request    &{BINS_DETAILS}

    ${json_payload_is_created}=    Run Keyword And Return Status     Should Not Be Empty    ${json_payload}

    Run Keyword If    ${json_payload_is_created} == ${False}
    ...    Fail     The JSON payload for upload bin was not created!

    #Save the payload in a Global Variable
    Set Global Variable    ${REST_PAYLOAD}        ${json_payload}

    Log Many    ${json_payload}



Create Bin Rejection Request
    [Arguments]    &{bins}

    #This keyword generates a JSON payload for bin requests by passing a dictionary of bins.
    #Each bin is represented by a set of key-value pairs, with keys like bin1, date1, and binIds1.

    #Arguments:
     #&{BINS_DETAILS}: A dictionary containing bin data. The dictionary should contain the following format for each bin:

        #- binIdX: The bin id (e.g., binId1='uuid').
        #- outcomeX: The outcome associated with the bin (e.g., outcome1=Deleted).
        #- rejectionCommentX: The rejection comment for the bin (e.g., rejectionComment1='Bin must be added.'.

     #Here X represents an index (e.g., 1, 2, 3, etc.), and the method is flexible enough to accept any number of bins, even those with non-sequential or multi-digit indices (e.g., bin123, bin10).

     #Return Value:
        #Returns the generated JSON payload as a string, formatted with indentation for readability.

     #Usage Example
        #${bins} =    Create Dictionary    binid1=0193877e-54fe-791b-9ecf-e8600f2f2431    outcome1=Deleted    rejectionComment1=This bin must be added.
        #${json_result}=    Create Bin Reject Request    &{bins}

    # Log the received bins to ensure it's a valid dictionary
    Log    Received bins: &{bins}

    #${bin_request_generator} =    Upload    @{bins}
    ${json_result} =    Create Bin Reject Request    &{bins}

    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal_BinTables/data/RejectBin.json
    Write JSON data To File    ${JSON_FILE}    ${json_result}

    RETURN    ${json_result}


The User executes the Reject Bin API Request
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}
     Log Many   ${REST_PAYLOAD}

    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/RejectBin.json

    Log Many   ${payload}
    ${endpoint}=        Get Reject Bin Endpoint    ${base_url}
    ${method}=          Set Variable   PUT
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}
    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     payload=${payload}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance


The Reject Bin controller returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'Upload' REST API call failed, the returned status is '${status_code}'


The expected Error must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     "${api_error_message_detail}"     "${EXPECTED_ERROR_MESSAGE}"
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       binId

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             ${error_msg_two_verification}=    Set Variable If  "${EXPECTED_ERROR_MESSAGE}" in "${api_error_message_fields}"     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Reject' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.


Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}



#Keywords to read error response fields
Get Error details data
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}


Get Response Status Code
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}


Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}
    


#The below keywords are for Database Verifications

The Bin details are reflecting as expected on the database
    [Arguments]     ${BIN_ID}   ${BIN_OUTCOME}   ${BIN_REJECTION_COMMENTS}   ${BIN_STATUS}

    # Ensure the bins are not empty

    ${parameter_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${BIN_ID}
    Run Keyword If    not ${parameter_contain_data}
    ...     Fail    Please provide the Bin Id for the Bin that must be queried from the DB.

    ${bin_outcome_must_be_verified}=    Set Variable If  '${BIN_OUTCOME}' != '${EMPTY}' and '${BIN_OUTCOME}' != 'None'     ${True}     ${False}
    ${bin_outcome_verification_passed}=    Set Variable If  ${bin_outcome_must_be_verified}     ${False}     ${True}

    ${bin_reject_comment_must_be_verified}=    Set Variable If  "${BIN_REJECTION_COMMENTS}" != '${EMPTY}' and "${BIN_REJECTION_COMMENTS}" != 'None'     ${True}     ${False}
    ${bin_reject_comment_verification_passed}=    Set Variable If  ${bin_reject_comment_must_be_verified}     ${False}     ${True}

    ${bin_status_must_be_verified}=    Set Variable If  '${BIN_STATUS}' != '${EMPTY}' and '${BIN_STATUS}' != 'None'     ${True}     ${False}
    ${bin_status_verification_passed}=    Set Variable If  ${bin_status_must_be_verified}     ${False}     ${True}

    #Get the Database details for the current Bin
    ${db_results}=     Get the last action tracker details for the Bin    ${BIN_ID}
    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}
    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database query for bin id: '${BIN_ID}' returned no results!

     #Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}
    #Loop through the returned DB results and verify them against the user's supplied parameters
    ${num_rows}=    Get Length    ${db_results}
    FOR    ${row}    IN    @{db_results}
        #Verify the bin details
        ${bin_outcome_data}=            Get Column Data By Name       ${row}       ActionType
        ${bin_reject_comment_data}=     Get Column Data By Name       ${row}       RejectionComment
        ${bin_status_data}=             Get Column Data By Name       ${row}       Status

        IF   ${bin_outcome_must_be_verified}
           ${bin_outcome_text}=         Get Bin Outcome Text     ${bin_outcome_data}
           ${bin_outcome_text}=  Convert To Upper Case    ${bin_outcome_text}
           ${BIN_OUTCOME}=  Convert To Upper Case    ${BIN_OUTCOME}

           ${bin_outcome_verification_passed}=       Run Keyword And Return Status
           ...  Verify that values are equal     '${BIN_OUTCOME}'     '${bin_outcome_text}'
        END

        IF   ${bin_reject_comment_must_be_verified}
            IF  '${bin_reject_comment_data}' != 'None'
                 ${bin_reject_comment_data}=  Convert To Upper Case    ${bin_reject_comment_data}
            END

           ${BIN_REJECTION_COMMENTS}=  Convert To Upper Case    ${BIN_REJECTION_COMMENTS}
           ${bin_reject_comment_verification_passed}=       Run Keyword And Return Status
           ...  Verify that values are equal     "${BIN_REJECTION_COMMENTS}"     "${bin_reject_comment_data}"
        END

        IF   ${bin_status_must_be_verified}
           ${bin_status_text}=         Get Bin Status Text     ${bin_status_data}
           ${bin_status_text}=  Convert To Upper Case    ${bin_status_text}
           ${BIN_STATUS}=  Convert To Upper Case    ${BIN_STATUS}
           ${bin_status_verification_passed}=       Run Keyword And Return Status
           ...  Verify that values are equal     '${BIN_STATUS}'     '${bin_status_text}'
        END


    END

    #Verify that all the data that needed verification was verified successfully
    IF  ${bin_outcome_must_be_verified}
        Run Keyword If    ${bin_outcome_verification_passed} == ${False}
        ...    Fail  The Outcome value: '${BIN_OUTCOME}', for BIN ID: '${BIN_ID}' was not found on the Database. The actual outcome on the Database is '${bin_outcome_text}'.
        ...  ELSE
        ...    Log    Database verification for the Outcome value: '${BIN_OUTCOME}', for BIN ID: '${BIN_ID}' was successful.
    END

    IF  ${bin_reject_comment_must_be_verified}
        Run Keyword If    ${bin_reject_comment_verification_passed} == ${False}
        ...    Fail  The Rejection Comment value: '${BIN_REJECTION_COMMENTS}', for BIN ID: '${BIN_ID}' was not found on the Database. The rejection comment found on teh Database is '${bin_reject_comment_data}'.
        ...  ELSE
        ...    Log    Database verification for the Rejection Comment value: '${BIN_REJECTION_COMMENTS}', for BIN ID: '${BIN_ID}' was successful.
    END

    IF  ${bin_status_must_be_verified}
        Run Keyword If    ${bin_status_verification_passed} == ${False}
        ...    Fail  The Bin Status value: '${BIN_STATUS}', for BIN ID: '${BIN_ID}' was not found on the Database. The Bin Status found on the Database is '${bin_status_text}'.
        ...  ELSE
        ...    Log    Database verification for the Bin Status value: '${BIN_STATUS}', for BIN ID: '${BIN_ID}' was successful.
    END



The rejected bin details must be showing as expcted on the database
    [Arguments]     ${BIN_ID}   ${BIN_OUTCOME}   ${BIN_REJECTION_COMMENTS}   ${BIN_STATUS}
    The Bin details are reflecting as expected on the database      ${BIN_ID}   ${BIN_OUTCOME}   ${BIN_REJECTION_COMMENTS}   ${BIN_STATUS}

Get Bin Count
    [Arguments]    &{kwargs}
    ${count}=    Set Variable     0

    FOR    ${key}    ${value}    IN    &{kwargs}
        ${temp_key}=  Convert To String  ${key}
        ${Key_is_bin_number}=      Run Keyword And Return Status   Should Match Regexp    ${temp_key}    ^bin[0-9]+$
        IF    ${Key_is_bin_number}
             ${count} =    Evaluate    ${count} + 1
        END
    END

    RETURN    ${count}

#*** Test Cases ***
#Unit Test

  #  ${bins}=    Create Dictionary       binId1=df62ccca-c5fa-4e6c-96fe-03b180929080  bin1=7004534    date1=2026-11-11   binTypeIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e  binOutcome1=Added    binId2=59ef7be3-c73b-4648-91a1-568064a700ae  bin2=7004535    date2=2026-11-12  binTypeIds2=a7ff7c25-057b-461b-9fa1-50d471202b52,bb679411-b69d-42b1-a6c6-8e7cdc63d6c4  binOutcome2=Added
  #  The User Populates the Update Bin JSON payload with     &{bins}

