*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../keywords/Common/Login.robot
Resource                                            ../../../../keywords/Common/HomePage.robot
Resource                                            ../../../../keywords/Common/Navigation.robot
Resource                                            ../../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../../keywords/Common/SetEnvironmentVariales.robot

*** Variables ***

*** Keywords ***
 Validates the Cost Centre Filter 
    [Arguments]  ${DOCUMENTATION}    ${EXPECTED_COST_CENTRE_NUMBER}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page 

    And the user navigates to the ATM Control Dashboard

    Then the user validates the Cost Centre filter on the ATM Control Dashboard    ${EXPECTED_COST_CENTRE_NUMBER}


| *Test Cases*                                                                              |      *DOCUMENTATION*                   | *EXPECTED_COST_CENTRE_NUMBER* | *TEST_ENVIRONMENT*   |
| MQ_TC_82_Filter_by_Cost_Center_on_ATM_C_Dashboard    | Validates the Cost Centre Filter   |    User verfies the Cost Centre filter |         87738635              |    MTGLA_UAT         | 
