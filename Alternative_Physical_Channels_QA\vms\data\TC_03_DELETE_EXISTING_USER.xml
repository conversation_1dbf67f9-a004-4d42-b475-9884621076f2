<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240905 11:09:37.328">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_03_DELETE_EXISTING_USER.robot">
      <test name="Delete the VMS user that has a role of 'Supervisor'." id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Deletes a VMS user that has a 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240905 11:09:38.280" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240905 11:10:19.644" status="PASS" starttime="20240905 11:09:38.280"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Deletes a VMS user that has a 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240905 11:10:19.647" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20240905 11:10:20.515" status="PASS" starttime="20240905 11:10:19.647"/>
         </kw>
         <kw library="Selenium" name="And Deletes the user from the VMS">
            <doc>Deletes a VMS user that has a 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240905 11:10:20.516" level="INFO">And Deletes the user from the VMS</msg>
            <status endtime="20240905 11:10:40.733" status="PASS" starttime="20240905 11:10:20.516"/>
         </kw>
         <kw library="Selenium" name="Then The user should not be found when you are searching for it on VMS">
            <doc>Deletes a VMS user that has a 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240905 11:10:40.734" level="INFO">Then The user should not be found when you are searching for it on VMS</msg>
            <status endtime="20240905 11:10:48.139" status="PASS" starttime="20240905 11:10:40.734"/>
         </kw>
         <tags>
            <tag>Delete the VMS user that has a role of 'Supervisor'.</tag>
         </tags>
         <status endtime="20240905 11:11:01.441" critical="yes" status="PASS" starttime="20240905 11:09:38.280"/>
      </test>
      <status endtime="20240905 11:11:01.442" status="PASS" starttime="20240905 11:09:37.328"/>
   </suite>
   <statistics>
      <total>
         <stat pass="1" fail="0">Critical Tests</stat>
         <stat pass="1" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">Delete the VMS user that has a role of 'Supervisor'.</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="1" fail="0" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
