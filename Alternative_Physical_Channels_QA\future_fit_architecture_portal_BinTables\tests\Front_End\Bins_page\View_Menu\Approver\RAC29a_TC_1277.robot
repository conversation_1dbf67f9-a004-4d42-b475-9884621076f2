*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1277




*** Keywords ***
Verify Bins displayed on View Entries Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TO_VERIFY}
    Set Test Documentation  ${DOCUMENTATION}
    Given The Bin Number does not exist in the database                                ${BIN_TO_VERIFY}
    When The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    And The User clicks Bins Menu
    And The user populates the search text                                             ${BIN_TO_VERIFY}
    Then The search results must be empty if the bin does not exist in the database    ${BIN_TO_VERIFY}


| *** Test Cases ***                                                                                                                          |        *DOCUMENTATION*    		        |         *BASE_URL*                  |         *BIN_TO_VERIFY*           |
| Approver_Verify No Results are Displayed if the BIN Does Not Exist in the System             | Verify Bins displayed on View Entries Page   | Verify bins against the database data.    |           ${EMPTY}                  |         zxwe2435dz                  |
