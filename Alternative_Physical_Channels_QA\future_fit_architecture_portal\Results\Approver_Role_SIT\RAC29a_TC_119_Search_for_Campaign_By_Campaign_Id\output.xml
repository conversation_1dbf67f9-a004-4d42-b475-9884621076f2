<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:08:40.460237" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_119_Search_for_Campaign_By_Campaign_Id.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:41.686159" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:08:41.685159" elapsed="0.001000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:41.686159" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:08:41.686159" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:41.686159" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:08:41.686159" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:41.687159" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:08:41.687159" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:41.687159" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:08:41.687159" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:08:41.685159" elapsed="0.002000"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_119_FFT_Approval_Search_for_Campaign_By_Campaign_Id" line="38">
<kw name="Validating the Search function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:08:41.688159" level="INFO">Set test documentation to:
Search by campaign ID</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:08:41.688159" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:08:41.787577" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:08:41.688159" elapsed="0.099418"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:08:41.788592" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:08:41.788592" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:08:41.790576" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:08:41.790576" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:08:41.790576" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:08:41.790576" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:08:41.790576" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:08:41.790576" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:08:41.790576" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:08:41.790576" elapsed="0.000997"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:08:41.822157" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:08:42.143120" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T12:08:42.143120" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:08:41.791573" elapsed="0.351547"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:08:42.144112" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:08:42.143120" elapsed="0.000992"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:08:42.143120" elapsed="0.000992"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:08:41.789590" elapsed="0.354522"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:42.145112" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:08:42.144112" elapsed="0.001000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:08:42.145112" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:08:42.145112" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:08:42.145112" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:08:42.145112" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:08:42.145112" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:08:42.145112" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:08:42.145112" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:08:42.146110" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:08:42.145112" elapsed="0.000998"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:08:42.146110" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:08:42.146110" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:08:42.145112" elapsed="0.000998"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:08:42.145112" elapsed="0.000998"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:08:42.146110" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:08:42.146110" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:08:42.146110" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002C6579C4D70&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:08:42.146110" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:08:42.146110" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:08:42.147111" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:08:42.146110" elapsed="0.001001"/>
</branch>
<status status="PASS" start="2024-10-29T12:08:42.146110" elapsed="0.001001"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:08:42.147111" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:08:42.147111" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:08:42.147111" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:08:42.147111" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:08:42.147111" elapsed="0.001000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:08:42.148111" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:08:42.148111" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:08:42.148111" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:08:42.148111" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:08:42.149111" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:08:42.148111" elapsed="0.001000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:08:42.149111" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:08:41.688159" elapsed="33.862645"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:09:15.551880" elapsed="0.135575"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:15.692654" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.145")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:15.687455" elapsed="0.005199"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:15.692654" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.145")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:15.692654" elapsed="0.025719"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:09:15.719481" elapsed="0.007893"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:15.719481" elapsed="0.008891"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:15.735373" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.146")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:15.728372" elapsed="0.007001"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:09:20.735760" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:09:15.735373" elapsed="5.000387"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:09:20.735760" elapsed="0.017641"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:20.753401" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.146")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:20.753401" elapsed="0.071287"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:09:20.824688" elapsed="0.437704"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:20.824688" elapsed="0.437704"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:09:21.262392" elapsed="0.004551"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:21.262392" elapsed="0.004551"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:21.276045" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:09:21.266943" elapsed="0.009102"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:15.551880" elapsed="5.724165"/>
</kw>
<kw name="And The user inputs a campaign ID on the Search field" owner="Approvals">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:09:26.276529" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:09:21.276045" elapsed="5.000484"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:26.298976" level="INFO">${Camapaign_ID} = CNQ012v001Q42024</msg>
<var>${Camapaign_ID}</var>
<arg>xpath=//tr[@role='row']/td[2]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:26.277048" elapsed="0.021928"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:09:26.299526" level="INFO">Campaign Name retrieved: CNQ012v001Q42024</msg>
<arg>Campaign Name retrieved: ${Camapaign_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:09:26.299526" elapsed="0.000000"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:26.305636" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.154")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:26.299526" elapsed="0.006110"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:26.305636" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.154")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:26.305636" elapsed="0.022373"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:26.328009" level="INFO">Typing text 'CNQ012v001Q42024' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.154")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${Camapaign_ID}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:26.328009" elapsed="0.048182"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:09:28.376753" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:09:26.376191" elapsed="2.000562"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:09:28.377774" level="INFO">${Camapaign_ID} = CNQ012v001Q42024</msg>
<arg>${Camapaign_ID}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:09:28.376753" elapsed="0.001021"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:09:28.377774" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="7d445226c6edc63c049a8d72d4428240", element="f.189419960AAE2DA00743EDB3907422CC.d.80ECC001A9EADDF0B76ACAAB16031B0B.e.154")&gt;</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:09:28.377774" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:21.276045" elapsed="7.102723"/>
</kw>
<kw name="Then The user verifies the search results returned by Campaign ID" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_RESULT_XPATH}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:09:28.378768" elapsed="0.014218"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:28.403346" level="INFO">${search_results} = CNQ012v001Q42024</msg>
<var>${search_results}</var>
<arg>xpath=//td[contains(text(), '${Camapaign_ID}')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:28.393960" elapsed="0.009386"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${search_results}</arg>
<arg>${Camapaign_ID}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T12:09:28.403346" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:28.403346" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:09:28.408354" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${SEARCH_RESULT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:28.403346" elapsed="0.005008"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${result_count} &gt; 0</arg>
<arg>msg=No search results found!</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:09:28.408354" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:09:28.408354" level="INFO">----Search completed successfully using campaign ID: CNQ012v001Q42024.----</msg>
<arg>----Search completed successfully using campaign ID: ${Camapaign_ID}.----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:09:28.408354" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Search completed successfully using campaign ID: ${Camapaign_ID}.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:09:28.408354" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:28.378768" elapsed="0.029586"/>
</kw>
<arg>Search by campaign ID</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:08:41.688159" elapsed="46.720195"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:09:28.409394" elapsed="0.004958"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:28.409394" elapsed="0.004958"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:28.415353" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:09:28.415353" elapsed="0.033855"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:09:31.450947" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:09:28.449208" elapsed="3.001739"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:09:31.451959" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:09:31.511765" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:09:31.512766" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:09:31.450947" elapsed="0.098919">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:09:33.551341" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:09:31.549866" elapsed="2.001475"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:09:33.551341" elapsed="3.171533"/>
</kw>
<status status="FAIL" start="2024-10-29T12:09:28.415353" elapsed="8.307521">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:09:28.414352" elapsed="8.309526"/>
</kw>
<status status="PASS" start="2024-10-29T12:09:28.409394" elapsed="8.314484"/>
</kw>
<doc>Search by campaign ID</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:08:41.687159" elapsed="55.036719"/>
</test>
<doc>Testing the Search Funtion on Campaign Approvals: Search by Campaign ID</doc>
<status status="PASS" start="2024-10-29T12:08:40.878002" elapsed="55.848874"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:08:40.453166" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_119_Search_for_Campaign_By_Campaign_Id.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:08:41.643024" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:08:42.144112" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T12:09:00.431109" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:09:10.441005" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:09:15.457602" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:09:28.403346" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
