*** Settings ***
# Author Name               : <PERSON>hab<PERSON>
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/SearchBinTypebyName_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
Search Bin Types by Name
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}  ${BIN_TYPE_NAME}  ${ACTION}  ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}
    Set Global Variable    ${GLOBAL_BIN_TYPE_NAME}    ${BIN_TYPE_NAME}

    # Step 1: Send DELETE request to the API
    Given The User sends a GET Request to Search Bin Types by Name   ${BASE_URL}  ${BIN_TYPE_NAME}  ${ACTION}

    # Step 2: Check for expected status code
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}

    # Step 3: Verify the response matches a Bin Type existing in the database
    Then The User verifies that the searched Bin Type exists in the database   

| *** Test Cases ***                                               |             *DOCUMENTATION*    |    *BASE_URL*              | *BIN_TYPE_NAME* |  *ACTION* |  *EXPECTED_STATUS_CODE*
| Search Bin Types by Name            | Search Bin Types by Name   | Search Bin Types by Name       |                            | Token           | add       |      200
