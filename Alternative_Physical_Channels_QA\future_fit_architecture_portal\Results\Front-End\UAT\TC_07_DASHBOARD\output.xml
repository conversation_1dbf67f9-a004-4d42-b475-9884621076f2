<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 07:54:04.165" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_07_DASHBOARD.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:54:04.895" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:04.895"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:54:04.895" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:04.895"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:54:04.895" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:04.895"/>
</kw>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:04.895"/>
</kw>
<test id="s1-t1" name="FFT - Dashboard - validate dashboard screen" line="39">
<kw name="Validates Calendar View Page">
<arg>Validates Calendar View Page</arg>
<arg>APC_UAT</arg>
<arg>155057356</arg>
<arg>BUSINESS_USER</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 07:54:04.895" level="INFO">Set test documentation to:
Validates Calendar View Page</msg>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:04.895"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:54:04.895" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057356'.</msg>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:04.895"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:54:05.054" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:05.054"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:54:05.054" endtime="******** 07:54:05.054"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:54:05.054" endtime="******** 07:54:05.054"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:54:05.069" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 07:54:05.069" endtime="******** 07:54:05.069"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:54:05.069" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 07:54:05.069" endtime="******** 07:54:05.069"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:54:05.069" endtime="******** 07:54:05.069"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 07:54:05.224" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 07:54:05.901" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 07:54:05.901" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 19244 has been terminated.
SUCCESS: The process "chrome.exe" with PID 29360 has been terminated.
SUCCESS: The process "chrome.exe" with PID 9696 has been ter...</msg>
<status status="PASS" starttime="******** 07:54:05.069" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<status status="PASS" starttime="******** 07:54:05.069" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:54:05.901" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x00000191E976B0B0&gt;</msg>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 07:54:05.901" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:54:05.901" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:54:05.901" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 07:54:05.901" endtime="******** 07:54:05.901"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 07:54:05.904" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 07:54:05.904" endtime="******** 07:54:10.834"/>
</kw>
<status status="PASS" starttime="******** 07:54:05.054" endtime="******** 07:54:10.834"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 07:54:10.841" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 07:54:10.841" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 07:54:10.834" endtime="******** 07:54:10.841"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 07:54:10.841" endtime="******** 07:54:10.853"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 07:54:10.853" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 07:54:10.853" endtime="******** 07:54:11.138"/>
</kw>
<status status="PASS" starttime="******** 07:54:10.853" endtime="******** 07:54:11.138"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:54:21.138" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:54:11.138" endtime="******** 07:54:21.138"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:54:21.138" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:54:21.196" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 07:54:21.138" endtime="******** 07:54:21.196"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:54:21.196" endtime="******** 07:54:21.196"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:54:41.358" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 07:54:21.196" endtime="******** 07:54:41.360"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:54:41.360" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:54:41.621" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 07:54:41.360" endtime="******** 07:54:41.621"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:54:41.621" endtime="******** 07:54:41.621"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:54:51.623" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:54:41.621" endtime="******** 07:54:51.623"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:54:51.623" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:54:51.645" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 07:54:51.623" endtime="******** 07:54:51.645"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:54:51.645" endtime="******** 07:54:51.645"/>
</kw>
<status status="PASS" starttime="******** 07:54:10.834" endtime="******** 07:54:51.645"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:54:51.768" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:54:51.645" endtime="******** 07:54:51.768"/>
</kw>
<status status="PASS" starttime="******** 07:54:05.054" endtime="******** 07:54:51.768"/>
</kw>
<status status="PASS" starttime="******** 07:54:05.054" endtime="******** 07:54:51.768"/>
</kw>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:54:51.768"/>
</kw>
<kw name="And The user validates the dashboard" library="Dashboard">
<kw name="Log To Console" library="BuiltIn">
<arg>-----------------------------The user views the dashboard</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:54:51.768" endtime="******** 07:54:51.815"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:55:01.815" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:54:51.815" endtime="******** 07:55:01.815"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${ATM_MARKETING_DASHBOARD}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:01.822" level="INFO">Current page contains element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/mat-list-item[1]/span'.</msg>
<status status="PASS" starttime="******** 07:55:01.815" endtime="******** 07:55:01.822"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:01.822" level="INFO">Current page contains element 'xpath=//span[contains(text(),'Calendar View')]/parent::span/parent::*'.</msg>
<status status="PASS" starttime="******** 07:55:01.822" endtime="******** 07:55:01.822"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${ATM_MARKETING_DASHBOARD}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:55:01.822" level="INFO">Clicking element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/mat-list-item[1]/span'.</msg>
<status status="PASS" starttime="******** 07:55:01.822" endtime="******** 07:55:01.904"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:55:11.904" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:55:01.904" endtime="******** 07:55:11.904"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Installed Schedule Version</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 07:55:11.913" level="INFO">Current page contains text 'Installed Schedule Version'.</msg>
<status status="PASS" starttime="******** 07:55:11.904" endtime="******** 07:55:11.914"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_ON_THE_DASHBOARD}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:11.920" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[1]/mat-card/mat-card-content/p/mat-card-subtitle'.</msg>
<status status="PASS" starttime="******** 07:55:11.914" endtime="******** 07:55:11.920"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${ATMS_LATEST_SCHEDULE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:11.927" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[2]/mat-card/mat-card-content/p/mat-card-subtitle'.</msg>
<status status="PASS" starttime="******** 07:55:11.920" endtime="******** 07:55:11.927"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${FAILED_UPLOAD_SCHEDULES}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:11.930" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[3]/mat-card/mat-card-content/p/mat-card-subtitle'.</msg>
<status status="PASS" starttime="******** 07:55:11.927" endtime="******** 07:55:11.930"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${DEVICES_ATM}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:11.940" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[4]/mat-card/mat-card-content/p/mat-card-subtitle'.</msg>
<status status="PASS" starttime="******** 07:55:11.930" endtime="******** 07:55:11.940"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CURRENT_VERSION}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 07:55:11.947" level="INFO">Current page contains element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[5]/mat-card/mat-card-content/p/mat-card-subtitle'.</msg>
<status status="PASS" starttime="******** 07:55:11.940" endtime="******** 07:55:11.947"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${Campaign_Number}</var>
<arg>${CAMPAIGN_NUMBERS}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:55:11.965" level="INFO">${Campaign_Number} = 54</msg>
<status status="PASS" starttime="******** 07:55:11.947" endtime="******** 07:55:11.965"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${ATMS_Latest_Schedule_Numbers}</var>
<arg>${ATMS_LATEST_SCHEDULE_NUMBERS}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:55:11.972" level="INFO">${ATMS_Latest_Schedule_Numbers} = 837</msg>
<status status="PASS" starttime="******** 07:55:11.965" endtime="******** 07:55:11.972"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${Failed_Upload_Schedules_Numbers}</var>
<arg>${FAILED_UPLOAD_SCHEDULES_NUMBERS}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:55:11.987" level="INFO">${Failed_Upload_Schedules_Numbers} = 0</msg>
<status status="PASS" starttime="******** 07:55:11.972" endtime="******** 07:55:11.987"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${Devices_ATM_Numbers}</var>
<arg>${DEVICES_ATM_NUMBERS}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:55:12.003" level="INFO">${Devices_ATM_Numbers} = 5729</msg>
<status status="PASS" starttime="******** 07:55:11.987" endtime="******** 07:55:12.003"/>
</kw>
<kw name="Should Not Be Equal" library="BuiltIn">
<arg>${Campaign_Number}</arg>
<arg>0</arg>
<doc>Fails if the given objects are equal.</doc>
<status status="PASS" starttime="******** 07:55:12.003" endtime="******** 07:55:12.003"/>
</kw>
<kw name="Should Not Be Equal" library="BuiltIn">
<arg>${Devices_ATM_Numbers}</arg>
<arg>0</arg>
<doc>Fails if the given objects are equal.</doc>
<status status="PASS" starttime="******** 07:55:12.003" endtime="******** 07:55:12.003"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>Dashboard.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:55:12.076" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Dashboard.png"&gt;&lt;img src="Dashboard.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:55:12.003" endtime="******** 07:55:12.076"/>
</kw>
<status status="PASS" starttime="******** 07:54:51.768" endtime="******** 07:55:12.088"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 07:55:12.088" endtime="******** 07:55:12.126"/>
</kw>
<status status="PASS" starttime="******** 07:55:12.088" endtime="******** 07:55:12.126"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:55:12.126" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 07:55:12.208" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-3.png"&gt;&lt;img src="selenium-screenshot-3.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 07:55:12.208" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 07:55:12.126" endtime="******** 07:55:12.208"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:55:12.208" endtime="******** 07:55:12.208"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:55:12.208" endtime="******** 07:55:12.208"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 07:55:12.208" endtime="******** 07:55:12.208"/>
</kw>
<status status="FAIL" starttime="******** 07:55:12.126" endtime="******** 07:55:12.208"/>
</kw>
<status status="PASS" starttime="******** 07:55:12.126" endtime="******** 07:55:12.208"/>
</kw>
<status status="PASS" starttime="******** 07:55:12.088" endtime="******** 07:55:12.208"/>
</kw>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:55:12.208"/>
</kw>
<doc>Validates Calendar View Page</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 07:54:04.895" endtime="******** 07:55:12.208"/>
</test>
<doc>Testing Future-fit APC Portal ATM Marketing Dashboard</doc>
<status status="PASS" starttime="******** 07:54:04.484" endtime="******** 07:55:15.647"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 07:54:08.124" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 07:54:21.138" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:54:41.360" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:54:51.623" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:55:15.568" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
