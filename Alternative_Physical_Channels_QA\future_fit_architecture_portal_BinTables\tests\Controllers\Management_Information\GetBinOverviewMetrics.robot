*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../keywords/controllers/GetBinOverviewMetrics_Keywords.robot
Resource                            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
GetBinOverviewMetrics
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${SERVER_NUMBER}  ${EXPECTED_STATUS_CODE}   
    Set Test Documentation  ${DOCUMENTATION}
    Set Global Variable    ${GLOBAL_SERVER_NUMBER}    ${SERVER_NUMBER}

    Given The User sends a Get Request for GetBinOverviewMetrics using the Server Number    ${BASE_URL}      ${SERVER_NUMBER}   
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}       


| *** Test Cases ***                                |        *DOCUMENTATION*     |         *BASE_URL*          |  *SERVER_NUMBER*      |   *EXPECTED_STATUS_CODE*   |
| GetBinOverviewMetrics   | GetBinOverviewMetrics   | GetBinOverviewMetrics      |                             |        19             |        200                 |
