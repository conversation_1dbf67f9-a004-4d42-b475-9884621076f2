*** Settings ***
#Author Name               : <PERSON>hab<PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                            ../../../common_utilities/CommonUtils.py
Library                                            ../../../future_fit_architecture_portal_BinTables/keywords/controllers/resources/CommonFunctions.py


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot


*** Variables ***
${BIN_TYPES_BTN}                                    xpath=//p[contains(text(),'BIN TYPES')]
${BINS_BTN}                                         xpath=//p[contains(text(),'BINS')]


${USERNAME_INPUT}                                   name=username
${PASSWORD_INPUT}                                   name=password
${MICROSOFT_PICK_ACCOUNT}                           xpath=//small[@data-bind='text: session.unsafe_displayName']
${MICROSOFT_EMAIL_INPUT}                            name=loginfmt
${MICROSOFT_EMAIL_NEXT_BTN}                         id=idSIButton9
${MICROSOFT_EMAIL_PASSWORD_INPUT}                   name=passwd
${MICROSOFT_EMAIL_SIGN_IN_BTN}                      id=idSIButton9
${MICROSOFT_DONT_SAVE_PASSWORD}                     id=idBtn_Back
${LOG_OUT_TEXT_ELEMENT}                             xpath=//div[@class='wrap-content']
${BROWSER}

${BIN_TABLES_ADMIN_USER}                            ac30fc6b-5773-4edc-97f3-412e489e1c1f
${BIN_TABLES_CAPTURER_USER}                         decd98cd-20cf-4663-a6ad-60f6762bd241
${BIN_TABLES_APPROVER_USER}                         e2cec3df-91e1-4830-817b-0cbd22148e54
${BIN_TABLES_HOME_PAGE_ICON}                        xpath=//a[contains(@class, 'ng-star-inserted') and contains(text(),'BIN Tables')]



*** Keywords ***
The user navigates to Bin Tables Home page

    ${home_page_button_exists}=     Run Keyword And Return Status    Page Should Contain Element    ${BIN_TABLES_HOME_PAGE_ICON}

    IF    ${home_page_button_exists}
        Log     The 'Bin Tables Home Page Icon' button is displayed on the page.
    ELSE
        Capture Page Screenshot   BinTables_Home_Btn_NOT_Displayed.png
        Fail    The 'Bin Tables Home Page Icon' button was not displayed on the page!
    END
    Click Element    ${BIN_TABLES_HOME_PAGE_ICON}
    Sleep    3s
    Capture Page Screenshot   BinTables_Home_Btn_Displayed.png

    Log to console  --------------------------The user has clicked BinTables Home Page Menu


The User clicks Bin Type Menu

    ${bin_types_button_exists}=     Run Keyword And Return Status    Page Should Contain Element    ${BIN_TYPES_BTN}

    IF    ${bin_types_button_exists}
        Log     The 'Bin Types' button is displayed on the page.
    ELSE
        Capture Page Screenshot   BinTypes_Btn_NOT_Displayed.png
        Fail    The 'Bin Types' button was not displayed on the page!
    END

    Click Element    ${BIN_TYPES_BTN}
    Sleep    3s
    Capture Page Screenshot   BinTypes_Menu_Displayed.png

    Log to console  --------------------------The user has clicked BinTypes Menu



The User clicks Bins Menu

    ${bin_types_button_exists}=     Run Keyword And Return Status    Page Should Contain Element    ${BINS_BTN}

    IF    ${bin_types_button_exists}
        Log     The 'Bins' button is displayed on the page.
    ELSE
        Capture Page Screenshot   Bins_Btn_NOT_Displayed.png
        Fail    The 'Bins' button was not displayed on the page!
    END

    Click Element    ${BINS_BTN}
    Sleep    3s
    Capture Page Screenshot   Bins_Menu_Displayed.png

    Log to console  --------------------------The user has clicked Bins Menu

The Bin Table landing page must be displayed

     ${bin_types_button_exists}=     Run Keyword And Return Status    Page Should Contain Element    ${BIN_TYPES_BTN}

    IF    ${bin_types_button_exists}
        Log     The 'Bin Types' button is displayed on the page.
    ELSE
        Capture Page Screenshot   BinTypes_Btn_NOT_Displayed.png
        Run Keyword And Continue On Failure     Fail    The 'Bin Types' button was not displayed on the page!
    END

    ${bin_button_exists}=     Run Keyword And Return Status    Page Should Contain Element    ${BINS_BTN}

    IF    ${bin_button_exists}
        Log     The 'Bins' button is displayed on the page.
    ELSE
        Capture Page Screenshot   Bins_Btn_NOT_Displayed.png
        Fail    The 'Bins' button was not displayed on the page!
    END

    Capture Page Screenshot   Bins_Landing_Page_Displayed.png


The user access for granted for Bin Tables must correspond to the access of the logged in user
    [Arguments]     ${REQUIRED_USER_ACCESS}


    #This is the boolean value that checks if the correct user access is provided
    ${correct_access_name_provided}=        Set Variable    ${False}
    ${correct_access_name_provided}=        Set Variable If
         ...       '${REQUIRED_USER_ACCESS}' == 'Admin'        ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'Capturer'     ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'Approver'     ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'ADMIN'        ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'CAPTURER'     ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'APPROVER'     ${True}

    Run Keyword If    not ${correct_access_name_provided}
    ...    Fail     Please provide the right data for REQUIRED_USER_ACCESS. Expected values are: Admin, Capturer or Approver.

    ${uiid_to_verify}=        Set Variable If
         ...       '${REQUIRED_USER_ACCESS}' == 'Admin'        ${BIN_TABLES_ADMIN_USER}
         ...       '${REQUIRED_USER_ACCESS}' == 'Capturer'     ${BIN_TABLES_CAPTURER_USER}
         ...       '${REQUIRED_USER_ACCESS}' == 'Approver'     ${BIN_TABLES_APPROVER_USER}

    ${bin_tables_roles_not_required_by_user}=     Create List    ${BIN_TABLES_ADMIN_USER}     ${BIN_TABLES_CAPTURER_USER}       ${BIN_TABLES_APPROVER_USER}

    Remove Values From List    ${bin_tables_roles_not_required_by_user}    ${uiid_to_verify}
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${local_storage_keys}=    get local storage keys and values     net-accesstoken
    Log Many   Local Storage Keys:  ${local_storage_keys.items()}
    ${local_storage_keys_len}=   Get Length     ${local_storage_keys.items()}
    FOR    ${key}    ${value}    IN    &{local_storage_keys}
        ${json_obj}=    Evaluate    json.loads('''${value}''')    json
        ${secret}=    Get From Dictionary    ${json_obj}    secret
        Exit For Loop
    END

    ${decoded_token}=      decode jwt       ${secret}
    ${groups}=    Get From Dictionary    ${decoded_token}    groups
    Log Many        ${groups}
    ${required_group_found}=       Set Variable     ${False}
    FOR    ${item}    IN    @{groups}
        Log Many    List Item: ${item}
        #Verify that the correct groups have been allocated to the user
        FOR    ${unwanted_group}    IN    @{bin_tables_roles_not_required_by_user}
            IF    '${item.strip()}' == '${unwanted_group.strip()}'
                ${unwanted_group_name}=     Get From Dictionary     ${bin_tables_roles}         ${unwanted_group}
                Run Keyword And Continue On Failure    Fail    The Bin Tables role: '${unwanted_group_name}' has been assigned to the '${REQUIRED_USER_ACCESS}' user.
            END
        END

        IF    '${item.strip()}' == '${uiid_to_verify.strip()}'
                ${required_group_found}=       Set Variable     ${True}
        END
    END



    Run Keyword If    ${required_group_found}
    ...    Log Many    The Bin Tables role: '${REQUIRED_USER_ACCESS}' which is supposed to be assigned to the current user was found on the groups allocated to the user's access.
    ...  ELSE
    ...    Fail    The Bin Tables role: '${REQUIRED_USER_ACCESS}' which is supposed to be assigned to the current user was not found on the groups allocated to the user's access.

The user retrieves the role of the logged in user
    ${local_storage_keys}=    get local storage keys and values     net-accesstoken
    Log Many   Local Storage Keys:  ${local_storage_keys.items()}
    ${local_storage_keys_len}=   Get Length     ${local_storage_keys.items()}
    FOR    ${key}    ${value}    IN    &{local_storage_keys}
        ${json_obj}=    Evaluate    json.loads('''${value}''')    json
        ${secret}=    Get From Dictionary    ${json_obj}    secret
        Exit For Loop
    END
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${decoded_token}=      decode jwt       ${secret}
    ${groups}=    Get From Dictionary    ${decoded_token}    groups
    Log Many        ${groups}
    ${required_group_found}=       Set Variable     ${False}
    FOR    ${item}    IN    @{groups}
        Log Many    List Item: ${item}
        #Check if the user has one of Bin Tables roles
        ${bin_table_role_found}=        Run Keyword And Return Status    Should Be Equal As Strings    ${item.strip()}    ${BIN_TABLES_ADMIN_USER.strip()}
        IF    ${bin_table_role_found}
            ${required_group_found}=       Set Variable     ${True}
            Exit For Loop
        END
        ${bin_table_role_found}=        Run Keyword And Return Status    Should Be Equal As Strings    ${item.strip()}    ${BIN_TABLES_CAPTURER_USER.strip()}
        IF    ${bin_table_role_found}
            ${required_group_found}=       Set Variable     ${True}
            Exit For Loop
        END
        ${bin_table_role_found}=        Run Keyword And Return Status    Should Be Equal As Strings    ${item.strip()}    ${BIN_TABLES_APPROVER_USER.strip()}
        IF    ${bin_table_role_found}
            ${required_group_found}=       Set Variable     ${True}
            Exit For Loop
        END
    END
    #If the user has a Bin Tables role then return the role
    IF    ${required_group_found}
            ${user_role_description}=    Get From Dictionary    ${bin_tables_roles}    ${item.strip()}
            Log Many     The current user is allocated the Bin Tables role of '${user_role_description}'.
            RETURN   ${user_role_description}
    ELSE
            Fail     The current user is not allocated any of the Bin Tables roles.
            RETURN   ${user_role_description}
    END






