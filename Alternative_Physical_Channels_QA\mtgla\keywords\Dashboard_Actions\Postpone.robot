*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Navigation Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                            RequestsLibrary
Library                                            Collections
Library                                            String
Library                                             DatabaseLibrary
Library                                          ../../utility/DatabaseUtility.py
Library                                            DateTime

#***********************************PROJECT RESOURCES***************************************


*** Variables ***
#Postpone 
${Postpone_Action_Locator}                              //*[text()[normalize-space(.)='Postpone']]
${Postpone_Reason_Locator}                              //select[@id='PostponeReason']
${Postponse_Reason_Selection}                           //select[@id='PostponeReason']/option[text()='Absence of mandated official']
${Postponse_Date_Locator}                               //*[@id='PostponeDate']
${Incident_Number_Locator}                              //input[@id='IncidentNumber']
${Incident_Number}                                      INCTesting
${Postpone_Comment_Locator}                             //textarea[@id='Comment']
${Postpone_Comment}                                     Testing Postpone Action 
${Postponse_Action_Query}                               SELECT TOP (1000) [Id] ,[PostponeReason] ,[CommentText] ,[IncidentNumber] ,[PostponementDate] FROM [ATM_Mismatch_DEV].[dbo].[ATMControl_Daily_AtmRecons] ORDER BY [Id] DESC;
${Postpone_Reason_Front_End}                            1
${SAVE_ACTION_BUTTON}                                   //button[@data-save='modal']

*** Keywords ***
The User Checks And Actions Postpone If It Exists
    Page Should Contain    Cost Centre Information
    Page Should Contain    Dashboard 

    ${element_exists}=    Run Keyword And Return Status    Element Should Be Visible    ${Postpone_Action_Locator}
    Run Keyword If    ${element_exists}    Perform Postpone Flow
    Run Keyword Unless    ${element_exists}    Log To Console    "Postpone action is not available."

Perform Postpone Flow 
    Log To Console    "Postpone is available, proceeding with the action..."
    Click Element     ${Postpone_Action_Locator}
    Sleep    5s

    #Get Defaulted Date
    Click Element    ${Postponse_Date_Locator}
    ${Postpone_front_end_date}=    Get Text   ${Postponse_Date_Locator}/option[1]
    Log to console    Postpone Date:${Postpone_front_end_date}

    #Selecting Postpone Reason from dropdown
    Click Element     ${Postpone_Reason_Locator}
    #${Postpone_Reason_Front_End}=    Get Text    ${Postponse_Reason_Selection}
    #Log to console    Postpone Reason:${Postpone_Reason_Front_End}
    Click Element     ${Postponse_Reason_Selection}
    Sleep    3s

    #Input Incident Number 
    Input Text    ${Incident_Number_Locator}    ${Incident_Number}
    Sleep    2s

    #Input Comment 
    Input Text    ${Postpone_Comment_Locator}    ${Postpone_Comment} 
    Sleep    2s

    #Save Postponse Action
    Click Element    ${SAVE_ACTION_BUTTON}
    Sleep    5s
 

    #DATABASE VALIDATION
    ${Database_Postpone}=    DatabaseUtility.Execute Sql Query        ${Postponse_Action_Query}   
    Log To Console    ${Database_Postpone}

    #Extract database data
    ${Database_Postpone_Item}=    Get From List    ${Database_Postpone}    0

    #extract the dictionary key values from the item
    #DATE
    ${Postpone_Date_From_DB}=     Get From Dictionary    ${Database_Postpone_Item}    PostponementDate
    ${Trimmed_Postpone_Date_From_DB}=    Evaluate    str("${Postpone_Date_From_DB}").split('.')[0]
    ${Formatted_Postpone_Date_From_DB}=    Convert Date    ${Trimmed_Postpone_Date_From_DB}    result_format=%Y/%m/%d
    Log To Console    Formatted Database Postpone Date: ${Formatted_Postpone_Date_From_DB}
    
    #POSTPONE REASON
    ${Postpone_Reason_From_DB}=    Get From Dictionary    ${Database_Postpone_Item}    PostponeReason

    #INCIDENT NUMBER 
    ${Postpone_IncidentNo_From_DB}=    Get From Dictionary    ${Database_Postpone_Item}    IncidentNumber

    #POSTPONSE COMMENT
    ${Postpone_Comment_From_DB}=    Get From Dictionary    ${Database_Postpone_Item}    CommentText

    #Log the extracted values for verification
    Log To Console    Database Postpone Date: ${Formatted_Postpone_Date_From_DB}
    Log To Console    Database Postpone Reason: ${Postpone_Reason_From_DB}
    Log To Console    Database Incident Number: ${Postpone_IncidentNo_From_DB}
    Log To Console    Database Postpone Comment: ${Postpone_Comment_From_DB}



    #Compare Front End Data to Database Data
    Should Be Equal As Strings    ${Postpone_front_end_date}    ${Formatted_Postpone_Date_From_DB}
    Should Be Equal As Strings    ${Postpone_Reason_Front_End}    ${Postpone_Reason_From_DB}
    Should Be Equal As Strings    ${Incident_Number}    ${Postpone_IncidentNo_From_DB}
    Should Be Equal As Strings    ${Postpone_Comment}    ${Postpone_Comment_From_DB}

    Log to console    Postpone action successfully saved to the database. The front-end data match with the database data.