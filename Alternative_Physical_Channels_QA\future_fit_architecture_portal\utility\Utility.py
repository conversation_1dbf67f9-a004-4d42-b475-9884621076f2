import datetime
import os


class Utility:
    def get_path(self):
        working_directory = os.getcwd()
        return working_directory

    # ---------------------------------GET CURRENT DATE ---------------------------------------
    def get_current_date(self):
        datetime
        try:
            return datetime.date.today().strftime("%Y%m%d")
        except ValueError:
            print("\nError: Please check your date format")
            return 0

    # ----------------------ADD DAYS TO DATE OF BIRTH---------------------------
    def add_days_to_current_date(self, days_increment):
        try:
            current_date = str(self.get_current_date())

            initial_date_temp = datetime.datetime.strptime(current_date, "%Y%m%d")
            # print("@@@@@@@@@@@@@@",current_date)
            final_date = initial_date_temp + datetime.timedelta(days=days_increment)
            # print("@@@@@@@@@@@@@@",final_date)
            return final_date
            print("\nError: Please check your date format")
            return 0
        except TypeError:
            print("\nError: Please enter date in string format")
            return 0

    # ----------------------ADD DAYS TO DATE OF BIRTH---------------------------
    def get_day_to_select(self, days_increment):
        print("Selected day is ", days_increment)
        try:
            day = str(self.add_days_to_current_date(int(days_increment)))[8:10]
            print("dayalone:", day)
            if day[0:1] == "0":
                print("The date is less than 10")
                return day[1:2]
            else:
                print("The date is 10 or greater")
                return day
            return day
        except ValueError:
            print("\nError: Please check your date format")
            return 0
        except TypeError:
            print("\nError: Please enter date in string format")
            return 0

    def curent_day(self, days_increment):
        try:
            current_date = str(self.get_current_date())

            print(datetime.date.today().strftime("%d"))
            current_day = datetime.date.today().strftime("%d")
            total_days = current_day + days_increment
            print(total_days)

            return 0

        except TypeError:
            print("\nError: Please enter date in string format")
            return 0

    def find_project_root(start_path, marker='data'):
        current_path = start_path
        print(os.path.dirname(current_path))
        while current_path != os.path.dirname(current_path):  # Check if we are at the root
            if marker in os.listdir(current_path):
                return current_path
            current_path = os.path.dirname(current_path)
        return None

    # Usage
    #print(os.getcwd())
    #project_root = find_project_root(os.getcwd())
    #print(project_root)


