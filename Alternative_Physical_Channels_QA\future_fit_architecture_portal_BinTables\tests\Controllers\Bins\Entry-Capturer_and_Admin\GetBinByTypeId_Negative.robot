*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetBinById_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test
    [Arguments]        ${DOCUMENTATION}     ${BASE_URL}      ${EXPECTED_STATUS_CODE}     ${BIN_ID}     ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to search for bins using the Bin Id      ${BASE_URL}      ${BIN_ID}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed                            ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                                                                                                    |        *DOCUMENTATION*    		           |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |                *BIN_ID*                   |             *EXPECTED_ERROR_MESSAGE*                             |
| Search for a Bin Numbered using invalid 'binid ' on the GetBinById Controller                    | Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test       | Search Bin by Number on the GetBinById API   |                                     |           404               |   0193877e-54fe-791b-9ecf-e8600f2f2431    |   The bin with the specified identifier was not found.           |
| Search for a Bin Numbered using empty 'binid ' on the GetBinById Controller                      | Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test       | Search Bin by Number on the GetBinById API   |                                     |           404               |                                           |   Bin Id must not be null or empty.                              |
| Search for a Bin Numbered using numeric 'binid ' on the GetBinById Controller                    | Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test       | Search Bin by Number on the GetBinById API   |                                     |           400               |   121212122543237912342342342342342344    |   The value '121212122543237912342342342342342344' is not valid. |
| Search for a Bin Numbered using alpha-only 'binid ' on the GetBinById Controller                 | Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test       | Search Bin by Number on the GetBinById API   |                                     |           400               |   shdnfgrtfhdkfheyrhfjrfhfhrjfhfjrhryf    |   The value 'shdnfgrtfhdkfheyrhfjrfhfhrjfhfjrhryf' is not valid. |
| Search for a Bin Numbered using 'binid ' made up os special chars on the GetBinById Controller   | Search for a Bin using a Bin ID on the GetBinById Controller - Negative Test       | Search Bin by Number on the GetBinById API   |                                     |           400               |   @#$#*$&^%#*$^#%$()*&^%!~`,<>.?/?/;:{    |   The value '@#$#*$&^%#*$^#%$()*&^%!~`,<>.?/?/;:{' is not valid. |