*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/RejectBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Reject the pending Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_ID}   ${BIN_OUTCOME}    ${BIN_REJECTION_COMMENTS}   ${BIN_STATUS}    ${EXPECTED_STATUS_CODE}   ${REJECTED_BIN_STATUS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The Bin details are reflecting as expected on the database    ${BIN_ID}   ${BIN_OUTCOME}    ${EMPTY}   ${BIN_STATUS}
    And The user verifies that the current BIN is not Deleted   ${BIN_ID}
    When The User Populates the Reject Bin JSON payload with    ${BIN_ID}   ${BIN_OUTCOME}    ${BIN_REJECTION_COMMENTS}
    And The User executes the Reject Bin API Request    ${BASE_URL}
    When The Reject Bin controller returns an expected status code     ${EXPECTED_STATUS_CODE}
    Then The rejected bin details must be showing as expcted on the database     ${BIN_ID}   ${BIN_OUTCOME}    ${BIN_REJECTION_COMMENTS}   ${REJECTED_BIN_STATUS}

| *** Test Cases ***                                                                   |                   *DOCUMENTATION*                      |         *BASE_URL*               |          *BIN_ID*                          |          *BIN_OUTCOME*         |          *BIN_REJECTION_COMMENTS*              |          *BIN_STATUS*         |       *EXPECTED_STATUS_CODE*  |       *REJECTED_BIN_STATUS*   |
| Reject the pending bin with a comment.   | Reject the pending Bin                    | Reject a pending Bin that is assigned to the approver  |                                  |  887d9395-92e7-48f8-978b-897b8a623775      |            Added               |          The bin must be deleted not added.    |            Pending            |               200             |               Rejected        |
