*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1321




*** Keywords ***
Verify Bins displayed on View Entries Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TO_VERIFY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    Then The View Entries page must display Bins that match with the database data      ${BIN_TO_VERIFY}


| *** Test Cases ***                                                                                                                                |        *DOCUMENTATION*    		        |         *BASE_URL*                  |         *BIN_TO_VERIFY*           |
| Verify that the GetAllBins API returns all BINs when the user accesses the "View Menu"             | Verify Bins displayed on View Entries Page   | Verify bins against the database data.    |           ${EMPTY}                  |         ${EMPTY}                  |
