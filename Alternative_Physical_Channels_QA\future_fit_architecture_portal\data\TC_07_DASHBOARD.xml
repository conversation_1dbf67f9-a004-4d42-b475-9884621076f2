<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240726 08:44:17.855">
   <suite name="Future-Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\Dashboard\TC_11_GET_Dashboard_CONTROLLER.robot">
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data" id="s1-t1">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:44:19.585" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20240726 08:44:19.880" status="PASS" starttime="20240726 08:44:19.585"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:44:19.880" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20240726 08:45:03.479" status="PASS" starttime="20240726 08:44:19.880"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:45:03.479" level="INFO">And The service returns http status</msg>
            <status endtime="20240726 08:45:03.481" status="PASS" starttime="20240726 08:45:03.479"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:45:03.481" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20240726 08:45:03.500" status="PASS" starttime="20240726 08:45:03.481"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) must correspond to the APC Database">
            <doc>Verify the cotroller data for 'totalCampaigns' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:45:03.500" level="INFO">Then The field value(s) must correspond to the APC Database</msg>
            <status endtime="20240726 08:46:13.782" status="PASS" starttime="20240726 08:45:03.500"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data</tag>
         </tags>
         <status endtime="20240726 08:46:13.782" critical="yes" status="PASS" starttime="20240726 08:44:19.584"/>
      </test>
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data" id="s1-t2">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:46:13.784" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20240726 08:46:14.021" status="PASS" starttime="20240726 08:46:13.784"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:46:14.021" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20240726 08:47:08.706" status="PASS" starttime="20240726 08:46:14.021"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:47:08.706" level="INFO">And The service returns http status</msg>
            <status endtime="20240726 08:47:08.708" status="PASS" starttime="20240726 08:47:08.706"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:47:08.708" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20240726 08:47:08.718" status="PASS" starttime="20240726 08:47:08.708"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) must correspond to the APC Database">
            <doc>Verify the cotroller data for 'latestScheduleCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:47:08.718" level="INFO">Then The field value(s) must correspond to the APC Database</msg>
            <status endtime="20240726 08:47:34.464" status="PASS" starttime="20240726 08:47:08.718"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data</tag>
         </tags>
         <status endtime="20240726 08:47:34.466" critical="yes" status="PASS" starttime="20240726 08:46:13.783"/>
      </test>
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data" id="s1-t3">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:47:34.470" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20240726 08:47:34.717" status="PASS" starttime="20240726 08:47:34.470"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:47:34.717" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20240726 08:48:14.635" status="PASS" starttime="20240726 08:47:34.717"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:48:14.636" level="INFO">And The service returns http status</msg>
            <status endtime="20240726 08:48:14.638" status="PASS" starttime="20240726 08:48:14.636"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:48:14.638" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20240726 08:48:14.654" status="PASS" starttime="20240726 08:48:14.638"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) must correspond to the APC Database">
            <doc>Verify the cotroller data for 'failedUploadSchedulesCount' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:48:14.654" level="INFO">Then The field value(s) must correspond to the APC Database</msg>
            <status endtime="20240726 08:48:35.794" status="PASS" starttime="20240726 08:48:14.654"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data</tag>
         </tags>
         <status endtime="20240726 08:48:35.794" critical="yes" status="PASS" starttime="20240726 08:47:34.468"/>
      </test>
      <test name="FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data" id="s1-t4">
         <kw library="Selenium" name="Given The user creates a rest session">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:48:35.797" level="INFO">Given The user creates a rest session</msg>
            <status endtime="20240726 08:48:36.367" status="PASS" starttime="20240726 08:48:35.797"/>
         </kw>
         <kw library="Selenium" name="When The user makes Get Rest Call">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:48:36.367" level="INFO">When The user makes Get Rest Call</msg>
            <status endtime="20240726 08:49:14.291" status="PASS" starttime="20240726 08:48:36.367"/>
         </kw>
         <kw library="Selenium" name="And The service returns http status">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:49:14.291" level="INFO">And The service returns http status</msg>
            <status endtime="20240726 08:49:14.292" status="PASS" starttime="20240726 08:49:14.291"/>
         </kw>
         <kw library="Selenium" name="And The user reads the field value returned by the controller">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:49:14.292" level="INFO">And The user reads the field value returned by the controller</msg>
            <status endtime="20240726 08:49:14.311" status="PASS" starttime="20240726 08:49:14.292"/>
         </kw>
         <kw library="Selenium" name="Then The field value(s) must correspond to the APC Database">
            <doc>Verify the cotroller data for 'currentVersion' against the database</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20240726 08:49:14.311" level="INFO">Then The field value(s) must correspond to the APC Database</msg>
            <status endtime="20240726 08:49:14.857" status="PASS" starttime="20240726 08:49:14.311"/>
         </kw>
         <tags>
            <tag>FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data</tag>
         </tags>
         <status endtime="20240726 08:49:14.858" critical="yes" status="PASS" starttime="20240726 08:48:35.794"/>
      </test>
      <status endtime="20240726 08:49:14.862" status="PASS" starttime="20240726 08:44:17.855"/>
   </suite>
   <statistics>
      <total>
         <stat pass="4" fail="0">Critical Tests</stat>
         <stat pass="4" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'totalCampaigns' field data is the same a database data</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'latestScheduleCount' field data is the same a database data</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'failedUploadSchedulesCount' field data is the same a database data</stat>
         <stat pass="1" fail="0">FFT - Controllers - Get all Dashboard Details and verify that the 'currentVersion' field data is the same a database data</stat>
      </tag>
      <suite>
         <stat name="Future-Fit Portal" pass="4" fail="0" id="s1">Future-Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
