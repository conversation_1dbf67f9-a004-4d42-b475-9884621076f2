*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Documentation              Bin Tables GetUploadManagementInformation Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            ../../keywords/controllers/resources/managementinformation/GetUploadManagementInformation.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***


*** Keywords ***
The User sends a Get Request for GetUploadManagementInformation using the server version & bin status action
    [Arguments]     ${BASE_URL}     ${SERVER_VERSION}    ${BIN_STATUS_ACTION}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetUploadManagementInformation Instance    ${base_url}     ${SERVER_VERSION}    ${BIN_STATUS_ACTION}     #Create an instance of   GetBinsToReview

    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   headers=${headers}   method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'GetBinsToReview' REST API call failed, the returned status is '${status_code}'


# The below keywords are used to interact with the API POJOS

Create GetUploadManagementInformation Instance
    [Arguments]    ${BASE_URL}  ${SERVER_VERSION}    ${BIN_STATUS_ACTION}
    ${instance}=    Evaluate    GetUploadManagementInformation.CreateRESTRequest('${BASE_URL}','${SERVER_VERSION}','${BIN_STATUS_ACTION}')    modules=GetUploadManagementInformation
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords
Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}

    # Count the number of BINS returned
    ${UMI_instance}=      Get the Upload Management Information Details
    ${UMI_count}=    Get Length    ${UMI_instance}
    Log Many    ${UMI_count}
    Set Global Variable    ${TOTAL_NUMBER_OF_UMI}        ${UMI_count}

Get Response Status Code
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}


#Keyword to read successful response
Get the Upload Management Information Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_upload_management_information_details
    RETURN    ${result}