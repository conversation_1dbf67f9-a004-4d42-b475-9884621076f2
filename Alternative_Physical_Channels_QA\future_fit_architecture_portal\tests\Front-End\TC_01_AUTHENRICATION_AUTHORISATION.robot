*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables

Documentation                                       Testing future fit Authentication & Authorisation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/Navigation.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../Keywords/atm_marketing/CalendarView.robot
Resource                                            ../../keywords/atm_marketing/Authentication_Authorisation.robot

*** Keywords ***
Validates Authentication and Authorisation   
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${LOGON_USER}  ${TEST_ENVIRONMENT}  ${Test_Case}
    Set Test Documentation  ${DOCUMENTATION}

    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID} 

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}  Chrome  drivers\chromedriver.exe  ${LOGON_USER}

    And Authentication_Authorisation.Validate the test cases    ${Test_Case} 

    Then User logs out

| *** Test Cases ***                                                                   |      *DOCUMENTATION*             |  *TESTRAIL_TESTCASE_ID*    |      *LOGON_USER*         | *TEST_ENVIRONMENT*  |  *Test_Case*                |
#| Login & Logout - BA            | Validates Authentication and Authorisation   | Login & Logout                   |     155057390		        | BUSINESS_APPROVER         |      APC_UAT        |       *              |
#| Login- BA- Calendar            | Validates Authentication and Authorisation   | Login & Validate Calendar        |     155057392		        | BUSINESS_APPROVER         |      APC_UAT        |  Login- BA- Calendar        |
#| Login- BA- Dashboard           | Validates Authentication and Authorisation   | Login & Validate Dashboard       |     155057391	            | BUSINESS_APPROVER         |      APC_UAT        |	 Login- BA- Dashboard       |
#| Login- BA- Approval            | Validates Authentication and Authorisation   | Login & Validate Approvals       |     155057393		        | BUSINESS_APPROVER         |      APC_UAT        | Login- BA- Approval         |
| Login & Logout - BU            | Validates Authentication and Authorisation   | Login & Logout                   |     T155057357		        | BUSINESS_USER             |      APC_UAT        |      *                      |
| Login- BU- Calendar            | Validates Authentication and Authorisation   | Login & Validate Calendar        |     T155057360		        | BUSINESS_USER             |      APC_UAT        | Login- BA- Calendar         |
| Login- BU- Dashboard           | Validates Authentication and Authorisation   | Login & Validate Dashboard       |     T155057358	            | BUSINESS_USER             | 	   APC_UAT        | Login- BA- Dashboard        |
| Login- BU- Capture Campaign    | Validates Authentication and Authorisation   | Login & Validate Approvals       |     T155057384		        | BUSINESS_USER             |      APC_UAT        | Login- BU- Capture          |