import os
import base64
import re
import xml.etree.ElementTree as ET

import jwt
from jproperties import Properties
from robot.api.deco import keyword, library


def extract_values_from_xml(xml_file):
    try:
        # Parse the XML file
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # Define the namespaces (the default namespace in the XML)
        namespaces = {
            '': 'http://www.W3.org/BINTableSchema.xsd'  # Default namespace (empty string)
        }

        # Find all BIN elements using the namespace in the query
        values = []
        #print('Extracting values.....')
        for bin_elem in root.findall(".//BIN", namespaces):
            number_value = bin_elem.get("Number")  # Extract the "Number" attribute
            if number_value:
                values.append(number_value)

        return values

    except ET.ParseError as e:
        raise RuntimeError(f"Error parsing XML: {e}")
    except Exception as e:
        raise RuntimeError(f"An error occurred while extracting values from file...: {e}")


@library(scope='GLOBAL', auto_keywords=True)
class CommonFunctions:

    @keyword
    def exit_suite(self):
        raise Warning("Exiting the suite due to a condition")

    @keyword
    def decode_base64(self, encoded_str):
        try:
            # Step 1: Decode the base64 string to bytes
            decoded_bytes = base64.b64decode(encoded_str)
            # decoded_bytes = base64.b64decode(encoded_str,validate=True)
            # Optionally, return the decoded data as a string (assuming UTF-8 encoding)
            return decoded_bytes.decode('utf-8')  # or you can leave it as bytes
        except Exception as e:
            raise RuntimeError (f"Error decoding base64: {e}")

    @keyword
    def decode_jwt(self, token_str):
        try:
            # Decode the token without verifying the signature
            decoded_token = jwt.decode(token_str, options={"verify_signature": False})
            return decoded_token
        except Exception as e:
            raise RuntimeError(f"Error decoding jwt: {e}")

    @keyword
    def decode_base64_to_utf16_and_save(self, base64_string, file_path):
        try:
            #print('Data to decode.')
            print(base64_string)
            # Step 1: Decode the Base64 string into raw bytes
            decoded_bytes = base64.b64decode(base64_string)

            # Step 2: Decode the bytes to a UTF-16 string (since the original encoding is UTF-16)
            decoded_str = decoded_bytes.decode('utf-8')
            #print('Decoded Data')
            #print(decoded_str)

            # Step 3: Save the decoded string to the XML file in UTF-16 encoding
            with open(file_path, 'w', encoding='utf-16') as xml_file:
                xml_file.write(decoded_str)

            print(f"Decoded XML data saved to {file_path}.")

        except Exception as e:
            # raise RuntimeError(f"An error occurred: {e}")
            raise RuntimeError(f"An error occurred while decoding base64 string...: {e}")

    @keyword
    def is_base64_string(self, input_string):
        # Check if the string length is a multiple of 4 (Base64 encoding padding)
        if len(input_string) % 4 != 0:
            return False

        # Check if the string matches the Base64 character set (letters, digits, +, /, and padding =)
        base64_pattern = re.compile('^[A-Za-z0-9+/=]+$')
        if not base64_pattern.match(input_string):
            return False

        try:
            # Try decoding the string and check if it decodes correctly
            base64.b64decode(input_string, validate=True).decode()
            return True

        except Exception as e:
            raise RuntimeError(f"Error validating base64 string: {e}")


    @keyword
    def check_values_in_xml(self, file_a, file_b):
        """Check if all values in XML file A exist in XML file B."""
        values_a = extract_values_from_xml(file_a)
        #print('values_a')
        #print(values_a)

        if not values_a:
            raise RuntimeError(f"The file named '{file_a}' is empty. Hence it could not be read.")

        values_b = extract_values_from_xml(file_b)
        #print('values_b')
        #print(values_b)

        if not values_b:
            raise RuntimeError(f"The file named '{file_b}' is empty. Hence it could not be read.")

        # Check if every value from file A exists in file B
        missing_values = [value for value in values_a if value not in values_b]

        if missing_values:
            print(f"The following values from {file_a} are missing in {file_b}:")
            for value in missing_values:
                print(value)
            return False, missing_values
        else:
            print(f"All values from {file_a} are present in {file_b}.")
            return True

    @keyword
    def read_values_from_xml(self, file_name):

        values_a = extract_values_from_xml(file_name)
        # print('values_a')
        # print(values_a)

        if not values_a:
            raise RuntimeError(f"The file named '{file_name}' is empty. Hence it could not be read.")
        return  values_a


    @keyword
    def create_and_write_xml(self, file_name, file_data):
        # Open the file in write mode, which will overwrite existing content
        try:
            with open(file_name, "w", encoding="utf-8") as xml_file:
                xml_file.write(file_data)
            print(f"File '{file_name}' has been created or overwritten with new data.")
        except Exception as e:
            print(f"An error occurred while writing to the file: {e}")


class ExitTestException(Exception):
    """Custom exception to indicate the test should be exited."""

    pass
