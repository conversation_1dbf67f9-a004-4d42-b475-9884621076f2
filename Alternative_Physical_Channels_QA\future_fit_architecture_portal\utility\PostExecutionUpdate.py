from acintegration.TestrailIntegration import TestrailIntegration

import os


class PostExecutionUpdate:
    ROBOT_LISTENER_API_VERSION = 2

    def end_test(self, name, attrs):
        status_id = 0
        if attrs['status'] == 'FAIL':
            status_id = 5
            description = f"Test failed with error  : {attrs['message']}"
        else:
            status_id = 1
            description = f'Test completed successfully!!'

        # Update testrail
        testrailIntegration = TestrailIntegration(os.getenv('TESTRAIL_USERNAME').strip(' '),
                                                  os.getenv('TESTRAIL_PASSWORD').strip(' '))

        data = {
            "status_id": status_id,
            "comment": description,
            "version": "V2.2.1"
        }

        print(testrailIntegration.add_result(data, os.getenv('TESTRAIL_TESTCASE_ID')))
        print(
            f"--------------------------Testrail updated for test case {name}, test case id {os.getenv('TESTRAIL_TESTCASE_ID')}")

    def clear_testrail_credentials(self):
        if 'TESTRAIL_USERNAME' in os.environ: os.environ.pop('TESTRAIL_USERNAME')
        if 'TESTRAIL_PASSWORD' in os.environ: os.environ.pop('TESTRAIL_PASSWORD')

