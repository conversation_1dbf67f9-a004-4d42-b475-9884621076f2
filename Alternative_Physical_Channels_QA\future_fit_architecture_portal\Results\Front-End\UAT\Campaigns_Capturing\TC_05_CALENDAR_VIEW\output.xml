<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 07:37:06.815" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_05_CALENDAR_VIEW.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:37:07.998" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:07.998"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:37:07.998" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:07.998"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:37:07.998" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:07.998"/>
</kw>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:07.998"/>
</kw>
<test id="s1-t1" name="BU - Calendar View - Navigation - &lt; &amp; &gt; buttons" line="42">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057350</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 07:37:07.998" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:07.998"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:37:07.998" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057350'.</msg>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:07.998"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:37:08.272" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:37:08.272"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:37:08.272" endtime="******** 07:37:08.272"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:37:08.272" endtime="******** 07:37:08.274"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:37:08.274" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:37:08.274"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:37:08.274" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:37:08.274"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:37:08.276"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 07:37:08.418" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 07:37:09.270" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 07:37:09.270" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 14732 has been terminated.</msg>
<status status="PASS" starttime="******** 07:37:08.276" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:37:09.270" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000024AD39A9670&gt;</msg>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 07:37:09.270" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:37:09.270" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:37:09.270" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 07:37:09.270" endtime="******** 07:37:09.270"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 07:37:09.279" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 07:37:09.279" endtime="******** 07:37:19.254"/>
</kw>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:37:19.254"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 07:37:19.279" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 07:37:19.279" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 07:37:19.255" endtime="******** 07:37:19.279"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 07:37:19.280" endtime="******** 07:37:19.389"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 07:37:19.395" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 07:37:19.395" endtime="******** 07:37:26.208"/>
</kw>
<status status="PASS" starttime="******** 07:37:19.389" endtime="******** 07:37:26.208"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:37:36.211" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:37:26.208" endtime="******** 07:37:36.211"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:37:36.211" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:37:36.243" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 07:37:36.211" endtime="******** 07:37:36.243"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:37:36.243" endtime="******** 07:37:36.243"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:37:56.244" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 07:37:36.243" endtime="******** 07:37:56.244"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:37:56.244" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:37:56.614" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 07:37:56.244" endtime="******** 07:37:56.614"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:37:56.614" endtime="******** 07:37:56.614"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:38:06.615" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:37:56.614" endtime="******** 07:38:06.615"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:38:06.615" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:38:06.628" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 07:38:06.615" endtime="******** 07:38:06.628"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:38:06.628" endtime="******** 07:38:06.628"/>
</kw>
<status status="PASS" starttime="******** 07:37:19.254" endtime="******** 07:38:06.628"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:38:06.770" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-28.png"&gt;&lt;img src="selenium-screenshot-28.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:38:06.628" endtime="******** 07:38:06.770"/>
</kw>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:38:06.770"/>
</kw>
<status status="PASS" starttime="******** 07:37:08.274" endtime="******** 07:38:06.770"/>
</kw>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:38:06.770"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:38:11.770" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:38:06.770" endtime="******** 07:38:11.770"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:11.770" endtime="******** 07:38:11.770"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:38:11.770" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 07:38:11.770" endtime="******** 07:38:11.838"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 07:38:11.838" endtime="******** 07:38:11.867"/>
</kw>
<status status="PASS" starttime="******** 07:38:11.770" endtime="******** 07:38:11.867"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>---------------------------- validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:11.867" endtime="******** 07:38:11.872"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:38:21.873" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:38:11.872" endtime="******** 07:38:21.873"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${get_month_year}</var>
<arg>${CURRENT_MONTH_YEAR_LOCATOR}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:38:21.898" level="INFO">${get_month_year} = May 2024</msg>
<status status="PASS" starttime="******** 07:38:21.873" endtime="******** 07:38:21.898"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${get_month_year}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:21.898" endtime="******** 07:38:21.899"/>
</kw>
<kw name="Get Current Date" library="DateTime">
<var>${current}</var>
<arg>result_format=%Y-%m-%d %H:%M:%S</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="******** 07:38:21.900" level="INFO">${current} = 2024-05-30 07:38:21</msg>
<status status="PASS" starttime="******** 07:38:21.899" endtime="******** 07:38:21.900"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${CURRENT_DATE}</var>
<arg>${current}</arg>
<arg>result_format=%B %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:38:21.905" level="INFO">${CURRENT_DATE} = May 2024</msg>
<status status="PASS" starttime="******** 07:38:21.900" endtime="******** 07:38:21.905"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>NEXT MONTH ${CURRENT_DATE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:21.905" endtime="******** 07:38:21.905"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>${CURRENT_DATE}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 07:38:21.905" endtime="******** 07:38:21.921"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${PREVIOUS_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:38:21.922" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[1]'.</msg>
<status status="PASS" starttime="******** 07:38:21.922" endtime="******** 07:38:22.019"/>
</kw>
<kw name="Subtract Time From Date" library="DateTime">
<var>${get_previous_month}</var>
<arg>${current}</arg>
<arg>31 days</arg>
<doc>Subtracts time from date and returns the resulting date.</doc>
<msg timestamp="******** 07:38:22.019" level="INFO">${get_previous_month} = 2024-04-29 07:38:21.000</msg>
<status status="PASS" starttime="******** 07:38:22.019" endtime="******** 07:38:22.019"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${PREVIOUS_MONTH_DATE}</var>
<arg>${get_previous_month}</arg>
<arg>result_format=%B %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:38:22.020" level="INFO">${PREVIOUS_MONTH_DATE} = April 2024</msg>
<status status="PASS" starttime="******** 07:38:22.019" endtime="******** 07:38:22.020"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>PREVIOUS MONTH ${PREVIOUS_MONTH_DATE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:22.020" endtime="******** 07:38:22.020"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>${PREVIOUS_MONTH_DATE}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 07:38:22.020" endtime="******** 07:38:22.034"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:38:27.035" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:38:22.034" endtime="******** 07:38:27.035"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:38:27.035" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[2]'.</msg>
<status status="PASS" starttime="******** 07:38:27.035" endtime="******** 07:38:27.193"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:38:27.193" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[2]'.</msg>
<status status="PASS" starttime="******** 07:38:27.193" endtime="******** 07:38:27.326"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${get_next_month}</var>
<arg>${current}</arg>
<arg>31 days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="******** 07:38:27.326" level="INFO">${get_next_month} = 2024-06-30 07:38:21.000</msg>
<status status="PASS" starttime="******** 07:38:27.326" endtime="******** 07:38:27.326"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${NEXT_MONTH_DATE}</var>
<arg>${get_next_month}</arg>
<arg>result_format=%B %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 07:38:27.326" level="INFO">${NEXT_MONTH_DATE} = June 2024</msg>
<status status="PASS" starttime="******** 07:38:27.326" endtime="******** 07:38:27.326"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>NEXT MONTH ${NEXT_MONTH_DATE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:27.326" endtime="******** 07:38:27.326"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>${NEXT_MONTH_DATE}</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 07:38:27.326" endtime="******** 07:38:27.343"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>validateUserCanNavigate.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:38:27.468" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="validateUserCanNavigate.png"&gt;&lt;img src="validateUserCanNavigate.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:38:27.343" endtime="******** 07:38:27.468"/>
</kw>
<status status="PASS" starttime="******** 07:38:11.867" endtime="******** 07:38:27.468"/>
</kw>
<status status="PASS" starttime="******** 07:38:11.867" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.468"/>
</kw>
<status status="PASS" starttime="******** 07:38:11.867" endtime="******** 07:38:27.468"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.488"/>
</kw>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.488"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:38:27.488" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 07:38:27.631" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-29.png"&gt;&lt;img src="selenium-screenshot-29.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 07:38:27.631" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 07:38:27.488" endtime="******** 07:38:27.787"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:38:27.787" endtime="******** 07:38:27.787"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:38:27.787" endtime="******** 07:38:27.787"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 07:38:27.787" endtime="******** 07:38:27.787"/>
</kw>
<status status="FAIL" starttime="******** 07:38:27.488" endtime="******** 07:38:27.787"/>
</kw>
<status status="PASS" starttime="******** 07:38:27.488" endtime="******** 07:38:27.787"/>
</kw>
<status status="PASS" starttime="******** 07:38:27.468" endtime="******** 07:38:27.787"/>
</kw>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:38:27.787"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 07:37:07.998" endtime="******** 07:38:27.789"/>
</test>
<test id="s1-t2" name="BU- Calendar View- Preview" line="44">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057349</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>user can view a campaign</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 07:38:32.661" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 07:38:32.661" endtime="******** 07:38:32.661"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:38:32.663" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057349'.</msg>
<status status="PASS" starttime="******** 07:38:32.661" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:38:32.663" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:38:32.663" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:38:32.663" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:32.663"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 07:38:33.252" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 07:38:34.482" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 07:38:34.482" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 12784 has been terminated.
SUCCESS: The process "chrome.exe" with PID 15668 has been terminated.
SUCCESS: The process "chrome.exe" with PID 30752 has been te...</msg>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:34.482"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:38:34.482" endtime="******** 07:38:34.482"/>
</kw>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:34.482"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:38:34.482" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000024AD39E26F0&gt;</msg>
<status status="PASS" starttime="******** 07:38:34.482" endtime="******** 07:38:34.482"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 07:38:34.483" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 07:38:34.483" endtime="******** 07:38:34.483"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:38:34.483" endtime="******** 07:38:34.483"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:38:34.483" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 07:38:34.483" endtime="******** 07:38:34.483"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:38:34.483" endtime="******** 07:38:34.483"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:38:34.484" endtime="******** 07:38:34.484"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:38:34.484" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 07:38:34.484" endtime="******** 07:38:34.484"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 07:38:34.484" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 07:38:34.484" endtime="******** 07:38:41.942"/>
</kw>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:38:41.942"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 07:38:41.944" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 07:38:41.944" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 07:38:41.942" endtime="******** 07:38:41.944"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 07:38:41.944" endtime="******** 07:38:41.953"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 07:38:41.956" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 07:38:41.955" endtime="******** 07:38:42.586"/>
</kw>
<status status="PASS" starttime="******** 07:38:41.953" endtime="******** 07:38:42.587"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:38:52.589" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:38:42.587" endtime="******** 07:38:52.589"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:38:52.589" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:38:52.602" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 07:38:52.589" endtime="******** 07:38:52.602"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:38:52.602" endtime="******** 07:38:52.602"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:39:12.609" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 07:38:52.602" endtime="******** 07:39:12.609"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:39:12.609" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:39:13.483" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 07:39:12.609" endtime="******** 07:39:13.483"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:39:13.483" endtime="******** 07:39:13.483"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:39:23.484" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:39:13.483" endtime="******** 07:39:23.484"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:39:23.484" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:39:23.505" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 07:39:23.484" endtime="******** 07:39:23.505"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:39:23.505" endtime="******** 07:39:23.505"/>
</kw>
<status status="PASS" starttime="******** 07:38:41.942" endtime="******** 07:39:23.505"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:39:23.621" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-30.png"&gt;&lt;img src="selenium-screenshot-30.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:39:23.505" endtime="******** 07:39:23.621"/>
</kw>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:39:23.621"/>
</kw>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:39:23.621"/>
</kw>
<status status="PASS" starttime="******** 07:38:32.663" endtime="******** 07:39:23.621"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:39:28.622" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:39:23.621" endtime="******** 07:39:28.622"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:39:28.622" endtime="******** 07:39:28.622"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:39:28.622" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 07:39:28.622" endtime="******** 07:39:28.700"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 07:39:28.701" endtime="******** 07:39:28.739"/>
</kw>
<status status="PASS" starttime="******** 07:39:28.622" endtime="******** 07:39:28.739"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:39:28.741" endtime="******** 07:39:28.741"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="validate that the user can view a campaign" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>---------------------------- user views details inside the calendar</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:39:28.741" endtime="******** 07:39:28.742"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:39:33.743" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:39:28.742" endtime="******** 07:39:33.743"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${CAMPAIGN_ID_DESCRIPTION}</var>
<arg>${CAMPAIGN_ID_DESCRIPTION_xpath}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 07:39:33.881" level="INFO">${CAMPAIGN_ID_DESCRIPTION} = May 2024
month
Sun
Mon
Tue
Wed
Thu
Fri
Sat
28
29
30
1
2
3
4
5
6
7
8
9
10
11
Campaign History Test3 ( SSK )
12
Campaign History Test3 ( SSK )
13
Edit Camp ( ATM )
14
May 13th ( ATM )
15
16
Reverse camp...</msg>
<status status="PASS" starttime="******** 07:39:33.743" endtime="******** 07:39:33.881"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>-----------------</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:39:33.881" endtime="******** 07:39:33.881"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${CAMPAIGN_ID_DESCRIPTION}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:39:33.881" endtime="******** 07:39:33.881"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:39:38.881" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:39:33.881" endtime="******** 07:39:38.881"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAMPAIGN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:39:38.884" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[2]/td[5]/div/div[2]/div[4]/a'.</msg>
<msg timestamp="******** 07:39:39.053" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-31.png"&gt;&lt;img src="selenium-screenshot-31.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 07:39:39.053" level="FAIL">Element with locator 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[2]/td[5]/div/div[2]/div[4]/a' not found.</msg>
<status status="FAIL" starttime="******** 07:39:38.881" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>15s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_BY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${RECEIVE_DEVICE_TYPE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_WITH_CAMPAIGN_DATE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${REVIEW_MARKETING_SCREENS}</arg>
<arg>5</arg>
<arg>Review Marketing Screens</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${LANGUAGE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${DURATION}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.053" endtime="******** 07:39:39.053"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${PRIORITY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${BACK_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN_BY}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${RECEIVE_DEVICE_TYPE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${CALENDAR_WITH_CAMPAIGN_DATE}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CLOSE_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<arg>${FULL_CALENDAR}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>validateUserCanViewCampaign.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<status status="FAIL" starttime="******** 07:39:28.741" endtime="******** 07:39:39.061"/>
</kw>
<status status="FAIL" starttime="******** 07:39:28.741" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<status status="FAIL" starttime="******** 07:39:28.739" endtime="******** 07:39:39.061"/>
</kw>
<kw name="And User logs out" library="Logout">
<status status="NOT RUN" starttime="******** 07:39:39.061" endtime="******** 07:39:39.061"/>
</kw>
<status status="FAIL" starttime="******** 07:38:32.661" endtime="******** 07:39:39.061"/>
</kw>
<doc>Testing future fit</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="FAIL" starttime="******** 07:38:32.661" endtime="******** 07:39:39.061">Element with locator 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[2]/td[5]/div/div[2]/div[4]/a' not found.</status>
</test>
<test id="s1-t3" name="BA- Calendar view- Preview- Next, Back &amp; Close Buttons" line="47">
<kw name="Validates Calendar View Page">
<arg>Testing future fit</arg>
<arg>T155057374</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_UAT</arg>
<arg>user can view a campaign</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 07:39:39.332" level="INFO">Set test documentation to:
Testing future fit</msg>
<status status="PASS" starttime="******** 07:39:39.331" endtime="******** 07:39:39.332"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:39:39.332" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057374'.</msg>
<status status="PASS" starttime="******** 07:39:39.332" endtime="******** 07:39:39.332"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:39:39.332" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 07:39:39.332" endtime="******** 07:39:39.332"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:39:39.332" endtime="******** 07:39:39.332"/>
</kw>
