*** Settings ***
# Author Name               : Thabo
# Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/BinTypeUpdate_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot



*** Variables ***
${SUITE NAME}               BIN Tables - Update Bin Type by ID
${TEST_CASE_ID}             RAC29a-TC-1348


*** Keywords ***
Update Bin Type and Verify Response
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}    ${bintypeid}     ${name}    ${description}   ${EXPECTED_STATUS_CODE}


    IF    '${bintypeid}' == '${EMPTY}'
         ${bintypeid}=    Get the Bin Type id using Bin Type Name  ${name}
    END

    ${description}=     Generate random word

    Set Global Variable    ${GLOBAL_BIN_TYPE_ID}    ${bintypeid}
    Set Global Variable    ${GLOBAL_BIN_TYPE_NAME}    ${name}
    Set Global Variable    ${GLOBAL_BIN_TYPE_DESCRIPTION}    ${description}

    Given The User Populates the Update Bin Type JSON payload with   ${binTypeId}     ${name}    ${description}
    When The User sends the Update Bin Type API Request                ${BASE_URL}
    And The service returns an expected status code                 ${EXPECTED_STATUS_CODE}
    Then The updated Bin Type must exist in the database            

| *** Test Cases ***                                                                                                                 |             *DOCUMENTATION*           |    *BASE_URL*            | *bintypeid*       | *name*    | *description*   |    *EXPECTED_STATUS_CODE*    |
| Verify the Update API successfully edits a Bin Type (Bin Type Edit/Delete Menu)            | Update Bin Type and Verify Response   | Update Bin Type and verify response   |                          |  ${EMPTY}         |  Token    |    String2       |      200                    |