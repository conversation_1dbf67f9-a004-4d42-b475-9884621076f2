*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Bin Tables Add Bin Type Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py
Library                                            ../../keywords/controllers/resources/bintypes/AddBinType.py

#***********************************PROJECT RESOURCES***************************************
Resource                                             ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***
${SQL_GET_ADDED_BIN_TYPE_DETAILS}                    SELECT * FROM BinDbs.BinTypes order by CreatedDate desc

*** Keywords ***
The User Populates the Add Bin Type JSON payload with
    [Arguments]     ${BIN_TYPE_NAME}     ${BIN_TYPE_DESCRIPTION}



    ${json_payload}=    Create a Bin Type Request   ${BIN_TYPE_NAME}     ${BIN_TYPE_DESCRIPTION}

    ${json_payload_is_created}=    Run Keyword And Return Status     Should Not Be Empty    ${json_payload}

    Run Keyword If    ${json_payload_is_created} == ${False}
    ...    Fail     The JSON payload for upload bin was not created!

    #Save the payload in a Global Variable
    #Set Global Variable    ${REST_PAYLOAD}        ${json_payload}

    Log Many    ${json_payload}


Create a Bin Type Request
    [Arguments]    ${BIN_TYPE_NAME}     ${BIN_TYPE_DESCRIPTION}

    ${json_result} =    Create Add Bin Type Request     ${BIN_TYPE_NAME}     ${BIN_TYPE_DESCRIPTION}

    Log Many        ${json_result}
    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal_BinTables/data/AddBinType.json
    Write JSON data To File    ${JSON_FILE}    ${json_result}

    RETURN    ${json_result}



The User sends the Add Bin Type API Request
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
    Log     ${base_url}


    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/AddBinType.json

    Log Many   ${payload}
    ${endpoint}=        Get Endpoint    ${base_url}
    ${method}=          Set Variable   POST
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}
    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}     payload=${payload}

    Log Many    ${response}
    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    #Create an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'AddBinType' REST API did not return the expected status of '${EXPECTED_STATUS_CODE}', the returned status is '${status_code}'

The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}
    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
    IF    "${api_error_message_detail}" != "None"
         ${v2}=      Remove Quotes    ${api_error_message_detail}
    ELSE
         ${v2}=      Set Variable     ${api_error_message_detail}
    END


    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${v1}'     '${v2}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       BinNumber   binNumber   actionDate  binTypeIds  binUploadRequests

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'

             #Remove quotes from the strings
             ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
             IF    '${api_error_message_fields}' != 'None'
                 ${v2}=      Remove Quotes    ${api_error_message_fields}
             ELSE
                 ${v2}=      Set Variable     ${api_error_message_fields}
             END


             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${v1}' in '${v2}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'
    ...    Fail    The 'bintypes/add' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.
    ...  ELSE
    ...    Log Many    The 'bintypes/add' REST API call returned the expected message which is '${EXPECTED_ERROR_MESSAGE}'.


Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}


Get Response Status Code
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

Get Created Bin Type's ID
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_created_bin_type_id
    RETURN    ${result}

Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keywords to read error response fields




#The below keywords are for Database Verifications

The created Bin Type must exist in the database
    [Arguments]     ${BIN_TYPE_NAME}     ${BIN_TYPE_DESCRIPTION}

    ${created_bin_type_id}=     Get Created Bin Type's ID
    Log Many    Created Bin Type Id is: '${created_bin_type_id}'

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...     Fail    Please provide the Bin Type Name for the Bin Type that must be verified.


    ${bin_type_description_provided}=        Set Variable If
         ...       '${BIN_TYPE_DESCRIPTION}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' == ''             ${False}
         ...       '${BIN_TYPE_DESCRIPTION}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_DESCRIPTION}' != ''             ${True}


    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_TO_ADD}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no bin type callled '${BIN_TYPE_TO_ADD}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_id}=                        Get Column Data By Name       ${first_row_results}       Id
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_desc}=                        Get Column Data By Name       ${first_row_results}       Description
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    #Verify that the Bin Type details are correct
    Run Keyword If    '${db_bin_type_name}' == '${BIN_TYPE_NAME}'
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is the same as the provided Bin Type name.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The bin type name: '${db_bin_type_name}' from the DB is not the same as the provided Bin Type. The provided Bin Type name is '${BIN_TYPE_NAME}'.

    IF    ${bin_type_description_provided}
        Run Keyword If    '${db_bin_type_desc}' == '${BIN_TYPE_DESCRIPTION}'
        ...    Log Many  The bin type description: '${db_bin_type_desc}' from the DB is the same as the provided Bin Type description.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail  The bin type description: '${db_bin_type_desc}' from the DB is not the same as the provided Bin Type description. The provided Bin Type description is '${BIN_TYPE_DESCRIPTION}'.
    END

    Run Keyword If    '${db_bin_type_id}' == '${created_bin_type_id}'
    ...    Log Many  The bin type id: '${db_bin_type_id}' from the DB is the same as the created Bin Type's ID.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The bin type ID: '${db_bin_type_id}' from the DB is not the same as the created Bin Type's ID. The created Bin Type ID is '${created_bin_type_id}'.

    Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is active.
    ...  ELSE
    ...    Fail  The bin type name: '${db_bin_type_name}' from the DB is not active.
