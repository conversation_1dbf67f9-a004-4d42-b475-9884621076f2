*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-1198



*** Keywords ***
Verifying the error message displayed when a bin is not found on delete search
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${INVALID_BIN_NUMBER}    ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates an invalid Bin Number        ${INVALID_BIN_NUMBER}    
    Then The user verifies that the 'Bin not found' error message is displayed    ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                                                           |        *DOCUMENTATION*         |         *BASE_URL*             |         *INVALID_BIN_NUMBER*        |     *EXPECTED_ERROR_MESSAGE*    |
| Admin_Error Message Displayed When BIN Number is Not Found  | Verifying the error message displayed when a bin is not found on delete search |       Error Message Validation |           ${EMPTY}             |          09                         |      Bin number not found!      |
