*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK  REASSIGN
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Re-assign complaint to another consultant

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/qrcode_cc/LandingPage.robot   
Resource                                            ../keywords/qrcode_cc/AssignATMComplaints.robot 
Resource                                            ../keywords/qrcode_cc/ViewATMComplaintsAndCompliments.robot
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
#ALL FIELDS ARE EMPTY FOR ASSIGN COMPLAINT FUNCTIONALITY
${NEW_STATUS}    
${REASON_VALUE}  
${COMMENT_VALUE}  
${REFFERENCE_NUMBER}

*** Keyword ***
Re assign complaints
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${FUNCTION}  ${Tag}  ${ASSIGN_TO_USER}  ${REFERNCE_NO} 
    Set Test Documentation  ${DOCUMENTATION}

    Log to console  Function: ${FUNCTION} Assigned to user: ${ASSIGN_TO_USER} 

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe

    And The user clicks navigate to QR Code Complaints and Compliments screen link

    # And User searches for a compliant or task  ${REFERNCE_NO}

    # Sleep  10s
    
    # #DETERMINE TEST CASE TO EXECUTE
    # Run Keyword If  '${FUNCTION}'=='RE-ASSIGN'  User re-assigns a compliant  ${ASSIGN_TO_USER}
    
    #  #Search for the updated complaint
    # And User searches for a compliant or task  ${REFERNCE_NO}
    
    # And User views complaint/compliment details  ${COMMENT_VALUE}  ${ASSIGN_TO_USER}  ${NEW_STATUS}  ${REFFERENCE_NUMBER}  

    And User logs out

| *Test Case*                                                          |                                  *DOCUMENTATION*                                                          |  *TESTRAIL_TESTCASE_ID*  |    *FUNCTION*     |    *Tag*         |      *ASSIGN_TO_USER*           | *REFERNCE_NO*|
| ATM CC Re-assigned to another consultant   | Re assign complaints           |             ATM CC Re-assigned to another consultant                                               |      101597678            |     RE-ASSIGN     | Update status    |  ab013op - Libertine Makinta    |  ACR0000346  |      
 