*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/DeleteBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete a Bin




*** Keywords ***
Delete Bin Controller negative tests
    [Arguments]        ${BASE_URL}   ${DOCUMENTATION}   ${BIN_ID}    ${EXPECTED_STATUS_CODE}    ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User executes the Delete Bin API Request    ${BASE_URL}    ${BIN_ID}
    When The Delete Bin controller returns an expected status code     ${EXPECTED_STATUS_CODE}
    Then The expected Error must be displayed     ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                           |        *BASE_URL*       |          *DOCUMENTATION*           |          *BIN_ID*       |        *EXPECTED_STATUS_CODE*  |             *EXPECTED_ERROR_MESSAGE*               |
| Delete a bin without providing the 'binid' data.   | Delete Bin Controller negative tests    |                         |    Delete bin using invalid data   |                         |              404               |        Bin Id must not be null or empty.           |
