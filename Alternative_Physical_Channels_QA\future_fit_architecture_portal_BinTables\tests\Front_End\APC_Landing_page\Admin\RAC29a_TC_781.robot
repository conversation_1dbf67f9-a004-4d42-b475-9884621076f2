*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../keywords/front_end/Bin_Table_Landing_Page.robot
Resource                                            ../../../../keywords/front_end/APC_Portal_Landing_Page.robot


*** Variables ***
${SUITE NAME}               APC Landing Page 
${TEST_CASE_ID}             RAC29a-TC-781




*** Keywords ***
Verify Welcome Message Displays Correctly with User's Name Based on Access
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture                ${BASE_URL}
    When The user is redirected to the APC Portal Landing page
    Then The user verifies the Welcome Message displayed 


| *** Test Cases ***                                                                                                                                                     |        *DOCUMENTATION*     |         *BASE_URL*         |         
| Admin_Verify Welcome Message Displays Correctly with User's Name Based on Access     | Verify Welcome Message Displays Correctly with User's Name Based on Access   | Login Redirection          |           ${EMPTY}         |           
