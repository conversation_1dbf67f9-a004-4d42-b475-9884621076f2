*** Settings ***
#Author Name               : T<PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS - Validate Rows Per Page Filter
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${ROWS_TO_SELECT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And The user filters the number of users to be displayed using rows per page filter   ${ROWS_TO_SELECT}
    Then The number of rows returned on the page must be the same as the number that was used for filtering   ${ROWS_TO_SELECT}



| *Test Case*                                                                                                      |   *DOCUMENTATION*                                  | *TEST_ENVIRONMENT*  |  *ROWS_TO_SELECT*    |
# | Validate Rows per Page filter- User Management, Filter the number of users to be displayed to be 1 user per page.          | VMS - Validate Rows Per Page Filter | Filter nummber of rows to be displayed per page    |    VMS_UAT          |        1             |
# | Validate Rows per Page filter- User Management, Filter the number of users to be displayed to be 5 users per page.         | VMS - Validate Rows Per Page Filter | Filter nummber of rows to be displayed per page    |    VMS_UAT          |        5             |
| Validate Rows per Page filter- User Management, Filter the number of users to be displayed to be 10 users per page.         | VMS - Validate Rows Per Page Filter | Filter nummber of rows to be displayed per page    |    VMS_UAT          |        10            |
# | Validate Rows per Page filter- User Management, Filter the number of users to be displayed to be 15 users per page.        | VMS - Validate Rows Per Page Filter | Filter nummber of rows to be displayed per page    |    VMS_UAT          |        15            |