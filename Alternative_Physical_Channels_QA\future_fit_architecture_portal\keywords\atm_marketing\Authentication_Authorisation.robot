*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Test the Calendar view page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             XML

#***********************************PROJECT RESOURCES***************************************
Resource                                        ../common/Navigation.robot

*** Variables ***
${CALENDAR_VIEW_LINK}                               xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/mat-list-item[2]
${CAMPAIGN_VALENDAR_TABLE}                          css=body > app-root > app-sidenav > mat-sidenav-container > mat-sidenav-content > app-marketing-logs-view > full-calendar > div.fc-header-toolbar.fc-toolbar.fc-toolbar-ltr

${SEARCH_FIELD_INPUT}                               id~searchField
${DETAILS_BTN}                                      id=btnDetails
${UPDATE2_BTN}                                      id=btnUpdate
${CLOSE_POPUP_BTN}                                  xpath=//button[@class='close pull-right']

${REFRESH_BTN}                                      id=refresh
${ATM_MARKETING_DASHBOARD}                          xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/mat-list-item[1]/span
${INSTALLED_SCHEDULE_VERSION}                       xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[2]/div[2]/mat-card/mat-card-subtitle

${CAMPAIGN_ON_THE_DASHBOARD}                        xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[1]/mat-card/mat-card-content/p/mat-card-subtitle
${ATMS_LATEST_SCHEDULE}                             xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[2]/mat-card/mat-card-content/p/mat-card-subtitle
${FAILED_UPLOAD_SCHEDULES}                          xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[3]/mat-card/mat-card-content/p/mat-card-subtitle
${DEVICES_ATM}                                      xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[4]/mat-card/mat-card-content/p/mat-card-subtitle
${CURRENT_VERSION}                                  xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-atm-dashboard/div/div[1]/div[5]/mat-card/mat-card-content/p/mat-card-subtitle
${ATMS_WITH_INSTALLED_VERSIONS_BY_REGION}           aria-label=//*[@aria-label="ATMs With Installed Versions By Region"]

${CAMPAIGN_NAME}                                    name=campaignName
${CAMPAIGN_BY}                                      name=campaignBy
${MARKETING_TYPE}                                   name=marketingType
${RECEIVE_DEVICE_TYPE}                              name=receiverDeviceType
${SCREEN_NAME}                                      name=screenNumber
${CAMPAIGN}                                         xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[1]/td[4]/div/div[2]/div[1]/a/div/i
${CAMPAIGN_ID_DESCRIPTION_xpath}                    xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[3]/td[5]/div/div[2]/div[2]/a/div/i
${NEXT_BUTTON}                                      xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[1]/div[2]/button
${CLOSE_BUTTON}                                     xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-dialog-actions/button
${CALENDAR_WITH_CAMPAIGN_DATE}                      id=testing
${LANGUAGE}                                         xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer/div[1]
${DURATION}                                         xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer/div[2]
${PRIORITY}                                         xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-content/mat-grid-list/div/mat-grid-tile/div/div/mat-card/mat-card-footer/div[3]
${REVIEW_MARKETING_SCREENS}                         xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/mat-card/mat-card-title
${BACK_BUTTON}                                      xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[2]/div[2]/div/button
${FULL_CALENDAR}                                    xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar
${ADD_NEW_CAMPAIGN_BUTTON}                          xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/div/div/div[2]/div/button
${FILL_OUT_CAMPAIGN_TARGETED}                       xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[1]/mat-step-header[1]/div[3]/div
${CAMPAIGN_PREVIEW}                                 xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[4]/span
${CAMPAIGN_PREVIEW_SCREEN}                          xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-preview-screen/div[2]/mat-card/mat-dialog-content/mat-horizontal-stepper/div/div[1]/mat-step-header[1]/div[3]/div
${EXPAND_MENU}                                      xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-list-item[1]

*** Keywords ***
The user validate that the user has access to view data in the dashboard screen
    Log to console    -----------------------------The user views the dashboard
    Sleep    10s
    Click Element    ${ATM_MARKETING_DASHBOARD}

    Wait Until Element Is Visible  ${INSTALLED_SCHEDULE_VERSION}     8     Installed Schedule Version

    Page Should Contain Element                     ${CAMPAIGN_ON_THE_DASHBOARD} 
    Page Should Contain Element                     ${ATMS_LATEST_SCHEDULE} 
    Page Should Contain Element                     ${FAILED_UPLOAD_SCHEDULES}  
    Page Should Contain Element                     ${DEVICES_ATM} 
    Page Should Contain Element                     ${CURRENT_VERSION} 

    
    Capture page screenshot  DashboardScreen.png

The user validate that the user has access to View data and details in the Calendar View screen
    Log To Console    ---------------------------- user views details inside the calendar
    Sleep    55s

   # ${CAMPAIGN_ID_DESCRIPTION} =    Get Text    ${CAMPAIGN_ID_DESCRIPTION_xpath}
    Log to console    -----------------  
   # Log to console      ${CAMPAIGN_ID_DESCRIPTION} 
    
    Click Element    ${CAMPAIGN}

    Sleep    15s

    Page Should Contain Element                     ${CAMPAIGN_NAME} 
    Page Should Contain Element                     ${CAMPAIGN_BY} 
    Page Should Contain Element                     ${MARKETING_TYPE}
    Page Should Contain Element                     ${RECEIVE_DEVICE_TYPE}  
    Page Should Contain Element                     ${SCREEN_NAME}
    Page Should Contain Element                     ${NEXT_BUTTON} 
    Page Should Contain Element                     ${CLOSE_BUTTON}
    Page Should Contain Element                     ${CALENDAR_WITH_CAMPAIGN_DATE}

    Click Element    ${NEXT_BUTTON} 

    Sleep    5s

    Wait Until Element Is Visible  ${REVIEW_MARKETING_SCREENS}     5    Review Marketing Screens 

    Page Should Contain Element                     ${LANGUAGE}
    Page Should Contain Element                     ${DURATION} 
    Page Should Contain Element                     ${PRIORITY}
    
    Click Element    ${BACK_BUTTON}  

    Page Should Contain Element                     ${CAMPAIGN_NAME} 
    Page Should Contain Element                     ${CAMPAIGN_BY} 
    Page Should Contain Element                     ${MARKETING_TYPE}
    Page Should Contain Element                     ${RECEIVE_DEVICE_TYPE}  
    Page Should Contain Element                     ${SCREEN_NAME}
    Page Should Contain Element                     ${NEXT_BUTTON} 
    Page Should Contain Element                     ${CLOSE_BUTTON}
    Page Should Contain Element                     ${CALENDAR_WITH_CAMPAIGN_DATE}

    Click Element    ${CLOSE_BUTTON}  

    Sleep    5s
    
    Page Should Contain Element                     ${FULL_CALENDAR}


    Capture page screenshot  CalendarViewScreen.png

The user validate that the user has access to Add New Complaint in the Calendar View screen

    Log To Console    ---------------------------- The user validate that the user has access to Add New Complaint in the Calendar View screen
    
    Sleep    5s
    Click Element    ${ADD_NEW_CAMPAIGN_BUTTON}

    Sleep    5s

    Page Should Contain Element                     ${FILL_OUT_CAMPAIGN_TARGETED}

Validate that the user has access to Preview Approval in the Campaign Preview screen

    Log To Console    ----------------------------Validate that the user has access to Preview Approval in the Campaign Preview screen

    Click Element    ${CAMPAIGN_PREVIEW}   

    Sleep    5s

    Page Should Contain Element                    ${CAMPAIGN_PREVIEW_SCREEN}

    Capture page screenshot  CampaignPreviewScreen.png

Validate that the user can access the Calendar View 
    Click Element    xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span
    Page Should Contain   Greyed out Campaigns needs to be approved*

Validate that the user can access the Dashboard
    Click Element    xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[1]/span
    Page Should Contain    Installed Schedule Version

Validate that the user can access approvals under admin 
    Click Element    xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/mat-expansion-panel-header/span[1]
    Sleep    2s
    Click Element    xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/div/div/mat-nav-list/mat-list-item/span/span[3]
    Sleep    3s
    Page Should Contain    Approved By

Validate that the user can access CAPTURE campaign link  
    Click Element    ${CAPTURE_CAMPAIGN_LINK}
    Sleep    3s
    Page Should Contain    CAPTURE CAMPAIGN

Validate the test cases
    [Arguments]  ${Test_cases}

    Run Keyword If    '${Test_cases}' == "Login- BA- Calendar"    Validate that the user can access the Calendar View
    Run Keyword If    '${Test_cases}' == "Login- BA- Dashboard"    Validate that the user can access the Dashboard
    Run Keyword If    '${Test_cases}' == "Login- BA- Approval"    Validate that the user can access approvals under admin
    Run Keyword If    '${Test_cases}' == "Login- BU- Capture"    Validate that the user can access CAPTURE campaign link 