<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T11:56:18.088211" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_125_Preview_Campaign_Next_Button.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.173747" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:56:19.173747" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.174765" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:56:19.174765" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.174765" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:56:19.174765" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.174765" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:56:19.174765" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.174765" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T11:56:19.174765" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:19.173747" elapsed="0.002008"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_125_FFT_Preview_Campaign_Next_Button" line="36">
<kw name="Validating the Next Button on Approval Page preview">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.175755" level="INFO">Set test documentation to:
Next Button on Campaign Approvals Preview</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T11:56:19.175755" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.281317" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:56:19.176884" elapsed="0.104433"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:56:19.281317" elapsed="0.001006"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:56:19.282323" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.284325" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:56:19.284325" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T11:56:19.284325" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:56:19.284325" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T11:56:19.284325" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.284325" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T11:56:19.284325" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:56:19.285322" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.318367" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T11:56:19.650644" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T11:56:19.650644" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T11:56:19.285322" elapsed="0.365322"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.651644" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:56:19.651644" elapsed="0.001005"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:56:19.651644" elapsed="0.001005"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T11:56:19.284325" elapsed="0.368324"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.652649" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T11:56:19.652649" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T11:56:19.652649" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T11:56:19.652649" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T11:56:19.652649" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T11:56:19.652649" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T11:56:19.653644" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T11:56:19.653644" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T11:56:19.653644" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T11:56:19.654149" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:56:19.653644" elapsed="0.000505"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T11:56:19.654149" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:56:19.654149" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T11:56:19.653644" elapsed="0.000505"/>
</if>
<status status="NOT RUN" start="2024-10-29T11:56:19.653644" elapsed="0.000505"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.654149" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:56:19.654149" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.654149" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001B0899D4DA0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:56:19.654149" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T11:56:19.654149" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T11:56:19.655155" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T11:56:19.654149" elapsed="0.001006"/>
</branch>
<status status="PASS" start="2024-10-29T11:56:19.654149" elapsed="0.001006"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.655155" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T11:56:19.655155" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:56:19.655155" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T11:56:19.655155" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:56:19.655155" elapsed="0.000999"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:56:19.656154" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:56:19.656154" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:56:19.656154" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T11:56:19.656154" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.657155" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:56:19.656154" elapsed="0.001001"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T11:56:19.657155" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T11:56:19.176884" elapsed="33.483985"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:56:52.660869" elapsed="0.017998"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:52.685079" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="cfe388be18c95bd579d819944120fb86", element="f.459A00AEA2E41A68F95814DE35F91D74.d.A109A2DF3246F183D0F97E597C9FE2F6.e.152")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:56:52.678867" elapsed="0.006212"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:52.685079" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="cfe388be18c95bd579d819944120fb86", element="f.459A00AEA2E41A68F95814DE35F91D74.d.A109A2DF3246F183D0F97E597C9FE2F6.e.152")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:56:52.685079" elapsed="0.029880"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:56:52.714959" elapsed="0.007487"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:52.714959" elapsed="0.007487"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:52.728940" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="cfe388be18c95bd579d819944120fb86", element="f.459A00AEA2E41A68F95814DE35F91D74.d.A109A2DF3246F183D0F97E597C9FE2F6.e.153")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:56:52.723444" elapsed="0.005496"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:56:57.730616" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:56:52.729937" elapsed="5.000679"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T11:56:57.730616" elapsed="0.019445"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:57.750061" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="cfe388be18c95bd579d819944120fb86", element="f.459A00AEA2E41A68F95814DE35F91D74.d.A109A2DF3246F183D0F97E597C9FE2F6.e.153")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:56:57.750061" elapsed="0.073293"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:56:57.823354" elapsed="0.446034"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:57.823354" elapsed="0.446034"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:56:58.269388" elapsed="0.004999"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:58.269388" elapsed="0.004999"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:58.283715" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T11:56:58.274387" elapsed="0.009328"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:52.660869" elapsed="5.623854"/>
</kw>
<kw name="Then The user previews and validates the Next Button on Approval Preview" owner="Approvals">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:56:58.284723" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<arg>True</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:56:58.284723" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:56:58.284723" level="INFO">${TOTAL_CAMPAIGNS} = 0</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:56:58.284723" elapsed="0.000000"/>
</kw>
<while condition="${more_pages} == True">
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:58.291722" level="INFO">${campaigns} = 10</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:56:58.284723" elapsed="0.006999"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:56:58.292740" level="INFO">${TOTAL_CAMPAIGNS} = 10</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:56:58.291722" elapsed="0.001018"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:56:58.292740" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:56:58.292740" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:56:58.292740" level="INFO">Current page campaigns: 10 | Total so far: 10</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:56:58.292740" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:56:58.293762" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:56:58.292740" elapsed="0.067044"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:00.614115" level="INFO">Suppressing StaleElementReferenceException from Selenium.</msg>
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:56:58.360789" elapsed="2.459531"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:58.359784" elapsed="2.460536"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:57:00.820320" elapsed="0.016542"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:00.836862" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:00.836862" elapsed="0.048386"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----NEXT BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:57:00.885248" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:02.885394" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:00.885248" elapsed="2.000146"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:02.900961" level="INFO">Current page contains text 'Language:'.</msg>
<arg>Language:</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T11:57:02.885958" elapsed="0.015003"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:57:02.900961" elapsed="0.005366"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:02.900961" elapsed="0.005366"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:02.906327" elapsed="0.010605"/>
</kw>
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T11:57:02.916932" elapsed="0.013061"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:02.929993" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:02.929993" elapsed="0.038708"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:04.968980" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:02.968701" elapsed="2.000279"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:57:04.968980" elapsed="0.283189"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:05.252169" elapsed="0.016478"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:05.268647" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:05.268647" elapsed="0.047177"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-29T11:56:58.292740" elapsed="7.023084"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-29T11:56:58.292740" elapsed="7.023084"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:05.316827" elapsed="0.014996"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:05.345019" level="INFO">${is_disabled} = None</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:05.331823" elapsed="0.013196"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:57:05.346038" level="INFO">${is_enabled} = True</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:57:05.345019" elapsed="0.001019"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:57:05.346038" level="INFO">Next page button enabled: True</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:57:05.346038" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-29T11:57:05.346038" elapsed="0.000000"/>
</return>
<msg time="2024-10-29T11:57:05.346038" level="INFO">${more_pages} = True</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-29T11:57:05.316827" elapsed="0.029211"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:05.346038" elapsed="0.012477"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:10.360038" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:05.358515" elapsed="5.001523"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:57:10.360038" elapsed="0.284002"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:57:10.644040" elapsed="0.263330"/>
</kw>
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:57:10.644040" elapsed="0.264338"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click Next Page Button" owner="Approvals">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:10.908378" level="INFO">Executing JavaScript:
document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()
Without any arguments.</msg>
<arg>document.evaluate("//*[contains(@class,'mat-paginator-container')]//button[2]", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue.click()</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2024-10-29T11:57:10.908378" elapsed="0.015107"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:10.908378" elapsed="0.015107"/>
</kw>
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:57:10.908378" elapsed="0.015107"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:12.923899" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:10.923485" elapsed="2.000414"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:58.284723" elapsed="14.639176"/>
</iter>
<iter>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:12.932528" level="INFO">${campaigns} = 6</msg>
<var>${campaigns}</var>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:12.924902" elapsed="0.007626"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:57:12.932528" level="INFO">${TOTAL_CAMPAIGNS} = 16</msg>
<var>${TOTAL_CAMPAIGNS}</var>
<arg>${TOTAL_CAMPAIGNS} + ${campaigns}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:57:12.932528" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:57:12.932528" level="INFO">${campaigns_to_loop} = 1</msg>
<var>${campaigns_to_loop}</var>
<arg>min(${campaigns}, 1)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:57:12.932528" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:57:12.932528" level="INFO">Current page campaigns: 6 | Total so far: 16</msg>
<arg>Current page campaigns: ${campaigns} | Total so far: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:57:12.932528" elapsed="0.000000"/>
</kw>
<for flavor="IN RANGE">
<iter>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:12.934054" level="INFO">Clicking element 'xpath=//*[contains(@class,'cdk-table')]//tbody//tr[1]//*[name()='svg' and @data-icon='eye']'.</msg>
<arg>xpath=//*[contains(@class,'cdk-table')]//tbody//tr[${i + 1}]//*[name()='svg' and @data-icon='eye']</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:12.934054" elapsed="0.119552"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:57:13.053606" elapsed="2.168452"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:13.053606" elapsed="2.168452"/>
</kw>
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'overlay')]</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:57:15.223087" elapsed="0.010954"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:15.234041" level="INFO">Clicking button 'xpath=//button[@type='submit']'.</msg>
<arg>xpath=//button[@type='submit']</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:15.234041" elapsed="0.045012"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----NEXT BUTTON VALIDATION SUCCESSFUL----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:57:15.279053" elapsed="0.001286"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:17.280763" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:15.280339" elapsed="2.000424"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:17.289943" level="INFO">Current page contains text 'Language:'.</msg>
<arg>Language:</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T11:57:17.280763" elapsed="0.009180"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:57:17.289943" elapsed="0.005008"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:17.289943" elapsed="0.005008"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:17.294951" elapsed="0.009496"/>
</kw>
<kw name="Element Should Be Enabled" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Verifies that element identified by ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T11:57:17.304447" elapsed="0.014059"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:17.318506" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-stepper-previous')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:17.318506" elapsed="0.039095"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:19.357611" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:17.357601" elapsed="2.000010"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//mat-dialog-container</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:57:19.357611" elapsed="0.280270"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:19.637881" elapsed="0.012144"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:19.650025" level="INFO">Clicking button 'xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]'.</msg>
<arg>xpath=//button[@type='button' and contains(@class, 'mat-focus-indicator approve mat-button mat-button-base')]</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:19.650025" elapsed="0.033252"/>
</kw>
<var name="${i}">0</var>
<status status="PASS" start="2024-10-29T11:57:12.934054" elapsed="6.749223"/>
</iter>
<var>${i}</var>
<value>${campaigns_to_loop}</value>
<status status="PASS" start="2024-10-29T11:57:12.934054" elapsed="6.750205"/>
</for>
<kw name="Check If Next Page Button Is Enabled" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:19.684259" elapsed="0.011016"/>
</kw>
<kw name="Get Element Attribute" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:19.710258" level="INFO">${is_disabled} = true</msg>
<var>${is_disabled}</var>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>disabled</arg>
<doc>Returns the value of ``attribute`` from the element ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:19.696280" elapsed="0.013978"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T11:57:19.710258" level="INFO">${is_enabled} = False</msg>
<var>${is_enabled}</var>
<arg>'${is_disabled}' == 'None'</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T11:57:19.710258" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:57:19.711273" level="INFO">Next page button enabled: False</msg>
<arg>Next page button enabled: ${is_enabled}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:57:19.711273" elapsed="0.000000"/>
</kw>
<return>
<value>${is_enabled}</value>
<status status="PASS" start="2024-10-29T11:57:19.711273" elapsed="0.000000"/>
</return>
<msg time="2024-10-29T11:57:19.711273" level="INFO">${more_pages} = False</msg>
<var>${more_pages}</var>
<doc>Returns True if the "Next" button is enabled.</doc>
<status status="PASS" start="2024-10-29T11:57:19.684259" elapsed="0.027014"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<arg>timeout=5s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T11:57:19.711273" elapsed="0.013355"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:24.726104" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:19.725611" elapsed="5.000493"/>
</kw>
<kw name="Scroll Element Into View" owner="SeleniumLibrary">
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" start="2024-10-29T11:57:24.726104" elapsed="0.267852"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Scroll Element Into View</arg>
<arg>xpath=//*[contains(@class,'mat-paginator-container')]//button[2]</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:57:24.995071" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${more_pages}</arg>
<arg>Click Next Page Button</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T11:57:24.995071" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:26.995543" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:24.995071" elapsed="2.001100"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:12.923899" elapsed="14.072272"/>
</iter>
<status status="PASS" start="2024-10-29T11:56:58.284723" elapsed="28.712571"/>
</while>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:57:26.998193" level="INFO">Total campaigns identified on the front-end Approval List: 16</msg>
<arg>Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:57:26.998193" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns identified on the front-end Approval List: ${TOTAL_CAMPAIGNS}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:57:26.999274" elapsed="0.001493"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:57:27.001246" level="INFO">${db_type} = 'MYSQL'</msg>
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:57:27.001171" elapsed="0.000075"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T11:57:27.001246" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY} = SELECT COUNT(*) FROM ATM_Marketing.Campaign WHERE isActive = '1';</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T11:57:27.001246" elapsed="0.000000"/>
</kw>
<kw name="Execute SQL Query" owner="DBUtility">
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-29T11:57:27.002371" level="INFO">${return_data} = True</msg>
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-29T11:57:27.002371" elapsed="0.000000"/>
</kw>
<kw name="Convert To Boolean" owner="BuiltIn">
<msg time="2024-10-29T11:57:27.003344" level="INFO">${return_all} = False</msg>
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<status status="PASS" start="2024-10-29T11:57:27.002371" elapsed="0.000973"/>
</kw>
<kw name="Verify Data Using Database" owner="DatabaseUtility">
<msg time="2024-10-29T11:57:27.455884" level="INFO">connecting to MYSQL...
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg time="2024-10-29T11:57:27.455884" level="INFO">${data_found} = {'COUNT(*)': 16}</msg>
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<status status="PASS" start="2024-10-29T11:57:27.003344" elapsed="0.452540"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T11:57:27.455884" elapsed="0.001052"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T11:57:27.456936" elapsed="0.000000"/>
</kw>
<kw name="Should Not Contain" owner="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T11:57:27.457878" elapsed="0.002097"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" start="2024-10-29T11:57:27.459975" elapsed="0.000000"/>
</return>
<msg time="2024-10-29T11:57:27.459975" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = {'COUNT(*)': 16}</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${db_type}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST_QUERY}</arg>
<arg>True</arg>
<status status="PASS" start="2024-10-29T11:57:27.002371" elapsed="0.457604"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2024-10-29T11:57:27.459975" level="INFO">${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST} = 16</msg>
<var>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</var>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<arg>COUNT(*)</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2024-10-29T11:57:27.459975" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:57:27.460985" level="INFO">Total campaigns on the approval list (database): 16</msg>
<arg>Total campaigns on the approval list (database): ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:57:27.460985" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Total campaigns on the database approval list: ${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:57:27.460985" elapsed="0.000891"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T11:57:27.461876" level="INFO">Test completed. The Next button on the approval preview functions as expected.</msg>
<arg>Test completed. The Next button on the approval preview functions as expected.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T11:57:27.461876" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Test completed. The Next button on the approval preview functions as expected.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:57:27.461876" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal As Numbers" owner="BuiltIn">
<arg>${TOTAL_CAMPAIGNS}</arg>
<arg>${DATABASE_TOTAL_CAMPAIGN_APPROVAL_LIST}</arg>
<doc>Fails if objects are unequal after converting them to real numbers.</doc>
<status status="PASS" start="2024-10-29T11:57:27.462921" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----The front-end and database campaign counts on Approval List match----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T11:57:27.462921" elapsed="0.000954"/>
</kw>
<status status="PASS" start="2024-10-29T11:56:58.284723" elapsed="29.179152"/>
</kw>
<arg>Next Button on Campaign Approvals Preview</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T11:56:19.175755" elapsed="68.288120"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T11:57:27.463875" elapsed="0.006172"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:27.463875" elapsed="0.006172"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:27.470047" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T11:57:27.470047" elapsed="0.034006"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:30.504671" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:27.504053" elapsed="3.000618"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T11:57:30.505789" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T11:57:30.579751" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T11:57:30.579751" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T11:57:30.504671" elapsed="0.078090">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T11:57:32.583342" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T11:57:30.582761" elapsed="2.000581"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T11:57:32.583342" elapsed="3.181260"/>
</kw>
<status status="FAIL" start="2024-10-29T11:57:27.470047" elapsed="8.295745">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T11:57:27.470047" elapsed="8.295745"/>
</kw>
<status status="PASS" start="2024-10-29T11:57:27.463875" elapsed="8.301917"/>
</kw>
<doc>Next Button on Campaign Approvals Preview</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T11:56:19.175755" elapsed="76.591040"/>
</test>
<doc>Testing the Next Button on Campaign Approvals Preview</doc>
<status status="PASS" start="2024-10-29T11:56:18.496478" elapsed="77.272619"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T11:56:18.081185" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_125_Preview_Campaign_Next_Button.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T11:56:19.146751" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T11:56:19.651644" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T11:56:37.560425" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T11:56:47.571379" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T11:56:52.578685" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
