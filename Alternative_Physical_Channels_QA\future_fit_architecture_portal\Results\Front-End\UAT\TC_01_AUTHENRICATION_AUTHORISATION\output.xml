<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 08:10:55.873" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_01_AUTHENRICATION_AUTHORISATION.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:10:56.243" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.243"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:10:56.243" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.243"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:10:56.243" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.243"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.243"/>
</kw>
<test id="s1-t1" name="Login- BU- Capture Campaign" line="47">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Validate Approvals</arg>
<arg>T155057384</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>Login- BU- Capture</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:10:56.243" level="INFO">Set test documentation to:
Login &amp; Validate Approvals</msg>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.243"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:10:56.243" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057384'.</msg>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.243"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:10:56.346" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:10:56.346"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:56.346"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:56.346"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:10:56.346" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:56.346"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:10:56.346" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:56.346"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:56.346"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:10:56.514" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:10:57.152" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:10:57.152" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 6800 has been terminated.
SUCCESS: The process "chrome.exe" with PID 3592 has been terminated.
SUCCESS: The process "chrome.exe" with PID 14164 has been term...</msg>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:10:57.152" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000013E85A07AA0&gt;</msg>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:10:57.152" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:10:57.152" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:10:57.152" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:10:57.152"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:10:57.152" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:10:57.152" endtime="******** 08:11:01.971"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:11:01.971"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:11:01.978" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:11:01.978" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:11:01.973" endtime="******** 08:11:01.978"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:11:01.978" endtime="******** 08:11:01.986"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:11:01.989" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:11:01.989" endtime="******** 08:11:03.477"/>
</kw>
<status status="PASS" starttime="******** 08:11:01.986" endtime="******** 08:11:03.477"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:11:13.478" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:11:03.477" endtime="******** 08:11:13.478"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:11:13.478" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:11:13.489" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:11:13.478" endtime="******** 08:11:13.489"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:11:13.499" endtime="******** 08:11:13.499"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:11:33.500" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:11:13.499" endtime="******** 08:11:33.500"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:11:33.500" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:11:33.560" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:11:33.500" endtime="******** 08:11:33.560"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:11:33.560" endtime="******** 08:11:33.562"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:11:43.563" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:11:33.562" endtime="******** 08:11:43.563"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:11:43.563" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:11:43.572" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:11:43.563" endtime="******** 08:11:43.572"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:11:43.573" endtime="******** 08:11:43.573"/>
</kw>
<status status="PASS" starttime="******** 08:11:01.971" endtime="******** 08:11:43.573"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:11:43.677" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-9.png"&gt;&lt;img src="selenium-screenshot-9.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:11:43.573" endtime="******** 08:11:43.677"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:11:43.677"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.346" endtime="******** 08:11:43.677"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:11:43.677"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:11:48.684" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:11:43.683" endtime="******** 08:11:48.684"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:48.684"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:48.684"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:48.684"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that the user can access CAPTURE campaign link" library="Authentication_Authorisation">
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAPTURE_CAMPAIGN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:11:48.684" level="INFO">Clicking element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/div/mat-list-item/span/span[3]'.</msg>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:48.804"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:11:51.805" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 08:11:48.804" endtime="******** 08:11:51.805"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>CAPTURE CAMPAIGN</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 08:11:51.818" level="INFO">Current page contains text 'CAPTURE CAMPAIGN'.</msg>
<status status="PASS" starttime="******** 08:11:51.805" endtime="******** 08:11:51.818"/>
</kw>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:51.818"/>
</kw>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:51.818"/>
</kw>
<status status="PASS" starttime="******** 08:11:48.684" endtime="******** 08:11:51.818"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:11:51.824" endtime="******** 08:11:51.830"/>
</kw>
<status status="PASS" starttime="******** 08:11:51.824" endtime="******** 08:11:51.830"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:11:51.830" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:11:52.014" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-10.png"&gt;&lt;img src="selenium-screenshot-10.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:11:52.014" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:11:51.830" endtime="******** 08:11:52.158"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:11:52.158" endtime="******** 08:11:52.158"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:11:52.158" endtime="******** 08:11:52.158"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:11:52.158" endtime="******** 08:11:52.158"/>
</kw>
<status status="FAIL" starttime="******** 08:11:51.830" endtime="******** 08:11:52.159"/>
</kw>
<status status="PASS" starttime="******** 08:11:51.830" endtime="******** 08:11:52.159"/>
</kw>
<status status="PASS" starttime="******** 08:11:51.818" endtime="******** 08:11:52.159"/>
</kw>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:11:52.159"/>
</kw>
<doc>Login &amp; Validate Approvals</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:10:56.243" endtime="******** 08:11:52.159"/>
</test>
<test id="s1-t2" name="Login &amp; Logout - BU" line="48">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Logout</arg>
<arg>T155057357</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>*</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:11:55.832" level="INFO">Set test documentation to:
Login &amp; Logout</msg>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:55.832"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:11:55.832" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057357'.</msg>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:55.832"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:11:55.832" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:55.832"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:55.832"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:55.832"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:11:55.832" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:55.838"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:11:55.838" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:11:55.838" endtime="******** 08:11:55.838"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:11:55.838" endtime="******** 08:11:55.838"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:11:55.935" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:11:56.549" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:11:56.549" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 27472 has been terminated.
SUCCESS: The process "chrome.exe" with PID 6852 has been terminated.
SUCCESS: The process "chrome.exe" with PID 31312 has been ter...</msg>
<status status="PASS" starttime="******** 08:11:55.838" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:11:56.549" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000013E85A23080&gt;</msg>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:11:56.549" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:11:56.549" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:11:56.549" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:11:56.549"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:11:56.549" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:11:56.549" endtime="******** 08:12:01.485"/>
</kw>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:12:01.485"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:12:01.488" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:12:01.488" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:12:01.486" endtime="******** 08:12:01.488"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:12:01.488" endtime="******** 08:12:01.494"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:12:01.496" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:12:01.496" endtime="******** 08:12:01.693"/>
</kw>
<status status="PASS" starttime="******** 08:12:01.494" endtime="******** 08:12:01.693"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:12:11.694" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:12:01.693" endtime="******** 08:12:11.694"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:12:11.694" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:12:11.716" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:12:11.694" endtime="******** 08:12:11.716"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:12:11.716" endtime="******** 08:12:11.716"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:12:31.716" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:12:11.716" endtime="******** 08:12:31.716"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:12:31.716" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:12:31.727" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:12:31.716" endtime="******** 08:12:31.727"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:12:31.727" endtime="******** 08:12:31.727"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:12:41.742" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:12:31.727" endtime="******** 08:12:41.742"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:12:41.742" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:12:41.859" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:12:41.742" endtime="******** 08:12:41.859"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:12:41.859" endtime="******** 08:12:41.859"/>
</kw>
<status status="PASS" starttime="******** 08:12:01.485" endtime="******** 08:12:41.859"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:12:41.978" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-11.png"&gt;&lt;img src="selenium-screenshot-11.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:12:41.859" endtime="******** 08:12:41.978"/>
</kw>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:12:41.978"/>
</kw>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:12:41.978"/>
</kw>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:12:41.978"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:12:46.978" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:12:41.978" endtime="******** 08:12:46.978"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.978"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.978"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.978"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.978"/>
</kw>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.978"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.984"/>
</kw>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:46.984"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:12:46.984" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:12:47.096" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-12.png"&gt;&lt;img src="selenium-screenshot-12.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:12:47.096" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:12:46.984" endtime="******** 08:12:47.107"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:12:47.107" endtime="******** 08:12:47.107"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:12:47.107" endtime="******** 08:12:47.107"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:12:47.107" endtime="******** 08:12:47.107"/>
</kw>
<status status="FAIL" starttime="******** 08:12:46.984" endtime="******** 08:12:47.107"/>
</kw>
<status status="PASS" starttime="******** 08:12:46.984" endtime="******** 08:12:47.107"/>
</kw>
<status status="PASS" starttime="******** 08:12:46.978" endtime="******** 08:12:47.107"/>
</kw>
<status status="PASS" starttime="******** 08:11:55.832" endtime="******** 08:12:47.107"/>
</kw>
<doc>Login &amp; Logout</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:11:55.827" endtime="******** 08:12:47.107"/>
</test>
<test id="s1-t3" name="Login- BU- Calendar" line="49">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Validate Calendar</arg>
<arg>T155057360</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>Login- BA- Calendar</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:12:50.486" level="INFO">Set test documentation to:
Login &amp; Validate Calendar</msg>
<status status="PASS" starttime="******** 08:12:50.486" endtime="******** 08:12:50.486"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:12:50.486" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057360'.</msg>
<status status="PASS" starttime="******** 08:12:50.486" endtime="******** 08:12:50.486"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:12:50.487" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:50.487"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:50.487"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:50.487"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:12:50.487" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:50.487"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:12:50.487" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:50.487"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:50.487"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:12:50.943" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:12:51.819" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:12:51.819" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 14116 has been terminated.
SUCCESS: The process "chrome.exe" with PID 29180 has been terminated.
SUCCESS: The process "chrome.exe" with PID 7364 has been ter...</msg>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:12:51.819" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000013E85A20470&gt;</msg>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:12:51.819" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:12:51.819" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:12:51.819" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:51.819"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:12:51.819" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:12:51.819" endtime="******** 08:12:57.601"/>
</kw>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:12:57.601"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:12:57.605" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:12:57.606" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:12:57.602" endtime="******** 08:12:57.606"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:12:57.606" endtime="******** 08:12:57.616"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:12:57.619" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:12:57.619" endtime="******** 08:12:57.846"/>
</kw>
<status status="PASS" starttime="******** 08:12:57.616" endtime="******** 08:12:57.846"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:13:07.846" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:12:57.846" endtime="******** 08:13:07.846"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:13:07.846" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:13:07.923" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:13:07.846" endtime="******** 08:13:07.923"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:13:07.923" endtime="******** 08:13:07.924"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:13:27.931" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:13:07.924" endtime="******** 08:13:27.931"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:13:27.931" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:13:27.946" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:13:27.931" endtime="******** 08:13:27.946"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:13:27.946" endtime="******** 08:13:27.946"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:13:37.947" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:13:27.946" endtime="******** 08:13:37.947"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:13:37.947" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:13:37.983" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:13:37.947" endtime="******** 08:13:37.983"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:13:37.983" endtime="******** 08:13:37.983"/>
</kw>
<status status="PASS" starttime="******** 08:12:57.601" endtime="******** 08:13:37.983"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:13:38.061" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-13.png"&gt;&lt;img src="selenium-screenshot-13.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:13:37.983" endtime="******** 08:13:38.061"/>
</kw>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:13:38.061"/>
</kw>
<status status="PASS" starttime="******** 08:12:50.487" endtime="******** 08:13:38.061"/>
</kw>
<status status="PASS" starttime="******** 08:12:50.486" endtime="******** 08:13:38.061"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:13:43.061" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:13:38.061" endtime="******** 08:13:43.061"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that the user can access the Calendar View" library="Authentication_Authorisation">
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:13:43.061" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 08:13:43.061" endtime="******** 08:13:43.140"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Greyed out Campaigns needs to be approved*</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 08:13:43.175" level="INFO">Current page contains text 'Greyed out Campaigns needs to be approved*'.</msg>
<status status="PASS" starttime="******** 08:13:43.140" endtime="******** 08:13:43.175"/>
</kw>
<status status="PASS" starttime="******** 08:13:43.061" endtime="******** 08:13:43.175"/>
</kw>
<status status="PASS" starttime="******** 08:13:43.061" endtime="******** 08:13:43.175"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:13:43.175" endtime="******** 08:13:43.175"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:13:43.175" endtime="******** 08:13:43.175"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:13:43.175" endtime="******** 08:13:43.175"/>
</kw>
<status status="PASS" starttime="******** 08:13:43.061" endtime="******** 08:13:43.175"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:13:43.175" endtime="******** 08:13:43.190"/>
</kw>
<status status="PASS" starttime="******** 08:13:43.175" endtime="******** 08:13:43.190"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:13:43.192" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:13:43.402" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-14.png"&gt;&lt;img src="selenium-screenshot-14.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:13:43.404" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:13:43.192" endtime="******** 08:13:43.404"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:13:43.404" endtime="******** 08:13:43.404"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:13:43.404" endtime="******** 08:13:43.404"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:13:43.404" endtime="******** 08:13:43.404"/>
</kw>
<status status="FAIL" starttime="******** 08:13:43.190" endtime="******** 08:13:43.404"/>
</kw>
<status status="PASS" starttime="******** 08:13:43.190" endtime="******** 08:13:43.404"/>
</kw>
<status status="PASS" starttime="******** 08:13:43.175" endtime="******** 08:13:43.404"/>
</kw>
<status status="PASS" starttime="******** 08:12:50.486" endtime="******** 08:13:43.404"/>
</kw>
<doc>Login &amp; Validate Calendar</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:12:50.484" endtime="******** 08:13:43.404"/>
</test>
<test id="s1-t4" name="Login- BU- Dashboard" line="50">
<kw name="Validates Authentication and Authorisation">
<arg>Login &amp; Validate Dashboard</arg>
<arg>T155057358</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>Login- BA- Dashboard</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 08:13:46.922" level="INFO">Set test documentation to:
Login &amp; Validate Dashboard</msg>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 08:13:46.922" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057358'.</msg>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:13:46.922" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:13:46.922" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 08:13:46.922" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:46.922"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 08:13:47.036" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 08:13:47.694" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 08:13:47.694" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 2900 has been terminated.
SUCCESS: The process "chrome.exe" with PID 17060 has been terminated.
SUCCESS: The process "chrome.exe" with PID 16320 has been ter...</msg>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:47.694"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:13:47.694" endtime="******** 08:13:47.694"/>
</kw>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:47.695"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:13:47.695" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000013E85EF82C0&gt;</msg>
<status status="PASS" starttime="******** 08:13:47.695" endtime="******** 08:13:47.695"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 08:13:47.695" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 08:13:47.695" endtime="******** 08:13:47.695"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:13:47.695" endtime="******** 08:13:47.695"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 08:13:47.695" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 08:13:47.696" endtime="******** 08:13:47.696"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:13:47.696" endtime="******** 08:13:47.696"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 08:13:47.696" endtime="******** 08:13:47.696"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 08:13:47.697" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 08:13:47.697" endtime="******** 08:13:47.697"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 08:13:47.700" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 08:13:47.700" endtime="******** 08:13:53.104"/>
</kw>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:13:53.105"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 08:13:53.108" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 08:13:53.108" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 08:13:53.106" endtime="******** 08:13:53.108"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 08:13:53.108" endtime="******** 08:13:53.114"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 08:13:53.114" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 08:13:53.114" endtime="******** 08:13:53.403"/>
</kw>
<status status="PASS" starttime="******** 08:13:53.114" endtime="******** 08:13:53.403"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:14:03.405" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:13:53.403" endtime="******** 08:14:03.405"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:14:03.405" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:14:03.477" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 08:14:03.405" endtime="******** 08:14:03.477"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:14:03.477" endtime="******** 08:14:03.477"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:14:23.504" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 08:14:03.477" endtime="******** 08:14:23.507"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:14:23.507" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:14:23.523" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 08:14:23.507" endtime="******** 08:14:23.523"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:14:23.523" endtime="******** 08:14:23.524"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:14:33.524" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 08:14:23.524" endtime="******** 08:14:33.524"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 08:14:33.524" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:14:33.562" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 08:14:33.524" endtime="******** 08:14:33.562"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 08:14:33.562" endtime="******** 08:14:33.562"/>
</kw>
<status status="PASS" starttime="******** 08:13:53.105" endtime="******** 08:14:33.562"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 08:14:33.767" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-15.png"&gt;&lt;img src="selenium-screenshot-15.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 08:14:33.562" endtime="******** 08:14:33.767"/>
</kw>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:14:33.767"/>
</kw>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:14:33.768"/>
</kw>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:14:33.768"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 08:14:38.768" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 08:14:33.768" endtime="******** 08:14:38.768"/>
</kw>
<kw name="And Authentication_Authorisation.Validate the test cases" library="Authentication_Authorisation">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Calendar"</arg>
<arg>Validate that the user can access the Calendar View</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:14:38.768" endtime="******** 08:14:38.768"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Dashboard"</arg>
<arg>Validate that the user can access the Dashboard</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that the user can access the Dashboard" library="Authentication_Authorisation">
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[1]/span</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:14:38.772" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[1]/span'.</msg>
<status status="PASS" starttime="******** 08:14:38.768" endtime="******** 08:14:38.901"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Installed Schedule Version</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 08:14:38.931" level="INFO">Current page contains text 'Installed Schedule Version'.</msg>
<status status="PASS" starttime="******** 08:14:38.902" endtime="******** 08:14:38.931"/>
</kw>
<status status="PASS" starttime="******** 08:14:38.768" endtime="******** 08:14:38.931"/>
</kw>
<status status="PASS" starttime="******** 08:14:38.768" endtime="******** 08:14:38.931"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BA- Approval"</arg>
<arg>Validate that the user can access approvals under admin</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:14:38.931" endtime="******** 08:14:38.931"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "Login- BU- Capture"</arg>
<arg>Validate that the user can access CAPTURE campaign link</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 08:14:38.931" endtime="******** 08:14:38.931"/>
</kw>
<status status="PASS" starttime="******** 08:14:38.768" endtime="******** 08:14:38.931"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 08:14:38.934" endtime="******** 08:14:42.533"/>
</kw>
<status status="PASS" starttime="******** 08:14:38.934" endtime="******** 08:14:42.533"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 08:14:42.537" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 08:14:42.724" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-16.png"&gt;&lt;img src="selenium-screenshot-16.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 08:14:42.724" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 08:14:42.535" endtime="******** 08:14:42.725"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 08:14:42.725" endtime="******** 08:14:42.725"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 08:14:42.725" endtime="******** 08:14:42.726"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 08:14:42.726" endtime="******** 08:14:42.726"/>
</kw>
<status status="FAIL" starttime="******** 08:14:42.534" endtime="******** 08:14:42.726"/>
</kw>
<status status="PASS" starttime="******** 08:14:42.533" endtime="******** 08:14:42.726"/>
</kw>
<status status="PASS" starttime="******** 08:14:38.934" endtime="******** 08:14:42.726"/>
</kw>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:14:42.726"/>
</kw>
<doc>Login &amp; Validate Dashboard</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 08:13:46.922" endtime="******** 08:14:42.740"/>
</test>
<doc>Testing future fit Authentication &amp; Authorisation</doc>
<status status="PASS" starttime="******** 08:10:55.983" endtime="******** 08:14:46.288"/>
</suite>
<statistics>
<total>
<stat pass="4" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="4" fail="0" skip="0">FFT HEALTHCHECK</stat>
<stat pass="4" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="4" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 08:10:59.221" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:11:13.478" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:11:33.500" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:11:43.563" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:11:55.825" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:11:58.816" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:12:11.694" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:12:31.716" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:12:41.742" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:12:50.479" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:12:54.268" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:13:07.846" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:13:27.931" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:13:37.947" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:13:46.920" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
<msg timestamp="******** 08:13:49.775" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 08:14:03.405" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:14:23.507" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:14:33.524" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 08:14:46.260" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
