<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20241002 07:20:59.553">
   <suite name="VMS Portal" id="s1" source="C:\Users\<USER>\source\repos\alternative_physical_channels\vms\tests\ADMIN_USER_MANAGEMENT\RAC29a_TC_342_Validate_Add_New_User_User_Management.robot">
      <test name="Create a VMS user with a 'Browse' Role Test Case" id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:00.561" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20241002 07:21:28.395" status="PASS" starttime="20241002 07:21:00.561"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:28.395" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:21:29.253" status="PASS" starttime="20241002 07:21:28.395"/>
         </kw>
         <kw library="Selenium" name="And The user Adds a new VMS User">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:29.253" level="INFO">And The user Adds a new VMS User</msg>
            <status endtime="20241002 07:21:44.077" status="PASS" starttime="20241002 07:21:29.253"/>
         </kw>
         <kw library="Selenium" name="And The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:44.078" level="INFO">And The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:21:44.736" status="PASS" starttime="20241002 07:21:44.078"/>
         </kw>
         <kw library="Selenium" name="And Searches for existing user">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:44.737" level="INFO">And Searches for existing user</msg>
            <status endtime="20241002 07:21:48.018" status="PASS" starttime="20241002 07:21:44.737"/>
         </kw>
         <kw library="Selenium" name="Then The created user must be found on VMS Application and Database">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:48.019" level="INFO">Then The created user must be found on VMS Application and Database</msg>
            <status endtime="20241002 07:21:49.388" status="PASS" starttime="20241002 07:21:48.019"/>
         </kw>
         <tags>
            <tag>Create a VMS user with a 'Browse' Role Test Case</tag>
         </tags>
         <status endtime="20241002 07:21:55.237" critical="yes" status="PASS" starttime="20241002 07:21:00.559"/>
      </test>
      <test name="Create a VMS user with a 'User' Role Test Case" id="s1-t2">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Create a VMS user with 'User' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:21:55.252" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20241002 07:22:14.716" status="PASS" starttime="20241002 07:21:55.252"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'User' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:22:14.719" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:22:15.954" status="PASS" starttime="20241002 07:22:14.719"/>
         </kw>
         <kw library="Selenium" name="And The user Adds a new VMS User">
            <doc>Create a VMS user with 'User' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:22:15.955" level="INFO">And The user Adds a new VMS User</msg>
            <status endtime="20241002 07:22:29.505" status="PASS" starttime="20241002 07:22:15.955"/>
         </kw>
         <kw library="Selenium" name="And The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'User' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:22:29.505" level="INFO">And The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:22:30.069" status="PASS" starttime="20241002 07:22:29.505"/>
         </kw>
         <kw library="Selenium" name="And Searches for existing user">
            <doc>Create a VMS user with 'User' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:22:30.069" level="INFO">And Searches for existing user</msg>
            <status endtime="20241002 07:22:33.409" status="PASS" starttime="20241002 07:22:30.069"/>
         </kw>
         <kw library="Selenium" name="Then The created user must be found on VMS Application and Database">
            <doc>Create a VMS user with 'User' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:22:33.409" level="INFO">Then The created user must be found on VMS Application and Database</msg>
            <status endtime="20241002 07:22:34.113" status="PASS" starttime="20241002 07:22:33.409"/>
         </kw>
         <tags>
            <tag>Create a VMS user with a 'User' Role Test Case</tag>
         </tags>
         <status endtime="20241002 07:22:43.177" critical="yes" status="PASS" starttime="20241002 07:21:55.252"/>
      </test>
      <test name="Create a VMS user with a 'Supervisor' Role Test Case" id="s1-t3">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Create a VMS user with 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:22:43.177" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20241002 07:23:00.684" status="PASS" starttime="20241002 07:22:43.177"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:00.684" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:23:01.372" status="PASS" starttime="20241002 07:23:00.684"/>
         </kw>
         <kw library="Selenium" name="And The user Adds a new VMS User">
            <doc>Create a VMS user with 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:01.373" level="INFO">And The user Adds a new VMS User</msg>
            <status endtime="20241002 07:23:17.031" status="PASS" starttime="20241002 07:23:01.373"/>
         </kw>
         <kw library="Selenium" name="And The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:17.031" level="INFO">And The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:23:19.587" status="PASS" starttime="20241002 07:23:17.031"/>
         </kw>
         <kw library="Selenium" name="And Searches for existing user">
            <doc>Create a VMS user with 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:19.587" level="INFO">And Searches for existing user</msg>
            <status endtime="20241002 07:23:24.075" status="PASS" starttime="20241002 07:23:19.587"/>
         </kw>
         <kw library="Selenium" name="Then The created user must be found on VMS Application and Database">
            <doc>Create a VMS user with 'Supervisor' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:24.075" level="INFO">Then The created user must be found on VMS Application and Database</msg>
            <status endtime="20241002 07:23:25.483" status="PASS" starttime="20241002 07:23:24.075"/>
         </kw>
         <tags>
            <tag>Create a VMS user with a 'Supervisor' Role Test Case</tag>
         </tags>
         <status endtime="20241002 07:23:33.709" critical="yes" status="PASS" starttime="20241002 07:22:43.177"/>
      </test>
      <test name="Create a VMS user with a 'Administrator' Role Test Case" id="s1-t4">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Create a VMS user with 'Administrator' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:33.709" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20241002 07:23:51.471" status="PASS" starttime="20241002 07:23:33.709"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'Administrator' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:51.471" level="INFO">When The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:23:52.067" status="PASS" starttime="20241002 07:23:51.471"/>
         </kw>
         <kw library="Selenium" name="And The user Adds a new VMS User">
            <doc>Create a VMS user with 'Administrator' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:23:52.067" level="INFO">And The user Adds a new VMS User</msg>
            <status endtime="20241002 07:24:06.370" status="PASS" starttime="20241002 07:23:52.067"/>
         </kw>
         <kw library="Selenium" name="And The user navigates to Admin - User Management">
            <doc>Create a VMS user with 'Administrator' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:24:06.370" level="INFO">And The user navigates to Admin - User Management</msg>
            <status endtime="20241002 07:24:08.087" status="PASS" starttime="20241002 07:24:06.370"/>
         </kw>
         <kw library="Selenium" name="And Searches for existing user">
            <doc>Create a VMS user with 'Administrator' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:24:08.087" level="INFO">And Searches for existing user</msg>
            <status endtime="20241002 07:24:11.602" status="PASS" starttime="20241002 07:24:08.087"/>
         </kw>
         <kw library="Selenium" name="Then The created user must be found on VMS Application and Database">
            <doc>Create a VMS user with 'Administrator' Role</doc>
            <arguments>
               <arg></arg>
            </arguments>
            <msg timestamp="20241002 07:24:11.602" level="INFO">Then The created user must be found on VMS Application and Database</msg>
            <status endtime="20241002 07:24:12.013" status="PASS" starttime="20241002 07:24:11.602"/>
         </kw>
         <tags>
            <tag>Create a VMS user with a 'Administrator' Role Test Case</tag>
         </tags>
         <status endtime="20241002 07:24:17.969" critical="yes" status="PASS" starttime="20241002 07:23:33.709"/>
      </test>
      <status endtime="20241002 07:24:17.969" status="PASS" starttime="20241002 07:20:59.553"/>
   </suite>
   <statistics>
      <total>
         <stat pass="4" fail="0">Critical Tests</stat>
         <stat pass="4" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">Create a VMS user with a 'Browse' Role Test Case</stat>
         <stat pass="1" fail="0">Create a VMS user with a 'User' Role Test Case</stat>
         <stat pass="1" fail="0">Create a VMS user with a 'Supervisor' Role Test Case</stat>
         <stat pass="1" fail="0">Create a VMS user with a 'Administrator' Role Test Case</stat>
      </tag>
      <suite>
         <stat name="VMS Portal" pass="4" fail="0" id="s1">VMS Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
