import json
import re


from robot.api.deco import keyword


class BinTypeUpdate:
    def __init__(self, **kwargs):
        # Initialize bins as an empty dict
        self.bins = {}
        bintypeid = ""
        name = ""
        description = ""

        if 'bintypeid' in kwargs:
            print(f"bintypeid: {kwargs['bintypeid']}")
            bintypeid = kwargs['bintypeid']

        if 'name' in kwargs:
            print(f"name: {kwargs['name']}")
            name = kwargs['name']

        if 'description' in kwargs:
            print(f"description: {kwargs['description']}")
            description = kwargs['description']

        self.bins = {
            "bintypeid": bintypeid,
            "name": name,
            "description": description
        }

        print('Payload: ', self.bins)





    def get_json_request(self):
        # Return the bins in JSON format
        return json.dumps(self.bins, indent=4)

    @keyword
    def create_update_bin_type_request(self, bintypeid, name, description):
        """
        This method processes the bins and returns the JSON request.
        """
        kwargs = {'bintypeid': bintypeid, 'name': name, 'description': description}

        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def get_endpoint(self, domain):
        path = "/api/v1/bintables/admin/bintypes/update"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers
