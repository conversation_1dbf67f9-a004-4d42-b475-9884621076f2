*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             Collections

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/Navigation.robot
Resource                                            ../common/Navigation.robot

*** Variables ***
${CAMPAIGN_TARGET_YES_RADIO_BUTTON}                 xpath=//*[@id="mat-radio-2"] 
${CAMPAIGN_TARGET_NO_RADION_BUTTON}                 xpath=//*[@id="mat-radio-3"]
${REGIONAL_CAMPAIGN_DROPDOWN}                       xpath=//*[@id="cdk-step-content-0-0"]/app-campaign-targeted/div[2]/div[2]/mat-form-field
${ATM_DROPDOWN}                                     xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[1]/app-campaign-targeted/div[2]/div[2]/mat-form-field/div/div[1]/div
${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}                xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[3]/button[2]/span[1]

*** Keywords ***
The user fills out Campaign Targeted 
    [Arguments]        ${IS_CAMPAIGN_TARGETED}  ${CAMPAIGN_TARGET}  ${CAMPAIGN_TARGETED_REGION_OR_ATM} 

    Log To Console    Campaign Targeted: ${IS_CAMPAIGN_TARGETED}

    Log To Console    Campaign Target: ${CAMPAIGN_TARGET}
    
    User select radio button    ${IS_CAMPAIGN_TARGETED}


    # What is your Campaign Targeted?     
    Run Keyword If  '${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'  User select radio button  Region
    ...  ELSE IF    '${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'  User select radio button  ATM
    Sleep    2s
    
    Run Keyword If  '${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'  Select from dropdown  ${REGIONAL_CAMPAIGN_DROPDOWN}  ${CAMPAIGN_TARGETED_REGION_OR_ATM} 
    ...  ELSE IF    '${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'  Select ATM from dropdown  ${ATM_DROPDOWN}  ${CAMPAIGN_TARGETED_REGION_OR_ATM} 

   Capture page screenshot  FilloutCampaignTarget.png
