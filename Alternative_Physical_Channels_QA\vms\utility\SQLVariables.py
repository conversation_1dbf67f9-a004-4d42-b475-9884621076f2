# ========================================
# USER MANAGEMENT QUERIES
# ========================================
SQL_VMS_USER_DETAILS = "SELECT *  FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link  where usr.Username = 'USER_NAME'"
SQL_GET_TOTAL_NUMBER_OF_VMS_USERS = "SELECT count(*) as total_users   FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link where Enabled = 1"
SQL_VMS_VENDOR_EMAIL_DETAILS = "SELECT *  FROM [VMS_UAT].[core].[user]	usr INNER JOIN	[VMS_UAT].[core].[group]	grp ON	usr.Group_link = grp.Link  where usr.Username = 'USER_NAME'"

# ========================================
# EMAIL MANAGEMENT QUERIES
# ========================================
SQL_VMS_EMAIL_DETAILS = "SELECT * FROM [VMS_UAT].[core].[email] WHERE Link = 'LINK_ID'"
SQL_GET_RANDOM_VMS_EMAIL = "SELECT TOP 1 [Link], [Vendor], [Email] FROM [VMS_UAT].[core].[email] ORDER BY NEWID()"

# ========================================
# ATM GASPER DETAILS QUERIES
# ========================================
SQL_GET_VMS_GASPER_DETAILS = "SELECT COUNT(*) AS ROW_NUMBERS FROM [VMS_UAT].[core].[gasper_details]"
SQL_GET_VMS_GASPER_DETAILS_USING_ATM_ID = "SELECT [ID], [SERIAL_NUM], [ADDRESS], [ADDRESS2], [CITY], [REGION], [CLASS], [DATA_LINE], [BRANCH], [INSTITUTION], [OBJECT_TYPE], [ZONE], [IN_SERVICE], [OUT_OF_SERVICE], [ZIP], [EDITOR_NOTE], [IP_ADDRESS], [VENDOR_SITE_ID], [PM] FROM [VMS_UAT].[core].[gasper_details] WHERE [ID] LIKE '%atm_id%'"

# ========================================
# ATM DETAILS VALIDATION QUERIES
# ========================================
SQL_GET_ATM_DETAILS_FOR_COMPARISON = "SELECT [ID], [SERIAL_NUM], [ADDRESS], [ADDRESS2], [CITY], [REGION], [CLASS], [DATA_LINE], [BRANCH], [INSTITUTION], [OBJECT_TYPE], [ZONE], [IN_SERVICE], [OUT_OF_SERVICE], [ZIP], [EDITOR_NOTE], [IP_ADDRESS], [VENDOR_SITE_ID], [PM] FROM [VMS_UAT].[core].[gasper_details] WHERE [ID] = 'ATM_ID'"
SQL_CHECK_ATM_EXISTS = "SELECT COUNT(*) as atm_count FROM [VMS_UAT].[core].[gasper_details] WHERE [ID] = 'ATM_ID'"
