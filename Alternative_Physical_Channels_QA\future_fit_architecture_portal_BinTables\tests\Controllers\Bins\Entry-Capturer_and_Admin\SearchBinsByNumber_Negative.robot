*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/SearchBinsByNumber_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for a Bin using incorrect data
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_NUMBER}  ${ACTION}   ${EXPECTED_STATUS_CODE}   ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request to search for bins using a Bin Number    ${BASE_URL}      ${BIN_NUMBER}        ${ACTION}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The expected Error Message must be displayed                           ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                                             |        *DOCUMENTATION*    		 |         *BASE_URL*                  |     *BIN_NUMBER*      | *ACTION*   |   *EXPECTED_STATUS_CODE*   |         *EXPECTED_ERROR_MESSAGE*                                                  |
| Search for a Bin without supplying the 'action' parameter.           | Search for a Bin using incorrect data   | Search Bin by Number on the API   |                                     |       333333          |            |        400                 |       Invalid Operation: Valid action(s) are (add, edit, delete or reactivate).   |
| Search for a Bin supplying the 'action' parameter that is not valid. | Search for a Bin using incorrect data   | Search Bin by Number on the API   |                                     |       333333          |     xz     |        400                 |       Invalid Operation: Valid action(s) are (add, edit, delete or reactivate).   |
| Search for a Bin without supplying the 'binNumber' parameter.        | Search for a Bin using incorrect data   | Search Bin by Number on the API   |                                     |                       |     add    |        400                 |       Bin number must not be null or empty.                                       |
| Search for a Bin supplying the 'binNumber' that does not exist.      | Search for a Bin using incorrect data   | Search Bin by Number on the API   |                                     |       33333334        |     add    |        404                 |       The bin with the specified identifier was not found.                        |
| Search for an incorrect Bin number format.                            | Search for a Bin using incorrect data  | Search Bin by Number on the API   |                                     |       3s4w            |     add    |        404                 |       The bin with the specified identifier was not found.                        |
