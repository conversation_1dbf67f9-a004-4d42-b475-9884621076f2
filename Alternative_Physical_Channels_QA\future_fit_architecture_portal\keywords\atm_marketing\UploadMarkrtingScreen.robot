*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/Utility.py
Library                                             Collections
Library                                             String
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../common/Navigation.robot
Resource                                            ../../keywords/common/DBUtility.robot


*** Variables ***
${CAPTURE_CAMPAIGN_SAVE_BUTTON}                     xpath=//span[text()='Save']/parent::button
${LANGUAGE_DROPDOWN}                                xpath=//mat-select[@role="combobox" and @name="language"]
${CAPTURE_CAMPAIGN_LANGUAGE_PATH1}                  //span[contains(text(),
${CAPTURE_CAMPAIGN_LANGUAGE_PATH2}                  )]
${DURATION_DROPDOWN}                                xpath=//mat-select[@role="combobox" and @name="duation"]
${CAPTURED_CAMPAIGN_QUERY}                          SELECT * FROM ATM_Marketing.Campaign where campaignName = 'camp_name'



*** Keywords ***
The user captures marketing screen information
    [Arguments]        ${CAMPAIGN_LANGUAGE}  ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}

    ${counter}=        Set Variable  0

    #Split the languages and images using comma as a delimeter
    @{campaign_laguages}=       Split String    ${CAMPAIGN_LANGUAGE}  ,
    @{campaign_images}=         Split String    ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}  ,

    ${campaign_laguages_length}=      Get length  ${campaign_laguages}
    log  ${campaign_laguages}

    ${campaign_images_length}=      Get length  ${campaign_images}
    log  ${campaign_images}

    Log to Console      @{campaign_laguages}
    Log to Console      @{campaign_images}
    FOR    ${language}    IN   @{campaign_laguages}
        Log to Console     Language is   :  ${campaign_laguages[${counter}].strip()}
        ${current_image}=  Set Variable     ''
        Run Keyword If    (${campaign_images_length}-1) < ${counter}    Fail    Please provide an image path variable for '${language.strip()}' language!
        Exit For Loop IF    (${campaign_images_length}-1) < ${counter}
        Select from dropdown  ${LANGUAGE_DROPDOWN}  ${campaign_laguages[${counter}].strip()}
        # Adding Marketing Images
        ${image_directory}=     get path
        ${final_image_path}=          Catenate  SEPARATOR=\\  ${image_directory}  future_fit_architecture_portal   ${campaign_images[${counter}].strip()}
        Log to Console     Image path is : ${campaign_images[${counter}].strip()}
        Choose File    xpath://input[@value='select' and @class='ng-star-inserted']  ${final_image_path}
        Sleep    3s
        Capture Page Screenshot  UploadMarkrtingScreen.png
        ${counter}=        Set Variable  ${counter}+1
    END


The user saves the campaign

    Wait for spinner to disapear
    
    Click Element       ${CAPTURE_CAMPAIGN_SAVE_BUTTON}


The captured campaign must exist on the database

    #Fetch the details of the captured campaign from the environment variables
    ${campaign_captured_name}=   Get Environment Variable    CAPTURED_CAMPAIGN_NAME
    ${campaign_start_date}=    Get Environment Variable    CAPTURED_CAMPAIGN_START_DATE
    ${campaign_end_date}=    Get Environment Variable    CAPTURED_CAMPAIGN_END_DATE

    Log Many   Campaign Name to verify on the DB is: ${campaign_captured_name}
    Log Many   Campaign Start Date is: ${campaign_start_date}
    Log Many   Campaign End Date is: ${campaign_end_date}


    #Subtract 1 second from the campaign start date to match the data base field for the same
    # ${modified_campaign_start_date} =     Subtract Time From Date  ${campaign_start_date}  1
    #Log     Modified Campaign Date:${modified_campaign_start_date}

    ${modified_campaign_start_date_one}=  Replace String    ${campaign_start_date}    .000    ${SPACE}
    ${modified_campaign_start_date}=    Convert Date  ${modified_campaign_start_date_one.strip()}    exclude_millis=yes     date_format=%Y-%m-%d %H:%M:%S
    ${modified_campaign_end_date_one}=  Replace String    ${campaign_end_date}    .000    ${SPACE}
    ${modified_campaign_end_date}=    Convert Date  ${modified_campaign_end_date_one.strip()}    exclude_millis=yes     date_format=%Y-%m-%d %H:%M:%S
    ${modified_campaign_end_date}=    Replace String    ${modified_campaign_end_date}    00:00:00    23:59:59

    #use the schedule captured campaign name to create the DB query
    ${sql_query}=  Replace String    ${CAPTURED_CAMPAIGN_QUERY}    camp_name    ${campaign_captured_name}

    Log To Console    ${sql_query}
    Sleep   8s
    #Search for a campaign on the database
    ${db_type}=   Set Variable   'MYSQL'

    #Get the current schedule version from the database
    ${data_base_campaigns}=      Execute SQL Query  ${db_type}  ${sql_query}    True
    ${db_campaign_name}=    Get From Dictionary    ${data_base_campaigns}    campaignName
    ${db_campaign_start_date}=    Get From Dictionary    ${data_base_campaigns}    campaignStartDate
    ${db_campaign_end_date}=    Get From Dictionary    ${data_base_campaigns}    campaignEndDate
    ${db_is_approved}=    Get From Dictionary    ${data_base_campaigns}    isApproved
    ${db_is_isactive}=    Get From Dictionary    ${data_base_campaigns}    isActive

    Log Many  Campaign Name from the DB is:       ${db_campaign_name}
    Log Many  Campaign Start Date from the DB is: ${db_campaign_start_date}
    Log Many  Campaign End Date from the DB is:   ${db_campaign_end_date}
    Log Many  Campaign Active Status from the DB is:   ${db_is_isactive}
    Log Many  Campaign Approved Status from the DB is:   ${db_is_approved}

    Should Be Equal As Strings    ${campaign_captured_name}    ${db_campaign_name}
    Should Be Equal As Strings    ${modified_campaign_start_date}    ${db_campaign_start_date}
    Should Be Equal As Strings    ${modified_campaign_end_date}    ${db_campaign_end_date}

    #Verify that the campaign is active and not approved
    Run Keyword If    '${db_is_isactive}' != '1'
    ...    Fail    The campaign named '${campaign_captured_name}' is not active on the Database
    ...  ELSE IF    '${db_is_approved}' != '0'
    ...    Fail    The campaign named '${campaign_captured_name}' is active and also approved on the Database after it was captured.
    ...  ELSE
    ...    Log Many     The campaign named '${campaign_captured_name}', was found on the database. The start date of the campaign is  '${db_campaign_start_date}' and the end date is '${db_campaign_end_date}'.

