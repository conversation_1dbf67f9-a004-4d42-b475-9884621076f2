*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Sort Selection Functionality: Sort with Default Rows per Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Verify Sort Selection Functionality: Sort with Default Rows per Page
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${SORT_CRITERIA}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user chooses and clicks Sorting Criteria   ${SORT_CRITERIA}

    Then The user verifies that the data is sorted by The Sort Criteria in Ascending order

    Then The user chooses and clicks Sorting Criteria   ${SORT_CRITERIA}

    Then The user verifies that the data is sorted by The Sort Criteria in Ascending order

  #  Then The user verifies that the data is sorted by The Sort Criteria in Descending order



| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |    ***SORT_CRITERIA***   |
| Verify Sort Selection Functionality: Sort with Default Rows per Page | Verify Sort Selection Functionality: Sort with Default Rows per Page     | Verify Sort Selection Functionality: Sort with Default Rows per Page |  VMS_UAT    |   ATM Branch