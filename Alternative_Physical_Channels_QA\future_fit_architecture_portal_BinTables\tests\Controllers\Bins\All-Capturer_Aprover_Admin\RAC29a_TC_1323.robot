*** Settings ***
#Author Name               : Thabo
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/GetAllBins_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-1326




*** Keywords ***
Search for all Bins on the GetAllBins Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User gets all the active and inactive Bins from the Database
    When The User sends a Get Request for GetAllBins using an incorrect token      ${BASE_URL}      ${EMPTY}
    Then The service returns an expected status code                            ${EXPECTED_STATUS_CODE}


| *** Test Cases ***                                                                                                                                        |        *DOCUMENTATION*    		           |         *BASE_URL*                 |    *EXPECTED_STATUS_CODE*   |
| Verify that the GetAllBins API requires valid authentication to access BIN data (401 Unauthorized)   | Search for all Bins on the GetAllBins Controller   | Search Bin by Number on the GetAllBins API  |                                    |           401               |
