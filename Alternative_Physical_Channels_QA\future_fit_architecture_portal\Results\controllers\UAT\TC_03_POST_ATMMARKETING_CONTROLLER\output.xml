<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 09:05:26.862" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\ATMMarketing\TC_03_POST_ATMMARKETING_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:05:27.288" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 09:05:27.288" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.288" endtime="******** 09:05:27.304"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Download Marketing Campaign(s)" line="44">
<kw name="ATM marketing campaigns download">
<arg>Download Marketing Campaign(s)</arg>
<arg>155057479</arg>
<arg>ATMMarketing</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMMarketing</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>"id":15</arg>
<arg>id=0</arg>
<arg>atmNumber=08397</arg>
<arg>version=Thabo</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">Set test documentation to:
Download Marketing Campaign(s)</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057479'.</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:05:27.304" level="INFO">${path} = data/ATMMarketing.json</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.304"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 09:05:27.335" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.335"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.335"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 09:05:27.340" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 09:05:27.340" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 09:05:27.335" endtime="******** 09:05:27.340"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 09:05:27.340" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 09:05:27.340" endtime="******** 09:05:27.540"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 09:05:27.540" endtime="******** 09:05:27.540"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:05:27.540" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 09:05:27.540" endtime="******** 09:05:27.540"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.335" endtime="******** 09:05:27.540"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:05:27.540" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 09:05:27.540" endtime="******** 09:05:27.540"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:05:27.540" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 09:05:27.540" endtime="******** 09:05:27.540"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:05:27.541" level="INFO">${path} = data/ATMMarketing.json</msg>
<status status="PASS" starttime="******** 09:05:27.540" endtime="******** 09:05:27.541"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 09:05:27.541" level="INFO">${payload} = {'id': 0, 'atmNumber': '08397', 'version': 'Thabo'}</msg>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${payload}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:05:27.541" level="INFO">{'id': 0, 'atmNumber': '08397', 'version': 'Thabo'}</msg>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 09:05:27.541" level="INFO">${end_point} = /ATMMarketing</msg>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</kw>
<status status="NOT RUN" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 09:05:27.541" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</branch>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.541"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 09:05:27.972" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketing 
 path_url=/ATMMarketing 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fdSR85jLUjiP_yOEBnrdpNgKaAA7PcnB5k4nfJz9joG07H0tJMAo-XE_Wm8aGAgam-zML3jfZSNhcJL_Iw7-atPyO1gvcENTHwKgXnbIq8Hm0C81CWjepOKKVI4vH4Kd0UdadvIjusYbqV8PkaqwRacysjteDM0uF09pgxy041d8hburt3oclCrYAYPa_Ji5yrZfXvPD_mxoGgqTCzONOF4i2ntRthTmlqEjvK0G_vZfCYbqPc5zZ1qQ70PpHqkZ-LxgClyvgwmZviiawi_9DrC8zDGSu_9tCbkxDwfICx0K4LhunT9fgzakjHCJMs5RwgZnlhXGBLXUmVtAl2GGeg', 'Content-Length': '51'} 
 body=b'{"id": 0, "atmNumber": "08397", "version": "Thabo"}' 
 </msg>
<msg timestamp="******** 09:05:27.972" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketing 
 status=200, reason=OK 
 headers={'Date': 'Thu, 30 May 2024 07:05:27 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body={"id":15,"cycle":{"id":24,"cycleStart":"2024-04-01T00:00:00","cycleEnd":"2024-06-30T00:00:00","interval":"Q2 2024","marketingSchedules":[]},"scheduleVersion":"v001Q22024","isCurrentVersion":false,"updatedBy":"","updateDate":"2024-02-05T11:40:07.786941","updateDescription":" created in new Cycle","approval":{"id":1,"version":"v001Q42022","approvalTime":"2022-09-28T00:00:00","approvedBy":"Libertine Makinta (ZA)","campaignHistories":[],"marketingSchedules":[]},"campaigns":[]} 
 </msg>
<msg timestamp="******** 09:05:27.972" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 09:05:27.976" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 09:05:27.541" endtime="******** 09:05:27.976"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">{"id":15,"cycle":{"id":24,"cycleStart":"2024-04-01T00:00:00","cycleEnd":"2024-06-30T00:00:00","interval":"Q2 2024","marketingSchedules":[]},"scheduleVersion":"v001Q22024","isCurrentVersion":false,"updatedBy":"","updateDate":"2024-02-05T11:40:07.786941","updateDescription":" created in new Cycle","approval":{"id":1,"version":"v001Q42022","approvalTime":"2022-09-28T00:00:00","approvedBy":"Libertine Makinta (ZA)","campaignHistories":[],"marketingSchedules":[]},"campaigns":[]}</msg>
<status status="PASS" starttime="******** 09:05:27.976" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">${response.content} = {"id":15,"cycle":{"id":24,"cycleStart":"2024-04-01T00:00:00","cycleEnd":"2024-06-30T00:00:00","interval":"Q2 2024","marketingSchedules":[]},"scheduleVersion":"v001Q22024","isCurrentVersion":false,"upd...</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.540" endtime="******** 09:05:27.978"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Then The rest service must return the expected message" library="RestCalls">
<arg>${EXPECTED_MESSAGE}</arg>
<kw name="Log" library="BuiltIn">
<arg>Response Message : ${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">Response Message : {"id":15,"cycle":{"id":24,"cycleStart":"2024-04-01T00:00:00","cycleEnd":"2024-06-30T00:00:00","interval":"Q2 2024","marketingSchedules":[]},"scheduleVersion":"v001Q22024","isCurrentVersion":false,"updatedBy":"","updateDate":"2024-02-05T11:40:07.786941","updateDescription":" created in new Cycle","approval":{"id":1,"version":"v001Q42022","approvalTime":"2022-09-28T00:00:00","approvedBy":"Libertine Makinta (ZA)","campaignHistories":[],"marketingSchedules":[]},"campaigns":[]}</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:05:27.978" level="INFO">"id":15</msg>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<kw name="Should Contain" library="BuiltIn">
<arg>As Strings ${response.content}</arg>
<arg>${EXPECTED_MESSAGE}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.978" endtime="******** 09:05:27.978"/>
</kw>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.978"/>
</kw>
<doc>Download Marketing Campaign(s)</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 09:05:27.304" endtime="******** 09:05:27.978"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 09:05:26.994" endtime="******** 09:05:31.241"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 09:05:31.231" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
