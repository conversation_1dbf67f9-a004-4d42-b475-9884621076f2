*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : njabulo.kubhe<PERSON>@absa.africa

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Update From Gasper Button Functionality

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Verify Update From Gasper Button Functionality
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user presses Update ATMs from Gasper

    And The user verifies Gasper ATM Last Update Element


| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |
| Verify Update From Gasper Button Functionality | Verify Update From Gasper Button Functionality     | Verify Update From Gasper Button Functionality |      VMS_UAT             |