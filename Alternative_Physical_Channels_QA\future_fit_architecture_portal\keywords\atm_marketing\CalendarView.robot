*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Test the Calendar view page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             XML
Library                                             DateTime
Library                                             String
Library                                             Collections

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/GenericMethods.robot
Resource                                            ../../keywords/common/DBUtility.robot



*** Variables ***
${CALENDAR_VIEW_LINK}                               xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span
${CAMPAIGN_VALENDAR_TABLE}                          css=body > app-root > app-sidenav > mat-sidenav-container > mat-sidenav-content > app-marketing-logs-view > full-calendar > div.fc-header-toolbar.fc-toolbar.fc-toolbar-ltr
${REFRESH_BTN}                                      id=refresh

${CAMPAIGN_NAME_FIELD}                              name=campaignName
${CAMPAIGN_BY_FIELD}                                name=campaignBy
${RECEIVE_DEVICE_TYPE}                              name=receiverDeviceType

${NEXT_BUTTON}                                      xpath=//span[contains(text(), 'Next')]
${CLOSE_BUTTON}                                     xpath=//span[contains(text(), 'Close')]
${CALENDAR_WITH_CAMPAIGN_DATE}                      id=testing
${LANGUAGE}                                         css= .not.mat-primary
${MARKETING_IMAGE}                                  css= .mat-card-content>img
${BACK_BUTTON}                                      xpath=//span[contains(text(), 'Back')]
${PREVIOUS_MONTH}                                   css=div .fc-icon
${NEXT_MONTH}                                       xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[2]
${CURRENT_MONTH_YEAR_LOCATOR}                       xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[2]/h2
${CALENDAR_MONTH_NAME_LOCATOR}                      css=div .fc-toolbar-title
${APC_PORTAL_CALENDAR}                              xpath=//table/descendant::*[contains(@class,'fc-scrollgrid-sync-table')]/tbody
${APC_PORTAL_CALENDAR_ROWS}                         xpath=//table/descendant::*[contains(@class,'fc-scrollgrid-sync-table')]/tbody/tr
${CAMPAIGN_START_DATE}
@{Q1_MONTHS}
@{Q2_MONTHS}
@{Q3_MONTHS}
@{Q4_MONTHS}
${SCHEDULE_VERSION_INFO}                            v001Q32024
${ACTIVE_CAMPAIGNS_BY_CURRENT_SCHEDULE_QUERY}       SELECT * FROM ATM_Marketing.Campaign where version in ('SCH_VERSION') and isActive = true  ORDER BY campaignStartDate desc
${ACTIVE_CAMPAIGNS_NOT_APPROVED_QUERY}              SELECT * FROM ATM_Marketing.Campaign where isApproved = false and isActive = true ORDER BY campaignStartDate desc
${ACTIVE_CAMPAIGNS_THAT_ARE_APPROVED_QUERY}         SELECT * FROM ATM_Marketing.Campaign where isApproved = true and isActive = true ORDER BY campaignStartDate desc
${CAMPAIGN_NAME_LOCATED}
${CAMPAIGN_SEARCH_COUNTER}                          8

*** Keywords ***
The user clicks on Calendar View link
    Log to console  --------------------------The user clicks Calendar View link
    SeleniumLibrary.Click Element  ${CALENDAR_VIEW_LINK}

    Wait Until Element Is Visible  ${CAMPAIGN_VALENDAR_TABLE}     5     Campaing scheduler calendar not shown

The user must be able to navigate through the calendar by using < & > buttons

    Log To Console    ---------------------------- validate that the user can navigate through the calendar by using < & > buttons

     GenericMethods.Wait for spinner to disapear

    Verify that the current Month's name is displayed correctly on the Calendar

    Verify that the previous Month's name is displayed correctly on the Calendar

     #Get the correct element to click for the next month's date
    ${webelements}=    Get WebElements    ${PREVIOUS_MONTH}

    #Navigate to next month date on the front end
    Click Element    ${webelements[1]}
    Sleep    5s

    Verify that the current Month's name is displayed correctly on the Calendar

    Verify that the next Month's name is displayed correctly on the Calendar


Verify that the current Month's name is displayed correctly on the Calendar

     #Verify that the current date element is displayed
    ${element_count}=        Get the number of displayed web elements   ${CALENDAR_MONTH_NAME_LOCATOR}
    Log To Console    ${element count}

    Return From Keyword If	  ${element_count} == 0

    IF   ${element_count} > 1
        Fail	More than 1 element was located for '${CALENDAR_MONTH_NAME_LOCATOR}'
    END


    #Get the date currently displayed on the calendar
    ${current_month_date}=    Get Text    ${CALENDAR_MONTH_NAME_LOCATOR}
    Log To Console    Current displayed Date is: ${current_month_date}

    #Get the current date from the system calendar
    ${current}    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    ${CURRENT_DATE} =	Convert Date	${current}    result_format=%B %Y
    Log To Console    Original Current generated date is: ${current}
    Log To Console    Current generated date is: ${CURRENT_DATE}

    #Verify that the displayed date and the calendar date are the same
    Should Be Equal As Strings    ${current_month_date}    ${CURRENT_DATE}


Verify that the previous Month's name is displayed correctly on the Calendar

    #Get the previous month
     #Verify that the previous\next date element is displayed
    ${element_count}=        Get the number of displayed web elements   ${PREVIOUS_MONTH}
    Log To Console    ${element count}

    Return From Keyword If	  ${element_count} == 0

    IF   ${element_count} > 2
        Fail	More than 1 element was located for '${PREVIOUS_MONTH}'
    ELSE IF  ${element_count} != 2
        Fail	Less than 2 elements were located for '${PREVIOUS_MONTH}'
    END

    #Get the correct element to click for the previous date
    ${webelements}=    Get WebElements    ${PREVIOUS_MONTH}

    #Navigate to previous month date on the front end
    Click Element    ${webelements[0]}
    Sleep    5s


    #Get the date currently displayed on the calendar
    ${previous_month_date_from_page}=    Get Text    ${CALENDAR_MONTH_NAME_LOCATOR}
    Log To Console    Current displayed Date is: ${previous_month_date_from_page}

    #Get the previous date from the system calendar
    ${current}    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    ${previous_month_from_calendar} =	Subtract Time From Date	${current}	31 days
    ${PREVIOUS_MONTH_DATE}=    Convert Date	${previous_month_from_calendar}    result_format=%B %Y


    #Verify that the displayed date and the calendar date are the same for the previous Month details
    Should Be Equal As Strings    ${PREVIOUS_MONTH_DATE}    ${previous_month_date_from_page}

Verify that the next Month's name is displayed correctly on the Calendar

    #Get the correct element to click for the previous date
    ${webelements}=    Get WebElements    ${PREVIOUS_MONTH}

    #Navigate to next month date on the front end
    Click Element    ${webelements[1]}
    Sleep    5s

   #Get the date currently displayed on the calendar
    ${next_month_date_from_page}=    Get Text    ${CALENDAR_MONTH_NAME_LOCATOR}
    Log To Console    Current displayed Date is: ${next_month_date_from_page}

    #Get the previous date from the system calendar
    ${current}    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    ${next_month_from_calendar} =	Add Time To Date	${current}	31 days
    ${NEXT_MONTH_DATE}=    Convert Date	${next_month_from_calendar}    result_format=%B %Y


    #Verify that the displayed date and the calendar date are the same for the previous Month details
    Should Be Equal As Strings    ${NEXT_MONTH_DATE}    ${next_month_date_from_page}

Search for a campaign on the APC Portal Calendar
    [Arguments]         ${CAMPAIGN_NAME}    ${CAMPAIGN_STATUS_TO_FIND}

    Log To Console    ---------------------------- user views details inside the calendar
    ${search_by_name}=  Convert To Boolean    ${False}
    ${search_by_status}=  Convert To Boolean    ${False}
    ${search_for_any_campaign}=  Convert To Boolean    ${False}


    #Check if the campaign name or campaign status must be used for the search
    IF   '${CAMPAIGN_NAME.replace('\n','').strip()}'!=''
        ${search_by_name}=  Convert To Boolean    ${True}
    ELSE IF    '${CAMPAIGN_STATUS_TO_FIND.replace('\n','').strip()}'!=''
         ${search_by_status}=  Convert To Boolean    ${True}
    ELSE
         ${search_for_any_campaign}=  Convert To Boolean    ${True}
    END

    Sleep    5s


    The user clicks on Calendar View link

    ${element_count}=        Get the number of displayed web elements   ${APC_PORTAL_CALENDAR}


    Return From Keyword If	  ${element_count} == 0

    IF   ${element_count} > 1
        Fail	More than 1 element was located for the calendar element
    END

    #Loop through the rows of the APC calendar table to get campaign names
    ${rows}=    SeleniumLibrary.Get Element Count    ${APC_PORTAL_CALENDAR_ROWS}  #Get the number of rows from the APC calendar
    FOR    ${row_num}    IN RANGE    1    ${rows+1}
        ${curr_row_element_path} =   Catenate    ${APC_PORTAL_CALENDAR_ROWS}   [${row_num}]
        ${col_element_path} =   Catenate    ${curr_row_element_path}       /td
        ${cols}=    SeleniumLibrary.Get Element Count    ${col_element_path}  #Get the number of columns from the current row
        FOR    ${col_num}    IN RANGE    1    ${cols+1}       #Loop through columns
            ${curr_col_element_path} =   Catenate    ${col_element_path}   [${col_num}]    #build element path for the current column
            ${campaign_date}=     SeleniumLibrary.Get Element Attribute    ${curr_col_element_path}    data-date     #Read the campaign(s) start date (day)
            ${campaign_date_element_details}=   Catenate    ${curr_col_element_path}   //*[contains(@class,'fc-daygrid-day-top')]      #build element for campaign day
            ${campaign_element_details}=   Catenate    ${curr_col_element_path}   //*[contains(@class,'fc-daygrid-day-events')]      #build element for campaign name(s)
            ${campaign_name_text}=     SeleniumLibrary.Get Text    ${campaign_element_details}     #Read the campaign name
            #If the campaign details text is not empty then loop through all active campaign for that day
            IF   '${campaign_name_text.replace('\n','').strip()}'!=''
                #Get an element for individual campaign(s) for the current month
                ${individual_campaigns_element_details}=   Catenate    ${campaign_element_details}   //*[contains(@class,'fc-daygrid-event-harness')]      #build element for campaign name
                #Create element list for all available campaigns
                ${current_month_campaigns}=    SeleniumLibrary.Get Element Count    ${individual_campaigns_element_details}

                #Log   @######################################@
                #Log   Start Date is for the below campaigns is :${campaign_date}
                #Log   The number of total campaigns for the day is :${current_month_campaigns}
                #Log   @######################################@

                 #Loop through individual campaigns and get their name
                FOR    ${campaign_element}    IN RANGE    1    ${current_month_campaigns+1}

                    #Create an element for the current campaign
                    ${curr_campaign_element_path} =   Catenate    ${individual_campaigns_element_details}   [${campaign_element}]
                    ${current_campaign_name_text}=     Get Text    ${curr_campaign_element_path}     #Read the current campaign name

                    #Get the campaign status
                    ${curr_campaign_status_element_path} =   Catenate    ${curr_campaign_element_path}   /a
                    ${element_style}=     SeleniumLibrary.Get Element Attribute    ${curr_campaign_status_element_path}    style
                    ${element_array}=     Split String    ${element_style}    ;
                    ${first_element_array}=  Split String    ${element_array}[0]    :
                    Log   split string : ${first_element_array}
                    ${formatted_campaign_status}=     Remove String    ${first_element_array}[1]     ${SPACE}
                    IF    '${formatted_campaign_status}' == 'green'
                        ${campaign_status}=   Set Variable   Approved
                    ELSE
                        ${campaign_status}=   Set Variable   Not Approved
                    END

                    #Verify if the required campaign is the required one
                    IF   ${search_by_name}

                        #Remove device name from the Campaign name that is displayed on the calendar
                        IF  '( ATM )' in '${current_campaign_name_text}' or '( ATM Idle' in '${current_campaign_name_text}'
                             IF    '( ATM )' in '${current_campaign_name_text}'
                                 ${actual_campaign_name_text}=    Fetch From Left    ${current_campaign_name_text}    ( ATM )
                             ELSE
                                 ${actual_campaign_name_text}=    Fetch From Left    ${current_campaign_name_text}    ( ATM Idle
                             END
                        ELSE IF  '( SSK )' in '${current_campaign_name_text}' or '( SSK Idle' in '${current_campaign_name_text}'
                             IF    '( SSK )' in '${current_campaign_name_text}'
                                 ${actual_campaign_name_text}=    Fetch From Left    ${current_campaign_name_text}    ( SSK )
                             ELSE
                                 ${actual_campaign_name_text}=    Fetch From Left    ${current_campaign_name_text}    ( SSK Idle
                             END

                        ELSE IF  '( BCD )' in '${current_campaign_name_text}' or '( BCD Idle' in '${current_campaign_name_text}'
                             IF    '( BCD )' in '${current_campaign_name_text}'
                                 ${actual_campaign_name_text}=    Fetch From Left    ${current_campaign_name_text}    ( BCD )
                             ELSE
                                 ${actual_campaign_name_text}=    Fetch From Left    ${current_campaign_name_text}    ( BCD Idle
                             END
                        END

                        ${required_campaign}=  Set Variable   ${CAMPAIGN_NAME.replace('\n','').strip()}
                        ${curr_campaign}=  Set Variable   ${actual_campaign_name_text.replace('\n','').strip()}
                        #Remove spaces from the required campaign name
                        @{req_campaign_dict}=   Split String    ${required_campaign}
                        ${final_req_campaign_name} =   Set Variable
                        ${space_found}=  Set Variable   ${False}
                        FOR    ${element}    IN    @{req_campaign_dict}
                            ${ele}=    Set Variable  ${element.replace('\n','').strip()}
                            IF  '${ele}' ==''
                                ${final_req_campaign_name}=  remove values from list  ${req_campaign_dict}  ${ele}
                                ${space_found}=  Set Variable   ${True}
                            END
                        END

                        IF   ${space_found} == ${False}
                             ${final_req_campaign_name}=  Convert To String  ${req_campaign_dict}
                        END
                        Log    final_req_campaign_name= ${final_req_campaign_name}
                        #Remove spaces from the current campaign name
                        @{curr_campaign_dict}=   Split String    ${curr_campaign}
                        ${final_curr_campaign_name} =   Set Variable    ''
                        ${space_found}=  Set Variable   ${False}
                        FOR    ${element}    IN    @{curr_campaign_dict}
                            ${ele}=    Set Variable  ${element.replace('\n','').strip()}
                            IF   '${ele}' ==''
                                ${final_curr_campaign_name}=  remove values from list  ${curr_campaign_dict}  ${ele}
                                ${space_found}=  Set Variable   ${True}
                            END
                        END

                        IF  ${space_found} == ${False}
                             ${final_curr_campaign_name}=  Convert To String  ${curr_campaign_dict}
                        END

                        Log    final_curr_campaign_name= ${final_curr_campaign_name}

                        IF  ${final_curr_campaign_name}== ${final_req_campaign_name}
                            Log   Campaign has been found....
                            Log   Campaign Name is : ${campaign_name_text}
                            Log   Campaign Status is : ${campaign_status}
                            Log   Start Date is for the campaign is :${campaign_date}
                            Set Environment Variable    CAMPAIGN_NAME_LOCATED        ${campaign_name_text}
                            ${CAMPAIGN_START_DATE} =   Set Global Variable   ${campaign_date}
                             #Click to view the campaign
                             Click Element    ${curr_campaign_element_path}
                             RETURN  'Campaign found'
                        END

                    ELSE IF   ${search_by_status}

                        ${required_state}=  Set Variable   '${CAMPAIGN_STATUS_TO_FIND.replace('\n','').strip()}'
                        ${curr_state}=  Set Variable   '${CAMPAIGN_STATUS.replace('\n','').strip()}'

                        IF  ${required_state}== ${curr_state}
                            Log   Campaign has been found....
                            Log   Campaign Name is : ${campaign_name_text}
                            Log   Campaign Status is : ${campaign_status}
                            Log   Start Date is for the campaign is :${campaign_date}

                            Set Environment Variable    CAMPAIGN_NAME_LOCATED        ${campaign_name_text}
                            ${CAMPAIGN_START_DATE} =   Set Global Variable   ${campaign_date}
                             #Click to view the campaign
                            Click Element    ${curr_campaign_element_path}
                            RETURN  'Campaign found'
                        END
                    ELSE
                        Log   Campaign has been found....
                        Log   Campaign Name is : ${campaign_name_text}
                        Log   Campaign Status is : ${campaign_status}
                        Log   Start Date is for the campaign is :${campaign_date}

                        Set Environment Variable    CAMPAIGN_NAME_LOCATED        ${current_campaign_name_text}
                        ${CAMPAIGN_START_DATE} =   Set Global Variable   ${campaign_date}
                        #Click to view the campaign
                        Click Element    ${curr_campaign_element_path}
                        RETURN  'Campaign found'
                    END
                END
                #Log   $=======================================$
                #Log   $=======================================$
            END
       END
    END

    RETURN  'Campaign Not found'


Get the color the campaign displayed on APC Calendar
    [Arguments]         ${CAMPAIGN_STATUS_TO_FIND}

    Log To Console    ---------------------------- user views details inside the calendar


    #Check if the campaign name or campaign status must be used for the search
    IF   '${CAMPAIGN_STATUS_TO_FIND.replace('\n','').strip()}'==''
        Fail  Please specify the campaign status that must be verified!
    END

    Sleep    5s


    The user clicks on Calendar View link

    ${element_count}=        Get the number of displayed web elements   ${APC_PORTAL_CALENDAR}


    Return From Keyword If	  ${element_count} == 0

    IF   ${element_count} > 1
        Fail	More than 1 element was located for the calendar element
    END


    #Loop through the rows of the APC calendar table to get campaign names
    ${rows}=    SeleniumLibrary.Get Element Count    ${APC_PORTAL_CALENDAR_ROWS}  #Get the number of rows from the APC calendar

    FOR    ${row_num}    IN RANGE    1    ${rows+1}
        ${curr_row_element_path} =   Catenate    ${APC_PORTAL_CALENDAR_ROWS}   [${row_num}]
        ${col_element_path} =   Catenate    ${curr_row_element_path}       /td
        ${cols}=    SeleniumLibrary.Get Element Count    ${col_element_path}  #Get the number of columns from the current row

        Log  number of cols: ${cols}
        FOR    ${col_num}    IN RANGE    1    ${cols+1}       #Loop through columns
            ${curr_col_element_path} =   Catenate    ${col_element_path}   [${col_num}]    #build element path for the current column
            ${campaign_date}=     SeleniumLibrary.Get Element Attribute    ${curr_col_element_path}    data-date     #Read the campaign(s) start date (day)
            ${campaign_date_element_details}=   Catenate    ${curr_col_element_path}   //*[contains(@class,'fc-daygrid-day-top')]      #build element for campaign day
            ${campaign_element_details}=   Catenate    ${curr_col_element_path}   //*[contains(@class,'fc-daygrid-day-events')]      #build element for campaign name(s)
            ${campaign_name_text}=     SeleniumLibrary.Get Text    ${campaign_element_details}     #Read the campaign name


            #If the campaign details text is not empty then loop through all active campaign for that day
            IF   '${campaign_name_text.replace('\n','').strip()}'!=''
                #Get an element for individual campaign(s) for the current month
                ${individual_campaigns_element_details}=   Catenate    ${campaign_element_details}   //*[contains(@class,'fc-daygrid-event-harness')]      #build element for campaign name
                #Create element list for all available campaigns
                ${current_month_campaigns}=    SeleniumLibrary.Get Element Count    ${individual_campaigns_element_details}

                #Log   @######################################@
                #Log   Start Date is for the below campaigns is :${campaign_date}
                #Log   The number of total campaigns for the day is :${current_month_campaigns}
                #Log   @######################################@

                 #Loop through individual campaigns and get their name
                FOR    ${campaign_element}    IN RANGE    1    ${current_month_campaigns+1}

                    #Create an element for the current campaign
                    ${curr_campaign_element_path} =   Catenate    ${individual_campaigns_element_details}   [${campaign_element}]
                    ${current_campaign_name_text}=     Get Text    ${curr_campaign_element_path}     #Read the current campaign name

                    #Get the campaign status
                    ${curr_campaign_status_element_path} =   Catenate    ${curr_campaign_element_path}   /a
                    ${element_style}=     SeleniumLibrary.Get Element Attribute    ${curr_campaign_status_element_path}    style
                    ${element_array}=     Split String    ${element_style}    ;
                    ${first_element_array}=  Split String    ${element_array}[0]    :

                    ${formatted_campaign_status}=     Remove String    ${first_element_array}[1]     ${SPACE}
                    IF    '${formatted_campaign_status}' == 'green'
                        ${campaign_status}=   Set Variable   Approved
                    ELSE
                        ${campaign_status}=   Set Variable   Not Approved
                    END

                    #Verify if the required campaign is the required one
                    ${required_state}=  Set Variable   '${CAMPAIGN_STATUS_TO_FIND.replace('\n','').strip()}'
                    ${curr_state}=  Set Variable   '${CAMPAIGN_STATUS.replace('\n','').strip()}'

                    IF  ${required_state}== ${curr_state}
                        Log   Campaign has been found....
                        Log   Campaign Name is : ${current_campaign_name_text}
                        Log   Campaign Status is : ${campaign_status}
                        Log   Start Date is for the campaign is :${campaign_date}

                        Set Environment Variable    CAMPAIGN_NAME_LOCATED        ${current_campaign_name_text}
                        ${CAMPAIGN_START_DATE} =   Set Global Variable   ${campaign_date}

                        RETURN  ${formatted_campaign_status}
                    END
                END
                #Log   $=======================================$
                #Log   $=======================================$
            END
       END
    END

    RETURN  Campaign Not found




The user clicks the "navigate to previous month" icon on the calendar
    #Verify that the previous\next date element is displayed
    ${element_count}=        Get the number of displayed web elements   ${PREVIOUS_MONTH}
    Log To Console    ${element count}

    Return From Keyword If	  ${element_count} == 0

    IF   ${element_count} > 2
        Fail	More than 1 element was located for '${PREVIOUS_MONTH}'
    ELSE IF  ${element_count} != 2
        Fail	Less than 2 elements were located for '${PREVIOUS_MONTH}'
    END

    #Get the correct element to click for the previous date
    ${webelements}=    SeleniumLibrary.Get WebElements    ${PREVIOUS_MONTH}

    #Navigate to previous month date on the front end
    SeleniumLibrary.Click Element    ${webelements[0]}
    Wait Until Element Is Visible    ${CALENDAR_MONTH_NAME_LOCATOR}


The user clicks the "navigate to next month" icon on the calendar

   #Verify that the previous\next date element is displayed
    ${element_count}=        Get the number of displayed web elements   ${PREVIOUS_MONTH}
    Log To Console    ${element count}

    Return From Keyword If	  ${element_count} == 0

    IF   ${element_count} > 2
        Fail	More than 1 element was located for '${PREVIOUS_MONTH}'
    ELSE IF  ${element_count} != 2
        Fail	Less than 2 elements were located for '${PREVIOUS_MONTH}'
    END

    #Get the correct element to click for the previous date
    ${webelements}=    SeleniumLibrary.Get WebElements    ${PREVIOUS_MONTH}

    #Navigate to previous month date on the front end
    Click Element    ${webelements[1]}
    Wait Until Element Is Visible    ${CALENDAR_MONTH_NAME_LOCATOR}




The user gets the active campaign from the APC Database
    #Search for a campaign on the database
    ${db_type}=   Set Variable   'MYSQL'

    ${schedule_version_query}=  Set Variable        select scheduleVersion from MarketingSchedule where isCurrentVersion = true
    ${SCHEDULE_VERSION_INFO}=      Execute SQL Query  ${db_type}  ${schedule_version_query}    True
    ${sch_v}=       Get From Dictionary    ${SCHEDULE_VERSION_INFO}    scheduleVersion
    #use the schedule version provided to get the campaigns from the database
    ${sql_query}=  Replace String    ${ACTIVE_CAMPAIGNS_BY_CURRENT_SCHEDULE_QUERY}    SCH_VERSION    ${sch_v}

    Log To Console    ${sql_query}


    #Get the current schedule version from the database
    ${data_base_campaigns}=      Execute SQL Query  ${db_type}  ${sql_query}    True
    ${campaign_name}=    Get From Dictionary    ${data_base_campaigns}    campaignName
    ${campaign_start_date}=    Get From Dictionary    ${data_base_campaigns}    campaignStartDate
    ${is_approved}=    Get From Dictionary    ${data_base_campaigns}    isApproved

    IF    ${is_approved} == 1
        ${campaign_status}=  Set Variable  Approved
    ELSE
        ${campaign_status}=  Set Variable  Not Approved
    END

    #Save the values on environment variables
    Set Environment Variable    DATABASE_CAMPAIGN_NAME        ${campaign_name}
    Set Environment Variable    DATABASE_CAMPAIGN_STATUS      ${campaign_status}
    Set Environment Variable    DATABASE_CAMPAIGN_START_DATE  ${campaign_start_date}


The user gets the approved campaign from the APC Database

    #use the schedule version provided to get the campaigns from the database
    ${sql_query}=  Replace String    ${ACTIVE_CAMPAIGNS_THAT_ARE_APPROVED_QUERY}    SCH_VERSION    ${SCHEDULE_VERSION_INFO}

    Log To Console    ${sql_query}

    #Search for a campaign on the database
    ${db_type}=   Set Variable   'MYSQL'

    #Get the current schedule version from the database
    ${data_base_campaigns}=      Execute SQL Query  ${db_type}  ${sql_query}    True
    ${campaign_name}=    Get From Dictionary    ${data_base_campaigns}    campaignName
    ${campaign_start_date}=    Get From Dictionary    ${data_base_campaigns}    campaignStartDate
    ${is_approved}=    Get From Dictionary    ${data_base_campaigns}    isApproved

    IF    ${is_approved} == 1
        ${campaign_status}=  Set Variable  Approved
    ELSE
        ${campaign_status}=  Set Variable  Not Approved
    END

    #Save the values on environment variables
    Set Environment Variable    DATABASE_CAMPAIGN_NAME        ${campaign_name}
    Set Environment Variable    DATABASE_CAMPAIGN_STATUS      ${campaign_status}
    Set Environment Variable    DATABASE_CAMPAIGN_START_DATE  ${campaign_start_date}



The user gets the un-approved campaign from the APC Database

    #use the schedule version provided to get the campaigns from the database
    ${sql_query}=  Replace String    ${ACTIVE_CAMPAIGNS_NOT_APPROVED_QUERY}    SCH_VERSION    ${SCHEDULE_VERSION_INFO}

    Log To Console    ${sql_query}

    #Search for a campaign on the database
    ${db_type}=   Set Variable   'MYSQL'

    #Get the current schedule version from the database
    ${data_base_campaigns}=      Execute SQL Query  ${db_type}  ${sql_query}    True
    ${campaign_name}=    Get From Dictionary    ${data_base_campaigns}    campaignName
    ${campaign_start_date}=    Get From Dictionary    ${data_base_campaigns}    campaignStartDate
    ${is_approved}=    Get From Dictionary    ${data_base_campaigns}    isApproved

    IF    ${is_approved} == 1
        ${campaign_status}=  Set Variable  Approved
    ELSE
        ${campaign_status}=  Set Variable  Not Approved
    END

    #Save the values on environment variables
    Set Environment Variable    DATABASE_CAMPAIGN_NAME        ${campaign_name}
    Set Environment Variable    DATABASE_CAMPAIGN_STATUS      ${campaign_status}
    Set Environment Variable    DATABASE_CAMPAIGN_START_DATE  ${campaign_start_date}



The user must be able to preview the campaign found on the database using APC Portal

    ${database_campaign_name}=   Get Environment Variable    DATABASE_CAMPAIGN_NAME
    ${database_campaign_status}=    Get Environment Variable    DATABASE_CAMPAIGN_STATUS
    ${database_campaign_start_date}=    Get Environment Variable    DATABASE_CAMPAIGN_START_DATE

    Log To Console    Campaign Name to verify on FE is: ${database_campaign_name}
    Log To Console    Campaign Start Date is: ${campaign_start_date}
    Log To Console    Campaign Status to verify on FE is: ${database_campaign_status}

    #verify if campaign month is current or not
    ${date_is_in_past}=    Date is in the Past      ${database_campaign_start_date}

    Log To Console    Date is in the Past?: ${date_is_in_past}
    ${Campaign_found_bool}=   Set Variable   ${False}


    #Search for the campaign on APC Calendar using the current month plus 3 previous or past months
    FOR    ${counter}    IN RANGE    1    ${CAMPAIGN_SEARCH_COUNTER}

        Capture Page Screenshot     Calendar_Display_${counter}.png

        ${campign_search_results}=    Search for a campaign on the APC Portal Calendar      ${database_campaign_name}     ${database_campaign_status}

        #If the campaign is found then exit the Loop
        IF    ${campign_search_results} == 'Campaign found'
            ${Campaign_found_bool}=   Set Variable   ${True}
            Exit For Loop
        END

        ${webelements}=    SeleniumLibrary.Get WebElements    ${PREVIOUS_MONTH}

        IF    ${date_is_in_past}  # Search for the campaign using APC current and previous three months
            Log To Console    In Past
             #Navigate to previous month date on the front end
            Click Element    ${webelements[0]}

        ELSE
            Log To Console    In Future
             #Navigate to next month date on the front end
            Click Element    ${webelements[1]}

        END
        Sleep    5s


        Log To Console    Counter: ${counter}
     END


    IF    ${Campaign_found_bool} == $False
        Fail	Campaign was not found on the APC Portal			# Fails with the given message.
    ELSE
        GenericMethods.Wait for spinner to disapear

        Page Should Contain Element                     ${CAMPAIGN_NAME_FIELD}
        Page Should Contain Element                     ${CAMPAIGN_BY_FIELD}
        Page Should Contain Element                     ${RECEIVE_DEVICE_TYPE}
        Page Should Contain Element                     ${NEXT_BUTTON}
        Page Should Contain Element                     ${CLOSE_BUTTON}
        Page Should Contain Element                     ${CALENDAR_WITH_CAMPAIGN_DATE}
        Capture page screenshot  CampaignPreview_Page1.png

        Click Element    ${NEXT_BUTTON}
        Sleep    5s

        Page Should Contain Element                     ${LANGUAGE}

        #Get the number of languages displayed for the campaign

        @{webelements}=    SeleniumLibrary.Get WebElements    ${LANGUAGE}
        ${language_webelements_count}=   SeleniumLibrary.Get Element Count        ${LANGUAGE}

        FOR    ${element}    IN    @{webelements}
            ${ele_text}=  SeleniumLibrary.Get Text    ${element}
            Log    '${element}' language image is displayed for the campaign
        END

        #Get the number of images displayed
        ${image_webelements_count}=   SeleniumLibrary.Get Element Count        ${MARKETING_IMAGE}

        Should Be Equal As Strings    ${language_webelements_count}    ${image_webelements_count}
        Page Should Contain Element                     ${BACK_BUTTON}
        Capture page screenshot  CampaignPreview_Page2.png


        Click Element    ${BACK_BUTTON}
        Sleep    5s


        Page Should Contain Element                     ${CAMPAIGN_NAME_FIELD}
        Page Should Contain Element                     ${CAMPAIGN_BY_FIELD}
        Page Should Contain Element                     ${RECEIVE_DEVICE_TYPE}
        Page Should Contain Element                     ${NEXT_BUTTON}
        Page Should Contain Element                     ${CLOSE_BUTTON}
        Page Should Contain Element                     ${CALENDAR_WITH_CAMPAIGN_DATE}
        Capture page screenshot  CampaignPreview_Page1.1.png

        Click Element    ${CLOSE_BUTTON}

        Capture page screenshot  CampaignPreview_Page1.png

        ${CAMPAIGN_NAME_LOCATED}=   Get Environment Variable    CAMPAIGN_NAME_LOCATED

        Pass Execution  Campaign named: '${database_campaign_name}' with the status of: '${database_campaign_status}' and start date: '${database_campaign_start_date}' was found on the APC Portal			# Passes with the given message.


    END


The user must be able to verify that the approved Campaigns are displayed in a green color
    #Load the database variables
    ${database_campaign_name}=   Get Environment Variable    DATABASE_CAMPAIGN_NAME
    ${database_campaign_status}=    Get Environment Variable    DATABASE_CAMPAIGN_STATUS
    ${database_campaign_start_date}=    Get Environment Variable    DATABASE_CAMPAIGN_START_DATE

    Log To Console    Campaign Name to verify on FE is: ${database_campaign_name}
    Log To Console    Campaign Start Date is: ${database_campaign_start_date}
    Log To Console    Campaign Status to verify on FE is: ${database_campaign_status}

    #verify if campaign month is current or not
    ${date_is_in_past}=    Date is in the Past      ${database_campaign_start_date}

    Log To Console    Date is in the Past?: ${date_is_in_past}
    ${Campaign_found_bool}=   Set Variable   ${False}


    #Search for the campaign on APC Calendar using the current month plus 3 previous or past months
    FOR    ${counter}    IN RANGE    1    ${CAMPAIGN_SEARCH_COUNTER}

        ${results}=    Get the color the campaign displayed on APC Calendar     ${database_campaign_status}

        #If the campaign is found then exit the Loop
        IF    '${results}' != 'Campaign Not found'
            ${Campaign_found_bool}=   Set Variable   ${True}
            ${verified_status_color}=  Set Variable  ${results}
            Exit For Loop
        END

        ${webelements}=    SeleniumLibrary.Get WebElements    ${PREVIOUS_MONTH}

        IF    ${date_is_in_past}  # Search for the campaign using APC current and previous three months
            Log To Console    In Past
             #Navigate to previous month date on the front end
            Click Element    ${webelements[0]}

        ELSE
            Log To Console    In Future
             #Navigate to next month date on the front end
            Click Element    ${webelements[1]}

        END
        Sleep    5s


        Log To Console    Counter: ${counter}
     END


    IF    ${Campaign_found_bool} == $False
        Fail	Campaign with status '${database_campaign_status}' was not found on the APC Portal			# Fails with the given message.
    ELSE
        #Verify that the approved campaign is displayed in green color
        Should Be Equal As Strings    green    ${verified_status_color}

    END


The user must be able to verify that the un-approved Campaigns are displayed in a grey color
    #Load the database variables
    ${database_campaign_name}=   Get Environment Variable    DATABASE_CAMPAIGN_NAME
    ${database_campaign_status}=    Get Environment Variable    DATABASE_CAMPAIGN_STATUS
    ${database_campaign_start_date}=    Get Environment Variable    DATABASE_CAMPAIGN_START_DATE

    Log To Console    Campaign Name to verify on FE is: ${database_campaign_name}
    Log To Console    Campaign Start Date is: ${database_campaign_start_date}
    Log To Console    Campaign Status to verify on FE is: ${database_campaign_status}

    #verify if campaign month is current or not
    ${date_is_in_past}=    Date is in the Past      ${database_campaign_start_date}


    Log To Console    Date is in the Past?: ${date_is_in_past}
    ${Campaign_found_bool}=   Set Variable   ${False}


    #Search for the campaign on APC Calendar using the current month plus 3 previous or past months
    FOR    ${counter}    IN RANGE    1    ${CAMPAIGN_SEARCH_COUNTER}

        ${results}=    Get the color the campaign displayed on APC Calendar     ${database_campaign_status}

        #If the campaign is found then exit the Loop
        IF    '${results}' != 'Campaign Not found'
            ${Campaign_found_bool}=   Set Variable   ${True}
            ${verified_status_color}=  Set Variable  ${results}
            Exit For Loop
        END

        ${webelements}=    SeleniumLibrary.Get WebElements    ${PREVIOUS_MONTH}

        IF    ${date_is_in_past}  # Search for the campaign using APC current and previous three months
            Log To Console    In Past
             #Navigate to previous month date on the front end
            Click Element    ${webelements[0]}

        ELSE
            Log To Console    In Future
             #Navigate to next month date on the front end
            Click Element    ${webelements[1]}

        END
        Sleep    5s


        Log To Console    Counter: ${counter}
     END


    IF    ${Campaign_found_bool} == $False
        Fail	Campaign with status '${database_campaign_status}' was not found on the APC Portal			# Fails with the given message.
    ELSE
        #Verify that the approved campaign is displayed in green color
        Should Be Equal As Strings    grey    ${verified_status_color}

    END




#*** Test Cases ***
#Unit Test
#    View a campaign on the APC Portal Calendar     ${SCHEDULE_VERSION_INFO}