*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Documentation  View complaint/compliment details
Suite Setup                                         Set up environment variables

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                            String

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../keywords/VMSPage/Main.robot

*** Variables ***

*** Keywords ***
Main Page VMS Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks navigate to main

    And The user gets front end data

    #And The user gets database data

    #Then The front end data must be the same as the database data

*** Test Cases ***
| Main Page validation | Main Page VMS Validation     | Validates Top 10 ATMs with the highest calls for this month  |      VMS_UAT             |