*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       This is the test suite for creating an ATM Marketing Campaign using the Controller

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/api/RestCalls.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


#Run the script
#robot -d reports/controllers tests/TC_01_GET_CAPTURE_CAMPAIGN_CONTROLLER.robot

*** Variables ***
${SUITE NAME}               ATM Marketing Controllers Suite
${IS_HEADLESS_BROWSER}      No




*** Keywords ***
Create marketing campaign
    [Arguments]        ${DOCUMENTATION}    ${TESTRAIL_TESTCASE_ID}    ${DATA_FILE}    ${BASE_URL}    ${SERVICE_PATH}    ${EXPECTED_STATUS_CODE}    ${JSON_RESPONSE_REASON}    ${EXPECTED_MESSAGE}    &{KW_ARGS}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable                                   TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user prepares a json payload                      ${SUITE_NAME}       ${DATA_FILE}    &{KW_ARGS}
    When The user creates a rest session                        ${BASE_URL}
    And The user makes Post Rest Call                           ${SERVICE_PATH}     ${DATA_FILE}    ${EXPECTED_STATUS_CODE}
    And The service returns http status                         ${EXPECTED_STATUS_CODE}      ${JSON_RESPONSE_REASON}
    Then The rest service must return the expected message      ${EXPECTED_MESSAGE}

| *** Test Cases ***                                                                                                                                          |               *DOCUMENTATION*                   |  *TESTRAIL_TESTCASE_ID* |      *DATA_FILE*                            | *BASE_URL*                    | *SERVICE_PATH*                    | *EXPECTED_STATUS_CODE*           | *JSON_RESPONSE_REASON* | *EXPECTED_MESSAGE*                    | *KW_ARGS*                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| FFT - Controllers - Create campaign- is the campaign targeted - yes - campaign targeted - ATM - English and Afrikaans       | Create marketing campaign     | Create campaign- is the campaign targeted - yes |     T155057477	      |   ATMMarketingCampaign                      | APC_API_UAT_BASE_URL          | ATMMarketingCampaign              | 200                              | OK                     | Successfully created new campaign     | campaignName= S09005_Campaign_Created_by_automation_script  | campaignBy=Thabo Benjamin Setuke (ZA) | campaignStartDate=0 | campaignEndDate=5 | imageList[0]:marketingImage=images/3048_2010091absagenericatms_easter365x470-65eb2169a9de2_en.jpg      | imageList[0]:language:language=English | imageList[1]:marketingImage=images/MarketingE_en_7_af.jpg | imageList[1]:language:language=Afrikaans | isTargetted=True  | isTargetRegion=False | targetRegionOrAtm=S09005   | updatedBy=Thabo Benjamin Setuke (ZA) |
| FFT - Controllers - Create campaign - is the campaign targeted - yes - campaign targeted - Region - English and Afrikaans   | Create marketing campaign     | Create campaign- is the campaign targeted - yes |     T155057490          |   ATMMarketingCampaign                      | APC_API_UAT_BASE_URL          | ATMMarketingCampaign              | 200                              | OK                     | Successfully created new campaign     | campaignName= Gauteng_Campaign_Created_by_automation_script | campaignBy=Thabo Benjamin Setuke (ZA) | campaignStartDate=0 | campaignEndDate=5 | imageList[0]:marketingImage=images/3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg  | imageList[0]:language:language=English | imageList[1]:marketingImage=images/MarketingE_en_7_af.jpg | imageList[1]:language:language=Afrikaans | isTargetted=True  | isTargetRegion=True  | targetRegionOrAtm=Gauteng | updatedBy=Thabo Benjamin Setuke (ZA) |
| FFT - Controllers - Create campaign - is the campaign targeted - No - Language - English and Afrikaans                      | Create marketing campaign     | Create campaign- is the campaign targeted - yes |     T155057491	      |   ATMMarketingCampaign                      | APC_API_UAT_BASE_URL          | ATMMarketingCampaign              | 200                              | OK                     | Successfully created new campaign     | campaignName= Generic_Campaign_Created_by_automation_script | campaignBy=Thabo Benjamin Setuke (ZA) | campaignStartDate=0 | campaignEndDate=5 | imageList[0]:marketingImage=images/3050_2010091absagenericatms_humanrights365x470-65eb2169baa56_en.jpg | imageList[0]:language:language=English | imageList[1]:marketingImage=images/MarketingE_en_7_af.jpg | imageList[1]:language:language=Afrikaans | isTargetted=False | isTargetRegion=True  | targetRegionOrAtm=Gauteng | updatedBy=Thabo Benjamin Setuke (ZA) |
