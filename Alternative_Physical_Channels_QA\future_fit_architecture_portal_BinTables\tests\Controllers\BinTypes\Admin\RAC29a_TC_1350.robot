*** Settings ***
# Author Name               : Thabo
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/DeleteBinTypebyId_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
Delete Bin Type by ID
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}  ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}    ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    IF    '${CURR_BIN_TYPE_NAME}' != '${EMPTY}'
        Get the Bin Type ID from the database  ${CURR_BIN_TYPE_NAME}
        ${CURR_BIN_TYPE_NAME}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_NAME}
        ${CURR_BIN_TYPE_DESC}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_DESCRIPTION}
        ${BIN_TYPE_ID}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_ID}
    ELSE
        Get the random Bin Type to delete from the DB
        ${CURR_BIN_TYPE_NAME}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_NAME}
        ${CURR_BIN_TYPE_DESC}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_DESCRIPTION}
        ${BIN_TYPE_ID}=      Set Variable    ${DATABASE_RANDOM_BIN_TYPE_ID}
    END

    Given The User sends a DELETE Request to delete a Bin Type by ID         ${BASE_URL}  ${BIN_TYPE_ID}
    When The service returns an expected status code                         ${EXPECTED_STATUS_CODE}
    Then The deleted bin must be de-activated on the database                ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}

| *** Test Cases ***                                                                                     |             *DOCUMENTATION*    		     |    *BASE_URL*                  | *CURR_BIN_TYPE_NAME*            | *CURR_BIN_TYPE_DESC*            |   *EXPECTED_STATUS_CODE*
| Verify the DeleteBINtypeById API successfully deletes a Bin Type.            | Delete Bin Type by ID   | Delete Bin Type by ID and verify response   |                                | Thabo Bin Type2              | ${EMPTY}                        |       200
