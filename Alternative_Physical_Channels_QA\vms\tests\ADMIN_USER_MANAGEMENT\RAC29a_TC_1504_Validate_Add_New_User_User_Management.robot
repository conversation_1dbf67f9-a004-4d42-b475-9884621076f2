*** Settings ***
#Author Name               : TH<PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS User Creation
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${USER_NAME}       ${NAME}      ${USER_ROLE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And The user Adds a new VMS User    ${USER_NAME}       ${NAME}      ${USER_ROLE}
    And The user navigates to Admin - User Management
    And Searches for existing user  ${USER_NAME}
    Then The created user must be found on VMS Application and Database    ${USER_NAME}    ${NAME}     ${USER_ROLE}




| *Test Case*                                                                                     |      *DOCUMENTATION*                         | *TEST_ENVIRONMENT*   |  *USER_NAME*    |  *NAME*                          |  *USER_ROLE*      |
# | Validate +Add New User- User Management: Create a VMS user with a 'Browse' Role Test Case                | VMS User Creation             | Create a VMS user with 'Browse' Role         |    VMS_UAT           |    AB0540to     |  Automation User Browser         |    Browse         |
# | Validate +Add New User- User Management: Create a VMS user with a 'User' Role Test Case                  | VMS User Creation             | Create a VMS user with 'User' Role           |    VMS_UAT           |    AB0541to     |  Automation User                 |    User           |
# | Validate +Add New User- User Management: Create a VMS user with a 'Supervisor' Role Test Case            | VMS User Creation             | Create a VMS user with 'Supervisor' Role     |    VMS_UAT           |    AB0542to     |  Automation User Supervisor      |    Supervisor     |
| Validate +Add New User- User Management: Create a VMS user with a 'Administrator' Role Test Case         | VMS User Creation             | Create a VMS user with 'Administrator' Role  |    VMS_UAT           |    AB0543to     |  Automation User Administrator   |    Administrator  |