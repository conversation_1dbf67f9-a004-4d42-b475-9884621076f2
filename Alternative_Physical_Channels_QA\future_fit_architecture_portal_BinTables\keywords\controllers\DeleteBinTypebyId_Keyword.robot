*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            ../../keywords/controllers/resources/bintypes/DeleteBinTypeByID.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***
${GLOBAL_BIN_TYPE_ID}
${SQL_GET_DELETED_BIN_TYPE_DETAILS}    SELECT * FROM BinDbs.BinTypes order by DeletedDate desc

*** Keywords ***
The User sends a DELETE Request to delete a Bin Type by ID
    [Arguments]     ${BASE_URL}     ${BIN_TYPE_ID}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
    Log To Console     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create DeleteBinTypeByID Instance    ${base_url}      ${BIN_TYPE_ID}   #Create an instance of

    #${rest_request}=        Build Rest Request       ${base_url}     ${BIN_NUMBER}     ${ACTION}  #Instantiate the CreateRequest class
    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log To Console     ${endpoint}
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log To Console     ${params}
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   DELETE
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method}   headers=${headers}    params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    Log To Console     ${REST_RESPONSE}

    #Created an instance for the Response object
    Create ReadApiResponse Instance

Create DeleteBinTypeByID Instance
    [Arguments]    ${BASE_URL}  ${BIN_TYPE_ID}
    ${instance}=    Evaluate    DeleteBinTypeByID.CreateRESTRequest('${BASE_URL}','${BIN_TYPE_ID}')    modules=DeleteBinTypeByID
    RETURN    ${instance}



The User sends a Get Request to search for bins using a Bin Number
    [Arguments]     ${BASE_URL}     ${BIN_NUMBER}     ${ACTION}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
    Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create SearchBinsByNumber Instance    ${base_url}     ${BIN_NUMBER}     ${ACTION}   #Create an instance of

    #${rest_request}=        Build Rest Request       ${base_url}     ${BIN_NUMBER}     ${ACTION}  #Instantiate the CreateRequest class
    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   method=${method} headers=${headers}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance



The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'deletebintypebyid' REST API call failed, the returned status is '${status_code}'



The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}

    IF    '${EXPECTED_ERROR_MESSAGE}' == '[]'
        Verify that the API retruned an empty response
        RETURN
    END


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_detail}'     '${EXPECTED_ERROR_MESSAGE}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       BinNumber   binNumber   actionDate  binTypeIds  binUploadRequests     action

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${EXPECTED_ERROR_MESSAGE}' in '${api_error_message_fields}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Upload' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.

Verify that the API retruned an empty response
    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    #Verify that the response is empty
    ${verification_passed}=      Run Keyword And Return Status    Verify if values are equal     '[]'     '${bin_object_instace}'

    Run Keyword If    not ${verification_passed}
    ...    Fail     The API did not return an empty response!


The expected Bin Number details are retuned by the API Response
    [Arguments]     ${BIN_ID}   ${BIN_NUMBER}   ${ACTION_DATE}  ${BIN_TYPE_ID}  ${BIN_TYPE}


    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}

    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${binTypeId_must_be_verified}=    Set Variable If  '${BIN_TYPE_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binTypeId_verification_passed}=    Set Variable If  ${binTypeId_must_be_verified}     ${False}     ${True}

    ${binType_must_be_verified}=    Set Variable If  '${BIN_TYPE}' != '${EMPTY}'     ${True}     ${False}
    ${binType_verification_passed}=    Set Variable If  ${binType_must_be_verified}     ${False}     ${True}

    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Details
    Log Many    ${bin_object_instace}
    Log     '################################################'

    #Loop through all returned Bins to verify data
    FOR    ${index}    ${element}    IN ENUMERATE    @{bin_object_instace}
        Log    ${index}: ${element}
        ${binNumber_data}=        Get the Bin Number Details     ${element}
        Log    '${binNumber_data}'
        #IF the BIN Number is found then verify its details
        IF    '${binNumber_data}' == '${BIN_NUMBER}'
            Log     '################################################'
            ${binNumber_verification_passed}=      Set Variable    ${True}
            ${binNumber_verified}=      Set Variable    ${True}
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$

            ${binId_data}=            Get the Bin ID Details     ${element}
            #Check if the Bin ID must be verified against the user's data.
            IF    ${binID_must_be_verified}
                ${binID_verification_passed}=      Run Keyword And Return Status    Verify if values are equal     '${BIN_ID}'     '${binId_data}'
            END

            ${binActionDate_data}=    Get the Bin Action Date Details     ${element}
            #Check if the Action Date must be verified against the user's data
            IF    ${actionDate_must_be_verified}
                ${actionDate_verification_passed}=      Run Keyword And Return Status    Verify if values are equal     '${ACTION_DATE}'     '${binActionDate_data}'
            END

            #Loop through all Bin Types of the current Bin to verify data
            ${binTypes_instance}=       Get the Bin Types Details       ${element}
            FOR   ${binType_index}    ${binType_element}    IN ENUMERATE    @{binTypes_instance}
                Log    ${binType_index}: ${binType_element}
                Log     '################################################'


                ${binTypeId_data}=      Get the Bin Type ID Details     ${binType_element}
                Log    '${binTypeId_data}'


                ${binType_data}=        Get the Bin Type Details     ${binType_element}
                Log    '${binType_data}'


                IF  ${binTypeId_must_be_verified}
                    ${bin_type_id_array}=     Split String    ${BIN_TYPE_ID}     separator=,
                    FOR    ${type_id}    IN    @{bin_type_id_array}
                        #Check if the Bin Type ID must be verified against the user's data.
                        ${binTypeId_verification_passed}=      Run Keyword And Return Status    Verify if values are equal     '${type_id}'     '${binTypeId_data}'
                        Run Keyword If    ${binTypeId_verification_passed}
                        ...    Exit For Loop
                    END
                END

                IF    ${binType_must_be_verified}
                        ${bin_type_name_array}=     Split String    ${BIN_TYPE}     separator=,
                        FOR    ${type_name}    IN    @{bin_type_name_array}
                           #Check if the Bin Type ID must be verified against the user's data.
                            ${binType_verification_passed}=      Run Keyword And Return Status    Verify if values are equal     '${type_name}'     '${binType_data}'
                            Run Keyword If    ${binType_verification_passed}
                            ...    Exit For Loop
                        END
                END


                Log     '################################################'
            END
            Log     '################################################'

            #The loop will be exited if all the parameters have been verified against the response
            Run Keyword If    ${binNumber_verified}
            ...    Exit For Loop
            #$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
        END
    END

    #Verify that all the data that needed verification was verified successfully

    Run Keyword If    ${binNumber_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the API response.


    IF  ${binID_must_be_verified}
        Run Keyword If    ${binID_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    Verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END



    IF  ${actionDate_must_be_verified}
        Run Keyword If    ${actionDate_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    Verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binTypeId_must_be_verified}
        Run Keyword If    ${binTypeId_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The Bin Type ID value: '${BIN_TYPE_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    Verification for the Bin Type ID value: '${BIN_TYPE_ID}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

    IF  ${binType_must_be_verified}
        Run Keyword If    ${binType_verification_passed} == ${False}
    ...    Run Keyword And Continue On Failure  Fail  The Bin Type value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was not found on the API response.
    ...  ELSE
    ...    Log    Verification for the Bin Type value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was successful.
    END

#The below keywords are for Database Verifications
The Bin Number details must exist on the Bin Database
    [Arguments]     ${BIN_ID}   ${BIN_NUMBER}   ${ACTION_DATE}  ${BIN_TYPE_ID}  ${BIN_TYPE}


    #Check which parameters are to be verified against the response
    ${binNumber_must_be_verified}=    Set Variable If  '${BIN_NUMBER}' != '${EMPTY}'     ${True}     ${False}
    ${binNumber_verified}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}
    ${binNumber_verification_passed}=    Set Variable If  ${binNumber_must_be_verified}     ${False}     ${True}

    ${binID_must_be_verified}=    Set Variable If  '${BIN_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binID_verification_passed}=    Set Variable If  ${binID_must_be_verified}     ${False}     ${True}

    ${actionDate_must_be_verified}=    Set Variable If  '${ACTION_DATE}' != '${EMPTY}'     ${True}     ${False}
    ${actionDate_verification_passed}=    Set Variable If  ${actionDate_must_be_verified}     ${False}     ${True}

    ${binTypeId_must_be_verified}=    Set Variable If  '${BIN_TYPE_ID}' != '${EMPTY}'     ${True}     ${False}
    ${binTypeId_verification_passed}=    Set Variable If  ${binTypeId_must_be_verified}     ${False}     ${True}

    ${binType_must_be_verified}=    Set Variable If  '${BIN_TYPE}' != '${EMPTY}'     ${True}     ${False}
    ${binType_verification_passed}=    Set Variable If  ${binType_must_be_verified}     ${False}     ${True}

    ${db_results}=     Get the Bin details for Bins to be Reviwed from the Database    ${BIN_NUMBER}

    # Ensure the results are not empty
    ${dr_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_results}

    Run Keyword If    not ${dr_results_contain_data}
    ...     Fail    Database results for bin number: '${BIN_NUMBER}' returned no results

   # Check that results is a list using Evaluate
    ${is_list}=    Evaluate    isinstance(${db_results}, list)    # This checks if results is a list
    Should Be True    ${is_list}

    #Loop through the returned DB results and verify them against the user's supplied parameters
    ${num_rows}=    Get Length    ${db_results}

    #The below variables are used to detect and filter out the duplicate columns
    ${bin_type_already_read_value}=       Set Variable     ${EMPTY}
    ${tobeActionedBy_Value}=    Set Variable    ${EMPTY}

    FOR    ${row}    IN    @{db_results}
        ${bin_binType_data}=             Get Column Data By Name       ${row}       binType
        IF    '${bin_type_already_read_value}' == '${EMPTY}'
            ${bin_type_already_read_value}=   Set Variable     ${bin_binType_data}
        ELSE
             IF    '${bin_type_already_read_value}' == '${bin_binType_data}'
                 CONTINUE
             END
        END

        ${bin_toActionedBy_data}=        Get Column Data By Name       ${row}       toBeActionedBy
        ${bin_toActionedBy_data}=        Get Bin To Be Actioned By Text     ${bin_toActionedBy_data}

        IF    '${tobeActionedBy_Value}' == '${EMPTY}'
            ${tobeActionedBy_Value}=   Set Variable     ${bin_toActionedBy_data}
        ELSE
             IF    '${tobeActionedBy_Value}' != '${bin_toActionedBy_data}'
                 CONTINUE
             END
        END

        #Verify the bin details
        ${binID_data}=            Get Column Data By Name       ${row}       Bin_ID
        ${binNumber_data}=        Get Column Data By Name       ${row}       binNumber
        ${binActionDate_data}=    Get Column Data By Name       ${row}       actionDate
        ${BinTypeId_data}=        Get Column Data By Name       ${row}       binTypeID
        ${BinTypeName_data}=      Get Column Data By Name       ${row}       binType

        ${binID_verification_passed}=           Run Keyword And Return Status
        ...  Verify if values are equal     '${BIN_ID}'     '${binId_data}'
        ${binNumber_verification_passed}=       Run Keyword And Return Status
        ...  Verify if values are equal     '${BIN_NUMBER}'     '${binNumber_data}'
        ${actionDate_verification_passed}=   Run Keyword And Return Status
        ...  Verify if values are equal     '${ACTION_DATE}'     '${binActionDate_data}'



        ${bin_type_id_array}=     Split String    ${BIN_TYPE_ID}     separator=,

        FOR    ${type_id}    IN    @{bin_type_id_array}
            ${binTypeId_verification_passed}=       Run Keyword And Return Status
             ...  Verify if values are equal     '${type_id}'     '${BinTypeId_data}'
            Run Keyword If    ${binTypeId_verification_passed}
            ...    Exit For Loop
        END


        ${bin_type_name_array}=     Split String    ${BIN_TYPE}     separator=,

        FOR    ${type_name}    IN    @{bin_type_name_array}
            ${binType_verification_passed}=       Run Keyword And Return Status
            ...  Verify if values are equal     '${type_name}'     '${BinTypeName_data}'
            Run Keyword If    ${binType_verification_passed}
            ...    Exit For Loop
        END

        #Verify that all the data that needed verification was verified successfully

        Run Keyword If    ${binNumber_verification_passed} == ${False}
        ...    Run Keyword And Continue On Failure  Fail  The BIN Number: '${BIN_NUMBER}' was not found on the Database.
        ...  ELSE
        ...    Log    The BIN Number: '${BIN_NUMBER}' was found on the Database.


        IF  ${binID_must_be_verified}
            Run Keyword If    ${binID_verification_passed} == ${False}
            ...    Run Keyword And Continue On Failure  Fail  The BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
            ...  ELSE
            ...    Log    Database verification for the BIN ID: '${BIN_ID}', for BIN Number: '${BIN_NUMBER}' was successful.

            ${binID_verification_passed}=      Set Variable      ${False}
        END



        IF  ${actionDate_must_be_verified}
            Run Keyword If    ${actionDate_verification_passed} == ${False}
            ...    Run Keyword And Continue On Failure  Fail  The Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
            ...  ELSE
            ...    Log    Database verification for the Action Date value: '${ACTION_DATE}', for BIN Number: '${BIN_NUMBER}' was successful.

            ${actionDate_verification_passed}=      Set Variable      ${False}
        END

        IF  ${binTypeId_must_be_verified}
            Run Keyword If    ${binTypeId_verification_passed} == ${False}
            ...    Run Keyword And Continue On Failure  Fail  The Bin Type ID value: '${BIN_TYPE_ID}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
            ...  ELSE
            ...    Log    Database verification for the Bin Type ID value: '${BIN_TYPE_ID}', for BIN Number: '${BIN_NUMBER}' was successful.

            ${binTypeId_verification_passed}=      Set Variable      ${False}
        END

        IF  ${binType_must_be_verified}
            Run Keyword If    ${binType_verification_passed} == ${False}
            ...    Run Keyword And Continue On Failure  Fail  The Bin Type value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was not found on the Database.
            ...  ELSE
            ...    Log    Database verification for the Bin Type value: '${BIN_TYPE}', for BIN Number: '${BIN_NUMBER}' was successful.
            ${binType_verification_passed}=      Set Variable      ${False}
        END
    END





# The below keywords are used to interact with the API POJOS
Create SearchBinsByNumber Instance
    [Arguments]    ${BASE_URL}  ${BIN_NUMBER}   ${ACTION}
    ${instance}=    Evaluate    SearchBinsByNumber.CreateRESTRequest('${BASE_URL}','${BIN_NUMBER}','${ACTION}')    modules=SearchBinsByNumber
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords

Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}



Get Response Status Code
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

#Keywords to read error response fields
Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keyword to read successful response
Get the Bin Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bin_details
    RETURN    ${result}



Get the Bin ID Details
     [Arguments]     ${BINS_INSTNANCE}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_id
    RETURN    ${result}

Get the Bin Number Details
     [Arguments]     ${BINS_INSTNANCE}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_bin_number
    RETURN    ${result}

Get the Bin Action Date Details
     [Arguments]     ${BINS_INSTNANCE}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_action_date
    RETURN    ${result}

Get the Bin Types Details
     [Arguments]     ${BINS_INSTNANCE}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_bin_types
    RETURN    ${result}


Get the Bin Type ID Details
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_bin_type_id
    RETURN    ${result}

Get the Bin Type Details
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_bin_type
    RETURN    ${result}

Get the random Bin Type to delete from the DB

    ${db_bins_count}=   Set Variable    1
    ${db_bin_type_count_details}=         Get the count on active Bin Type from the Database
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_count_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    The are no active bin types found in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_count_details}    0    # Get the first row
    ${db_bin_type_count}=                        Get Column Data By Name       ${first_row_results}       total
    ${list_of_bin_types_already_read}=      Create List
    ${list_length}=     Get Length   ${list_of_bin_types_already_read}
    WHILE    '${list_length}' != '${db_bin_type_count}'

        ${db_bin_type_details}=         Get the random Bin Type details from the Database
        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

         Run Keyword If    not ${db_results_contain_data}
        ...     Fail    The random bin type was not found in the database.

        ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
        ${db_bin_type_id}=                        Get Column Data By Name       ${first_row_results}       Id
        ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
        ${db_bin_type_description}=                 Get Column Data By Name       ${first_row_results}       Description
        ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
        ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

        ${db_bin_type_details}=         Get the count of all Bins Linked to a Bin Type      ${db_bin_type_id}
        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}
        ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
        ${db_bins_count}=                        Get Column Data By Name       ${first_row_results}       Total_linked_bins

        IF    '${db_bins_count}' != '0'
           Append To List      ${list_of_bin_types_already_read}       ${db_bin_type_name}
           ${list_length}=     Get Length   ${list_of_bin_types_already_read}
        ELSE
            Exit For Loop
        END


    END

    #If the bin type was found
    IF    '${db_bins_count}' == '0'
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_ID}               ${db_bin_type_id}
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_NAME}               ${db_bin_type_name}
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_DESCRIPTION}        ${db_bin_type_description}
    ELSE
        Fail    There is no Bin Type that can be deleted, all bin types are linked to Bins.
    END


The deleted bin must be de-activated on the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    ${bin_type_name_provided} == ${False}
    ...    Fail  Please provide the name of the Bin Type that was deleted!


    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_TO_ADD}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no bin type callled '${BIN_TYPE_TO_ADD}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    #Verify that the Bin Type details are correct
    Run Keyword If    '${db_bin_type_name}' == '${BIN_TYPE_NAME}'
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is the same as the provided Bin Type name.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The bin type name: '${db_bin_type_name}' from the DB is not the same as the provided Bin Type. The provided Bin Type name is '${BIN_TYPE_NAME}'.


    Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
    ...    Fail  The bin type name: '${db_bin_type_name}' is still active in the database.
    ...  ELSE
    ...    Log Many  The bin type name: '${db_bin_type_name}' is not active in the database. The soft-delete was successful.


Get the Bin Type ID from the database
    [Arguments]     ${BIN_TYPE_NAME}
     ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_NAME}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}       msg=The Bin Type named '${BIN_TYPE_NAME}' does not exist in the database, hence it cannot be edited

     Run Keyword If    not ${db_results_contain_data}
    ...  Fail  There is no bin type callled '${BIN_TYPE_NAME}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_id}=                        Get Column Data By Name       ${first_row_results}       Id
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_description}=                 Get Column Data By Name       ${first_row_results}       Description
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${False}
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is active.

    #If the bin type is inactive then fetch the random Bin Type
    IF    ${db_bin_type_name_is_deleted_boolean} == ${True}
         Log Many   The bin type name: '${db_bin_type_name}' from the DB is not active, a random active bin type will be searched for on the database.
         Get the random Bin Type to delete from the DB
    ELSE
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_ID}               ${db_bin_type_id}
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_NAME}               ${db_bin_type_name}
        Set Global Variable    ${DATABASE_RANDOM_BIN_TYPE_DESCRIPTION}        ${db_bin_type_description}
    END


The bin type must still be active in the database
    [Arguments]     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}

    ${BIN_TYPE_TO_ADD}=      Remove Quotes       ${BIN_TYPE_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_ADD}' == ''             ${False}
         ...       '${BIN_TYPE_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_ADD}' != ''             ${True}

    Run Keyword If    ${bin_type_name_provided} == ${False}
    ...    Fail  Please provide the name of the Bin Type that was deleted!


    ${db_bin_type_details}=         Get the Bin Type details from the Database using the Bin Type Name      ${BIN_TYPE_TO_ADD}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no bin type callled '${BIN_TYPE_TO_ADD}' in the database.

    ${first_row_results}=                       Get From List    ${db_bin_type_details}    0    # Get the first row
    ${db_bin_type_name}=                        Get Column Data By Name       ${first_row_results}       Name
    ${db_bin_type_name_is_deleted}=             Get Column Data By Name       ${first_row_results}       IsDeleted
    ${db_bin_type_name_is_deleted_boolean}=     Check If One Or Zero        ${db_bin_type_name_is_deleted}

    #Verify that the Bin Type details are correct
    Run Keyword If    '${db_bin_type_name}' == '${BIN_TYPE_NAME}'
    ...    Log Many  The bin type name: '${db_bin_type_name}' from the DB is the same as the provided Bin Type name.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The bin type name: '${db_bin_type_name}' from the DB is not the same as the provided Bin Type. The provided Bin Type name is '${BIN_TYPE_NAME}'.


    Run Keyword If    ${db_bin_type_name_is_deleted_boolean} == ${True}
    ...    Fail  The bin type name: '${db_bin_type_name}' is not active in the database.
    ...  ELSE
    ...    Log Many  The bin type name: '${db_bin_type_name}' is active in the database.
