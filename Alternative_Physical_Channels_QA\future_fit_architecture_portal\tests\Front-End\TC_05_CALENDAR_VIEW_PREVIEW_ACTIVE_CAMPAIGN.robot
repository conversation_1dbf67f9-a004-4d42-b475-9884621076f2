*** Settings ***
#Author Name               : <PERSON>hab<PERSON> Setuke
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Testing Future Fit APC Portal Calendar Preview

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../Keywords/atm_marketing/CalendarView.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keywords ***
Validates Calendar View Page
   
    [Arguments]  ${DOCUMENTATION}       ${LOGON_USER}       ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user gets the active campaign from the APC Database
    When The user logs into Future Fit Architecture portal  ${TEST_ENVIRONMENT}       Edge  drivers\chromedriver.exe  ${LOGON_USER}
    And The user clicks on Calendar View link
    Then The user must be able to preview the campaign found on the database using APC Portal


| *** Test Cases ***                                                                                             |      *DOCUMENTATION*                             |      *LOGON_USER*         |   *TEST_ENVIRONMENT*        |
| Business User - Calendar View- Preview                             |    Validates Calendar View Page    |   Testing Future Fit APC Portal Calendar Preview |    BUSINESS_USER          |       APC_UAT               |
