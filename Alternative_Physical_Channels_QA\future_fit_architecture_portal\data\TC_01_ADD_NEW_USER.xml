<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20240829 08:09:13.154">
   <suite name="VMS Regression" id="s1" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_USER_MANAGEMENT\TC_01_ADD_NEW_USER.robot">
      <test name="Create a VMS user with a 'Browse' Role Test Case" id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg>Variable '${APPLICATION_USERNAME}' not found.

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) No browser is open.</arg>
            </arguments>
            <msg timestamp="20240829 08:09:16.712" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20240829 08:09:17.255" status="FAIL" starttime="20240829 08:09:16.712"/>
         </kw>
         <kw library="Selenium" name="When The user navigates to Admin - User Management and Adds a User">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg>Variable '${APPLICATION_USERNAME}' not found.

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) No browser is open.</arg>
            </arguments>
            <msg timestamp="20240829 08:09:17.255" level="INFO">When The user navigates to Admin - User Management and Adds a User</msg>
            <status endtime="20240829 08:09:17.255" status="NOT RUN" starttime="20240829 08:09:17.255"/>
         </kw>
         <kw library="Selenium" name="Then The created user must be added to the system">
            <doc>Create a VMS user with 'Browse' Role</doc>
            <arguments>
               <arg>Variable '${APPLICATION_USERNAME}' not found.

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) No browser is open.</arg>
            </arguments>
            <msg timestamp="20240829 08:09:17.255" level="INFO">Then The created user must be added to the system</msg>
            <status endtime="20240829 08:09:17.255" status="NOT RUN" starttime="20240829 08:09:17.255"/>
         </kw>
         <tags>
            <tag>Create a VMS user with a 'Browse' Role Test Case</tag>
         </tags>
         <status endtime="20240829 08:09:27.383" critical="yes" status="FAIL" starttime="20240829 08:09:16.712"/>
      </test>
      <status endtime="20240829 08:09:27.386" status="FAIL" starttime="20240829 08:09:13.154"/>
   </suite>
   <statistics>
      <total>
         <stat pass="0" fail="1">Critical Tests</stat>
         <stat pass="0" fail="1">All Tests</stat>
      </total>
      <tag>
         <stat pass="0" fail="1">Create a VMS user with a 'Browse' Role Test Case</stat>
      </tag>
      <suite>
         <stat name="VMS Regression" pass="0" fail="1" id="s1">VMS Regression</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
