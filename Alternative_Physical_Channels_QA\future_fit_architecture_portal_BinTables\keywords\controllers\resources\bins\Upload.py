import json
import re


from robot.api.deco import keyword


class Upload:
    def __init__(self, **kwargs):
        """
            The constructor initializes the class with a dictionary of bins and processes them to generate
            the necessary data for the JSON request. It extracts the bin number, action date, and bin type IDs for each bin,
            regardless of how many bins are passed or their indices.

            Arguments:

            kwargs: A dictionary containing the bin data, where keys represent the bin (e.g., bin1, bin2, etc.)
            and values represent the bin number. The dictionary should also include dateX and binIdsX for each bin.

            bins = {
                'bin1': '12345',
                'date1': '2024-11-11',
                'binIds1': ['uuid1', 'uuid2'],
                'bin2': '67890',
                'date2': '2024-11-12',
                'binIds2': ['uuid3', 'uuid4']
            }
            generator = Upload(bins)

            """

        # Initialize bins as an empty list
        self.bins = []

        # Loop through all bins keys and create bin data
        for key, value in kwargs.items():
            # Check if the key starts with 'bin' (e.g., 'bin1', 'bin2', ...)
            if key.startswith("bin"):
                # Extract the bin index using regex (to allow for any number format)
                match = re.match(r"bin(\d+)", key)
                if match:
                    bin_index = match.group(1)  # Extract the index as string (e.g., '1', '2', '123')

                    # Now, retrieve associated data for this bin
                    bin_date = kwargs.get(f"date{bin_index}")  # Get the corresponding date (e.g., 'date1', 'date2')
                    bin_ids = kwargs.get(
                        f"binIds{bin_index}")  # Get the corresponding bin IDs (e.g., 'binIds1', 'binIds2')

                    # Make sure bin_ids is a list (in case it was passed as a string)
                    if isinstance(bin_ids, str):
                        bin_ids = bin_ids.strip("[]").split(",")  # Convert the string to a list

                    self.bins.append({
                        "binNumber": value,  # bin1, bin2, ... contain the bin number
                        "actionDate": bin_date,
                        "binTypeIds": bin_ids
                    })

                    # Append bin data if all required info is valid
                    #if bin_date and isinstance(bin_ids, list):
                       # self.bins.append({
                       #     "binNumber": value,  # bin1, bin2, ... contain the bin number
                       #     "actionDate": bin_date,
                       #     "binTypeIds": bin_ids
                       # })

    def get_json_request(self):
        # Return the bins in JSON format
        return json.dumps(self.bins, indent=4)

    @keyword
    def create_bin_request(self, **kwargs):
        """
        This method processes the bins and returns the JSON request.
        """
        self.__init__(**kwargs)  # Initialize the class with dynamic kwargs
        return self.get_json_request()

    @keyword
    def get_endpoint(self, domain):
        path = "/api/v1/bintables/entry/bins/upload"
        url = f"{domain}{path}"
        return url

    @keyword
    def get_headers(self):
        headers = {
            'Content-Type': "application/json",
            'Accept': "*/*"
        }

        return headers





