from typing import List, Union


class CreateRESTRequest:
    def __init__(self, domain, server_number):
        self.domain = domain
        self.server_number = server_number

        self.params = {
            "serverNumber": self.server_number,
        }

    def get_endpoint(self):
        path = "/api/v1/bintables/all/managementinformation/getbinoverviewmetrics"
        url = f"{self.domain}{path}"
        return url

    def get_params(self):
        return self.params

