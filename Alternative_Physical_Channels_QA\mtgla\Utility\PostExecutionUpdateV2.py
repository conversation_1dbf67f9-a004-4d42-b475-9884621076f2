from acintegration.QMetryIntegration import QMetryIntegration
from jproperties import Properties
from xml.dom import minidom
import os
import time
from pathlib import Path




class PostExecutionUpdateV2:
    ROBOT_LISTENER_API_VERSION = 2

    global test_names_dict, test_id_dict, test_source_dict, test_doc_dict, test_start_time_dict, test_end_time_dict, test_status_dict, test_message_dict, test_count, step_names_dict, step_docs_dict, step_status_dict, step_start_time_dict, step_end_time_dict, step_count

    test_names_dict = {}
    test_id_dict = {}
    test_source_dict = {}
    test_doc_dict = {}
    test_start_time_dict = {}
    test_end_time_dict = {}
    test_status_dict = {}
    test_message_dict = {}
    step_names_dict = {}
    step_docs_dict = {}
    step_status_dict = {}
    step_start_time_dict = {}
    step_end_time_dict = {}
    test_count = 1
    step_count = 1

    def __init__(self):
        pass

    def end_suite(self, name, attributes):
        global SUITE_SOURCE, SUITE_NAME, SUITE_ID, SUITE_START_TIME, SUITE_END_TIME, SUITE_STATUS, pass_count, pass_count_2, fail_count, fail_count_2
        SUITE_ID = None

        SUITE_NAME = name
        if SUITE_ID is None:
            print("Suite ID not initialised...")
        SUITE_ID = str(attributes['id']).strip(' ')

        if SUITE_ID is None:
            print("Suite ID not initialised...")
        else:
            print("Suite ID is initialised...")

        SUITE_START_TIME = str(attributes['starttime']).strip(' ')
        SUITE_END_TIME = str(attributes['endtime']).strip(' ')
        SUITE_STATUS = str(attributes['status']).strip(' ')

        try:
            if SUITE_ID is not None:
                print("The variable 'SUITE_ID' exists.")
                if SUITE_ID != "s1":
                    return
            else:
                print("The variable 'SUITE_ID' does not exist.")
                return

        except NameError:
            print("The variable 'SUITE_ID' does not exist.")
            return

        root = minidom.Document()

        robot = root.createElement('robot')
        robot.setAttribute('rpa', 'false')
        robot.setAttribute('generator', 'Robot 3.2.1 (Python 3.5.4 on win32)')
        robot.setAttribute('generated', SUITE_START_TIME)

        suite = root.createElement('suite')
        # suite.setAttribute('source', SUITE_SOURCE)
        suite.setAttribute('name', SUITE_NAME)
        suite.setAttribute('id', SUITE_ID)

        # Create test cases elements
        for test_key, test_value in test_names_dict.items():
            # print("Key: ", test_key, "Value: ", "--->", test_value)
            test = root.createElement('test')
            test.setAttribute('name', test_value)

            SUITE_SOURCE = test_source_dict[test_value]
            suite.setAttribute('source', SUITE_SOURCE)

            test_id = test_id_dict[test_value]
            test.setAttribute('id', test_id)
            test_doc = test_doc_dict[test_value]
            # Create steps elements
            if str(os.getenv('UPLOAD_TEST_STEPS')).strip().upper() == 'YES':
                # print("Adding steps")
                for step_key, step_value in step_names_dict.items():
                    if test_id in step_key:
                        kw = root.createElement('kw')
                        kw.setAttribute('library', "Selenium")
                        kw.setAttribute('name', step_value)

                        # step_doc = step_docs_dict[step_key]
                        doc = root.createElement('doc')

                        doc.appendChild(root.createTextNode(test_doc))
                        kw.appendChild(doc)

                        arguments = root.createElement('arguments')
                        arg = root.createElement('arg')

                        test_message = test_message_dict[test_value]
                        arg.appendChild(root.createTextNode(test_message))
                        arguments.appendChild(arg)

                        # arg = root.createElement('arg')
                        # arg.appendChild(root.createTextNode('chrome'))
                        # arguments.appendChild(arg)
                        kw.appendChild(arguments)

                        msg = root.createElement('msg')
                        step_start_time = step_start_time_dict[step_key]
                        msg.setAttribute('timestamp', step_start_time)
                        msg.setAttribute('level', 'INFO')
                        msg.appendChild(root.createTextNode(step_value))
                        kw.appendChild(msg)

                        status = root.createElement('status')
                        step_end_time = step_end_time_dict[step_key]
                        status.setAttribute('endtime', step_end_time)
                        step_status = step_status_dict[step_key]
                        status.setAttribute('status', step_status)

                        status.setAttribute('starttime', step_start_time)
                        kw.appendChild(status)  # Step status

                        # Add test step to Test Case
                        test.appendChild(kw)

            # Elements for Test tags and status
            tags = root.createElement('tags')
            tag = root.createElement('tag')
            tag.appendChild(root.createTextNode(test_value))

            tags.appendChild(tag)
            test.appendChild(tags)

            status = root.createElement('status')
            test_end_time = test_end_time_dict[test_value]
            status.setAttribute('endtime', test_end_time)
            status.setAttribute('critical', 'yes')
            test_status = test_status_dict[test_value]
            status.setAttribute('status', test_status)
            test_start_time = test_start_time_dict[test_value]
            status.setAttribute('starttime', test_start_time)
            test.appendChild(status)

            suite.appendChild(test)

        status = root.createElement('status')
        status.setAttribute('endtime', SUITE_END_TIME)
        status.setAttribute('status', SUITE_STATUS)
        status.setAttribute('starttime', SUITE_START_TIME)
        suite.appendChild(status)

        robot.appendChild(suite)

        statistics = root.createElement('statistics')

        pass_count = 0
        fail_count = 0
        # Get the number of tests that passed and failed
        for test_status_value in test_status_dict.values():

            if test_status_value == 'PASS':
                pass_count = pass_count + 1
            elif test_status_value == 'FAIL':
                fail_count = fail_count + 1

        stat_1_tag = 'Critical Tests'
        stat_2_tag = 'All Tests'

        total = root.createElement('total')
        stat = root.createElement('stat')
        pass_count_str = str(pass_count)
        fail_count_str = str(fail_count)
        stat.setAttribute('pass', pass_count_str)
        stat.setAttribute('fail', fail_count_str)
        stat.appendChild(root.createTextNode(stat_1_tag))
        total.appendChild(stat)

        stat = root.createElement('stat')
        stat.setAttribute('pass', pass_count_str)
        stat.setAttribute('fail', fail_count_str)
        stat.appendChild(root.createTextNode(stat_2_tag))
        total.appendChild(stat)
        statistics.appendChild(total)

        tag = root.createElement('tag')
        # Get the name(s) and number of tests that passed and failed
        for test_name in test_names_dict.values():
            pass_count_2 = 0
            fail_count_2 = 0

            for test_status_name, test_status_value in test_status_dict.items():

                if test_name == test_status_name:

                    if test_status_value == 'PASS':
                        pass_count_2 = pass_count_2 + 1
                    elif test_status_value == 'FAIL':
                        fail_count_2 = fail_count_2 + 1

            stat = root.createElement('stat')
            pass_count_2_str = str(pass_count_2)
            fail_count_2_str = str(fail_count_2)

            stat.setAttribute('pass', pass_count_2_str)
            stat.setAttribute('fail', fail_count_2_str)
            stat.appendChild(root.createTextNode(test_name))
            tag.appendChild(stat)
        statistics.appendChild(tag)

        suite = root.createElement('suite')
        stat = root.createElement('stat')
        stat.setAttribute('name', SUITE_NAME)
        pass_count_str = str(pass_count)
        fail_count_str = str(fail_count)
        stat.setAttribute('pass', pass_count_str)
        stat.setAttribute('fail', fail_count_str)
        stat.setAttribute('id', SUITE_ID)
        stat.appendChild(root.createTextNode(SUITE_NAME))
        suite.appendChild(stat)
        statistics.appendChild(suite)
        robot.appendChild(statistics)

        errors = root.createElement('errors')
        robot.appendChild(errors)
        root.appendChild(robot)

        xml_str = root.toprettyxml(indent="   ", encoding="utf-8")

        # Get the root directory assuming this script is in a subdirectory
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
        print("Project Root Directory:", project_root)

        file_path = str(os.getenv('ROBOT_FILE_PATH'))

        robot_file_path = os.path.join(project_root, file_path)

        print('robot file path is', robot_file_path)

        with open(robot_file_path, "w") as f:
            f.write(xml_str.decode("utf-8"))

        # Read Qmetry project variables

        entityType = self.get_property('entityType')
        testsuiteName = self.get_property('testsuiteName')
        testsuiteId = self.get_property('testsuiteId')
        projectID = self.get_property('projectID')
        apikey = self.get_property('apikey')

        kwargs = {'entityType': entityType, 'testsuiteName': testsuiteName, 'testsuiteId': testsuiteId,
                  'projectID': projectID, 'apikey': apikey}

        time.sleep(5)

        print("Uploading Results...")

        # Update QMetry
        qmetryIntegration = QMetryIntegration()

        print(qmetryIntegration.upload_results(robot_file_path, **kwargs))
        print(
            f"--------------------------Qmetry Test Suite updated for test case {TEST_NAME}")

    def end_keyword(self, name, attributes):
        global step_count, TEST_STEP_START_TIME, TEST_STEP_END_TIME, TEST_STEP_STATUS

        if str(attributes['kwname']).strip(' ').startswith('Given') or str(attributes['kwname']).strip(' ').startswith(
                'And') or str(attributes['kwname']).strip(' ').startswith('When')  or str(attributes['kwname']).strip(' ').startswith('Then'):
            step_names_dict[TEST_ID + '_Step_' + str(step_count)] = str(attributes['kwname']).strip(' ')
            step_docs_dict[TEST_ID + '_Step_' + str(step_count)] = str(attributes['doc']).strip(' ')
            step_status_dict[TEST_ID + '_Step_' + str(step_count)] = str(attributes['status']).strip(' ')
            step_start_time_dict[TEST_ID + '_Step_' + str(step_count)] = str(attributes['starttime']).strip(' ')
            step_end_time_dict[TEST_ID + '_Step_' + str(step_count)] = str(attributes['endtime']).strip(' ')

            step_count = step_count + 1
            os.environ['TEST_STEP_NAME'] = str(attributes['kwname']).strip(' ')

    def start_test(self, name, attrs):
        global TEST_NAME, TEST_ID, test_count

        TEST_NAME = name
        TEST_ID = attrs['id']

        test_names_dict['Test_Name_' + str(test_count)] = TEST_NAME
        test_id_dict[TEST_NAME] = TEST_ID

        test_count = test_count + 1

    def end_test(self, name, attrs):
        global TEST_DOC, TEST_START_TIME, TEST_END_TIME, TEST_STATUS, TEST_MESSAGE, step_count, TEST_SOURCE
        TEST_SOURCE = attrs['source']
        TEST_DOC = attrs['doc']
        TEST_START_TIME = attrs['starttime']
        TEST_END_TIME = attrs['endtime']
        TEST_STATUS = attrs['status']
        TEST_MESSAGE = attrs['message']
        step_count = 1

        test_source_dict[TEST_NAME] = TEST_SOURCE

        test_doc_dict[TEST_NAME] = TEST_DOC
        test_doc_dict[TEST_NAME] = TEST_DOC
        test_start_time_dict[TEST_NAME] = TEST_START_TIME
        test_end_time_dict[TEST_NAME] = TEST_END_TIME
        test_status_dict[TEST_NAME] = TEST_STATUS
        test_message_dict[TEST_NAME] = TEST_MESSAGE

    def clear_testrail_credentials(self):
        if 'UPLOAD_TEST_STEPS' in os.environ:  os.environ.pop('UPLOAD_TEST_STEPS')
        if 'TESTRAIL_PASSWORD' in os.environ:  os.environ.pop('TESTRAIL_PASSWORD')

    def get_property(self, str_property_name):
        configs = Properties()
        #chrome_path = root + 'common_utilities/app-config.properties'
        root = Path(__file__).parent.parent
        configs_file_path = os.path.join(root, 'utility/app-config.properties')
        with open(configs_file_path, 'rb') as config_file:
            configs.load(config_file)
            try:
                if configs[str_property_name] is not None:
                    property_retrieved = configs.get(str_property_name).data
                    # print("Property value fetched is: ", property_retrieved)
                    return property_retrieved
                else:
                    print("Property named: " + str_property_name + " does not exist on file!")
                    return None
            except KeyError as ke:
                print(f'{ke}, lookup key was: ' + str(str_property_name))
                return None

