<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 10:15:45.162" rpa="false" schemaversion="4">
<suite id="s1" name="Future Fit Portal" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_03_CAPTURE_CAMPAIGN.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:15:47.808" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:15:47.808" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\POST_CAMPAIGN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:15:47.808" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:15:47.808" level="INFO">Environment variable 'BASE_URL' set to value 'APC_DEV'.</msg>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.808"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:15:47.818" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:15:47.818" level="INFO">Environment variable called 'SUITE_DIRECTORY', does not exist.</msg>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:15:47.818" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:15:47.818" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:15:47.818"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.808" endtime="******** 10:15:47.818"/>
</kw>
<test id="s1-t1" name="FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English And Afrikaans_09005" line="62">
<kw name="Create marketing campaign">
<arg>Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans</arg>
<arg>Yes</arg>
<arg>ATM</arg>
<arg>S11782 ABSA LAB, 270 Republic Road - Randburg</arg>
<arg>${EMPTY}</arg>
<arg>Idle</arg>
<arg>ATM</arg>
<arg>4</arg>
<arg>6</arg>
<arg>English,Afrikaans</arg>
<arg>images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg,images\\MarketingA_af_2.jpg</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 10:15:47.822" level="INFO">Set test documentation to:
Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans</msg>
<status status="PASS" starttime="******** 10:15:47.822" endtime="******** 10:15:47.822"/>
</kw>
<if>
<branch type="IF" condition="'${CAMPAIGN_NAME}' == '${EMPTY}' or '${CAMPAIGN_NAME}' == ''">
<kw name="Generate Random Campaign Name" library="CommonUtils">
<var>${random_word}</var>
<msg timestamp="******** 10:15:48.073" level="INFO">${random_word} = DiminuentCampaign</msg>
<status status="PASS" starttime="******** 10:15:47.822" endtime="******** 10:15:48.073"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${CAMPAIGN_NAME}</var>
<arg>${random_word}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:15:48.073" level="INFO">${CAMPAIGN_NAME} = DiminuentCampaign</msg>
<status status="PASS" starttime="******** 10:15:48.073" endtime="******** 10:15:48.073"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.822" endtime="******** 10:15:48.073"/>
</branch>
<status status="PASS" starttime="******** 10:15:47.822" endtime="******** 10:15:48.073"/>
</if>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 10:15:48.197" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 10:15:48.073" endtime="******** 10:15:48.197"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 10:15:48.197" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:15:48.197" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</branch>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</if>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:15:48.197" level="INFO">${handle} = msedge.exe</msg>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.197"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 10:15:48.228" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 10:15:48.859" level="INFO">${rc_code} = 128</msg>
<msg timestamp="******** 10:15:48.859" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.859"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:15:48.860" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="******** 10:15:48.860" endtime="******** 10:15:48.862"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.860" endtime="******** 10:15:48.862"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:48.862"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:15:48.862" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:48.862"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 10:15:48.862" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:48.862"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 10:15:48.862" level="INFO">${browser_name} = EDGE</msg>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:48.862"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:15:48.862" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:48.862"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<msg timestamp="******** 10:15:48.862" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x00000227FFA7C140&gt;</msg>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:48.862"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="******** 10:15:48.862" endtime="******** 10:15:48.862"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 10:15:48.865" endtime="******** 10:15:48.865"/>
</kw>
<status status="NOT RUN" starttime="******** 10:15:48.862" endtime="******** 10:15:48.865"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="******** 10:15:48.868" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<status status="PASS" starttime="******** 10:15:48.865" endtime="******** 10:15:52.711"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.865" endtime="******** 10:15:52.711"/>
</branch>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:52.711"/>
</if>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:52.711"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.712" endtime="******** 10:15:52.712"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.713" endtime="******** 10:15:52.713"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.714" endtime="******** 10:15:52.714"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.714" endtime="******** 10:15:52.714"/>
</kw>
<status status="NOT RUN" starttime="******** 10:15:52.713" endtime="******** 10:15:52.714"/>
</branch>
<status status="NOT RUN" starttime="******** 10:15:52.713" endtime="******** 10:15:52.715"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.715" endtime="******** 10:15:52.715"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.715" endtime="******** 10:15:52.715"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.716" endtime="******** 10:15:52.716"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.716" endtime="******** 10:15:52.716"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.716" endtime="******** 10:15:52.717"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.717" endtime="******** 10:15:52.717"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.718" endtime="******** 10:15:52.718"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.718" endtime="******** 10:15:52.718"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.718" endtime="******** 10:15:52.719"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.719" endtime="******** 10:15:52.719"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.719" endtime="******** 10:15:52.719"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.719" endtime="******** 10:15:52.719"/>
</kw>
<status status="NOT RUN" starttime="******** 10:15:52.712" endtime="******** 10:15:52.719"/>
</branch>
<status status="PASS" starttime="******** 10:15:48.862" endtime="******** 10:15:52.720"/>
</if>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:15:52.720"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Get Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" starttime="******** 10:15:52.721" endtime="******** 10:15:52.721"/>
</kw>
<msg timestamp="******** 10:15:52.721" level="INFO">${url_exists_on_env_var} = True</msg>
<status status="PASS" starttime="******** 10:15:52.720" endtime="******** 10:15:52.721"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:15:52.721" level="INFO">${BASE_URL} = APC_DEV</msg>
<status status="PASS" starttime="******** 10:15:52.721" endtime="******** 10:15:52.721"/>
</kw>
<status status="PASS" starttime="******** 10:15:52.721" endtime="******** 10:15:52.721"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:15:52.721" endtime="******** 10:15:52.721"/>
</kw>
<status status="NOT RUN" starttime="******** 10:15:52.721" endtime="******** 10:15:52.721"/>
</branch>
<status status="PASS" starttime="******** 10:15:52.721" endtime="******** 10:15:52.721"/>
</if>
<kw name="Read Config Property" library="CommonUtils">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 10:15:52.749" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 10:15:52.749" level="INFO">${base_url} = https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 10:15:52.721" endtime="******** 10:15:52.749"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${base_url}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:15:52.753" level="INFO">https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 10:15:52.753" endtime="******** 10:15:52.753"/>
</kw>
<kw name="Load" library="Login">
<arg>${BASE_URL}</arg>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 10:15:52.753" endtime="******** 10:15:52.864"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 10:15:52.870" level="INFO">Opening url 'https://apc-portal.atm-marketing-dev.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 10:15:52.870" endtime="******** 10:15:54.031"/>
</kw>
<status status="PASS" starttime="******** 10:15:52.865" endtime="******** 10:15:54.031"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:04.032" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 10:15:54.032" endtime="******** 10:16:04.032"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:16:04.032" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:16:10.208" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 10:16:04.032" endtime="******** 10:16:10.208"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 10:16:10.208" endtime="******** 10:16:10.209"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:20.227" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 10:16:10.209" endtime="******** 10:16:20.227"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:16:20.370" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:16:21.574" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 10:16:20.370" endtime="******** 10:16:21.575"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 10:16:21.575" endtime="******** 10:16:21.575"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:26.589" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:16:21.575" endtime="******** 10:16:26.589"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:16:26.589" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:16:28.000" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 10:16:26.589" endtime="******** 10:16:28.000"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 10:16:28.001" endtime="******** 10:16:28.001"/>
</kw>
<status status="PASS" starttime="******** 10:15:52.753" endtime="******** 10:16:28.001"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:16:28.560" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-60.png"&gt;&lt;img src="selenium-screenshot-60.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:16:28.001" endtime="******** 10:16:28.560"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:16:28.560"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.197" endtime="******** 10:16:28.560"/>
</kw>
<kw name="The user navigates to their Specified Dashboard" library="common_keywords">
<arg>ATM_MARKETING_DASHBOARD</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>$DASHBOARD == 'ATM_MARKETING_DASHBOARD'</arg>
<arg>The user clicks Dashboard</arg>
<arg>${ATM_MARKETING_DASHBOARD_XPATH}</arg>
<arg>ELSE IF</arg>
<arg>$DASHBOARD == 'BIN_TABLES'</arg>
<arg>The user clicks Dashboard</arg>
<arg>${BIN_TABLES_DASHBOARD_XPATH}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="The user clicks Dashboard" library="common_keywords">
<arg>${ATM_MARKETING_DASHBOARD_XPATH}</arg>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${DASHBOARD_XPATH}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 10:16:28.562" endtime="******** 10:16:28.615"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DASHBOARD_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:28.616" level="INFO">Clicking element 'xpath=//div[contains(@class, 'landing-content')]//p[contains(text(), 'ATM MARKETING')]'.</msg>
<status status="PASS" starttime="******** 10:16:28.616" endtime="******** 10:16:28.924"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user has clicked the specified dashboard</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:28.924" endtime="******** 10:16:28.928"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:16:29.291" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-61.png"&gt;&lt;img src="selenium-screenshot-61.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:16:28.928" endtime="******** 10:16:29.291"/>
</kw>
<status status="PASS" starttime="******** 10:16:28.561" endtime="******** 10:16:29.291"/>
</kw>
<status status="PASS" starttime="******** 10:16:28.561" endtime="******** 10:16:29.291"/>
</kw>
<status status="PASS" starttime="******** 10:16:28.561" endtime="******** 10:16:29.291"/>
</kw>
<status status="PASS" starttime="******** 10:15:48.073" endtime="******** 10:16:29.291"/>
</kw>
<kw name="When The user clicks on the Capture campaign link" library="Navigation">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks the Capture campaign link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:29.291" endtime="******** 10:16:29.291"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:34.322" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:16:29.291" endtime="******** 10:16:34.322"/>
</kw>
<kw name="Run Keyword Until Success" library="Navigation">
<arg>Click Element</arg>
<arg>${CAPTURE_CAMPAIGN_LINK}</arg>
<kw name="Wait Until Keyword Succeeds" library="BuiltIn">
<arg>30s</arg>
<arg>1s</arg>
<arg>${KW}</arg>
<arg>${KWARGS}</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${KWARGS}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:34.322" level="INFO">Clicking element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/div/mat-list-item/span/span[3]'.</msg>
<status status="PASS" starttime="******** 10:16:34.322" endtime="******** 10:16:39.193"/>
</kw>
<status status="PASS" starttime="******** 10:16:34.322" endtime="******** 10:16:39.193"/>
</kw>
<status status="PASS" starttime="******** 10:16:34.322" endtime="******** 10:16:39.193"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>Fill out Campaign Targeted</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 10:16:39.193" endtime="******** 10:16:39.270"/>
</kw>
<status status="PASS" starttime="******** 10:16:29.291" endtime="******** 10:16:39.270"/>
</kw>
<kw name="And The user fills out Campaign Targeted" library="FilloutCampaignTarget">
<arg>${IS_CAMPAIGN_TARGETED}</arg>
<arg>${CAMPAIGN_TARGET}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Targeted: ${IS_CAMPAIGN_TARGETED}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:39.270" endtime="******** 10:16:39.293"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Target: ${CAMPAIGN_TARGET}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:39.293" endtime="******** 10:16:39.302"/>
</kw>
<kw name="User select radio button" library="Navigation">
<arg>${IS_CAMPAIGN_TARGETED}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Selecting radio button ${RADIO_BUTTON_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:39.302" endtime="******** 10:16:39.302"/>
</kw>
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 10:16:39.302" endtime="******** 10:16:40.932"/>
</kw>
<status status="PASS" starttime="******** 10:16:39.302" endtime="******** 10:16:40.932"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${RADIO_BUTTON_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:40.944" level="INFO">${path_string} = //input[@value='</msg>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.944"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.944"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:40.944" level="INFO">${path_string} = //input[@value='Yes</msg>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.944"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.944"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:40.944" level="INFO">${path_string} = //input[@value='Yes']/parent::*/parent::*/parent::*</msg>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.944"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.944"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:16:40.944" endtime="******** 10:16:40.996"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:40.996" level="INFO">Clicking element 'xpath=//input[@value='Yes']/parent::*/parent::*/parent::*'.</msg>
<status status="PASS" starttime="******** 10:16:40.996" endtime="******** 10:16:41.274"/>
</kw>
<status status="PASS" starttime="******** 10:16:39.302" endtime="******** 10:16:41.274"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>User select radio button</arg>
<arg>Region</arg>
<arg>ELSE IF</arg>
<arg>'${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>User select radio button</arg>
<arg>ATM</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="User select radio button" library="Navigation">
<arg>ATM</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Selecting radio button ${RADIO_BUTTON_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:41.274" endtime="******** 10:16:41.274"/>
</kw>
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 10:16:41.274" endtime="******** 10:16:41.295"/>
</kw>
<status status="PASS" starttime="******** 10:16:41.274" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${RADIO_BUTTON_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:41.295" level="INFO">${path_string} = //input[@value='</msg>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:41.295" level="INFO">${path_string} = //input[@value='ATM</msg>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:41.295" level="INFO">${path_string} = //input[@value='ATM']/parent::*/parent::*/parent::*</msg>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.295"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:16:41.295" endtime="******** 10:16:41.327"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:41.327" level="INFO">Clicking element 'xpath=//input[@value='ATM']/parent::*/parent::*/parent::*'.</msg>
<status status="PASS" starttime="******** 10:16:41.327" endtime="******** 10:16:41.675"/>
</kw>
<status status="PASS" starttime="******** 10:16:41.274" endtime="******** 10:16:41.675"/>
</kw>
<status status="PASS" starttime="******** 10:16:41.274" endtime="******** 10:16:41.675"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:43.675" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:16:41.675" endtime="******** 10:16:43.675"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>Select from dropdown</arg>
<arg>${REGIONAL_CAMPAIGN_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<arg>ELSE IF</arg>
<arg>'${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>Select ATM from dropdown</arg>
<arg>${ATM_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Select ATM from dropdown" library="Navigation">
<arg>${ATM_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:43.675" endtime="******** 10:16:43.754"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:45.761" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:16:43.760" endtime="******** 10:16:45.761"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:45.761" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[1]/app-campaign-targeted/div[2]/div[2]/mat-form-field/div/div[1]/div'.</msg>
<status status="PASS" starttime="******** 10:16:45.761" endtime="******** 10:16:47.657"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:52.658" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:16:47.657" endtime="******** 10:16:52.658"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_ATM_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:52.658" level="INFO">${path_string} = //span[contains(text(),</msg>
<status status="PASS" starttime="******** 10:16:52.658" endtime="******** 10:16:52.658"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:52.658" endtime="******** 10:16:52.768"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:52.769" level="INFO">${path_string} = //span[contains(text(),'S11782 ABSA LAB, 270 Republic Road - Randburg</msg>
<status status="PASS" starttime="******** 10:16:52.768" endtime="******** 10:16:52.769"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:52.769" endtime="******** 10:16:52.779"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_ATM_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:16:52.780" level="INFO">${path_string} = //span[contains(text(),'S11782 ABSA LAB, 270 Republic Road - Randburg')]</msg>
<status status="PASS" starttime="******** 10:16:52.779" endtime="******** 10:16:52.780"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:52.780" endtime="******** 10:16:52.908"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:16:52.908" endtime="******** 10:16:53.026"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:53.027" level="INFO">Clicking element 'xpath=//span[contains(text(),'S11782 ABSA LAB, 270 Republic Road - Randburg')]'.</msg>
<status status="PASS" starttime="******** 10:16:53.026" endtime="******** 10:16:53.270"/>
</kw>
<status status="PASS" starttime="******** 10:16:43.675" endtime="******** 10:16:53.270"/>
</kw>
<status status="PASS" starttime="******** 10:16:43.675" endtime="******** 10:16:53.270"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>FilloutCampaignTarget.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:16:53.386" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="FilloutCampaignTarget.png"&gt;&lt;img src="FilloutCampaignTarget.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:16:53.271" endtime="******** 10:16:53.386"/>
</kw>
<status status="PASS" starttime="******** 10:16:39.270" endtime="******** 10:16:53.386"/>
</kw>
<kw name="And The user clicks on Next button" library="Navigation">
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:16:53.386" endtime="******** 10:16:53.417"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${Next_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:53.417" level="INFO">Clicking element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 10:16:53.417" endtime="******** 10:16:53.540"/>
</kw>
<status status="PASS" starttime="******** 10:16:53.386" endtime="******** 10:16:53.540"/>
</kw>
<kw name="And The user fills out Campaign" library="FilloutYourCampaign">
<arg>${CAMPAIGN_NAME}</arg>
<arg>${MARKETING_TYPE}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>${CAMPAIGN_END_DATE}</arg>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:16:55.583" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:16:53.540" endtime="******** 10:16:55.583"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:16:55.583" level="INFO">Clicking element 'xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input'.</msg>
<status status="PASS" starttime="******** 10:16:55.583" endtime="******** 10:16:57.798"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME_INPUT}</arg>
<arg>${CAMPAIGN_NAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="******** 10:16:57.799" level="INFO">Typing text 'DiminuentCampaign' into text field 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 10:16:57.799" endtime="******** 10:16:59.051"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Marketing Type: ${MARKETING_TYPE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:59.051" endtime="******** 10:16:59.051"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Receiver Device Type: ${RECEIVER_DEVICE_TYPE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:59.051" endtime="******** 10:16:59.051"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${RECEIVER_DEVICE_TYPE}' == 'ATM'</arg>
<arg>Select from dropdown</arg>
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>ELSE</arg>
<arg>Select from dropdown</arg>
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Select from dropdown" library="Navigation">
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:16:59.051" endtime="******** 10:16:59.051"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:01.053" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:16:59.051" endtime="******** 10:17:01.053"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:01.053" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="receiverDeviceType"]'.</msg>
<status status="PASS" starttime="******** 10:17:01.053" endtime="******** 10:17:01.156"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:06.165" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:17:01.157" endtime="******** 10:17:06.165"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:06.165" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 10:17:06.165" endtime="******** 10:17:06.165"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:06.165" endtime="******** 10:17:06.451"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:06.452" level="INFO">${path_string} = //span[text()=' ATM </msg>
<status status="PASS" starttime="******** 10:17:06.451" endtime="******** 10:17:06.452"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:06.452" endtime="******** 10:17:06.469"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:06.470" level="INFO">${path_string} = //span[text()=' ATM ']</msg>
<status status="PASS" starttime="******** 10:17:06.469" endtime="******** 10:17:06.470"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:06.470" endtime="******** 10:17:06.567"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:17:06.567" endtime="******** 10:17:06.826"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:06.826" level="INFO">Clicking element 'xpath=//span[text()=' ATM ']'.</msg>
<status status="PASS" starttime="******** 10:17:06.826" endtime="******** 10:17:06.878"/>
</kw>
<status status="PASS" starttime="******** 10:16:59.051" endtime="******** 10:17:06.878"/>
</kw>
<status status="PASS" starttime="******** 10:16:59.051" endtime="******** 10:17:06.878"/>
</kw>
<kw name="Select Campaign start and end date" library="FilloutYourCampaign">
<arg>${CAMPAIGN_START_DATE_DATA}</arg>
<arg>${CAMPAIGN_END_DATE_DATA}</arg>
<if>
<branch type="IF" condition="'${CAMPAIGN_START_DATE_DATA.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign start date. The date provided must be a number which represent the number of days from today.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:06.880" endtime="******** 10:17:06.880"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:06.879" endtime="******** 10:17:06.880"/>
</branch>
<branch type="ELSE IF" condition="'${CAMPAIGN_END_DATE_DATA.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign end date. The date provided must be a number which represent the number of days from today.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:06.881" endtime="******** 10:17:06.881"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:06.880" endtime="******** 10:17:06.881"/>
</branch>
<status status="PASS" starttime="******** 10:17:06.879" endtime="******** 10:17:06.881"/>
</if>
<kw name="Check Data Type" library="GenericMethods">
<var>${start_date_data_type}</var>
<arg>${CAMPAIGN_START_DATE_DATA}</arg>
<doc>Checks if the ${object } is INTEGER, NUMBER or STRING</doc>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>not "${object}"</arg>
<arg>NONE</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Number</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Number" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to a floating point number.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 10:17:06.882" level="INFO">${value} = 4.0</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isnumber}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'float'&gt;</msg>
<msg timestamp="******** 10:17:06.882" level="FAIL">4 != 4.0</msg>
<status status="FAIL" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${isnumber} = False</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Integer</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Integer" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to an integer number.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 10:17:06.882" level="INFO">${value} = 4</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isinteger}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${isinteger} = True</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isnumber}</arg>
<arg>'NUMBER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isinteger}</arg>
<arg>'INTEGER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">Returning from the enclosing user keyword.</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Return From Keyword" library="BuiltIn">
<arg>'STRING'</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="NOT RUN" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${start_date_data_type} = 'INTEGER'</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<if>
<branch type="IF" condition="${start_date_data_type}!='INTEGER'">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the start date in Integer formart!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</branch>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</if>
<kw name="Check Data Type" library="GenericMethods">
<var>${start_end_data_type}</var>
<arg>${CAMPAIGN_END_DATE_DATA}</arg>
<doc>Checks if the ${object } is INTEGER, NUMBER or STRING</doc>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>not "${object}"</arg>
<arg>NONE</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Number</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Number" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to a floating point number.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 10:17:06.882" level="INFO">${value} = 6.0</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isnumber}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'float'&gt;</msg>
<msg timestamp="******** 10:17:06.882" level="FAIL">6 != 6.0</msg>
<status status="FAIL" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${isnumber} = False</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<var>${result}</var>
<var>${value}</var>
<arg>Convert To Integer</arg>
<arg>${object}</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Convert To Integer" library="BuiltIn">
<arg>${object}</arg>
<doc>Converts the given item to an integer number.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${result} = PASS</msg>
<msg timestamp="******** 10:17:06.882" level="INFO">${value} = 6</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${isinteger}</var>
<arg>Should Be Equal As Strings</arg>
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${object}</arg>
<arg>${value}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${isinteger} = True</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isnumber}</arg>
<arg>'NUMBER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${isinteger}</arg>
<arg>'INTEGER'</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">Returning from the enclosing user keyword.</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Return From Keyword" library="BuiltIn">
<arg>'STRING'</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="NOT RUN" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<msg timestamp="******** 10:17:06.882" level="INFO">${start_end_data_type} = 'INTEGER'</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<if>
<branch type="IF" condition="${start_end_data_type}!='INTEGER'">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the end date in Integer formart!</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</branch>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</if>
<kw name="Get Current Date" library="DateTime">
<var>${current_date}</var>
<arg>result_format=%Y-%m-%d</arg>
<doc>Returns current local or UTC time with an optional increment.</doc>
<msg timestamp="******** 10:17:06.882" level="INFO">${current_date} = 2025-02-03</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.882"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${campaign_required_start_date}</var>
<arg>${current_date}</arg>
<arg>${CAMPAIGN_START_DATE_DATA} days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="******** 10:17:06.898" level="INFO">${campaign_required_start_date} = 2025-02-07 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:06.882" endtime="******** 10:17:06.898"/>
</kw>
<kw name="Add Time To Date" library="DateTime">
<var>${campaign_required_end_date}</var>
<arg>${current_date}</arg>
<arg>${CAMPAIGN_END_DATE_DATA} days</arg>
<doc>Adds time to date and returns the resulting date.</doc>
<msg timestamp="******** 10:17:06.898" level="INFO">${campaign_required_end_date} = 2025-02-09 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:06.898" endtime="******** 10:17:06.898"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date is: ${campaign_required_start_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:06.898" level="INFO">Campaign Start Date is: 2025-02-07 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:06.898" endtime="******** 10:17:06.898"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date is: ${campaign_required_end_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:06.898" level="INFO">Campaign End Date is: 2025-02-09 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:06.898" endtime="******** 10:17:06.903"/>
</kw>
<kw name="Search for Date on the Campaign Capturing Calendar" library="FilloutYourCampaign">
<arg>${campaign_required_start_date}</arg>
<arg>${campaign_required_end_date}</arg>
<if>
<branch type="IF" condition="'${CAMPAIGN_START_DATE.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign start date.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.138" endtime="******** 10:17:07.138"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:06.903" endtime="******** 10:17:07.138"/>
</branch>
<branch type="ELSE IF" condition="'${CAMPAIGN_END_DATE.replace('\n','').strip()}'==''">
<kw name="Fail" library="BuiltIn">
<arg>Please provide the campaign end date.</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.138" endtime="******** 10:17:07.138"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.138" endtime="******** 10:17:07.138"/>
</branch>
<status status="PASS" starttime="******** 10:17:06.903" endtime="******** 10:17:07.138"/>
</if>
<kw name="Get the number of displayed web elements" library="GenericMethods">
<var>${element_count}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${webelements}</var>
<arg>${ELEMENT_LOCATOR}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="******** 10:17:07.187" level="INFO">${webelements} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="5a9b54b3c584496d0efc2542a5ebb79e", element="f.B91BC4FFB44A7707ADAE09F43B72ACEA.d.2ED755B6852B56543CD31001145008B2.e.216")&gt;]</msg>
<status status="PASS" starttime="******** 10:17:07.138" endtime="******** 10:17:07.187"/>
</kw>
<if>
<branch type="IF" condition="'' == '${webelements}'">
<kw name="Fail" library="BuiltIn">
<arg>Element: '${ELEMENT_LOCATOR}' was not found</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.187" endtime="******** 10:17:07.187"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.187" endtime="******** 10:17:07.187"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.187" endtime="******** 10:17:07.187"/>
</if>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${count}</var>
<arg>${ELEMENT_LOCATOR}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:07.204" level="INFO">${count} = 1</msg>
<status status="PASS" starttime="******** 10:17:07.187" endtime="******** 10:17:07.204"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Element count is: ${count}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:07.204" endtime="******** 10:17:07.489"/>
</kw>
<return>
<value>${count}</value>
<status status="PASS" starttime="******** 10:17:07.489" endtime="******** 10:17:07.489"/>
</return>
<msg timestamp="******** 10:17:07.490" level="INFO">${element_count} = 1</msg>
<status status="PASS" starttime="******** 10:17:07.138" endtime="******** 10:17:07.490"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>${element_count} == 0</arg>
<arg>Fail</arg>
<arg>Calendar element for selecting the campaign date was not found.</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<if>
<branch type="IF" condition="${element_count} &gt; 1">
<kw name="Fail" library="BuiltIn">
<arg>More than 1 element was located for the calendar element</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</if>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_month_year_element_path}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<arg>/descendant::*[@id='mat-calendar-button-0']</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${calendar_month_year_element_path} = xpath=//mat-card[@id='testing'] /descendant::*[@id='mat-calendar-button-0']</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_next_month_element_path}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<arg>/descendant::*[contains(@class,'mat-calendar-next-button')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${calendar_next_month_element_path} = xpath=//mat-card[@id='testing'] /descendant::*[contains(@class,'mat-calendar-next-button')]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_body_element_path}</var>
<arg>${CAPTURE_CAMPAIGN_CALENDAR}</arg>
<arg>/descendant::tbody</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${calendar_body_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${calendar_body_rows_element_path}</var>
<arg>${calendar_body_element_path}</arg>
<arg>/tr</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${calendar_body_rows_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_start_date_month_and_year}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>result_format=%b %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_required_start_date_month_and_year} = Feb 2025</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_start_date_month}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>result_format=%b</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_required_start_date_month} = Feb</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_end_date_month_and_year}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<arg>result_format=%b %Y</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_required_end_date_month_and_year} = Feb 2025</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${campaign_required_end_date_month}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<arg>result_format=%b</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_required_end_date_month} = Feb</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Create Campaign Quarters" library="GenericMethods">
<kw name="Create List" library="BuiltIn">
<var>@{q1_months}</var>
<arg>Jan</arg>
<arg>Feb</arg>
<arg>Mar</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{q1_months} = [ Jan | Feb | Mar ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>@{q2_months}</var>
<arg>Apr</arg>
<arg>May</arg>
<arg>Jun</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{q2_months} = [ Apr | May | Jun ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>@{q3_months}</var>
<arg>Jul</arg>
<arg>Aug</arg>
<arg>Sep</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{q3_months} = [ Jul | Aug | Sep ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Create List" library="BuiltIn">
<var>@{q4_months}</var>
<arg>Oct</arg>
<arg>Nov</arg>
<arg>Dec</arg>
<doc>Returns a list containing given items.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{q4_months} = [ Oct | Nov | Dec ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q1_MONTHS}</arg>
<arg>@{q1_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{Q1_MONTHS} = [ Jan | Feb | Mar ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q2_MONTHS}</arg>
<arg>@{q2_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{Q2_MONTHS} = [ Apr | May | Jun ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q3_MONTHS}</arg>
<arg>@{q3_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{Q3_MONTHS} = [ Jul | Aug | Sep ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>@{Q4_MONTHS}</arg>
<arg>@{q4_months}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">@{Q4_MONTHS} = [ Oct | Nov | Dec ]</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_required_start_date_month}' in ${Q1_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q1</arg>
<doc>Converts the given item to a Unicode string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_start_date_quarter} = q1</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_start_date_month}' in ${Q2_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q2</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_start_date_month}' in ${Q3_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q3</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<branch type="ELSE">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_start_date_quarter}</var>
<arg>q4</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</if>
<if>
<branch type="IF" condition="'${campaign_required_end_date_month}' in ${Q1_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q1</arg>
<doc>Converts the given item to a Unicode string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_end_date_quarter} = q1</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_end_date_month}' in ${Q2_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q2</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<branch type="ELSE IF" condition="'${campaign_required_end_date_month}' in ${Q3_MONTHS}">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q3</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<branch type="ELSE">
<kw name="Convert To String" library="BuiltIn">
<var>${campaign_end_date_quarter}</var>
<arg>q4</arg>
<doc>Converts the given item to a Unicode string.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</if>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${campaign_start_date_quarter}' != '${campaign_end_date_quarter}'</arg>
<arg>Fail</arg>
<arg>Campaign Start Date and End Date must fall in the same Cycle! The campaign start date falls in '${campaign_start_date_quarter}', while the end date falls in '${campaign_end_date_quarter}'</arg>
<arg>ELSE IF</arg>
<arg>'${campaign_start_date_quarter}' == '${campaign_end_date_quarter}'</arg>
<arg>Log Many</arg>
<arg>Campaign Start Date and End Date fall in the same Cycle...</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Start Date and End Date fall in the same Cycle...</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">Campaign Start Date and End Date fall in the same Cycle...</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_start_date_month_and_year: ${campaign_required_start_date_month_and_year}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">campaign_required_start_date_month_and_year: Feb 2025</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_start_date_month: ${campaign_required_start_date_month}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">campaign_required_start_date_month: Feb</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_end_date_month_and_year: ${campaign_required_end_date_month_and_year}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">campaign_required_end_date_month_and_year: Feb 2025</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_required_end_date_month: ${campaign_required_end_date_month}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">campaign_required_end_date_month: Feb</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Split String" library="String">
<var>${start_date_array}</var>
<arg>${CAMPAIGN_START_DATE}</arg>
<arg>${SPACE}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${start_date_array} = ['2025-02-07', '00:00:00.000']</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Split String" library="String">
<var>${start_date_array_two}</var>
<arg>${start_date_array}[0]</arg>
<arg>separator=-</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${start_date_array_two} = ['2025', '02', '07']</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_start_day}</var>
<arg>${start_date_array_two}[2]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${campaign_start_day} = 07</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_start_day:${campaign_start_day}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">campaign_start_day:07</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Split String" library="String">
<var>${end_date_array}</var>
<arg>${CAMPAIGN_END_DATE}</arg>
<arg>${SPACE}</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 10:17:07.491" level="INFO">${end_date_array} = ['2025-02-09', '00:00:00.000']</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.491"/>
</kw>
<kw name="Split String" library="String">
<var>${end_date_array_two}</var>
<arg>${end_date_array}[0]</arg>
<arg>separator=-</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 10:17:07.556" level="INFO">${end_date_array_two} = ['2025', '02', '09']</msg>
<status status="PASS" starttime="******** 10:17:07.491" endtime="******** 10:17:07.556"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_end_day}</var>
<arg>${end_date_array_two}[2]</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:07.585" level="INFO">${campaign_end_day} = 09</msg>
<status status="PASS" starttime="******** 10:17:07.576" endtime="******** 10:17:07.585"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>campaign_end_day:${campaign_end_day}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.585" level="INFO">campaign_end_day:09</msg>
<status status="PASS" starttime="******** 10:17:07.585" endtime="******** 10:17:07.585"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${campaign_required_start_date_month_and_year}</var>
<arg>${campaign_required_start_date_month_and_year}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 10:17:07.585" level="INFO">${campaign_required_start_date_month_and_year} = FEB 2025</msg>
<status status="PASS" starttime="******** 10:17:07.585" endtime="******** 10:17:07.585"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:07.620" level="INFO">${curr_month_year_displayed} = FEB 2025</msg>
<status status="PASS" starttime="******** 10:17:07.585" endtime="******** 10:17:07.620"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Current Displayed Month and Year:${curr_month_year_displayed}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.620" level="INFO">Current Displayed Month and Year:FEB 2025</msg>
<status status="PASS" starttime="******** 10:17:07.620" endtime="******** 10:17:07.620"/>
</kw>
<while condition="'${curr_month_year_displayed.strip()}' != '${campaign_required_start_date_month_and_year.strip()}'">
<iter>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${calendar_next_month_element_path}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.622" endtime="******** 10:17:07.622"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.622" endtime="******** 10:17:07.622"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.622" endtime="******** 10:17:07.622"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Month displayed:'${curr_month_year_displayed}'</arg>
<arg>Month Required:'${campaign_required_start_date_month_and_year}'</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.622" endtime="******** 10:17:07.622"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.620" endtime="******** 10:17:07.622"/>
</iter>
<status status="NOT RUN" starttime="******** 10:17:07.620" endtime="******** 10:17:07.622"/>
</while>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${rows}</var>
<arg>${calendar_body_rows_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">${rows} = 5</msg>
<status status="PASS" starttime="******** 10:17:07.622" endtime="******** 10:17:07.633"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">${bool_start_date_selected} = False</msg>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_end_selected}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">${bool_start_end_selected} = False</msg>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</kw>
<kw name="Get Substring" library="String">
<var>${campaign_start_day_part}</var>
<arg>${campaign_start_day.strip()}</arg>
<arg>0</arg>
<arg>1</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">${campaign_start_day_part} = 0</msg>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_start_day.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">07</msg>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day_part}' == '0'">
<kw name="Get Substring" library="String">
<var>${campaign_start_day}</var>
<arg>${campaign_start_day.strip()}</arg>
<arg>1</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">${campaign_start_day} = 7</msg>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</if>
<kw name="Log" library="BuiltIn">
<arg>${campaign_start_day_part}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.633" level="INFO">0</msg>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:07.633"/>
</kw>
<for flavor="IN RANGE">
<var>${row_num}</var>
<value>1</value>
<value>${rows+1}</value>
<iter>
<var name="${row_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.680" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1]</msg>
<status status="PASS" starttime="******** 10:17:07.680" endtime="******** 10:17:07.680"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.680" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td</msg>
<status status="PASS" starttime="******** 10:17:07.680" endtime="******** 10:17:07.680"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:07.696" level="INFO">${cols} = 2</msg>
<status status="PASS" starttime="******** 10:17:07.680" endtime="******** 10:17:07.696"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.696" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td [1]</msg>
<status status="PASS" starttime="******** 10:17:07.696" endtime="******** 10:17:07.696"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:07.717" level="INFO">${campaign_date} = FEB</msg>
<status status="PASS" starttime="******** 10:17:07.696" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.717" level="INFO">FEB</msg>
<status status="PASS" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</if>
<status status="PASS" starttime="******** 10:17:07.696" endtime="******** 10:17:07.717"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.717" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td [2]</msg>
<status status="PASS" starttime="******** 10:17:07.717" endtime="******** 10:17:07.717"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:07.728" level="INFO">${campaign_date} = 1</msg>
<status status="PASS" starttime="******** 10:17:07.717" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.728" level="INFO">1</msg>
<status status="PASS" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.728"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.743" endtime="******** 10:17:07.743"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.728" endtime="******** 10:17:07.743"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.728" endtime="******** 10:17:07.743"/>
</if>
<status status="PASS" starttime="******** 10:17:07.717" endtime="******** 10:17:07.743"/>
</iter>
<status status="PASS" starttime="******** 10:17:07.696" endtime="******** 10:17:07.743"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.743"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.680" endtime="******** 10:17:07.743"/>
</iter>
<iter>
<var name="${row_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.743" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2]</msg>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.743"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.743" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td</msg>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.743"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:07.743" level="INFO">${cols} = 7</msg>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.743"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.743" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [1]</msg>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.743"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:07.775" level="INFO">${campaign_date} = 2</msg>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.775"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.775" level="INFO">2</msg>
<status status="PASS" starttime="******** 10:17:07.775" endtime="******** 10:17:07.775"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.775" endtime="******** 10:17:07.778"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.775" endtime="******** 10:17:07.778"/>
</if>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:07.778"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.778" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [2]</msg>
<status status="PASS" starttime="******** 10:17:07.778" endtime="******** 10:17:07.778"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:07.791" level="INFO">${campaign_date} = 3</msg>
<status status="PASS" starttime="******** 10:17:07.778" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.791" level="INFO">3</msg>
<status status="PASS" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</if>
<status status="PASS" starttime="******** 10:17:07.778" endtime="******** 10:17:07.791"/>
</iter>
<iter>
<var name="${col_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.791" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [3]</msg>
<status status="PASS" starttime="******** 10:17:07.791" endtime="******** 10:17:07.791"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:07.812" level="INFO">${campaign_date} = 4</msg>
<status status="PASS" starttime="******** 10:17:07.791" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:07.812" level="INFO">4</msg>
<status status="PASS" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</branch>
<status status="PASS" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</if>
<status status="PASS" starttime="******** 10:17:07.791" endtime="******** 10:17:07.812"/>
</iter>
<iter>
<var name="${col_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:07.812" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [4]</msg>
<status status="PASS" starttime="******** 10:17:07.812" endtime="******** 10:17:07.812"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:08.100" level="INFO">${campaign_date} = 5</msg>
<status status="PASS" starttime="******** 10:17:07.812" endtime="******** 10:17:08.100"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:08.100" level="INFO">5</msg>
<status status="PASS" starttime="******** 10:17:08.100" endtime="******** 10:17:08.100"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.102" endtime="******** 10:17:08.102"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.102" endtime="******** 10:17:08.102"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.102" endtime="******** 10:17:08.102"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.102" endtime="******** 10:17:08.102"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.102" endtime="******** 10:17:08.103"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.103" endtime="******** 10:17:08.103"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.103" endtime="******** 10:17:08.103"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:08.101" endtime="******** 10:17:08.103"/>
</branch>
<status status="PASS" starttime="******** 10:17:08.101" endtime="******** 10:17:08.103"/>
</if>
<status status="PASS" starttime="******** 10:17:07.812" endtime="******** 10:17:08.103"/>
</iter>
<iter>
<var name="${col_num}">5</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:08.103" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [5]</msg>
<status status="PASS" starttime="******** 10:17:08.103" endtime="******** 10:17:08.103"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:08.586" level="INFO">${campaign_date} = 6</msg>
<status status="PASS" starttime="******** 10:17:08.103" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:08.586" level="INFO">6</msg>
<status status="PASS" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</branch>
<status status="PASS" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</if>
<status status="PASS" starttime="******** 10:17:08.103" endtime="******** 10:17:08.586"/>
</iter>
<iter>
<var name="${col_num}">6</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:08.586" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [6]</msg>
<status status="PASS" starttime="******** 10:17:08.586" endtime="******** 10:17:08.586"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:09.075" level="INFO">${campaign_date} = 7</msg>
<status status="PASS" starttime="******** 10:17:08.586" endtime="******** 10:17:09.075"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:09.076" level="INFO">7</msg>
<status status="PASS" starttime="******** 10:17:09.076" endtime="******** 10:17:09.076"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_start_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" starttime="******** 10:17:09.076" endtime="******** 10:17:09.694"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:09.695" level="INFO">${row_num} = 0</msg>
<status status="PASS" starttime="******** 10:17:09.695" endtime="******** 10:17:09.695"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_start_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:09.695" level="INFO">${bool_start_date_selected} = True</msg>
<status status="PASS" starttime="******** 10:17:09.695" endtime="******** 10:17:09.695"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:09.696" level="INFO">Clicking element 'xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [6]'.</msg>
<status status="PASS" starttime="******** 10:17:09.695" endtime="******** 10:17:09.763"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign Start Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:09.764" level="INFO">Campaign Start Date Selected!</msg>
<status status="PASS" starttime="******** 10:17:09.764" endtime="******** 10:17:09.764"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>StartDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:17:09.983" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="StartDateSelection.png"&gt;&lt;img src="StartDateSelection.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:17:09.764" endtime="******** 10:17:09.983"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 10:17:09.983" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 10:17:09.983" endtime="******** 10:17:09.983"/>
</kw>
<status status="PASS" starttime="******** 10:17:09.076" endtime="******** 10:17:09.983"/>
</branch>
<status status="PASS" starttime="******** 10:17:09.076" endtime="******** 10:17:09.983"/>
</if>
<status status="PASS" starttime="******** 10:17:08.586" endtime="******** 10:17:09.983"/>
</iter>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:09.983"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 10:17:09.983" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 10:17:09.983" endtime="******** 10:17:09.983"/>
</kw>
<status status="PASS" starttime="******** 10:17:09.983" endtime="******** 10:17:09.983"/>
</kw>
<status status="PASS" starttime="******** 10:17:07.743" endtime="******** 10:17:09.983"/>
</iter>
<status status="PASS" starttime="******** 10:17:07.633" endtime="******** 10:17:09.983"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_start_date_selected} == ${False}</arg>
<arg>Fail</arg>
<arg>Campaign start date: '${campaign_start_day.strip()}' was not selected!!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:09.983" endtime="******** 10:17:09.983"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${campaign_required_end_date_month_and_year}</var>
<arg>${campaign_required_end_date_month_and_year}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 10:17:09.983" level="INFO">${campaign_required_end_date_month_and_year} = FEB 2025</msg>
<status status="PASS" starttime="******** 10:17:09.983" endtime="******** 10:17:09.983"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:10.448" level="INFO">${curr_month_year_displayed} = FEB 2025</msg>
<status status="PASS" starttime="******** 10:17:09.983" endtime="******** 10:17:10.448"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Current Displayed Month and Year:${curr_month_year_displayed}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:10.448" endtime="******** 10:17:10.457"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Month Required:${campaign_required_end_date_month_and_year}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</kw>
<while condition="'${curr_month_year_displayed.strip()}' != '${campaign_required_end_date_month_and_year.strip()}'">
<iter>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${calendar_next_month_element_path}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${curr_month_year_displayed}</var>
<arg>${calendar_month_year_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Month displayed:'${curr_month_year_displayed}'</arg>
<arg>Month Required:'${campaign_required_end_date_month_and_year}'</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</iter>
<status status="NOT RUN" starttime="******** 10:17:10.457" endtime="******** 10:17:10.457"/>
</while>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${rows_new}</var>
<arg>${calendar_body_rows_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">${rows_new} = 5</msg>
<status status="PASS" starttime="******** 10:17:10.457" endtime="******** 10:17:10.570"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${False}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">${bool_end_date_selected} = False</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_end_day.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">09</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<kw name="Get Substring" library="String">
<var>${campaign_end_day_part}</var>
<arg>${campaign_end_day.strip()}</arg>
<arg>0</arg>
<arg>1</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">${campaign_end_day_part} = 0</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day_part}' == '0'">
<kw name="Get Substring" library="String">
<var>${campaign_end_day}</var>
<arg>${campaign_end_day.strip()}</arg>
<arg>1</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">${campaign_end_day} = 9</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</branch>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</if>
<kw name="Log" library="BuiltIn">
<arg>${campaign_end_day.strip()}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">9</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<for flavor="IN RANGE">
<var>${row_num}</var>
<value>1</value>
<value>${rows_new+1}</value>
<iter>
<var name="${row_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1]</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:10.570" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.570"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:10.797" level="INFO">${cols} = 2</msg>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:10.798"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:10.799" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td [1]</msg>
<status status="PASS" starttime="******** 10:17:10.799" endtime="******** 10:17:10.799"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:11.181" level="INFO">${campaign_date} = FEB</msg>
<status status="PASS" starttime="******** 10:17:10.799" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:11.181" level="INFO">FEB</msg>
<status status="PASS" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</branch>
<status status="PASS" starttime="******** 10:17:11.181" endtime="******** 10:17:11.181"/>
</if>
<status status="PASS" starttime="******** 10:17:10.799" endtime="******** 10:17:11.181"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:11.186" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [1] /td [2]</msg>
<status status="PASS" starttime="******** 10:17:11.181" endtime="******** 10:17:11.186"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:11.707" level="INFO">${campaign_date} = 1</msg>
<status status="PASS" starttime="******** 10:17:11.186" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:11.707" level="INFO">1</msg>
<status status="PASS" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.707"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:11.712" endtime="******** 10:17:11.712"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:11.707" endtime="******** 10:17:11.712"/>
</branch>
<status status="PASS" starttime="******** 10:17:11.707" endtime="******** 10:17:11.712"/>
</if>
<status status="PASS" starttime="******** 10:17:11.181" endtime="******** 10:17:11.712"/>
</iter>
<status status="PASS" starttime="******** 10:17:10.798" endtime="******** 10:17:11.712"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:11.712" endtime="******** 10:17:11.712"/>
</kw>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:11.712"/>
</iter>
<iter>
<var name="${row_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:11.712" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2]</msg>
<status status="PASS" starttime="******** 10:17:11.712" endtime="******** 10:17:11.712"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:11.712" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td</msg>
<status status="PASS" starttime="******** 10:17:11.712" endtime="******** 10:17:11.712"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:11.884" level="INFO">${cols} = 7</msg>
<status status="PASS" starttime="******** 10:17:11.712" endtime="******** 10:17:11.884"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:11.885" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [1]</msg>
<status status="PASS" starttime="******** 10:17:11.885" endtime="******** 10:17:11.885"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:12.377" level="INFO">${campaign_date} = 2</msg>
<status status="PASS" starttime="******** 10:17:11.885" endtime="******** 10:17:12.377"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:12.378" level="INFO">2</msg>
<status status="PASS" starttime="******** 10:17:12.378" endtime="******** 10:17:12.378"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.382" endtime="******** 10:17:12.382"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.383" endtime="******** 10:17:12.383"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.383" endtime="******** 10:17:12.383"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.383" endtime="******** 10:17:12.383"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.383" endtime="******** 10:17:12.383"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.383" endtime="******** 10:17:12.383"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.383" endtime="******** 10:17:12.383"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:12.378" endtime="******** 10:17:12.383"/>
</branch>
<status status="PASS" starttime="******** 10:17:12.378" endtime="******** 10:17:12.383"/>
</if>
<status status="PASS" starttime="******** 10:17:11.885" endtime="******** 10:17:12.383"/>
</iter>
<iter>
<var name="${col_num}">2</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:12.386" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [2]</msg>
<status status="PASS" starttime="******** 10:17:12.386" endtime="******** 10:17:12.386"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:12.618" level="INFO">${campaign_date} = 3</msg>
<status status="PASS" starttime="******** 10:17:12.386" endtime="******** 10:17:12.618"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:12.621" level="INFO">3</msg>
<status status="PASS" starttime="******** 10:17:12.621" endtime="******** 10:17:12.621"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</branch>
<status status="PASS" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</if>
<status status="PASS" starttime="******** 10:17:12.383" endtime="******** 10:17:12.622"/>
</iter>
<iter>
<var name="${col_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:12.622" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [3]</msg>
<status status="PASS" starttime="******** 10:17:12.622" endtime="******** 10:17:12.622"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:13.093" level="INFO">${campaign_date} = 4</msg>
<status status="PASS" starttime="******** 10:17:12.622" endtime="******** 10:17:13.093"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:13.093" level="INFO">4</msg>
<status status="PASS" starttime="******** 10:17:13.093" endtime="******** 10:17:13.093"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.096" endtime="******** 10:17:13.096"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.097" endtime="******** 10:17:13.097"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.097" endtime="******** 10:17:13.097"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.097" endtime="******** 10:17:13.097"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.097" endtime="******** 10:17:13.097"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.097" endtime="******** 10:17:13.097"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.097" endtime="******** 10:17:13.097"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:13.094" endtime="******** 10:17:13.097"/>
</branch>
<status status="PASS" starttime="******** 10:17:13.094" endtime="******** 10:17:13.097"/>
</if>
<status status="PASS" starttime="******** 10:17:12.622" endtime="******** 10:17:13.097"/>
</iter>
<iter>
<var name="${col_num}">4</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:13.098" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [4]</msg>
<status status="PASS" starttime="******** 10:17:13.098" endtime="******** 10:17:13.098"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:13.461" level="INFO">${campaign_date} = 5</msg>
<status status="PASS" starttime="******** 10:17:13.098" endtime="******** 10:17:13.461"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:13.461" level="INFO">5</msg>
<status status="PASS" starttime="******** 10:17:13.461" endtime="******** 10:17:13.463"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</branch>
<status status="PASS" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</if>
<status status="PASS" starttime="******** 10:17:13.097" endtime="******** 10:17:13.463"/>
</iter>
<iter>
<var name="${col_num}">5</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:13.463" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [5]</msg>
<status status="PASS" starttime="******** 10:17:13.463" endtime="******** 10:17:13.463"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:14.015" level="INFO">${campaign_date} = 6</msg>
<status status="PASS" starttime="******** 10:17:13.463" endtime="******** 10:17:14.015"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:14.016" level="INFO">6</msg>
<status status="PASS" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</branch>
<status status="PASS" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</if>
<status status="PASS" starttime="******** 10:17:13.463" endtime="******** 10:17:14.016"/>
</iter>
<iter>
<var name="${col_num}">6</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:14.016" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [6]</msg>
<status status="PASS" starttime="******** 10:17:14.016" endtime="******** 10:17:14.016"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:14.630" level="INFO">${campaign_date} = 7</msg>
<status status="PASS" starttime="******** 10:17:14.016" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:14.630" level="INFO">7</msg>
<status status="PASS" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</branch>
<status status="PASS" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</if>
<status status="PASS" starttime="******** 10:17:14.016" endtime="******** 10:17:14.630"/>
</iter>
<iter>
<var name="${col_num}">7</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:14.630" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [2] /td [7]</msg>
<status status="PASS" starttime="******** 10:17:14.630" endtime="******** 10:17:14.630"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:14.754" level="INFO">${campaign_date} = 8</msg>
<status status="PASS" starttime="******** 10:17:14.630" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:14.754" level="INFO">8</msg>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<status status="NOT RUN" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</branch>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</if>
<status status="PASS" starttime="******** 10:17:14.630" endtime="******** 10:17:14.754"/>
</iter>
<status status="PASS" starttime="******** 10:17:11.884" endtime="******** 10:17:14.754"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<status status="PASS" starttime="******** 10:17:11.712" endtime="******** 10:17:14.754"/>
</iter>
<iter>
<var name="${row_num}">3</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_row_element_path}</var>
<arg>${calendar_body_rows_element_path}</arg>
<arg>[${row_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:14.754" level="INFO">${curr_row_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3]</msg>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${col_element_path}</var>
<arg>${curr_row_element_path}</arg>
<arg>/td</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:14.754" level="INFO">${col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td</msg>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:14.754"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${cols}</var>
<arg>${col_element_path}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:17:14.858" level="INFO">${cols} = 7</msg>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:14.858"/>
</kw>
<for flavor="IN RANGE">
<var>${col_num}</var>
<value>1</value>
<value>${cols+1}</value>
<iter>
<var name="${col_num}">1</var>
<kw name="Catenate" library="BuiltIn">
<var>${curr_col_element_path}</var>
<arg>${col_element_path}</arg>
<arg>[${col_num}]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:14.859" level="INFO">${curr_col_element_path} = xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [1]</msg>
<status status="PASS" starttime="******** 10:17:14.859" endtime="******** 10:17:14.859"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${campaign_date}</var>
<arg>${curr_col_element_path}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:15.227" level="INFO">${campaign_date} = 9</msg>
<status status="PASS" starttime="******** 10:17:14.859" endtime="******** 10:17:15.227"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_date}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:15.227" level="INFO">9</msg>
<status status="PASS" starttime="******** 10:17:15.227" endtime="******** 10:17:15.227"/>
</kw>
<if>
<branch type="IF" condition="'${campaign_end_day.strip()}' == '${campaign_date.strip()}'">
<kw name="Set Focus To Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" starttime="******** 10:17:15.227" endtime="******** 10:17:15.362"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${row_num}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:15.363" level="INFO">${row_num} = 0</msg>
<status status="PASS" starttime="******** 10:17:15.363" endtime="******** 10:17:15.363"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${bool_end_date_selected}</var>
<arg>${True}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:15.363" level="INFO">${bool_end_date_selected} = True</msg>
<status status="PASS" starttime="******** 10:17:15.363" endtime="******** 10:17:15.363"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${curr_col_element_path}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:15.363" level="INFO">Clicking element 'xpath=//mat-card[@id='testing'] /descendant::tbody /tr [3] /td [1]'.</msg>
<status status="PASS" starttime="******** 10:17:15.363" endtime="******** 10:17:15.907"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Campaign End Date Selected!</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:15.908" level="INFO">Campaign End Date Selected!</msg>
<status status="PASS" starttime="******** 10:17:15.907" endtime="******** 10:17:15.908"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>EndDateSelection.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:17:16.055" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="EndDateSelection.png"&gt;&lt;img src="EndDateSelection.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:17:15.908" endtime="******** 10:17:16.055"/>
</kw>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 10:17:16.055" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:16.055"/>
</kw>
<status status="PASS" starttime="******** 10:17:15.227" endtime="******** 10:17:16.055"/>
</branch>
<status status="PASS" starttime="******** 10:17:15.227" endtime="******** 10:17:16.055"/>
</if>
<status status="PASS" starttime="******** 10:17:14.858" endtime="******** 10:17:16.055"/>
</iter>
<status status="PASS" starttime="******** 10:17:14.858" endtime="******** 10:17:16.055"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${True}</arg>
<arg>Exit For Loop</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Exit For Loop" library="BuiltIn">
<doc>Stops executing the enclosing FOR loop.</doc>
<msg timestamp="******** 10:17:16.055" level="INFO">Exiting for loop altogether.</msg>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:16.055"/>
</kw>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:16.055"/>
</kw>
<status status="PASS" starttime="******** 10:17:14.754" endtime="******** 10:17:16.055"/>
</iter>
<status status="PASS" starttime="******** 10:17:10.570" endtime="******** 10:17:16.055"/>
</for>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${bool_end_date_selected} == ${False}</arg>
<arg>Fail</arg>
<arg>Campaign end date: '${campaign_end_day.strip()}' was not selected!!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:16.055"/>
</kw>
<status status="PASS" starttime="******** 10:17:06.903" endtime="******** 10:17:16.055"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAPTURED_CAMPAIGN_START_DATE</arg>
<arg>${campaign_required_start_date}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:17:16.055" level="INFO">Environment variable 'CAPTURED_CAMPAIGN_START_DATE' set to value '2025-02-07 00:00:00.000'.</msg>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:16.055"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAPTURED_CAMPAIGN_END_DATE</arg>
<arg>${campaign_required_end_date}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:17:16.055" level="INFO">Environment variable 'CAPTURED_CAMPAIGN_END_DATE' set to value '2025-02-09 00:00:00.000'.</msg>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:16.055"/>
</kw>
<status status="PASS" starttime="******** 10:17:06.879" endtime="******** 10:17:16.055"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:18.056" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:17:16.055" endtime="******** 10:17:18.056"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>CAPTURED_CAMPAIGN_NAME</arg>
<arg>${CAMPAIGN_NAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:17:18.057" level="INFO">Environment variable 'CAPTURED_CAMPAIGN_NAME' set to value 'DiminuentCampaign'.</msg>
<status status="PASS" starttime="******** 10:17:18.056" endtime="******** 10:17:18.057"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>FilloutYourCampaign.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:17:18.163" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="FilloutYourCampaign.png"&gt;&lt;img src="FilloutYourCampaign.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:17:18.057" endtime="******** 10:17:18.163"/>
</kw>
<status status="PASS" starttime="******** 10:16:53.540" endtime="******** 10:17:18.163"/>
</kw>
<kw name="And The user clicks on Next button in Fill Out your Campaign screen" library="Navigation">
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:17:18.163" endtime="******** 10:17:18.185"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:18.185" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[3]/button[2]/span[1]'.</msg>
<status status="PASS" starttime="******** 10:17:18.185" endtime="******** 10:17:18.258"/>
</kw>
<status status="PASS" starttime="******** 10:17:18.163" endtime="******** 10:17:18.258"/>
</kw>
<kw name="And The user captures marketing screen information" library="UploadMarkrtingScreen">
<arg>${CAMPAIGN_LANGUAGE}</arg>
<arg>${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>0</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:18.259" level="INFO">${counter} = 0</msg>
<status status="PASS" starttime="******** 10:17:18.259" endtime="******** 10:17:18.259"/>
</kw>
<kw name="Split String" library="String">
<var>@{campaign_laguages}</var>
<arg>${CAMPAIGN_LANGUAGE}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 10:17:18.260" level="INFO">@{campaign_laguages} = [ English | Afrikaans ]</msg>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.260"/>
</kw>
<kw name="Split String" library="String">
<var>@{campaign_images}</var>
<arg>${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}</arg>
<arg>,</arg>
<doc>Splits the ``string`` using ``separator`` as a delimiter string.</doc>
<msg timestamp="******** 10:17:18.260" level="INFO">@{campaign_images} = [ images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg | images\MarketingA_af_2.jpg ]</msg>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.260"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${campaign_laguages_length}</var>
<arg>${campaign_laguages}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 10:17:18.260" level="INFO">Length is 2</msg>
<msg timestamp="******** 10:17:18.260" level="INFO">${campaign_laguages_length} = 2</msg>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.260"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_laguages}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:18.260" level="INFO">['English', 'Afrikaans']</msg>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.260"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${campaign_images_length}</var>
<arg>${campaign_images}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="******** 10:17:18.260" level="INFO">Length is 2</msg>
<msg timestamp="******** 10:17:18.260" level="INFO">${campaign_images_length} = 2</msg>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.260"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${campaign_images}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:17:18.260" level="INFO">['images\\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg', 'images\\MarketingA_af_2.jpg']</msg>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.260"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>@{campaign_laguages}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:18.260" endtime="******** 10:17:18.371"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>@{campaign_images}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:18.371" endtime="******** 10:17:18.371"/>
</kw>
<for flavor="IN">
<var>${language}</var>
<value>@{campaign_laguages}</value>
<iter>
<var name="${language}">English</var>
<kw name="Log To Console" library="BuiltIn">
<arg>Language is</arg>
<arg>:</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:18.371" endtime="******** 10:17:18.387"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${current_image}</var>
<arg>''</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:18.387" level="INFO">${current_image} = ''</msg>
<status status="PASS" starttime="******** 10:17:18.387" endtime="******** 10:17:18.387"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<arg>Fail</arg>
<arg>Please provide an image path variable for '${language.strip()}' language!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:18.387" endtime="******** 10:17:18.388"/>
</kw>
<kw name="Exit For Loop If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<doc>Stops executing the enclosing FOR loop if the ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:18.388" endtime="******** 10:17:18.388"/>
</kw>
<kw name="Select from dropdown" library="Navigation">
<arg>${LANGUAGE_DROPDOWN}</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:18.388" endtime="******** 10:17:18.388"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:20.388" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:17:18.388" endtime="******** 10:17:20.388"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:20.389" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="language"]'.</msg>
<status status="PASS" starttime="******** 10:17:20.388" endtime="******** 10:17:22.265"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:27.265" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:17:22.265" endtime="******** 10:17:27.265"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:27.265" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 10:17:27.265" endtime="******** 10:17:27.265"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:27.265" endtime="******** 10:17:27.359"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:27.467" level="INFO">${path_string} = //span[text()=' English </msg>
<status status="PASS" starttime="******** 10:17:27.365" endtime="******** 10:17:27.467"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:27.473" endtime="******** 10:17:27.487"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:27.499" level="INFO">${path_string} = //span[text()=' English ']</msg>
<status status="PASS" starttime="******** 10:17:27.499" endtime="******** 10:17:27.499"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:27.499" endtime="******** 10:17:27.711"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:17:27.711" endtime="******** 10:17:27.954"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:27.954" level="INFO">Clicking element 'xpath=//span[text()=' English ']'.</msg>
<status status="PASS" starttime="******** 10:17:27.954" endtime="******** 10:17:30.054"/>
</kw>
<status status="PASS" starttime="******** 10:17:18.388" endtime="******** 10:17:30.054"/>
</kw>
<kw name="Get Path" library="Utility">
<var>${image_directory}</var>
<msg timestamp="******** 10:17:30.054" level="INFO">${image_directory} = C:\Users\<USER>\source\repos\alternative_physical_channels</msg>
<status status="PASS" starttime="******** 10:17:30.054" endtime="******** 10:17:30.054"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${final_image_path}</var>
<arg>SEPARATOR=\\</arg>
<arg>${image_directory}</arg>
<arg>future_fit_architecture_portal</arg>
<arg>${campaign_images[${counter}].strip()}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:30.054" level="INFO">${final_image_path} = C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg</msg>
<status status="PASS" starttime="******** 10:17:30.054" endtime="******** 10:17:30.054"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Image path is : ${campaign_images[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:30.054" endtime="******** 10:17:30.166"/>
</kw>
<kw name="Choose File" library="SeleniumLibrary">
<arg>xpath://input[@value='select' and @class='ng-star-inserted']</arg>
<arg>${final_image_path}</arg>
<doc>Inputs the ``file_path`` into the file input field ``locator``.</doc>
<msg timestamp="******** 10:17:30.166" level="INFO">Sending C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\3049_2010091absagenericatms_freedomday365x470-65eb2169b2de4_en.jpg to browser.</msg>
<status status="PASS" starttime="******** 10:17:30.166" endtime="******** 10:17:32.994"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:35.994" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 10:17:32.994" endtime="******** 10:17:35.994"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>UploadMarkrtingScreen.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:17:36.131" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="UploadMarkrtingScreen.png"&gt;&lt;img src="UploadMarkrtingScreen.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:17:35.994" endtime="******** 10:17:36.131"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>${counter}+1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:36.131" level="INFO">${counter} = 0+1</msg>
<status status="PASS" starttime="******** 10:17:36.131" endtime="******** 10:17:36.131"/>
</kw>
<status status="PASS" starttime="******** 10:17:18.371" endtime="******** 10:17:36.131"/>
</iter>
<iter>
<var name="${language}">Afrikaans</var>
<kw name="Log To Console" library="BuiltIn">
<arg>Language is</arg>
<arg>:</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:36.131" endtime="******** 10:17:36.153"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${current_image}</var>
<arg>''</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:36.153" level="INFO">${current_image} = ''</msg>
<status status="PASS" starttime="******** 10:17:36.153" endtime="******** 10:17:36.153"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<arg>Fail</arg>
<arg>Please provide an image path variable for '${language.strip()}' language!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:36.153" endtime="******** 10:17:36.153"/>
</kw>
<kw name="Exit For Loop If" library="BuiltIn">
<arg>(${campaign_images_length}-1) &lt; ${counter}</arg>
<doc>Stops executing the enclosing FOR loop if the ``condition`` is true.</doc>
<status status="PASS" starttime="******** 10:17:36.154" endtime="******** 10:17:36.154"/>
</kw>
<kw name="Select from dropdown" library="Navigation">
<arg>${LANGUAGE_DROPDOWN}</arg>
<arg>${campaign_laguages[${counter}].strip()}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:36.155" endtime="******** 10:17:36.159"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:38.162" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 10:17:36.159" endtime="******** 10:17:38.162"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:38.162" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="language"]'.</msg>
<status status="PASS" starttime="******** 10:17:38.162" endtime="******** 10:17:38.227"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:43.228" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:17:38.227" endtime="******** 10:17:43.228"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:43.228" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 10:17:43.228" endtime="******** 10:17:43.228"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:43.229" endtime="******** 10:17:43.245"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:43.245" level="INFO">${path_string} = //span[text()=' Afrikaans </msg>
<status status="PASS" starttime="******** 10:17:43.245" endtime="******** 10:17:43.245"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:43.245" endtime="******** 10:17:43.245"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:43.245" level="INFO">${path_string} = //span[text()=' Afrikaans ']</msg>
<status status="PASS" starttime="******** 10:17:43.245" endtime="******** 10:17:43.245"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:43.245" endtime="******** 10:17:43.245"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 10:17:43.245" endtime="******** 10:17:43.269"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:43.269" level="INFO">Clicking element 'xpath=//span[text()=' Afrikaans ']'.</msg>
<status status="PASS" starttime="******** 10:17:43.269" endtime="******** 10:17:43.526"/>
</kw>
<status status="PASS" starttime="******** 10:17:36.154" endtime="******** 10:17:43.526"/>
</kw>
<kw name="Get Path" library="Utility">
<var>${image_directory}</var>
<msg timestamp="******** 10:17:43.527" level="INFO">${image_directory} = C:\Users\<USER>\source\repos\alternative_physical_channels</msg>
<status status="PASS" starttime="******** 10:17:43.527" endtime="******** 10:17:43.527"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${final_image_path}</var>
<arg>SEPARATOR=\\</arg>
<arg>${image_directory}</arg>
<arg>future_fit_architecture_portal</arg>
<arg>${campaign_images[${counter}].strip()}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 10:17:43.528" level="INFO">${final_image_path} = C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\MarketingA_af_2.jpg</msg>
<status status="PASS" starttime="******** 10:17:43.527" endtime="******** 10:17:43.528"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Image path is : ${campaign_images[${counter}].strip()}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:43.529" endtime="******** 10:17:43.601"/>
</kw>
<kw name="Choose File" library="SeleniumLibrary">
<arg>xpath://input[@value='select' and @class='ng-star-inserted']</arg>
<arg>${final_image_path}</arg>
<doc>Inputs the ``file_path`` into the file input field ``locator``.</doc>
<msg timestamp="******** 10:17:43.601" level="INFO">Sending C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\images\MarketingA_af_2.jpg to browser.</msg>
<status status="PASS" starttime="******** 10:17:43.601" endtime="******** 10:17:44.730"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:47.754" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="******** 10:17:44.730" endtime="******** 10:17:47.754"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>UploadMarkrtingScreen.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 10:17:47.931" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="UploadMarkrtingScreen.png"&gt;&lt;img src="UploadMarkrtingScreen.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 10:17:47.754" endtime="******** 10:17:47.931"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${counter}</var>
<arg>${counter}+1</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:47.931" level="INFO">${counter} = 0+1+1</msg>
<status status="PASS" starttime="******** 10:17:47.931" endtime="******** 10:17:47.931"/>
</kw>
<status status="PASS" starttime="******** 10:17:36.131" endtime="******** 10:17:47.931"/>
</iter>
<status status="PASS" starttime="******** 10:17:18.371" endtime="******** 10:17:47.931"/>
</for>
<status status="PASS" starttime="******** 10:17:18.259" endtime="******** 10:17:47.931"/>
</kw>
<kw name="And The user saves the campaign" library="UploadMarkrtingScreen">
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 10:17:47.931" endtime="******** 10:17:47.954"/>
</kw>
<status status="PASS" starttime="******** 10:17:47.931" endtime="******** 10:17:47.954"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CAPTURE_CAMPAIGN_SAVE_BUTTON}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 10:17:47.954" level="INFO">Clicking element 'xpath=//span[text()='Save']/parent::button'.</msg>
<status status="PASS" starttime="******** 10:17:47.954" endtime="******** 10:17:48.166"/>
</kw>
<status status="PASS" starttime="******** 10:17:47.931" endtime="******** 10:17:48.166"/>
</kw>
<kw name="Then The captured campaign must exist on the database" library="UploadMarkrtingScreen">
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${campaign_captured_name}</var>
<arg>CAPTURED_CAMPAIGN_NAME</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:17:48.166" level="INFO">${campaign_captured_name} = DiminuentCampaign</msg>
<status status="PASS" starttime="******** 10:17:48.166" endtime="******** 10:17:48.166"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${campaign_start_date}</var>
<arg>CAPTURED_CAMPAIGN_START_DATE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:17:48.166" level="INFO">${campaign_start_date} = 2025-02-07 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:48.166" endtime="******** 10:17:48.166"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${campaign_end_date}</var>
<arg>CAPTURED_CAMPAIGN_END_DATE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:17:48.166" level="INFO">${campaign_end_date} = 2025-02-09 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:48.166" endtime="******** 10:17:48.166"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Name to verify on the DB is: ${campaign_captured_name}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:48.166" level="INFO">Campaign Name to verify on the DB is: DiminuentCampaign</msg>
<status status="PASS" starttime="******** 10:17:48.166" endtime="******** 10:17:48.166"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Start Date is: ${campaign_start_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">Campaign Start Date is: 2025-02-07 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign End Date is: ${campaign_end_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">Campaign End Date is: 2025-02-09 00:00:00.000</msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Replace String" library="String">
<var>${modified_campaign_start_date_one}</var>
<arg>${campaign_start_date}</arg>
<arg>.000</arg>
<arg>${SPACE}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">${modified_campaign_start_date_one} = 2025-02-07 00:00:00 </msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${modified_campaign_start_date}</var>
<arg>${modified_campaign_start_date_one.strip()}</arg>
<arg>exclude_millis=yes</arg>
<arg>date_format=%Y-%m-%d %H:%M:%S</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">${modified_campaign_start_date} = 2025-02-07 00:00:00</msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Replace String" library="String">
<var>${modified_campaign_end_date_one}</var>
<arg>${campaign_end_date}</arg>
<arg>.000</arg>
<arg>${SPACE}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">${modified_campaign_end_date_one} = 2025-02-09 00:00:00 </msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Convert Date" library="DateTime">
<var>${modified_campaign_end_date}</var>
<arg>${modified_campaign_end_date_one.strip()}</arg>
<arg>exclude_millis=yes</arg>
<arg>date_format=%Y-%m-%d %H:%M:%S</arg>
<doc>Converts between supported `date formats`.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">${modified_campaign_end_date} = 2025-02-09 00:00:00</msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Replace String" library="String">
<var>${modified_campaign_end_date}</var>
<arg>${modified_campaign_end_date}</arg>
<arg>00:00:00</arg>
<arg>23:59:59</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">${modified_campaign_end_date} = 2025-02-09 23:59:59</msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Replace String" library="String">
<var>${sql_query}</var>
<arg>${CAPTURED_CAMPAIGN_QUERY}</arg>
<arg>camp_name</arg>
<arg>${campaign_captured_name}</arg>
<doc>Replaces ``search_for`` in the given ``string`` with ``replace_with``.</doc>
<msg timestamp="******** 10:17:48.170" level="INFO">${sql_query} = SELECT * FROM ATM_Marketing.Campaign where campaignName = 'DiminuentCampaign'</msg>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.170"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${sql_query}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:17:48.170" endtime="******** 10:17:48.181"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>8s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:17:56.181" level="INFO">Slept 8 seconds</msg>
<status status="PASS" starttime="******** 10:17:48.181" endtime="******** 10:17:56.181"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${db_type}</var>
<arg>'MYSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:17:56.181" level="INFO">${db_type} = 'MYSQL'</msg>
<status status="PASS" starttime="******** 10:17:56.181" endtime="******** 10:17:56.181"/>
</kw>
<kw name="Execute SQL Query" library="DBUtility">
<var>${data_base_campaigns}</var>
<arg>${db_type}</arg>
<arg>${sql_query}</arg>
<arg>True</arg>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_data}</var>
<arg>${RETURN_DATA_BOOLEAN}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="******** 10:17:56.181" level="INFO">${return_data} = True</msg>
<status status="PASS" starttime="******** 10:17:56.181" endtime="******** 10:17:56.181"/>
</kw>
<kw name="Convert To Boolean" library="BuiltIn">
<var>${return_all}</var>
<arg>${RETURN_ALL_RECORDS}</arg>
<doc>Converts the given item to Boolean true or false.</doc>
<msg timestamp="******** 10:17:56.181" level="INFO">${return_all} = False</msg>
<status status="PASS" starttime="******** 10:17:56.181" endtime="******** 10:17:56.184"/>
</kw>
<kw name="Verify Data Using Database" library="CommonUtils">
<var>${data_found}</var>
<arg>${DB_TYPE}</arg>
<arg>${QUERY}</arg>
<arg>${return_data}</arg>
<arg>${return_all}</arg>
<arg>&amp;{FIELDS_TO_VALIDATE}</arg>
<msg timestamp="******** 10:17:57.696" level="INFO">connecting to MYSQL...
Property value fetched is:  oss-dsdc-01371.corp.dsarena.com
Property value fetched is:  ATM_Marketing
Property value fetched is:  app_account
Property value fetched is:  DG9UIMbXWs
connected to MSSQL...
Connected to MySQL Server version  8.0.37-29
You're connected to database:  ('ATM_Marketing',)
1 is the total number of records returned by the query executed.
Returning 1 record....</msg>
<msg timestamp="******** 10:17:57.696" level="INFO">${data_found} = {'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2025-02-09 23:59:59', 'campaignHistoryId': 1, 'campaignId': 'CNQ046v001Q12025', 'campaignName': 'DiminuentCampaign', 'campaignStartDate...</msg>
<status status="PASS" starttime="******** 10:17:56.184" endtime="******** 10:17:57.696"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>Failed</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 10:17:57.696" endtime="******** 10:17:57.696"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${null}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 10:17:57.696" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Should Not Contain" library="BuiltIn">
<arg>${data_found}</arg>
<arg>${EMPTY}</arg>
<doc>Fails if ``container`` contains ``item`` one or more times.</doc>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<return>
<value>${data_found}</value>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</return>
<msg timestamp="******** 10:17:57.698" level="INFO">${data_base_campaigns} = {'campaignBy': 'Thabo Benjamin Setuke (ZA)', 'campaignEndDate': '2025-02-09 23:59:59', 'campaignHistoryId': 1, 'campaignId': 'CNQ046v001Q12025', 'campaignName': 'DiminuentCampaign', 'campaignStartDate...</msg>
<status status="PASS" starttime="******** 10:17:56.181" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_campaign_name}</var>
<arg>${data_base_campaigns}</arg>
<arg>campaignName</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 10:17:57.698" level="INFO">${db_campaign_name} = DiminuentCampaign</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_campaign_start_date}</var>
<arg>${data_base_campaigns}</arg>
<arg>campaignStartDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 10:17:57.698" level="INFO">${db_campaign_start_date} = 2025-02-07 00:00:00</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_campaign_end_date}</var>
<arg>${data_base_campaigns}</arg>
<arg>campaignEndDate</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 10:17:57.698" level="INFO">${db_campaign_end_date} = 2025-02-09 23:59:59</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_is_approved}</var>
<arg>${data_base_campaigns}</arg>
<arg>isApproved</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 10:17:57.698" level="INFO">${db_is_approved} = 0</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Get From Dictionary" library="Collections">
<var>${db_is_isactive}</var>
<arg>${data_base_campaigns}</arg>
<arg>isActive</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<msg timestamp="******** 10:17:57.698" level="INFO">${db_is_isactive} = 1</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Name from the DB is:</arg>
<arg>${db_campaign_name}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:57.698" level="INFO">Campaign Name from the DB is:</msg>
<msg timestamp="******** 10:17:57.698" level="INFO">DiminuentCampaign</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.698"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Start Date from the DB is: ${db_campaign_start_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:57.704" level="INFO">Campaign Start Date from the DB is: 2025-02-07 00:00:00</msg>
<status status="PASS" starttime="******** 10:17:57.698" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign End Date from the DB is:</arg>
<arg>${db_campaign_end_date}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:57.704" level="INFO">Campaign End Date from the DB is:</msg>
<msg timestamp="******** 10:17:57.704" level="INFO">2025-02-09 23:59:59</msg>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Active Status from the DB is:</arg>
<arg>${db_is_isactive}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:57.704" level="INFO">Campaign Active Status from the DB is:</msg>
<msg timestamp="******** 10:17:57.704" level="INFO">1</msg>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Campaign Approved Status from the DB is:</arg>
<arg>${db_is_approved}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:57.704" level="INFO">Campaign Approved Status from the DB is:</msg>
<msg timestamp="******** 10:17:57.704" level="INFO">0</msg>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${campaign_captured_name}</arg>
<arg>${db_campaign_name}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${modified_campaign_start_date}</arg>
<arg>${db_campaign_start_date}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${modified_campaign_end_date}</arg>
<arg>${db_campaign_end_date}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${db_is_isactive}' != '1'</arg>
<arg>Fail</arg>
<arg>The campaign named '${campaign_captured_name}' is not active on the Database</arg>
<arg>ELSE IF</arg>
<arg>'${db_is_approved}' != '0'</arg>
<arg>Fail</arg>
<arg>The campaign named '${campaign_captured_name}' is active and also approved on the Database after it was captured.</arg>
<arg>ELSE</arg>
<arg>Log Many</arg>
<arg>The campaign named '${campaign_captured_name}', was found on the database. The start date of the campaign is</arg>
<arg>'${db_campaign_start_date}' and the end date is '${db_campaign_end_date}'.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log Many" library="BuiltIn">
<arg>The campaign named '${campaign_captured_name}', was found on the database. The start date of the campaign is</arg>
<arg>'${db_campaign_start_date}' and the end date is '${db_campaign_end_date}'.</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="******** 10:17:57.704" level="INFO">The campaign named 'DiminuentCampaign', was found on the database. The start date of the campaign is</msg>
<msg timestamp="******** 10:17:57.704" level="INFO">'2025-02-07 00:00:00' and the end date is '2025-02-09 23:59:59'.</msg>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<status status="PASS" starttime="******** 10:17:57.704" endtime="******** 10:17:57.704"/>
</kw>
<status status="PASS" starttime="******** 10:17:48.166" endtime="******** 10:17:57.704"/>
</kw>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:17:57.704"/>
</kw>
<kw name="End Web Test" library="Login" type="TEARDOWN">
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:18:02.736" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 10:17:57.735" endtime="******** 10:18:02.736"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="******** 10:18:02.736" endtime="******** 10:18:06.082"/>
</kw>
<status status="PASS" starttime="******** 10:17:57.735" endtime="******** 10:18:06.082"/>
</kw>
<doc>Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English  And Afrikaans</doc>
<tag>FFA_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 10:15:47.818" endtime="******** 10:18:06.185"/>
</test>
<doc>Create Campaign page</doc>
<status status="PASS" starttime="******** 10:15:46.348" endtime="******** 10:18:06.353"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 10:15:48.860" level="WARN">There was error during termination of process</msg>
<msg timestamp="******** 10:16:04.032" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:16:20.370" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:16:26.589" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
