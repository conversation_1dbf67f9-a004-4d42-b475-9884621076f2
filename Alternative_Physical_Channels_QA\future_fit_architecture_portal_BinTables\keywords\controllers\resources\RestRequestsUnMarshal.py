from typing import Union, List


class ReadApiResponse:
    def __init__(self, data: Union[dict, List]):
        if not isinstance(data, (dict, list)):
            raise ValueError("Argument must be a dict or a list.")

        self.data = data
        print('Data is ', self.data.get('data'))
        self.api_response = ApiResponse(
            self.data.get('status'),
            self.data.get('status_code'),
            self.data.get('data')
        )

    # Example getter methods
    def get_field(self, field_name):
        # Get a field from the response
        return self.data.get(field_name)

    def get_list_of_server_versions(self):
        # Get a field from the response
        return self.data.get('data')

    def get_upload_management_information_details(self):
        # Get a field from the response
        return self.data.get('data')

    def get_bin_overview_metrics(self):
        # Get a field from the response
        return self.data.get('data')

    def get_created_bin_type_id(self):
        # Get a field from the response
        return self.data.get('data')

    def get_updated_bin_type_id(self):
        # Get a field from the response
        return self.data.get('data')

    def get_all_bin_types(self):
        # Get a field from the response
        return self.data.get('data')

    def get_api_status(self):
        """Get the status from the API response"""
        if self.api_response:
            return self.api_response.get_status()
        else:
            raise ValueError("API response not initialized.")

    def get_api_status_code(self):
        """Get the status code from the API response"""
        if self.api_response:
            return self.api_response.get_status_code()
        else:
            raise ValueError("API response not initialized.")

    def get_api_data_type(self):
        """Get the 'type' field from the 'data' object"""
        if self.api_response:
            return self.api_response.get_data().get_type()
        else:
            raise ValueError("API response not initialized.")

    def get_api_data_title(self):
        """Get the 'title' field from the 'data' object"""
        if self.api_response:
            return self.api_response.get_data().get_title()
        else:
            raise ValueError("API response not initialized.")

    def get_api_data_status(self):
        """Get the 'status' field from the 'data' object"""
        if self.api_response:
            return self.api_response.get_data().get_status()
        else:
            raise ValueError("API response not initialized.")

    def get_api_data_detail(self):
        """Get the 'detail' field from the 'data' object"""
        if self.api_response:
            return self.api_response.get_data().get_detail()
        else:
            raise ValueError("API response not initialized.")

    def get_api_data_trace_id(self):
        """Get the 'traceId' field from the 'data' object"""
        if self.api_response:
            return self.api_response.get_data().get_trace_id()
        else:
            raise ValueError("API response not initialized.")

    def get_api_data_errors(self):
        """Get the 'errors' field from the 'data' object"""
        if self.api_response:
            return self.api_response.get_data().get_errors()
        else:
            raise ValueError("API response not initialized.")

    # method to fetch errors for a specific field regardless of index
    def get_errors_for_field(self, field_name):
        """Get all errors associated with a specific field name (e.g., 'BinNumber', 'actionDate')"""
        if self.api_response:
            errors = self.api_response.get_data().get_errors_for_field(field_name)
            if errors:
                return errors
            else:
                return "No errors found for the field"
        else:
            raise ValueError("API response not initialized.")

    def get_bin_type_id_details(self):
        if self.api_response:
            return self.api_response.get_bin_type_id_details()
        else:
            raise ValueError("API response not initialized.")

    def get_bin_type_details(self):
        if self.api_response:
            return self.api_response.get_bin_type_details()
        else:
            raise ValueError("API response not initialized.")

    def get_bin_details(self):
        if self.api_response:
            return self.api_response.get_bin_details()
        else:
            raise ValueError("API response not initialized.")

    def get_bins_to_review_details(self):
        if self.api_response:
            return self.api_response.get_bins_to_review_details()
        else:
            raise ValueError("API response not initialized.")

    def get_bins_by_bin_type_name_details(self):
        if self.api_response:
            return self.api_response.get_bins_by_bin_type_name_details()
        else:
            raise ValueError("API response not initialized.")

    def get_bin_by_id_details(self):
        if self.api_response:
            return self.api_response.get_bin_by_id_details()
        else:
            raise ValueError("API response not initialized.")

    def get_download_bin_table_details(self):
        if self.api_response:
            return self.api_response.get_download_bin_table_details()
        else:
            raise ValueError("API response not initialized.")


class DownloadBinTable:
    def __init__(self, data, server_version, check_sum):
        # Initialize the object with the given data
        self.data = data
        self.server_version = server_version
        self.check_sum = check_sum
        print('DownloadBinTable initialized...')

    # Getters for the 'data' dictionary keys

    def get_field(self, field_name):
        # Get a field from the response
        return self.data.get(field_name, None)

    def get_bin_table_domestic(self):
        return self.data.get("Domestic", None)

    def get_bin_table_on_us(self):
        return self.data.get("On-Us", None)

    def get_bin_table_contactless(self):
        return self.data.get("Contactless", None)

    def get_bin_table_invalid(self):
        return self.data.get("Invalid", None)

    def get_bin_table_token(self):
        return self.data.get("Token", None)

    # Getter for 'serverVersion'
    def get_server_version(self):
        return self.server_version

    # Getter for 'checkSum'
    def get_check_sum(self):
        return self.check_sum

    # String representation of the object
    def __str__(self):
        return (f"DownloadBinTable(\n"
                f"  binTableDomestic: {self.get_bin_table_domestic()},\n"
                f"  binTableOnUs: {self.get_bin_table_on_us()},\n"
                f"  binTableContactless: {self.get_bin_table_contactless()},\n"
                f"  binTableInvalid: {self.get_bin_table_invalid()},\n"
                f"  serverVersion: {self.get_server_version()},\n"
                f"  checkSum: {self.get_check_sum()}\n)"
                )


class BinType:
    def __init__(self, bin_types: List[str]):
        self.binTypes = bin_types  # This is a list (array) of binTypes

    def get_bin_type_id(self):
        return self.binTypes['binTypeId']

    def get_bin_type(self):
        return self.binTypes['binType']


# Define a class for Bin
class Bin:
    def __init__(self, id: str, bin_number: str, action_date: str, bin_types: List[str]):
        self.id = id
        self.binNumber = bin_number
        self.actionDate = action_date
        self.binTypes = bin_types  # This is a list (array) of binTypes

    def get_id(self):
        return self.id

    def get_bin_number(self):
        return self.binNumber

    def get_action_date(self):
        return self.actionDate

    def get_bin_types(self) -> List[BinType]:
        return [BinType(bin_type) for bin_type in self.binTypes]

    def __str__(self):
        return f"Bin(id={self.id}, binNumber={self.binNumber}, actionDate={self.actionDate}, binTypes={self.binTypes})"


class BinTypeDetails:
    def __init__(self, id: str, bin_type_name: str, bin_type_description: str, captured_by: str, captured_date: str):
        self.id = id
        self.bin_type_name = bin_type_name
        self.bin_type_description = bin_type_description
        self.captured_by = captured_by
        self.captured_date = captured_date

    def get_id(self):
        return self.id

    def get_bin_type_name(self):
        return self.bin_type_name

    def get_bin_type_description(self):
        return self.bin_type_description

    def get_bin_type_captured_by(self):
        return self.captured_by

    def get_bin_type_captured_date(self):
        return self.captured_date

    def __str__(self):
        return f"BinTypeDetails(id={self.id}, name={self.bin_type_name}, description={self.bin_type_description}, capturedBy={self.captured_by}, capturedDate={self.captured_date})"


class BinToReview:
    def __init__(self, id: str, captured_date: str, captured_by: str,
                 bin_number: str, action_date: str, review_status: str, outcome: str,
                 rejected_comment: str, reviewed_by: str,
                 reviewed_date: str, latest_server_number: str, bin_type: str):
        self.id = id
        self.captured_date = captured_date
        self.captured_by = captured_by
        self.bin_number = bin_number
        self.action_date = action_date
        self.review_status = review_status
        self.outcome = outcome

        self.rejected_comment = rejected_comment
        self.reviewed_by = reviewed_by
        self.reviewed_date = reviewed_date
        self.latest_server_number = latest_server_number
        self.bin_type = bin_type

    def get_id(self):
        return self.id

    def get_captured_date(self):
        return self.captured_date

    def get_captured_by(self):
        return self.captured_by

    def get_bin_number(self):
        return self.bin_number

    def get_action_date(self):
        return self.action_date

    def get_review_status(self):
        return self.review_status

    def get_outcome(self):
        return self.outcome

    def get_rejected_comment(self):
        return self.rejected_comment

    def get_reviewed_by(self):
        return self.reviewed_by

    def get_reviewed_date(self):
        return self.reviewed_date

    def get_latest_server_number(self):
        return self.latest_server_number

    def get_bin_type(self):
        return self.bin_type

    def __str__(self):
        return (
            f"BinToReview(id={self.id}, capturedDate={self.captured_date}, capturedBy={self.captured_by}, binNumber={self.bin_number},"
            f"actionDate={self.action_date}), reviewStatus={self.review_status}), outcome={self.outcome}),"
            f"rejectedComment={self.rejected_comment}), reviewedBy={self.reviewed_by}),"
            f"reviewedDate={self.reviewed_date}), latestServerNumber={self.latest_server_number}),"
            f"binType={self.bin_type})")


class BinById:
    def __init__(self, id: str, captured_date: str, captured_by: str,
                 bin_number: str, action_date: str, review_status: str, outcome: str,
                 to_be_actioned_by: str, rejected_comment: str, reviewed_by: str,
                 reviewed_date: str, latest_server_number: str, bin_types: List[str]):
        self.id = id
        self.captured_date = captured_date
        self.captured_by = captured_by
        self.bin_number = bin_number
        self.action_date = action_date
        self.review_status = review_status
        self.outcome = outcome
        self.to_be_actioned_by = to_be_actioned_by
        self.rejected_comment = rejected_comment
        self.reviewed_by = reviewed_by
        self.reviewed_date = reviewed_date
        self.latest_server_number = latest_server_number
        self.bin_types = bin_types

    def get_id(self):
        return self.id

    def get_captured_date(self):
        return self.captured_date

    def get_captured_by(self):
        return self.captured_by

    def get_bin_number(self):
        return self.bin_number

    def get_action_date(self):
        return self.action_date

    def get_review_status(self):
        return self.review_status

    def get_outcome(self):
        return self.outcome

    def get_to_be_actioned_by(self):
        return self.to_be_actioned_by

    def get_rejected_comment(self):
        return self.rejected_comment

    def get_reviewed_by(self):
        return self.reviewed_by

    def get_reviewed_date(self):
        return self.reviewed_date

    def get_latest_server_number(self):
        return self.latest_server_number

    def get_bin_types(self) -> List[BinType]:
        return [BinType(bin_type) for bin_type in self.bin_types]

    def __str__(self):
        return (
            f"BinById(id={self.id}, capturedDate={self.captured_date}, capturedBy={self.captured_by}, binNumber={self.bin_number},"
            f"actionDate={self.action_date}), reviewStatus={self.review_status}), outcome={self.outcome}),"
            f"toBeActionedBy={self.to_be_actioned_by}), rejectedComment={self.rejected_comment}), reviewedBy={self.reviewed_by}),"
            f"reviewedDate={self.reviewed_date}), latestServerNumber={self.latest_server_number}),"
            f"binTypes={self.bin_types})")


class BinsByBinTypeName:
    def __init__(self, id: str, bin_number: str, bin_type: str, action_date: str):
        self.id = id
        self.bin_number = bin_number
        self.bin_type = bin_type
        self.action_date = action_date

    def get_id(self):
        return self.id

    def get_bin_number(self):
        return self.bin_number

    def get_action_date(self):
        return self.action_date

    def get_bin_type(self):
        return self.bin_type

    def __str__(self):
        return (
            f"BinsByBinTypeName(id={self.id}, binNumber={self.bin_number},"
            f" binType={self.bin_type}), actionDate={self.action_date})")


class ApiResponse:
    def __init__(self, status, status_code, data=None):
        self._status = status
        self._status_code = status_code

        # Check if the response was successful (status code 200-299)
        if 200 <= self._status_code < 300:
            # Success - Save response to the Successful Response Object
            self._data = data
        elif self._status_code == 401:
            self._data = 'The request is unauthenticated'
        else:
            # Failure - return status code and error message (with the body content)
            self._data = Data(**data)  # Passing the nested 'data' dictionary to Data class

    def get_status(self):
        return self._status

    def get_status_code(self):
        return self._status_code

    def get_data(self):
        return self._data

    def get_bin_details(self) -> List[Bin]:

        if isinstance(self._data, dict):
            # If 'data' is a dictionary, check for 'binTypes' key
            bin_types_list = self._data.get('binTypes', [])
            return [Bin(bin_type['id'], bin_type['binNumber'], bin_type['actionDate'], bin_type['binTypes']) for
                    bin_type in bin_types_list]
        elif isinstance(self._data, list):
            # If 'data' is a list, assume it directly contains user objects
            return [Bin(bin_type['id'], bin_type['binNumber'], bin_type['actionDate'], bin_type['binTypes']) for
                    bin_type in self._data]
        else:
            print("Error: Expected 'data' to be either a dictionary or a list.")
            return []

    def get_bins_to_review_details(self) -> List[BinToReview]:

        if isinstance(self._data, list):
            # If 'data' is a list, assume it directly contains Bin objects
            return [
                BinToReview(bin_details['id'], bin_details['capturedDate'], bin_details['capturedBy'],
                            bin_details['binNumber'],
                            bin_details['actionDate'], bin_details['reviewStatus'], bin_details['outcome'],
                            bin_details['rejectedComment'], bin_details['reviewedBy'], bin_details['reviewedDate'],
                            bin_details['latestServerNumber'], bin_details['binType']) for bin_details in self._data]
        else:
            print("Error: Expected 'data' to be a list.")
            return []

    def get_bins_by_bin_type_name_details(self) -> List[BinsByBinTypeName]:

        if isinstance(self._data, list):
            # If 'data' is a list, assume it directly contains Bin objects
            return [
                BinsByBinTypeName(bin_details['id'], bin_details['binNumber'], bin_details['binType'],
                                  bin_details['actionDate']) for bin_details in self._data]
        else:
            print("Error: Expected 'data' to be a list.")
            return []

    def get_bin_type_details(self) -> List[BinTypeDetails]:

        if isinstance(self._data, list):
            # If 'data' is a list, assume it directly contains Bin Types objects
            return [
                BinTypeDetails(bin_type_details['id'], bin_type_details['name'], bin_type_details['description'],
                               bin_type_details['capturedBy'], bin_type_details['capturedDate']) for bin_type_details in
                self._data]
        else:
            print("Error: Expected 'data' to be a Bin Types list.")
            return []

    def get_bin_type_id_details(self) -> {BinTypeDetails}:
        print("Initializing the Bin Type Details.")
        if isinstance(self._data, dict):
            # If 'data' is a dict, assume it directly contains Bin objects
            return {BinTypeDetails(self._data['id'], self._data['name'], self._data['description'],
                                   self._data['capturedBy'], self._data['capturedDate'])}
        else:
            print("Error: Expected 'data' to be a dict.")
            return []

    def get_download_bin_table_details(self) -> {DownloadBinTable}:

        if isinstance(self._data, dict):
            # If 'data' is a dict, assume it directly contains Bin objects
            return {DownloadBinTable(self._data['data'], self._data['serverVersion'],
                                     self._data['checkSum'])}
        else:
            print("Error: Expected 'data' to be a dict.")
            return []

    def get_bin_by_id_details(self) -> {BinById}:

        if isinstance(self._data, dict):
            # If 'data' is a dict, assume it directly contains Bin objects
            return {
                BinById(self._data['id'], self._data['capturedDate'], self._data['capturedBy'],
                        self._data['binNumber'],
                        self._data['actionDate'], self._data['reviewStatus'], self._data['outcome'],
                        self._data['toBeActionedBy'],
                        self._data['rejectedComment'], self._data['reviewedBy'], self._data['reviewedDate'],
                        self._data['latestServerNumber'], self._data['binTypes'])}
        else:
            print("Error: Expected 'data' to be a dict.")
            return []


class Data:
    def __init__(self, type=None, title=None, status=None, detail=None, traceId=None, errors=None, id=None):
        """
        Initializes the data fields from the response JSON.
        This class will handle both simple and complex error responses.
        """
        self._type = type
        self._title = title
        self._status = status
        self._detail = detail
        self._trace_id = traceId
        self._errors = errors if errors else {}

    # Getters for all the attributes
    def get_type(self):
        return self._type

    def get_title(self):
        return self._title

    def get_status(self):
        return self._status

    def get_detail(self):
        return self._detail

    def get_trace_id(self):
        return self._trace_id

    def get_errors(self):
        return self._errors

        # General getter method for error messages at any path

    def get_error_at(self, path):
        """
        Returns the error message(s) for a given error path (e.g., "$[0]", "[1].BinNumber", etc.).
        If the path doesn't exist, returns None.
        """
        return self._errors.get(path)

    # Method to fetch all errors related to a specific field name, regardless of index
    def get_errors_for_field(self, field_name):
        """
        Returns all errors associated with a specific field, regardless of the index number.
        Flattened to just the error messages (no list).
        """
        matching_errors = []
        for key, error_list in self._errors.items():
            if field_name in key:  # Check if the field name is part of the key
                matching_errors.extend(error_list)  # Add all errors for that field
        # Join errors into a single string, each error separated by a newline (or any separator you prefer)
        return "\n".join(matching_errors) if matching_errors else None

# Create Robot keywords
