*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control- Branch Dashboard- Validating the Custodian Edit Flow Via Maintenance
Suite Setup                 Set up environment variables               
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot



*** Variables ***


*** Keywords ***
The user validates the Custodian Edit Flow on Branch dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page

    And the user navigates to the ATM Control Dashboard  

    And the user selects a branch to access the Branch Dashboard 

    And the user clicks on Custodian Maintenance 

    And the user selects a Custodian to edit 

    And the user gets the current Custodian details

    And the user makes changes to the selected Custodian

    And the user saves the Custodian change on the front end 

    Then the user verifies that the Custodian change was successfully saved correctly to the database




| *Test Cases*                                                                                                                       |      *DOCUMENTATION*      | *TEST_ENVIRONMENT*   |
|   MQ_TC_11_Branch_Dashboard_validate_Custodian_Edit_feature    |    The user validates the Custodian Edit Flow on Branch dashboard |        Custodian Edit     |    MTGLA_UAT         | 