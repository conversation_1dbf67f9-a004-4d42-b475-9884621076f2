
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.edge.service import Service as Edge_Service
from selenium.webdriver.edge.options import Options as Edge_Options
import subprocess
import os
import time


def navigate(URL):
    logged_in_user = os.getlogin()
    print('Logged in User', logged_in_user)
    subprocess.call("TASKKILL /f  /IM  CHROME.EXE")
    subprocess.call("TASKKILL /f  /IM  CHROMEDRIVER.EXE")
    # Create an instance of ChromeOptions
    chrome_options = Options()
    # Configure ChromeOptions to run in headless mode
    chrome_options.add_argument('--headless=new')
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument('--ignore-certificate-errors')
    chrome_options.add_argument('--enable-features=NetworkServic')

    # Automatically download and install the latest ChromeDriver
    chrome_install = ChromeDriverManager().install()
    folder = os.path.dirname(chrome_install)
    chromedriver_path = os.path.join(folder, "chromedriver.exe")
    service = Service(chromedriver_path)

    # provide location where chrome stores profiles
    chrome_path = "C:/Users/"+logged_in_user+"/AppData/Local/Google/Chrome/User Data"
    print('STARTING CHROME FROM THIS PATH:',chrome_path)
    chrome_options.add_argument(r"--user-data-dir="+chrome_path)
    # provide the profile name with which we want to open browser
    chrome_options.add_argument(r'--profile-directory=Default')
    # Initialize the WebDriver with the configured ChromeOptions
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print('Initial URL is:', URL)

    driver.get(URL)
    # Wait for the page to load
    time.sleep(10)  # You can adjust this value based on your needs
    print('Redirect URL is:', driver.current_url)
    current_url= driver.current_url
    driver.close()
    driver.quit()
    subprocess.call("TASKKILL /f  /IM  CHROME.EXE")
    subprocess.call("TASKKILL /f  /IM  CHROMEDRIVER.EXE")
    return current_url

def navigate_with_edge(URL):
    # Set the path to the Edge WebDriver
    webdriver_path = 'C:/bin/msedgedriver.exe'

    # Create Edge options if needed
    edge_options = Edge_Options()
    # Configure EdgeOptions to run in headless mode
    edge_options.add_argument('--headless=new')
    edge_options.add_argument("--no-sandbox")
    edge_options.add_argument('--ignore-certificate-errors')
    edge_options.add_argument('--enable-features=NetworkServic')
    edge_options.add_argument('--disable-gpu')
    # Set up the service
    service = Edge_Service(webdriver_path)

    # Create a new instance of the Edge driver
    driver = webdriver.Edge(service=service, options=edge_options)
    print('Initial URL is:', URL)

    driver.get(URL)
    # Wait for the page to load
    time.sleep(10)  # You can adjust this value based on your needs
    print('Redirect URL is:', driver.current_url)
    current_url= driver.current_url
    driver.close()
    driver.quit()
    # Use subprocess.call to kill Edge browser and WebDriver processes
    subprocess.call(["taskkill", "/F", "/IM", "msedge.exe"])
    subprocess.call(["taskkill", "/F", "/IM", "msedgedriver.exe"])
    return current_url
