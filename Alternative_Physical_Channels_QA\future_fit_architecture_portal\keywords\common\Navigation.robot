*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Login to vms system

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                        ../../../common_utilities/Logout.robot
Resource                        GenericMethods.robot
*** Variables ***

${CAPTURE_CAMPAIGN_LINK}                            xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/div/mat-list-item/span/span[3]
${REGION_RADIO_BUTTON}                              xpath=//*[@id="mat-radio-5"]
${ATM_RADIO_BUTTON}                                 xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[1]/app-campaign-targeted/div[2]/mat-radio-group/mat-radio-button[2]/label/span[1]/span[2]
${REGIONAL_CAMPAIGN_DROPDOWN}                       xpath=//*[@id="cdk-step-content-0-0"]/app-campaign-targeted/div[2]/div[2]/mat-form-field
${REGIONAL_CAMPAIN_SELECTION}                       xpath=//span[text()=' Gauteng ']
${ATM_DROPDOWN}                                     xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[1]/app-campaign-targeted/div[2]/div[2]/mat-form-field/div/div[1]/div
${ATM_CAMPAIGN_SELECTION}                           xpath=/html/body/div[2]/div/div/div/mat-option/span
${NEXT_BUTTON}                                      xpath=//span[contains(text(),'Next')]/parent::button
${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}                xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[3]/button[2]/span[1]
${RADIO_BUTTON_SELECTOR1}                           //input[@value='
${RADIO_BUTTON_SELECTOR2}                           ]/parent::*/parent::*/parent::*
${DROPDOWN_SELECTOR1}                               //span[text()=
${DROPDOWN_SELECTOR2}                               ]
${DROPDOWN_ATM_SELECTOR1}                           //span[contains(text(),
${DROPDOWN_ATM_SELECTOR2}                           )]

*** Keywords ***
The user clicks on Next button
    Wait Until Element Is Enabled    ${NEXT_BUTTON}
    Click Element     ${Next_Button}

The user clicks on Next button in Fill Out your Campaign screen
    Wait Until Element Is Enabled    ${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}
    Click Element     ${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}

#Click on Capture campaign link
The user clicks on the Capture campaign link
    Log to console  --------------------------The user clicks the Capture campaign link
    Sleep    5s
    Run Keyword Until Success   Click Element     ${CAPTURE_CAMPAIGN_LINK}
    #Click Element      ${CAPTURE_CAMPAIGN_LINK}
    Wait Until page contains      Fill out Campaign Targeted

User select radio button
    [Arguments]  ${RADIO_BUTTON_VALUE}
    
    Log To Console    Selecting radio button ${RADIO_BUTTON_VALUE}
    Wait for spinner to disapear

    ${path_string} =   Catenate    SEPARATOR='  ${RADIO_BUTTON_SELECTOR1}

    Log To Console    String 1 ${path_string}
    ${path_string} =   Catenate    SEPARATOR=  ${path_string}  ${RADIO_BUTTON_VALUE}
    Log To Console    String 2 ${path_string}
    ${path_string} =   Catenate    SEPARATOR='  ${path_string}  ${RADIO_BUTTON_SELECTOR2}
    Log To Console    String 3 ${path_string}
    
    Wait Until Element Is Enabled    xpath=${path_string}
    
    Click Element    xpath=${path_string}

Select from dropdown 
    [Arguments]  ${DROPDOWN}  ${DROPDOWN_SELECTION_VALUE}
    
    Log To Console    Dropdown value is ${DROPDOWN_SELECTION_VALUE}

    Sleep    2s

    Click Element    ${DROPDOWN}

    Sleep    5s

    ${path_string} =   Catenate    SEPARATOR='  ${DROPDOWN_SELECTOR1}   ${SPACE}

    Log To Console    String 1 ${path_string}
    ${path_string} =   Catenate    SEPARATOR=  ${path_string}  ${DROPDOWN_SELECTION_VALUE}  ${SPACE}
    Log To Console    String 2 ${path_string}   
    ${path_string} =   Catenate    SEPARATOR='  ${path_string}  ${DROPDOWN_SELECTOR2}
    Log To Console    String 3 ${path_string}
    
    Wait Until Element Is Enabled    xpath=${path_string}
    Click Element    xpath=${path_string}

Select ATM from dropdown 
    [Arguments]  ${DROPDOWN}  ${DROPDOWN_SELECTION_VALUE}
    
    Log To Console    Dropdown value is ${DROPDOWN_SELECTION_VALUE}

    Sleep    2s

    Click Element    ${DROPDOWN}

    Sleep    5s

    ${path_string} =   Catenate    SEPARATOR='  ${DROPDOWN_ATM_SELECTOR1}

    Log To Console    String 1 ${path_string}
    ${path_string} =   Catenate    SEPARATOR='  ${path_string}  ${DROPDOWN_SELECTION_VALUE}
    Log To Console    String 2 ${path_string}   
    ${path_string} =   Catenate    SEPARATOR='  ${path_string}  ${DROPDOWN_ATM_SELECTOR2}
    Log To Console    String 3 ${path_string}
    
    Wait Until Element Is Enabled    xpath=${path_string}
    Click Element    xpath=${path_string}

The user clicks close Button 
    Click Element     xpath=//*[@id="mat-dialog-1"]/app-edit-dialog/mat-dialog-actions/button/span[1]/span

The user logs out 
    User logs out


Run Keyword Until Success
    [Arguments]         ${KW}   ${KWARGS}
    Wait Until Keyword Succeeds    30s    1s    ${KW}   ${KWARGS}

The user navigates to the Campaign Approvals page 
    Wait Until Element Is Visible    xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]
    ${Admin_Expansion}=    Get WebElement    xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]
    Click Element    ${Admin_Expansion}
    
    Wait for spinner to disapear
    ${Approvals_Button}=    Get WebElement    xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span
    Sleep    5s
    Wait Until Element Is Enabled    ${Approvals_Button}
    Click Element    ${Approvals_Button}
    Wait for spinner to disapear

    Wait for spinner to disapear

    Page Should Contain    Approvals

The user navigates back to the Campaign Approvals page
    Sleep    5s
    ${Approvals_Button}=    Get WebElement    xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span
    Wait Until Element Is Enabled    ${Approvals_Button}
    Click Element    ${Approvals_Button}
    Wait for spinner to disapear
    Sleep    5s
    Page Should Contain    APPROVALS
    Execute JavaScript    location.reload(true)
