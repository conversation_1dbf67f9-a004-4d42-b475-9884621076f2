from typing import List, Union


class CreateRESTRequest:
    def __init__(self, domain, server_version, bin_status_action):
        self.domain = domain
        self.server_version = server_version
        self.bin_status_action = bin_status_action

        self.params = {
            "serverVersion": self.server_version,
            "binStatusAction": self.bin_status_action # Adding a query parameter to filter the results by Bin Number
        }

    def get_endpoint(self):
        path = "/api/v1/bintables/all/managementinformation/getuploadmanagementinformation"
        url = f"{self.domain}{path}"
        return url

    def get_params(self):
        return self.params

