<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20241023 10:14:00.808">
   <suite name="Future Fit Portal" id="s1" source="C:\Users\<USER>\source\repos\alternative_physical_channels\future_fit_architecture_portal\tests\Front-End\TC_05_CALENDAR_VIEW_VERIFY_THE_UN_APPROVED_CAMPAIGNS_DISPLAY_COLOR.robot">
      <test name="Business User - Calendar View- Preview" id="s1-s5-t1">
         <tags>
            <tag>Business User - Calendar View- Preview</tag>
         </tags>
         <status endtime="20241023 10:14:03.521" critical="yes" status="FAIL" starttime="20241023 10:14:03.520"/>
      </test>
      <test name="Business User - Calendar View- Preview" id="s1-s5-t1">
         <tags>
            <tag>Business User - Calendar View- Preview</tag>
         </tags>
         <status endtime="20241023 10:14:03.521" critical="yes" status="FAIL" starttime="20241023 10:14:03.520"/>
      </test>
      <test name="Business User - Calendar View- Preview" id="s1-s5-t1">
         <tags>
            <tag>Business User - Calendar View- Preview</tag>
         </tags>
         <status endtime="20241023 10:14:03.521" critical="yes" status="FAIL" starttime="20241023 10:14:03.520"/>
      </test>
      <test name="Business User - Calendar View- Preview" id="s1-s5-t1">
         <tags>
            <tag>Business User - Calendar View- Preview</tag>
         </tags>
         <status endtime="20241023 10:14:03.521" critical="yes" status="FAIL" starttime="20241023 10:14:03.520"/>
      </test>
      <test name="Business User - Calendar View- Preview" id="s1-s5-t1">
         <tags>
            <tag>Business User - Calendar View- Preview</tag>
         </tags>
         <status endtime="20241023 10:14:03.521" critical="yes" status="FAIL" starttime="20241023 10:14:03.520"/>
      </test>
      <status endtime="20241023 10:14:03.523" status="FAIL" starttime="20241023 10:14:00.808"/>
   </suite>
   <statistics>
      <total>
         <stat pass="0" fail="1">Critical Tests</stat>
         <stat pass="0" fail="1">All Tests</stat>
      </total>
      <tag>
         <stat pass="0" fail="1">Business User - Calendar View- Preview</stat>
         <stat pass="0" fail="1">Business User - Calendar View- Preview</stat>
         <stat pass="0" fail="1">Business User - Calendar View- Preview</stat>
         <stat pass="0" fail="1">Business User - Calendar View- Preview</stat>
         <stat pass="0" fail="1">Business User - Calendar View- Preview</stat>
      </tag>
      <suite>
         <stat name="Future Fit Portal" pass="0" fail="1" id="s1">Future Fit Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
