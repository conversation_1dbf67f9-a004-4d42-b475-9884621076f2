trigger:
- feature/bin_tables

pool:
  name: <PERSON><PERSON>G<PERSON>
  demands:
    - Agent.Name -equals Automation Agent 1


jobs:
- job: RunRobotTests
  displayName: 'Run Robot Framework Tests'

  steps:
  - script: |
      "C:\Program Files\Python313\python.exe" -m pip install --upgrade pip
      "C:\Program Files\Python313\python.exe" -m pip install robotframework
      "C:\Program Files\Python313\python.exe" -m pip install --upgrade robotframework-requests
      "C:\Program Files\Python313\python.exe" -m pip install --upgrade robotframework-seleniumlibrary
      "C:\Program Files\Python313\python.exe" -m pip install --upgrade robotframework-jsonlibrary
      "C:\Program Files\Python313\python.exe" -m pip install jproperties
      "C:\Program Files\Python313\python.exe" -m pip install matplotlib
      "C:\Program Files\Python313\python.exe" -m pip install webdriver-manager
      "C:\Program Files\Python313\python.exe" -m pip install mysql
      "C:\Program Files\Python313\python.exe" -m pip install mysql-connector-python
      "C:\Program Files\Python313\python.exe" -m pip install pymssql
      "C:\Program Files\Python313\python.exe" -m pip install pyodbc
      "C:\Program Files\Python313\python.exe" -m pip install random_word
      "C:\Program Files\Python313\python.exe" -m pip install jwt
      "C:\Program Files\Python313\python.exe" -m pip install robotframework-databaselibrary
      "C:\Program Files\Python313\python.exe" -m pip install PyAutoGUI
    workingDirectory: '$(System.DefaultWorkingDirectory)'
    displayName: 'Install Robot Framework and Dependencies'
    continueOnError: true
    condition: always()


  - powershell: |
      $pythonPath = "C:\Program Files\Python313\python.exe"  # Specify the location of Python.exe
      & $pythonPath -m robot -d results --variable ROBOT_FILE_PATH:"Bin_Tables.xml" --variable SUITE_DIRECTORY:"future_fit_architecture_portal_BinTables/data"  --variable BASE_URL:APC_SIT --variable BROWSER:edge --variable UPLOAD_TEST_STEPS:Yes --variable IS_HEADLESS_BROWSER:No -N "Bin Tables Controllers" --listener common_utilities/PostExecutionUpdateV2.py future_fit_architecture_portal_BinTables/tests/Front_End/BinTypes_page/View_Menu/Admin/RAC29a_TC_1042.robot
    displayName: 'Run Robot Framework Tests'
    failOnStderr: true
    continueOnError: true


  - task: PublishPipelineArtifact@1
    displayName: 'Publish Pipeline Artifact'
    inputs:
      artifact: 'Test Results'
    continueOnError: true
    condition: always()

  - task: PublishTestResults@2
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: 'results/output.xml'
      failTaskOnFailedTests: true
    displayName: 'Publish Test Results'

  - task: PublishTestResults@2
    condition: succeededOrFailed()
    inputs:
      testResultsFiles: 'results/output.xml'
      testRunTitle: 'Publish test results for Python $(python.version)'