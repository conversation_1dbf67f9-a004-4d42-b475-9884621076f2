*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Delete_Bins_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             Default




*** Keywords ***
Delete a Bin Number
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The deleted bin must be de-activated on the database                        ${BIN_NAME}
    When The user logs into Future Fit Architecture - Bin Tables portal               ${BASE_URL}
    And The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates the deleted Bin number on the binNumber input field        ${BIN_NAME}
    Then The error message must be displayed

| *** Test Cases ***                                                         |        *DOCUMENTATION*        |         *BASE_URL*             |         *BIN_NAME*          |         *BIN_TYPE_NAME*          |         *BIN_ACTION_DATE*        |
#| Delete a Bin number 'OttajaniteBi'.            | Delete a Bin Number       |       Delete a Bin Number.    |           ${EMPTY}             |           OttajaniteBi      |         Testing Bin Type Add     |         1/21/2025                |
| Delete a Bin number '123456' which has already been deleted.              | Delete a Bin Number       |       Delete a Bin Number.    |           ${EMPTY}             |             123456          |         Domestic                 |         1/21/2025                |
