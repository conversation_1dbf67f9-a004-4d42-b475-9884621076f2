*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../keywords/Common/Login.robot
Resource                                            ../../../../keywords/Common/HomePage.robot
Resource                                            ../../../../keywords/Common/Navigation.robot
Resource                                            ../../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../../keywords/Common/SetEnvironmentVariales.robot
Resource                                            ../../../../keywords/EOD_Control_Dashboard.robot
Resource                                            ../../../../keywords/Account_Information.robot

*** Variables ***

*** Keywords ***
Validates the "Flagged Transactions" Menu on Account Information
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application
    When the user lands on the Home page
    And the user navigates to the EOD Control Dashboard Menu
    And the user clicks on an account
    And the user navigates to the "Flagged Transactions Menu"
    And the user verifies the "Flagged Transactions" menu under "Account" on the front end
    Then the user verifies that the Flagged Transactions data displayed matches the data in the database

| *Test Cases*                                                                                                              |      *DOCUMENTATION*    | *TEST_ENVIRONMENT*   |
| 	Verify the Flagged Transactions Menu on account		| Validates the "Flagged Transactions" Menu on Account Information  |    Account Information  |    MTGLA_UAT         | 
