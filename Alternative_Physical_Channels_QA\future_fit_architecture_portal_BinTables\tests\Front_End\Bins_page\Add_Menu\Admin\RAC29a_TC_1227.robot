*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify that the Add a Bin Button is Disabled for empty Action Date Field
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_NAME}    ${BIN_TYPE_NAME}    
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_NAME}' == '${EMPTY}' or '${BIN_NAME}' == ''
         ${random_word}=     Generate random bin name
         ${BIN_NAME}=   Set Variable     ${random_word}
         ${BIN_NAME}=    Get Substring    ${BIN_NAME}    0    12
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The user navigates to 'Add' Bin tab
    And The User populates the Bin name    ${BIN_NAME}  
    And The User populates the Bin Type    ${BIN_TYPE_NAME}   
    And The User allows the Action Date to be empty and verfies that the 'Add' button is disabled     

| *** Test Cases ***                                                                                                                   |        *DOCUMENTATION*       |         *BASE_URL*                  |         *BIN_NAME*          |         *BIN_TYPE_NAME*        |         
| Admin_Add Button Disabled for Empty Action Date Field   | Verify that the Add a Bin Button is Disabled for empty Action Date Field   | Testing the Add Bin Button   |           ${EMPTY}                  |            ${EMPTY}         |         Domestic               |         
