*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
# Test Teardown                                       User logs out

Documentation  Create Campaign page- Negative Tests 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary 
Library                                             OperatingSystem
Library                                             String
Library                                             DateTime

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/Navigation.robot
Resource                                            ../../keywords/atm_marketing/UploadMarkrtingScreen.robot
Resource                                            ../../keywords/atm_marketing/CalendarView.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../keywords/atm_marketing/FilloutCampaignTarget.robot
Resource                                            ../../keywords/atm_marketing/FilloutYourCampaign.robot
Resource                                            ../../keywords/atm_marketing/Approvals.robot

*** Variables ***

*** Keywords ***
Create marketing campaign Negative 
    [Arguments]        ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${IS_CAMPAIGN_TARGETED}  ${CAMPAIGN_TARGET}  ${CAMPAIGN_TARGETED_REGION_OR_ATM}  ${CAMPAIGN_NAME}  ${MARKETING_TYPE}  ${RECEIVER_DEVICE_TYPE}  ${NUMBER_OF_DAYS_FROM_START_DATE}  ${CAMPAIGN_LANGUAGE}  ${IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY}  ${DLE_SCREEN_IMAGE_AFRIKAANS_DIRECTORY}  ${LOGON_USER}    ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    And The user validates the Negative testcases for Capture Campaign

    And The user logs out 



*** Test Cases ***                                                                                                                                                      |                                  *DOCUMENTATION*                                                                  |  *TESTRAIL_TESTCASE_ID*   |    *IS_CAMPAIGN_TARGETED*    |    *CAMPAIGN_TARGET*         |      *CAMPAIGN_TARGETED_REGION_OR_ATM*              |      *CAMPAIGN_NAME*  |      *MARKETING_TYPE*    |   *RECEIVER_DEVICE_TYPE*        |  NUMBER_OF_DAYS_FROM_START_DATE |     * CAMPAIGN_LANGUAGE*      |           *IDLE_SCREEN_IMAGE_ENGLISH_DIRECTORY*  |           *IDLE_SCREEN_IMAGE_AFRIKAANS_DIRECTORY*  |   *LOGON_USER*                |                       *TEST_ENVIRONMENT*                                          |
#| FFT - Campaign Capturing - negative test - verify that the B - Approver is not able to capture a campaign           |    Create marketing campaign Negative    |        BA not able to capture campaign                                                                            |     ********	             |            Yes
#| FFT - Create campaign 2 years in the future                                                                         |    Create marketing campaign Negative    |        Campaigns 2 years in the future is not allowed                                                             |      T152652402           |            Yes               |     Region                   |        Western Cape                                     |  Automation Campaign  |      Idle                |        ATM                      |     700                         |           English             |       images\\MarketingA_en_4.jpg                |      images\\MarketingA_en_4.jpg                   |    BUSINESS_USER          |  APC_UAT                                                  |
