<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="20240912 10:21:12.972" rpa="false" schemaversion="4">
<suite id="s1" name="VMS Portal" source="C:\Users\<USER>\source\repos\vms\tests\ADMIN_EMAIL_MANAGEMENT\RAC29a_TC_331_Validate_Rows_per_Page_filter_Email_Management.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240912 10:21:13.854" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:13.854"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240912 10:21:13.854" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Users\<USER>\source\repos\vms\data\USER_ADMIN_REGRESSION.xml'.</msg>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:13.854"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240912 10:21:13.854" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:13.854"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240912 10:21:13.854" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:13.854"/>
</kw>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:13.854"/>
</kw>
<test id="s1-t1" name="Filter the number of Vendor Emails displayed to show 1 email per page." line="37">
<kw name="VMS Vendor Email - Validate Rows Per Page Filter">
<arg>Filter nummber of rows to be displayed per page</arg>
<arg>VMS_UAT</arg>
<arg>1</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240912 10:21:13.854" level="INFO">Set test documentation to:
Filter nummber of rows to be displayed per page</msg>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:13.854"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:14.042" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:14.042"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:14.042" endtime="20240912 10:21:14.042"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:14.042" endtime="20240912 10:21:14.042"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:14.048" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:14.048"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:21:14.048" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:14.048"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:14.048"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240912 10:21:14.087" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240912 10:21:14.556" level="INFO">${rc_code} = 0</msg>
<msg timestamp="20240912 10:21:14.556" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 21672 has been terminated.
SUCCESS: The process "chrome.exe" with PID 31704 has been terminated.
SUCCESS: The process "chrome.exe" with PID 30244 has been te...</msg>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${browser_name} = CHROME</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</branch>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</if>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001CBE6AE9DC0&gt;</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</branch>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\User...</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:14.556"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20240912 10:21:14.556" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:17.395"/>
</kw>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:17.395"/>
</branch>
<status status="PASS" starttime="20240912 10:21:14.556" endtime="20240912 10:21:17.395"/>
</if>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:17.396"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20240912 10:21:17.433" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg timestamp="20240912 10:21:17.433" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<status status="PASS" starttime="20240912 10:21:17.397" endtime="20240912 10:21:17.433"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20240912 10:21:17.434" endtime="20240912 10:21:17.439"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20240912 10:21:17.439" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<status status="PASS" starttime="20240912 10:21:17.439" endtime="20240912 10:21:21.604"/>
</kw>
<status status="PASS" starttime="20240912 10:21:17.439" endtime="20240912 10:21:21.605"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:23.606" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:21:21.605" endtime="20240912 10:21:23.606"/>
</kw>
<status status="PASS" starttime="20240912 10:21:17.396" endtime="20240912 10:21:23.606"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:23.728" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:21:23.606" endtime="20240912 10:21:23.728"/>
</kw>
<msg timestamp="20240912 10:21:23.728" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:21:23.606" endtime="20240912 10:21:23.728"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:23.728" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:21:23.728" endtime="20240912 10:21:23.807"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:21:23.823" endtime="20240912 10:21:23.840"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:23.840" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:21:23.840" endtime="20240912 10:21:23.923"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:23.923" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:21:23.923" endtime="20240912 10:21:28.373"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:30.374" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:21:28.373" endtime="20240912 10:21:30.374"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:30.506" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-19.png"&gt;&lt;img src="selenium-screenshot-19.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="20240912 10:21:30.506" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<status status="FAIL" starttime="20240912 10:21:30.374" endtime="20240912 10:21:30.563"/>
</kw>
<msg timestamp="20240912 10:21:30.563" level="INFO">${User_Name_Element_Visible} = False</msg>
<status status="PASS" starttime="20240912 10:21:30.374" endtime="20240912 10:21:30.563"/>
</kw>
<status status="PASS" starttime="20240912 10:21:23.728" endtime="20240912 10:21:30.563"/>
</iter>
<status status="PASS" starttime="20240912 10:21:23.728" endtime="20240912 10:21:30.563"/>
</while>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:30.563" endtime="20240912 10:21:30.563"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:30.669" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-20.png"&gt;&lt;img src="selenium-screenshot-20.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:30.563" endtime="20240912 10:21:30.669"/>
</kw>
<status status="PASS" starttime="20240912 10:21:14.048" endtime="20240912 10:21:30.669"/>
</kw>
<status status="PASS" starttime="20240912 10:21:14.042" endtime="20240912 10:21:30.669"/>
</kw>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:30.669"/>
</kw>
<kw name="When The user navigates to Admin - Email Management" library="EmailManagement">
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:21:30.681" level="INFO">Current page contains text 'Dashboard'.</msg>
<status status="PASS" starttime="20240912 10:21:30.669" endtime="20240912 10:21:30.688"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:30.786" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:30.688" endtime="20240912 10:21:30.786"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:30.794" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<status status="PASS" starttime="20240912 10:21:30.786" endtime="20240912 10:21:30.831"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:30.832" level="INFO">Clicking element 'xpath=//a[contains(@class,'nav-link')][text()[normalize-space(.)='Email Management']]'.</msg>
<status status="PASS" starttime="20240912 10:21:30.831" endtime="20240912 10:21:31.304"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:21:31.332" level="INFO">Current page contains text 'Email Management'.</msg>
<status status="PASS" starttime="20240912 10:21:31.304" endtime="20240912 10:21:31.332"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:31.457" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Management_Landing_Page.png"&gt;&lt;img src="Email_Management_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:31.332" endtime="20240912 10:21:31.457"/>
</kw>
<status status="PASS" starttime="20240912 10:21:30.669" endtime="20240912 10:21:31.458"/>
</kw>
<kw name="And The user filters the number of Vendor Email to be displayed using rows per page filter" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Validate Integer" library="Common_Functions">
<var>${integer_validation}</var>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:21:31.459" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20240912 10:21:31.458" endtime="20240912 10:21:31.459"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be an integer. The valid values are 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:21:31.459" endtime="20240912 10:21:31.459"/>
</kw>
<kw name="Check Element In List" library="Common_Functions">
<var>${is_in_list}</var>
<arg>${STRING_LIST}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:21:31.460" level="INFO">${is_in_list} = True</msg>
<status status="PASS" starttime="20240912 10:21:31.459" endtime="20240912 10:21:31.460"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${is_in_list} == ${False}</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be amongst 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:21:31.460" endtime="20240912 10:21:31.460"/>
</kw>
<kw name="Select From List By Value" library="SeleniumLibrary">
<arg>${ROWS_PER_PAGE_FILTER}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Selects options from selection list ``locator`` by ``values``.</doc>
<msg timestamp="20240912 10:21:31.461" level="INFO">Selecting options from selection list 'xpath=//select[@id='changeRows'][@name='rowsPerPage']' by value 1.</msg>
<status status="PASS" starttime="20240912 10:21:31.460" endtime="20240912 10:21:31.542"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:31.656" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_1.png"&gt;&lt;img src="USER_FILTER_1.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:31.543" endtime="20240912 10:21:31.656"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.458" endtime="20240912 10:21:31.656"/>
</kw>
<kw name="Then The number of rows returned on the page must be the same as the number that was used for filtering" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:31.674" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<status status="PASS" starttime="20240912 10:21:31.656" endtime="20240912 10:21:31.674"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_tbody_element}</var>
<arg>${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:21:31.674" level="INFO">${table_tbody_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]</msg>
<status status="PASS" starttime="20240912 10:21:31.674" endtime="20240912 10:21:31.674"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_rows_element}</var>
<arg>${table_tbody_element}/tr</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:21:31.674" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]/tr</msg>
<status status="PASS" starttime="20240912 10:21:31.674" endtime="20240912 10:21:31.674"/>
</kw>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${rows}</var>
<arg>${table_rows_element}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="20240912 10:21:31.682" level="INFO">${rows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="ec9447aa108e83a2a93fc913849f90b6", element="f.64410ABABEC8F0D80EE18FF8BF1FEBBA.d.6D444B45EB096D6DF20D51554F8A8D24.e.209")&gt;]</msg>
<status status="PASS" starttime="20240912 10:21:31.674" endtime="20240912 10:21:31.682"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${table_rows_count}</var>
<arg>${rows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:21:31.682" level="INFO">Length is 1</msg>
<msg timestamp="20240912 10:21:31.682" level="INFO">${table_rows_count} = 1</msg>
<status status="PASS" starttime="20240912 10:21:31.682" endtime="20240912 10:21:31.682"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${table_rows_count}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="20240912 10:21:31.682" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="20240912 10:21:31.682" endtime="20240912 10:21:31.682"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.682" endtime="20240912 10:21:31.682"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:21:31.682" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:21:31.682" level="INFO">: Expected Value = 1, Actual Value = 1</msg>
<status status="PASS" starttime="20240912 10:21:31.682" endtime="20240912 10:21:31.682"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.682" endtime="20240912 10:21:31.682"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${page_number_text}</var>
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:31.712" level="INFO">${page_number_text} = Showing 1 to 1 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:21:31.682" endtime="20240912 10:21:31.712"/>
</kw>
<kw name="Get Substring" library="String">
<var>${substring}</var>
<arg>${page_number_text}</arg>
<arg>13</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:21:31.712" level="INFO">${substring} = 1 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:21:31.712" endtime="20240912 10:21:31.712"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${rows_variable_digits_length}</var>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:21:31.712" level="INFO">Length is 1</msg>
<msg timestamp="20240912 10:21:31.712" level="INFO">${rows_variable_digits_length} = 1</msg>
<status status="PASS" starttime="20240912 10:21:31.712" endtime="20240912 10:21:31.712"/>
</kw>
<if>
<branch type="IF" condition="${rows_variable_digits_length} == 1">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:21:31.713" level="INFO">${substring2} = 1 </msg>
<status status="PASS" starttime="20240912 10:21:31.713" endtime="20240912 10:21:31.713"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.713" endtime="20240912 10:21:31.713"/>
</branch>
<branch type="ELSE">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>3</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<status status="NOT RUN" starttime="20240912 10:21:31.713" endtime="20240912 10:21:31.713"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:31.713" endtime="20240912 10:21:31.713"/>
</branch>
<status status="PASS" starttime="20240912 10:21:31.712" endtime="20240912 10:21:31.713"/>
</if>
<kw name="Set Variable" library="BuiltIn">
<var>${trimmed_string}</var>
<arg>${substring2.strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:21:31.714" level="INFO">${trimmed_string} = 1</msg>
<status status="PASS" starttime="20240912 10:21:31.713" endtime="20240912 10:21:31.714"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${trimmed_string}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="20240912 10:21:31.714" endtime="20240912 10:21:31.714"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.714" endtime="20240912 10:21:31.714"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:21:31.715" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:21:31.715" level="INFO">: Expected Value = 1, Actual Value = 1</msg>
<status status="PASS" starttime="20240912 10:21:31.715" endtime="20240912 10:21:31.715"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.714" endtime="20240912 10:21:31.715"/>
</kw>
<kw name="Scroll Element Into View" library="SeleniumLibrary">
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" starttime="20240912 10:21:31.715" endtime="20240912 10:21:31.989"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}_VERIFICATION.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:32.074" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_1_VERIFICATION.png"&gt;&lt;img src="USER_FILTER_1_VERIFICATION.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:31.989" endtime="20240912 10:21:32.074"/>
</kw>
<status status="PASS" starttime="20240912 10:21:31.656" endtime="20240912 10:21:32.074"/>
</kw>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:32.074"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:21:32.080" endtime="20240912 10:21:32.091"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:32.092" endtime="20240912 10:21:32.092"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:32.092" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<status status="PASS" starttime="20240912 10:21:32.092" endtime="20240912 10:21:32.322"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:35.323" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20240912 10:21:32.323" endtime="20240912 10:21:35.323"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240912 10:21:35.323" endtime="20240912 10:21:37.958"/>
</kw>
<status status="PASS" starttime="20240912 10:21:32.080" endtime="20240912 10:21:37.958"/>
</kw>
<doc>Filter nummber of rows to be displayed per page</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" starttime="20240912 10:21:13.854" endtime="20240912 10:21:37.958"/>
</test>
<test id="s1-t2" name="Filter the number of Vendor Emails displayed to show 5 emails per page." line="38">
<kw name="VMS Vendor Email - Validate Rows Per Page Filter">
<arg>Filter nummber of rows to be displayed per page</arg>
<arg>VMS_UAT</arg>
<arg>5</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240912 10:21:37.958" level="INFO">Set test documentation to:
Filter nummber of rows to be displayed per page</msg>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:37.958"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:37.958" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:37.958"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:37.958"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:37.958"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:37.958" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:37.958"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:21:37.958" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:37.958"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:38.068"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240912 10:21:38.304" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240912 10:21:38.684" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240912 10:21:38.684" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240912 10:21:38.068" endtime="20240912 10:21:38.684"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240912 10:21:38.684" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240912 10:21:38.684" endtime="20240912 10:21:38.699"/>
</kw>
<status status="PASS" starttime="20240912 10:21:38.684" endtime="20240912 10:21:38.699"/>
</kw>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${browser_name} = CHROME</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</branch>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</if>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001CBE6B0AD20&gt;</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</branch>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\User...</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:38.699"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20240912 10:21:38.699" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:41.014"/>
</kw>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:41.014"/>
</branch>
<status status="PASS" starttime="20240912 10:21:38.699" endtime="20240912 10:21:41.014"/>
</if>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:41.014"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20240912 10:21:41.021" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg timestamp="20240912 10:21:41.022" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<status status="PASS" starttime="20240912 10:21:41.016" endtime="20240912 10:21:41.022"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20240912 10:21:41.022" endtime="20240912 10:21:41.027"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20240912 10:21:41.029" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<status status="PASS" starttime="20240912 10:21:41.029" endtime="20240912 10:21:42.806"/>
</kw>
<status status="PASS" starttime="20240912 10:21:41.028" endtime="20240912 10:21:42.806"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:44.807" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:21:42.806" endtime="20240912 10:21:44.807"/>
</kw>
<status status="PASS" starttime="20240912 10:21:41.014" endtime="20240912 10:21:44.807"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:44.818" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:21:44.807" endtime="20240912 10:21:44.818"/>
</kw>
<msg timestamp="20240912 10:21:44.818" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:21:44.807" endtime="20240912 10:21:44.818"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:44.818" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:21:44.818" endtime="20240912 10:21:44.885"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:21:44.885" endtime="20240912 10:21:44.904"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:44.904" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:21:44.904" endtime="20240912 10:21:44.968"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:44.968" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:21:44.968" endtime="20240912 10:21:45.514"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:47.514" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:21:45.514" endtime="20240912 10:21:47.514"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:47.534" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:21:47.514" endtime="20240912 10:21:47.534"/>
</kw>
<msg timestamp="20240912 10:21:47.535" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:21:47.514" endtime="20240912 10:21:47.535"/>
</kw>
<status status="PASS" starttime="20240912 10:21:44.818" endtime="20240912 10:21:47.535"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:47.535" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:21:47.535" endtime="20240912 10:21:47.572"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:21:47.572" endtime="20240912 10:21:47.583"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:47.583" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:21:47.583" endtime="20240912 10:21:47.643"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:47.643" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:21:47.643" endtime="20240912 10:21:50.942"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:52.943" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:21:50.942" endtime="20240912 10:21:52.943"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:53.077" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-21.png"&gt;&lt;img src="selenium-screenshot-21.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="20240912 10:21:53.077" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<status status="FAIL" starttime="20240912 10:21:52.943" endtime="20240912 10:21:53.077"/>
</kw>
<msg timestamp="20240912 10:21:53.077" level="INFO">${User_Name_Element_Visible} = False</msg>
<status status="PASS" starttime="20240912 10:21:52.943" endtime="20240912 10:21:53.077"/>
</kw>
<status status="PASS" starttime="20240912 10:21:47.535" endtime="20240912 10:21:53.077"/>
</iter>
<status status="PASS" starttime="20240912 10:21:44.818" endtime="20240912 10:21:53.077"/>
</while>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:53.077" endtime="20240912 10:21:53.077"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:53.228" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-22.png"&gt;&lt;img src="selenium-screenshot-22.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:53.077" endtime="20240912 10:21:53.228"/>
</kw>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:53.228"/>
</kw>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:53.228"/>
</kw>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:53.228"/>
</kw>
<kw name="When The user navigates to Admin - Email Management" library="EmailManagement">
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:21:53.250" level="INFO">Current page contains text 'Dashboard'.</msg>
<status status="PASS" starttime="20240912 10:21:53.228" endtime="20240912 10:21:53.250"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:53.329" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:53.254" endtime="20240912 10:21:53.329"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:53.329" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<status status="PASS" starttime="20240912 10:21:53.329" endtime="20240912 10:21:53.390"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:53.390" level="INFO">Clicking element 'xpath=//a[contains(@class,'nav-link')][text()[normalize-space(.)='Email Management']]'.</msg>
<status status="PASS" starttime="20240912 10:21:53.390" endtime="20240912 10:21:53.791"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:21:53.811" level="INFO">Current page contains text 'Email Management'.</msg>
<status status="PASS" starttime="20240912 10:21:53.791" endtime="20240912 10:21:53.812"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:53.943" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Management_Landing_Page.png"&gt;&lt;img src="Email_Management_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:53.812" endtime="20240912 10:21:53.943"/>
</kw>
<status status="PASS" starttime="20240912 10:21:53.228" endtime="20240912 10:21:53.943"/>
</kw>
<kw name="And The user filters the number of Vendor Email to be displayed using rows per page filter" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Validate Integer" library="Common_Functions">
<var>${integer_validation}</var>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:21:53.943" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20240912 10:21:53.943" endtime="20240912 10:21:53.943"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be an integer. The valid values are 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:21:53.943" endtime="20240912 10:21:53.943"/>
</kw>
<kw name="Check Element In List" library="Common_Functions">
<var>${is_in_list}</var>
<arg>${STRING_LIST}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:21:53.943" level="INFO">${is_in_list} = True</msg>
<status status="PASS" starttime="20240912 10:21:53.943" endtime="20240912 10:21:53.943"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${is_in_list} == ${False}</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be amongst 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:21:53.943" endtime="20240912 10:21:53.943"/>
</kw>
<kw name="Select From List By Value" library="SeleniumLibrary">
<arg>${ROWS_PER_PAGE_FILTER}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Selects options from selection list ``locator`` by ``values``.</doc>
<msg timestamp="20240912 10:21:53.943" level="INFO">Selecting options from selection list 'xpath=//select[@id='changeRows'][@name='rowsPerPage']' by value 5.</msg>
<status status="PASS" starttime="20240912 10:21:53.943" endtime="20240912 10:21:54.028"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:54.154" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_5.png"&gt;&lt;img src="USER_FILTER_5.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:54.029" endtime="20240912 10:21:54.154"/>
</kw>
<status status="PASS" starttime="20240912 10:21:53.943" endtime="20240912 10:21:54.154"/>
</kw>
<kw name="Then The number of rows returned on the page must be the same as the number that was used for filtering" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:21:54.167" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<status status="PASS" starttime="20240912 10:21:54.154" endtime="20240912 10:21:54.167"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_tbody_element}</var>
<arg>${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:21:54.167" level="INFO">${table_tbody_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]</msg>
<status status="PASS" starttime="20240912 10:21:54.167" endtime="20240912 10:21:54.167"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_rows_element}</var>
<arg>${table_tbody_element}/tr</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:21:54.167" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]/tr</msg>
<status status="PASS" starttime="20240912 10:21:54.167" endtime="20240912 10:21:54.167"/>
</kw>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${rows}</var>
<arg>${table_rows_element}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="20240912 10:21:54.171" level="INFO">${rows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="611250695396a386e2884289d20f350a", element="f.5379715AA9B7FB89AACA8BEB68CB1F7B.d.CFAEC7800EDAFD56B6D635E3A61F7614.e.166")&gt;, &lt;selenium.webdri...</msg>
<status status="PASS" starttime="20240912 10:21:54.167" endtime="20240912 10:21:54.171"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${table_rows_count}</var>
<arg>${rows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:21:54.175" level="INFO">Length is 5</msg>
<msg timestamp="20240912 10:21:54.175" level="INFO">${table_rows_count} = 5</msg>
<status status="PASS" starttime="20240912 10:21:54.175" endtime="20240912 10:21:54.175"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${table_rows_count}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="20240912 10:21:54.175" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="20240912 10:21:54.175" endtime="20240912 10:21:54.175"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.175" endtime="20240912 10:21:54.175"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:21:54.176" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:21:54.176" level="INFO">: Expected Value = 5, Actual Value = 5</msg>
<status status="PASS" starttime="20240912 10:21:54.176" endtime="20240912 10:21:54.176"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.175" endtime="20240912 10:21:54.176"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${page_number_text}</var>
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:54.272" level="INFO">${page_number_text} = Showing 1 to 5 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:21:54.176" endtime="20240912 10:21:54.272"/>
</kw>
<kw name="Get Substring" library="String">
<var>${substring}</var>
<arg>${page_number_text}</arg>
<arg>13</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:21:54.272" level="INFO">${substring} = 5 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${rows_variable_digits_length}</var>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:21:54.272" level="INFO">Length is 1</msg>
<msg timestamp="20240912 10:21:54.272" level="INFO">${rows_variable_digits_length} = 1</msg>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<if>
<branch type="IF" condition="${rows_variable_digits_length} == 1">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:21:54.272" level="INFO">${substring2} = 5 </msg>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</branch>
<branch type="ELSE">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>3</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<status status="NOT RUN" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</branch>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</if>
<kw name="Set Variable" library="BuiltIn">
<var>${trimmed_string}</var>
<arg>${substring2.strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:21:54.272" level="INFO">${trimmed_string} = 5</msg>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${trimmed_string}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:21:54.272" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:21:54.272" level="INFO">: Expected Value = 5, Actual Value = 5</msg>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.272"/>
</kw>
<kw name="Scroll Element Into View" library="SeleniumLibrary">
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" starttime="20240912 10:21:54.272" endtime="20240912 10:21:54.556"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}_VERIFICATION.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:21:54.659" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_5_VERIFICATION.png"&gt;&lt;img src="USER_FILTER_5_VERIFICATION.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:21:54.556" endtime="20240912 10:21:54.659"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.154" endtime="20240912 10:21:54.659"/>
</kw>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:21:54.659"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:21:54.659" endtime="20240912 10:21:54.673"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:21:54.674" endtime="20240912 10:21:54.674"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:21:54.674" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<status status="PASS" starttime="20240912 10:21:54.674" endtime="20240912 10:21:54.919"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:21:57.921" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20240912 10:21:54.919" endtime="20240912 10:21:57.921"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240912 10:21:57.921" endtime="20240912 10:22:00.308"/>
</kw>
<status status="PASS" starttime="20240912 10:21:54.659" endtime="20240912 10:22:00.308"/>
</kw>
<doc>Filter nummber of rows to be displayed per page</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" starttime="20240912 10:21:37.958" endtime="20240912 10:22:00.308"/>
</test>
<test id="s1-t3" name="Filter the number of Vendor Emails displayed to show 10 emails per page." line="39">
<kw name="VMS Vendor Email - Validate Rows Per Page Filter">
<arg>Filter nummber of rows to be displayed per page</arg>
<arg>VMS_UAT</arg>
<arg>10</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240912 10:22:00.308" level="INFO">Set test documentation to:
Filter nummber of rows to be displayed per page</msg>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:00.308" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:00.308" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:22:00.308" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.308"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240912 10:22:00.340" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240912 10:22:00.781" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240912 10:22:00.781" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240912 10:22:00.781" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${browser_name} = CHROME</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</branch>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</if>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001CBE6B75160&gt;</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</branch>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:22:00.781" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:00.781"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:00.789" endtime="20240912 10:22:00.789"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:00.789" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240912 10:22:00.789" endtime="20240912 10:22:00.789"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240912 10:22:00.789" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\User...</msg>
<status status="PASS" starttime="20240912 10:22:00.789" endtime="20240912 10:22:00.789"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20240912 10:22:00.789" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20240912 10:22:00.789" endtime="20240912 10:22:03.198"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:03.198"/>
</branch>
<status status="PASS" starttime="20240912 10:22:00.781" endtime="20240912 10:22:03.198"/>
</if>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:03.198"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20240912 10:22:03.198" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg timestamp="20240912 10:22:03.198" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<status status="PASS" starttime="20240912 10:22:03.198" endtime="20240912 10:22:03.198"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20240912 10:22:03.198" endtime="20240912 10:22:03.213"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20240912 10:22:03.216" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<status status="PASS" starttime="20240912 10:22:03.215" endtime="20240912 10:22:06.623"/>
</kw>
<status status="PASS" starttime="20240912 10:22:03.213" endtime="20240912 10:22:06.623"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:08.624" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:22:06.623" endtime="20240912 10:22:08.624"/>
</kw>
<status status="PASS" starttime="20240912 10:22:03.198" endtime="20240912 10:22:08.624"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:08.649" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:22:08.624" endtime="20240912 10:22:08.649"/>
</kw>
<msg timestamp="20240912 10:22:08.649" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:22:08.624" endtime="20240912 10:22:08.649"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:08.649" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:22:08.649" endtime="20240912 10:22:08.706"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:22:08.706" endtime="20240912 10:22:08.719"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:08.719" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:22:08.719" endtime="20240912 10:22:08.766"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:08.766" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:22:08.766" endtime="20240912 10:22:09.267"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:11.268" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:22:09.267" endtime="20240912 10:22:11.268"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:11.290" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:22:11.268" endtime="20240912 10:22:11.290"/>
</kw>
<msg timestamp="20240912 10:22:11.290" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:22:11.268" endtime="20240912 10:22:11.290"/>
</kw>
<status status="PASS" starttime="20240912 10:22:08.649" endtime="20240912 10:22:11.290"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:11.290" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:22:11.290" endtime="20240912 10:22:11.377"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:22:11.377" endtime="20240912 10:22:11.388"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:11.388" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:22:11.388" endtime="20240912 10:22:11.447"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:11.448" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:22:11.448" endtime="20240912 10:22:19.704"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:21.708" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:22:19.704" endtime="20240912 10:22:21.708"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:21.823" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-23.png"&gt;&lt;img src="selenium-screenshot-23.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="20240912 10:22:21.823" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<status status="FAIL" starttime="20240912 10:22:21.708" endtime="20240912 10:22:21.823"/>
</kw>
<msg timestamp="20240912 10:22:21.823" level="INFO">${User_Name_Element_Visible} = False</msg>
<status status="PASS" starttime="20240912 10:22:21.708" endtime="20240912 10:22:21.823"/>
</kw>
<status status="PASS" starttime="20240912 10:22:11.290" endtime="20240912 10:22:21.823"/>
</iter>
<status status="PASS" starttime="20240912 10:22:08.649" endtime="20240912 10:22:21.823"/>
</while>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:21.823" endtime="20240912 10:22:21.823"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:21.927" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-24.png"&gt;&lt;img src="selenium-screenshot-24.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:21.823" endtime="20240912 10:22:21.927"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:21.927"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:21.927"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:21.927"/>
</kw>
<kw name="When The user navigates to Admin - Email Management" library="EmailManagement">
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:22:21.940" level="INFO">Current page contains text 'Dashboard'.</msg>
<status status="PASS" starttime="20240912 10:22:21.927" endtime="20240912 10:22:21.940"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:22.056" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:21.940" endtime="20240912 10:22:22.056"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:22.056" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<status status="PASS" starttime="20240912 10:22:22.056" endtime="20240912 10:22:22.131"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:22.132" level="INFO">Clicking element 'xpath=//a[contains(@class,'nav-link')][text()[normalize-space(.)='Email Management']]'.</msg>
<status status="PASS" starttime="20240912 10:22:22.131" endtime="20240912 10:22:22.739"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:22:22.768" level="INFO">Current page contains text 'Email Management'.</msg>
<status status="PASS" starttime="20240912 10:22:22.739" endtime="20240912 10:22:22.768"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:22.881" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Management_Landing_Page.png"&gt;&lt;img src="Email_Management_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:22.768" endtime="20240912 10:22:22.881"/>
</kw>
<status status="PASS" starttime="20240912 10:22:21.927" endtime="20240912 10:22:22.882"/>
</kw>
<kw name="And The user filters the number of Vendor Email to be displayed using rows per page filter" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Validate Integer" library="Common_Functions">
<var>${integer_validation}</var>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:22:22.882" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20240912 10:22:22.882" endtime="20240912 10:22:22.882"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be an integer. The valid values are 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:22:22.883" endtime="20240912 10:22:22.883"/>
</kw>
<kw name="Check Element In List" library="Common_Functions">
<var>${is_in_list}</var>
<arg>${STRING_LIST}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:22:22.883" level="INFO">${is_in_list} = True</msg>
<status status="PASS" starttime="20240912 10:22:22.883" endtime="20240912 10:22:22.883"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${is_in_list} == ${False}</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be amongst 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:22:22.883" endtime="20240912 10:22:22.884"/>
</kw>
<kw name="Select From List By Value" library="SeleniumLibrary">
<arg>${ROWS_PER_PAGE_FILTER}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Selects options from selection list ``locator`` by ``values``.</doc>
<msg timestamp="20240912 10:22:22.884" level="INFO">Selecting options from selection list 'xpath=//select[@id='changeRows'][@name='rowsPerPage']' by value 10.</msg>
<status status="PASS" starttime="20240912 10:22:22.884" endtime="20240912 10:22:22.981"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:23.107" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_10.png"&gt;&lt;img src="USER_FILTER_10.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:22.982" endtime="20240912 10:22:23.107"/>
</kw>
<status status="PASS" starttime="20240912 10:22:22.882" endtime="20240912 10:22:23.107"/>
</kw>
<kw name="Then The number of rows returned on the page must be the same as the number that was used for filtering" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:23.139" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<status status="PASS" starttime="20240912 10:22:23.107" endtime="20240912 10:22:23.139"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_tbody_element}</var>
<arg>${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:22:23.140" level="INFO">${table_tbody_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]</msg>
<status status="PASS" starttime="20240912 10:22:23.140" endtime="20240912 10:22:23.140"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_rows_element}</var>
<arg>${table_tbody_element}/tr</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:22:23.140" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]/tr</msg>
<status status="PASS" starttime="20240912 10:22:23.140" endtime="20240912 10:22:23.140"/>
</kw>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${rows}</var>
<arg>${table_rows_element}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="20240912 10:22:23.157" level="INFO">${rows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="4810ad853f0d9126c3477e7ee9c40765", element="f.A8CE1871D1BDC445038FA976713D61E8.d.D9BBAEA0BAFB4AE9077BE76EFD25C994.e.179")&gt;, &lt;selenium.webdri...</msg>
<status status="PASS" starttime="20240912 10:22:23.140" endtime="20240912 10:22:23.157"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${table_rows_count}</var>
<arg>${rows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:22:23.157" level="INFO">Length is 10</msg>
<msg timestamp="20240912 10:22:23.157" level="INFO">${table_rows_count} = 10</msg>
<status status="PASS" starttime="20240912 10:22:23.157" endtime="20240912 10:22:23.157"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${table_rows_count}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="20240912 10:22:23.157" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="20240912 10:22:23.157" endtime="20240912 10:22:23.157"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.157" endtime="20240912 10:22:23.157"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:22:23.157" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:22:23.157" level="INFO">: Expected Value = 10, Actual Value = 10</msg>
<status status="PASS" starttime="20240912 10:22:23.157" endtime="20240912 10:22:23.157"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.157" endtime="20240912 10:22:23.157"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${page_number_text}</var>
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:23.194" level="INFO">${page_number_text} = Showing 1 to 10 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:22:23.157" endtime="20240912 10:22:23.194"/>
</kw>
<kw name="Get Substring" library="String">
<var>${substring}</var>
<arg>${page_number_text}</arg>
<arg>13</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:22:23.195" level="INFO">${substring} = 10 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.195"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${rows_variable_digits_length}</var>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:22:23.195" level="INFO">Length is 2</msg>
<msg timestamp="20240912 10:22:23.195" level="INFO">${rows_variable_digits_length} = 2</msg>
<status status="PASS" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.195"/>
</kw>
<if>
<branch type="IF" condition="${rows_variable_digits_length} == 1">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<status status="NOT RUN" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.195"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.195"/>
</branch>
<branch type="ELSE">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>3</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:22:23.196" level="INFO">${substring2} = 10 </msg>
<status status="PASS" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.196"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.196"/>
</branch>
<status status="PASS" starttime="20240912 10:22:23.195" endtime="20240912 10:22:23.196"/>
</if>
<kw name="Set Variable" library="BuiltIn">
<var>${trimmed_string}</var>
<arg>${substring2.strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:22:23.197" level="INFO">${trimmed_string} = 10</msg>
<status status="PASS" starttime="20240912 10:22:23.196" endtime="20240912 10:22:23.197"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${trimmed_string}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="20240912 10:22:23.197" endtime="20240912 10:22:23.198"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.197" endtime="20240912 10:22:23.198"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:22:23.198" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:22:23.198" level="INFO">: Expected Value = 10, Actual Value = 10</msg>
<status status="PASS" starttime="20240912 10:22:23.198" endtime="20240912 10:22:23.198"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.197" endtime="20240912 10:22:23.198"/>
</kw>
<kw name="Scroll Element Into View" library="SeleniumLibrary">
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" starttime="20240912 10:22:23.198" endtime="20240912 10:22:23.484"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}_VERIFICATION.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:23.670" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_10_VERIFICATION.png"&gt;&lt;img src="USER_FILTER_10_VERIFICATION.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:23.484" endtime="20240912 10:22:23.670"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.107" endtime="20240912 10:22:23.670"/>
</kw>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:23.670"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:22:23.670" endtime="20240912 10:22:23.699"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:23.699" endtime="20240912 10:22:23.699"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:23.700" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<status status="PASS" starttime="20240912 10:22:23.699" endtime="20240912 10:22:23.924"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:26.929" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20240912 10:22:23.924" endtime="20240912 10:22:26.929"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240912 10:22:26.929" endtime="20240912 10:22:29.291"/>
</kw>
<status status="PASS" starttime="20240912 10:22:23.670" endtime="20240912 10:22:29.291"/>
</kw>
<doc>Filter nummber of rows to be displayed per page</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" starttime="20240912 10:22:00.308" endtime="20240912 10:22:29.292"/>
</test>
<test id="s1-t4" name="Filter the number of Vendor Emails displayed to show 15 emails per page." line="40">
<kw name="VMS Vendor Email - Validate Rows Per Page Filter">
<arg>Filter nummber of rows to be displayed per page</arg>
<arg>VMS_UAT</arg>
<arg>15</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240912 10:22:29.296" level="INFO">Set test documentation to:
Filter nummber of rows to be displayed per page</msg>
<status status="PASS" starttime="20240912 10:22:29.295" endtime="20240912 10:22:29.296"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:29.297" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240912 10:22:29.296" endtime="20240912 10:22:29.297"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:29.297" endtime="20240912 10:22:29.298"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:29.298" endtime="20240912 10:22:29.299"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:29.300" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:29.300"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:22:29.300" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:29.300"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:29.300"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240912 10:22:29.338" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240912 10:22:29.940" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240912 10:22:29.940" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:29.940"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240912 10:22:29.940" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240912 10:22:29.940" endtime="20240912 10:22:29.957"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.940" endtime="20240912 10:22:29.957"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:29.957"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:22:29.957" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20240912 10:22:29.957" endtime="20240912 10:22:29.957"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:22:29.957" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20240912 10:22:29.957" endtime="20240912 10:22:29.957"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20240912 10:22:29.959" level="INFO">${browser_name} = CHROME</msg>
<status status="PASS" starttime="20240912 10:22:29.957" endtime="20240912 10:22:29.959"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:22:29.959" endtime="20240912 10:22:29.959"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:22:29.959" endtime="20240912 10:22:29.959"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</branch>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</if>
<status status="NOT RUN" starttime="20240912 10:22:29.959" endtime="20240912 10:22:29.960"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:22:29.960" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<status status="PASS" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:29.960" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001CBE6B0A090&gt;</msg>
<status status="PASS" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.960"/>
</branch>
<status status="PASS" starttime="20240912 10:22:29.960" endtime="20240912 10:22:29.962"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240912 10:22:29.962" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240912 10:22:29.962" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240912 10:22:29.962" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240912 10:22:29.962" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\User...</msg>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:29.962"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20240912 10:22:29.962" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20240912 10:22:29.962" endtime="20240912 10:22:32.338"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.960" endtime="20240912 10:22:32.338"/>
</branch>
<status status="PASS" starttime="20240912 10:22:29.959" endtime="20240912 10:22:32.338"/>
</if>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:32.338"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20240912 10:22:32.343" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg timestamp="20240912 10:22:32.343" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<status status="PASS" starttime="20240912 10:22:32.339" endtime="20240912 10:22:32.343"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20240912 10:22:32.343" endtime="20240912 10:22:32.352"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20240912 10:22:32.354" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<status status="PASS" starttime="20240912 10:22:32.354" endtime="20240912 10:22:37.311"/>
</kw>
<status status="PASS" starttime="20240912 10:22:32.352" endtime="20240912 10:22:37.311"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:39.313" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:22:37.311" endtime="20240912 10:22:39.313"/>
</kw>
<status status="PASS" starttime="20240912 10:22:32.338" endtime="20240912 10:22:39.313"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:39.337" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:22:39.313" endtime="20240912 10:22:39.337"/>
</kw>
<msg timestamp="20240912 10:22:39.337" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:22:39.313" endtime="20240912 10:22:39.337"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:39.337" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:22:39.337" endtime="20240912 10:22:39.403"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:22:39.403" endtime="20240912 10:22:39.418"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:39.418" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:22:39.418" endtime="20240912 10:22:39.491"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:39.491" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:22:39.491" endtime="20240912 10:22:39.932"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:41.932" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:22:39.932" endtime="20240912 10:22:41.932"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:41.945" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20240912 10:22:41.933" endtime="20240912 10:22:41.945"/>
</kw>
<msg timestamp="20240912 10:22:41.945" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20240912 10:22:41.933" endtime="20240912 10:22:41.945"/>
</kw>
<status status="PASS" starttime="20240912 10:22:39.337" endtime="20240912 10:22:41.945"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:41.945" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20240912 10:22:41.945" endtime="20240912 10:22:42.203"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:22:42.203" endtime="20240912 10:22:42.219"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:42.219" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20240912 10:22:42.219" endtime="20240912 10:22:42.384"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:42.384" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20240912 10:22:42.384" endtime="20240912 10:22:44.568"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:46.568" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20240912 10:22:44.568" endtime="20240912 10:22:46.568"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:46.689" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-25.png"&gt;&lt;img src="selenium-screenshot-25.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="20240912 10:22:46.689" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<status status="FAIL" starttime="20240912 10:22:46.568" endtime="20240912 10:22:46.692"/>
</kw>
<msg timestamp="20240912 10:22:46.692" level="INFO">${User_Name_Element_Visible} = False</msg>
<status status="PASS" starttime="20240912 10:22:46.568" endtime="20240912 10:22:46.692"/>
</kw>
<status status="PASS" starttime="20240912 10:22:41.945" endtime="20240912 10:22:46.692"/>
</iter>
<status status="PASS" starttime="20240912 10:22:39.337" endtime="20240912 10:22:46.692"/>
</while>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:46.692" endtime="20240912 10:22:46.698"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:46.810" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-26.png"&gt;&lt;img src="selenium-screenshot-26.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:46.698" endtime="20240912 10:22:46.810"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.300" endtime="20240912 10:22:46.810"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.299" endtime="20240912 10:22:46.810"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.296" endtime="20240912 10:22:46.810"/>
</kw>
<kw name="When The user navigates to Admin - Email Management" library="EmailManagement">
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:22:46.878" level="INFO">Current page contains text 'Dashboard'.</msg>
<status status="PASS" starttime="20240912 10:22:46.810" endtime="20240912 10:22:46.878"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:47.005" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:46.878" endtime="20240912 10:22:47.005"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:47.005" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<status status="PASS" starttime="20240912 10:22:47.005" endtime="20240912 10:22:47.103"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${EMAIL_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:47.103" level="INFO">Clicking element 'xpath=//a[contains(@class,'nav-link')][text()[normalize-space(.)='Email Management']]'.</msg>
<status status="PASS" starttime="20240912 10:22:47.103" endtime="20240912 10:22:47.755"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Email Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="20240912 10:22:47.777" level="INFO">Current page contains text 'Email Management'.</msg>
<status status="PASS" starttime="20240912 10:22:47.755" endtime="20240912 10:22:47.777"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>Email_Management_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:47.899" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="Email_Management_Landing_Page.png"&gt;&lt;img src="Email_Management_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:47.777" endtime="20240912 10:22:47.899"/>
</kw>
<status status="PASS" starttime="20240912 10:22:46.810" endtime="20240912 10:22:47.899"/>
</kw>
<kw name="And The user filters the number of Vendor Email to be displayed using rows per page filter" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Validate Integer" library="Common_Functions">
<var>${integer_validation}</var>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:22:47.901" level="INFO">${integer_validation} = valid integer</msg>
<status status="PASS" starttime="20240912 10:22:47.900" endtime="20240912 10:22:47.901"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${integer_validation}' == 'integer not valid'</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be an integer. The valid values are 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:22:47.901" endtime="20240912 10:22:47.901"/>
</kw>
<kw name="Check Element In List" library="Common_Functions">
<var>${is_in_list}</var>
<arg>${STRING_LIST}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<msg timestamp="20240912 10:22:47.902" level="INFO">${is_in_list} = True</msg>
<status status="PASS" starttime="20240912 10:22:47.902" endtime="20240912 10:22:47.902"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>${is_in_list} == ${False}</arg>
<arg>Fail</arg>
<arg>The provided value, which is '${ROWS_TO_SELECT}', for the parameter: ROWS_TO_SELECT, must be amongst 1, 5, 10 and 15.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240912 10:22:47.903" endtime="20240912 10:22:47.903"/>
</kw>
<kw name="Select From List By Value" library="SeleniumLibrary">
<arg>${ROWS_PER_PAGE_FILTER}</arg>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Selects options from selection list ``locator`` by ``values``.</doc>
<msg timestamp="20240912 10:22:47.904" level="INFO">Selecting options from selection list 'xpath=//select[@id='changeRows'][@name='rowsPerPage']' by value 15.</msg>
<status status="PASS" starttime="20240912 10:22:47.903" endtime="20240912 10:22:47.988"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:48.099" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_15.png"&gt;&lt;img src="USER_FILTER_15.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:47.988" endtime="20240912 10:22:48.099"/>
</kw>
<status status="PASS" starttime="20240912 10:22:47.900" endtime="20240912 10:22:48.099"/>
</kw>
<kw name="Then The number of rows returned on the page must be the same as the number that was used for filtering" library="EmailManagement">
<arg>${ROWS_TO_SELECT}</arg>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${VMS_USERS_TABLE}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20240912 10:22:48.123" level="INFO">Element 'xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]' is displayed.</msg>
<status status="PASS" starttime="20240912 10:22:48.100" endtime="20240912 10:22:48.123"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_tbody_element}</var>
<arg>${VMS_USERS_TABLE}/tbody[contains(@class,'gs-table-body')]</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:22:48.124" level="INFO">${table_tbody_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]</msg>
<status status="PASS" starttime="20240912 10:22:48.123" endtime="20240912 10:22:48.124"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${table_rows_element}</var>
<arg>${table_tbody_element}/tr</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240912 10:22:48.124" level="INFO">${table_rows_element} = xpath=//table[contains(@class,'table')][contains(@class,'gs-table')]/tbody[contains(@class,'gs-table-body')]/tr</msg>
<status status="PASS" starttime="20240912 10:22:48.124" endtime="20240912 10:22:48.124"/>
</kw>
<kw name="Get WebElements" library="SeleniumLibrary">
<var>${rows}</var>
<arg>${table_rows_element}</arg>
<doc>Returns a list of WebElement objects matching the ``locator``.</doc>
<msg timestamp="20240912 10:22:48.139" level="INFO">${rows} = [&lt;selenium.webdriver.remote.webelement.WebElement (session="e1bc8b8c8beb1f94b8bac02d66c35c30", element="f.57E716014FC1203A1E190488B13F51E9.d.D6FDB31584720DEE6B19D5701BE15ACF.e.166")&gt;, &lt;selenium.webdri...</msg>
<status status="PASS" starttime="20240912 10:22:48.124" endtime="20240912 10:22:48.140"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${table_rows_count}</var>
<arg>${rows}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:22:48.140" level="INFO">Length is 15</msg>
<msg timestamp="20240912 10:22:48.140" level="INFO">${table_rows_count} = 15</msg>
<status status="PASS" starttime="20240912 10:22:48.140" endtime="20240912 10:22:48.140"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${table_rows_count}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<msg timestamp="20240912 10:22:48.141" level="INFO">Argument types are:
&lt;class 'str'&gt;
&lt;class 'int'&gt;</msg>
<status status="PASS" starttime="20240912 10:22:48.141" endtime="20240912 10:22:48.142"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.141" endtime="20240912 10:22:48.142"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:22:48.142" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:22:48.142" level="INFO">: Expected Value = 15, Actual Value = 15</msg>
<status status="PASS" starttime="20240912 10:22:48.142" endtime="20240912 10:22:48.142"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.140" endtime="20240912 10:22:48.142"/>
</kw>
<kw name="Get Text" library="SeleniumLibrary">
<var>${page_number_text}</var>
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:48.170" level="INFO">${page_number_text} = Showing 1 to 15 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:22:48.143" endtime="20240912 10:22:48.170"/>
</kw>
<kw name="Get Substring" library="String">
<var>${substring}</var>
<arg>${page_number_text}</arg>
<arg>13</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:22:48.171" level="INFO">${substring} = 15 of 110 rows</msg>
<status status="PASS" starttime="20240912 10:22:48.171" endtime="20240912 10:22:48.171"/>
</kw>
<kw name="Get Length" library="BuiltIn">
<var>${rows_variable_digits_length}</var>
<arg>${ROWS_TO_SELECT}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<msg timestamp="20240912 10:22:48.171" level="INFO">Length is 2</msg>
<msg timestamp="20240912 10:22:48.172" level="INFO">${rows_variable_digits_length} = 2</msg>
<status status="PASS" starttime="20240912 10:22:48.171" endtime="20240912 10:22:48.172"/>
</kw>
<if>
<branch type="IF" condition="${rows_variable_digits_length} == 1">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>2</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<status status="NOT RUN" starttime="20240912 10:22:48.172" endtime="20240912 10:22:48.172"/>
</kw>
<status status="NOT RUN" starttime="20240912 10:22:48.172" endtime="20240912 10:22:48.172"/>
</branch>
<branch type="ELSE">
<kw name="Get Substring" library="String">
<var>${substring2}</var>
<arg>${substring}</arg>
<arg>0</arg>
<arg>3</arg>
<doc>Returns a substring from ``start`` index to ``end`` index.</doc>
<msg timestamp="20240912 10:22:48.173" level="INFO">${substring2} = 15 </msg>
<status status="PASS" starttime="20240912 10:22:48.173" endtime="20240912 10:22:48.173"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.172" endtime="20240912 10:22:48.173"/>
</branch>
<status status="PASS" starttime="20240912 10:22:48.172" endtime="20240912 10:22:48.173"/>
</if>
<kw name="Set Variable" library="BuiltIn">
<var>${trimmed_string}</var>
<arg>${substring2.strip()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20240912 10:22:48.174" level="INFO">${trimmed_string} = 15</msg>
<status status="PASS" starttime="20240912 10:22:48.173" endtime="20240912 10:22:48.174"/>
</kw>
<kw name="Verify if values are equal" library="common_keywords">
<arg>${ROWS_TO_SELECT}</arg>
<arg>${trimmed_string}</arg>
<kw name="Run Keyword And Continue On Failure" library="BuiltIn">
<arg>Should Be Equal As Strings</arg>
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Runs the keyword and continues execution even if a failure occurs.</doc>
<kw name="Should Be Equal As Strings" library="BuiltIn">
<arg>${EXPECTED_VALUE}</arg>
<arg>${ACTUAL_VALUE}</arg>
<doc>Fails if objects are unequal after converting them to strings.</doc>
<status status="PASS" starttime="20240912 10:22:48.175" endtime="20240912 10:22:48.175"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.175" endtime="20240912 10:22:48.176"/>
</kw>
<kw name="Log Many" library="BuiltIn">
<arg>Comparison result</arg>
<arg>: Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}</arg>
<doc>Logs the given messages as separate entries using the INFO level.</doc>
<msg timestamp="20240912 10:22:48.176" level="INFO">Comparison result</msg>
<msg timestamp="20240912 10:22:48.176" level="INFO">: Expected Value = 15, Actual Value = 15</msg>
<status status="PASS" starttime="20240912 10:22:48.176" endtime="20240912 10:22:48.176"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.175" endtime="20240912 10:22:48.176"/>
</kw>
<kw name="Scroll Element Into View" library="SeleniumLibrary">
<arg>${ROW_NUMBERS_DISPLAYED}</arg>
<doc>Scrolls the element identified by ``locator`` into view.</doc>
<status status="PASS" starttime="20240912 10:22:48.176" endtime="20240912 10:22:48.472"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>USER_FILTER_${ROWS_TO_SELECT}_VERIFICATION.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="20240912 10:22:48.575" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="USER_FILTER_15_VERIFICATION.png"&gt;&lt;img src="USER_FILTER_15_VERIFICATION.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="20240912 10:22:48.472" endtime="20240912 10:22:48.575"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.100" endtime="20240912 10:22:48.575"/>
</kw>
<status status="PASS" starttime="20240912 10:22:29.295" endtime="20240912 10:22:48.575"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20240912 10:22:48.575" endtime="20240912 10:22:48.599"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240912 10:22:48.599" endtime="20240912 10:22:48.601"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20240912 10:22:48.601" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<status status="PASS" starttime="20240912 10:22:48.601" endtime="20240912 10:22:48.884"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20240912 10:22:51.884" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20240912 10:22:48.884" endtime="20240912 10:22:51.884"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20240912 10:22:51.884" endtime="20240912 10:22:54.831"/>
</kw>
<status status="PASS" starttime="20240912 10:22:48.575" endtime="20240912 10:22:54.831"/>
</kw>
<doc>Filter nummber of rows to be displayed per page</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" starttime="20240912 10:22:29.294" endtime="20240912 10:22:54.831"/>
</test>
<doc>Add new User to VMS</doc>
<status status="PASS" starttime="20240912 10:21:13.104" endtime="20240912 10:22:54.834"/>
</suite>
<statistics>
<total>
<stat pass="4" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="4" fail="0" skip="0">HEALTHCHECK_STATUS</stat>
<stat pass="4" fail="0" skip="0">Login</stat>
<stat pass="4" fail="0" skip="0">VMS_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="4" fail="0" skip="0" id="s1" name="VMS Portal">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="20240912 10:21:38.684" level="WARN">There was error during termination of process</msg>
<msg timestamp="20240912 10:22:00.781" level="WARN">There was error during termination of process</msg>
<msg timestamp="20240912 10:22:29.940" level="WARN">There was error during termination of process</msg>
</errors>
</robot>
