*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Setup                                          The User gets a Non-draft bin for deletion process
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-915




*** Keywords ***
Verifying that when a Non-Draft Bin is deleted- The request is routed to the Approver 
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The Bin Number is active in the Database                         ${BIN_NAME}
    When The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    And The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates the Bin details of the bin to be deleted        ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    And The user must be able to delete the bin
    Then The User verifies that the delete request was routed to the correct user role (approver)    ${BIN_NAME}

| *** Test Cases ***                                                                                                                                                                                                 |        *DOCUMENTATION*                               |         *BASE_URL*             |         *BIN_NAME*              |         *BIN_TYPE_NAME*          |         *BIN_ACTION_DATE*        |
| 	Capturer_Verify that when deleting a bin (not a draft), the delete process is routed to the approver for approval	    | Verifying that when a Non-Draft Bin is deleted- The request is routed to the Approver  | Validing correct role assignment for delete request  |           ${EMPTY}             | ${global_non_draft_bin_number}  |         Contactless              |         3/21/2025                |
