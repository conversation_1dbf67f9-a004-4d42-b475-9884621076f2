*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Verifying that the approver role does not permit users to capture campaigns 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../common_utilities/Login.robot
Resource                                            ../../../../common_utilities/Logout.robot
Resource                                            ../../../Keywords/atm_marketing/Dashboard.robot
Resource                                            ../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/atm_marketing/NegativeScenarios.robot
Resource                                            ../../../keywords/common/Navigation.robot
Resource                                            ../../../keywords/atm_marketing/Approvals.robot

*** Keyword ***
Validating the Approver Role
    [Arguments]  ${DOCUMENTATION}    ${LOGON_USER}    ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}    Chrome    drivers\chromedriver.exe  ${LOGON_USER}

    When The user has an active Approver Role 

    Then The user should not be able to access Capture Campaign

    
| *Test Cases*                                                                   |      *DOCUMENTATION*      |      *LOGON_USER*          |    *TEST_ENVIRONMENT*   | 
| RAC29a_TC_122_Show_maximum_campaign_feature   |  Validating the Approver Role  |  Approver Role            |    BUSINESS_APPROVER       |     APC_UAT             |