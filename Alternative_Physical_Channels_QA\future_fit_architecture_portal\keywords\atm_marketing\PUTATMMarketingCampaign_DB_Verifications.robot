*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             String
Variables                                          ../../utility/SQLVariables.py
#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot
Resource                                            ../../keywords/common/ATMMarketing_JSON_Requests.robot


#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***

Get the untargted campaign history details from the database
    [Arguments]     ${CAMPAIGN_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${CAMPAIGN_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${campaign_history_query}   Set Variable     ${SQL_GET_ATM_MARKETING_CAMPAIGN_HISTORY}
    ${campaign_history_query}=  Replace String      ${campaign_history_query}       campaign_ID     '${CAMPAIGN_ID}'



    ${data_base_campaign_history_details}=      Execute SQL Query  ${db_type}  ${campaign_history_query}    True
    RETURN      ${data_base_campaign_history_details}

Get the language details from the database
    [Arguments]     ${LANGUAGE}

    #Verify that all parameters are supplied
    Run Keyword If    '${LANGUAGE}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${campaign_language_query}   Set Variable     ${SQL_GET_MARKETING_LANGUAGES_USING_LANGUAGE_NAME}
    ${campaign_language_query}=  Replace String      ${campaign_language_query}       L_ANG     '${LANGUAGE}'



    ${data_base_campaign_language_details}=      Execute SQL Query  ${db_type}  ${campaign_language_query}    True
    RETURN      ${data_base_campaign_language_details}


Get the untargted campaign details based on the update history from the database
    [Arguments]     ${CAMPAIGN_ID}      ${CAMPAIGN_HISTORY_ID}

    #Verify that all parameters are supplied
    Run Keyword If    '${CAMPAIGN_ID}' == '${EMPTY}' or '${CAMPAIGN_HISTORY_ID}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${campaign_targted_query}   Set Variable     ${SQL_GET_ATM_MARKETING_CAMPAIGN_DETAILS_BASED_ON_HISTORY}
    ${campaign_targted_query}=  Replace String      ${campaign_targted_query}       campaign_ID     '${CAMPAIGN_ID}'
    ${campaign_targted_query}=  Replace String      ${campaign_targted_query}       campaign_history_id     '${CAMPAIGN_HISTORY_ID}'



    ${data_base_campaign_targeted_campaign_details}=      Execute SQL Query  ${db_type}  ${campaign_targted_query}    True   True
    RETURN      ${data_base_campaign_targeted_campaign_details}



Get Campaign History details for the Campaign to be edited
    [Arguments]     ${CAMPAIGN_ID}

     #Verify that the provided varibles are of the correct data type
    ${integer_validation}=       Validate Integer    ${CAMPAIGN_ID}
    Run Keyword If    '${integer_validation}' == 'integer not valid'    Fail    The provided value for CAMPAIGN_ID, which is '${CAMPAIGN_ID}', is not an integer.


    ${campaign_history_details}=          Get the untargted campaign history details from the database      ${CAMPAIGN_ID}
    ${campaign_history_id}=      Get From Dictionary    ${campaign_history_details}    id

    ${campaign_details}=          Get the untargted campaign details based on the update history from the database      ${CAMPAIGN_ID}       ${campaign_history_id}
    ${result}=  Run Keyword And Return Status    Should Not be Empty     '${campaign_details}'
    Run Keyword If    '${result}' == 'False'    Fail    The campaign with the ID: ${CAMPAIGN_ID}, does not exist on the DB. Hence it could not be edited.

    RETURN  ${campaign_details}

