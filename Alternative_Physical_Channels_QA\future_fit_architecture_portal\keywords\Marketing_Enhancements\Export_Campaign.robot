*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation  VMS Dashboard Validation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                             String
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../../utility/DatabaseUtility.py
Resource                                             ../../keywords/common/DBUtility.robot
Library                                             XML
Library                                             Collections


#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${EXPORT_DATA_BUTTON}       xpath=//button//span//span[contains(text(), 'Export')]
${CALENDER_VIEW_PAGE_BUTTON}       xpath=//span[contains(text(), 'Calendar View')]
${DOWNLOADDIR}      C:/Users/<USER>/Downloads
${FILE_FOUND}   ${False}

*** Keywords ***
The user clicks on Export Campaign Data button
    Wait Until Element Is Visible    ${EXPORT_DATA_BUTTON}
    Click Element    ${EXPORT_DATA_BUTTON}
    Log to console      --------------------------The user has clicked Export Campaign Data button
    Capture Page Screenshot
    Sleep    5

The user navigates to the Calendar View page
    Wait Until Element Is Visible       ${CALENDER_VIEW_PAGE_BUTTON}    timeout=10
    Click Element    ${CALENDER_VIEW_PAGE_BUTTON}
    Log to console      --------------------------The user has clicked Calender View Page button
    Capture Page Screenshot

The user verifies that export file has been downloaded
    ${files}=    List Files In Directory    ${DOWNLOADDIR}
    FOR    ${file}    IN    @{files}
        ${dir}    ${filename} =    Split Path    ${file}
        ${result}=    Run Keyword And Ignore Error    Should Match Regexp   '${filename}'    .*exported_data.*
        IF    '${result[0]}' == 'PASS'
            ${FILE_FOUND}=  Set Variable  ${True}
            Log To Console    File found: ${file}
            Exit For Loop
        ELSE
            Log To Console    Filename does not match 'export' pattern: ${filename}
        END
    END
    Run Keyword If    ${FILE_FOUND} == ${False}  Fail   No file found with 'export' in the filename in directory ${DOWNLOADDIR}

