*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       SLA Status Per Main Vendor- This Week Validation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             String


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${APPLICATION_USERNAME}                             AB038N8
${APPLICATION_PASSWORD}                             67355870@SitholeBrother
${BROWSER}                                          edge
${IS_HEADLESS_BROWSER}                              No

*** Keywords ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user lands on the dashboard page

    # Simple validation that we're on the dashboard
    Page Should Contain    Dashboard

    # Validate SLA Status section exists
    Page Should Contain    SLA Status

*** Test Cases ***
| Validate SLA Status per Main Vendor- This Week | Dashboard Validation     | Validates SLA Status Per Main Vendor for this week   |      VMS_UAT             |