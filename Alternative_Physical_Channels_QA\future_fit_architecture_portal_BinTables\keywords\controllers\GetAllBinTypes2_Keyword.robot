** Settings ***
#Author Name               : <PERSON><PERSON>
#Email Address             : Ya<PERSON>.<EMAIL>

Documentation              Bin Tables GetAllBinTypes Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            ../../keywords/controllers/resources/bintypes/GetAllBinTypes.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py


#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../keywords/controllers/GetAllBinTypes2_Keyword.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***
${SQL_GET_ALL_BIN_TYPES}                           SELECT * FROM BinDbs.BinTypes where IsDeleted= '0'

*** Keywords ***
The User sends a Get Request for GetAllBinTypes
    [Arguments]     ${BASE_URL}     

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Get Property From File      ${BASE_URL}
     Log     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create GetAllBinTypes Instance    ${base_url}          #Create an instance of   GetAllBins

    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${response} =       Send Rest Request    ${endpoint}   method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance

The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'GetAllBinTypes' REST API call failed, the returned status is '${status_code}'

Create GetAllBinTypes Instance
    [Arguments]    ${BASE_URL}  
    ${instance}=    Evaluate    GetAllBinTypes.CreateRESTRequest('${BASE_URL}')    modules=GetAllBinTypes
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

The Active Bin Types are retuned by the API Response
    Run Keyword    Create ReadApiResponse Instance
# Respose Keywords
Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}

    ${ALL_BIN_TYPES}=    Get the Bin Types    
    ${bin_type_count}=    Get Length    ${ALL_BIN_TYPES}
    Log Many    ${bin_type_count}
    Set Suite Variable    ${bin_type_count}

Get the Bin Types
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_all_bin_types
    RETURN    ${result}


Get Response Status Code
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

The Active Bin Types returned must exist on the Bin Database
    Log    The Bin Type Count returned by service is:${bin_type_count}

    ${db_type}=   Set Variable   MYSQL
    ${my_query}   Set Variable    ${SQL_GET_ALL_BIN_TYPES}

    ${database_get_all_bin_types_results}=    Execute Select Query    ${DB_TYPE}    ${DB_HOST}    ${DB_NAME}    ${DB_USER}    ${DB_PASSWORD}    ${my_query}

    Log Many    ${database_get_all_bin_types_results}

    ${database_bin_type_count}=    Get Length    ${database_get_all_bin_types_results}
    Log    Number of rows returned: ${database_bin_type_count}
    Log Many    ${database_bin_type_count}
    
    Should Be Equal    ${bin_type_count}    ${database_bin_type_count}