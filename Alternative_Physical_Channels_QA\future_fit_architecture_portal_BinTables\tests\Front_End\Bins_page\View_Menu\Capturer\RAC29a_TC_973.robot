*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number
${TEST_CASE_ID}             RAC29a-TC-973




*** Keywords ***
Verify Bins displayed on View Entries Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${COL_NAMES}
    Set Test Documentation  ${DOCUMENTATION}
    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    Then The expected columns must be displayed on the View Entries table               ${COL_NAMES}


| *** Test Cases ***                                                                                                                                                                                                                                                 |        *DOCUMENTATION*    		         |         *BASE_URL*                  |                                             *COL_NAMES*                                                        |
| Capturer_Verify BINTable Includes All Required Data Fields: Bin Number, BIN Type, Action Date, Captured Date, Captured By, Outcome, Review Date, Review By, Review Status, Rejection Comment, Edit Icon             | Verify Bins displayed on View Entries Page   | Verify bins against the database data.    |           ${EMPTY}                  |    Bin Number,Bin Type,Action Date,Captured Date,Captured By,Outcome,Review Date,Reviewed By,Review Status     |
