import jwt

# Sample JWT strings
jwt1 = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayIsImtpZCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOJKcuE-aXO295ovQUTjsJdKerV9aJ085EleMfUvpnWyqcINIQ0SOVszgYfmeWmpcV0D2bRDEDGdJttq4pUMgo88YP8rjW-u12T0ihK8TV_946H4lxWOr2C58i47trPWe_zaLNkuWXueJ7youziW0vIgpr2oK98RJzjYG-crhuRw9sLvdoVeRtKtWznb85UVr2tl-WvTQZ4uRr49KnSyq19IIivBu-b68DOa1dVuLbKRAM3zUx8Bsu-tIdvbJxpy7hDQstMKDtvqap2JkHjiK6n2prplvb2WGxn7IOE_ZVcqGtkuXitJzQFM3BuenGxw5RK1axCZ7LvxgpQ-qFtYlQ'
jwt2 = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayIsImtpZCI6ImltaTBZMnowZFlLeEJ0dEFxS19UdDVoWUJUayJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iOJKcuE-aXO295ovQUTjsJdKerV9aJ085EleMfUvpnWyqcINIQ0SOVszgYfmeWmpcV0D2bRDEDGdJttq4pUMgo88YP8rjW-u12T0ihK8TV_946H4lxWOr2C58i47trPWe_zaLNkuWXueJ7youziW0vIgpr2oK98RJzjYG-crhuRw9sLvdoVeRtKtWznb85UVr2tl-WvTQZ4uRr49KnSyq19IIivBu-b68DOa1dVuLbKRAM3zUx8Bsu-tIdvbJxpy7hDQstMKDtvqap2JkHjiK6n2prplvb2WGxn7IOE_ZVcqGtkuXitJzQFM3BuenGxw5RK1axCZ7LvxgpQ-qFtYlQ'

# Decode JWTs without verifying the signature
decoded_jwt1 = jwt.decode(jwt1, options={"verify_signature": False})
decoded_jwt2 = jwt.decode(jwt2, options={"verify_signature": False})

# Compare the entire payloads of the JWTs
if decoded_jwt1 == decoded_jwt2:
    print("The JWT payloads are identical.")
else:
    print("The JWT payloads are different.")

# Optionally, you can compare the signatures too
signature_jwt1 = jwt1.split('.')[2]
signature_jwt2 = jwt2.split('.')[2]
print('signature_jwt1', signature_jwt1)
print('signature_jwt2', signature_jwt2)

if signature_jwt1 == signature_jwt2:
    print("The JWT signatures are identical.")
else:
    print("The JWT signatures are different.")
