*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/UploadBin_Keywords.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot



*** Variables ***
${SUITE NAME}               BIN Tables - Upload Bin(s) details to Bin Tables




*** Keywords ***
Upload Bin(s)
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}   &{BINS_DETAILS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User Populates the Upload Bin JSON payload with    &{BINS_DETAILS}
    When The User sends the Upload Bin API Request               ${BASE_URL}
    And The service returns an expected status code         ${EXPECTED_STATUS_CODE}
    Then The Created Bin Number(s) details must exist on the Bin Database    &{BINS_DETAILS}

| *** Test Cases ***                                      |        *DOCUMENTATION*    		  |         *BASE_URL*                  |    *EXPECTED_STATUS_CODE*   |                *BINS_DETAILS*                                                                                                                                                                                                                        |
| Upload (Add) Bins to the Bin Tables   | Upload Bin(s)   | Add Bin(s) to the Bin Tables      |                                     |         200                 |   bin1=7004900 | date1=2026-11-11 | binIds1=0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e  |  bin2=7004901  |  date2=2026-11-12  |  binIds2=a7ff7c25-057b-461b-9fa1-50d471202b52,bb679411-b69d-42b1-a6c6-8e7cdc63d6c4  |  bin3=7004902  |  date3=2026-11-12  |  binIds3=a7ff7c25-057b-461b-9fa1-50d471202b52,bb679411-b69d-42b1-a6c6-8e7cdc63d6c4,0e446d56-d033-498e-9c5d-ed81ad8f3208,7283d28c-2b81-4c0e-a2e2-b0a6cd54d58e,9d628472-2928-4084-9a5a-2d1815a85c33  |
