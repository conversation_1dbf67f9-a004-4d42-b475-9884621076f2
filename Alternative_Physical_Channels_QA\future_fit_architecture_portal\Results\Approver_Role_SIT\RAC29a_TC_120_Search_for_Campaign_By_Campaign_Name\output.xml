<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:05:51.340635" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_120_Search_for_Campaign_By_Campaign_Name.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.378727" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:05:52.378727" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.379723" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:05:52.378727" elapsed="0.000996"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.379723" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:05:52.379723" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.379723" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:05:52.379723" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.379723" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:05:52.379723" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:52.378727" elapsed="0.000996"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_120_FFT_Approval_Search_for_Campaign_By_Campaign_Name" line="38">
<kw name="Validating the Search function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.380723" level="INFO">Set test documentation to:
Search by campaign name</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:05:52.380723" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.474026" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:05:52.381722" elapsed="0.092304"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:05:52.474026" elapsed="0.001459"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:05:52.475485" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.476490" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:05:52.476490" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:05:52.476490" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:05:52.476490" elapsed="0.001017"/>
</branch>
<status status="PASS" start="2024-10-29T12:05:52.476490" elapsed="0.001017"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.477507" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:05:52.477507" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:05:52.477507" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.510105" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:05:52.836930" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-29T12:05:52.836930" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:05:52.477507" elapsed="0.359423"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.837937" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:05:52.837937" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:05:52.836930" elapsed="0.001007"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:05:52.476490" elapsed="0.362445"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.838935" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:05:52.838935" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:05:52.838935" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:05:52.838935" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:05:52.838935" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:05:52.838935" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:05:52.838935" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:05:52.838935" elapsed="0.001021"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.839956" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:05:52.839956" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.840952" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001889BAD4EC0&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:05:52.839956" elapsed="0.000996"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:05:52.840952" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:05:52.840952" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:05:52.840952" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:05:52.840952" elapsed="0.000000"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.840952" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:05:52.840952" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:05:52.840952" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:05:52.841936" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:05:52.841936" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:05:52.841936" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:05:52.841936" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:05:52.841936" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:05:52.843034" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.843034" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:05:52.843034" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:05:52.843034" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:05:52.380723" elapsed="33.439512"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:06:25.820235" elapsed="0.019759"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:25.845008" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.66")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:25.839994" elapsed="0.005014"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:25.846007" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.66")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:25.846007" elapsed="0.030816"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:06:25.876823" elapsed="0.008254"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:25.876823" elapsed="0.008254"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:25.893212" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.67")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:25.885077" elapsed="0.008135"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:06:30.893721" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:06:25.893212" elapsed="5.000509"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:06:30.894235" elapsed="0.016689"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:30.911908" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.67")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:30.911908" elapsed="0.067567"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:06:30.979475" elapsed="0.238466"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:30.979475" elapsed="0.238466"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:06:31.218992" elapsed="0.006997"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:31.218992" elapsed="0.006997"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:31.241499" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:06:31.225989" elapsed="0.015510"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:25.820235" elapsed="5.421264"/>
</kw>
<kw name="And The user inputs a campaign name on the Search field" owner="Approvals">
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:06:36.242736" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:06:31.242500" elapsed="5.000236"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:36.262161" level="INFO">${Campaign_Name} = SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<var>${Campaign_Name}</var>
<arg>xpath=//td[contains(@class, 'mat-column-campaignName')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:36.243768" elapsed="0.018393"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:06:36.262161" level="INFO">Campaign Name retrieved: SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<arg>Campaign Name retrieved: ${Campaign_Name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:06:36.262161" elapsed="0.000000"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:36.268783" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.75")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:36.262161" elapsed="0.006622"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:36.268783" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.75")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:36.268783" elapsed="0.022865"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:36.292567" level="INFO">Typing text 'SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.75")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${Campaign_Name}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:36.291648" elapsed="0.071792"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:06:38.363673" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:06:36.363440" elapsed="2.000233"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:06:38.364771" level="INFO">${Campaign_Name} = SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<arg>${Campaign_Name}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:06:38.363673" elapsed="0.001098"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:06:38.365771" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="270b2a7da91c2e2abb654081ef0b552d", element="f.0F9FB415931418A622A90816748E936D.d.CA00A968055E972829116FCF1C9A74C1.e.75")&gt;</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:06:38.365395" elapsed="0.000508"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:31.241499" elapsed="7.124404"/>
</kw>
<kw name="Then The user verifies the search results returned by Campaign Name" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_RESULT_XPATH}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:06:38.367027" elapsed="0.016386"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:38.394274" level="INFO">${search_results} = SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk</msg>
<var>${search_results}</var>
<arg>xpath=//td[contains(text(), '${Campaign_Name}')]</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:38.383413" elapsed="0.010861"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${search_results}</arg>
<arg>${Campaign_Name}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T12:06:38.394274" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:38.394274" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:06:38.399353" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${SEARCH_RESULT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:38.394274" elapsed="0.005079"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${result_count} &gt; 0</arg>
<arg>msg=No search results found!</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:06:38.400311" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:06:38.400311" level="INFO">----Search completed successfully using campaign name: SIT_Cycle_4_Test Un_-Targeted_English Thabo_Setuk.----</msg>
<arg>----Search completed successfully using campaign name: ${Campaign_Name}.----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:06:38.400311" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Search completed successfully using campaign name: ${Campaign_Name}.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:06:38.400311" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:38.365903" elapsed="0.035406"/>
</kw>
<arg>Search by campaign name</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:05:52.380723" elapsed="46.020586"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:06:38.401309" elapsed="0.005949"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:38.401309" elapsed="0.005949"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:38.408276" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:06:38.408276" elapsed="0.031489"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:06:41.440052" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:06:38.439765" elapsed="3.000287"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:06:41.441071" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:06:41.508087" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:06:41.509114" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:06:41.440052" elapsed="0.070262">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:06:43.512367" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:06:41.510314" elapsed="2.002053"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:06:43.513355" elapsed="2.262871"/>
</kw>
<status status="FAIL" start="2024-10-29T12:06:38.407258" elapsed="7.368968">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:06:38.407258" elapsed="7.368968"/>
</kw>
<status status="PASS" start="2024-10-29T12:06:38.401309" elapsed="7.374917"/>
</kw>
<doc>Search by campaign name</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:05:52.379723" elapsed="53.397445"/>
</test>
<doc>Testing the Search Funtion on Campaign Approvals: Search by Campaign Name</doc>
<status status="PASS" start="2024-10-29T12:05:51.741040" elapsed="54.037187"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:05:51.334629" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_120_Search_for_Campaign_By_Campaign_Name.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:05:52.352505" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:05:52.837937" level="WARN">There was error during termination of process</msg>
<msg time="2024-10-29T12:06:10.691001" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:06:20.702775" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:06:25.716731" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:06:38.394274" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
