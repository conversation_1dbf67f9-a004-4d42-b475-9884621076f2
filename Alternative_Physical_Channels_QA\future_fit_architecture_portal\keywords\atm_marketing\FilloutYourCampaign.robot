*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/Utility.py
Library                                             DateTime

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../common/Navigation.robot
Resource                                            ../../keywords/common/GenericMethods.robot


*** Variables ***
${CAMPAIGN_NAME_INPUT}                              name=campaignName
${MARKETING_TYPE_DROPDOWN}                          xpath=//mat-select[@role="combobox" and @name="marketingType"]
${TRANSACTONAL_TYPE}                                xpath=//mat-option/span[contains(text(),"ATM")]
${IDLE_TYPE}                                        xpath=//*[@id="mat-option-15"]/span
${RECEIVER_DEVICE_TYPE_DROPDOWN}                    xpath=//mat-select[@role="combobox" and @name="receiverDeviceType"]
${ATM_DEVICE_TYPE}                                  xpath=/html/body/div[2]/div[2]/div/div/div/mat-option[1]/span
${SSK_DEVICE_TYPE}                                  xpath=//*[@id="mat-option-17"]/span
${BCD_DEVICE_TYPE}                                  xpath=//*[@id="mat-option-18"]/span
${CALENDAR_DAY_PATH1}                               //div[text()=
${CALENDAR_DAY_PATH2}                               ]/parent::*
${NEXT_MONTH}                                       xpath=//*[@aria-label="Next month"]
${LAST_DAY_OF_THE_MONTH}                            xpath=//div[text()=' 31 ']/parent::*

${CALENDAR_MONTH_NAME_LOCATOR}                      css=div .fc-toolbar-title
${CAPTURE_CAMPAIGN_CALENDAR}                        xpath=//mat-card[@id='testing']
${APC_PORTAL_CALENDAR_ROWS}                         xpath=//table/descendant::*[contains(@class,'fc-scrollgrid-sync-table')]/tbody/tr



*** Keywords ***

# FILL OUT YOUR CAMPAIGN
The user fills out Campaign
    [Arguments]        ${CAMPAIGN_NAME}  ${MARKETING_TYPE}  ${RECEIVER_DEVICE_TYPE}  ${CAMPAIGN_START_DATE_DATA}    ${CAMPAIGN_END_DATE_DATA}
    Sleep     2s
    
    Click Element     xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input

    input text         ${CAMPAIGN_NAME_INPUT}         ${CAMPAIGN_NAME}

    # Marketing Type Selection
    Log To Console    Marketing Type: ${MARKETING_TYPE}       
    
    # Run Keyword If    '${MARKETING_TYPE}' == 'Idle'  Select from dropdown  ${MARKETING_TYPE_DROPDOWN}  ${MARKETING_TYPE}
    #    ...  ELSE    Select from dropdown  ${MARKETING_TYPE_DROPDOWN}  ${MARKETING_TYPE}

    # Receiver Type Selection 
    Log To Console    Receiver Device Type: ${RECEIVER_DEVICE_TYPE}
    
    Run Keyword If    '${RECEIVER_DEVICE_TYPE}' == 'ATM'  Select from dropdown  ${RECEIVER_DEVICE_TYPE_DROPDOWN}  ${RECEIVER_DEVICE_TYPE}
      ...  ELSE    Select from dropdown  ${RECEIVER_DEVICE_TYPE_DROPDOWN}  ${RECEIVER_DEVICE_TYPE}


    #Select Campaign start and end date
    Select Campaign start and end date      ${CAMPAIGN_START_DATE_DATA}     ${CAMPAIGN_END_DATE_DATA}

    Sleep    2s

    #Save the details of the captured campaign to the environment variables
    Set Environment Variable    CAPTURED_CAMPAIGN_NAME            ${CAMPAIGN_NAME}

    Capture page screenshot  FilloutYourCampaign.png


Select Campaign start and end date
    [Arguments]    ${CAMPAIGN_START_DATE_DATA}    ${CAMPAIGN_END_DATE_DATA}

    #Verify that the campaign start date and end date have been provided
    IF   '${CAMPAIGN_START_DATE_DATA.replace('\n','').strip()}'==''
        Fail  Please provide the campaign start date. The date provided must be a number which represent the number of days from today.
    ELSE IF    '${CAMPAIGN_END_DATE_DATA.replace('\n','').strip()}'==''
        Fail  Please provide the campaign end date. The date provided must be a number which represent the number of days from today.
    END



    ${start_date_data_type}=    Check Data Type    ${CAMPAIGN_START_DATE_DATA}
    IF   ${start_date_data_type}!='INTEGER'
        Fail  Please provide the start date in Integer formart!
    END


    ${start_end_data_type}=    Check Data Type    ${CAMPAIGN_END_DATE_DATA}
    IF   ${start_end_data_type}!='INTEGER'
        Fail  Please provide the end date in Integer formart!
    END

    #Get the previous date from the system calendar
    ${current_date}              DateTime.Get Current Date    result_format=%Y-%m-%d

    #create the campaign start date using the number of days specified by ${CAMPAIGN_START_DATE_DATA} parameter
    ${campaign_required_start_date} =	Add Time To Date	${current_date}	${CAMPAIGN_START_DATE_DATA} days

    #create the campaign end date using the number of days specified by ${CAMPAIGN_END_DATE_DATA} parameter
    ${campaign_required_end_date} =	Add Time To Date	${current_date}	${CAMPAIGN_END_DATE_DATA} days

    Log  Campaign Start Date is: ${campaign_required_start_date}
    Log  Campaign End Date is: ${campaign_required_end_date}
    Search for Date on the Campaign Capturing Calendar      ${campaign_required_start_date}     ${campaign_required_end_date}
    

    Set Environment Variable    CAPTURED_CAMPAIGN_START_DATE      ${campaign_required_start_date}
    Set Environment Variable    CAPTURED_CAMPAIGN_END_DATE        ${campaign_required_end_date}


Search for Date on the Campaign Capturing Calendar
     [Arguments]    ${CAMPAIGN_START_DATE}    ${CAMPAIGN_END_DATE}

    #Verify that the campaign start date and end date have been provided
    IF   '${CAMPAIGN_START_DATE.replace('\n','').strip()}'==''
        Fail  Please provide the campaign start date.
    ELSE IF    '${CAMPAIGN_END_DATE.replace('\n','').strip()}'==''
        Fail  Please provide the campaign end date.
    END


    ${element_count}=        Get the number of displayed web elements   ${CAPTURE_CAMPAIGN_CALENDAR}
    Return From Keyword If	  ${element_count} == 0     Fail	Calendar element for selecting the campaign date was not found.
    IF   ${element_count} > 1
        Fail	More than 1 element was located for the calendar element
    END

    ${calendar_month_year_element_path}=  Catenate      ${CAPTURE_CAMPAIGN_CALENDAR}        /descendant::*[@id='mat-calendar-button-0']
    ${calendar_next_month_element_path}=  Catenate      ${CAPTURE_CAMPAIGN_CALENDAR}        /descendant::*[contains(@class,'mat-calendar-next-button')]
    ${calendar_body_element_path}=  Catenate      ${CAPTURE_CAMPAIGN_CALENDAR}        /descendant::tbody
    ${calendar_body_rows_element_path}=  Catenate      ${calendar_body_element_path}        /tr

    #Get the month and year for the start date and end date to be selected
    ${campaign_required_start_date_month_and_year}=    Convert Date	${CAMPAIGN_START_DATE}    result_format=%b %Y
    ${campaign_required_start_date_month}=    Convert Date	${CAMPAIGN_START_DATE}    result_format=%b
    ${campaign_required_end_date_month_and_year}=    Convert Date	${CAMPAIGN_END_DATE}    result_format=%b %Y
    ${campaign_required_end_date_month}=    Convert Date	${CAMPAIGN_END_DATE}    result_format=%b

    #Verify that the Campaign start month and end month are in the same quarter
    Create Campaign Quarters

    IF    '${campaign_required_start_date_month}' in ${Q1_MONTHS}
        ${campaign_start_date_quarter}=     Convert To String    q1
    ELSE IF    '${campaign_required_start_date_month}' in ${Q2_MONTHS}
        ${campaign_start_date_quarter}=     Convert To String    q2
    ELSE IF    '${campaign_required_start_date_month}' in ${Q3_MONTHS}
        ${campaign_start_date_quarter}=     Convert To String    q3
    ELSE
        ${campaign_start_date_quarter}=     Convert To String    q4
    END

    IF    '${campaign_required_end_date_month}' in ${Q1_MONTHS}
        ${campaign_end_date_quarter}=     Convert To String    q1
    ELSE IF    '${campaign_required_end_date_month}' in ${Q2_MONTHS}
        ${campaign_end_date_quarter}=     Convert To String    q2
    ELSE IF    '${campaign_required_end_date_month}' in ${Q3_MONTHS}
        ${campaign_end_date_quarter}=     Convert To String    q3
    ELSE
        ${campaign_end_date_quarter}=     Convert To String    q4
    END

    Run Keyword If    '${campaign_start_date_quarter}' != '${campaign_end_date_quarter}'
    ...    Fail    Campaign Start Date and End Date must fall in the same Cycle! The campaign start date falls in '${campaign_start_date_quarter}', while the end date falls in '${campaign_end_date_quarter}'
    ...  ELSE IF    '${campaign_start_date_quarter}' == '${campaign_end_date_quarter}'
    ...    Log Many    Campaign Start Date and End Date fall in the same Cycle...


    Log     campaign_required_start_date_month_and_year: ${campaign_required_start_date_month_and_year}
    Log     campaign_required_start_date_month: ${campaign_required_start_date_month}
    Log     campaign_required_end_date_month_and_year: ${campaign_required_end_date_month_and_year}
    Log     campaign_required_end_date_month: ${campaign_required_end_date_month}

    #Get the day to select for the start date
    ${start_date_array}=    Split String    ${CAMPAIGN_START_DATE}      ${SPACE}
    ${start_date_array_two}=    Split String    ${start_date_array}[0]      separator=-
    ${campaign_start_day}=      Set Variable    ${start_date_array_two}[2]

    Log     campaign_start_day:${campaign_start_day}

    #Get the day to select for the end date
    ${end_date_array}=    Split String    ${CAMPAIGN_END_DATE}      ${SPACE}
    ${end_date_array_two}=    Split String    ${end_date_array}[0]      separator=-
    ${campaign_end_day}=      Set Variable    ${end_date_array_two}[2]

    Log     campaign_end_day:${campaign_end_day}

    ${campaign_required_start_date_month_and_year}=  Convert To Uppercase  ${campaign_required_start_date_month_and_year}

    #Verify that the calendar is on the month that must be selected for start date, if not then navigate to the correct month
    ${curr_month_year_displayed}=       Get Text    ${calendar_month_year_element_path}
    Log    Current Displayed Month and Year:${curr_month_year_displayed}

    WHILE    '${curr_month_year_displayed.strip()}' != '${campaign_required_start_date_month_and_year.strip()}'
        Click Button    ${calendar_next_month_element_path}
        Sleep    5s
         ${curr_month_year_displayed}=       Get Text    ${calendar_month_year_element_path}
         Log Many   Month displayed:'${curr_month_year_displayed}'  Month Required:'${campaign_required_start_date_month_and_year}'
    END

    #Loop through the calendar to search for the month and day required for start date and end date
    ${rows}=    SeleniumLibrary.Get Element Count    ${calendar_body_rows_element_path}  #Get the number of rows from the APC calendar
    ${bool_start_date_selected}=    Set Variable    ${False}
    ${bool_start_end_selected}=    Set Variable    ${False}
    #Select campaign start date
    ${campaign_start_day_part} =      String.Get Substring    ${campaign_start_day.strip()}    0      1

    Log    ${campaign_start_day.strip()}


    IF    '${campaign_start_day_part}' == '0'
         ${campaign_start_day} =      String.Get Substring    ${campaign_start_day.strip()}    1      2
    END

    Log    ${campaign_start_day_part}

    FOR    ${row_num}    IN RANGE    1    ${rows+1}

        ${curr_row_element_path} =   Catenate    ${calendar_body_rows_element_path}   [${row_num}]
        ${col_element_path} =   Catenate    ${curr_row_element_path}       /td
        ${cols}=    SeleniumLibrary.Get Element Count    ${col_element_path}  #Get the number of columns from the current row

        FOR    ${col_num}    IN RANGE    1    ${cols+1}       #Loop through columns

            ${curr_col_element_path} =   Catenate    ${col_element_path}   [${col_num}]    #build element path for the current column
            ${campaign_date}=     SeleniumLibrary.Get Text    ${curr_col_element_path}        #Read the campaign(s) start date (day)
            Log    ${campaign_date}
            #If the current date is the required one for start date then select it
            IF    '${campaign_start_day.strip()}' == '${campaign_date.strip()}'
                Set Focus To Element        ${curr_col_element_path}

                ${row_num}=  Set Variable  0
                ${bool_start_date_selected}=    Set Variable    ${True}
                Click Element    ${curr_col_element_path}
                Log     Campaign Start Date Selected!
                Capture page screenshot  StartDateSelection.png
                Exit For Loop
            END

        END
        Run Keyword If  ${bool_start_date_selected} == ${True}  Exit For Loop
    END

    Run Keyword If  ${bool_start_date_selected} == ${False}  Fail   Campaign start date: '${campaign_start_day.strip()}' was not selected!!

     #Verify that the calendar is on the month that must be selected for end date, if not then navigate to the correct month
    ${campaign_required_end_date_month_and_year}=  Convert To Uppercase  ${campaign_required_end_date_month_and_year}

    ${curr_month_year_displayed}=       Get Text    ${calendar_month_year_element_path}
    Log To Console    Current Displayed Month and Year:${curr_month_year_displayed}
    Log To Console    Month Required:${campaign_required_end_date_month_and_year}

    WHILE    '${curr_month_year_displayed.strip()}' != '${campaign_required_end_date_month_and_year.strip()}'
        Click Button    ${calendar_next_month_element_path}
        Sleep    5s
         ${curr_month_year_displayed}=       Get Text    ${calendar_month_year_element_path}
         Log To Console    Month displayed:'${curr_month_year_displayed}'  Month Required:'${campaign_required_end_date_month_and_year}'
    END

    ${rows_new}=    SeleniumLibrary.Get Element Count    ${calendar_body_rows_element_path}  #Get the number of rows from the APC calendar
    ${bool_end_date_selected}=    Set Variable    ${False}
    #Select campaign end date
    Log   ${campaign_end_day.strip()}

    ${campaign_end_day_part} =      String.Get Substring    ${campaign_end_day.strip()}    0      1


    IF    '${campaign_end_day_part}' == '0'
         ${campaign_end_day} =      String.Get Substring    ${campaign_end_day.strip()}    1      2
    END

    Log    ${campaign_end_day.strip()}
    FOR    ${row_num}    IN RANGE    1    ${rows_new+1}

        ${curr_row_element_path} =   Catenate    ${calendar_body_rows_element_path}   [${row_num}]
        ${col_element_path} =   Catenate    ${curr_row_element_path}       /td
        ${cols}=    SeleniumLibrary.Get Element Count    ${col_element_path}  #Get the number of columns from the current row

        FOR    ${col_num}    IN RANGE    1    ${cols+1}       #Loop through columns

            ${curr_col_element_path} =   Catenate    ${col_element_path}   [${col_num}]    #build element path for the current column
            ${campaign_date}=     SeleniumLibrary.Get Text    ${curr_col_element_path}        #Read the campaign(s) start date (day)
            Log    ${campaign_date}
            #If the current date is the required one for start date then select it
             IF    '${campaign_end_day.strip()}' == '${campaign_date.strip()}'
                Set Focus To Element        ${curr_col_element_path}

                ${row_num}=  Set Variable  0
                ${bool_end_date_selected}=    Set Variable    ${True}
                Click Element    ${curr_col_element_path}
                Log     Campaign End Date Selected!
                Capture page screenshot  EndDateSelection.png
                Exit For Loop
             END

        END
        Run Keyword If  ${bool_end_date_selected} == ${True}  Exit For Loop
    END

        Run Keyword If  ${bool_end_date_selected} == ${False}  Fail   Campaign end date: '${campaign_end_day.strip()}' was not selected!!



