*** Settings ***
#Author Name               : Thabo
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite


Library             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Add_BinTypes_Page.robot
Resource            ../../../../keywords/front_end/View_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Add a Bin Type
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_TYPE_NAME}' == '${EMPTY}' or '${BIN_TYPE_NAME}' == ''
         ${random_word}=     Generate random word
         ${BIN_TYPE_NAME}=   Set Variable     ${random_word}
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user must not be able to add, edit or delete a Bin Type without Admin Access
    And The user navigates to 'Add' Bin Type tab
    And The User populates the Bin Type details and save the Bin Tpe to the database    ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}
    And The created Bin Type must be displayed on the 'View' page                       ${BIN_TYPE_NAME}
    Then The created bin type should exist in the database                               ${BIN_TYPE_NAME}    ${BIN_TYPE_DESCRIPTION}


| *** Test Cases ***                                                                                   |        *DOCUMENTATION*    		  |         *BASE_URL*                  |         *BIN_TYPE_NAME*          |         *BIN_TYPE_DESCRIPTION*        |
| Admin_Redirect to View Menu After Successful BINtype Creation                | Add a Bin Type   | Add new Bin Type to Bin Tables.   |           ${EMPTY}                  |            ${EMPTY}              |         Bin type added by Thabo       |
