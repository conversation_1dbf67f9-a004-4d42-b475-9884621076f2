*** Settings ***
# Author Name               : Thabo
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/GetBinTypesById_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID
${TEST_CASE_ID}             RAC29a-TC-1344


*** Keywords ***
Get all Bin Types by Id
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}  ${BIN_TYPE_ID}    ${BIN_TYPE_NAME}      ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    IF    '${BIN_TYPE_ID}' == '${EMPTY}'
         ${bintypeid}=    Get the BinType id using Bin Type Name  ${BIN_TYPE_NAME}
    END
    Set Global Variable    ${GLOBAL_BIN_TYPE_ID}    ${BIN_TYPE_ID}

    Given The User sends a request to get a Bin Type by Id   ${BASE_URL}      ${BIN_TYPE_ID}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    Then The User verifies that the Bin Type returned by the service exists on the database

| *** Test Cases ***                                                                                                                                           |    *DOCUMENTATION*    	   |    *BASE_URL*       | *BIN_TYPE_ID*      |   *BIN_TYPE_NAME*           |   *EXPECTED_STATUS_CODE*    |
| Verify the GetBINtypeById API response when retrieving details of a specific BIN type by its ID (Bin Type View)            | Get all Bin Types by Id         | Get all Bin Types by Id   |                     | ${EMPTY}           |       Domestic              |   200                       |
