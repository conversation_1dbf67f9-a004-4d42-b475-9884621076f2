<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T14:32:14.575385" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role">
<suite id="s1-s1" name="RAC29a TC 117 Approve Campaign" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_117_Approve_Campaign.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T14:32:15.986889" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T14:32:15.985891" elapsed="0.000998"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T14:32:15.986889" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T14:32:15.986889" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T14:32:15.987885" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T14:32:15.986889" elapsed="0.000996"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T14:32:15.987885" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T14:32:15.987885" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T14:32:15.987885" level="INFO">Environment variable 'BASE_URL' set to value 'APC_UAT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T14:32:15.987885" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T14:32:15.984888" elapsed="0.002997"/>
</kw>
<test id="s1-s1-t1" name="RAC29a_TC_117_FFT_Approval_Approve_Campaign" line="43">
<kw name="Validating the Approval function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T14:32:15.988885" level="INFO">Set test documentation to:
Approve Campaign</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T14:32:15.988885" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T14:32:16.088330" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T14:32:15.988885" elapsed="0.099445"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T14:32:16.088330" elapsed="0.001000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T14:32:16.089330" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T14:32:16.090349" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T14:32:16.090349" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T14:32:16.090349" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T14:32:16.090349" elapsed="0.001002"/>
</branch>
<status status="PASS" start="2024-10-29T14:32:16.090349" elapsed="0.001002"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T14:32:16.091351" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T14:32:16.091351" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T14:32:16.091351" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T14:32:16.124274" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T14:32:16.562074" level="INFO">${rc_code} = 0</msg>
<msg time="2024-10-29T14:32:16.562074" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 30516 has been terminated.
SUCCESS: The process "chrome.exe" with PID 32444 has been terminated.
SUCCESS: The process "chrome.exe" with PID 22568 has been te...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T14:32:16.091351" elapsed="0.470723"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T14:32:16.562074" elapsed="0.001006"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T14:32:16.090349" elapsed="0.472731"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T14:32:16.563594" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T14:32:16.563594" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T14:32:16.564604" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T14:32:16.564604" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T14:32:16.564604" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T14:32:16.564604" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
