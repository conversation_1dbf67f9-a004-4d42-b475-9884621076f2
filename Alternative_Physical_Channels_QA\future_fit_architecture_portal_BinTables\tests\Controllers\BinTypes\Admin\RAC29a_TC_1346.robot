*** Settings ***
# Author Name               : Thabo
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

Library             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/BinTypeAdd_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot



*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
Add Bin Type and Verify Response
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}    ${BIN_TYPE_NAME}    ${BIN_TYPE_DETAILS}    ${EXPECTED_STATUS_CODE}
    IF    '${BIN_TYPE_NAME}' == '${EMPTY}' or '${BIN_TYPE_NAME}' == ''
         ${random_word}=     Generate random word
         ${BIN_TYPE_NAME}=   Set Variable     ${random_word}
    END

    Given The User Populates the Add Bin Type JSON payload with   ${BIN_TYPE_NAME}    ${BIN_TYPE_DETAILS}
    When The User sends the Add Bin Type API Request                ${BASE_URL}
    And The service returns an expected status code                 ${EXPECTED_STATUS_CODE}
    Then The created Bin Type must exist in the database            ${BIN_TYPE_NAME}    ${BIN_TYPE_DETAILS}

| *** Test Cases ***                                                                                                                                                                   |             *DOCUMENTATION*        |    *BASE_URL*            | *BIN_TYPE_NAME*         | *BIN_TYPE_DETAILS*                  |    *EXPECTED_STATUS_CODE*    |
| Verify that the Add API successfully creates a new BIN type when both the Name and Description are provided correctly (Add Bin Type)            | Add Bin Type and Verify Response   | Add Bin Type and verify response   |                          |  ${EMPTY}               |  Generated by the automated test    |       201                    |