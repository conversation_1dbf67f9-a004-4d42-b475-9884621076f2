*** Settings ***
#Author Name               : T<PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS_HEALTHCHECK     HEALTHCHECK_STATUS   Login
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS
Documentation                                       Add new User to VMS

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/common_keywords.robot
Resource                                            ../../keywords/VMSPage/UserManagement.robot

*Variables*


*** Keywords ***
VMS - Verify User Management details against VMS database
    [Arguments]  ${DOCUMENTATION}       ${TEST_ENVIRONMENT}     ${COLUMN_TO_READ}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}
    When The user navigates to Admin - User Management
    And The user reads all values from a certain column of User Management Page, then value read must correspond to the database        ${COLUMN_TO_READ}



| *Test Case*                                                                                                                                                                                                                 |   *DOCUMENTATION*                                                   | *TEST_ENVIRONMENT*  |  *COLUMN_TO_READ*  |
| Validate Search-UserName column- User Management          | VMS - Verify User Management details against VMS database | Verify the details of 'UserName' column agains the Database data    |    VMS_UAT          |    UserName        |
