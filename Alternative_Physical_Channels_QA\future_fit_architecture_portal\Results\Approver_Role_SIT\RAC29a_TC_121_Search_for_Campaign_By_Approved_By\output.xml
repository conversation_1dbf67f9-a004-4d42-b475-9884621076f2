<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.12.1 on win32)" generated="2024-10-29T12:04:21.754076" rpa="false" schemaversion="5">
<suite id="s1" name="Future Fit Portal" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_121_Search_for_Campaign_By_Approved_By.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:22.874918" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:04:22.874918" elapsed="0.001000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:22.875918" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\data\Approver_Role'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:04:22.875918" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:22.875918" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:04:22.875918" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:22.876918" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:04:22.876918" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:22.876918" level="INFO">Environment variable 'BASE_URL' set to value 'APC_SIT'.</msg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-29T12:04:22.876918" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:04:22.874918" elapsed="0.002000"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_121_FFT_Approval_Search_for_Campaign_By_Approved_By" line="38">
<kw name="Validating the Search function on Campaign Approvals">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-29T12:04:22.877932" level="INFO">Set test documentation to:
Search by Approved By user</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-29T12:04:22.877932" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:04:22.979447" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:04:22.878930" elapsed="0.100517"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:04:22.979447" elapsed="0.000999"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:04:22.980446" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:04:22.983690" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:04:22.983690" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:04:22.984197" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:04:22.984197" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:04:22.983690" elapsed="0.000507"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-29T12:04:22.984197" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-29T12:04:22.984197" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:04:22.984197" elapsed="0.001012"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-29T12:04:23.022621" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-29T12:04:23.409774" level="INFO">${rc_code} = 0</msg>
<msg time="2024-10-29T12:04:23.409774" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 26312 has been terminated.
SUCCESS: The process "chrome.exe" with PID 21016 has been terminated.
SUCCESS: The process "chrome.exe" with PID 32632 has been te...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2024-10-29T12:04:22.985209" elapsed="0.424565"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-29T12:04:23.409774" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-29T12:04:22.982446" elapsed="0.427328"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:23.410866" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-29T12:04:23.410866" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:04:23.410866" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:04:23.410866" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-29T12:04:23.410866" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-29T12:04:23.410866" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:04:23.411920" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:04:23.411920" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2024-10-29T12:04:23.411920" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:04:23.412937" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:04:23.411920" elapsed="0.001017"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2024-10-29T12:04:23.412937" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:04:23.412937" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2024-10-29T12:04:23.411920" elapsed="0.001017"/>
</if>
<status status="NOT RUN" start="2024-10-29T12:04:23.411920" elapsed="0.001017"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-29T12:04:23.412937" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-29T12:04:23.412937" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:04:23.413919" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002283B144080&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:04:23.413919" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-29T12:04:23.413919" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2024-10-29T12:04:23.413919" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-29T12:04:23.413919" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-29T12:04:23.413919" elapsed="0.001004"/>
</if>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-29T12:04:23.414923" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-29T12:04:23.414923" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:04:23.414923" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-29T12:04:23.415919" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:04:23.415919" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:04:23.415919" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:04:23.415919" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:04:23.415919" elapsed="0.001507"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2024-10-29T12:04:23.417590" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-29T12:04:23.417590" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-29T12:04:23.417590" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2024-10-29T12:04:23.417590" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<status status="PASS" start="2024-10-29T12:04:22.877932" elapsed="34.052931"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" owner="Navigation">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:04:56.930863" elapsed="0.017028"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:04:56.953294" level="INFO">${Admin_Expansion} = &lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.151")&gt;</msg>
<var>${Admin_Expansion}</var>
<arg>xpath=//*[@id="mat-expansion-panel-header-1"]/span[2]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:04:56.947891" elapsed="0.005403"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:04:56.954295" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.151")&gt;'.</msg>
<arg>${Admin_Expansion}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:04:56.953294" elapsed="0.027426"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:04:56.980720" elapsed="0.009999"/>
</kw>
<status status="PASS" start="2024-10-29T12:04:56.980720" elapsed="0.009999"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:04:56.997719" level="INFO">${Approvals_Button} = &lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.152")&gt;</msg>
<var>${Approvals_Button}</var>
<arg>xpath=//*[@id="cdk-accordion-child-1"]/div/mat-nav-list/mat-list-item/span</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:04:56.990719" elapsed="0.007000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:01.997918" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:04:56.997719" elapsed="5.000199"/>
</kw>
<kw name="Wait Until Element Is Enabled" owner="SeleniumLibrary">
<arg>${Approvals_Button}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" start="2024-10-29T12:05:01.997918" elapsed="0.020818"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:02.018736" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.152")&gt;'.</msg>
<arg>${Approvals_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:02.018736" elapsed="0.074391"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:05:02.093127" elapsed="0.439724"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:02.093127" elapsed="0.439724"/>
</kw>
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:05:02.533096" elapsed="0.006014"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:02.533096" elapsed="0.006014"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:02.551115" level="INFO">Current page contains text 'Approvals'.</msg>
<arg>Approvals</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2024-10-29T12:05:02.539110" elapsed="0.012005"/>
</kw>
<status status="PASS" start="2024-10-29T12:04:56.930863" elapsed="5.620252"/>
</kw>
<kw name="And The user inputs an Approved By user on the Search field" owner="Approvals">
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${dropdown_xpath}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:02.552114" elapsed="0.014548"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:02.566662" level="INFO">Clicking element '//mat-select[@id='mat-select-0']'.</msg>
<arg>${dropdown_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:02.566662" elapsed="0.031402"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:02.599086" level="INFO">Clicking element '//span[contains(text(), '100')]'.</msg>
<arg>${option_100_xpath}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:02.598064" elapsed="0.043629"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:07.641969" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:05:02.641693" elapsed="5.000276"/>
</kw>
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${APPROVED_BY_XPATH}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:07.641969" elapsed="0.012680"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:07.667513" level="INFO">${Approved_By_User} = Yaash Ramsahar (ZA)</msg>
<var>${Approved_By_User}</var>
<arg>${APPROVED_BY_XPATH}</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:07.654649" elapsed="0.012864"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:05:07.668519" level="INFO">Campaign Name retrieved: Yaash Ramsahar (ZA)</msg>
<arg>Campaign Name retrieved: ${Approved_By_User}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:05:07.667513" elapsed="0.001006"/>
</kw>
<kw name="Get WebElement" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:07.675718" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.158")&gt;</msg>
<var>${Search_Bar_XPATH}</var>
<arg>xpath=//input[@type='text' and @placeholder='Search Campaign' and contains(@class, 'search-input')]</arg>
<doc>Returns the first WebElement matching the given ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:07.668519" elapsed="0.007199"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:09.676429" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:05:07.675718" elapsed="2.000711"/>
</kw>
<kw name="Set Focus To Element" owner="SeleniumLibrary">
<arg>${Search_Bar_XPATH}</arg>
<doc>Sets the focus to the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:09.677004" elapsed="0.011196"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:12.688421" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:05:09.688200" elapsed="3.000221"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:12.689446" level="INFO">Clicking element '&lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.158")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:12.688421" elapsed="0.027146"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:12.715567" level="INFO">Typing text 'Yaash Ramsahar (ZA)' into text field '&lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.158")&gt;'.</msg>
<arg>${Search_Bar_XPATH}</arg>
<arg>${Approved_By_User}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:12.715567" elapsed="0.053133"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:14.770105" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:05:12.768700" elapsed="2.001405"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:05:14.771124" level="INFO">${Approved_By_User} = Yaash Ramsahar (ZA)</msg>
<arg>${Approved_By_User}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:05:14.770105" elapsed="0.001019"/>
</kw>
<kw name="Set Suite Variable" owner="BuiltIn">
<msg time="2024-10-29T12:05:14.771124" level="INFO">${Search_Bar_XPATH} = &lt;selenium.webdriver.remote.webelement.WebElement (session="f9aad9e156b760858fdc01ff41634a38", element="f.4653019FCA065FBB8082037EA7CAFB08.d.FF898A7420B832AD7DF76A368288F222.e.158")&gt;</msg>
<arg>${Search_Bar_XPATH}</arg>
<doc>Makes a variable available everywhere within the scope of the current suite.</doc>
<status status="PASS" start="2024-10-29T12:05:14.771124" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:02.551115" elapsed="12.221005"/>
</kw>
<kw name="Then The user verifies the search results returned by the Approved By user" owner="Approvals">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${SEARCH_RESULT_XPATH}</arg>
<arg>timeout=10s</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2024-10-29T12:05:14.772120" elapsed="0.013611"/>
</kw>
<kw name="Get Text" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:14.796921" level="INFO">${search_results} = Yaash Ramsahar (ZA)</msg>
<var>${search_results}</var>
<arg>xpath=//tr[contains(@class, 'mat-row')]//td[contains(@class, 'mat-column-ApprovedBy') and normalize-space(text())='${Approved_By_User}']</arg>
<doc>Returns the text value of the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:14.785731" elapsed="0.011190"/>
</kw>
<kw name="Should Contain" owner="BuiltIn">
<arg>${search_results}</arg>
<arg>${Approved_By_User}</arg>
<doc>Fails if ``container`` does not contain ``item`` one or more times.</doc>
<status status="PASS" start="2024-10-29T12:05:14.796921" elapsed="0.000000"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:14.796921" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:05:14.801031" level="INFO">${result_count} = 1</msg>
<var>${result_count}</var>
<arg>${SEARCH_RESULT_XPATH}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:14.796921" elapsed="0.004110"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${result_count} &gt; 0</arg>
<arg>msg=No search results found!</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2024-10-29T12:05:14.801031" elapsed="0.001016"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-29T12:05:14.802047" level="INFO">----Search completed successfully using campaign name: Yaash Ramsahar (ZA).----</msg>
<arg>----Search completed successfully using campaign name: ${Approved_By_User}.----</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-29T12:05:14.802047" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>----Search completed successfully using campaign name: ${Approved_By_User}.----</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-29T12:05:14.802047" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:14.772120" elapsed="0.029927"/>
</kw>
<arg>Search by Approved By user</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_SIT</arg>
<status status="PASS" start="2024-10-29T12:04:22.877932" elapsed="51.925023"/>
</kw>
<kw name="User logs out" owner="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" owner="GenericMethods">
<kw name="Wait Until Element Is Not Visible" owner="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" start="2024-10-29T12:05:14.803929" elapsed="0.006387"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:14.802955" elapsed="0.007361"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Logout" owner="Logout">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:14.811322" level="INFO">Clicking element 'xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]'.</msg>
<arg>xpath=/html/body/app-root/app-top-nav/mat-toolbar/div/div/button/span[1]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2024-10-29T12:05:14.810316" elapsed="0.033743"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:17.845600" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:05:14.844059" elapsed="3.001541"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2024-10-29T12:05:17.845600" level="INFO">Clicking element 'xpath=/html/body/div[2]/div[2]/div/div/div/button'.</msg>
<msg time="2024-10-29T12:05:17.904545" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2024-10-29T12:05:17.904545" level="FAIL">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</msg>
<arg>xpath=/html/body/div[2]/div[2]/div/div/div/button</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="FAIL" start="2024-10-29T12:05:17.845600" elapsed="0.062005">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-29T12:05:19.908219" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-29T12:05:17.907605" elapsed="2.000614"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2024-10-29T12:05:19.908842" elapsed="2.214619"/>
</kw>
<status status="FAIL" start="2024-10-29T12:05:14.810316" elapsed="7.313145">Element with locator 'xpath=/html/body/div[2]/div[2]/div/div/div/button' not found.</status>
</kw>
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2024-10-29T12:05:14.810316" elapsed="7.313145"/>
</kw>
<status status="PASS" start="2024-10-29T12:05:14.802955" elapsed="7.320506"/>
</kw>
<doc>Search by Approved By user</doc>
<tag>FFA_HEALTHCHECK</tag>
<status status="PASS" start="2024-10-29T12:04:22.876918" elapsed="59.247538"/>
</test>
<doc>Testing the Search Funtion on Campaign Approvals: Search by Approved By user</doc>
<status status="PASS" start="2024-10-29T12:04:22.183830" elapsed="59.941621"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFA_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future Fit Portal">Future Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-29T12:04:21.746918" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_121_Search_for_Campaign_By_Approved_By.robot' on line 24: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-29T12:04:22.845917" level="WARN">Error in file 'C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\atm_marketing\Approvals.robot' on line 128: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2024-10-29T12:04:41.819730" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:04:51.832831" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:04:56.851916" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2024-10-29T12:05:14.796921" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
