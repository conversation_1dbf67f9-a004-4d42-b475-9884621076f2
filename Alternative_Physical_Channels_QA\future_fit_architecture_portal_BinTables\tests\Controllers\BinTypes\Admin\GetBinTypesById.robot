*** Settings ***
# Author Name               : <PERSON>hab<PERSON>
# Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/controllers/GetBinTypesById_Keyword.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Bin Type by ID


*** Keywords ***
Get all Bin Types by Id
    [Arguments]    ${DOCUMENTATION}    ${BASE_URL}  ${BIN_TYPE_ID}    ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}
    Set Global Variable    ${GLOBAL_BIN_TYPE_ID}    ${BIN_TYPE_ID}

    # Step 1: Send DELETE request to the API
    Given The User sends a request to get a Bin Type by Id   ${BASE_URL}      ${BIN_TYPE_ID}

    # Step 2: Check for expected status code
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}

    # Step 3: Verify that the Bin Type returned by the service exists on the database
    Then The User verifies that the Bin Type returned by the service exists on the database

| *** Test Cases ***                                                   |    *DOCUMENTATION*    	   |    *BASE_URL*       | *BIN_TYPE_ID*                         |   *EXPECTED_STATUS_CODE*    |
| Get all Bin Types by Id            | Get all Bin Types by Id         | Get all Bin Types by Id   |                     | 03eae50f-0d4d-4275-bb9e-0d67062775e0  |       200                   |
