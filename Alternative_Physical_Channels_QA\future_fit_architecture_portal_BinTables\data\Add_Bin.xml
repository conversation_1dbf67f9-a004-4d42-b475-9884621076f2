<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20250331 14:13:04.051">
   <suite name="BIN_TABLES_HEALTHCHECK REGRESSION" id="s1" source="C:\development\APC_Automation_2\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\tests\Front_End\Bin_Table_Landing_Page\Capturer\RAC29a_TC_803.robot">
      <test name="Admin_Verify User Details Display Correct Information and Role in the Top Right Corner" id="s1-s1-s1-t1">
         <tags>
            <tag>Admin_Verify User Details Display Correct Information and Role in the Top Right Corner</tag>
         </tags>
         <status endtime="20250331 14:13:46.793" critical="yes" status="PASS" starttime="20250331 14:13:05.133"/>
      </test>
      <test name="Admin_Verify Home Icon Navigates to the Landing Page from Bin Table" id="s1-s1-s2-t1">
         <tags>
            <tag>Admin_Verify Home Icon Navigates to the Landing Page from Bin Table</tag>
         </tags>
         <status endtime="20250331 14:14:26.380" critical="yes" status="PASS" starttime="20250331 14:13:46.864"/>
      </test>
      <test name="Admin_Verify BINtypes Panel is Accessible" id="s1-s1-s3-t1">
         <tags>
            <tag>Admin_Verify BINtypes Panel is Accessible</tag>
         </tags>
         <status endtime="20250331 14:15:06.083" critical="yes" status="PASS" starttime="20250331 14:14:26.447"/>
      </test>
      <test name="Admin_Verify BinTable Landing Page Loads Correctly Upon Selection" id="s1-s1-s4-t1">
         <tags>
            <tag>Admin_Verify BinTable Landing Page Loads Correctly Upon Selection</tag>
         </tags>
         <status endtime="20250331 14:15:39.226" critical="yes" status="PASS" starttime="20250331 14:15:06.148"/>
      </test>
      <test name="Admin_Verify Bin Panel is Accessible" id="s1-s1-s5-t1">
         <tags>
            <tag>Admin_Verify Bin Panel is Accessible</tag>
         </tags>
         <status endtime="20250331 14:16:22.158" critical="yes" status="PASS" starttime="20250331 14:15:39.295"/>
      </test>
      <test name="Admin_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing" id="s1-s1-s6-t1">
         <tags>
            <tag>Admin_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing</tag>
         </tags>
         <status endtime="20250331 14:16:59.608" critical="yes" status="PASS" starttime="20250331 14:16:22.221"/>
      </test>
      <test name="Approver_Verify User Details Display Correct Information and Role in the Top Right Corner" id="s1-s2-s1-t1">
         <tags>
            <tag>Approver_Verify User Details Display Correct Information and Role in the Top Right Corner</tag>
         </tags>
         <status endtime="20250331 14:17:40.576" critical="yes" status="PASS" starttime="20250331 14:16:59.680"/>
      </test>
      <test name="Approver_Verify Home Icon Navigates to the Landing Page from Bin Table" id="s1-s2-s2-t1">
         <tags>
            <tag>Approver_Verify Home Icon Navigates to the Landing Page from Bin Table</tag>
         </tags>
         <status endtime="20250331 14:18:20.859" critical="yes" status="PASS" starttime="20250331 14:17:40.654"/>
      </test>
      <test name="Approver_Verify BINtypes Panel is Accessible" id="s1-s2-s3-t1">
         <tags>
            <tag>Approver_Verify BINtypes Panel is Accessible</tag>
         </tags>
         <status endtime="20250331 14:19:00.509" critical="yes" status="PASS" starttime="20250331 14:18:20.923"/>
      </test>
      <test name="Approver_Verify BinTable Landing Page Loads Correctly Upon Selection" id="s1-s2-s4-t1">
         <tags>
            <tag>Approver_Verify BinTable Landing Page Loads Correctly Upon Selection</tag>
         </tags>
         <status endtime="20250331 14:19:38.870" critical="yes" status="PASS" starttime="20250331 14:19:00.572"/>
      </test>
      <test name="Approver_Verify Bin Panel is Accessible" id="s1-s2-s5-t1">
         <tags>
            <tag>Approver_Verify Bin Panel is Accessible</tag>
         </tags>
         <status endtime="20250331 14:20:21.363" critical="yes" status="PASS" starttime="20250331 14:19:38.937"/>
      </test>
      <test name="Approver_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing" id="s1-s2-s6-t1">
         <tags>
            <tag>Approver_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing</tag>
         </tags>
         <status endtime="20250331 14:20:59.304" critical="yes" status="PASS" starttime="20250331 14:20:21.429"/>
      </test>
      <test name="Capturer_Verify BinTable Landing Page Loads Correctly Upon Selection" id="s1-s3-s1-t1">
         <tags>
            <tag>Capturer_Verify BinTable Landing Page Loads Correctly Upon Selection</tag>
         </tags>
         <status endtime="20250331 14:21:37.717" critical="yes" status="PASS" starttime="20250331 14:20:59.397"/>
      </test>
      <test name="Capturer_Verify Bin Panel is Accessible" id="s1-s3-s2-t1">
         <tags>
            <tag>Capturer_Verify Bin Panel is Accessible</tag>
         </tags>
         <status endtime="20250331 14:22:22.044" critical="yes" status="PASS" starttime="20250331 14:21:37.783"/>
      </test>
      <test name="Capturer_Verify BINtypes Panel is Accessible" id="s1-s3-s3-t1">
         <tags>
            <tag>Capturer_Verify BINtypes Panel is Accessible</tag>
         </tags>
         <status endtime="20250331 14:22:58.995" critical="yes" status="PASS" starttime="20250331 14:22:22.109"/>
      </test>
      <test name="Capturer_Verify User Details Display Correct Information and Role in the Top Right Corner" id="s1-s3-s4-t1">
         <tags>
            <tag>Capturer_Verify User Details Display Correct Information and Role in the Top Right Corner</tag>
         </tags>
         <status endtime="20250331 14:23:40.077" critical="yes" status="PASS" starttime="20250331 14:22:59.056"/>
      </test>
      <test name="Capturer_Verify Home Icon Navigates to the Landing Page from Bin Table" id="s1-s3-s5-t1">
         <tags>
            <tag>Capturer_Verify Home Icon Navigates to the Landing Page from Bin Table</tag>
         </tags>
         <status endtime="20250331 14:24:20.642" critical="yes" status="PASS" starttime="20250331 14:23:40.142"/>
      </test>
      <test name="Capturer_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing" id="s1-s3-s6-t1">
         <tags>
            <tag>Capturer_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing</tag>
         </tags>
         <status endtime="20250331 14:24:57.733" critical="yes" status="PASS" starttime="20250331 14:24:20.703"/>
      </test>
      <status endtime="20250331 14:24:57.736" status="PASS" starttime="20250331 14:13:04.051"/>
   </suite>
   <statistics>
      <total>
         <stat pass="18" fail="0">Critical Tests</stat>
         <stat pass="18" fail="0">All Tests</stat>
      </total>
      <tag>
         <stat pass="1" fail="0">Admin_Verify User Details Display Correct Information and Role in the Top Right Corner</stat>
         <stat pass="1" fail="0">Admin_Verify Home Icon Navigates to the Landing Page from Bin Table</stat>
         <stat pass="1" fail="0">Admin_Verify BINtypes Panel is Accessible</stat>
         <stat pass="1" fail="0">Admin_Verify BinTable Landing Page Loads Correctly Upon Selection</stat>
         <stat pass="1" fail="0">Admin_Verify Bin Panel is Accessible</stat>
         <stat pass="1" fail="0">Admin_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing</stat>
         <stat pass="1" fail="0">Approver_Verify User Details Display Correct Information and Role in the Top Right Corner</stat>
         <stat pass="1" fail="0">Approver_Verify Home Icon Navigates to the Landing Page from Bin Table</stat>
         <stat pass="1" fail="0">Approver_Verify BINtypes Panel is Accessible</stat>
         <stat pass="1" fail="0">Approver_Verify BinTable Landing Page Loads Correctly Upon Selection</stat>
         <stat pass="1" fail="0">Approver_Verify Bin Panel is Accessible</stat>
         <stat pass="1" fail="0">Approver_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing</stat>
         <stat pass="1" fail="0">Capturer_Verify BinTable Landing Page Loads Correctly Upon Selection</stat>
         <stat pass="1" fail="0">Capturer_Verify Bin Panel is Accessible</stat>
         <stat pass="1" fail="0">Capturer_Verify BINtypes Panel is Accessible</stat>
         <stat pass="1" fail="0">Capturer_Verify User Details Display Correct Information and Role in the Top Right Corner</stat>
         <stat pass="1" fail="0">Capturer_Verify Home Icon Navigates to the Landing Page from Bin Table</stat>
         <stat pass="1" fail="0">Capturer_Verify &quot;APC Portal&quot; Static Field Appears Correctly in the Top Navigation on Bin Table landing</stat>
      </tag>
      <suite>
         <stat name="BIN_TABLES_HEALTHCHECK REGRESSION" pass="18" fail="0" id="s1">BIN_TABLES_HEALTHCHECK REGRESSION</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
