*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime
Library                                          ../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/front_end/Landing_Page.robot


*** Variables ***
${BIN_TYPES_BTN}                                    xpath=//p[contains(text(),'BIN TYPES')]
${BIN_TYPE_NAME}                                    xpath=//span[@class='bintype-text']
${LINKED_BINS_TABLE}                                xpath=//table[contains(@class, 'mat-table') and contains(@class, 'cdk-table') and contains(@class, 'mat-sort')]
${PAGINATOR_RANGE_LABEL}                            xpath=//div[@class='mat-paginator-range-label']
${PAGINATOR_ICON}                                   xpath=//button[contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-paginator-navigation-nex') and contains(@class, 'mat-icon-button')]
${FILTER_LIST}                                      xpath=//mat-select[@role='combobox']
${FILTER_LIST_5_ITEMS}                              xpath=//span[contains(text(),' 5 ')]
${FILTER_LIST_10_ITEMS}                             xpath=//span[contains(text(),' 10 ')]
${FILTER_LIST_25_ITEMS}                             xpath=//span[contains(text(),' 25 ')]
${FILTER_LIST_100_ITEMS}                            xpath=//span[contains(text(),' 100 ')]
${VIEW_BIN_TYPES_BTN}                               xpath=//span[contains(text(),'View')]
${SEARCH_INPUT}                                     xpath=//input[contains(@class, 'search-input') and contains(@class, 'ng-pristine') and contains(@class, 'ng-valid') and contains(@class, 'ng-touched')]
${BIN_NUMBER_SORT_HEADER}                           xpath=//div[contains(@class, 'mat-sort-header-content') and contains(text(),' BIN Number ')]
${ACTION_DATE_SORT_HEADER}                          xpath=//div[contains(@class, 'mat-sort-header-content') and contains(text(),' Action Date ')]
${ADD_BIN_TYPE_BTN}                                 xpath=//span[contains(text(),'Add')]
${EDIT_DELETE_BIN_TYPE_BTN}                         xpath=//span[contains(text(),'Edit / Delete')]
${EXPORT_BIN_TYPES_BTN}                             xpath=//span[contains(text(),'Export')]





*** Keywords ***
The user gets the details of a soft-deleted bin type from the database

    #Get the Bin Type from the database
    ${db_bin_type_results}=     Get a Bin Type from the database using 'isDeleted' status    0

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no soft-deleted Bin Types in the database.

    IF    not ${db_results_contain_data}
        Set Global Variable    ${SOFT_DELETED_BIN_TYPE_DETAILS}         ${EMPTY}
    ELSE
        Set Global Variable    ${SOFT_DELETED_BIN_TYPE_DETAILS}         ${db_bin_type_results}
    END


The user gets the details of a soft-deleted bins for a bin type from the database
    [Arguments]     ${BIN_TYPE_TO_VERIFY}

     ${string_to_validate}=      Remove Quotes       ${BIN_TYPE_TO_VERIFY}

    #This is the boolean value that checks if the bin type name is provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${string_to_validate}' == '${EMPTY}'     ${False}
         ...       '${string_to_validate}' == ''             ${False}
         ...       '${string_to_validate}' != '${EMPTY}'     ${True}
         ...       '${string_to_validate}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type!

    #Get the Bin Type from the database
    ${db_bin_type_results}=     Get all in-active Bins associated with the Bin Type from the Database using the Bin Type Name    ${BIN_TYPE_TO_VERIFY}

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_bin_type_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no soft-deleted Bins for Bin Type '${BIN_TYPE_TO_VERIFY}' in the database.

    IF    not ${db_results_contain_data}
        Set Global Variable    ${SOFT_DELETED_BINS_FOR_BIN_TYPE_DETAILS}         ${EMPTY}
    ELSE
        Set Global Variable    ${SOFT_DELETED_BINS_FOR_BIN_TYPE_DETAILS}         ${db_bin_type_results}
    END


The soft-deleted bins previously associated with the bin type must not be displayed on the Front End
    [Arguments]     ${BIN_TYPE_TO_VERIFY}

    IF  "${SOFT_DELETED_BINS_FOR_BIN_TYPE_DETAILS}" == "${EMPTY}"
        RETURN
    END

    ${BIN_TYPE_TO_VERIFY}=      Remove Quotes       ${BIN_TYPE_TO_VERIFY}

    #This is the boolean value that checks if all bin types must be verified
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_VERIFY}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' == ''             ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type!



    #Split the Bin Types names using a comma
    ${bin_types_list}=         Split String    ${BIN_TYPE_TO_VERIFY}    separator=,
    Log  ${bin_types_list}
    # Get all the Bin Types displayed on the View page
    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Run Keyword And Warn On Failure    Fail   There are no Bin Types displayed on the 'View' page. Please check if there are active Bin Types in the DB.

    IF    not ${bin_types_names_are_displayed}
         RETURN
    END

    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${elements_count}=      Get Length    ${bin_types_elements}


    FOR    ${element}    IN    @{bin_types_list}
        ${user_bin_type_found}=     Set Variable    ${False}
        Log    Current Bin Type to verify is: '${element}'
        #Verify if the Bin Type name is displayed on the 'View' Page
        FOR    ${page_element}    IN    @{bin_types_elements}
             Log    Current Bin Type on 'View' page is: '${page_element}'
             ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${page_element}
             Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
             IF    '${bin_type_name_text.strip()}' == '${element.strip()}'
                  ${user_bin_type_found}=     Set Variable    ${True}
                  Exit For Loop
             END
        END
        IF    ${user_bin_type_found}
            Log Many    The Bin Type named: '${element}' was found on the 'View' page, and its linked Bins will be verified.

            Verify that soft-deleted bins are not displayed on the front end     ${page_element}
            ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}
            Run Keyword If    not ${bin_types_names_are_displayed}
            ...    Fail   There are no Bin Types displayed on the 'View' page!
            ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
        ELSE
            Capture Page Screenshot   ${element}_Displayed_FAIL.png
            Run Keyword And Continue On Failure    Fail     The Bin Type named: '${element}' was not found on the 'View' page.
        END
    END

Verify that soft-deleted bins are not displayed on the front end
    [Arguments]     ${BIN_TYPE_ELEMENT}
    #Verify that the current page is BINTYPES

    ${correct_page_displayed}=      Correct Page is displayed    BINTYPES

    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${BIN_TYPE_ELEMENT}

    IF    '${bin_type_name_text}' == 'On-US'
        ${bin_type_name_text}=      Set Variable    On Us
    END

    #Click on the bin type to Verify
    SeleniumLibrary.Click Element    ${BIN_TYPE_ELEMENT}
    Sleep    4s

    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    ${bin_type_name_text}
    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END



    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The linked Bins table is not displayed on the 'View - ${bin_type_name_text}' page.

    #Filter the table to show 100 items
     ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            SeleniumLibrary.Click Element    ${FILTER_LIST}
            Sleep    3s
        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Run Keyword And Continue On Failure    Fail  There are no linked Bins displayed on the screen for Bin Type: '${bin_type_name_text}'.
        Capture Page Screenshot   ${bin_type_name_text}_Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed for '${bin_type_name_text}' Bins!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    ${record_counter}=    Set Variable  0
    WHILE    '${disabled_attribute}' == 'False' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${LINKED_BINS_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${linked_bins_table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END

        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_type_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[2]
           ${bin_type}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
           ${curr_row_bin_action_date_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[3]
           ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
           ${date1_normalized}=        Convert Date    ${bin_action_date.strip()}    date_format=%b %d, %Y

           ${db_row_data}=     Get Row By Column Value From DB results and warn if no row found     ${SOFT_DELETED_BINS_FOR_BIN_TYPE_DETAILS}     'Number'      ${bin_number}

           ${bin_number_is_active_in_the_db}=     Run Keyword And Return Status       Should Be Equal As Strings    None    ${db_row_data}

           Run Keyword If    not ${bin_number_is_active_in_the_db}
           ...    Run Keyword And Continue On Failure    Fail   The in-active Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_name_text}', was displayed on the front end.

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
        ${attr}=                    Get Current Element Attributes    ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
        Log Many    next_page_disabled_status:   '${disabled_attribute}'
        IF    '${disabled_attribute}' != 'True' and '${record_counter}' == '${total_number_of_items_on_page}'
                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN
             ELSE
                IF    '${disabled_attribute}' == 'True'
                         #Click on the 'Next' button
                         SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                         Sleep    4s
                END
        END
    END

    #${view_button_is_displayed}=     Run Keyword And Return Status     GenericMethods.Run Keyword Until Success      Wait Until Element Is Enabled    ${VIEW_BIN_TYPES_BTN}
    ${view_button_is_displayed}=     Wait for Element to be enabled    ${VIEW_BIN_TYPES_BTN}

    #Navigate to the Bin Types View Menu
    IF   ${view_button_is_displayed}
         Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
         SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
         Sleep    4s
    END


The soft-deleted bin types must not be displayed on the 'View' page

    Capture Page Screenshot   BinTypes_Displayed_on_page.png
    # Get all the Bin Types displayed on the View page
    Sleep   4s
    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${number_of_elements_found}=    Get Length     ${bin_types_elements}

     # Ensure the results are not empty
    ${bin_types_names_are_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}

    IF    "${SOFT_DELETED_BIN_TYPE_DETAILS}" == "${EMPTY}"

         Run Keyword And Warn On Failure    Fail    There are no soft-deleted Bin Types available in the database.
         RETURN
    ELSE
        IF    not ${bin_types_names_are_displayed}
             Capture Page Screenshot   BinTypes_Not_Displayed_FAIL.png
             Fail   There are no Bin Types displayed on the 'View Bin Types' page.
        ELSE
             ${DB_TOTAL_INACTIVE_BINS}=     Get Length    ${SOFT_DELETED_BIN_TYPE_DETAILS}
        END
    END

    Log Many       ${number_of_elements_found}

    #Verify that each Bin Type name that is shown on the page exists in the DB and it is active

    FOR    ${element}    IN    @{bin_types_elements}
        ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element}
        Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
        #Get the Bin Type from the database results
        ${bin_type_db_results}=     Get the Bin Type details from the Database using the Bin Type Name      ${bin_type_name_text}
       # Ensure the results are not empty
        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bin_type_db_results}
        Run Keyword If    not ${db_results_contain_data}
        ...     Run Keyword And Continue On Failure    Fail    The Bin Type named: '${bin_type_name_text}' does not exist in the database.

        IF    ${db_results_contain_data}
          ${first_row_results}=             Get From List    ${bin_type_db_results}    0    # Get the first row
          ${bin_type_is_deleted_status}=    Get Column Data By Name       ${first_row_results}       IsDeleted
          ${bin_type_is_deleted}=           Check If One Or Zero    ${bin_type_is_deleted_status}

          IF    ${bin_type_is_deleted}
             Run Keyword And Continue On Failure    Fail    The Bin Type named: '${bin_type_name_text}' is soft-deleted in the database, but it is displayed on the 'View' Bin Types page.
             Capture Page Screenshot   ${bin_type_name_text}_Displayed_FAIL.png
          ELSE
             Log Many   The Bin Type named: '${bin_type_name_text}' is active in the database, and it is also displayed on the 'View' Bin Types page.
          END
        END
    END




The Bin Types displayed on the 'View' page must exist in the database and must be active

    #Get the total number of actibe Bin Types from the database
    ${total_bin_types_db_results}=     Get the count on active Bin Type from the Database

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${total_bin_types_db_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no active Bin Types on the database.


    ${first_row_results}=             Get From List    ${total_bin_types_db_results}    0    # Get the first row
    ${DB_TOTAL_ACTIVE_BINS}=       Get Column Data By Name       ${first_row_results}       total

    # Get all the Bin Types displayed on the View page
    Sleep   4s
    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${number_of_elements_found}=    Get Length     ${bin_types_elements}
     
     # Ensure the results are not empty
    ${bint_types_names_are_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}

    IF    not ${bint_types_names_are_displayed} and '${DB_TOTAL_ACTIVE_BINS}' == '0'
         Capture Page Screenshot   BinTypes_Not_Displayed_INFO.png
         Run Keyword And Warn On Failure    Fail    There are no Bin Types displayed on the 'View Bin Types' page because the database has no active Bin Types.
         RETURN
    ELSE
        IF    not ${bint_types_names_are_displayed} and '${DB_TOTAL_ACTIVE_BINS}' != '0'
             Capture Page Screenshot   BinTypes_Not_Displayed_FAIL.png
             Fail   There are no Bin Types displayed on the 'View Bin Types' page but the database has '${DB_TOTAL_ACTIVE_BINS}' active Bin Types.
        ELSE
             #Verify that the total number on Bin Types displayed on the page is the same
             #as the total number of active Bin Types on the database
             ${total_number_of_displayed_bin_types_is_the_same_as_database_count}=     Run Keyword And Return Status       Should Be Equal As Strings    ${DB_TOTAL_ACTIVE_BINS}    ${number_of_elements_found}

             Run Keyword If    not ${total_number_of_displayed_bin_types_is_the_same_as_database_count}
             ...     Run Keyword And Continue On Failure    Fail    The total number of Bin Types displayed on the front End is '${number_of_elements_found}' while the total number of active Bin Types on the database is '${DB_TOTAL_ACTIVE_BINS}'.


              Run Keyword If    ${total_number_of_displayed_bin_types_is_the_same_as_database_count}
             ...     Log Many    The total number of Bin Types displayed on the front End is '${number_of_elements_found}' while the total number of active Bin Types on the database is '${DB_TOTAL_ACTIVE_BINS}'. Both totals are the same.

        END
    END

    Log Many       ${number_of_elements_found}

    #Verify that each Bin Type name that is shown on the page exists in the DB and it is active

    FOR    ${element}    IN    @{bin_types_elements}
        ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element}
        Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}

        #Get the Bin Type from the database
        ${bin_type_db_results}=     Get the Bin Type details from the Database using the Bin Type Name    ${bin_type_name_text.strip()}

       # Ensure the results are not empty
        ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bin_type_db_results}
        Run Keyword If    not ${db_results_contain_data}
        ...     Run Keyword And Continue On Failure    Fail    The Bin Type named: '${bin_type_name_text}' does not exist in the database.

        IF    ${db_results_contain_data}
          ${first_row_results}=             Get From List    ${bin_type_db_results}    0    # Get the first row
          ${bin_type_is_deleted_status}=    Get Column Data By Name       ${first_row_results}       IsDeleted
          ${bin_type_is_deleted}=           Check If One Or Zero    ${bin_type_is_deleted_status}

          IF    ${bin_type_is_deleted}
             Run Keyword And Continue On Failure    Fail    The Bin Type named: '${bin_type_name_text}' is soft-deleted in the database, but it is displayed on the 'View' Bin Types page.
             Capture Page Screenshot   ${bin_type_name_text}_Displayed_FAIL.png
          ELSE
             Log Many   The Bin Type named: '${bin_type_name_text}' is active in the database, and it is also displayed on the 'View' Bin Types page.
          END
        END

    END


The created Bin Type must be displayed on the 'View' page
    [Arguments]   ${BIN_TYPE_TO_VERIFY}

     ${BIN_TYPE_TO_VERIFY}=      Remove Quotes       ${BIN_TYPE_TO_VERIFY}

    #This is the boolean value that checks if all bin types must be verified
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_VERIFY}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' == ''             ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail   Please provide the name of the Bin type that must be verified!

    # Get all the Bin Types displayed on the View page
    Sleep   4s
    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${number_of_elements_found}=    Get Length     ${bin_types_elements}

     # Ensure the results are not empty
    ${bint_types_names_are_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}

    IF    not ${bint_types_names_are_displayed}
         Capture Page Screenshot   BinTypes_Not_Displayed_INFO.png
         Fail    There are no Bin Types displayed on the 'View Bin Types' page.
    END

    Capture Page Screenshot   BinTypes_Displayed_INFO.png

    Log Many       ${number_of_elements_found}

    #Verify that each Bin Type name that is shown on the page exists in the DB and it is active
    ${user_bin_type_found}=     Set Variable    ${False}
    ${record_counter}=    Set Variable  1
    FOR    ${element}    IN    @{bin_types_elements}
        ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element}

        IF    '${bin_type_name_text.strip()}' == '${BIN_TYPE_TO_VERIFY.strip()}'
             ${user_bin_type_found}=     Set Variable    ${True}
             Log Many    The Bin Type named: '${BIN_TYPE_TO_VERIFY.strip()}' was displayed on the 'View Bin Types' page.
             Exit For Loop
        END
        ${record_counter}=    Evaluate    ${record_counter} + 1
    END

    Run Keyword If    not ${user_bin_type_found}
    ...  Fail   The Bin Type named: '${BIN_TYPE_TO_VERIFY.strip()}' was not displayed on the 'View Bin Types' page.


The bins linked to the Bin Type(s) must match with the database data
    [Arguments]     ${BIN_TYPE_TO_VERIFY}
    ${BIN_TYPE_TO_VERIFY}=      Remove Quotes       ${BIN_TYPE_TO_VERIFY}

    #This is the boolean value that checks if all bin types must be verified
    ${verify_all_bin_types}=        Set Variable If
         ...       '${BIN_TYPE_TO_VERIFY}' == '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' == ''             ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' != '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' != ''             ${False}

    Log  ${verify_all_bin_types}

    #Split the Bin Types names using a comma
    ${bin_types_list}=         Split String    ${BIN_TYPE_TO_VERIFY}    separator=,
    Log  ${bin_types_list}
    # Get all the Bin Types displayed on the View page
    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Run Keyword And Warn On Failure    Fail   There are no Bin Types displayed on the 'View' page. Please check if there are active Bin Types in the DB.

    IF    not ${bin_types_names_are_displayed}
         RETURN
    END

    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${elements_count}=      Get Length    ${bin_types_elements}
    
    #To be run if all bin types displayed on the page must be verified against the DB
    IF    ${verify_all_bin_types}

         FOR    ${index}    IN RANGE    0    ${elements_count}
            ${element}=    Get From List    ${bin_types_elements}    ${index}
            Log    Element ${index}: ${element}
            ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element}
            Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
            Verify the bins linked to the Bin Type(s) against the database data     ${element}

            ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}
            Run Keyword If    not ${bin_types_names_are_displayed}
            ...    Fail   There are no Bin Types displayed on the 'View' page!
            ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}

        END
    ELSE
        #Verify specific Bin Types
        FOR    ${element}    IN    @{bin_types_list}
            ${user_bin_type_found}=     Set Variable    ${False}
            Log    Current Bin Type to verify is: '${element}'
            #Verify if the Bin Type name is displayed on the 'View' Page
            FOR    ${page_element}    IN    @{bin_types_elements}
                 Log    Current Bin Type on 'View' page is: '${page_element}'
                 ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${page_element}
                 Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
                 IF    '${bin_type_name_text.strip()}' == '${element.strip()}'
                      ${user_bin_type_found}=     Set Variable    ${True}
                      Exit For Loop
                 END
            END
            IF    ${user_bin_type_found}
                Log Many    The Bin Type named: '${element}' was found on the 'View' page, and its linked Bins will be verified.

                Verify the bins linked to the Bin Type(s) against the database data     ${page_element}
                ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}
                Run Keyword If    not ${bin_types_names_are_displayed}
                ...    Fail   There are no Bin Types displayed on the 'View' page!
                ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
            ELSE
                Capture Page Screenshot   ${element}_Displayed_FAIL.png
                Run Keyword And Continue On Failure    Fail     The Bin Type named: '${element}' was not found on the 'View' page.
            END
        END
    END



The user selects a Bin Type on the View Bin Types Page
    [Arguments]     ${BIN_TYPE_TO_VERIFY}

    ${BIN_TYPE_TO_VERIFY}=      Remove Quotes       ${BIN_TYPE_TO_VERIFY}

    #This is the boolean value that checks if the bin type name is provided
    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_TO_VERIFY}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' == ''             ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be selected!

    # Get all the Bin Types displayed on the View page
    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Fail   There are no Bin Types displayed on the 'View' page. Please check if there are active Bin Types in the DB.

    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${elements_count}=      Get Length    ${bin_types_elements}

    #Loop through the Bin Type names displayed on the page
    #and select the one that the user wants selected.
    ${bin_type_selected}=       Set Variable    ${False}

    FOR    ${index}    IN RANGE    0    ${elements_count}
        ${element}=    Get From List    ${bin_types_elements}    ${index}
        ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element}
        Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
        ${bin_type_name_text}=      Convert To Upper Case    ${bin_type_name_text.strip()}
        ${user_bin_type_name_text}=      Convert To Upper Case    ${BIN_TYPE_TO_VERIFY.strip()}

        #If the user's chosen Bin Type is found
        IF    '${bin_type_name_text}' == '${user_bin_type_name_text}'
             SeleniumLibrary.Click Element    ${element}
             ${bin_type_selected}=       Set Variable    ${True}
             Exit For Loop
        END
    END

    Run Keyword If    not ${bin_type_selected}
    ...    Fail    The bin type named: '${BIN_TYPE_TO_VERIFY}' was not found on the Bin Types page!




The selected Bin Type Page which contains linked Bins is be displayed
    [Arguments]     ${BIN_TYPE_TO_VERIFY}

    ${bin_type_name_text}=      Convert To Upper Case    ${BIN_TYPE_TO_VERIFY}

    IF    '${bin_type_name_text}' == 'ON-US'
        ${bin_type_name_text}=      Set Variable    OnUs
    END

    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    ${BIN_TYPE_TO_VERIFY}
    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

The user populates the search text
    [Arguments]     ${SEARCH_TEXT}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SEARCH_TEXT}      msg=Search Criteria cannot be empty!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Run Keyword And Continue On Failure  Fail     Please provide the search Text to be used!

    Log Many    Search data provided :)
    Sleep    3s
    SeleniumLibrary.Input Text    ${SEARCH_INPUT}    ${SEARCH_TEXT}
    Capture Page Screenshot   ${SEARCH_TEXT}_populated.png

The user sorts the Bins results
    [Arguments]     ${SORT_BY}

    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${SORT_BY}      msg=The column name data that must be used to sort is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Run Keyword And Continue On Failure  Fail     Please provide the column name that must be used for sorting BINS!

    Log Many    Sort data provided :)
    Sleep    3s
    ${provided_sort_data}=      Convert To Upper Case    ${SORT_BY}

    IF    '${provided_sort_data}' == 'BIN NUMBER'
         ${btn_is_displayed}=     Wait for Element to be enabled    ${BIN_NUMBER_SORT_HEADER}
         Scroll Element Into View    ${BIN_NUMBER_SORT_HEADER}
         ${btn_is_displayed}=     Wait for Element to be enabled    ${BIN_NUMBER_SORT_HEADER}
         IF    ${btn_is_displayed}
             SeleniumLibrary.Click Element    ${BIN_NUMBER_SORT_HEADER}
         ELSE
             Fail   The BIN_NUMBER_SORT_HEADER button is not displayed.
         END

    ELSE
         IF    '${provided_sort_data}' == 'ACTION DATE'
              ${btn_is_displayed}=     Wait for Element to be enabled    ${ACTION_DATE_SORT_HEADER}
              Scroll Element Into View    ${ACTION_DATE_SORT_HEADER}
              ${btn_is_displayed}=     Wait for Element to be enabled    ${ACTION_DATE_SORT_HEADER}
              IF    ${btn_is_displayed}
                  SeleniumLibrary.Click Element    ${ACTION_DATE_SORT_HEADER}
              ELSE
                  Fail   The ACTION_DATE_SORT_HEADER button is not displayed.
              END

         ELSE
              Fail   The sort button could not be clicked, please make sure that the 'SORT_BY' variable is either 'Bin Number' or 'Action Date'
         END
    END


    Capture Page Screenshot   ${SORT_BY}_header_clicked.png

Verify the bins linked to the Bin Type(s) against the database data
    [Arguments]     ${BIN_TYPE_ELEMENT}
    #Verify that the current page is BINTYPES

    ${correct_page_displayed}=      Correct Page is displayed    BINTYPES

    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${BIN_TYPE_ELEMENT}
    
    IF    '${bin_type_name_text}' == 'On-US'
        ${bin_type_name_text}=      Set Variable    OnUs
    END

    #Click on the bin type to Verify
    SeleniumLibrary.Click Element    ${BIN_TYPE_ELEMENT}
    Sleep    4s

    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    ${bin_type_name_text}
    IF    not ${correct_page_displayed}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Get the Bin Type from the database
    ${bins_details_for_bin_type_db_results}=     Get all active Bins details associated with the Bin Type from the Database using the Bin Type Name    ${bin_type_name_text.strip()}

   # Ensure the results are not empty
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bins_details_for_bin_type_db_results}
    Run Keyword If    not ${db_results_contain_data}
    ...     Run Keyword And Warn On Failure    Fail    There are no associated Bins on the DB for the Bin Type named: '${bin_type_name_text}'.

    IF    not ${db_results_contain_data}
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${number_of_rows_returned_from_db}=    Get Length     ${bins_details_for_bin_type_db_results}
    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The linked Bins table is not displayed on the 'View - ${bin_type_name_text}' page.


    #Filter the table to show 100 items
     ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            SeleniumLibrary.Click Element    ${FILTER_LIST}
            Sleep    3s
        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        IF  '${number_of_rows_returned_from_db}' == '0'
                Run Keyword And Warn On Failure    Fail    There are no Bins linked to Bin Type: '${bin_type_name_text}'.
            ELSE
                Run Keyword And Continue On Failure    Fail  There are no linked Bins displayed on the screen for Bin Type: '${bin_type_name_text}'. But the DB has '${number_of_rows_returned_from_db}' bins linked to Bin Type: '${bin_type_name_text}'.
        END
        Capture Page Screenshot   ${bin_type_name_text}_Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    ${bins_displayed_on_page_equal_db_total}=     Run Keyword And Return Status       Should Be Equal As Strings    ${total_number_of_items_on_page}    ${number_of_rows_returned_from_db}

    Run Keyword If    not ${bins_displayed_on_page_equal_db_total}
    ...    Run Keyword And Continue On Failure    Fail   The total number of associated Bins that are displayed on the page for Bin Type '${bin_type_name_text}' is not the same as the DB Bins for the same Bin Type. The DB total is '${number_of_rows_returned_from_db}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.
    ...  ELSE
    ...    Log Many    The total number of associated Bins that are displayed on the page for Bin Type '${bin_type_name_text}' is the same as the DB Bins for the same Bin Type. The DB total is '${number_of_rows_returned_from_db}' and the total displayed on the Front End is '${total_number_of_items_on_page}'.


     ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed for '${bin_type_name_text}' Bins!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
    ${attr}=                    Get Current Element Attributes    ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
    Log Many    next_page_disabled_status:   '${disabled_attribute}'
    ${record_counter}=    Set Variable  0
    ${list_of_verified_bins}=    Create List
    WHILE    '${disabled_attribute}' == '' or '${disabled_attribute}' == 'False' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${LINKED_BINS_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${linked_bins_table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END


        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}

           IF    '${record_counter}' == '0'
               Append To List   ${list_of_verified_bins}    ${bin_number}
           ELSE
               #Verify that the current Bin number has not been read before
                Run Keyword If    '${bin_number}' in ${list_of_verified_bins}    Run Keyword And Continue On Failure    Fail   The Bin number: '${bin_number}' is duplicated for Bin Type:  '${bin_type_name_text}'.
                ...    ELSE    Append To List   ${list_of_verified_bins}    ${bin_number}
           END

           #Log To Console   Current Bin number: ${bin_number}
           #Log To Console   ================================
           ${curr_row_bin_type_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[2]
           ${bin_type}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
           ${curr_row_bin_action_date_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[3]
           ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
           ${date1_normalized}=        Convert Date    ${bin_action_date.strip()}    date_format=%b %d, %Y

           ${db_row_data}=     Get Row By Column Value From DB results     ${bins_details_for_bin_type_db_results}     'BinNumber'      ${bin_number}

           ${bin_number_is_active_in_the_db}=     Run Keyword And Return Status       Should Not Be Equal As Strings    None    ${db_row_data}

           Run Keyword If    not ${bin_number_is_active_in_the_db}
           ...    Run Keyword And Continue On Failure    Fail   The active Bin Number: '${bin_number}', linked to Bin Type: '${bin_type_name_text}', was not found in the database.

           Log Many    ${db_row_data}

           IF    ${bin_number_is_active_in_the_db}
                ${db_bin_type}=    Get Column Data By Name       ${db_row_data}       BinType
                ${db_bin_action_date}=    Get Column Data By Name       ${db_row_data}       ActionDate
                ${db_bin_action_date_string}=       Convert To String    ${db_bin_action_date}
                ${date2_normalized}=      Convert Date    ${db_bin_action_date_string}    date_format=%Y-%m-%d
                Run Keyword And Continue On Failure     Should Be Equal As Strings    ${date1_normalized}    ${date2_normalized}        msg=The database action date:'${db_bin_action_date_string}' is not the same as the action date displayed on the front end which is: '${bin_action_date}'. This is for Bin number: '${bin_number}'.
                Run Keyword And Continue On Failure     Should Be Equal As Strings    ${bin_type.strip()}    ${db_bin_type.strip()}     msg=The database bin type:'${db_bin_type.strip()}' is not the same as the bin type displayed on the front end which is: '${bin_type.strip()}'. This is for Bin number: '${bin_number}'.
           END

           ${record_counter}=    Evaluate    ${record_counter} + 1
           #Log To Console    The counter value is ${record_counter}
        END

        ${web_element}=         SeleniumLibrary.Get Webelement  ${PAGINATOR_ICON}
        ${attr}=                    Get Current Element Attributes    ${web_element}
        ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
        Log To Console    next_page_disabled_status:   '${disabled_attribute}'
        IF    '${disabled_attribute}' == 'True' or '${record_counter}' == '${total_number_of_items_on_page}'

                ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
                Log Many  ${paginator_range_data}
                ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
                ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
                ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
                ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
                Log Many    ${total_number_of_items_on_current_page}
                Log Many    ${total_number_of_items_on_page}

                Run Keyword If    '${total_number_of_items_on_current_page}' == '${number_of_rows_returned_from_db}'
                ...    Log Many     All ${number_of_rows_returned_from_db} '${bin_type_name_text}' Bins have been verified successfully against the database.
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  Not all '${bin_type_name_text}' Bins have been verified against the DB. The total number of active Bins in the DB is '${number_of_rows_returned_from_db}', but the bins that are displayed on the View Linked Bins page is '${total_number_of_items_on_current_page}'.

                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN

             ELSE
                  #Click on the 'Next' button
                 SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                 Sleep    4s

        END
    END

    #${view_button_is_displayed}=     Run Keyword And Return Status     GenericMethods.Run Keyword Until Success      Wait Until Element Is Enabled    ${VIEW_BIN_TYPES_BTN}
    ${view_button_is_displayed}=     Wait for Element to be enabled    ${VIEW_BIN_TYPES_BTN}

    #Navigate to the Bin Types View Menu
    IF   ${view_button_is_displayed}
         Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
         SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
         Sleep    4s
    END

The returned Bin Numbers results must contain the characters of the search-data that was used to search
    [Arguments]     ${SEARCH_TEXT}

    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

    Capture Page Screenshot   Filter_Page_Results.png

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The linked Bins table is not displayed on the 'View - Bins' page.

    #Filter the table to show 100 items
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed for the search data: '${SEARCH_TEXT}'.
        Capture Page Screenshot   ${SEARCH_TEXT}_Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    ${record_counter}=    Set Variable  0
    WHILE    '${next_page_disabled_status}' == 'None' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${LINKED_BINS_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${linked_bins_table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END


        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}

           ${bin_number_contains_search_text}=     Run Keyword And Return Status       Should Contain    ${bin_number}    ${SEARCH_TEXT}

           Run Keyword If    not ${bin_number_contains_search_text}
           ...    Run Keyword And Continue On Failure    Fail   The Bin Number: '${bin_number}' does not contain the search text: '${SEARCH_TEXT}'.


           IF    ${bin_number_contains_search_text}
                Log Many    The Bin Number: '${bin_number}' contains the search text: '${SEARCH_TEXT}'.
           END

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled

        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'



        IF    '${next_page_disabled_status}' != 'None' and '${record_counter}' == '${total_number_of_items_on_page}'

                ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
                Log Many  ${paginator_range_data}
                ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
                ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
                ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
                ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
                Log Many    ${total_number_of_items_on_current_page}
                Log Many    ${total_number_of_items_on_page}

                Run Keyword If    '${total_number_of_items_on_current_page}' == '${record_counter}'
                ...    Log Many     All ${total_number_of_items_on_current_page} Bins have been verified successfully against the search text: '${SEARCH_TEXT}'..
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  Not all Bins have been verified against the search text: '${SEARCH_TEXT}'. The total number of Bins on the page is '${total_number_of_items_on_current_page}', but the total number of bins that have been verified is  '${record_counter}'.

                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN

             ELSE
                IF    '${next_page_disabled_status}' == 'None'
                         #Click on the 'Next' button
                         SeleniumLibrary.Click Element    ${PAGINATOR_ICON}
                         Sleep    4s
                END

        END
    END

    #${view_button_is_displayed}=     Run Keyword And Return Status     GenericMethods.Run Keyword Until Success      Wait Until Element Is Enabled    ${VIEW_BIN_TYPES_BTN}
    ${view_button_is_displayed}=     Wait for Element to be enabled    ${VIEW_BIN_TYPES_BTN}

    #Navigate to the Bin Types View Menu
    IF   ${view_button_is_displayed}
         Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
         SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
         Sleep    4s
    END

The returned Bin Numbers results must not contain Bin Numbers data
    [Arguments]     ${SEARCH_TEXT}
    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

    Capture Page Screenshot   Filter_Page_Results.png

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The linked Bins table is not displayed on the 'View - Bins' page.

    #Verify that the Bins total displayed on the Front End zero
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
    Log Many    ${number_of_items_on_current_page_array}
    Log Many    ${total_number_of_items_on_page}
    ${bin_numbers_not_returned}=     Run Keyword and Return Status   Should Be Equal As Strings   ${total_number_of_items_on_page.strip()}    0
    Run Keyword If    ${bin_numbers_not_returned}
    ...    Log Many    There are no Bins displayed for the search data: '${SEARCH_TEXT}'.
    ...  ELSE
    ...     Run Keyword And Continue on Failure  Fail  The bin numbers search for the using data: '${SEARCH_TEXT}' returned bin numbers.


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}

    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Fail    The 'Next Page' paginator icon is not displayed!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    ${next_page_disabled_status}=   Convert To Lower Case   ${next_page_disabled_status}

    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    Run Keyword If    '${next_page_disabled_status}' == 'true'
    ...    Log Many    The 'Next Page' paginator icon is disabled because there are no search results on the page.
    ...  ELSE
    ...    Run Keyword And Continue on Failure  Fail  The 'Next Page' paginator icon is not disabled as expected.

    Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
    SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}




The returned BINS must be sorted in ascending order
    [Arguments]     ${COLUMN_NAME_TO_VERIFY}


    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${COLUMN_NAME_TO_VERIFY}      msg=The column name data that must be used to sort verification is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name that was used for sorting BINS!

    Log Many    Sort data provided :)
    Sleep    3s
    ${provided_sort_data}=      Convert To Upper Case    ${COLUMN_NAME_TO_VERIFY}

    Run Keyword If    '${provided_sort_data}' != 'BIN NUMBER' and '${provided_sort_data}' != 'ACTION DATE'
    ...    Fail   The sorted bins could not be verified, please make sure that the 'COLUMN_NAME_TO_VERIFY' variable is either 'Bin Number' or 'Action Date'


    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The linked Bins table is not displayed on the 'View - Bins' page.

    #Filter the table to show 100 items
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}

        ELSE
            Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${FILTER_LIST_100_ITEMS}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed on the page.
        Capture Page Screenshot   Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Fail    The 'Next Page' paginator icon is not displayed!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    ${record_counter}=    Set Variable  0
    ${values}=    Create List

    WHILE    '${next_page_disabled_status}' == 'None' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${LINKED_BINS_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${linked_bins_table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END


        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_action_date_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[3]
           ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
           ${date1_normalized}=        Convert Date    ${bin_action_date.strip()}    date_format=%b %d, %Y


           IF    '${provided_sort_data}' == 'ACTION DATE'
                 ${cell_text}=    SeleniumLibrary.Get Text  ${curr_row_bin_action_date_ele}
                 ${date}=     Set Variable   ${cell_text}
                 ${month}=    Convert Month To Number    ${date}
                 ${day}=      Extract Day From Date    ${date}
                 ${year}=     Extract Year From Date    ${date}
                 ${cell_text}=     Convert To Integer    ${year}${month}${day}
           ELSE
                 ${cell_text}=    SeleniumLibrary.Get Text  ${curr_row_bin_ele}
           END

           Append To List    ${values}    ${cell_text}

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled

        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'



        IF    '${next_page_disabled_status}' != 'None' and '${record_counter}' == '${total_number_of_items_on_page}'

                ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
                Log Many  ${paginator_range_data}
                ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
                ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
                ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
                ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
                ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
                Log Many    ${total_number_of_items_on_current_page}
                Log Many    ${total_number_of_items_on_page}

                Run Keyword If    '${total_number_of_items_on_current_page}' == '${record_counter}'
                ...    Log Many     All ${total_number_of_items_on_current_page} Bins have been verified successfully.
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  Not all Bins have been verified. The total number of Bins on the page is '${total_number_of_items_on_current_page}', but the total number of bins that have been verified is  '${record_counter}'.

                Log Many    ${values}
                #Verify that records have been sorted in ascending order
                ${ascending}=    Set Variable    ${True}
                ${length}=    Get Length    ${values}    # Get the length of the list
                FOR    ${i}    IN RANGE    0    ${length - 1}
                    ${current}=    Get From List    ${values}    ${i}
                    ${next}=    Get From List    ${values}    ${i+1}
                    ${current_int}=    Evaluate    int('${current}')
                    ${next_int}=    Evaluate    int('${next}')
                    IF    ${ascending}
                        IF    ${current_int} > ${next_int}
                          ${ascending}=    Set Variable     ${False}
                          Exit For Loop
                        END
                    END

                END

                Run Keyword If    ${ascending}
                ...    Log Many    The table column: '${provided_sort_data}' is in ascending order. The data is populated as ${values}.
                ...  ELSE
                ...    Run Keyword And Continue On Failure    Fail  The table column: '${provided_sort_data}' is not in ascending order. The data is populated as ${values}.

                 Capture Page Screenshot   data_sorted_ascending_by_${provided_sort_data}.png
                RETURN
        END
    END


The returned BINS must be sorted in desceding order
    [Arguments]     ${COLUMN_NAME_TO_VERIFY}


    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${COLUMN_NAME_TO_VERIFY}      msg=The column name data that must be used to sort verification is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the column name that was used for sorting BINS!

    Log Many    Sort data provided :)
    Sleep    3s
    ${provided_sort_data}=      Convert To Upper Case    ${COLUMN_NAME_TO_VERIFY}

    Run Keyword If    '${provided_sort_data}' != 'BIN NUMBER' and '${provided_sort_data}' != 'ACTION DATE'
    ...    Fail   The sorted bins could not be verified, please make sure that the 'COLUMN_NAME_TO_VERIFY' variable is either 'Bin Number' or 'Action Date'


    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The linked Bins table is not displayed on the 'View - Bins' page.


    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed on the page.
        Capture Page Screenshot   Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Fail    The 'Next Page' paginator icon is not displayed!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    ${record_counter}=    Set Variable  0
    ${values}=    Create List

    WHILE    '${next_page_disabled_status}' == 'None' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${LINKED_BINS_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${linked_bins_table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END


        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_action_date_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[3]
           ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
           ${date1_normalized}=        Convert Date    ${bin_action_date.strip()}    date_format=%b %d, %Y


           IF    '${provided_sort_data}' == 'ACTION DATE'
                 ${cell_text}=    SeleniumLibrary.Get Text  ${curr_row_bin_action_date_ele}
                 ${date}=     Set Variable   ${cell_text}
                 ${month}=    Convert Month To Number    ${date}
                 ${day}=      Extract Day From Date    ${date}
                 ${year}=     Extract Year From Date    ${date}
                 ${cell_text}=     Convert To Integer    ${year}${month}${day}
           ELSE
                 ${cell_text}=    SeleniumLibrary.Get Text  ${curr_row_bin_ele}
           END

           Append To List    ${values}    ${cell_text}

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled

        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'



        IF    '${next_page_disabled_status}' != 'None' and '${record_counter}' == '${total_number_of_items_on_page}'

            ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
            Log Many  ${paginator_range_data}
            ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
            ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
            ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
            ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
            ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
            ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
            Log Many    ${total_number_of_items_on_current_page}
            Log Many    ${total_number_of_items_on_page}

            Run Keyword If    '${total_number_of_items_on_current_page}' == '${record_counter}'
            ...    Log Many     All ${total_number_of_items_on_current_page} Bins have been verified successfully.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail  Not all Bins have been verified. The total number of Bins on the page is '${total_number_of_items_on_current_page}', but the total number of bins that have been verified is  '${record_counter}'.

            Log Many    ${values}
            #Verify that records have been sorted in desceding order
            ${descending}=    Set Variable     ${True}
            ${length}=    Get Length    ${values}    # Get the length of the list
            FOR    ${i}    IN RANGE    0    ${length - 1}
                ${current}=    Get From List    ${values}    ${i}
                ${next}=    Get From List    ${values}    ${i+1}
                ${current_int}=    Evaluate    int('${current}')
                ${next_int}=    Evaluate    int('${next}')

                IF    ${descending}
                    IF    ${current_int} < ${next_int}
                      ${descending}=    Set Variable     ${False}
                      Exit For Loop
                    END
                END
            END

            Run Keyword If    ${descending}
            ...    Log Many    The table column: '${provided_sort_data}' is in descending order. The data is populated as ${values}.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail  The table column: '${provided_sort_data}' is not in descending order. The data is populated as ${values}.

            Capture Page Screenshot   data_sorted_descending_by_${provided_sort_data}.png
            RETURN
        END
    END


The returned BINS must neither be sorted in ascending nor desceding order
    [Arguments]     ${COLUMN_NAME_TO_VERIFY}


    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${COLUMN_NAME_TO_VERIFY}      msg=The column name data that must be used to sort verification is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Run Keyword And Continue On Failure  Fail     Please provide the column name that was used for sorting BINS!

    Log Many    Sort data provided :)
    Sleep    3s
    ${provided_sort_data}=      Convert To Upper Case    ${COLUMN_NAME_TO_VERIFY}

    Run Keyword If    '${provided_sort_data}' != 'BIN NUMBER' and '${provided_sort_data}' != 'ACTION DATE'
    ...    Fail   The sorted bins could not be verified, please make sure that the 'COLUMN_NAME_TO_VERIFY' variable is either 'Bin Number' or 'Action Date'


    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Run Keyword And Continue On Failure    Fail   The linked Bins table is not displayed on the 'View - Bins' page.


    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed on the page.
        Capture Page Screenshot   Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}


    ${paginator_icon_total}=           SeleniumLibrary.Get Element Count    ${PAGINATOR_ICON}


    IF    '${paginator_icon_total}' == '0'
        Capture Page Screenshot   Paginator_Icon_Not_Displayed.png
        Run Keyword And Continue on Failure  Fail    The 'Next Page' paginator icon is not displayed!
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    #Check if the paginator icon is disabled
    ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled
    Log Many    next_page_disabled_status:   '${next_page_disabled_status}'
    ${record_counter}=    Set Variable  0
    ${values}=    Create List

    WHILE    '${next_page_disabled_status}' == 'None' or '${record_counter}' != '${total_number_of_items_on_page}'
         #Loop through the displayed Bins and verify their details against the DB information
        ${linked_bins_table_rows_element_text}=       Catenate  ${LINKED_BINS_TABLE}/tbody/tr
        ${table_rows_displayed}=     Wait for Element to be enabled    ${linked_bins_table_rows_element_text}

        IF    not ${table_rows_displayed}
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
        END


        ${linked_bins_table_rows_total}=           SeleniumLibrary.Get Element Count    ${linked_bins_table_rows_element_text}

        FOR    ${index}    IN RANGE    1    ${linked_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_action_date_ele}=           Catenate  ${LINKED_BINS_TABLE}/tbody/tr[${index}]/td[3]
           ${bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
           ${date1_normalized}=        Convert Date    ${bin_action_date.strip()}    date_format=%b %d, %Y


           IF    '${provided_sort_data}' == 'ACTION DATE'
                 ${cell_text}=    SeleniumLibrary.Get Text  ${curr_row_bin_action_date_ele}
                 ${date}=     Set Variable   ${cell_text}
                 ${month}=    Convert Month To Number    ${date}
                 ${day}=      Extract Day From Date    ${date}
                 ${year}=     Extract Year From Date    ${date}
                 ${cell_text}=     Convert To Integer    ${year}${month}${day}

           ELSE
                 ${cell_text}=    SeleniumLibrary.Get Text  ${curr_row_bin_ele}
           END

           Append To List    ${values}    ${cell_text}

           ${record_counter}=    Evaluate    ${record_counter} + 1
           Log    The counter value is ${record_counter}
        END

        ${next_page_disabled_status}=    SeleniumLibrary.Get Element Attribute    ${PAGINATOR_ICON}    disabled

        Log Many    next_page_disabled_status:   '${next_page_disabled_status}'



        IF    '${next_page_disabled_status}' != 'None' and '${record_counter}' == '${total_number_of_items_on_page}'

            ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
            Log Many  ${paginator_range_data}
            ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
            ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
            ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–
            ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
            ${total_number_of_items_on_current_page}=   Set Variable    ${total_number_of_items_on_current_page.strip()}
            ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page.strip()}
            Log Many    ${total_number_of_items_on_current_page}
            Log Many    ${total_number_of_items_on_page}

            Run Keyword If    '${total_number_of_items_on_current_page}' == '${record_counter}'
            ...    Log Many     All ${total_number_of_items_on_current_page} Bins have been verified successfully.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail  Not all Bins have been verified. The total number of Bins on the page is '${total_number_of_items_on_current_page}', but the total number of bins that have been verified is  '${record_counter}'.

            Log Many    ${values}
            #Verify that records have not been sorted in ascending or descending order
            ${ascending}=    Set Variable     ${True}
            ${descending}=    Set Variable     ${True}
            ${length}=    Get Length    ${values}    # Get the length of the list
            FOR    ${i}    IN RANGE    0    ${length - 1}
                ${current}=    Get From List    ${values}    ${i}
                ${next}=    Get From List    ${values}    ${i+1}
                ${current_int}=    Evaluate    int('${current}')
                ${next_int}=    Evaluate    int('${next}')

                IF    ${ascending}
                     IF    ${current_int} > ${next_int}
                          ${ascending}=    Set Variable     ${False}
                     END
                END
                IF    ${descending}
                    IF    ${current_int} < ${next_int}
                          ${descending}=    Set Variable     ${False}
                    END
                END

                Exit For Loop If    not ${ascending} and not ${descending}
            END

            Run Keyword If    not ${ascending} and not ${descending}
            ...    Log Many    The table column: '${provided_sort_data}' is not sorted order. The data is populated as ${values}.
            ...  ELSE IF     ${descending}
            ...    Run Keyword And Continue On Failure    Fail  The table column: '${provided_sort_data}' is in descending order. The data is populated as ${values}.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail  The table column: '${provided_sort_data}' is in ascending order. The data is populated as ${values}.


            Capture Page Screenshot   data_not_sorted_by_${provided_sort_data}.png

            RETURN
        END
    END





The user must be able to filter the Bins displayed on the page
    [Arguments]     ${FILTER_NUMBER_TO_USE}


    ${is_not_empty}=    Run Keyword And Return Status    Should Not Be Empty    ${FILTER_NUMBER_TO_USE}      msg=The column name data that must be used to sort verification is not given!
    #This is the boolean value that checks if the search data is provided
    Run Keyword If    not ${is_not_empty}
    ...   Fail     Please provide the number that must used to filter BINS! The number can either be 5, 10, 25 or 100.

    Log Many    Filter data provided :)
    ${FILTER_NUMBER_TO_USE}=    Set Variable    ${FILTER_NUMBER_TO_USE.strip()}

    Run Keyword If    '${FILTER_NUMBER_TO_USE}' != '5' and '${FILTER_NUMBER_TO_USE}' != '10' and '${FILTER_NUMBER_TO_USE}' != '25' and '${FILTER_NUMBER_TO_USE}' != '100'
    ...    Fail     Please provide the correct number that must used to filter BINS! The number can either be 5, 10, 25 or 100.

    #Decide which filter number to click
    ${filter_element}=       Set Variable If
         ...       '${FILTER_NUMBER_TO_USE}' == '5'     ${FILTER_LIST_5_ITEMS}
         ...       '${FILTER_NUMBER_TO_USE}' == '10'    ${FILTER_LIST_10_ITEMS}
         ...       '${FILTER_NUMBER_TO_USE}' == '25'    ${FILTER_LIST_25_ITEMS}
         ...       '${FILTER_NUMBER_TO_USE}' == '100'   ${FILTER_LIST_100_ITEMS}



    ${linked_bins_table_elements}=           SeleniumLibrary.Get WebElements    ${LINKED_BINS_TABLE}
    ${number_of_elements_found}=    Get Length     ${linked_bins_table_elements}

     # Ensure the results are not empty
    ${table_is_displayed}=     Run Keyword And Return Status       Should Not Be Equal As Strings    0    ${number_of_elements_found}
    Run Keyword If    not ${table_is_displayed}
    ...    Fail   The linked Bins table is not displayed on the 'View - Bins' page.

    #Filter the table to show 100 items
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${FILTER_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${FILTER_LIST}

        ELSE
            Fail    The Filter List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
            SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
            Sleep    4s
            RETURN
    END

    SeleniumLibrary.Click Element    ${filter_element}
    Sleep    3s

    #Verify that the Bins total displayed on the Front End is the same
    #as active Bins on the DB for the current Bin Type.
    ${paginator_range_data}=      SeleniumLibrary.Get Text    ${PAGINATOR_RANGE_LABEL}
    Log Many  ${paginator_range_data}
    ${total_number_of_items_on_page_array}=       Split String    ${paginator_range_data}     separator=of
    ${total_number_of_items_on_page}=   Set Variable    ${total_number_of_items_on_page_array}[1]
    ${number_of_items_on_current_page_array}=   Split String    ${total_number_of_items_on_page_array}[0]   separator=–

    #If no Bins are displayed on the page
    IF    "${number_of_items_on_current_page_array}" == "['0 ']"
        Log Many    There are no Bins displayed on the page.
        Capture Page Screenshot   Bins_Not_Displayed.png
        Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
        SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
        Sleep    4s
        RETURN
    END

    ${total_number_of_items_on_current_page}=   Set Variable    ${number_of_items_on_current_page_array}[1]
    ${total_number_of_items_on_current_page}=   Evaluate    int('${total_number_of_items_on_current_page.strip()}')
    ${total_number_of_items_on_page}=           Evaluate    int('${total_number_of_items_on_page.strip()}')
    ${FILTER_NUMBER_TO_USE}=                    Evaluate    int('${FILTER_NUMBER_TO_USE}')
    Log Many    ${total_number_of_items_on_current_page}
    Log Many    ${total_number_of_items_on_page}

    #Verify that the total number of Bins displayed on the current page are as per the user's chosen number for filtering

    IF    ${total_number_of_items_on_current_page} < ${FILTER_NUMBER_TO_USE}
        IF    ${total_number_of_items_on_page} < ${FILTER_NUMBER_TO_USE}
            Log Many     The number of Bins displayed on the page is '${total_number_of_items_on_current_page}' which is the total number of BINS linked to the current Bin Type.
        ELSE
            Run Keyword And Continue On Failure    Fail  The number of Bins displayed on the page is '${total_number_of_items_on_current_page}' which is less than the filtered number: '${FILTER_NUMBER_TO_USE}'.
        END
    ELSE
        IF    ${total_number_of_items_on_current_page} == ${FILTER_NUMBER_TO_USE}
            Log Many     The number of Bins displayed on the page is '${FILTER_NUMBER_TO_USE}', this is the same as the number that was used to filer results.
        ELSE
            Run Keyword And Continue On Failure    Fail  The number of BINS displayed on the current page is greater than the filtered number. The number used to filter BINS is '${FILTER_NUMBER_TO_USE}', and the number of BINS displayed on the page is '${total_number_of_items_on_current_page}'.
        END
    END

    Capture Page Screenshot   data_filtered_by_${FILTER_NUMBER_TO_USE}.png

The user must not be able to add, edit or delete a Bin Type without Admin Access

    #Get the current role for the logged in user
    ${logged_in_users_role}=        The user retrieves the role of the logged in user

    Capture Page Screenshot   BinTypes_Btn_Validation.png
    ${add_bin_type_btn_is_displayed}=        Run Keyword And Return Status    Element Should Be Visible    ${ADD_BIN_TYPE_BTN}
    ${edit_or_delete_bin_type_btn_is_displayed}=        Run Keyword And Return Status    Element Should Be Visible    ${EDIT_DELETE_BIN_TYPE_BTN}

    #If the user's role is 'Admin', verify that the 'Add' and 'Edit/Delete' Bin Types tabs are displayed
    IF    '${logged_in_users_role}' == 'Admin'
        Run Keyword If    ${add_bin_type_btn_is_displayed} and ${edit_or_delete_bin_type_btn_is_displayed}
        ...    Log Many     The 'Add' and 'Edit/Delete' Bin Type buttons are displayed because the logged in user is an Admin.
        ...  ELSE
        ...    Fail   The 'Add' and 'Edit/Delete' Bin Type buttons are not displayed even though the logged-in user is an Admin.
    ELSE
        Run Keyword If    ${add_bin_type_btn_is_displayed} and ${edit_or_delete_bin_type_btn_is_displayed}
        ...    Fail     The 'Add' and 'Edit/Delete' Bin Type buttons are displayed even though the logged-in user is not an Admin. The logged in user's role is  '${logged_in_users_role}'
        ...  ELSE
        ...    Log Many   The 'Add' and 'Edit/Delete' Bin Type buttons are not displayed because the logged-in user is not an Admin. The logged in user's role is  '${logged_in_users_role}'.
    END


The user must be able to export the Bins linked to Bin Type
    [Arguments]     ${BIN_TYPE_NAME_TO_EXPORT}

    ${BIN_TYPE_TO_VERIFY}=      Remove Quotes       ${BIN_TYPE_NAME_TO_EXPORT}

    #This is the boolean value that checks if all bin types must be verified
    ${export_all_bin_types}=        Set Variable If
         ...       '${BIN_TYPE_TO_VERIFY}' == '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' == ''             ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' != '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' != ''             ${False}

    ${bin_types_list}=         Split String    ${BIN_TYPE_TO_VERIFY}    separator=,
    Log  ${bin_types_list}


    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Fail   There are no Bin Types displayed on the 'View' page. Please check if there are active Bin Types in the DB.

    IF    not ${bin_types_names_are_displayed}
         RETURN
    END

    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${elements_count}=      Get Length    ${bin_types_elements}

    #To be run if details of all bin types displayed on the page must be exported
    IF    ${export_all_bin_types}

         FOR    ${index}    IN RANGE    0    ${elements_count}
            ${element}=    Get From List    ${bin_types_elements}    ${index}
            Log    Element ${index}: ${element}
            ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element.strip()}
            Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}

            SeleniumLibrary.Click Element    ${element}
            Sleep    4s

            ${correct_page_displayed}=      Correct Page is displayed    ${bin_type_name_text}

            IF    not ${correct_page_displayed}
                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN
            END
            Scroll Up The Page
            Sleep   2s

            #Click the export button
            Scroll Element Into View    ${EXPORT_BIN_TYPES_BTN}

            SeleniumLibrary.Click Element    ${EXPORT_BIN_TYPES_BTN}
            Capture Page Screenshot   ${element}1_Displayed.png
            ${file_name}=     Catenate      ${element}_Bins_data.xlsx
            Wait Until File Is Downloaded    ${file_name}    30s

        END
    ELSE
        #Verify specific Bin Types
        FOR    ${element}    IN    @{bin_types_list}
            ${user_bin_type_found}=     Set Variable    ${False}
            Log    Current Bin Type to verify is: '${element}'
            #Verify if the Bin Type name is displayed on the 'View' Page
            FOR    ${page_element}    IN    @{bin_types_elements}
                 Log    Current Bin Type on 'View' page is: '${page_element}'
                 ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${page_element}
                 Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
                 IF    '${bin_type_name_text.strip()}' == '${element.strip()}'
                      ${user_bin_type_found}=     Set Variable    ${True}
                      Exit For Loop
                 END
            END
            IF    ${user_bin_type_found}
                Log Many    The Bin Type named: '${element}' was found on the 'View' page, and its linked Bins details will be exported.

                SeleniumLibrary.Click Element    ${page_element}
                Sleep    4s
                ${correct_page_displayed}=      Correct Page is displayed    ${element}

                IF    not ${correct_page_displayed}
                    Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}

                    SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                    Sleep    30s
                    RETURN
                END
                Scroll Up The Page
                Sleep   2s

                #Click the export button
                Scroll Element Into View    ${EXPORT_BIN_TYPES_BTN}

                SeleniumLibrary.Click Element    ${EXPORT_BIN_TYPES_BTN}
                ${file_name}=     Catenate      ${element}_Bins_data.xlsx
                Capture Page Screenshot   ${element}_Displayed.png
                Wait Until File Is Downloaded    ${file_name}    30s
            ELSE
                Capture Page Screenshot   ${element}_Displayed_FAIL.png
                Run Keyword And Continue On Failure    Fail     The Bin Type named: '${element}' was not found on the 'View' page.
            END
        END
    END



The export Bin Type button must be enabled
    [Arguments]     ${BIN_TYPE_NAME_TO_EXPORT}

    ${BIN_TYPE_TO_VERIFY}=      Remove Quotes       ${BIN_TYPE_NAME_TO_EXPORT}

    #This is the boolean value that checks if all bin types must be verified
    ${export_all_bin_types}=        Set Variable If
         ...       '${BIN_TYPE_TO_VERIFY}' == '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' == ''             ${True}
         ...       '${BIN_TYPE_TO_VERIFY}' != '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_TO_VERIFY}' != ''             ${False}

    ${bin_types_list}=         Split String    ${BIN_TYPE_TO_VERIFY}    separator=,
    Log  ${bin_types_list}


    ${bin_types_names_are_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_NAME}

    Run Keyword If    not ${bin_types_names_are_displayed}
    ...    Fail   There are no Bin Types displayed on the 'View' page. Please check if there are active Bin Types in the DB.

    IF    not ${bin_types_names_are_displayed}
         RETURN
    END

    ${bin_types_elements}=           SeleniumLibrary.Get WebElements    ${BIN_TYPE_NAME}
    ${elements_count}=      Get Length    ${bin_types_elements}

    #To be run if details of all bin types displayed on the page must be exported
    IF    ${export_all_bin_types}

         FOR    ${index}    IN RANGE    0    ${elements_count}
            ${element}=    Get From List    ${bin_types_elements}    ${index}
            Log    Element ${index}: ${element}
            ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${element.strip()}
            Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}

            SeleniumLibrary.Click Element    ${element}
            Sleep    4s

            ${correct_page_displayed}=      Correct Page is displayed    ${bin_type_name_text}

            IF    not ${correct_page_displayed}
                Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}
                SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                Sleep    4s
                RETURN
            END
            Scroll Up The Page
            Sleep   2s

            Capture Page Screenshot   export_button_status.png

            #Check if the export button is disabled
            ${web_element}=         SeleniumLibrary.Get Webelement  ${EXPORT_BIN_TYPES_BTN}
            ${attr}=                    Get Current Element Attributes    ${web_element}
            ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
            ${disabled_attribute}=    Convert To String     ${disabled_attribute}
            ${disabled_attribute}=    Convert To Lower Case     ${disabled_attribute}

            Log Many    next_page_disabled_status:   '${disabled_attribute}'

            ${export_button_is_enabled}=        Run Keyword And Return Status    Should Not Be Equal        'true'      '${disabled_attribute}'

            Run Keyword If    ${export_button_is_enabled}
            ...    Log Many    The 'Export' button is enabled as was expected.
            ...  ELSE
            ...    Fail       The 'Export' button is disabled.

        END
    ELSE
        #Verify specific Bin Types
        FOR    ${element}    IN    @{bin_types_list}
            ${user_bin_type_found}=     Set Variable    ${False}
            Log    Current Bin Type to verify is: '${element}'
            #Verify if the Bin Type name is displayed on the 'View' Page
            FOR    ${page_element}    IN    @{bin_types_elements}
                 Log    Current Bin Type on 'View' page is: '${page_element}'
                 ${bin_type_name_text}=      SeleniumLibrary.Get Text    ${page_element}
                 Log Many    Bin Type name displayed on the 'View' page is: ${bin_type_name_text}
                 IF    '${bin_type_name_text.strip()}' == '${element.strip()}'
                      ${user_bin_type_found}=     Set Variable    ${True}
                      Exit For Loop
                 END
            END
            IF    ${user_bin_type_found}
                Log Many    The Bin Type named: '${element}' was found on the 'View' page, and its linked Bins details will be exported.

                SeleniumLibrary.Click Element    ${page_element}
                Sleep    4s
                ${correct_page_displayed}=      Correct Page is displayed    ${element}

                IF    not ${correct_page_displayed}
                    Scroll Element Into View    ${VIEW_BIN_TYPES_BTN}

                    SeleniumLibrary.Click Element    ${VIEW_BIN_TYPES_BTN}
                    Sleep    30s
                    RETURN
                END
                Scroll Up The Page
                Sleep   2s

                #Check if the export button is disabled
                ${web_element}=         SeleniumLibrary.Get Webelement  ${EXPORT_BIN_TYPES_BTN}
                ${attr}=                    Get Current Element Attributes    ${web_element}
                ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled
                ${disabled_attribute}=    Convert To String     ${disabled_attribute}
                ${disabled_attribute}=    Convert To Lower Case     ${disabled_attribute}

                Log Many    next_page_disabled_status:   '${disabled_attribute}'

                ${export_button_is_enabled}=        Run Keyword And Return Status    Should Not Be Equal        'true'      '${disabled_attribute}'

                Run Keyword If    ${export_button_is_enabled}
                ...    Log Many    The 'Export' button is enabled as was expected.
                ...  ELSE
                ...    Fail       The 'Export' button is disabled.

            ELSE
                Capture Page Screenshot   ${element}_Displayed_FAIL.png
                Run Keyword And Continue On Failure    Fail     The Bin Type named: '${element}' was not found on the 'View' page.
            END
        END
    END

Wait Until File Is Downloaded
    [Arguments]    ${file_name}    ${timeout}
    Sleep    3s
    ${user_home}=    Get Environment Variable    UserProfile
    Sleep    3s


    ${result}=    Wait Until Keyword Succeeds    ${timeout}    1s    Run Keyword And Return Status    File Should Exist    ${user_home}/Downloads/${file_name}

    Run Keyword If     '${result}' == 'False'
    ...    Fail    File '${file_name}' was not downloaded within ${timeout}
    ...  ELSE
    ...    Log Many    File '${file_name}' was downloaded successfully within ${timeout}