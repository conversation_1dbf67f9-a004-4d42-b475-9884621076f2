<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 07:58:14.607" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_06_Calendar_Negative.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:58:14.920" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:14.920"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:58:14.920" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:14.920"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:58:14.920" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:14.920"/>
</kw>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:14.920"/>
</kw>
<test id="s1-t1" name="FFT - Calendar View - Navigation - Verify that the B-User can only see campaigns belonging to the current quarter" line="43">
<kw name="Validates Calendar View Page">
<arg>BU- Current Quarter Campaigns</arg>
<arg>T155057382</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<arg>B-User can only see campaigns belonging to the current quarter</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 07:58:14.920" level="INFO">Set test documentation to:
BU- Current Quarter Campaigns</msg>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:14.920"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 07:58:14.920" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057382'.</msg>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:14.920"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:58:15.283" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:58:15.283"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:15.283"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:15.283"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:58:15.283" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:15.283"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 07:58:15.283" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:15.283"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:15.283"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 07:58:15.460" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 07:58:16.164" level="INFO">${rc_code} = 128</msg>
<msg timestamp="******** 07:58:16.164" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 07:58:16.164" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:58:16.164" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000001CDA6E89700&gt;</msg>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 07:58:16.164" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 07:58:16.164" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 07:58:16.164" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:16.164"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 07:58:16.164" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 07:58:16.164" endtime="******** 07:58:21.238"/>
</kw>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:58:21.238"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 07:58:21.245" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 07:58:21.245" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 07:58:21.239" endtime="******** 07:58:21.246"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 07:58:21.246" endtime="******** 07:58:21.250"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 07:58:21.253" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 07:58:21.252" endtime="******** 07:58:21.489"/>
</kw>
<status status="PASS" starttime="******** 07:58:21.250" endtime="******** 07:58:21.490"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:58:31.492" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:58:21.491" endtime="******** 07:58:31.492"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:58:31.492" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:58:31.510" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 07:58:31.492" endtime="******** 07:58:31.510"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:58:31.510" endtime="******** 07:58:31.510"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:58:51.511" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 07:58:31.510" endtime="******** 07:58:51.511"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:58:51.511" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:58:52.038" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 07:58:51.511" endtime="******** 07:58:52.038"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:58:52.038" endtime="******** 07:58:52.038"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:59:02.064" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 07:58:52.038" endtime="******** 07:59:02.064"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 07:59:02.064" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:59:02.433" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 07:59:02.064" endtime="******** 07:59:02.433"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 07:59:02.433" endtime="******** 07:59:02.433"/>
</kw>
<status status="PASS" starttime="******** 07:58:21.238" endtime="******** 07:59:02.433"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 07:59:02.575" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1.png"&gt;&lt;img src="selenium-screenshot-1.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 07:59:02.434" endtime="******** 07:59:02.575"/>
</kw>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:59:02.575"/>
</kw>
<status status="PASS" starttime="******** 07:58:15.283" endtime="******** 07:59:02.575"/>
</kw>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:59:02.575"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:59:07.576" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 07:59:02.575" endtime="******** 07:59:07.576"/>
</kw>
<kw name="And The user clicks on Calendar View link" library="CalendarView">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks Calendar View link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 07:59:07.576" endtime="******** 07:59:07.576"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:59:07.578" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 07:59:07.576" endtime="******** 07:59:07.662"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${CAMPAIGN_VALENDAR_TABLE}</arg>
<arg>5</arg>
<arg>Campaing scheduler calendar not shown</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="******** 07:59:07.662" endtime="******** 07:59:07.788"/>
</kw>
<status status="PASS" starttime="******** 07:59:07.576" endtime="******** 07:59:07.788"/>
</kw>
<kw name="And Validate the test cases" library="CalendarView">
<arg>${Test_Case}</arg>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can navigate through the calendar by using &lt; &amp; &gt; buttons"</arg>
<arg>validate that the user can navigate through the calendar by using &lt; &amp; &gt; buttons</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "user can view a campaign"</arg>
<arg>validate that the user can view a campaign</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA Validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU validates that approved campaigns are highlighted in green"</arg>
<arg>Validate Approved Campaigns Are Green</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BA validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "BU Validates that non-approved campaigns are marked grey"</arg>
<arg>Validate Non-approved Campaigns Are Grey</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-Approver can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BA can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:07.788"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${Test_cases}' == "B-User can only see campaigns belonging to the current quarter"</arg>
<arg>Validate that BU can only have a view of campaigns in the current Quarter</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Validate that BU can only have a view of campaigns in the current Quarter" library="CalendarView">
<kw name="Click Element" library="SeleniumLibrary">
<arg>${CALENDAR_VIEW_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:59:07.790" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/mat-expansion-panel/div/div/mat-nav-list/mat-list-item[2]/span'.</msg>
<status status="PASS" starttime="******** 07:59:07.790" endtime="******** 07:59:07.856"/>
</kw>
<kw name="Page Should Contain" library="SeleniumLibrary">
<arg>Greyed out Campaigns needs to be approved*</arg>
<doc>Verifies that current page contains ``text``.</doc>
<msg timestamp="******** 07:59:07.869" level="INFO">Current page contains text 'Greyed out Campaigns needs to be approved*'.</msg>
<status status="PASS" starttime="******** 07:59:07.856" endtime="******** 07:59:07.869"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${PREVIOUS_MONTH}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:59:07.869" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[1]'.</msg>
<status status="PASS" starttime="******** 07:59:07.869" endtime="******** 07:59:07.960"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 07:59:09.960" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 07:59:07.960" endtime="******** 07:59:09.960"/>
</kw>
<kw name="Double Click Element" library="SeleniumLibrary">
<arg>${PREVIOUS_MONTH}</arg>
<doc>Double clicks the element identified by ``locator``.</doc>
<msg timestamp="******** 07:59:09.960" level="INFO">Double clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[1]/div[1]/div/button[1]'.</msg>
<status status="PASS" starttime="******** 07:59:09.960" endtime="******** 07:59:10.355"/>
</kw>
<kw name="Page Should Not Contain Element" library="SeleniumLibrary">
<arg>${CAMPAIGN}</arg>
<doc>Verifies that element ``locator`` is not found on the current page.</doc>
<msg timestamp="******** 07:59:10.381" level="INFO">Current page does not contain element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-logs-view/full-calendar/div[2]/div/table/tbody/tr/td/div/div/div/table/tbody/tr[2]/td[5]/div/div[2]/div[4]/a'.</msg>
<status status="PASS" starttime="******** 07:59:10.357" endtime="******** 07:59:10.381"/>
</kw>
<status status="PASS" starttime="******** 07:59:07.790" endtime="******** 07:59:10.381"/>
</kw>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:10.381"/>
</kw>
<status status="PASS" starttime="******** 07:59:07.788" endtime="******** 07:59:10.381"/>
</kw>
<kw name="And User logs out" library="Logout">
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 07:59:10.382" endtime="******** 07:59:10.397"/>
</kw>
<status status="PASS" starttime="******** 07:59:10.381" endtime="******** 07:59:10.397"/>
</kw>
<kw name="Run Keyword And Ignore Error" library="BuiltIn">
<arg>Logout</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<kw name="Logout" library="Logout">
<kw name="Click Element" library="SeleniumLibrary">
<arg>css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 07:59:10.399" level="INFO">Clicking element 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon'.</msg>
<msg timestamp="******** 07:59:10.541" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg timestamp="******** 07:59:10.543" level="FAIL">Element with locator 'css=body &gt; app-root &gt; app-top-nav &gt; mat-toolbar &gt; div &gt; div &gt; button &gt; span.mat-button-wrapper &gt; mat-icon' not found.</msg>
<status status="FAIL" starttime="******** 07:59:10.398" endtime="******** 07:59:10.623"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 07:59:10.623" endtime="******** 07:59:10.623"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//button[contains(text(),'Logout')]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 07:59:10.623" endtime="******** 07:59:10.627"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" starttime="******** 07:59:10.627" endtime="******** 07:59:10.627"/>
</kw>
<status status="FAIL" starttime="******** 07:59:10.398" endtime="******** 07:59:10.627"/>
</kw>
<status status="PASS" starttime="******** 07:59:10.398" endtime="******** 07:59:10.627"/>
</kw>
<status status="PASS" starttime="******** 07:59:10.381" endtime="******** 07:59:10.627"/>
</kw>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:59:10.627"/>
</kw>
<doc>BU- Current Quarter Campaigns</doc>
<tag>FFT HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 07:58:14.920" endtime="******** 07:59:10.627"/>
</test>
<doc>Testing future fit APC Portal Calendar Negative tests</doc>
<status status="PASS" starttime="******** 07:58:14.701" endtime="******** 07:59:14.320"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 07:58:16.164" level="WARN">There was error during termination of process</msg>
<msg timestamp="******** 07:58:18.360" level="WARN">The chromedriver version (124.0.6367.91) detected in PATH at C:\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.113); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<msg timestamp="******** 07:58:31.492" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:58:51.511" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:59:02.064" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 07:59:14.294" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
