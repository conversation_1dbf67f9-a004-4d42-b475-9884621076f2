*** Settings ***
#Author Name               : <PERSON><PERSON>
#Email Address             : yaash.ramsa<PERSON><EMAIL>


Documentation              APC Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime
Library                                              ../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/front_end/View_Bins_Page.robot
Resource                                          ../../keywords/front_end/Bin_Table_Landing_Page.robot


*** Variables ***
${MARKETING_EXPORT_CAMPAIGN_BUTTON}=              xpath=//*[text()[normalize-space(.)='Export Campaign Data']]

*** Keywords ***
The user is redirected to the APC Portal Landing page
    #Verify User Is on the APC Landing Page after login 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${ATM_MARKETING_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    User was not directed to the APC Landing page after login

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TABLES_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    User was not directed to the APC Landing page after login

    #Welcome Panel Display
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${WELCOME_BOARD}
    Run Keyword If    '${status}' == 'False'    Fail    Welcome board not displayed 

    ${WELCOME_BOARD_TEXT}=    Get Text    ${WELCOME_BOARD}
    Log    Welcome Board contains text: ${WELCOME_BOARD_TEXT}

    #Notice Board Display 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${NOTICE_BOARD}
    Run Keyword If    '${status}' == 'False'    Fail    Notice Board not displayed

    ${NOTICE_BOARD_TEXT}=    Get Text    ${NOTICE_BOARD}
    Log    Notice Board contains text: ${NOTICE_BOARD_TEXT}

the user retrieves the current URL
    ${APC_CURRENT_URL}=    Get Location
    Log    Current URL ON APC Landing Page:${APC_CURRENT_URL}

    Set Suite Variable    ${APC_CURRENT_URL}

the URL must match the expected APC Landing page URL
    [Arguments]    ${EXPECTED_APC_LANDING_PAGE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
          ${BASE_URL}=      Get Environment Variable    BASE_URL
          #Get the domain url details
          ${EXPECTED_APC_LANDING_PAGE_URL}=    Read Config Property      ${BASE_URL}

    END

    IF    '${EXPECTED_APC_LANDING_PAGE_URL}' in '${APC_CURRENT_URL}'
        Log Many    URL Displayed on APC Landing page matches the expected URL
    ELSE
       Fail    URL Displayed on APC Landing page does not match the expected URL. The displayed URL is '${APC_CURRENT_URL}'; while the expected URL is  ${EXPECTED_APC_LANDING_PAGE_URL}
    END

The user verfies that the "APC Portal" field appears correctly on the APC Landing Page 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${APC_PORTAL_FIELD}
    Run Keyword If    '${status}' == 'False'    Fail    APC Portal Field is not visible from the APC Landing Page

The user clicks on the Home Icon from the Marketing Landing Page
    #Verify user is on the Marketing Landing Page 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${MARKETING_EXPORT_CAMPAIGN_BUTTON}
    Run Keyword If    '${status}' == 'False'    Fail    User is not on the APC landing page

    #Click the home button
    Wait Until Element Is Enabled    ${HOME_BUTTON}
    Click Element    ${HOME_BUTTON}
    Sleep    2s 

The user navigates to the Marketing Portal 
    Wait Until Element Is Enabled    ${ATM_MARKETING_MENU}
    Click Element    ${ATM_MARKETING_MENU}
    Sleep    5s

The user verfies that the Notice Board on APC Landing Page Displays Correct Number of Notices 
    #Notice Board Display 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${NOTICE_BOARD}
    Run Keyword If    '${status}' == 'False'    Log    Notice Board not displayed

    ${NOTICE_BOARD_TEXT}=    Get Text    ${NOTICE_BOARD}
    Log    Notice Board contains text: ${NOTICE_BOARD_TEXT}

The user verifies successful access to the Marketing Portal
    #Verify user is on the Marketing Landing Page 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${MARKETING_EXPORT_CAMPAIGN_BUTTON}
    Run Keyword If    '${status}' == 'False'    Fail    User is not on the APC landing page

The user navigates to Bin Tables
    Wait Until Element Is Enabled    ${BIN_TABLES_MENU}
    Click Element    ${BIN_TABLES_MENU}
    Sleep    5s

The user verifies successful access to Bin Tables 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BINS_MENU}
    Run Keyword If    '${status}' == 'False'    Log    Bins Menu not displayed 

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TYPE_MENU}
    Run Keyword If    '${status}' == 'False'    Log    Bin Type Menu not displayed

    #Welcome Panel Display
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${WELCOME_BOARD}
    Run Keyword If    '${status}' == 'False'    Log    Welcome board not displayed 

    ${WELCOME_BOARD_TEXT}=    Get Text    ${WELCOME_BOARD}
    Log    Welcome Board contains text: ${WELCOME_BOARD_TEXT}

    #Notice Board Display 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${NOTICE_BOARD}
    Run Keyword If    '${status}' == 'False'    Log    Notice Board not displayed

    ${NOTICE_BOARD_TEXT}=    Get Text    ${NOTICE_BOARD}
    Log    Notice Board contains text: ${NOTICE_BOARD_TEXT}

the user verifies if the Marketing and Bin Table links are greyed out
    ${is_marketing_link_clickable}=    Run Keyword And Return Status    Wait Until Element Is Enabled    ${ATM_MARKETING_MENU}    3s
    Run Keyword If    ${is_marketing_link_clickable}         Access ATM Marketing Link
    ...    ELSE    
    ...    Log    User does NOT have access, ATM Marketing is greyed out.
    Set Suite Variable    ${is_marketing_link_clickable}

    #Click the home button to check the Bin Table Link access
    Wait Until Element Is Enabled    ${HOME_BUTTON}
    Click Element    ${HOME_BUTTON}
    Sleep    2s 


    ${is_bin_table_link_clickable}=    Run Keyword And Return Status    Wait Until Element Is Enabled    ${BIN_TABLES_MENU}    3s
    Run Keyword If    ${is_bin_table_link_clickable}    Access Bin Table Link 
    ...    ELSE    
    ...    Log    User does NOT have access, Bin Table is greyed out.
    Set Suite Variable    ${is_bin_table_link_clickable}
    
if the links are greyed out, the user must not be able to access ATM Marketing or Bin Tables
    Run Keyword If    not ${is_marketing_link_clickable} and not ${is_bin_table_link_clickable}   
    ...    Log    Both links are greyed out, the user will not be able to access ATM Marketing or Bin Tables.
    

Access ATM Marketing Link
    Wait Until Element Is Enabled    ${ATM_MARKETING_MENU}
    Click Element    ${ATM_MARKETING_MENU}
    Sleep    5s

    #Verify user is on the Marketing Landing Page 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${MARKETING_EXPORT_CAMPAIGN_BUTTON}
    Run Keyword If    '${status}' == 'False'    Fail    User is not on the APC landing page

Access Bin Table Link
    Wait Until Element Is Enabled    ${BIN_TABLES_MENU}
    Click Element    ${BIN_TABLES_MENU}
    Sleep    5s

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BINS_MENU}
    Run Keyword If    '${status}' == 'False'    Log    Bins Menu not displayed 

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TYPE_MENU}
    Run Keyword If    '${status}' == 'False'    Log    Bin Type Menu not displayed

The user verifies the Welcome Message displayed
    #Welcome Panel Display
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${WELCOME_BOARD}
    Run Keyword If    '${status}' == 'False'    Log    Welcome board not displayed 

    ${WELCOME_BOARD_TEXT}=    Get Text    ${WELCOME_BOARD}
    Log    Welcome Board contains text: ${WELCOME_BOARD_TEXT}