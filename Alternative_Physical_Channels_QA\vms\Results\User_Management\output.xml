<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="20241002 12:05:55.801" rpa="false" schemaversion="4">
<suite id="s1" name="VMS Portal" source="C:\Users\<USER>\source\repos\alternative_physical_channels\vms\tests\ADMIN_USER_MANAGEMENT\RAC29a_TC_342_Validate_Add_New_User_User_Management.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241002 12:05:57.435" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<status status="PASS" starttime="20241002 12:05:57.435" endtime="20241002 12:05:57.435"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241002 12:05:57.437" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'data\User_Management.xml'.</msg>
<status status="PASS" starttime="20241002 12:05:57.437" endtime="20241002 12:05:57.437"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241002 12:05:57.437" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20241002 12:05:57.437" endtime="20241002 12:05:57.437"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20241002 12:05:57.438" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<status status="PASS" starttime="20241002 12:05:57.437" endtime="20241002 12:05:57.438"/>
</kw>
<status status="PASS" starttime="20241002 12:05:57.435" endtime="20241002 12:05:57.438"/>
</kw>
<test id="s1-t1" name="Create a VMS user with a 'Browse' Role Test Case" line="40">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'Browse' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0540to</arg>
<arg>Automation User Browser</arg>
<arg>Browse</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20241002 12:05:57.439" level="INFO">Set test documentation to:
Create a VMS user with 'Browse' Role</msg>
<status status="PASS" starttime="20241002 12:05:57.438" endtime="20241002 12:05:57.439"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:05:57.552" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20241002 12:05:57.440" endtime="20241002 12:05:57.552"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241002 12:05:57.552" endtime="20241002 12:05:57.553"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241002 12:05:57.553" endtime="20241002 12:05:57.553"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:05:57.555" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20241002 12:05:57.554" endtime="20241002 12:05:57.555"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20241002 12:05:57.555" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20241002 12:05:57.555" endtime="20241002 12:05:57.555"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20241002 12:05:57.555" endtime="20241002 12:05:57.556"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20241002 12:05:57.589" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20241002 12:05:58.050" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20241002 12:05:58.050" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20241002 12:05:57.556" endtime="20241002 12:05:58.050"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20241002 12:05:58.051" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20241002 12:05:58.051" endtime="20241002 12:05:58.052"/>
</kw>
<status status="PASS" starttime="20241002 12:05:58.050" endtime="20241002 12:05:58.052"/>
</kw>
<status status="PASS" starttime="20241002 12:05:57.554" endtime="20241002 12:05:58.052"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241002 12:05:58.053" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="20241002 12:05:58.053" endtime="20241002 12:05:58.053"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20241002 12:05:58.053" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="20241002 12:05:58.053" endtime="20241002 12:05:58.053"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="20241002 12:05:58.054" level="INFO">${browser_name} = CHROME</msg>
<status status="PASS" starttime="20241002 12:05:58.053" endtime="20241002 12:05:58.054"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.054"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.054"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.054"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.054"/>
</kw>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.054"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.055"/>
</kw>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.055"/>
</branch>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.055"/>
</if>
<status status="NOT RUN" starttime="20241002 12:05:58.054" endtime="20241002 12:05:58.055"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="20241002 12:05:58.056" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<status status="PASS" starttime="20241002 12:05:58.055" endtime="20241002 12:05:58.056"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:05:58.056" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000022BD003F560&gt;</msg>
<status status="PASS" starttime="20241002 12:05:58.056" endtime="20241002 12:05:58.056"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="20241002 12:05:58.056" endtime="20241002 12:05:58.056"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="20241002 12:05:58.056" endtime="20241002 12:05:58.057"/>
</kw>
<status status="NOT RUN" starttime="20241002 12:05:58.056" endtime="20241002 12:05:58.057"/>
</branch>
<status status="PASS" starttime="20241002 12:05:58.056" endtime="20241002 12:05:58.057"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20241002 12:05:58.057" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20241002 12:05:58.057" endtime="20241002 12:05:58.057"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20241002 12:05:58.057" endtime="20241002 12:05:58.058"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20241002 12:05:58.058" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="20241002 12:05:58.058" endtime="20241002 12:05:58.058"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20241002 12:05:58.058" endtime="20241002 12:05:58.059"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20241002 12:05:58.059" endtime="20241002 12:05:58.059"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20241002 12:05:58.059" endtime="20241002 12:05:58.059"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20241002 12:05:58.059" endtime="20241002 12:05:58.059"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:05:58.060" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20241002 12:05:58.060" endtime="20241002 12:05:58.060"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20241002 12:05:58.060" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\User...</msg>
<status status="PASS" starttime="20241002 12:05:58.060" endtime="20241002 12:05:58.060"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="20241002 12:05:58.061" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<status status="PASS" starttime="20241002 12:05:58.060" endtime="20241002 12:06:01.367"/>
</kw>
<status status="PASS" starttime="20241002 12:05:58.055" endtime="20241002 12:06:01.367"/>
</branch>
<status status="PASS" starttime="20241002 12:05:58.054" endtime="20241002 12:06:01.367"/>
</if>
<status status="PASS" starttime="20241002 12:05:57.554" endtime="20241002 12:06:01.367"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="20241002 12:06:01.384" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg timestamp="20241002 12:06:01.384" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<status status="PASS" starttime="20241002 12:06:01.368" endtime="20241002 12:06:01.384"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="20241002 12:06:01.385" endtime="20241002 12:06:01.393"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="20241002 12:06:01.398" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<status status="PASS" starttime="20241002 12:06:01.398" endtime="20241002 12:06:06.003"/>
</kw>
<status status="PASS" starttime="20241002 12:06:01.393" endtime="20241002 12:06:06.003"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:08.005" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:06.004" endtime="20241002 12:06:08.005"/>
</kw>
<status status="PASS" starttime="20241002 12:06:01.367" endtime="20241002 12:06:08.005"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:08.051" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:08.006" endtime="20241002 12:06:08.051"/>
</kw>
<msg timestamp="20241002 12:06:08.051" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:08.006" endtime="20241002 12:06:08.051"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:08.053" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:08.052" endtime="20241002 12:06:08.154"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:08.154" endtime="20241002 12:06:08.180"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:08.181" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:08.180" endtime="20241002 12:06:08.269"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:08.270" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:08.270" endtime="20241002 12:06:08.463"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:10.463" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:08.463" endtime="20241002 12:06:10.463"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:10.483" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:10.463" endtime="20241002 12:06:10.483"/>
</kw>
<msg timestamp="20241002 12:06:10.483" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:10.463" endtime="20241002 12:06:10.483"/>
</kw>
<status status="PASS" starttime="20241002 12:06:08.052" endtime="20241002 12:06:10.483"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:10.484" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:10.484" endtime="20241002 12:06:10.571"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:10.571" endtime="20241002 12:06:10.592"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:10.594" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:10.594" endtime="20241002 12:06:10.718"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:10.719" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:10.718" endtime="20241002 12:06:10.883"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:12.885" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:10.884" endtime="20241002 12:06:12.885"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:12.913" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:12.886" endtime="20241002 12:06:12.913"/>
</kw>
<msg timestamp="20241002 12:06:12.913" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:12.886" endtime="20241002 12:06:12.913"/>
</kw>
<status status="PASS" starttime="20241002 12:06:10.484" endtime="20241002 12:06:12.913"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:12.915" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:12.914" endtime="20241002 12:06:13.044"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:13.046" endtime="20241002 12:06:13.063"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:13.064" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:13.064" endtime="20241002 12:06:13.154"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:13.156" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:13.155" endtime="20241002 12:06:13.339"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:15.341" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:13.339" endtime="20241002 12:06:15.341"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:15.370" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:15.342" endtime="20241002 12:06:15.370"/>
</kw>
<msg timestamp="20241002 12:06:15.371" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:15.341" endtime="20241002 12:06:15.371"/>
</kw>
<status status="PASS" starttime="20241002 12:06:12.913" endtime="20241002 12:06:15.371"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:15.372" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:15.372" endtime="20241002 12:06:15.492"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:15.493" endtime="20241002 12:06:15.515"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:15.515" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:15.515" endtime="20241002 12:06:15.607"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:15.608" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:15.607" endtime="20241002 12:06:15.801"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:17.802" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:15.801" endtime="20241002 12:06:17.802"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:17.832" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:17.804" endtime="20241002 12:06:17.832"/>
</kw>
<msg timestamp="20241002 12:06:17.832" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:17.802" endtime="20241002 12:06:17.832"/>
</kw>
<status status="PASS" starttime="20241002 12:06:15.371" endtime="20241002 12:06:17.832"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:17.834" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:17.833" endtime="20241002 12:06:17.949"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:17.950" endtime="20241002 12:06:17.970"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:17.971" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:17.970" endtime="20241002 12:06:18.272"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:18.273" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:18.272" endtime="20241002 12:06:18.798"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:20.799" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:18.798" endtime="20241002 12:06:20.799"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:20.941" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:20.799" endtime="20241002 12:06:20.942"/>
</kw>
<msg timestamp="20241002 12:06:20.942" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:20.799" endtime="20241002 12:06:20.942"/>
</kw>
<status status="PASS" starttime="20241002 12:06:17.832" endtime="20241002 12:06:20.942"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:20.944" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:20.943" endtime="20241002 12:06:21.350"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:21.350" endtime="20241002 12:06:21.462"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:21.463" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:21.463" endtime="20241002 12:06:21.713"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:21.714" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:21.713" endtime="20241002 12:06:22.406"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:24.406" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:22.406" endtime="20241002 12:06:24.406"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:24.425" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:24.407" endtime="20241002 12:06:24.425"/>
</kw>
<msg timestamp="20241002 12:06:24.425" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:24.407" endtime="20241002 12:06:24.425"/>
</kw>
<status status="PASS" starttime="20241002 12:06:20.943" endtime="20241002 12:06:24.425"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:24.426" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:24.426" endtime="20241002 12:06:24.516"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:24.517" endtime="20241002 12:06:24.535"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:24.536" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:24.535" endtime="20241002 12:06:24.702"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:24.703" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:24.702" endtime="20241002 12:06:24.977"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:26.979" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:24.978" endtime="20241002 12:06:26.979"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:27.003" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:26.981" endtime="20241002 12:06:27.004"/>
</kw>
<msg timestamp="20241002 12:06:27.004" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:26.980" endtime="20241002 12:06:27.004"/>
</kw>
<status status="PASS" starttime="20241002 12:06:24.425" endtime="20241002 12:06:27.004"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:27.005" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:27.005" endtime="20241002 12:06:27.129"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:27.129" endtime="20241002 12:06:27.150"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:27.150" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:27.150" endtime="20241002 12:06:27.258"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:27.258" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:27.258" endtime="20241002 12:06:27.464"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:29.466" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:27.465" endtime="20241002 12:06:29.466"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:29.490" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<status status="PASS" starttime="20241002 12:06:29.466" endtime="20241002 12:06:29.490"/>
</kw>
<msg timestamp="20241002 12:06:29.490" level="INFO">${User_Name_Element_Visible} = True</msg>
<status status="PASS" starttime="20241002 12:06:29.466" endtime="20241002 12:06:29.490"/>
</kw>
<status status="PASS" starttime="20241002 12:06:27.005" endtime="20241002 12:06:29.490"/>
</iter>
<iter>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:29.491" level="INFO">Typing text 'AB032TO' into text field 'name=txtUsername'.</msg>
<status status="PASS" starttime="20241002 12:06:29.490" endtime="20241002 12:06:29.584"/>
</kw>
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" starttime="20241002 12:06:29.584" endtime="20241002 12:06:29.604"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:29.605" level="INFO">Typing text 'Kukinyane@1988' into text field 'name=txtPassword'.</msg>
<status status="PASS" starttime="20241002 12:06:29.604" endtime="20241002 12:06:29.706"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:29.707" level="INFO">Clicking button 'name=btnLogon'.</msg>
<status status="PASS" starttime="20241002 12:06:29.706" endtime="20241002 12:06:29.981"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:31.982" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="20241002 12:06:29.982" endtime="20241002 12:06:31.982"/>
</kw>
<kw name="Run Keyword And Return Status" library="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<kw name="Element Should Be Visible" library="SeleniumLibrary">
<arg>${USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:31.994" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<msg timestamp="20241002 12:06:31.997" level="FAIL">NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<status status="FAIL" starttime="20241002 12:06:31.982" endtime="20241002 12:06:32.007"/>
</kw>
<msg timestamp="20241002 12:06:32.007" level="INFO">${User_Name_Element_Visible} = False</msg>
<status status="PASS" starttime="20241002 12:06:31.982" endtime="20241002 12:06:32.007"/>
</kw>
<status status="PASS" starttime="20241002 12:06:29.490" endtime="20241002 12:06:32.007"/>
</iter>
<status status="PASS" starttime="20241002 12:06:08.051" endtime="20241002 12:06:32.008"/>
</while>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:32.009" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:32.009" endtime="20241002 12:06:32.010"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="20241002 12:06:32.010" endtime="20241002 12:06:32.010"/>
</kw>
<status status="FAIL" starttime="20241002 12:05:57.554" endtime="20241002 12:06:32.011"/>
</kw>
<status status="FAIL" starttime="20241002 12:05:57.553" endtime="20241002 12:06:32.011"/>
</kw>
<status status="FAIL" starttime="20241002 12:05:57.439" endtime="20241002 12:06:32.011"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:32.012" endtime="20241002 12:06:32.012"/>
</kw>
<kw name="And The user Adds a new VMS User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:32.013" endtime="20241002 12:06:32.013"/>
</kw>
<kw name="And The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:32.014" endtime="20241002 12:06:32.014"/>
</kw>
<kw name="And Searches for existing user" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20241002 12:06:32.014" endtime="20241002 12:06:32.014"/>
</kw>
<kw name="Then The created user must be found on VMS Application and Database" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:32.014" endtime="20241002 12:06:32.014"/>
</kw>
<status status="FAIL" starttime="20241002 12:05:57.438" endtime="20241002 12:06:32.014"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:32.021" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<msg timestamp="20241002 12:06:32.022" level="FAIL">NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<status status="FAIL" starttime="20241002 12:06:32.016" endtime="20241002 12:06:32.028"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:32.029" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:32.029" endtime="20241002 12:06:32.030"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:32.031" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20241002 12:06:32.038" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<msg timestamp="20241002 12:06:32.039" level="FAIL">NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<status status="FAIL" starttime="20241002 12:06:32.030" endtime="20241002 12:06:32.043"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:35.043" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20241002 12:06:32.043" endtime="20241002 12:06:35.043"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20241002 12:06:35.044" endtime="20241002 12:06:37.120"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:32.016" endtime="20241002 12:06:37.120">Several failures occurred:

1) NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
    [ Message content over the limit has been removed. ]
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]</status>
</kw>
<doc>Create a VMS user with 'Browse' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20241002 12:05:57.438" endtime="20241002 12:06:37.121">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) NoSuchWindowException: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
    [ Message content over the limit has been removed. ]
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]</status>
</test>
<test id="s1-t2" name="Create a VMS user with a 'User' Role Test Case" line="41">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'User' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0541to</arg>
<arg>Automation User</arg>
<arg>User</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20241002 12:06:37.122" level="INFO">Set test documentation to:
Create a VMS user with 'User' Role</msg>
<status status="PASS" starttime="20241002 12:06:37.122" endtime="20241002 12:06:37.122"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:06:37.123" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20241002 12:06:37.123" endtime="20241002 12:06:37.123"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:37.124" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:37.123" endtime="20241002 12:06:37.124"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20241002 12:06:37.125" endtime="20241002 12:06:37.125"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20241002 12:06:37.125" endtime="20241002 12:06:37.125"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:37.123" endtime="20241002 12:06:37.125"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:37.125" endtime="20241002 12:06:37.125"/>
</kw>
<kw name="And The user Adds a new VMS User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:37.126" endtime="20241002 12:06:37.126"/>
</kw>
<kw name="And The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:37.126" endtime="20241002 12:06:37.126"/>
</kw>
<kw name="And Searches for existing user" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20241002 12:06:37.126" endtime="20241002 12:06:37.126"/>
</kw>
<kw name="Then The created user must be found on VMS Application and Database" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:37.126" endtime="20241002 12:06:37.127"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:37.121" endtime="20241002 12:06:37.127"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:37.128" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20241002 12:06:37.128" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20241002 12:06:37.127" endtime="20241002 12:06:37.129"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:37.130" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:37.130" endtime="20241002 12:06:37.130"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:37.130" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20241002 12:06:37.130" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20241002 12:06:37.131" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20241002 12:06:37.130" endtime="20241002 12:06:37.131"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:40.133" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20241002 12:06:37.132" endtime="20241002 12:06:40.133"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20241002 12:06:40.133" endtime="20241002 12:06:40.133"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:37.127" endtime="20241002 12:06:40.133">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Create a VMS user with 'User' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20241002 12:06:37.121" endtime="20241002 12:06:40.133">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-t3" name="Create a VMS user with a 'Supervisor' Role Test Case" line="42">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'Supervisor' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0542to</arg>
<arg>Automation User Supervisor</arg>
<arg>Supervisor</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20241002 12:06:40.134" level="INFO">Set test documentation to:
Create a VMS user with 'Supervisor' Role</msg>
<status status="PASS" starttime="20241002 12:06:40.134" endtime="20241002 12:06:40.134"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:06:40.135" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20241002 12:06:40.135" endtime="20241002 12:06:40.135"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:40.135" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:40.135" endtime="20241002 12:06:40.135"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20241002 12:06:40.135" endtime="20241002 12:06:40.136"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20241002 12:06:40.136" endtime="20241002 12:06:40.136"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:40.134" endtime="20241002 12:06:40.136"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:40.137" endtime="20241002 12:06:40.137"/>
</kw>
<kw name="And The user Adds a new VMS User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:40.137" endtime="20241002 12:06:40.137"/>
</kw>
<kw name="And The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:40.137" endtime="20241002 12:06:40.137"/>
</kw>
<kw name="And Searches for existing user" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20241002 12:06:40.138" endtime="20241002 12:06:40.138"/>
</kw>
<kw name="Then The created user must be found on VMS Application and Database" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:40.138" endtime="20241002 12:06:40.138"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:40.133" endtime="20241002 12:06:40.138"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:40.139" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20241002 12:06:40.139" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20241002 12:06:40.139" endtime="20241002 12:06:40.140"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:40.141" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:40.141" endtime="20241002 12:06:40.142"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:40.142" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20241002 12:06:40.142" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20241002 12:06:40.142" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20241002 12:06:40.142" endtime="20241002 12:06:40.143"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:43.145" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20241002 12:06:40.143" endtime="20241002 12:06:43.145"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20241002 12:06:43.148" endtime="20241002 12:06:43.149"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:40.138" endtime="20241002 12:06:43.150">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Create a VMS user with 'Supervisor' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20241002 12:06:40.133" endtime="20241002 12:06:43.151">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<test id="s1-t4" name="Create a VMS user with a 'Administrator' Role Test Case" line="43">
<kw name="VMS User Creation">
<arg>Create a VMS user with 'Administrator' Role</arg>
<arg>VMS_UAT</arg>
<arg>AB0543to</arg>
<arg>Automation User Administrator</arg>
<arg>Administrator</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20241002 12:06:43.159" level="INFO">Set test documentation to:
Create a VMS user with 'Administrator' Role</msg>
<status status="PASS" starttime="20241002 12:06:43.159" endtime="20241002 12:06:43.159"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20241002 12:06:43.161" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20241002 12:06:43.160" endtime="20241002 12:06:43.161"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:43.161" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:43.161" endtime="20241002 12:06:43.162"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="20241002 12:06:43.162" endtime="20241002 12:06:43.162"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="NOT RUN" starttime="20241002 12:06:43.162" endtime="20241002 12:06:43.162"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:43.159" endtime="20241002 12:06:43.162"/>
</kw>
<kw name="When The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:43.163" endtime="20241002 12:06:43.163"/>
</kw>
<kw name="And The user Adds a new VMS User" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:43.163" endtime="20241002 12:06:43.163"/>
</kw>
<kw name="And The user navigates to Admin - User Management" library="UserManagement">
<status status="NOT RUN" starttime="20241002 12:06:43.163" endtime="20241002 12:06:43.163"/>
</kw>
<kw name="And Searches for existing user" library="UserManagement">
<arg>${USER_NAME}</arg>
<status status="NOT RUN" starttime="20241002 12:06:43.164" endtime="20241002 12:06:43.164"/>
</kw>
<kw name="Then The created user must be found on VMS Application and Database" library="UserManagement">
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="NOT RUN" starttime="20241002 12:06:43.164" endtime="20241002 12:06:43.164"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:43.158" endtime="20241002 12:06:43.165"/>
</kw>
<kw name="The user logs out of VMS" library="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<msg timestamp="20241002 12:06:43.167" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20241002 12:06:43.168" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20241002 12:06:43.166" endtime="20241002 12:06:43.170"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<msg timestamp="20241002 12:06:43.171" level="FAIL">OSError: [WinError 233] No process is on the other end of the pipe</msg>
<status status="FAIL" starttime="20241002 12:06:43.171" endtime="20241002 12:06:43.171"/>
</kw>
<kw name="Click Button" library="SeleniumLibrary">
<arg>${LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<msg timestamp="20241002 12:06:43.173" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg timestamp="20241002 12:06:43.173" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg timestamp="20241002 12:06:43.173" level="FAIL">No browser is open.</msg>
<status status="FAIL" starttime="20241002 12:06:43.172" endtime="20241002 12:06:43.175"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="20241002 12:06:46.176" level="INFO">Slept 3 seconds</msg>
<status status="PASS" starttime="20241002 12:06:43.175" endtime="20241002 12:06:46.176"/>
</kw>
<kw name="Close Browser" library="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" starttime="20241002 12:06:46.177" endtime="20241002 12:06:46.177"/>
</kw>
<status status="FAIL" starttime="20241002 12:06:43.166" endtime="20241002 12:06:46.177">Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</kw>
<doc>Create a VMS user with 'Administrator' Role</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="FAIL" starttime="20241002 12:06:43.151" endtime="20241002 12:06:46.178">OSError: [WinError 233] No process is on the other end of the pipe

Also teardown failed:
Several failures occurred:

1) No browser is open.

2) OSError: [WinError 233] No process is on the other end of the pipe

3) No browser is open.</status>
</test>
<doc>Add new User to VMS</doc>
<status status="FAIL" starttime="20241002 12:05:56.053" endtime="20241002 12:06:46.180"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="4" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="4" skip="0">HEALTHCHECK_STATUS</stat>
<stat pass="0" fail="4" skip="0">Login</stat>
<stat pass="0" fail="4" skip="0">VMS_HEALTHCHECK</stat>
</tag>
<suite>
<stat pass="0" fail="4" skip="0" id="s1" name="VMS Portal">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="20241002 12:05:58.051" level="WARN">There was error during termination of process</msg>
<msg timestamp="20241002 12:06:31.994" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<msg timestamp="20241002 12:06:32.021" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<msg timestamp="20241002 12:06:32.038" level="WARN">Keyword 'Capture Page Screenshot' could not be run on failure: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=129.0.6668.71)
Stacktrace:
	GetHandleVerifier [0x00007FF71D0AB632+29090]
	(No symbol) [0x00007FF71D01E6E9]
	(No symbol) [0x00007FF71CEDB1CA]
	(No symbol) [0x00007FF71CEAFAF5]
	(No symbol) [0x00007FF71CF5E2E7]
	(No symbol) [0x00007FF71CF75EE1]
	(No symbol) [0x00007FF71CF56493]
	(No symbol) [0x00007FF71CF209B1]
	(No symbol) [0x00007FF71CF21B11]
	GetHandleVerifier [0x00007FF71D3C883D+3294125]
	GetHandleVerifier [0x00007FF71D414423+3604371]
	GetHandleVerifier [0x00007FF71D40A2E7+3563095]
	GetHandleVerifier [0x00007FF71D166F16+797318]
	(No symbol) [0x00007FF71D02986F]
	(No symbol) [0x00007FF71D025454]
	(No symbol) [0x00007FF71D0255E0]
	(No symbol) [0x00007FF71D014A7F]
	BaseThreadInitThunk [0x00007FF85003257D+29]
	RtlUserThreadStart [0x00007FF850A0AF28+40]
</msg>
<msg timestamp="20241002 12:06:46.183" level="ERROR">Calling method 'end_suite' of listener 'C:\Users\<USER>\source\repos\alternative_physical_channels\vms\utility\PostExecutionUpdateV2.py' failed: OSError: [WinError 233] No process is on the other end of the pipe</msg>
</errors>
</robot>
