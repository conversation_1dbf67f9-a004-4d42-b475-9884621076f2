<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.1 on win32)" generated="20240312 16:58:50.920" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\development\future-fit-architecture-portal-docker\tests\Front-End\TC_08_Admin.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 16:58:51.252" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.252"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 16:58:51.252" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'PasswordTR'.</msg>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.252"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 16:58:51.252" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.252"/>
</kw>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.252"/>
</kw>
<test id="s1-t1" name="FFT - Approval - Approve Campaign" line="42">
<kw name="Validates Admin Features">
<arg>Approve campaign</arg>
<arg>T155057361</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_DEV</arg>
<arg>B-Approver Approve campaign</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="20240312 16:58:51.252" level="INFO">Set test documentation to:
Approve campaign</msg>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.252"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="20240312 16:58:51.252" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value 'T155057361'.</msg>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.252"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 16:58:51.303" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 16:58:51.252" endtime="20240312 16:58:51.303"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.303"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.303"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 16:58:51.303" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.303"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 16:58:51.303" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.303"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.303"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 16:58:51.344" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 16:58:51.537" level="INFO">${rc_code} = 0</msg>
<msg timestamp="20240312 16:58:51.537" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 28140 has been terminated.
SUCCESS: The process "chrome.exe" with PID 24840 has been terminated.
SUCCESS: The process "chrome.exe" with PID 9564 has been ter...</msg>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<status status="PASS" starttime="20240312 16:58:51.303" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 16:58:51.537" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E60680&gt;</msg>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 16:58:51.537" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 16:58:51.537" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 16:58:51.537" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 16:58:51.537" endtime="20240312 16:58:51.537"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 16:58:51.537" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:00:02.627" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:00:02.627" endtime="20240312 17:00:02.627"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:00:02.627" endtime="20240312 17:00:02.628"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:00:02.628" endtime="20240312 17:00:02.628"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:00:02.629" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:00:02.629" endtime="20240312 17:00:02.629"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:00:02.629" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:00:02.629" endtime="20240312 17:00:02.629"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:00:02.629" endtime="20240312 17:00:02.629"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:00:02.665" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:00:02.847" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:00:02.847" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:00:02.629" endtime="20240312 17:00:02.847"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:00:02.847" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:00:02.847" endtime="20240312 17:00:02.848"/>
</kw>
<status status="PASS" starttime="20240312 17:00:02.847" endtime="20240312 17:00:02.848"/>
</kw>
<status status="PASS" starttime="20240312 17:00:02.628" endtime="20240312 17:00:02.848"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:00:02.848" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E487A0&gt;</msg>
<status status="PASS" starttime="20240312 17:00:02.848" endtime="20240312 17:00:02.848"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:00:02.848" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:00:02.848" endtime="20240312 17:00:02.848"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:00:02.849" endtime="20240312 17:00:02.849"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:00:02.849" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:00:02.849" endtime="20240312 17:00:02.849"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:00:02.849" endtime="20240312 17:00:02.849"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:00:02.849" endtime="20240312 17:00:02.849"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:00:02.849" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:00:02.849" endtime="20240312 17:00:02.849"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:00:02.849" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:01:10.567" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:01:10.567" endtime="20240312 17:01:10.567"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:01:10.567" endtime="20240312 17:01:10.567"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:01:10.567" endtime="20240312 17:01:10.571"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:01:10.572" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:01:10.571" endtime="20240312 17:01:10.572"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:01:10.572" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:01:10.572" endtime="20240312 17:01:10.572"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:01:10.572" endtime="20240312 17:01:10.573"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:01:10.605" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:01:10.783" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:01:10.783" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:01:10.573" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:01:10.783" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<status status="PASS" starttime="20240312 17:01:10.571" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:01:10.783" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E63F20&gt;</msg>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:01:10.783" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:01:10.783" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:01:10.783" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:01:10.783" endtime="20240312 17:01:10.783"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:01:10.783" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:02:13.170" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:02:13.170" endtime="20240312 17:02:13.170"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:02:13.170" endtime="20240312 17:02:13.170"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:02:13.170" endtime="20240312 17:02:13.178"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:02:13.180" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:02:13.180" endtime="20240312 17:02:13.180"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:02:13.180" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:02:13.180" endtime="20240312 17:02:13.180"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:02:13.180" endtime="20240312 17:02:13.181"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:02:13.225" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:02:13.387" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:02:13.387" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:02:13.181" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:02:13.387" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<status status="PASS" starttime="20240312 17:02:13.179" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:02:13.387" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E9A930&gt;</msg>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:02:13.387" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:02:13.387" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:02:13.387" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:02:13.387" endtime="20240312 17:02:13.387"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:02:13.387" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:03:15.699" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:03:15.699" endtime="20240312 17:03:15.699"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:03:15.699" endtime="20240312 17:03:15.701"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:03:15.701" endtime="20240312 17:03:15.701"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:03:15.703" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:03:15.702" endtime="20240312 17:03:15.703"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:03:15.703" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:03:15.703" endtime="20240312 17:03:15.703"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:03:15.703" endtime="20240312 17:03:15.704"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:03:15.756" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:03:15.919" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:03:15.919" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:03:15.704" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:03:15.919" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<status status="PASS" starttime="20240312 17:03:15.702" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:03:15.919" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E4B6B0&gt;</msg>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:03:15.919" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:03:15.919" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:03:15.919" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:03:15.919" endtime="20240312 17:03:15.919"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:03:15.919" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:04:18.117" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:04:18.116" endtime="20240312 17:04:18.117"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:04:18.117" endtime="20240312 17:04:18.117"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:04:18.117" endtime="20240312 17:04:18.118"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:04:18.120" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:04:18.119" endtime="20240312 17:04:18.120"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:04:18.120" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:04:18.120" endtime="20240312 17:04:18.120"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:04:18.120" endtime="20240312 17:04:18.121"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:04:18.167" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:04:18.340" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:04:18.340" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:04:18.121" endtime="20240312 17:04:18.340"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:04:18.340" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:04:18.340" endtime="20240312 17:04:18.341"/>
</kw>
<status status="PASS" starttime="20240312 17:04:18.340" endtime="20240312 17:04:18.341"/>
</kw>
<status status="PASS" starttime="20240312 17:04:18.119" endtime="20240312 17:04:18.341"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:04:18.342" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E63FB0&gt;</msg>
<status status="PASS" starttime="20240312 17:04:18.341" endtime="20240312 17:04:18.342"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:04:18.342" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:04:18.342" endtime="20240312 17:04:18.342"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:04:18.342" endtime="20240312 17:04:18.342"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:04:18.342" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:04:18.342" endtime="20240312 17:04:18.342"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:04:18.342" endtime="20240312 17:04:18.342"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:04:18.342" endtime="20240312 17:04:18.343"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:04:18.343" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:04:18.343" endtime="20240312 17:04:18.343"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:04:18.343" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:05:30.227" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:05:30.227" endtime="20240312 17:05:30.227"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:05:30.227" endtime="20240312 17:05:30.227"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:05:30.227" endtime="20240312 17:05:30.227"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:05:30.228" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:05:30.228" endtime="20240312 17:05:30.228"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:05:30.228" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:05:30.228" endtime="20240312 17:05:30.228"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:05:30.228" endtime="20240312 17:05:30.229"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:05:30.263" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:05:30.435" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:05:30.435" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:05:30.229" endtime="20240312 17:05:30.435"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:05:30.435" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:05:30.435" endtime="20240312 17:05:30.436"/>
</kw>
<status status="PASS" starttime="20240312 17:05:30.435" endtime="20240312 17:05:30.436"/>
</kw>
<status status="PASS" starttime="20240312 17:05:30.228" endtime="20240312 17:05:30.436"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:05:30.436" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E60530&gt;</msg>
<status status="PASS" starttime="20240312 17:05:30.436" endtime="20240312 17:05:30.436"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:05:30.436" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:05:30.436" endtime="20240312 17:05:30.436"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:05:30.436" endtime="20240312 17:05:30.436"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:05:30.436" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:05:30.436" endtime="20240312 17:05:30.436"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:05:30.436" endtime="20240312 17:05:30.437"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:05:30.437" endtime="20240312 17:05:30.437"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:05:30.437" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:05:30.437" endtime="20240312 17:05:30.437"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:05:30.437" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:06:32.233" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:06:32.233" endtime="20240312 17:06:32.233"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:06:32.233" endtime="20240312 17:06:32.240"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:06:32.240" endtime="20240312 17:06:32.240"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:06:32.242" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:06:32.242" endtime="20240312 17:06:32.242"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:06:32.243" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:06:32.242" endtime="20240312 17:06:32.243"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:06:32.243" endtime="20240312 17:06:32.243"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:06:32.281" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:06:32.487" level="INFO">${rc_code} = 0</msg>
<msg timestamp="20240312 17:06:32.487" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 31208 has been terminated.
SUCCESS: The process "chrome.exe" with PID 30296 has been terminated.
SUCCESS: The process "chrome.exe" with PID 29796 has been te...</msg>
<status status="PASS" starttime="20240312 17:06:32.243" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<status status="PASS" starttime="20240312 17:06:32.241" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:06:32.487" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E9A4E0&gt;</msg>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:06:32.487" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:06:32.487" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:06:32.487" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:06:32.487" endtime="20240312 17:06:32.487"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:06:32.487" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:07:38.285" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="20240312 17:07:38.285" endtime="20240312 17:07:38.285"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:07:38.285" endtime="20240312 17:07:38.286"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:07:38.286" endtime="20240312 17:07:38.287"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:07:38.288" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="20240312 17:07:38.288" endtime="20240312 17:07:38.288"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="20240312 17:07:38.288" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="20240312 17:07:38.288" endtime="20240312 17:07:38.289"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="20240312 17:07:38.289" endtime="20240312 17:07:38.289"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="20240312 17:07:38.332" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="20240312 17:07:38.495" level="INFO">${rc_code} = 128</msg>
<msg timestamp="20240312 17:07:38.495" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
<status status="PASS" starttime="20240312 17:07:38.289" endtime="20240312 17:07:38.495"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="20240312 17:07:38.496" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="20240312 17:07:38.496" endtime="20240312 17:07:38.496"/>
</kw>
<status status="PASS" starttime="20240312 17:07:38.496" endtime="20240312 17:07:38.496"/>
</kw>
<status status="PASS" starttime="20240312 17:07:38.287" endtime="20240312 17:07:38.496"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:07:38.497" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x0000026736E9B470&gt;</msg>
<status status="PASS" starttime="20240312 17:07:38.496" endtime="20240312 17:07:38.497"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="20240312 17:07:38.497" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="20240312 17:07:38.497" endtime="20240312 17:07:38.497"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:07:38.497" endtime="20240312 17:07:38.497"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="20240312 17:07:38.497" level="INFO">${user_home} = C:\Users\<USER>\Users\ab022bc</msg>
<status status="PASS" starttime="20240312 17:07:38.497" endtime="20240312 17:07:38.497"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:07:38.497" endtime="20240312 17:07:38.497"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="20240312 17:07:38.497" endtime="20240312 17:07:38.497"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="20240312 17:07:38.498" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="20240312 17:07:38.497" endtime="20240312 17:07:38.498"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="20240312 17:07:38.498" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>