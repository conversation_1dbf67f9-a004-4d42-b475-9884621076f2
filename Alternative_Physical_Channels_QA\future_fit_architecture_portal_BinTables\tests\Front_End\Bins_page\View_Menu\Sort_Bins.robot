*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/View_Bins_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Sort Bins displayed on the View Entries page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${SORT_DATA}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal         ${BASE_URL}
    When The User clicks Bins Menu
    And The user sorts the Bins results                                               ${SORT_DATA}
    Then The returned BINS must be sorted in ascending order                          ${SORT_DATA}

| *** Test Cases ***                                                                                                                       |        *DOCUMENTATION*    		   |         *BASE_URL*                  |      *SORT_DATA*      |
| Sort Bins displayed on the 'Vew Entries' page using the Bin Number column.            | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Bin Number        |
| Sort Bins displayed on the 'Vew Entries' page using the Bin Type column.              | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Bin Type          |
| Sort Bins displayed on the 'Vew Entries' page using the Action Date column.           | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Action Date       |
| Sort Bins displayed on the 'Vew Entries' page using the Captured Date column.         | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Captured Date     |
| Sort Bins displayed on the 'Vew Entries' page using the Captured By column.           | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Captured By       |
| Sort Bins displayed on the 'Vew Entries' page using the Outcome column.               | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Outcome           |
#| Sort Bins displayed on the 'Vew Entries' page using the Review Date column.           | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Review Date       |
| Sort Bins displayed on the 'Vew Entries' page using the Reviewed By column.           | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Reviewed By       |
| Sort Bins displayed on the 'Vew Entries' page using the Review Status column.         | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Review Status     |
| Sort Bins displayed on the 'Vew Entries' page using the Rejected Comment column.      | Sort Bins displayed on the View Entries page     | Sort Bins linked to a Bin Type.   |           ${EMPTY}                  |     Rejected Comment  |
