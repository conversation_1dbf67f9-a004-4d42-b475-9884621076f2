*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation              MTGLA Navigation Keywords

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                             RequestsLibrary
Library                                             Collections    WITH NAME    Collections
Library                                             String
Library                                             DatabaseLibrary
Library                                             ../utility/DatabaseUtility.py
Library                                             ../utility/Common_Functions.py
Library                                             DateTime
Library                                             re
Library                                             BuiltIn
Variables                                           ../utility/SQL.py

#***********************************PROJECT RESOURCES***************************************
#Acronymn Buster
#FT= Flagged Transactions
#GLT= GL Transactions
#ST= Subledger Transactions 
#TT= Tiff Transactions 


*** Variables ***
#General EOD Xpath Locators 
${EOD_DATE_FILTER}                              xpath=//*[text()[normalize-space(.)='Filter']]
${EOD_RUN_BUTTON}                               xpath=//*[text()[normalize-space(.)='Run']]
${EOD_DASHBOARD_DATE_FIELD}                     xpath=//*[@type='date']
${PROVINCE_LOCATOR}                             xpath=//tr[@style="font-weight:bolder"]/td[@class="w3-button"]
${COST_CENTRES_LOCATOR}                         xpath=//tr[contains(@class, 'w3-medium')]/td[contains(@class, 'w3-button')]
${ACCOUNTS_LOCATOR}                             xpath=//tbody/tr/td/a
${FLAGGED_TRANSACTIONS_MENU}                    xpath=//*[text()[normalize-space(.)='Flagged Transactions']]
${NEXT_PAGE_BUTTON}                             xpath=//*[@id='glTransactions_paginate']//a[contains(text(), 'Next')]
${COMMENT_BOX}                                  xpath=//*[@name='Comment']
${SAVE_BUTTON}                                  xpath=//*[@id='savebutton']
${ATTESTATION_MENU}                             xpath=/html/body/div[4]/div/div[2]/div/div[2]/div[1]/button[6]
${ATTESTATION_ADD_COMMENT}                      xpath=//*[text()[normalize-space(.)='Add Comment']]
${ATTESTATION_UPLOAD_DOCUMENT}                  xpath=//*[text()[normalize-space(.)='Upload Document']]
${CLOSE_BUTTON}                                 xpath=//*[text()[normalize-space(.)='Close']]
${CHOOSE_FILE_LOCATOR}                          xpath//*[@id='fileUpload1'][@type='file']
${FILE_PATH}                                    C:\Users\<USER>\OneDrive - Absa\My Documents\Alternative Physical Channels\ATM_SQUAD\MTGLA\Automation file upload\MTGLA TEST FILE UPLOAD.docx

#Recon Transactions Menu
${RT_EFFECTIVE_DATE_HEADER_LOCATOR}             xpath=//*[@id="glTransactions"]/thead/tr[2]/th[1]
${RT_EFFECTIVE_DATE_LOCATOR}                    xpath=//table[@id='glTransactions']//tbody//tr//td[@class='w3-left-align sorting_1' and normalize-space(text()) != '']
${RT_TRANSACTION_DETAILS_LOCATOR}               xpath=//table[@id='glTransactions']//tbody//tr//td[2]
${RT_GL_DETAILS_LOCATOR}                        xpath=//table[@id='glTransactions']//tbody//tr//td[3]
${RT_SUBLEDGER_DETAILS_LOCATOR}                 xpath=//table[@id='glTransactions']//tbody//tr//td[4]
${RT_SUBLEDGER_LOCATOR}                         xpath=//table[@id='glTransactions']//tbody//tr//td[5]
${RT_SUBLEDGER_GL_LOCATOR}                      xpath=//table[@id='glTransactions']//tbody//tr//td[6]
${RT_FLAGGED_LOCATOR}                           xpath=//table[@id='glTransactions']//tbody//tr//td[7]
${RT_AGING_LOCATOR}                             xpath=//table[@id='glTransactions']//tbody//tr//td[8]
${RT_DATE_CLEARED_LOCATOR}                      xpath=//table[@id='glTransactions']//tbody//tr//td[9]
${RT_COMMENTS(CLICK)_LOCATOR}                   xpath=//table[@id='glTransactions']//tbody//tr//td[10]
${RT_SUBSTANTIATED_LOCATOR}                     xpath=//table[@id='glTransactions']//tbody//tr//td[11]
${RT_AMOUNT_LOCATOR}                            xpath=//table[@id='glTransactions']//tbody//tr//td[14]
${RT_PAGINATION_XPATH}                          xpath=//*[@id='glTransactions_paginate']
${RT_NEXT_PAGE_BUTTON}                          xpath=//*[@id="glTransactions_next"]
${RT_FIRST_PAGE_BUTTON}                         xpath=//*[@id="glTransactions_paginate"]/span/a[1]

#Flagged Transactions Menu
${FLAGGED_TRANSACTIONS_MENU}                    xpath=//*[text()[normalize-space(.)='Flagged Transactions']]
${FT_EFFECTIVE_DATE_HEADER_LOCATOR}             xpath=//*[@id="flagTransactions"]/thead/tr[2]/th[1]
${FT_PAGINATION_XPATH}                          xpath=//*[@id="flagTransactions_paginate"]
${FT_NEXT_PAGE_BUTTON}                          xpath=//*[@id="flagTransactions_next"]
${FT_First_Page_Button}                         xpath=//*[@id="flagTransactions_paginate"]/span/a[1]
${FT_EFFECTIVE_DATE_LOCATOR}                    xpath=//table[@id='flagTransactions']//td[@class='w3-left-align sorting_1']
${FT_JOURNAL_LOCATOR}                           xpath=//table[@id='flagTransactions']//td[@class='w3-left-align']
${FT_GL_DETAILS_LOCATOR}                        xpath=//table[@id='flagTransactions']//tr//td[3]         #Exclude out Audit Header //table[@id='flagTransactions']//tr//td[3]//a
${FT_SL_DETAILS_LOCATOR}                        xpath=//table[@id='flagTransactions']//tr//td[4]         #Extract out the Rand Amount 
${FT_SUB_LEDGER_LOCATOR}                        xpath=//table[@id='flagTransactions']//tr//td[5]         #Extract out the Rand Amount
${FT_SLID_LOCATOR}                              xpath=//table[@id='flagTransactions']//tr//td[6]         #Extract out the Rand Amount
${FT_FLAGGED_LOCATOR}                           xpath=//table[@id='flagTransactions']//tr//td[7]
${FT_AGING_LOCATOR}                             xpath=//table[@id='flagTransactions']//tr//td[8]
${FT_DATE_CLEARED_LOCATOR}                      xpath=//table[@id='flagTransactions']//tr//td[9]
${FT_COMMENTS(CLICK)_LOCATOR}                   xpath=//table[@id='flagTransactions']//tr//td[10]
${FT_SUBSTANTIATED_LOCATOR}                     xpath=//table[@id='flagTransactions']//tr//td[11]
${FT_DEBIT_LOCATOR}                             xpath=//table[@id='flagTransactions']//tr//td[12]
${FT_CREDIT_LOCATOR}                            xpath=//table[@id='flagTransactions']//tr//td[13]
${FT_AMOUNT_LOCATOR}                            xpath=//table[@id='flagTransactions']//tr//td[14]

#GL Transactions Menu
${GL_TRANSACTIONS_MENU_LOCATOR}                 xpath=//*[text()[normalize-space(.)='GL Transactions']]
${GL_EFFECTIVE_DATE_HEADER_LOCATOR}             xpath=//*[@id="glsTransactions"]/thead/tr[2]/th[1]
${GL_EFFECTIVE_DATE_LOCATOR}                    xpath=//table[@id='glsTransactions']//tbody//tr//td[@class='w3-left-align sorting_1' and normalize-space(text()) != '']
${GL_JOURNAL_LOCATOR}                           xpath=//table[@id='glsTransactions']//tbody//tr//td[2]
${GL_DETAILS_LOCATOR}                           xpath=//table[@id='glsTransactions']//tbody//tr//td[3]
${GL_SLID_LOCATOR}                              xpath=//table[@id='glsTransactions']//tbody//tr//td[4]
${GL_AMOUNT_LOCATOR}                            xpath=//table[@id='glsTransactions']//tbody//tr//td[7]
${GL_PAGINATION_XPATH}                          xpath=//*[@id='glsTransactions_paginate']
${GL_NEXT_PAGE_BUTTON}                          xpath=//*[@id="glsTransactions_next"]
${GL_FIRST_PAGE_BUTTON}                         xpath=//*[@id="glsTransactions_paginate"]/span/a[1]

#Subledger Transactions Menu
${SL_TRANSACTIONS_MENU_LOCATOR}                 xpath=//*[text()[normalize-space(.)='Subledger Transactions']]
${SL_PAGINATION_XPATH}                          xpath=//*[@id='subTransactions_paginate']    
${SL_EFFECTIVE_DATE_HEADER_LOCATOR}             xpath=//*[@id="subTransactions"]/thead/tr[2]/th[1]
${SL_EFFECTIVE_DATE_LOCATOR}                    xpath=//table[@id='subTransactions']//tbody//tr//td[@class='w3-left-align sorting_1' and normalize-space(text()) != '']
${SL_TELLER_NUMBER_LOCATOR}                     xpath=//table[@id='subTransactions']//tbody//tr//td[2]
${SL_FUNCTION_CODE_LOCATOR}                     xpath=//table[@id='subTransactions']//tbody//tr//td[3]
${SL_SUBLEDGER_LOCATOR}                         xpath=//table[@id='subTransactions']//tbody//tr//td[4]
${SL_DETAILS_LOCATOR}                           xpath=//table[@id='subTransactions']//tbody//tr//td[6]
${SL_AMOUNT_LOCATOR}                            xpath=//table[@id='subTransactions']//tbody//tr//td[9]
${SL_NEXT_PAGE_BUTTON}                          xpath=//*[@id="subTransactions_next"]
${SL_FIRST_PAGE_BUTTON}                         xpath=//*[@id="subTransactions_paginate"]/span/a[1]

#Tiff Transactions Menu 
${TIFF_TRANSACTIONS_MENU_LOCATOR}               xpath=//*[text()[normalize-space(.)='Tiff Transactions']]
${TT_PAGINATION_XPATH}                          xpath=//*[@id="tiffTransactions_paginate"]
${TT_EFFECTIVE_DATE_HEADER_LOCATOR}             xpath=//*[@id="tiffTransactions"]/thead/tr[2]/th[1]
${TT_EFFECTIVE_DATE_LOCATOR}                    xpath=//table[@id='tiffTransactions']//tbody//tr//td[@class='w3-left-align sorting_1' and normalize-space(text()) != '']
${TT_TELLER_NUMBER_LOCATOR}                     xpath=//table[@id='tiffTransactions']//tbody//tr//td[2]
${TT_MODE_LOCATOR}                              xpath=//table[@id='tiffTransactions']//tbody//tr//td[3]
${TT_JOURNAL_NO_LOCATOR}                        xpath=//table[@id='tiffTransactions']//tbody//tr//td[4]
${TT_ACCOUNT_NO_LOCATOR}                        xpath=//table[@id='tiffTransactions']//tbody//tr//td[5]
${TT_FUNCTION_CODE_LOCATOR}                     xpath=//table[@id='tiffTransactions']//tbody//tr//td[6]
${TT_AMOUNT_LOCATOR}                            xpath=//table[@id='tiffTransactions']//tbody//tr//td[9]
${TT_NEXT_PAGE_BUTTON}                          xpath=//*[@id="tiffTransactions_next"]
${TT_FIRST_PAGE_BUTTON}                         xpath=//*[@id="tiffTransactions_paginate"]/span/a[1]


*** Keywords ***
the user accesses an account with Flagged Transactions
    #Get Pronvinces Displayed 
    ${PROVINCES}=    Get WebElements    ${PROVINCE_LOCATOR}
    ${ALL_PROVINCES}=    Create List
    FOR    ${PROVINCE}    IN    @{PROVINCES}
        ${PROVINCE_NAME}=    Get Text    ${PROVINCE}
        Append To List    ${ALL_PROVINCES}    ${PROVINCE_NAME}
    END
    Log Many    ${ALL_PROVINCES}

    ${PROVINCE_COUNT}=    Get Length    ${ALL_PROVINCES}
    
    Log    Total provinces displayed: ${PROVINCE_COUNT}
    
    #Fail if there are no provinces displayed and Click the first one
    Run Keyword If    ${PROVINCE_COUNT} == 0    Fail    No provinces displayed on EOD Dashboard!
    ...    ELSE    Click Element    ${PROVINCES}[0]
    
    #Click on first availabe province 
    ${PROVINCE_SELECTED}=    SeleniumLibrary.Get Text        ${PROVINCES}[0]

    SeleniumLibrary.Capture Page Screenshot
    
    Log    Clicked on:${PROVINCE_SELECTED}
    

    #Get Cost Centres Displayed for that province 
    ${COST_CENTRES}=    Get WebElements    ${COST_CENTRES_LOCATOR}
    ${ALL_COST_CENTRES}=    Create List
    FOR    ${COST_CENTRE}    IN    @{COST_CENTRES}
        ${COST_CENTRE_NAME}    SeleniumLibrary.Get Text    ${COST_CENTRE}
        Append To List    ${ALL_COST_CENTRES}    ${COST_CENTRE_NAME}

    END

    Log Many    ${ALL_COST_CENTRES}

    ${COST_CENTRE_COUNT}=    Get Length    ${ALL_COST_CENTRES}
    
    Log    Total provinces displayed: ${COST_CENTRE_COUNT}


    #Fail if there are no cost centres displayed and Click the first one
    Run Keyword If    ${COST_CENTRE_COUNT} == 0    Fail    No cost centres displayed on EOD Dashboard!
    ...    ELSE    Click Element    ${COST_CENTRES}[0]
    
    #Click on the first available cost centre
    ${COST_CENTRE_SELECTED}=    SeleniumLibrary.Get Text        ${COST_CENTRES}[0]

    SeleniumLibrary.Capture Page Screenshot
    
    Log    Clicked on:${COST_CENTRE_SELECTED}
    
    
    #Get all Accounts displayed 
    ${ACCOUNTS}=    Get WebElements    ${ACCOUNTS_LOCATOR}
    ${ALL_ACCOUNTS}=    Create List
    FOR    ${ACCOUNT}    IN    @{ACCOUNTS}
        ${ACCOUNT_NAME}    SeleniumLibrary.Get Text    ${ACCOUNT}
        Append To List    ${ALL_ACCOUNTS}    ${ACCOUNT_NAME}
    END

    Log Many    ${ALL_ACCOUNTS}

    ${ACCOUNT_COUNT}=    Get Length    ${ALL_ACCOUNTS}

    Log    Total accounts displayed: ${ACCOUNT_COUNT}

    #Fail if there are no accounts displayed and Click the first one
    ${ACCOUNT_SELECTED}=    SeleniumLibrary.Get Text        ${ACCOUNTS}[0]
    Run Keyword If    ${ACCOUNT_COUNT} == 0    Fail    No accounts displayed on Dashboard!
    ...    ELSE    Click Element    ${ACCOUNTS}[0]
    
    
    SeleniumLibrary.Capture Page Screenshot
    
    Log    Clicked on: ${ACCOUNT_SELECTED}

    Sleep    5s

    #Navigate to 'Flagged Transactions Menu'
    Wait until element is enabled    ${FLAGGED_TRANSACTIONS_MENU}
    Click Element    ${FLAGGED_TRANSACTIONS_MENU}
    Sleep    2s

    #Check Flagged Transactions
    ${has_more_pages}=    Set Variable    True
    ${flagged_transactions_found}=    Set Variable    False
    FOR    ${index}    IN RANGE    1    100  # Adjust range if needed
        Run Keyword If    '${has_more_pages}' == 'True'    the user adds a comment to substantiate the flagged transactions 
        Run Keyword If    '${has_more_pages}' == 'True'    Click Next Page Button
        Run Keyword If    '${has_more_pages}' == 'True'    Check If More Pages Exist
        Run Keyword If    '${has_more_pages}' == 'False'   Log    No more pages of flagged transactions found.
        Run Keyword If    '${flagged_transactions_found}' == 'False'    SKIP    WARN
    END


the user adds a comment to substantiate the flagged transactions 
    ${flagged_transactions}=    Get WebElements    xpath=//*[@id="flagTransactions"]/tbody//tr
    ${transaction_count}=    Get Length    ${flagged_transactions}

    Run Keyword If    ${transaction_count} > 0    Set Global Variable    ${flagged_transactions_found}    True
    Log    Total flagged transactions found: ${transaction_count}

    #Store all flagged transaction elements for later use
    Set Global Variable    ${ALL_FLAGGED_TRANSACTIONS}    ${flagged_transactions}

    Run Keyword If    '${flagged_transactions_found}' == 'False'    Log    No flagged transactions to substantiate.

    FOR    ${transaction}    IN    @{ALL_FLAGGED_TRANSACTIONS}
        Click Element    xpath=//td[@class="w3-center "]/a[contains(@data-url, '/Eod/AddComment/')]
        Wait until element is visible    ${COMMENT_BOX}
        
        #Input a comment
        Click Element    ${COMMENT_BOX}
        Input Text    ${COMMENT_BOX}    Automated Comment: Test Script 

        #Optional File Upload 

        #Input Responsible to Clear 

        #Input BU Affected 
        
        #Click Save 
        Click Element    ${SAVE_BUTTON}
        
        Sleep    2s    
    END

    Log    Completed substantiating all flagged transactions 

Click Next Page Button
    Wait Until Element Is Visible    ${NEXT_PAGE_BUTTON}
    Click Element    ${NEXT_PAGE_BUTTON}

Check If More Pages Exist
    ${has_more_pages}=    Run Keyword And Return Status    Element Should Be Visible    ${NEXT_PAGE_BUTTON}

the user navigates to the Attestation menu on the account
    #Navigate to 'Attestation Menu'
    Wait until element is enabled    ${ATTESTATION_MENU}
    Click Element    ${ATTESTATION_MENU}
    Sleep    2s

the user completes the Add Comment and Upload Document actions in the Attestation menu
    #Add comment on Attestation Menu
        Wait until Element is enabled    ${ATTESTATION_ADD_COMMENT}
        Click Element    ${ATTESTATION_ADD_COMMENT}
        Wait until element is visible    ${COMMENT_BOX}

        #Input a Comment
        Click Element    ${COMMENT_BOX}
        Input Text    ${COMMENT_BOX}    Generated by Automation Script

        #Click Save 
        Click Element    ${SAVE_BUTTON}
        Sleep    3s 

    #Upload Document 
    Wait Until Element Is Visible    ${ATTESTATION_UPLOAD_DOCUMENT}
    Click Element     ${ATTESTATION_UPLOAD_DOCUMENT}

        #Input Text
        Click Element    ${COMMENT_BOX}
        Input Text    ${COMMENT_BOX}    Generated by Automation Script

        #Upload Document   
        Choose File    ${CHOOSE_FILE_LOCATOR}    ${FILE_PATH}
        Sleep    2s  
        
        #Click Save 
        Click Element    ${SAVE_BUTTON}
        Sleep    3s 

the user clicks on an account
    #Get Pronvinces Displayed 
    ${PROVINCES}=    Get WebElements    ${PROVINCE_LOCATOR}
    ${ALL_PROVINCES}=    Create List
    FOR    ${PROVINCE}    IN    @{PROVINCES}
        ${PROVINCE_NAME}=    Get Text    ${PROVINCE}
        Append To List    ${ALL_PROVINCES}    ${PROVINCE_NAME}
    END
    Log Many    ${ALL_PROVINCES}

    ${PROVINCE_COUNT}=    Get Length    ${ALL_PROVINCES}
    
    Log    Total provinces displayed: ${PROVINCE_COUNT}
    
    #Fail if there are no provinces displayed and Click the first one
    Run Keyword If    ${PROVINCE_COUNT} == 0    Fail    No provinces displayed on EOD Dashboard!
    ...    ELSE    Click Element    ${PROVINCES}[0]
    
    #Click on first availabe province 
    ${PROVINCE_SELECTED}=    SeleniumLibrary.Get Text        ${PROVINCES}[0]

    SeleniumLibrary.Capture Page Screenshot
    
    Log    Clicked on:${PROVINCE_SELECTED}
    

    #Get Cost Centres Displayed for that province 
    ${COST_CENTRES}=    Get WebElements    ${COST_CENTRES_LOCATOR}
    ${ALL_COST_CENTRES}=    Create List
    FOR    ${COST_CENTRE}    IN    @{COST_CENTRES}
        ${COST_CENTRE_NAME}    SeleniumLibrary.Get Text    ${COST_CENTRE}
        Append To List    ${ALL_COST_CENTRES}    ${COST_CENTRE_NAME}

    END

    Log Many    ${ALL_COST_CENTRES}

    ${COST_CENTRE_COUNT}=    Get Length    ${ALL_COST_CENTRES}
    
    Log    Total provinces displayed: ${COST_CENTRE_COUNT}


    #Fail if there are no cost centres displayed and Click the first one
    Run Keyword If    ${COST_CENTRE_COUNT} == 0    Fail    No cost centres displayed on EOD Dashboard!
    ...    ELSE    Click Element    ${COST_CENTRES}[0]
    
    #Click on the first available cost centre
    ${COST_CENTRE_SELECTED}=    SeleniumLibrary.Get Text        ${COST_CENTRES}[0]

    SeleniumLibrary.Capture Page Screenshot
    
    Log    Clicked on:${COST_CENTRE_SELECTED}
    
    ${cost_centre_numeric}    Split string    ${COST_CENTRE_SELECTED}   
    ${cost_centre_numeric}    Set Variable    ${cost_centre_numeric[0]}
    Set Global Variable    ${COST_CENTRE_NUMBER}    ${cost_centre_numeric}
    
    
    #Get all Accounts displayed 
    ${ACCOUNTS}=    Get WebElements    ${ACCOUNTS_LOCATOR}
    ${ALL_ACCOUNTS}=    Create List
    FOR    ${ACCOUNT}    IN    @{ACCOUNTS}
        ${ACCOUNT_NAME}    SeleniumLibrary.Get Text    ${ACCOUNT}
        Append To List    ${ALL_ACCOUNTS}    ${ACCOUNT_NAME}
    END

    Log Many    ${ALL_ACCOUNTS}

    ${ACCOUNT_COUNT}=    Get Length    ${ALL_ACCOUNTS}

    Log    Total accounts displayed: ${ACCOUNT_COUNT}

    #Fail if there are no accounts displayed and Click the first one
    ${ACCOUNT_SELECTED}=    SeleniumLibrary.Get Text        ${ACCOUNTS}[0]
    Run Keyword If    ${ACCOUNT_COUNT} == 0    Fail    No accounts displayed on Dashboard!
    ...    ELSE    Click Element    ${ACCOUNTS}[0]
    
    
    SeleniumLibrary.Capture Page Screenshot
    
    Log    Clicked on: ${ACCOUNT_SELECTED}

    ${account_number_numeric}    Split string    ${ACCOUNT_SELECTED}   
    ${account_number_numeric}    Set Variable    ${account_number_numeric[0]}
    Set Global Variable    ${ACCOUNT_NUMBER}    ${account_number_numeric}

    Sleep    5s



the user verifies the "Recon Transactions" menu under "Account" on the front end
    #If there is no Recon Transactions displayed, test should not fail, rather skip 
    ${STATUS}=    Run Keyword And Return Status    Wait until Element is enabled    ${RT_EFFECTIVE_DATE_HEADER_LOCATOR}
    Run keyword if    ${status} == False       Skip    There are no Recon Transactions displayed for account: ${ACCOUNT_NUMBER} under Cost Centre: ${COST_CENTRE_NUMBER}
    Run keyword if    ${status} == True     Proceeding to verify "Recon Transactions" menu under "Account" on the front end

Proceeding to verify "Recon Transactions" menu under "Account" on the front end
    #Get the pagination elements (if there are multiple pages)
    ${pagination_elements}=    Get WebElements    ${RT_PAGINATION_XPATH}//a
    Log    Pagination Elements: ${pagination_elements}  # Log the pagination elements

    #Check if the pagination elements are not empty (before starting the loop)
    ${pagination_length}=    Get Length    ${pagination_elements}
    Run Keyword If    ${pagination_length} == 0    Fail    No pagination elements found

    #Get the last page number 
    ${last_page_text}=    Get Text    ${pagination_elements[-2]}  # Get the text of the second to last element
    Log    Last Page Text: ${last_page_text}  # Log the last page text

    #Handle case where last page text is empty or invalid
    Run Keyword If    '${last_page_text}' == ''    Fail    Last page number not found or is empty
    ${num_pages}=    Convert To Integer    ${last_page_text}


    #Get All Effective Dates for the Recon Transactions Menu
    ${frontend_rt_effective_dates}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        # Get the effective dates from the current page
        ${ALL_RT_EFFECTIVE_DATES}=    Get WebElements    ${RT_EFFECTIVE_DATE_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${date_element}    IN    @{ALL_RT_EFFECTIVE_DATES}
            ${date_text}=    Get Text    ${date_element}
            Append To List    ${frontend_rt_effective_dates}    ${date_text}
        END
        #Log the collected effective dates for this page
        Log    Effective Dates for Page ${page_num}: ${frontend_rt_effective_dates}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load
        Wait Until Element Is Visible    ${RT_EFFECTIVE_DATE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Effective Dates: ${frontend_rt_effective_dates}

    Set Suite Variable    ${frontend_rt_effective_dates}

    ${effective_date_length}=    Get length    ${frontend_rt_effective_dates}
    Log    ${effective_date_length}




    #Get All Transaction Details for the Recon Transactions Menu
    ${frontend_rt_transaction_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_TRANSACTIONS}=    Get WebElements    ${RT_TRANSACTION_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${transaction_detail_element}    IN    @{ALL_RT_TRANSACTIONS}
            ${transaction_text}=    Get Text    ${transaction_detail_element}
            Append To List    ${frontend_rt_transaction_details}    ${transaction_text}
        END

        #Log the collected effective dates for this page
        Log    Transactions for Page ${page_num}: ${frontend_rt_transaction_details}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_TRANSACTION_DETAILS_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Transactions: ${frontend_rt_transaction_details}

    Set Suite Variable    ${frontend_rt_transaction_details}

    ${transaction_length}=    Get length    ${frontend_rt_transaction_details}
    Log    ${transaction_length}




    #Get All GL Details for the Recon Transactions Menu
    ${frontend_rt_gl_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_GLDETAILS}=    Get WebElements    ${RT_GL_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_gl_detail_element}    IN    @{ALL_RT_GLDETAILS}
            ${rt_gldetail_text}=    Get Text    ${rt_gl_detail_element}
            Append To List    ${frontend_rt_gl_details}    ${rt_gldetail_text}
        END

        #Log the collected effective dates for this page
        Log   RT GL Details for Page ${page_num}: ${frontend_rt_gl_details}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_GL_DETAILS_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All GL Details: ${frontend_rt_gl_details}

    Set Suite Variable    ${frontend_rt_gl_details}

    ${rt_gldetail_length}=    Get length    ${frontend_rt_gl_details}
    Log    ${rt_gldetail_length}



    #Get All SUBLEDGER_DETAILS for the Recon Transactions Menu
    ${frontend_rt_subledger_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_SUBLEDGER_DETAILS}=    Get WebElements    ${RT_SUBLEDGER_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_subledger_detail_element}    IN    @{ALL_RT_SUBLEDGER_DETAILS}
            ${rt_subledger_detail_text}=    Get Text    ${rt_subledger_detail_element}
            Append To List    ${frontend_rt_subledger_details}    ${rt_subledger_detail_text}
        END

        #Log the collected effective dates for this page
        Log   RT Subledger Details for Page ${page_num}: ${frontend_rt_subledger_details}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_SUBLEDGER_DETAILS_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Subledger Details: ${frontend_rt_subledger_details}

    Set Suite Variable    ${frontend_rt_subledger_details}

    ${rt_subledger_detail_length}=    Get length    ${frontend_rt_subledger_details}
    Log    ${rt_subledger_detail_length}




    #Get All SUBLEDGER for the Recon Transactions Menu
    ${frontend_rt_subledger}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_SUBLEDGER}=    Get WebElements    ${RT_SUBLEDGER_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_subledger_element}    IN    @{ALL_RT_SUBLEDGER}
            ${rt_subledger_text}=    Get Text    ${rt_subledger_element}
            Append To List    ${frontend_rt_subledger}    ${rt_subledger_text}
        END

        #Log the collected effective dates for this page
        Log   RT Subledger for Page ${page_num}: ${frontend_rt_subledger}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_SUBLEDGER_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Subledger: ${frontend_rt_subledger}

    Set Suite Variable    ${frontend_rt_subledger}

    ${rt_subledger_length}=    Get length    ${frontend_rt_subledger}
    Log    ${rt_subledger_length}




    #Get All SUBLEDGER GL for the Recon Transactions Menu
    ${frontend_rt_subledger_gl}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_SUBLEDGER_GL}=    Get WebElements    ${RT_SUBLEDGER_GL_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_subledger_gl_element}    IN    @{ALL_RT_SUBLEDGER_GL}
            ${rt_subledger_gl_text}=    Get Text    ${rt_subledger_gl_element}
            Append To List    ${frontend_rt_subledger_gl}    ${rt_subledger_gl_text}
        END

        #Log the collected effective dates for this page
        Log   RT Subledger GL for Page ${page_num}: ${frontend_rt_subledger_gl}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_SUBLEDGER_GL_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Subledger GL: ${frontend_rt_subledger_gl}

    Set Suite Variable    ${frontend_rt_subledger_gl}

    ${rt_subledger_gl_length}=    Get length    ${frontend_rt_subledger_gl}
    Log    ${rt_subledger_gl_length}




    #Get All FLAGGED INFOfor the Recon Transactions Menu
    ${frontend_rt_flagged_info}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_FLAGGED}=    Get WebElements    ${RT_FLAGGED_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_flagged_element}    IN    @{ALL_RT_FLAGGED}
            ${rt_flagged_text}=    Get Text    ${rt_flagged_element}
            Append To List    ${frontend_rt_flagged_info}    ${rt_flagged_text}
        END

        #Log the collected effective dates for this page
        Log   RT FLAGGED for Page ${page_num}: ${frontend_rt_flagged_info}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_FLAGGED_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All FLAGGED: ${frontend_rt_flagged_info}

    Set Suite Variable    ${frontend_rt_flagged_info}

    ${rt_flagged_length}=    Get length    ${frontend_rt_flagged_info}
    Log    ${rt_flagged_length}






    #Get All AGING INFOfor the Recon Transactions Menu
    ${frontend_rt_aging_info}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_AGING}=    Get WebElements    ${RT_AGING_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_aging_element}    IN    @{ALL_RT_AGING}
            ${rt_aging_text}=    Get Text    ${rt_aging_element}
            Append To List    ${frontend_rt_aging_info}    ${rt_aging_text}
        END

        #Log the collected effective dates for this page
        Log   RT AGING for Page ${page_num}: ${frontend_rt_aging_info}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_AGING_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All AGING: ${frontend_rt_aging_info}

    Set Suite Variable    ${frontend_rt_aging_info}

    ${rt_aging_length}=    Get length    ${frontend_rt_aging_info}
    Log    ${rt_aging_length}






    #Get All DATE CLEARED INFO for the Recon Transactions Menu
    ${frontend_rt_date_cleared_info}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_DATE_CLEARED}=    Get WebElements    ${RT_DATE_CLEARED_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_date_cleared_element}    IN    @{ALL_RT_DATE_CLEARED}
            ${rt_date_cleared_text}=    Get Text    ${rt_date_cleared_element}
            Append To List    ${frontend_rt_date_cleared_info}    ${rt_date_cleared_text}
        END

        #Log the collected effective dates for this page
        Log   RT DATE CLEARED for Page ${page_num}: ${frontend_rt_date_cleared_info}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_DATE_CLEARED_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All DATE CLEARED: ${frontend_rt_date_cleared_info}

    Set Suite Variable    ${frontend_rt_date_cleared_info}

    ${rt_date_cleared_length}=    Get length    ${frontend_rt_date_cleared_info}
    Log    ${rt_date_cleared_length}





    #Get All COMMENTS(CLICK) INFO for the Recon Transactions Menu
    ${frontend_rt_comments_click_info}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_COMMENTS_CLICK}=    Get WebElements    ${RT_COMMENTS(CLICK)_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_comments_click_element}    IN    @{ALL_RT_COMMENTS_CLICK}
            ${rt_comments_click_text}=    Get Text    ${rt_comments_click_element}
            Append To List    ${frontend_rt_comments_click_info}    ${rt_comments_click_text}
        END

        #Log the collected effective dates for this page
        Log   RT COMMENTS_CLICK for Page ${page_num}: ${frontend_rt_comments_click_info}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_COMMENTS(CLICK)_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All COMMENTS_CLICK: ${frontend_rt_comments_click_info}

    Set Suite Variable    ${frontend_rt_comments_click_info}

    ${rt_comments_click_length}=    Get length    ${frontend_rt_comments_click_info}
    Log    ${rt_comments_click_length}




    #Get All SUBSTANTIATED INFO for the Recon Transactions Menu
    ${frontend_rt_substantiated_info}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_SUBSTANTIATED}=    Get WebElements    ${RT_SUBSTANTIATED_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_substantiated_element}    IN    @{ALL_RT_SUBSTANTIATED}
            ${rt_substantiated_text}=    Get Text    ${rt_substantiated_element}
            Append To List    ${frontend_rt_substantiated_info}    ${rt_substantiated_text}
        END

        #Log the collected effective dates for this page
        Log   RT substantiated for Page ${page_num}: ${frontend_rt_substantiated_info}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_SUBSTANTIATED_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All SUBSTANTIATED: ${frontend_rt_substantiated_info}

    Set Suite Variable    ${frontend_rt_substantiated_info}

    ${rt_substantiated_length}=    Get length    ${frontend_rt_substantiated_info}
    Log    ${rt_substantiated_length}



    #Get All AMOUNT for the Recon Transactions Menu
    ${frontend_rt_amount_info}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_RT_AMOUNT}=    Get WebElements    ${RT_AMOUNT _LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${rt_amount_element}    IN    @{ALL_RT_AMOUNT}
            ${rt_amount_text}=    Get Text    ${rt_amount_element}
            Append To List    ${frontend_rt_amount_info}    ${rt_amount_text}
        END

        #Log the collected effective dates for this page
        Log   RT amount for Page ${page_num}: ${frontend_rt_amount_info}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${RT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${RT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${RT_AMOUNT_LOCATOR}    timeout=10s
    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${RT_First_Page_Button}
    Click Element    ${RT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All SUBSTANTIATED: ${frontend_rt_amount_info}

    Set Suite Variable    ${frontend_rt_amount_info}

    ${rt_amount_length}=    Get length    ${frontend_rt_amount_info}
    Log    ${rt_amount_length}


the user verifies that the Recon Transactions data displayed matches the data in the database
    #Retrieve database data for Flagged Transactions and Compare the front-end retreived data 
    #Defining the Query
    ${my_query}   Set Variable   ${DB_RECON_TRANSACTIONS_QUERY}

    ${my_query} =  Replace String    ${my_query}    Recon_Date    ${RECON_DATE}
    ${my_query} =  Replace String    ${my_query}    Cost_Centre_Number    ${COST_CENTRE_NUMBER}
    ${my_query} =  Replace String    ${my_query}    Account_Number    ${ACCOUNT_NUMBER}

    Log    Recon Date: ${RECON_DATE}
    Log    Cost Centre Number: ${COST_CENTRE_NUMBER}
    Log    Account Number: ${ACCOUNT_NUMBER}
    
    Log    ${my_query}

    #Database Execution 
    ${DATABASE_RECON_TRANSACTIONS_DATA}=    DatabaseUtility.Execute Sql Query        ${my_query}
    Log    ${DATABASE_RECON_TRANSACTIONS_DATA}
    
    #Compare Effective Dates of the Recon Transactions Menu
    ${database_rt_effective_dates}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${effective_date}=    Get From Dictionary    ${entry}    EffectiveDate
        Append To List    ${database_rt_effective_dates}    ${effective_date}
    END

    Log    ${database_rt_effective_dates}
    Log    ${frontend_rt_effective_dates}

    ${db_rt_effective_dates}=    Evaluate    [d.split()[0] for d in ${database_rt_effective_dates}]
    ${frontend_rt_effective_dates}=    Evaluate    [d.replace('/', '-') for d in ${frontend_rt_effective_dates}]
    Log    Normalized DB: ${db_rt_effective_dates}
    Log    Normalized FE: ${frontend_rt_effective_dates}

    ${are_equal}=    Evaluate    ${db_rt_effective_dates} == ${frontend_rt_effective_dates}
    Should Be True    ${are_equal}    The front end effective does and database effective dates do not match for the Recon Transactions Menu


    #Compare Transaction Details of the Recon Transactions Menu
    ${database_rt_transaction_details}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${transaction_details}=    Get From Dictionary    ${entry}    TransactionDetails
        Append To List    ${database_rt_transaction_details}    ${transaction_details}
    END

    Log    ${database_rt_transaction_details}
    Log    ${frontend_rt_transaction_details}

    ${db_rt_transaction_details}=    Evaluate    [i.replace('-', ' - ').strip() for i in ${database_rt_transaction_details}]
    ${are_equal}=    Evaluate    ${db_rt_transaction_details} == ${frontend_rt_transaction_details} 
    Should Be True    ${are_equal}    The front end Transactions displayed do not match the database records for Recon Transactions Menu


    #Compare GL Details of the Recon Transactions Menu
    ${database_rt_gl_details}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${gl_details}=    Get From Dictionary    ${entry}    GLDetails
        Append To List    ${database_rt_gl_details}    ${gl_details}
    END

    Log    ${database_rt_gl_details}
    Log    ${frontend_rt_gl_details}


    ${database_rt_gl_details_trimmed}    Create List
    ${frontend_rt_gl_details_trimmed}    Create List

    FOR  ${item}  IN  @{database_rt_gl_details}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List  ${database_rt_gl_details_trimmed}  ${trimmed_item}
    END 

    FOR  ${item}  IN  @{frontend_rt_gl_details}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List  ${frontend_rt_gl_details_trimmed}  ${trimmed_item}
    END

    Should Be Equal  ${database_rt_gl_details_trimmed}  ${frontend_rt_gl_details_trimmed}



    #Compare subledger Details of the Recon Transactions Menu
    ${database_rt_subledger_details}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${subledger_details}=    Get From Dictionary    ${entry}    SubledgerDetails
        Append To List    ${database_rt_subledger_details}    ${subledger_details}
    END

    Log    ${database_rt_subledger_details}
    Log    ${frontend_rt_subledger_details}
    Should Be Equal    ${database_rt_subledger_details}    ${frontend_rt_subledger_details} 



    #Compare subledger of the Recon Transactions Menu
    ${database_rt_subledger}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${subledger}=    Get From Dictionary    ${entry}    SubLedger
        Append To List    ${database_rt_subledger}    ${subledger}
    END

    Log    ${database_rt_subledger}
    Log    ${frontend_rt_subledger}

    ${db_rt_subledger}=    Sanitize Data    ${database_rt_subledger}
    ${fe_rt_subledger}=    Sanitize Data    ${frontend_rt_subledger}

    Should be equal    ${db_rt_subledger}    ${fe_rt_subledger}


    #Compare Flagged of the Recon Transactions Menu
    ${database_rt_flagged}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${flagged}=    Get From Dictionary    ${entry}    Flaged
        Append To List    ${database_rt_flagged}    ${flagged}
    END

    Log    ${database_rt_flagged}
    Log    ${frontend_rt_flagged_info} 

    ${db_rt_flagged}=    Map Boolean Values In List      ${database_rt_flagged}
    Should be equal    ${db_rt_flagged}    ${frontend_rt_flagged_info}



    #Compare Aging of the Recon Transactions Menu
    ${database_rt_aging}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${aging}=    Get From Dictionary    ${entry}    Aging
        Append To List    ${database_rt_aging}    ${aging}
    END

    Log    ${database_rt_aging}
    Log    ${frontend_rt_aging_info} 
    ${db_rt_aging}=    Convert List Items To String       ${database_rt_aging} 
    
    Should be equal    ${db_rt_aging}    ${frontend_rt_aging_info} 



    #Compare Date Cleared of the Recon Transactions Menu
    ${database_rt_date_cleared}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${date_cleared}=    Get From Dictionary    ${entry}    DateCleared
        Append To List    ${database_rt_date_cleared}    ${date_cleared}
    END

    Log    ${database_rt_date_cleared}
    Log    ${frontend_rt_date_cleared_info} 

    ${db_rt_date_cleared}=    Clean None And Empty Values From List    ${database_rt_date_cleared}
    ${fe_rt_date_cleared_info}=    Clean None And Empty Values From List    ${frontend_rt_date_cleared_info} 
    
    Should be equal     ${db_rt_date_cleared}          ${fe_rt_date_cleared_info} 
    



    #Compare Comments (Click) of the Recon Transactions Menu
    #${database_rt_comments_click}=    Create List
    #FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        #${comments_click}=    Get From Dictionary    ${entry}    DateCleared
        #Append To List    ${database_rt_comments_click}    ${comments_click}
    #END

    #Log    ${database_rt_comments_click}
    #Log    ${frontend_rt_comments_click_info} 
    #Should Be Equal    ${database_rt_comments_click}    ${frontend_rt_comments_click_info} 



    #Compare Substantiated of the Recon Transactions Menu
    ${database_rt_substantiated}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${substantiated}=    Get From Dictionary    ${entry}    Substantiated
        Append To List    ${database_rt_substantiated}    ${substantiated}
    END

    ${db_rt_substantiated}=    Map Boolean Values In List    ${database_rt_substantiated}
    Should Be Equal    ${db_rt_substantiated}    ${frontend_rt_substantiated_info} 



    #Compare Amount of the Recon Transactions Menu
    ${database_rt_amount}=    Create List
    FOR    ${entry}    IN    @{DATABASE_RECON_TRANSACTIONS_DATA}
        ${amount}=    Get From Dictionary    ${entry}    Amount
        Append To List    ${database_rt_amount}    ${amount}
    END

    Log    ${database_rt_amount}
    Log    ${frontend_rt_amount_info} 

    ${fe_rt_amount_info}=      Normalize Currency List    ${frontend_rt_amount_info}

    Should Be Equal    ${database_rt_amount}    ${fe_rt_amount_info} 

the user navigates to the "Flagged Transactions Menu"
    ${STATUS}=    Run Keyword And Return Status    Wait Until Element Is Enabled    ${FLAGGED_TRANSACTIONS_MENU}
    Run keyword if    ${status} == False       Fail    Flagged Transaction Menu button is not functioning
    Click Element    ${FLAGGED_TRANSACTIONS_MENU}

the user verifies the "Flagged Transactions" menu under "Account" on the front end
    #If there is no Flagged Transactions displayed, test should not fail, rather skip 
    ${STATUS}=    Run Keyword And Return Status    Wait until Element is enabled    ${FT_EFFECTIVE_DATE_HEADER_LOCATOR}
    Run keyword if    ${status} == False       Skip    There are no Flagged Transactions displayed for account: ${ACCOUNT_NUMBER} under Cost Centre: ${COST_CENTRE_NUMBER}
    Run keyword if    ${status} == True     Proceeding to verify "Flagged Transactions" menu under "Account" on the front end

Proceeding to verify "Flagged Transactions" menu under "Account" on the front end
    #Get the pagination elements 
    ${pagination_elements}=    Get WebElements    ${FT_PAGINATION_XPATH}//a
    Log    Pagination Elements: ${pagination_elements}  

    #Check if the pagination elements are not empty 
    ${pagination_length}=    Get Length    ${pagination_elements}
    Run Keyword If    ${pagination_length} == 0    Fail    No pagination elements found

    #Get the last page number 
    ${last_page_text}=    Get Text    ${pagination_elements[-2]}  
    Log    Last Page Text: ${last_page_text}  

    #Handle case where last page text is empty or invalid
    Run Keyword If    '${last_page_text}' == ''    Fail    Last page number not found or is empty
    ${num_pages}=    Convert To Integer    ${last_page_text}


    #Get All Effective Dates for the Flagged Transactions Menu
    ${frontend_ft_effective_dates}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_EFFECTIVE_DATES}=    Get WebElements    ${FT_EFFECTIVE_DATE_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${date_element}    IN    @{ALL_FT_EFFECTIVE_DATES}
            ${date_text}=    Get Text    ${date_element}
            Append To List    ${frontend_ft_effective_dates}    ${date_text}
        END
        #Log the collected effective dates for this page
        Log    Effective Dates for Page ${page_num}: ${frontend_ft_effective_dates}

        # Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_EFFECTIVE_DATE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Effective Dates: ${frontend_ft_effective_dates}

    Set Suite Variable    ${frontend_ft_effective_dates}

    ${effective_date_length}=    Get length    ${frontend_ft_effective_dates}
    Log    ${effective_date_length}



    
    #Get All Journal Entries for the Flagged Transactions Menu
    ${FT_JOURNAL_ENTRIES}=    Create List
    # Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        Wait Until Element Is Visible    ${FT_JOURNAL_LOCATOR}    timeout=10s
        # Get the effective dates from the current page
        ${ALL_FT_JOURNAL_ENTRIES}=    Get WebElements    ${FT_JOURNAL_LOCATOR}
        
        # Iterate through each effective date and add it to the list
        FOR    ${journal_element}    IN    @{ALL_FT_JOURNAL_ENTRIES}
            ${journal_text}=    Get Text    ${journal_element}
            Append To List    ${FT_JOURNAL_ENTRIES}    ${journal_text}
        END
        # Log the collected effective dates for this page
        Log    Journal Entries for Page ${page_num}: ${FT_JOURNAL_ENTRIES}

        # Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Sleep    3s
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}

        Wait Until Element Is Visible    ${FT_JOURNAL_LOCATOR}    timeout=10s
        

    END
    
    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Log    All Journal Entries: ${FT_JOURNAL_ENTRIES}
    Set Suite Variable    ${FT_JOURNAL_ENTRIES}

    ${FT_JOURNAL_ENTRIES_length}=    Get length    ${FT_JOURNAL_ENTRIES}
    Log    ${FT_JOURNAL_ENTRIES_length}




#Get All GL Details for the Flagged Transactions Menu
    ${FT_GL_DETAILS_FRONTEND}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_GL_DETAILS}=    Get WebElements    ${FT_GL_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${gl_details_element}    IN    @{ALL_FT_GL_DETAILS}
            ${gl_detail_text}=    Get Text    ${gl_details_element}
            Append To List    ${FT_GL_DETAILS_FRONTEND}    ${gl_detail_text}
        END
        #Log the collected effective dates for this page
        Log    GL Details for Page ${page_num}: ${FT_GL_DETAILS_FRONTEND}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_GL_DETAILS_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    Remove Values From List    ${FT_GL_DETAILS_FRONTEND}    Audit
    Log    All Gl Details for Flagged Transactions: ${FT_GL_DETAILS_FRONTEND}
    Set Suite Variable    ${FT_GL_DETAILS_FRONTEND}

    ${gl_details_length}=    Get length   ${FT_GL_DETAILS_FRONTEND}
    Log    ${gl_details_length}



#Get All SL Details for the Flagged Transactions Menu
    ${FT_SL_DETAILS}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_SL_DETAILS}=    Get WebElements    ${FT_SL_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${sl_details_element}    IN    @{ALL_FT_SL_DETAILS}
            ${sl_detail_text}=    Get Text    ${sl_details_element}
            Append To List    ${FT_SL_DETAILS}    ${sl_detail_text}
        END
        #Log the collected effective dates for this page
        Log    SL Details for Page ${page_num}: ${FT_SL_DETAILS}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_SL_DETAILS_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All Sl Details for Flagged Transactions: ${FT_SL_DETAILS}

    ${filtered_ft_sl_details}=    Common_Functions.filter_details    ${FT_SL_DETAILS}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT Details: ${filtered_ft_sl_details}
    Set Suite Variable    ${filtered_ft_sl_details}

    ${sl_details_length}=    Get length    ${filtered_ft_sl_details}
    Log    ${sl_details_length}


#Get All Subledger rows for the Flagged Transactions Menu
    ${FT_SUB_LEDGER}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_SUB_LEDGER}=    Get WebElements    ${FT_SUB_LEDGER_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${subledger_element}    IN    @{ALL_FT_SUB_LEDGER}
            ${subledger_text}=    Get Text    ${subledger_element}
            Append To List    ${FT_SUB_LEDGER}    ${sl_detail_text}
        END
        #Log the collected effective dates for this page
        Log    SUB LEDGER for Page ${page_num}: ${FT_SUB_LEDGER}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_SUB_LEDGER_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All sl Details for Flagged Transactions: ${FT_SUB_LEDGER}

    ${filtered_frontend_subledger_details}=    Common_Functions.filter_details    ${FT_SUB_LEDGER}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT Details: ${filtered_frontend_subledger_details}
    Set Suite Variable    ${filtered_frontend_subledger_details}

    ${sl_details_length}=    Get length    ${filtered_frontend_subledger_details}
    Log    ${sl_details_length}





#Get All SLID rows for the Flagged Transactions Menu
    ${FT_SLID}=    Create List
    # Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        # Get the effective dates from the current page
        ${ALL_FT_SLID}=    Get WebElements    ${FT_SLID_LOCATOR}
        
        # Iterate through each effective date and add it to the list
        FOR    ${slid_element}    IN    @{ALL_FT_SLID}
            ${slid_text}=    Get Text    ${slid_element}
            Append To List    ${FT_SLID}    ${slid_text}
        END
        #Log the collected effective dates for this page
        Log    SLID for Page ${page_num}: ${FT_SLID}

        # Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_SLID_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All sl Details for Flagged Transactions: ${FT_SLID}

    ${filtered_ft_slid_frontend}=    Common_Functions.filter_details    ${FT_SLID}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT SLID: ${filtered_ft_slid_frontend}
    Set Suite Variable    ${filtered_ft_slid_frontend}

    ${slid_length}=    Get length    ${filtered_ft_slid_frontend}
    Log    ${slid_length}




#Get All Flagged data from the Flagged Transactions Menu
    ${FT_FLAGGED}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_FLAGGED}=    Get WebElements    ${FT_FLAGGED_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${flagged_element}    IN    @{ALL_FT_FLAGGED}
            ${flagged_text}=    Get Text    ${flagged_element}
            Append To List    ${FT_FLAGGED}    ${flagged_text}
        END
        #Log the collected flagged data for this page
        Log    Flagged for Page ${page_num}: ${FT_SLID}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_FLAGGED_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All Flagged data for Flagged Transactions: ${FT_FLAGGED}

    ${frontend_flagged_data}=    Common_Functions.filter_details    ${FT_FLAGGED}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT FLAGGED: ${frontend_flagged_data}
    Set Suite Variable    ${frontend_flagged_data}

    ${flagged_length}=    Get length    ${frontend_flagged_data}
    Log    ${flagged_length}




#Get All Aging data from the Flagged Transactions Menu
    ${FT_AGING}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_AGING}=    Get WebElements    ${FT_AGING_LOCATOR}
        
        #Iterate through each AGING data and add it to the list
        FOR    ${aging_element}    IN    @{ALL_FT_AGING}
            ${aging_text}=    Get Text    ${aging_element}
            Append To List    ${FT_AGING}    ${aging_text}
        END
        #Log the collected flagged data for this page
        Log    Aging data for Page ${page_num}: ${FT_AGING}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_AGING_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All Aging data for Flagged Transactions: ${FT_AGING}

    ${frontend_ft_aging_data}=    Common_Functions.filter_details    ${FT_AGING}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT Aging: $${frontend_ft_aging_data}
    Set Suite Variable    ${frontend_ft_aging_data}

    ${aging_length}=    Get length    ${frontend_ft_aging_data}
    Log    ${aging_length}



    #Get All Date Cleared data from the Flagged Transactions Menu
    ${FT_DATE_CLEARED}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_DATE_CLEARED}=    Get WebElements    ${FT_DATE_CLEARED_LOCATOR}
        
        #Iterate through each AGING data and add it to the list
        FOR    ${date_cleared_element}    IN    @{ALL_FT_DATE_CLEARED}
            ${date_cleared_text}=    Get Text    ${date_cleared_element}
            Append To List    ${FT_DATE_CLEARED}    ${date_cleared_text}
        END
        #Log the collected flagged data for this page
        Log    DATE CLEARED data for Page ${page_num}: ${FT_DATE_CLEARED}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_DATE_CLEARED_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All DATE CLEARED data for Flagged Transactions: ${FT_DATE_CLEARED}

    ${frontend_ft_date_cleared_data}=    Common_Functions.filter_details    ${FT_DATE_CLEARED}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT date cleared: ${frontend_ft_date_cleared_data}
    Set Suite Variable    ${frontend_ft_date_cleared_data}

    ${date_cleared_length}=    Get length    ${frontend_ft_date_cleared_data}
    Log    ${date_cleared_length}




#Get All Substantiated data from the Flagged Transactions Menu
    ${FT_SUBSTANTIATED}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_FT_SUBSTANTIATED}=    Get WebElements    ${FT_SUBSTANTIATED_LOCATOR}
        
        #Iterate through each AGING data and add it to the list
        FOR    ${substantiated_element}    IN    @{ALL_FT_SUBSTANTIATED}
            ${substantiated_text}=    Get Text    ${substantiated_element}
            Append To List    ${FT_SUBSTANTIATED}    ${substantiated_text}
        END
        #Log the collected flagged data for this page
        Log    substantiated data for Page ${page_num}: ${FT_SUBSTANTIATED}

        #Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        #Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_SUBSTANTIATED_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected effective dates after all pages are processed
    #Remove Values From List    ${FT_SL_DETAILS}    Audit
    Log    All SUBSTANTIATED data for Flagged Transactions: ${FT_SUBSTANTIATED}

    ${frontend_ft_substantiated_data}=    Common_Functions.filter_details    ${FT_SUBSTANTIATED}
    
    #Log the filtered details (everything except empty strings and amounts)
    Log    Filtered FT SUBSTANTIATED: ${frontend_ft_substantiated_data}
    Set Suite Variable    ${frontend_ft_substantiated_data}

    ${substantiated_length}=    Get length     ${frontend_ft_substantiated_data}
    Log    ${substantiated_length}



#Get All Amount data from the Flagged Transactions Menu
    ${FT_AMOUNT}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        # Get the effective dates from the current page
        ${ALL_FT_AMOUNT}=    Get WebElements    ${FT_AMOUNT_LOCATOR}
        
        # Iterate through each AGING data and add it to the list
        FOR    ${amount_element}    IN    @{ALL_FT_AMOUNT}
            ${amount_text}=    Get Text    ${amount_element}
            Append To List    ${FT_AMOUNT}    ${amount_text}
        END
        #Log the collected flagged data for this page
        Log    substantiated data for Page ${page_num}: ${FT_AMOUNT}

        # Click "Next" button if it's not the last page
        Scroll Element Into View   ${FT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${FT_NEXT_PAGE_BUTTON}
        
        # Wait for the next page to load 
        Wait Until Element Is Visible    ${FT_AMOUNT_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${FT_First_Page_Button}
    Click Element    ${FT_First_Page_Button}

    #Log all collected AMOUNTs after all pages are processed
    
    Log    All SUBSTANTIATED data for Flagged Transactions: ${FT_AMOUNT}
    Set Suite Variable    ${FT_AMOUNT}


    ${amount_length}=    Get length     ${FT_AMOUNT}
    Log    ${amount_length}

the user verifies that the Flagged Transactions data displayed matches the data in the database
    #Retrieve database data for Flagged Transactions and Compare the front-end retreived data 
    #Defining the Query
    ${my_query}   Set Variable   ${DB_FLAGGED_TRANSACTIONS_QUERY}

    ${my_query} =  Replace String    ${my_query}    Recon_Date    ${RECON_DATE}
    ${my_query} =  Replace String    ${my_query}    Cost_Centre_Number    ${COST_CENTRE_NUMBER}
    ${my_query} =  Replace String    ${my_query}    Account_Number    ${ACCOUNT_NUMBER}

    Log    Recon Date: ${RECON_DATE}
    Log    Cost Centre Number: ${COST_CENTRE_NUMBER}
    Log    Account Number: ${ACCOUNT_NUMBER}
    
    Log    ${my_query}

    #Database Execution 
    ${DATABASE_FLAGGED_TRANSACTIONS_DATA}=    DatabaseUtility.Execute Sql Query        ${my_query}
    Log    ${DATABASE_FLAGGED_TRANSACTIONS_DATA}
    
    #Compare Effective Dates of the Flagged Transactions Menu
    ${database_ft_effective_dates}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${effective_date}=    Get From Dictionary    ${entry}    EffectiveDate
        Append To List    ${database_ft_effective_dates}    ${effective_date}
    END

    Log    ${database_ft_effective_dates}
    Log    ${frontend_ft_effective_dates}

    ${db_ft_effective_dates}=    Evaluate    [d.split()[0] for d in ${database_ft_effective_dates}]
    ${frontend_ft_effective_dates}=    Evaluate    [d.replace('/', '-') for d in ${frontend_ft_effective_dates}]
    Log    Normalized DB: ${db_ft_effective_dates}
    Log    Normalized FE: ${frontend_ft_effective_dates}

    ${are_equal}=    Evaluate    ${db_ft_effective_dates} == ${frontend_ft_effective_dates}
    Should Be True    ${are_equal}    The front end effective does and database effective dates do not match for the Flagged Transactions Menu

    #Compare Journal Information of the Flagged Transactions Menu
    ${database_ft_journal_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${journal}=    Get From Dictionary    ${entry}    Journal
        Append To List    ${database_ft_journal_data}    ${journal}
    END

    Log    ${database_ft_journal_data}
    Log    ${FT_JOURNAL_ENTRIES}

    ${db_ft_journal_data}=    Evaluate    [i.replace('-', ' - ').strip() for i in ${database_ft_journal_data}]
    ${are_equal}=    Evaluate    ${db_ft_journal_data} == ${FT_JOURNAL_ENTRIES}
    Should Be True    ${are_equal}    The front end Journal data displayed do not match the database records for Flagged Transactions Menu

    

    #Compare GLDetails Information of the Flagged Transactions Menu
    ${database_ft_gldetails}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${gl_detail}=    Get From Dictionary    ${entry}    GLDetails
        Append To List    ${database_ft_gldetails}    ${gl_detail}
        END

    Log    ${database_ft_gldetails}
    Log    ${FT_GL_DETAILS_FRONTEND}

    ${database_ft_gldetails_trimmed}=  Create List
    ${FT_GL_DETAILS_FRONTEND_trimmed}=  Create List

    FOR  ${item}  IN  @{database_ft_gldetails}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List  ${database_ft_gldetails_trimmed}  ${trimmed_item}
    END 

    FOR  ${item}  IN  @{FT_GL_DETAILS_FRONTEND}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List  ${FT_GL_DETAILS_FRONTEND_trimmed}  ${trimmed_item}
    END

    Should Be Equal  ${database_ft_gldetails_trimmed}  ${FT_GL_DETAILS_FRONTEND_trimmed}

    
    #Compare SL Details Information of the Flagged Transactions Menu
    ${database_ft_sl_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_sl_details}=    Get From Dictionary    ${entry}    SLDetails
        Append To List    ${database_ft_sl_details_data}     ${ft_sl_details}
    END

    Log    ${database_ft_sl_details_data}
    Log    ${filtered_ft_sl_details}

    ${filtered_database_ft_sl_details_data} =  Create List
    FOR  ${sub_item}  IN  ${database_ft_sl_details_data}
        FOR  ${element}  IN  @{sub_item}  # Use @{} to indicate that ${sub_item} is a list
            Run Keyword If  '${element}' != ''  Append To List  ${filtered_database_ft_sl_details_data}  ${element}
        END
    END

    Should Be Equal  ${filtered_database_ft_sl_details_data}  ${filtered_ft_sl_details}

    #Compare Sub Ledger Details Information of the Flagged Transactions Menu
    ${database_ft_subledger_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_subledger_details}=    Get From Dictionary    ${entry}    SubLedger
        Append To List    ${database_ft_subledger_details_data}     ${ft_subledger_details}
    END

    Log    ${database_ft_subledger_details_data}
    Log    ${filtered_frontend_subledger_details}

    ${database_ft_subledger_details_data}=    Sanitize Data    ${database_ft_subledger_details_data}
    ${filtered_frontend_subledger_details}=    Sanitize Data    ${filtered_frontend_subledger_details}
    
    Log    ${database_ft_subledger_details_data}
    Log    ${filtered_frontend_subledger_details}
    
    Should Be Equal    ${database_ft_subledger_details_data}    ${filtered_frontend_subledger_details}

    #Compare SLID Details Information of the Flagged Transactions Menu
    ${database_ft_slid_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_slid_details}=    Get From Dictionary    ${entry}    SLID
        Append To List    ${database_ft_slid_details_data}     ${ft_slid_details}
    END

    Log    ${database_ft_slid_details_data}
    Log    ${filtered_ft_slid_frontend}

    ${database_ft_slid_details_data}=    Sanitize Data    ${database_ft_slid_details_data}
    ${filtered_ft_slid_frontend}=    Sanitize Data    ${filtered_ft_slid_frontend}

    Log    ${database_ft_slid_details_data}
    Log    ${filtered_ft_slid_frontend}
    Should Be Equal    ${database_ft_slid_details_data}    ${filtered_ft_slid_frontend}



    #Compare Flagged Details Information of the Flagged Transactions Menu
    ${database_ft_flagged_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_flagged_details}=    Get From Dictionary    ${entry}    Flaged
        Append To List    ${database_ft_flagged_details_data}     ${ft_flagged_details}
    END

    Log    ${database_ft_flagged_details_data}
    Log    ${frontend_flagged_data}

    ${database_ft_flagged_details_data}=    Map Boolean Values In List    ${database_ft_flagged_details_data}
    Log    ${database_ft_flagged_details_data}
    Log    ${frontend_flagged_data}
    Should Be Equal    ${database_ft_flagged_details_data}    ${frontend_flagged_data}
    

    #Compare Aging Details Information of the Flagged Transactions Menu
    ${database_ft_aging_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_aging_details}=    Get From Dictionary    ${entry}    Aging
        Append To List    ${database_ft_aging_details_data}     ${ft_aging_details}
    END

    Log    ${database_ft_aging_details_data}
    Log    ${frontend_ft_aging_data}

    ${database_ft_aging_details_data}=    Convert List Items To String    ${database_ft_aging_details_data}
    Should Be Equal    ${database_ft_aging_details_data}    ${frontend_ft_aging_data}
    
    #Compare Date Cleared Details Information of the Flagged Transactions Menu
    ${database_ft_date_cleared_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_date_cleared_details}=    Get From Dictionary    ${entry}    DateCleared
        Append To List    ${database_ft_date_cleared_details_data}     ${ft_date_cleared_details}
    END

    Log    ${database_ft_date_cleared_details_data}
    Log    ${frontend_ft_date_cleared_data}

    ${database_ft_date_cleared_data}=    Clean None Values From List    ${database_ft_date_cleared_details_data}
    Should Be Equal    ${database_ft_date_cleared_data}    ${frontend_ft_date_cleared_data}
    
    

    #Compare Substantiated Details Information of the Flagged Transactions Menu
    ${database_ft_substantiated_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_substantiated_details}=    Get From Dictionary    ${entry}    Substantiated
        Append To List    ${database_ft_substantiated_details_data}     ${ft_substantiated_details}
    END

    Log    ${database_ft_substantiated_details_data}
    Log    ${frontend_ft_substantiated_data}

    ${database_ft_substantiated_data}=    Map Boolean Values In List    ${database_ft_substantiated_details_data}
    Log    ${database_ft_substantiated_data}
    Log    ${frontend_ft_substantiated_data}

    Should Be Equal    ${database_ft_substantiated_data}    ${frontend_ft_substantiated_data}

    
    #Compare Amount Details Information of the Flagged Transactions Menu
    ${database_ft_amount_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_FLAGGED_TRANSACTIONS_DATA}
        ${ft_amount_details}=    Get From Dictionary    ${entry}    Amount
        Append To List    ${database_ft_amount_details_data}     ${ft_amount_details}
    END

    Log    ${database_ft_amount_details_data}
    Log    ${FT_AMOUNT}

    ${normalized_frontend_amounts}=    Normalize Currency List    ${FT_AMOUNT}
    Should Be Equal    ${database_ft_amount_details_data}    ${normalized_frontend_amounts}

    
the user navigates to the "GL Transactions Menu"
    ${STATUS}=    Run Keyword And Return Status    Wait Until Element Is Enabled    ${GL_TRANSACTIONS_MENU_LOCATOR}
    Run keyword if    ${status} == False       Fail    GL Transaction Menu button is not functioning
    Click Element    ${GL_TRANSACTIONS_MENU_LOCATOR}

the user verifies the "GL Transactions" menu under "Account" on the front end
    #If there is no Flagged Transactions displayed, test should not fail, rather skip 
    ${STATUS}=    Run Keyword And Return Status    Wait until Element is enabled    ${GL_EFFECTIVE_DATE_HEADER_LOCATOR}
    Run keyword if    ${status} == False       Skip    There are no GL Transactions displayed for account: ${ACCOUNT_NUMBER} under Cost Centre: ${COST_CENTRE_NUMBER}
    Run keyword if    ${status} == True     Proceeding to verify "GL Transactions" menu under "Account" on the front end

Proceeding to verify "GL Transactions" menu under "Account" on the front end
    #Get the pagination elements 
    ${pagination_elements}=    Get WebElements    ${GL_PAGINATION_XPATH}//a
    Log    Pagination Elements: ${pagination_elements}  

    #Check if the pagination elements are not empty (before starting the loop)
    ${pagination_length}=    Get Length    ${pagination_elements}
    Run Keyword If    ${pagination_length} == 0    Fail    No pagination elements found

    #Get the last page number 
    ${last_page_text}=    Get Text    ${pagination_elements[-2]}  
    Log    Last Page Text: ${last_page_text}  

    #Handle case where last page text is empty or invalid
    Run Keyword If    '${last_page_text}' == ''    Fail    Last page number not found or is empty
    ${num_pages}=    Convert To Integer    ${last_page_text}




    #Get All Effective Dates for the GL Transactions Menu
    ${frontend_gl_effective_dates}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_GL_EFFECTIVE_DATES}=    Get WebElements    ${GL_EFFECTIVE_DATE_LOCATOR}
        
        # Iterate through each effective date and add it to the list
        FOR    ${date_element}    IN    @{ALL_GL_EFFECTIVE_DATES}
            ${date_text}=    Get Text    ${date_element}
            Append To List    ${frontend_gl_effective_dates}    ${date_text}
        END
        
        Log    GL Effective Dates for Page ${page_num}: ${frontend_gl_effective_dates}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${GL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${GL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${GL_EFFECTIVE_DATE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${GL_FIRST_PAGE_BUTTON}
    Click Element    ${GL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All GL Effective Dates: ${frontend_gl_effective_dates}

    Set Suite Variable    ${frontend_gl_effective_dates}

    ${effective_date_length}=    Get length    ${frontend_gl_effective_dates}
    Log    ${effective_date_length}


    #Get All Journal Details for the GL Transactions Menu
    ${frontend_gl_journal_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_GL_JOURNAL_DETAILS}=    Get WebElements    ${GL_JOURNAL_LOCATOR}
        
        # Iterate through each effective date and add it to the list
        FOR    ${journal_element}    IN    @{ALL_GL_JOURNAL_DETAILS}
            ${journal_text}=    Get Text    ${journal_element}
            Append To List    ${frontend_gl_journal_details}    ${journal_text}
        END
        
        Log   Journal Details for Page ${page_num}: ${frontend_gl_journal_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${GL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${GL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${GL_JOURNAL_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${GL_FIRST_PAGE_BUTTON}
    Click Element    ${GL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Journal Details: ${frontend_gl_journal_details}

    Set Suite Variable   ${frontend_gl_journal_details}

    ${gl_journal_details_length}=    Get length    ${frontend_gl_journal_details}
    Log     ${gl_journal_details_length}


    #Get All GL Details for the GL Transactions Menu
    ${frontend_gl_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_GL_DETAILS}=    Get WebElements    ${GL_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${gldetails_element}    IN    @{ALL_GL_DETAILS}
            ${gldetails_text}=    Get Text    ${gldetails_element}
            Append To List    ${frontend_gl_details}    ${gldetails_text}
        END
        
        Log   GL Details for Page ${page_num}: ${frontend_gl_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${GL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${GL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${GL_DETAILS_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${GL_FIRST_PAGE_BUTTON}
    Click Element    ${GL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All GL Details: ${frontend_gl_details}

    Set Suite Variable   ${frontend_gl_details}

    ${gl_details_length}=    Get length    ${frontend_gl_details}
    Log     ${gl_details_length}



    #Get All SLID Details for the GL Transactions Menu
    ${frontend_gl_slid}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_GL_SLID}=    Get WebElements    ${GL_SLID_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${gl_slid_element}    IN    @{ALL_GL_SLID}
            ${gl_slid_text}=    Get Text    ${gl_slid_element}
            Append To List    ${frontend_gl_slid}    ${gl_slid_text}
        END
        
        Log   GL SLID for Page ${page_num}: ${frontend_gl_slid}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${GL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${GL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${GL_SLID_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${GL_FIRST_PAGE_BUTTON}
    Click Element    ${GL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All GL SLID: ${frontend_gl_slid}

    Set Suite Variable   ${frontend_gl_slid}

    ${gl_slid_length}=    Get length    ${frontend_gl_slid}
    Log     ${gl_slid_length}



    
    #Get All AMOUNT Details for the GL Transactions Menu
    ${frontend_gl_amount}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_GL_AMOUNT}=    Get WebElements    ${GL_AMOUNT_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${gl_amount_element}    IN    @{ALL_GL_AMOUNT}
            ${gl_amount_text}=    Get Text    ${gl_amount_element}
            Append To List    ${frontend_gl_amount}    ${gl_amount_text}
        END
        
        Log   GL SLID for Page ${page_num}: ${frontend_gl_amount}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${GL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${GL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${GL_AMOUNT_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${GL_FIRST_PAGE_BUTTON}
    Click Element    ${GL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All GL AMOUNT: ${frontend_gl_amount}

    Set Suite Variable   ${frontend_gl_amount}

    ${gl_amount_length}=    Get length    ${frontend_gl_amount}
    Log     ${gl_amount_length}


the user verifies that the GL Transactions data displayed matches the data in the database
    #Retrieve database data for Flagged Transactions and Compare the front-end retreived data 
    #Defining the Query
    ${my_query}   Set Variable   ${GL_FLAGGED_TRANSACTIONS_QUERY}

    ${my_query} =  Replace String    ${my_query}    Recon_Date    ${RECON_DATE}
    ${my_query} =  Replace String    ${my_query}    Cost_Centre_Number    ${COST_CENTRE_NUMBER}
    ${my_query} =  Replace String    ${my_query}    Account_Number    ${ACCOUNT_NUMBER}

    Log    Recon Date: ${RECON_DATE}
    Log    Cost Centre Number: ${COST_CENTRE_NUMBER}
    Log    Account Number: ${ACCOUNT_NUMBER}
    
    Log    ${my_query}

    #Database Execution 
    ${DATABASE_GL_TRANSACTIONS_DATA}=    DatabaseUtility.Execute Sql Query        ${my_query}
    Log    ${DATABASE_GL_TRANSACTIONS_DATA}
    
    #Compare GL Effective Dates of the GL Transactions Menu
    ${database_gl_effective_dates}=    Create List
    FOR    ${entry}    IN    @{DATABASE_GL_TRANSACTIONS_DATA}
        ${gl_effective_date}=    Get From Dictionary    ${entry}    EffectiveDate
        Append To List    ${database_gl_effective_dates}    ${gl_effective_date}
    END

    Log    ${database_gl_effective_dates}
    Log    ${frontend_gl_effective_dates}

    ${db_gl_effective_dates}=    Evaluate    [d.split()[0] for d in ${database_gl_effective_dates}]
    ${frontend_gl_effective_dates}=    Evaluate    [d.replace('/', '-') for d in ${frontend_gl_effective_dates}]
    Log    Normalized DB: ${db_gl_effective_dates}
    Log    Normalized FE: ${frontend_gl_effective_dates}

    ${are_equal}=    Evaluate    ${db_gl_effective_dates} == ${frontend_gl_effective_dates}
    Should Be True    ${are_equal}    The front end GL effective does and database effective dates do not match for the GL Transactions Menu



    #Compare Journal Information of the GL Transactions Menu
    ${database_gl_journal_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_GL_TRANSACTIONS_DATA}
        ${journal}=    Get From Dictionary    ${entry}    Journal
        Append To List    ${database_gl_journal_data}    ${journal}
    END

    Log    ${database_gl_journal_data}
    Log    ${frontend_gl_journal_details}

    ${db_gl_journal_data}=    Evaluate    [i.replace('-', ' - ').strip() for i in ${database_gl_journal_data}]
    ${are_equal}=    Evaluate    ${db_gl_journal_data} == ${frontend_gl_journal_details}
    Should Be True    ${are_equal}    The front end Journal data displayed do not match the database records for GL Transactions Menu


    #Compare GLDetails Information of the GL Transactions Menu
    ${database_gl_gldetails}=    Create List
    FOR    ${entry}    IN    @{DATABASE_GL_TRANSACTIONS_DATA}
        ${gl_detail}=    Get From Dictionary    ${entry}    GLDetails
        Append To List    ${database_gl_gldetails}    ${gl_detail}
        END

    Log    ${database_gl_gldetails}
    Log    ${frontend_gl_details}

    ${database_gl_gldetails_trimmed}=  Create List
    ${GL_GL_DETAILS_FRONTEND_trimmed}=  Create List

    FOR  ${item}  IN  @{database_gl_gldetails}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List  ${database_gl_gldetails_trimmed}  ${trimmed_item}
    END 

    FOR  ${item}  IN  @{frontend_gl_details}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List   ${GL_GL_DETAILS_FRONTEND_trimmed}  ${trimmed_item}
    END

    Lists Should Match Ignoring Order    ${database_gl_gldetails_trimmed}  ${GL_GL_DETAILS_FRONTEND_trimmed}




    #Compare SLID Details Information of the Flagged Transactions Menu
    ${database_gl_slid_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_GL_TRANSACTIONS_DATA}
        ${gl_slid_details}=    Get From Dictionary    ${entry}    SLID
        Append To List    ${database_gl_slid_details_data}     ${gl_slid_details}
    END

    Log    ${database_gl_slid_details_data}
    Log    ${frontend_gl_slid}

    ${database_gl_slid_details_data}=    Sanitize Data    ${database_gl_slid_details_data}
    ${filtered_gl_slid_frontend}=    Sanitize Data    ${frontend_gl_slid}

    Log    ${database_gl_slid_details_data}
    Log    ${filtered_gl_slid_frontend}
    Should Be Equal    ${database_gl_slid_details_data}    ${filtered_gl_slid_frontend}




    #Compare Amount Details Information of the Flagged Transactions Menu
    ${database_gl_amount_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_GL_TRANSACTIONS_DATA}
        ${gl_amount_details}=    Get From Dictionary    ${entry}    Amount
        Append To List    ${database_gl_amount_details_data}     ${gl_amount_details}
    END

    Log    ${database_gl_amount_details_data}
    Log    ${frontend_gl_amount}

    ${normalized_frontend_amounts}=    Normalize Currency List    ${frontend_gl_amount}

    Sort List    ${database_gl_amount_details_data}
    Sort List    ${normalized_frontend_amounts}
    Should be equal    ${database_gl_amount_details_data}    ${normalized_frontend_amounts}

the user navigates to the "Subledger Transactions Menu"
    ${STATUS}=    Run Keyword And Return Status    Wait Until Element Is Enabled    ${SL_TRANSACTIONS_MENU_LOCATOR}
    Run keyword if    ${status} == False       Fail    Subledger Transaction Menu button is not functioning
    Click Element    ${SL_TRANSACTIONS_MENU_LOCATOR}

the user verifies the "Subledger Transactions" menu under "Account" on the front end
    #If there is no Tiff Transactions displayed, test should not fail, rather skip 
    ${STATUS}=    Run Keyword And Return Status    Wait until Element is enabled    ${SL_EFFECTIVE_DATE_HEADER_LOCATOR}
    Run keyword if    ${status} == False       Skip    There are no Subledger Transactions displayed for account: ${ACCOUNT_NUMBER} under Cost Centre: ${COST_CENTRE_NUMBER}
    Run keyword if    ${status} == True     Proceeding to verify "Subledger Transactions" menu under "Account" on the front end

Proceeding to verify "Subledger Transactions" menu under "Account" on the front end
    #Get the pagination elements 
    ${pagination_elements}=    Get WebElements    ${SL_PAGINATION_XPATH}//a
    Log    Pagination Elements: ${pagination_elements}  

    #Check if the pagination elements are not empty (before starting the loop)
    ${pagination_length}=    Get Length    ${pagination_elements}
    Run Keyword If    ${pagination_length} == 0    Fail    No pagination elements found

    #Get the last page number 
    ${last_page_text}=    Get Text    ${pagination_elements[-2]}  
    Log    Last Page Text: ${last_page_text}  

    #Handle case where last page text is empty or invalid
    Run Keyword If    '${last_page_text}' == ''    Fail    Last page number not found or is empty
    ${num_pages}=    Convert To Integer    ${last_page_text}
    

    #Get All Effective Dates for the Subledger Transactions Menu
    ${frontend_sl_effective_dates}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_SL_EFFECTIVE_DATES}=    Get WebElements    ${SL_EFFECTIVE_DATE_LOCATOR}
        
        # Iterate through each effective date and add it to the list
        FOR    ${date_element}    IN    @{ALL_SL_EFFECTIVE_DATES}
            ${date_text}=    Get Text    ${date_element}
            Append To List    ${frontend_sl_effective_dates}    ${date_text}
        END
        
        Log    Subledger Effective Dates for Page ${page_num}: ${frontend_sl_effective_dates}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${SL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${SL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${SL_EFFECTIVE_DATE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${SL_FIRST_PAGE_BUTTON}
    Click Element    ${SL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Subledger Effective Dates: ${frontend_sl_effective_dates}

    Set Suite Variable    ${frontend_sl_effective_dates}

    ${effective_date_length}=    Get length    ${frontend_sl_effective_dates}
    Log    ${effective_date_length}


    #Get All Teller Numbers for the Subledger Transactions Menu
    ${frontend_sl_teller_numbers}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_SL_TELLER_NUMBERS}=    Get WebElements    ${SL_TELLER_NUMBER_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${teller_number_element}    IN    @{ALL_SL_TELLER_NUMBERS}
            ${teller_number_text}=    Get Text    ${teller_number_element}
            Append To List    ${frontend_sl_teller_numbers}    ${teller_number_text}
        END
        
        Log    Subledger Teller Numbers for Page ${page_num}: ${frontend_sl_teller_numbers}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${SL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${SL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${SL_TELLER_NUMBER_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${SL_FIRST_PAGE_BUTTON}
    Click Element    ${SL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Subledger Teller Numbers: ${frontend_sl_teller_numbers}

    Set Suite Variable    ${frontend_sl_teller_numbers}

    ${sl_teller_number_length}=    Get length    ${frontend_sl_teller_numbers}
    Log    ${sl_teller_number_length}



    #Get All Function Code for the Subledger Transactions Menu
    ${frontend_sl_function_code}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_SL_FUNCTION_CODE}=    Get WebElements    ${SL_FUNCTION_CODE_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${function_code_element}    IN    @{ALL_SL_FUNCTION_CODE}
            ${function_code_text}=    Get Text    ${function_code_element}
            Append To List    ${frontend_sl_function_code}    ${function_code_text}
        END
        
        Log    Subledger Function Code for Page ${page_num}: ${frontend_sl_function_code}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${SL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${SL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${SL_FUNCTION_CODE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${SL_FIRST_PAGE_BUTTON}
    Click Element    ${SL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Function Codes: ${frontend_sl_function_code}

    Set Suite Variable    ${frontend_sl_function_code}

    ${sl_function_code_length}=    Get length   ${frontend_sl_function_code}
    Log    ${sl_function_code_length}




    #Get All Subledger details for the Subledger Transactions Menu
    ${frontend_sl_subledger_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_SL_SUBLEDGER_DETAILS}=    Get WebElements    ${SL_SUBLEDGER_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${sl_subledger_details_element}    IN    @{ALL_SL_SUBLEDGER_DETAILS}
            ${subledger_text}=    Get Text    ${sl_subledger_details_element}
            Append To List    ${frontend_sl_subledger_details}    ${subledger_text}
        END
        
        Log    Subledger Details for Page ${page_num}: ${frontend_sl_subledger_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${SL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${SL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${SL_SUBLEDGER_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${SL_FIRST_PAGE_BUTTON}
    Click Element    ${SL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Subledger Details: ${frontend_sl_subledger_details}

    Set Suite Variable    ${frontend_sl_subledger_details}

    ${sl_subledger_details_length}=    Get length   ${frontend_sl_subledger_details}
    Log    ${sl_subledger_details_length}




    #Get All details for the Subledger Transactions Menu
    ${frontend_sl_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_SL_DETAILS}=    Get WebElements    ${SL_DETAILS_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${sl_details_element}    IN    @{ALL_SL_DETAILS}
            ${details_text}=    Get Text    ${sl_details_element}
            Append To List    ${frontend_sl_details}    ${details_text}
        END
        
        Log    Details for Page ${page_num}: ${frontend_sl_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${SL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${SL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${SL_DETAILS_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${SL_FIRST_PAGE_BUTTON}
    Click Element    ${SL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Details: ${frontend_sl_details}

    Set Suite Variable    ${frontend_sl_details}

    ${sl_details_length}=    Get length   ${frontend_sl_details}
    Log    ${sl_details_length}




    #Get All Amounts for the Subledger Transactions Menu
    ${frontend_sl_amount}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_SL_AMOUNT}=    Get WebElements    ${SL_AMOUNT_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${sl_amount_element}    IN    @{ALL_SL_AMOUNT}
            ${amount_text}=    Get Text    ${sl_amount_element}
            Append To List    ${frontend_sl_amount}    ${amount_text}
        END
        
        Log    Details for Page ${page_num}: ${frontend_sl_amount}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${SL_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${SL_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${SL_AMOUNT_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${SL_FIRST_PAGE_BUTTON}
    Click Element    ${SL_FIRST_PAGE_BUTTON}

    #Log all collected gl  dates after all pages are processed
    Log    All Amounts: ${frontend_sl_amount}

    Set Suite Variable    ${frontend_sl_amount}

    ${sl_amount_length}=    Get length   ${frontend_sl_amount}
    Log    ${sl_amount_length}
    




the user verifies that the Subledger Transactions data displayed matches the data in the database
    #Retrieve database data for Subledger Transactions and Compare the front-end retreived data 
    #Defining the Query
    ${my_query}   Set Variable   ${DB_SUBLEDGER_TRANSACTIONS_QUERY}

    ${my_query} =  Replace String    ${my_query}    Recon_Date    ${RECON_DATE}
    ${my_query} =  Replace String    ${my_query}    Cost_Centre_Number    ${COST_CENTRE_NUMBER}
    ${my_query} =  Replace String    ${my_query}    Account_Number    ${ACCOUNT_NUMBER}

    Log    Recon Date: ${RECON_DATE}
    Log    Cost Centre Number: ${COST_CENTRE_NUMBER}
    Log    Account Number: ${ACCOUNT_NUMBER}
    
    Log    ${my_query}

    #Database Execution 
    ${DATABASE_SUBLEDGER_TRANSACTIONS_DATA}=    DatabaseUtility.Execute Sql Query        ${my_query}
    Log    ${DATABASE_SUBLEDGER_TRANSACTIONS_DATA}
    
    #Compare Effective Dates of the Subledger Transactions Menu
    ${database_sl_effective_dates}=    Create List
    FOR    ${entry}    IN    @{DATABASE_SUBLEDGER_TRANSACTIONS_DATA}
        ${effective_date}=    Get From Dictionary    ${entry}    EffectiveDate
        Append To List    ${database_sl_effective_dates}    ${effective_date}
    END

    Log    ${database_sl_effective_dates}
    Log    ${frontend_sl_effective_dates}

    ${db_sl_effective_dates}=    Evaluate    [d.split()[0] for d in ${database_sl_effective_dates}]
    ${frontend_sl_effective_dates}=    Evaluate    [d.replace('/', '-') for d in ${frontend_sl_effective_dates}]
    Log    Normalized DB: ${db_sl_effective_dates}
    Log    Normalized FE: ${frontend_sl_effective_dates}

    ${are_equal}=    Evaluate    ${db_sl_effective_dates} == ${frontend_sl_effective_dates}
    Should Be True    ${are_equal}    The front end effective does and database effective dates do not match for the Subledger Transactions Menu



    #Compare Teller Number of the Subledger Transactions Menu
    ${database_sl_teller_number}=    Create List
    FOR    ${entry}    IN    @{DATABASE_SUBLEDGER_TRANSACTIONS_DATA}
        ${teller_number}=    Get From Dictionary    ${entry}    TellerNumber
        Append To List    ${database_sl_teller_number}    ${teller_number}
    END

    Log    ${database_sl_teller_number}
    Log    ${frontend_sl_teller_numbers}
    
    ${database_sl_teller_number} =  Evaluate    sorted([str(i) for i in ${database_sl_teller_number}])
    ${frontend_sl_teller_numbers} =  Evaluate    sorted([str(i) for i in ${frontend_sl_teller_numbers}])
    Should Be Equal    ${database_sl_teller_number}    ${frontend_sl_teller_numbers}


    #Compare Subledger Information of the Subledger Transactions Menu
    ${database_sl_subledger_details}=    Create List
    FOR    ${entry}    IN    @{DATABASE_SUBLEDGER_TRANSACTIONS_DATA}
        ${subledger_detail}=    Get From Dictionary    ${entry}    SubLedger
        Append To List    ${database_sl_subledger_details}    ${subledger_detail}
        END

    Log    ${database_sl_subledger_details}
    Log    ${frontend_sl_subledger_details}

    ${database_sl_gldetails_trimmed}=  Create List
    ${SL_GL_DETAILS_FRONTEND_trimmed}=  Create List

    FOR  ${item}  IN  @{database_sl_subledger_details}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List  ${database_sl_gldetails_trimmed}  ${trimmed_item}
    END 

    FOR  ${item}  IN  @{frontend_sl_subledger_details}
        ${trimmed_item}=  Remove String  ${item}  ${SPACE}
        Append To List   ${SL_GL_DETAILS_FRONTEND_trimmed}  ${trimmed_item}
    END

    Lists Should Match Ignoring Order    ${database_sl_gldetails_trimmed}  ${SL_GL_DETAILS_FRONTEND_trimmed}

    #Compare Details Information of the Subledger Transactions Menu
    ${database_sl_details}=    Create List
    FOR    ${entry}    IN    @{DATABASE_SUBLEDGER_TRANSACTIONS_DATA}
        ${subledger_gl_detail}=    Get From Dictionary    ${entry}    GLDetails
        Append To List    ${database_sl_details}    ${subledger_gl_detail}
        END

    Log    ${database_sl_details}
    Log    ${frontend_sl_details}

    ${database_sl_details}=    Clean None and Empty Values From List    ${database_sl_gldetails_trimmed}
    ${frontend_sl_details}=    Clean None and Empty Values From List    ${SL_GL_DETAILS_FRONTEND_trimmed}
    Lists Should Match Ignoring Order    ${database_sl_details}    ${frontend_sl_details}


    #Compare Amount Details Information of the Flagged Transactions Menu
    ${database_sl_amount_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_SUBLEDGER_TRANSACTIONS_DATA}
        ${sl_amount_details}=    Get From Dictionary    ${entry}    Amount
        Append To List    ${database_sl_amount_details_data}     ${sl_amount_details}
    END

    Log    ${database_sl_amount_details_data}
    Log    ${frontend_sl_amount}

    ${normalized_frontend_amounts}=    Normalize Currency List    ${frontend_sl_amount}

    Sort List    ${database_sl_amount_details_data}
    Sort List    ${normalized_frontend_amounts}
    Should be equal    ${database_sl_amount_details_data}    ${normalized_frontend_amounts}
    
the user navigates to the "Tiff Transactions Menu"
    ${STATUS}=    Run Keyword And Return Status    Wait Until Element Is Enabled    ${TIFF_TRANSACTIONS_MENU_LOCATOR}
    Run keyword if    ${status} == False       Fail    Tiff Transaction Menu button is not functioning
    Click Element    ${TIFF_TRANSACTIONS_MENU_LOCATOR}

the user verifies the "Tiff Transactions" menu under "Account" on the front end
    #If there is no Flagged Transactions displayed, test should not fail, rather skip 
    ${STATUS}=    Run Keyword And Return Status    Wait until Element is enabled    ${TT_EFFECTIVE_DATE_HEADER_LOCATOR}
    Run keyword if    ${status} == False       Skip    There are no Flagged Transactions displayed for account: ${ACCOUNT_NUMBER} under Cost Centre: ${COST_CENTRE_NUMBER}
    Run keyword if    ${status} == True     Proceeding to verify "Tiff Transactions" menu under "Account" on the front end

Proceeding to verify "Tiff Transactions" menu under "Account" on the front end
    #Get the pagination elements 
    ${pagination_elements}=    Get WebElements    ${TT_PAGINATION_XPATH}//a
    Log    Pagination Elements: ${pagination_elements}  

    #Check if the pagination elements are not empty (before starting the loop)
    ${pagination_length}=    Get Length    ${pagination_elements}
    Run Keyword If    ${pagination_length} == 0    Fail    No pagination elements found

    #Get the last page number 
    ${last_page_text}=    Get Text    ${pagination_elements[-2]}  
    Log    Last Page Text: ${last_page_text}  

    #Handle case where last page text is empty or invalid
    Run Keyword If    '${last_page_text}' == ''    Fail    Last page number not found or is empty
    ${num_pages}=    Convert To Integer    ${last_page_text}


    
    #Get All Effective Dates for the Tiff Transactions Menu
    ${frontend_tt_effective_dates}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_EFFECTIVE_DATES}=    Get WebElements    ${TT_EFFECTIVE_DATE_LOCATOR}
        
        # Iterate through each effective date and add it to the list
        FOR    ${date_element}    IN    @{ALL_TT_EFFECTIVE_DATES}
            ${date_text}=    Get Text    ${date_element}
            Append To List    ${frontend_tt_effective_dates}    ${date_text}
        END
        
        Log    Tiff Transaction Effective Dates for Page ${page_num}: ${frontend_tt_effective_dates}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_EFFECTIVE_DATE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff dates after all pages are processed
    Log    All Tiff Transaction Effective Dates: ${frontend_tt_effective_dates}

    Set Suite Variable    ${frontend_tt_effective_dates}

    ${effective_date_length}=    Get length    ${frontend_tt_effective_dates}
    Log    ${effective_date_length}


    
    #Get All Teller Numbers for the Tiff Transactions Menu
    ${frontend_tt_teller_numbers}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_TELLER_NUMBERS}=    Get WebElements    ${TT_TELLER_NUMBER_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${teller_number_element}    IN    @{ALL_TT_TELLER_NUMBERS}
            ${teller_number_text}=    Get Text    ${teller_number_element}
            Append To List    ${frontend_tt_teller_numbers}    ${teller_number_text}
        END
        
        Log    Tiff Transactions Teller Numbers for Page ${page_num}: ${frontend_tt_teller_numbers}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_TELLER_NUMBER_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff Transactions dates after all pages are processed
    Log    All Tiff Transactions Teller Numbers: ${frontend_tt_teller_numbers}

    Set Suite Variable    ${frontend_tt_teller_numbers}

    ${tt_teller_number_length}=    Get length    ${frontend_tt_teller_numbers}
    Log    ${tt_teller_number_length}



    #Get All Mode Details for the Tiff Transactions Menu
    ${frontend_tt_mode_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_MODE_DETAILS}=    Get WebElements    ${TT_MODE_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${mode_element}    IN    @{ALL_TT_MODE_DETAILS}
            ${mode_text}=    Get Text    ${mode_element}
            Append To List    ${frontend_tt_mode_details}    ${mode_text}
        END
        
        Log    Tiff Transactions Mode Details for Page ${page_num}: ${frontend_tt_mode_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_MODE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff Transactions dates after all pages are processed
    Log    All Tiff Transactions Mode Details: ${frontend_tt_mode_details}

    Set Suite Variable    ${frontend_tt_mode_details}

    ${tt_mode_length}=    Get length    ${frontend_tt_mode_details}
    Log    ${tt_mode_length}





    #Get All Journal No for the Tiff Transactions Menu
    ${frontend_tt_journal_no_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_JOURNAL_NO_DETAILS}=    Get WebElements    ${TT_JOURNAL_NO_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${journal_element}    IN    @{ALL_TT_JOURNAL_NO_DETAILS}
            ${journal_no_text}=    Get Text    ${journal_element}
            Append To List    ${frontend_tt_journal_no_details}    ${journal_no_text}
        END
        
        Log    Tiff Transactions Journal No for Page ${page_num}: ${frontend_tt_journal_no_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_JOURNAL_NO_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff Transactions dates after all pages are processed
    Log    All Tiff Transactions Journal No Details: ${frontend_tt_journal_no_details}

    Set Suite Variable    ${frontend_tt_journal_no_details}

    ${tt_journal_length}=    Get length    ${frontend_tt_journal_no_details}
    Log    ${tt_journal_length} 






    #Get All Account No for the Tiff Transactions Menu
    ${frontend_tt_account_no_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_ACCOUNT_NO_DETAILS}=    Get WebElements    ${TT_ACCOUNT_NO_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${account_element}    IN    @{ALL_TT_ACCOUNT_NO_DETAILS}
            ${account_no_text}=    Get Text    ${account_element}
            Append To List    ${frontend_tt_account_no_details}    ${account_no_text}
        END
        
        Log    Tiff Transactions Account No for Page ${page_num}: ${frontend_tt_account_no_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_ACCOUNT_NO_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff Transactions dates after all pages are processed
    Log    All Tiff Transactions Account No Details: ${frontend_tt_account_no_details}

    Set Suite Variable    ${frontend_tt_account_no_details}

    ${tt_account_length}=    Get length    ${frontend_tt_account_no_details}
    Log    ${tt_account_length} 





    #Get All Function Code for the Tiff Transactions Menu
    ${frontend_tt_function_code_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_FUNCTION_CODE_DETAILS}=    Get WebElements    ${TT_FUNCTION_CODE_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${function_code_element}    IN    @{ALL_TT_FUNCTION_CODE_DETAILS}
            ${function_code_text}=    Get Text    ${function_code_element}
            Append To List    ${frontend_tt_function_code_details}    ${function_code_text}
        END
        
        Log    Tiff Transactions Function Code for Page ${page_num}: ${frontend_tt_function_code_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_FUNCTION_CODE_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff Transactions dates after all pages are processed
    Log    All Tiff Transactions Function Code Details: ${frontend_tt_function_code_details}

    Set Suite Variable    ${frontend_tt_function_code_details}

    ${tt_function_code_length}=    Get length    ${frontend_tt_function_code_details}
    Log    ${tt_function_code_length}



    #Get All Function Code for the Tiff Transactions Menu
    ${frontend_tt_amount_details}=    Create List
    #Loop through each page using pagination controls
    FOR    ${page_num}    IN RANGE    1    ${num_pages + 1}
        #Get the effective dates from the current page
        ${ALL_TT_AMOUNT_DETAILS}=    Get WebElements    ${TT_AMOUNT_LOCATOR}
        
        #Iterate through each effective date and add it to the list
        FOR    ${amount_element}    IN    @{ALL_TT_AMOUNT_DETAILS}
            ${amount_text}=    Get Text    ${amount_element}
            Append To List    ${frontend_tt_amount_details}    ${amount_text}
        END
        
        Log    Tiff Transactions Amounts for Page ${page_num}: ${frontend_tt_amount_details}

        #Click Next button if it's not the last page
        Scroll Element Into View   ${TT_PAGINATION_XPATH}
        Run Keyword If    ${page_num} < ${num_pages}    Click Element    ${TT_NEXT_PAGE_BUTTON}
        
        Wait Until Element Is Visible    ${TT_AMOUNT_LOCATOR}    timeout=10s

    END

    #Naviagte back to first page for the Next loop
    Wait until element is enabled    ${TT_FIRST_PAGE_BUTTON}
    Click Element    ${TT_FIRST_PAGE_BUTTON}

    #Log all collected Tiff Transactions dates after all pages are processed
    Log    All Tiff Transactions Amount Details: ${frontend_tt_amount_details}

    Set Suite Variable    ${frontend_tt_amount_details}

    ${tt_amount_length}=    Get length    ${frontend_tt_amount_details}
    Log    ${tt_amount_length}




the user verifies that the Tiff Transactions data displayed matches the data in the database
    #Retrieve database data for Subledger Transactions and Compare the front-end retreived data 
    #Defining the Query
    ${my_query}   Set Variable   ${DB_TIFF_TRANSACTIONS_QUERY}

    ${my_query} =  Replace String    ${my_query}    Recon_Date    ${RECON_DATE}
    ${my_query} =  Replace String    ${my_query}    Cost_Centre_Number    ${COST_CENTRE_NUMBER}
    ${my_query} =  Replace String    ${my_query}    Account_Number    ${ACCOUNT_NUMBER}

    Log    Recon Date: ${RECON_DATE}
    Log    Cost Centre Number: ${COST_CENTRE_NUMBER}
    Log    Account Number: ${ACCOUNT_NUMBER}
    
    Log    ${my_query}

    #Database Execution 
    ${DATABASE_TIFF_TRANSACTIONS_DATA}=    DatabaseUtility.Execute Sql Query        ${my_query}
    Log    ${DATABASE_TIFF_TRANSACTIONS_DATA}


    #Compare Effective Dates of the Tiff Transactions Menu
    ${database_tt_effective_dates}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${effective_date}=    Get From Dictionary    ${entry}    EffectiveDate
        Append To List    ${database_tt_effective_dates}    ${effective_date}
    END

    Log    ${database_tt_effective_dates}
    Log    ${frontend_tt_effective_dates}

    ${db_tt_effective_dates}=    Evaluate    [d.split()[0] for d in ${database_tt_effective_dates}]
    ${frontend_tt_effective_dates}=    Evaluate    [d.replace('/', '-') for d in ${frontend_tt_effective_dates}]
    Log    Normalized DB: ${db_tt_effective_dates}
    Log    Normalized FE: ${frontend_tt_effective_dates}

    ${are_equal}=    Evaluate    ${db_tt_effective_dates} == ${frontend_tt_effective_dates}
    Should Be True    ${are_equal}    The front end effective does and database effective dates do not match for the Tiff Transactions Menu


    #Compare Teller Number of the Tiff Transactions Menu
    ${database_tt_teller_number}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${teller_number}=    Get From Dictionary    ${entry}    teller
        Append To List    ${database_tt_teller_number}    ${teller_number}
    END

    Log    ${database_tt_teller_number}
    Log    ${frontend_tt_teller_numbers}
    
    ${database_tt_teller_number} =  Evaluate    sorted([str(i) for i in ${database_tt_teller_number}])
    ${frontend_tt_teller_numbers} =  Evaluate    sorted([str(i) for i in ${frontend_tt_teller_numbers}])
    Should Be Equal    ${database_tt_teller_number}    ${frontend_tt_teller_numbers}


    #Compare Mode of the Tiff Transactions Menu
    ${database_tt_mode}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${mode}=    Get From Dictionary    ${entry}    Mode
        Append To List    ${database_tt_mode}    ${mode}
    END

    Log    ${database_tt_mode}
    Log    ${frontend_tt_mode_details}

    ${normalized_db_tt}=    Normalize TT Mode Format    ${database_tt_mode}
    ${normalized_frontend_tt}=    Normalize TT Mode Format    ${frontend_tt_mode_details}
    Should Be Equal    ${normalized_db_tt}    ${normalized_frontend_tt}


    #Compare Journal no of the Tiff Transactions Menu
    ${database_tt_journal}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${journal}=    Get From Dictionary    ${entry}    Journal
        Append To List    ${database_tt_journal}    ${journal}
    END

    Log    ${database_tt_journal}
    Log    ${frontend_tt_journal_no_details}

    ${cleaned_database_tt_journal}=    Clean None And Empty Values From List    ${database_tt_journal}
    ${cleaned_frontend_tt_journal_no_details}=    Clean None And Empty Values From List    ${frontend_tt_journal_no_details}


    Should be equal    ${cleaned_database_tt_journal}   ${cleaned_frontend_tt_journal_no_details}



    #Compare Account no of the Tiff Transactions Menu
    ${database_tt_account}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${account_no}=    Get From Dictionary    ${entry}    AccountNumber
        Append To List     ${database_tt_account}    ${account_no}
    END

    Log     ${database_tt_account}
    Log    ${frontend_tt_account_no_details}

    ${cleaned_frontend_tt_account}=    Convert List Strings To Integers    ${frontend_tt_account_no_details}
    Should Be Equal    ${database_tt_account}    ${cleaned_frontend_tt_account}


    #Compare Function Code of the Tiff Transactions Menu
    ${database_tt_function_code}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${function_code_no}=    Get From Dictionary    ${entry}    FunctionCode
        Append To List     ${database_tt_function_code}    ${function_code_no}
    END

    Log     ${database_tt_function_code}
    Log    ${frontend_tt_function_code_details}

    ${clean_db_codes}=    Evaluate    [code.replace(' - ', '-').strip() for code in ${database_tt_function_code}]
    ${clean_fe_codes}=    Evaluate    [code.replace(' - ', '-').strip() for code in ${frontend_tt_function_code_details}]
    #Sort both lists to ignore the order and compare content
    ${sorted_db_codes} =   BuiltIn.Evaluate    sorted(${clean_db_codes})
    ${sorted_fe_codes} =   BuiltIn.Evaluate    sorted(${clean_fe_codes})
    BuiltIn.Should Be Equal    ${sorted_db_codes}    ${sorted_fe_codes}


    #Compare Amount Details Information of the Flagged Transactions Menu
    ${database_tt_amount_details_data}=    Create List
    FOR    ${entry}    IN    @{DATABASE_TIFF_TRANSACTIONS_DATA}
        ${tt_amount_details}=    Get From Dictionary    ${entry}    Amount
        Append To List    ${database_tt_amount_details_data}     ${tt_amount_details}
    END

    Log    ${database_tt_amount_details_data}
    Log    ${frontend_tt_amount_details}

    ${normalized_frontend_amounts}=    Normalize Currency List    ${frontend_tt_amount_details}
    Log    DATABASE VALUES: ${database_tt_amount_details_data}
    Log    FRONTEND VALUES: ${normalized_frontend_amounts}

    





































Get Current Date and apend as Recon Date and Filter Date 
    # Step 1: Get the current date without time (in YYYY-MM-DD format)
    ${CURRENT_DATE} =    Evaluate    datetime.datetime.now().strftime('%Y-%m-%d')    modules=datetime 
    Log    Current Date: ${CURRENT_DATE}    # Log the current date

    # Step 2: Subtract 1 day from the current date using Python's timedelta
    ${PREVIOUS_DAY} =    Evaluate    (datetime.datetime.strptime('${CURRENT_DATE}', '%Y-%m-%d') - datetime.timedelta(days=4)).strftime('%Y-%m-%d')    modules=datetime
    Log    Previous Day: ${PREVIOUS_DAY}    # Log the previous day

    # Step 3: Split the date part from the time
    ${DATE_PART} =    String.Split String    ${PREVIOUS_DAY}    ${SPACE}
    Log    Date Part: ${DATE_PART[0]}   # Should give you the previous day (e.g., '2025-04-06')

    # Step 4: Append the time part (e.g., 00:00:00.0000000)
    ${RESULT} =    BuiltIn.Catenate    SEPARATOR=    ${DATE_PART[0]}    ${SPACE}    00:00:00.0000000
    Log    ${RESULT}    # Should log something like '2025-04-06 00:00:00.0000000'

    # Step 5: Set the global variable
    Set Global Variable    ${RECON_DATE}    ${RESULT}

    # Step 6: Extract Month, Day, and Year from the date part
    ${YEAR} =    String.Get Substring    ${DATE_PART[0]}    0    4   # Extract Year
    ${MONTH} =   String.Get Substring    ${DATE_PART[0]}    5    7   # Extract Month
    ${DAY} =     String.Get Substring    ${DATE_PART[0]}    8    10  # Extract Day

    # Step 7: Format the date as MMDDYYYY
    ${FORMATTED_DATE} =    BuiltIn.Set Variable    ${MONTH}${DAY}${YEAR}
    Log    Formatted date: ${FORMATTED_DATE}    # Should log something like '04062025'

    Set Global Variable    ${FILTER_DATE}    ${FORMATTED_DATE}

    #Filter 
    Wait until element is enabled     ${EOD_DATE_FILTER}
    Click Element    ${EOD_DATE_FILTER}

    # Wait for the date input field to be visible
    Wait Until Element Is Visible    id=Filter_ReconDate

    # Set the date value in the input field
    Click Element    ${EOD_DASHBOARD_DATE_FIELD}
    Sleep    3s
    #Input Date MMDDYYYY
    Input Text    id=Filter_ReconDate    ${FILTER_DATE}

    Log    Custom date set to: ${FILTER_DATE}

    Wait until element is enabled    ${EOD_RUN_BUTTON}
    Click Element    ${EOD_RUN_BUTTON}    
    Sleep   3s


Sanitize Data
    [Arguments]    ${data}
    ${cleaned_data}=    Evaluate    [] if all([item == '' for item in ${data}]) else ${data}    # Will return [] if all elements are empty strings
    [Return]    ${cleaned_data}
Map Boolean Values In List
    [Arguments]    ${input_list}
    ${mapped}=    Evaluate    ['Yes' if item is True else 'No' if item is False else item for item in ${input_list}]
    [Return]    ${mapped}
    
Convert List Items To String
    [Arguments]    ${input_list}
    ${converted}=    Evaluate    [str(item) for item in ${input_list}]
    [Return]    ${converted}

Clean None Values From List
    [Arguments]    ${input_list}
    ${cleaned}=    Evaluate    [item for item in ${input_list} if item is not None]
    [Return]    ${cleaned}

Normalize Currency List
    [Arguments]    ${input_list}
    ${normalized}=    Create List
    FOR    ${item}    IN    @{input_list}
        ${stripped}=    Replace String    ${item}    R    ${EMPTY}
        ${stripped}=    Replace String    ${stripped}    ,    ${EMPTY}
        ${stripped}=    Replace String    ${stripped}    ${SPACE}    ${EMPTY}
        ${value}=    Evaluate    decimal.Decimal("${stripped}")    modules=decimal
        Append To List    ${normalized}    ${value}
    END
    [Return]    ${normalized}



Lists Should Match Ignoring Order
    [Arguments]    ${list1}    ${list2}
    ${sorted1}=    Evaluate    sorted(${list1})
    ${sorted2}=    Evaluate    sorted(${list2})
    Should Be Equal    ${sorted1}    ${sorted2}

Normalize TT Mode Format
    [Arguments]    ${input_list}
    ${normalized}=    Evaluate    [item.replace(' - ', '-').strip() for item in ${input_list}]
    [Return]    ${normalized}

Clean None And Empty Values From List
    [Arguments]    ${input_list}
    ${cleaned}=    Evaluate    [item for item in ${input_list} if item not in [None, '']]
    [Return]    ${cleaned}

Convert List Strings To Integers
    [Arguments]    ${input_list}
    ${converted}=    Evaluate    [int(item) for item in ${input_list}]
    [Return]    ${converted}

