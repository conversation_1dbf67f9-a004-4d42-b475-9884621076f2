*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    MAIN ADD NEW CALL
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Test status update functionality of ATM Complaints & Complement

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../Keywords/qrcode_cc/LandingPage.robot   
Resource                                            ../keywords/qrcode_cc/Main.robot 
Resource                                            ../keywords/common/SetEnvironmentVariales.robot

*** Keyword ***
Main add new call   
    [Arguments]    ${DOCUMENTATION}    ${TESTRAIL_TESTCASE_ID}    ${SELECT_AN_ATM}    ${VENDOR_DROPDOWN}    ${DEVICE_DROPDOWN}    ${EPP_SERIAL_TEXT}    ${ERROR_DESCRIPTION_TEXT}    ${VENDOR_REF_TEXT}    ${CUSTODIAN_TEXT}    ${INVOICE_NO_TEXT}    ${COST_CENTRE_TEXT}    ${COMMENTS_TEXT}    ${CELLPHONE_TEXT}
    Set Test Documentation    ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe

    Sleep    2s
   
    And The user clicks navigate to main    ${SELECT_AN_ATM}    ${VENDOR_DROPDOWN}    ${DEVICE_DROPDOWN}    ${EPP_SERIAL_TEXT}    ${ERROR_DESCRIPTION_TEXT}    ${VENDOR_REF_TEXT}    ${CUSTODIAN_TEXT}    ${INVOICE_NO_TEXT}    ${COST_CENTRE_TEXT}    ${COMMENTS_TEXT}    ${CELLPHONE_TEXT}

| *Test Case*                                                                |    *DOCUMENTATION*                                    |    *TESTRAIL_TESTCASE_ID*       |    *SELECT_AN_ATM*                   |   *VENDOR_DROPDOWN*  | *DEVICE_DROPDOWN* |  *EPP_SERIAL_TEXT* | *ERROR_DESCRIPTION_TEXT*  | *VENDOR_REF_TEXT*  | *CUSTODIAN_TEXT*  | *INVOICE_NO_TEXT*      | *COST_CENTRE_TEXT*  | *COMMENTS_TEXT*  | *CELLPHONE_TEXT* |                   
| Main - add new call, validation on vendor and ncr     | Main add new call  | Main - add new call, validation on vendor and ncr     |    101597682                     | S08003 - THE GLEN 1                  |    BMS               |    Card reader    |    TESTING         | Testing error description | testing vendor ref | testing custodian | testing invoice number | testing cost centre | testing comments |   08000000000               |
| Main - add new call , validation on vendor and vetera | Main add new call  | Main - add new call , validation on vendor and vetera |    101597681                     | T5581F14 - Absa Building 1st Floor   |    Gijima            |    Card reader    |    TESTING         | Testing error description | testing vendor ref | testing custodian | testing invoice number | testing cost centre | testing comments | 0800000000 |

