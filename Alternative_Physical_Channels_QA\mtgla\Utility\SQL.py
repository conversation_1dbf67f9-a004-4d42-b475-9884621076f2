DB_RECON_TRANSACTIONS_QUERY=        "SELECT gl.[EffectiveDate] as EffectiveDate, gl.JournalId + '-'+ j.Description as TransactionDetails, gl.Description as GLDetails, gl.SubDescription as SubledgerDetails, gl.SubLedger, gl.Flaged as Flaged, gl.Aging, gl.DateCleared, Substantiated, gl.[Id] ,gl.[EOD_Daily_AccountId] ,gl.[ReconDate] ,gl.[ProcessDate] ,gl.[Items] ,gl.[Amount] ,gl.[JournalId] ,gl.[EOD_JournalIDId] ,gl.[Description] ,gl.[Details] ,gl.[Flaged] ,gl.[Aging] ,gl.[BU] ,gl.[DateCleared] ,gl.[PostSLA] ,gl.[Responsible] ,gl.[Substantiated] ,gl.[MatchId] ,gl.[AccountNumber] ,gl.[CostCentre] ,gl.[CurrentAging] ,gl.[CurrentPostSLA] ,gl.[CurrentReconDate] ,gl.[SLID] ,gl.[FromSubledger] ,gl.[SubDescription] ,gl.[SubLedger] ,gl.[SubLedgerGroup] ,gl.[Highlight] FROM [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_GLTransactions] gl inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_Accounts] ac on ac.Id = gl.EOD_Daily_AccountId inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_JournalIDs] j on j.Id = gl.EOD_JournalIDId where gl.ReconDate = 'Recon_Date' and  ac.AccountNumber = Account_Number and ac.CostCentre = Cost_Centre_Number order by EffectiveDate, Id asc"
DB_FLAGGED_TRANSACTIONS_QUERY=      "SELECT gl.[EffectiveDate] as EffectiveDate, gl.JournalId + '-'+ j.Description as Journal, gl.Description as GLDetails, gl.SubDescription as SLDetails, gl.SubLedger, gl.[SLID], gl.Flaged as Flaged, gl.Aging, gl.DateCleared, Substantiated, gl.[Amount] FROM [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_GLTransactions] gl inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_Accounts] ac on ac.Id = gl.EOD_Daily_AccountId inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_JournalIDs] j on j.Id = gl.EOD_JournalIDId where gl.ReconDate = 'Recon_Date' and  ac.AccountNumber = Account_Number and ac.CostCentre = Cost_Centre_Number and Flaged = 1"
GL_FLAGGED_TRANSACTIONS_QUERY=      "SELECT gl.[EffectiveDate] as EffectiveDate, gl.JournalId + '-'+ j.Description as Journal, gl.Description as GLDetails, gl.[SLID], gl.[Amount], gl.[Id] ,gl.[EOD_Daily_AccountId] ,gl.[ReconDate] ,gl.[ProcessDate] ,gl.[Amount] ,gl.[JournalId] ,gl.[EOD_JournalIDId] ,gl.[Description] ,gl.[AccountNumber] ,gl.[CostCentre] FROM [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_GLSTransactions] gl inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_Accounts] ac on ac.Id = gl.EOD_Daily_AccountId inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_JournalIDs] j on j.Id = gl.EOD_JournalIDId where gl.ReconDate = 'Recon_Date' and  ac.AccountNumber = Account_Number and ac.CostCentre = Cost_Centre_Number"
DB_SUBLEDGER_TRANSACTIONS_QUERY=    "SELECT gl.[EffectiveDate] as EffectiveDate, gl.Teller as TellerNumber, f.Code + '-' + f.Description as FcntionCode, gl.SubLedger, gl.Description as GLDetails, gl.[Amount] FROM [ATM_Mismatch_DEV].[dbo].EOD_Daily_SubTransactions gl inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_Accounts] ac on ac.Id = gl.EOD_Daily_AccountId inner join EOD_T_FunctionCodes f on f.Id = gl.EOD_FunctionCodeId where gl.ReconDate = 'Recon_Date' and  ac.AccountNumber = Account_Number and ac.CostCentre = Cost_Centre_Number order by EffectiveDate desc" 
DB_TIFF_TRANSACTIONS_QUERY=         "SELECT tiff.[EffectiveDate] as EffectiveDate, tiff.ATM as teller, CONCAT(tiff.Mode , '-', mo.Description) AS Mode, tiff.JournalNo as Journal, tiff.AccountNumber, CONCAT(tiff.FunctionCode , '-', f.Description) AS FunctionCode, tiff.[Amount] FROM [ATM_Mismatch_DEV].[dbo].EOD_T_Daily_Tiffs tiff inner join [ATM_Mismatch_DEV].[dbo].[EOD_T_Daily_Accounts] ac on ac.Id = tiff.EOD_Daily_AccountId left join [ATM_Mismatch_DEV].[dbo].EOD_T_Daily_GLTransactions gl on tiff.EOD_Daily_GLTransactionId = gl.Id inner join EOD_T_FunctionCodes f on f.Id = tiff.EOD_FunctionCodeId inner join EOD_T_Modes mo on mo.Id = tiff.EOD_ModeId where tiff.ReconDate = 'Recon_Date' and  tiff.AccountNumber = Account_Number and ac.CostCentre = Cost_Centre_Number and ShowOnTran = 1"