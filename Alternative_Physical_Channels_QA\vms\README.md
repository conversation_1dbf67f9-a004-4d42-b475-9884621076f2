# VMS
The purpose of this project is to validate VMS system functionality  

## Running the test without tags, and default report title
robot -d reports .\tests\test.robot

## Running the test with tags, and a defined report title
robot -d reports -i Regression -reporttitle -N "VMS" .\tests\test.robot

## Pick up result of each test run and send update Qmetry
   robot -d reports/Unit_Tests/TC_04_GET_ATMMarkertingCampaign_CONTROLLER_Headless --variable ROBOT_FILE_PATH:"C:\Users\<USER>\source\repos\vms\data\TC_01_ADD_NEW_USER.xml" --variable BROWSER:edge --variable UPLOAD_TEST_STEPS:Yes --variable IS_HEADLESS_BROWSER:No --variable APPLICATION_USERNAME:"AB032TO" --variable APPLICATION_PASSWORD:"password" -i "VMS HEALTHCHECK"  -N "Future-Fit Portal" --listener .\utility\PostExecutionUpdateV2.py  'tests/ADMIN_USER_MANAGEMENT/TC_01_ADD_NEW_USER.robot'


#Installing testrailintegration
- Download the module from Github
- Unzip the directory
- cd into it
- run " pip3 install ."
- in the code import the package 
   import 
