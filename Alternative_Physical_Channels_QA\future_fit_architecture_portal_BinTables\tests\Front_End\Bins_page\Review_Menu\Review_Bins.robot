*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Review_Bins_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Verify and review Bins displayed on Review Bins Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TO_REVIEW}     ${SELECT_BINS}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal                    ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Review' Bins tab
    And The user verifies that the Bins details displayed on page matches the database     ${BIN_TO_REVIEW}     ${SELECT_BINS}
    And The user approves the bins
    Then The bins(s) must have the status of approved in the database                      ${BIN_TO_REVIEW}


| *** Test Cases ***                                                                                                                   |        *DOCUMENTATION*    		                                    |         *BASE_URL*                  |         *BIN_TO_REVIEW*                |         *SELECT_BINS*           |
| Verify Bins details and approve those Bins.                                 | Verify and review Bins displayed on Review Bins Page   | Verifies bins against the database data and approves the Bin(s).    |           ${EMPTY}                 |         155,303064756                  |            True                 |
