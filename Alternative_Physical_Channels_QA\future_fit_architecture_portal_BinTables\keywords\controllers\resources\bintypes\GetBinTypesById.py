
class CreateRESTRequest:
    def __init__(self, domain, bin_type_id):
        self.domain = domain
        self.bin_type_id = bin_type_id

        self.params = {
            "binTypeId": self.bin_type_id,  # Adding a query parameter to filter the results by Bin Id
        }

    def get_endpoint(self):
        path = "/api/v1/bintables/admin/bintypes/getbintypebyid"
        url = f"{self.domain}{path}"
        return url


    def get_params(self):
        return self.params
