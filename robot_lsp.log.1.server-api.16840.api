server-api: 2025-06-12 10:10:52 UTC pid: 16840 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.1.api']

server-api: 2025-06-12 10:10:52 UTC pid: 16840 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-12 10:10:52 UTC pid: 16840 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-12 10:10:52 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 16840

server-api: 2025-06-12 10:10:55 UTC pid: 16840 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-12 10:10:55 UTC pid: 16840 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-12 10:10:55 UTC pid: 16840 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-12 10:11:56 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 14, method: hover

server-api: 2025-06-12 10:12:46 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 19, method: hover

server-api: 2025-06-12 10:13:43 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 31, method: hover

server-api: 2025-06-12 10:15:37 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 44, method: hover

server-api: 2025-06-12 10:17:31 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 60

server-api: 2025-06-12 10:17:31 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 62

server-api: 2025-06-12 10:17:31 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 63

server-api: 2025-06-12 10:17:31 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 66, method: hover

server-api: 2025-06-12 10:17:31 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 65, method: code_action

server-api: 2025-06-12 10:17:31 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 64

server-api: 2025-06-12 10:17:32 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 67, method: code_action

server-api: 2025-06-12 10:17:32 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 69

server-api: 2025-06-12 10:17:32 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 71, method: code_action

server-api: 2025-06-12 10:17:33 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 74

server-api: 2025-06-12 10:17:33 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 73, method: completeAll

server-api: 2025-06-12 10:17:33 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 75

server-api: 2025-06-12 10:17:33 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 76, method: code_action

server-api: 2025-06-12 10:17:34 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 78, method: code_action

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 252, method: findDefinition

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 254, method: hover

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 256

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 253

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 257, method: findDefinition

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 258, method: findDefinition

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 261, method: hover

server-api: 2025-06-12 10:47:25 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 260, method: hover

server-api: 2025-06-12 10:49:13 UTC pid: 16840 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-12 11:02:20 UTC pid: 16840 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 493, method: hover

server-api: 2025-06-12 11:11:47 UTC pid: 16840 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-12 11:11:48 UTC pid: 16840 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-12 11:11:49 UTC pid: 16840 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-12 11:11:50 UTC pid: 16840 - ThreadPoolExecutor-0_8 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-12 11:11:50 UTC pid: 16840 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

