*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/RejectBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Reject the pending Bin
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_ID}   ${BIN_OUTCOME}    ${BIN_REJECTION_COMMENTS}   ${BIN_STATUS}    ${EXPECTED_STATUS_CODE}   ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User Populates the Reject Bin JSON payload with    ${BIN_ID}   ${BIN_OUTCOME}    ${BIN_REJECTION_COMMENTS}
    When The User executes the Reject Bin API Request    ${BASE_URL}
    And The Reject Bin controller returns an expected status code     ${EXPECTED_STATUS_CODE}
    Then The expected Error must be displayed     ${EXPECTED_ERROR_MESSAGE}

| *** Test Cases ***                                                                            |                   *DOCUMENTATION*                      |         *BASE_URL*               |          *BIN_ID*                          |          *BIN_OUTCOME*         |          *BIN_REJECTION_COMMENTS*              |          *BIN_STATUS*         |       *EXPECTED_STATUS_CODE*  |       *EXPECTED_ERROR_MESSAGE*                                                               |
| Reject the bin that has a review status of 'Rejected'.            | Reject the pending Bin    | Reject a pending Bin that is assigned to the approver  |                                  |  887d9395-92e7-48f8-978b-897b8a623775      |            Added               |          The bin must be deleted not added.    |            Pending            |               400             |      Cannot approve a non-pending bin.                                                       |
| Reject the bin without populating the 'binId' field.              | Reject the pending Bin    | Reject a pending Bin that is assigned to the approver  |                                  |                                            |            Added               |          The bin must be deleted not added.    |            Pending            |               400             |      The JSON value could not be converted to System.Nullable`1[System.Guid]. Path: $.binId  |
| Reject the bin without populating the 'outcome' field.            | Reject the pending Bin    | Reject a pending Bin that is assigned to the approver  |                                  |  887d9395-92e7-48f8-978b-897b8a623775      |                                |          The bin must be deleted not added.    |            Pending            |               500             |      Value cannot be null. (Parameter 'The Outcome must not be null or empty.')              |
| Reject the bin without populating the 'rejectionComment' field.   | Reject the pending Bin    | Reject a pending Bin that is assigned to the approver  |                                  |  887d9395-92e7-48f8-978b-897b8a623775      |            Added               |                                                |            Pending            |               500             |      Value cannot be null. (Parameter 'The Rejection Comment must not be null or empty.')    |
