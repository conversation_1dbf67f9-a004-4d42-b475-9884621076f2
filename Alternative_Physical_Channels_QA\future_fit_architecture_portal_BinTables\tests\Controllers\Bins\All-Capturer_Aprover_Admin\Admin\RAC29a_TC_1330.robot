*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../../keywords/controllers/GetBinsToReview_Keywords.robot
Resource                            ../../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Search for all Bins to be reviewed using a Bin Number on the GetBinsToReview Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetBinsToReview using an incorrect token      ${BASE_URL}       ${EMPTY}
    Then The service returns an expected status code                            ${EXPECTED_STATUS_CODE}


| *** Test Cases ***                                                                                                                                                                                          |        *DOCUMENTATION*    		                 |         *BASE_URL*                 |    *EXPECTED_STATUS_CODE*   |
| Verify that the GetBinsToReview API requires valid authentication to retrieve the BINs list (401 Unauthrorized)   | Search for all Bins to be reviewed using a Bin Number on the GetBinsToReview Controller   | Search Bin by Number on the GetBinsToReview API  |                                    |           401               |
