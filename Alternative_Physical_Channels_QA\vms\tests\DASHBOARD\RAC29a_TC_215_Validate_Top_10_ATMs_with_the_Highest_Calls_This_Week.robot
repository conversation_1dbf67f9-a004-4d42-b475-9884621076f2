*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    DASHBOARD
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Top 10 ATMs with the highest calls for this week

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot    
Resource                                            ../../keywords/VMSPage/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Keyword ***
Dashboard Validation
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}    
    Set Test Documentation  ${DOCUMENTATION} 

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}  
    When The user lands on the dashboard page

    And The user reads the dashboard details for Top 10 ATMs with the highest calls for this week

    And The user reads the database details for Top 10 ATMs with the highest calls for this week

    Then The Database details must be the same as Front End details for Top 10 ATMs with the highest calls for this week

| *Test Case*                                                                                     |                *DOCUMENTATION*                    |     *TEST_ENVIRONMENT*   |        
| Validate Top 10 ATMs with the Highest Calls- This Week | Dashboard Validation     | Validates Top 10 ATMs with the highest calls for this week   |      VMS_UAT             |