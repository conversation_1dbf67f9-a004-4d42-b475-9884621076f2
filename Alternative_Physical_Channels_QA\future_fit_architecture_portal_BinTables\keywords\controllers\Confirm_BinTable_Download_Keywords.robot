*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            JSONLibrary
Library                                             ../../../common_utilities/CommonUtils.py
Library                                            ../../keywords/controllers/resources/deviceversions/ConfirmDownload.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/common/DatabaseConnector.robot

#***********************************PROJECT VARIABLES***************************************

*** Variables ***



*** Keywords ***

The User Populates the Confirm Bin Table Download JSON payload
    [Arguments]     ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}

    ${payload}=     Create the Confirm Bin Table Download Instance     ${DEVICE_NAME}   ${IS_SUCCESSFUL_HANDSHAKE}  ${IS_SUCCESSFUL_DOWNLOAD}   ${DOWNLOAD_ERROR}   ${HANDSHAKE_ERROR}


    ${json_payload_is_created}=    Run Keyword And Return Status     Should Not Be Empty    ${payload}

    Run Keyword If    ${json_payload_is_created} == ${False}
    ...    Fail     The JSON payload for ConfirmDownload bin was not created!

    #Save the payload in a Global Variable
    #Set Global Variable    ${REST_PAYLOAD}        ${json_payload}

    Log Many    ${payload}

The User sends a POST Request to confirm the download of bin table
    [Arguments]     ${BASE_URL}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property   ${BASE_URL}
     Log     ${base_url}

    ${payload}=  Load JSON From File	future_fit_architecture_portal_BinTables/data/ConfirmDownload.json
    Log Many    ${payload}
    ${endpoint}=    Get Endpoint    ${base_url}  #intialize the endpoint value
    Log Many    ${endpoint}
    ${headers}=    Get Headers     #intialize the parameters
    Log Many    ${headers}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   POST
    ${response} =       Send Rest Request    ${endpoint}   method=${method}     headers=${headers}  payload=${payload}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}

    #Created an instance for the Response object
    Create ReadApiResponse Instance



Create the Confirm Bin Table Download Instance
    [Arguments]    ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}
    ${json_result} =    Create Confirm Bin Table Download Request    ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}

    ${JSON_FILE}=   Set Variable        future_fit_architecture_portal_BinTables/data/ConfirmDownload.json
    Write JSON data To File    ${JSON_FILE}    ${json_result}

    RETURN    ${json_result}




The service returns the expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'Upload' REST API did not return the expected status of '${EXPECTED_STATUS_CODE}', the returned status is '${status_code}'

The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}
    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
    IF    "${api_error_message_detail}" != "None"
         ${v2}=      Remove Quotes    ${api_error_message_detail}
    ELSE
         ${v2}=      Set Variable     ${api_error_message_detail}
    END


    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${v1}'     '${v2}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       isSuccessfulHandshake     isSuccessfulDownload     handshakeError.status

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'

             #Remove quotes from the strings
             ${v1}=      Remove Quotes    ${EXPECTED_ERROR_MESSAGE}
             IF    '${api_error_message_fields}' != 'None'
                 ${v2}=      Remove Quotes    ${api_error_message_fields}
             ELSE
                 ${v2}=      Set Variable     ${api_error_message_fields}
             END


             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${v1}' in '${v2}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'confirmdownload' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.



Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}


Get Data Field
    [Arguments]     ${BINS_INSTNANCE}    ${FIELD_NAME}
    ${result}=    Call Method    ${BINS_INSTNANCE}    get_field   ${FIELD_NAME}
    RETURN    ${result}

Get Response Status Code
     #Read the response class instance from the global variable
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keywords to read error response fields


#The below keywords are for Database Verifications
The DeviceVersions database table must save the correct details posted by the ConfirmDownload API
    [Arguments]     ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}

     #This is the boolean value that checks if the bin type name and description are provided
    ${device_name_provided}=        Set Variable If
         ...       '${DEVICE_NAME}' == '${EMPTY}'     ${False}
         ...       '${DEVICE_NAME}' == ''             ${False}
         ...       '${DEVICE_NAME}' != '${EMPTY}'     ${True}
         ...       '${DEVICE_NAME}' != ''             ${True}

    Run Keyword If    not ${device_name_provided}
    ...    Fail     Please provide the name of the device!

    IF    '${IS_SUCCESSFUL_HANDSHAKE}' != '${EMPTY}' and '${IS_SUCCESSFUL_HANDSHAKE}' != ''
         ${successful_handshake_must_be_validated}=     Set Variable   ${True}
    ELSE
         ${successful_handshake_must_be_validated}=     Set Variable   ${False}
    END

    IF    '${IS_SUCCESSFUL_DOWNLOAD}' != '${EMPTY}' and '${IS_SUCCESSFUL_DOWNLOAD}' != ''
         ${successful_download_must_be_validated}=     Set Variable   ${True}
    ELSE
         ${successful_download_must_be_validated}=     Set Variable   ${False}
    END

    IF    '${DOWNLOAD_ERROR}' != '${EMPTY}' and '${DOWNLOAD_ERROR}' != ''
         ${download_error_must_be_validated}=     Set Variable   ${True}
    ELSE
         ${download_error_must_be_validated}=     Set Variable   ${False}
    END

    IF    ("${HANDSHAKE_ERROR}" != "${EMPTY}" and "${HANDSHAKE_ERROR}" != "") or "${HANDSHAKE_ERROR}" == "${None}"
         ${handshake_error_must_be_validated}=     Set Variable   ${True}
    ELSE
         ${handshake_error_must_be_validated}=     Set Variable   ${False}
    END

    ${db_device_versions_details}=         Get the DeviceVersions details from the Database      ${DEVICE_NAME.strip()}

    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${db_device_versions_details}

     Run Keyword If    not ${db_results_contain_data}
    ...     Fail    There is no device versions record for the device named: '${DEVICE_NAME}' in the database.

    ${first_row_results}=           Get From List    ${db_device_versions_details}    0    # Get the first row
    ${db_version_number}=           Get Column Data By Name       ${first_row_results}       VersionNumber
    ${db_download_start_time}=      Get Column Data By Name       ${first_row_results}       DownloadStartTime
    ${db_download_end_time}=        Get Column Data By Name       ${first_row_results}      DownloadEndTime
    ${db_is_successful_handshake}=  Get Column Data By Name       ${first_row_results}      IsSuccessfulHandshake
    ${db_is_successful_handshake_bool}=   Check If One Or Zero    ${db_is_successful_handshake}
    ${db_is_successful_download}=   Get Column Data By Name       ${first_row_results}      IsSuccessfulDownload
    ${db_is_successful_download_bool}=   Check If One Or Zero    ${db_is_successful_download}
    ${db_download_error}=           Get Column Data By Name       ${first_row_results}      DownloadError
    ${db_handshake_error}=          Get Column Data By Name       ${first_row_results}      HandshakeError

    #Verify that the 'Confirm Download' details are correct
    IF    ${successful_handshake_must_be_validated}
         Run Keyword If    '${db_is_successful_handshake_bool}' == '${IS_SUCCESSFUL_HANDSHAKE}'
         ...    Log Many  The IS_SUCCESSFUL_HANDSHAKE: '${db_is_successful_handshake_bool}' from the DB is the same as the provided data: '${IS_SUCCESSFUL_HANDSHAKE}'.
         ...  ELSE
         ...    Run Keyword And Continue On Failure    Fail  The IS_SUCCESSFUL_HANDSHAKE: '${db_is_successful_handshake_bool}' from the DB is not the same as the provided data: '${IS_SUCCESSFUL_HANDSHAKE}'.
    END

    IF    ${successful_download_must_be_validated}
              Run Keyword If    '${db_is_successful_download_bool}' == '${IS_SUCCESSFUL_DOWNLOAD}'
         ...    Log Many  The IS_SUCCESSFUL_DOWNLOAD: '${db_is_successful_download_bool}' from the DB is the same as the provided data: '${IS_SUCCESSFUL_DOWNLOAD}'.
         ...  ELSE
         ...    Run Keyword And Continue On Failure    Fail  The IS_SUCCESSFUL_DOWNLOAD: '${db_is_successful_download_bool}' from the DB is not the same as the provided data: '${IS_SUCCESSFUL_DOWNLOAD}'.
    END

    IF    ${download_error_must_be_validated}
         Run Keyword If    '${db_download_error}' == '${DOWNLOAD_ERROR}'
         ...    Log Many  The DOWNLOAD_ERROR: '${db_download_error}' from the DB is the same as the provided data: '${DOWNLOAD_ERROR}'.
         ...  ELSE
         ...    Run Keyword And Continue On Failure    Fail  The DOWNLOAD_ERROR: '${db_download_error}' from the DB is not the same as the provided data: '${DOWNLOAD_ERROR}'.
    END

    IF    ${handshake_error_must_be_validated}
         IF    "${HANDSHAKE_ERROR}" != "${None}"
             ${json_string}=            Convert Dictionary to JSON    ${HANDSHAKE_ERROR}
             ${db_handshake_error}=     Convert Dictionary to JSON    ${db_handshake_error}
             ${json_string}=            Evaluate    json.loads('${json_string}')    modules=json
             ${db_handshake_error}=     Evaluate    json.loads('${db_handshake_error}')    modules=json
             Compare Key-Value Pairs of Two Dictionaries        ${db_handshake_error}    ${json_string}
         ELSE
             ${json_string}=    Set Variable    null
             Run Keyword If    '${db_handshake_error}' == '${json_string}'
             ...    Log Many  The HANDSHAKE_ERROR: '${db_handshake_error}' from the DB is the same as the provided data: '${json_string}'.
             ...  ELSE
             ...    Run Keyword And Continue On Failure    Fail  The HANDSHAKE_ERROR: '${db_handshake_error}' from the DB is not the same as the provided data: '${json_string}'.

         END

    END


