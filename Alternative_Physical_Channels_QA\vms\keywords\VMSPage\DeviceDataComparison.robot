*** Settings ***
Documentation    This file contains keywords for parsing and comparing device data
Library          Collections
Library          String
Library          BuiltIn

*** Keywords ***
Parse and compare device data
    [Documentation]    Parses and compares device data from UI and database sources
    [Arguments]    ${frontend_data}    ${database_data}
    
    # Initialize variables for storing parsed data
    ${frontend_devices}=    Create Dictionary
    ${database_devices}=    Create Dictionary
    ${comparison_report}=    Set Variable    Device Data Comparison Report:\n
    
    # Extract device data from frontend string
    # Format is typically: CallsloggedagainstDevices5ATM8BNA3CDM...
    ${frontend_clean}=    Remove String    ${frontend_data}    CallsloggedagainstDevices
    
    # Parse frontend data - pattern is usually [count][device_type]
    ${frontend_pairs}=    Create List
    ${current_number}=    Set Variable    ${EMPTY}
    ${current_text}=    Set Variable    ${EMPTY}
      # Parse character by character
    # Get the length of the string
    ${length}=    Get Length    ${frontend_clean}
    FOR    ${index}    IN RANGE    0    ${length}
        ${char}=    Get Substring    ${frontend_clean}    ${index}    ${index+1}
        ${is_digit}=    Run Keyword And Return Status    Should Match Regexp    ${char}    \\d
        
        # If it's a digit, add to current number
        ${current_number}=    Run Keyword If    ${is_digit}    Set Variable    ${current_number}${char}
                             ...    ELSE    Set Variable    ${current_number}
        
        # If it's text, add to current text                     
        ${current_text}=    Run Keyword If    not ${is_digit}    Set Variable    ${current_text}${char}
                          ...    ELSE    Set Variable    ${current_text}
          # Check if next character is a digit (to detect end of pattern)
        ${next_index}=    Evaluate    ${index} + 1
        ${is_last_char}=    Run Keyword And Return Status    Evaluate    ${next_index} >= ${length}
        
        # If not the last char, get the next char
        ${next_char}=    Run Keyword If    not ${is_last_char}    Get Substring    ${frontend_clean}    ${next_index}    ${next_index+1}
                        ...    ELSE    Set Variable    ${EMPTY}
                        
        ${next_is_digit}=    Run Keyword And Return Status    Should Match Regexp    ${next_char}    \\d
        
        # If we've completed a number-text pair OR we're at the end
        ${pair_complete}=    Run Keyword And Return Status    
        ...    Evaluate    (not ${is_digit} and ${next_is_digit}) or (not ${is_digit} and ${is_last_char})
        
        # If pair is complete and we have both number and text
        ${has_both_parts}=    Run Keyword And Return Status
        ...    Evaluate    "${current_number}" != "${EMPTY}" and "${current_text}" != "${EMPTY}"
        
        # If we've found a complete data pair
        Run Keyword If    ${pair_complete} and ${has_both_parts}
        ...    Run Keywords
        ...    Append To List    ${frontend_pairs}    ${current_number}:${current_text}
        ...    AND    Set To Dictionary    ${frontend_devices}    ${current_text}    ${current_number}
        ...    AND    Set Variable    ${current_number}    ${EMPTY}
        ...    AND    Set Variable    ${current_text}    ${EMPTY}
    END
    
    # Extract device data from database string using the same logic
    ${database_clean}=    Remove String    ${database_data}    CallsloggedagainstDevices
    
    # Parse database data - pattern is usually [count][device_type]
    ${database_pairs}=    Create List
    ${current_number}=    Set Variable    ${EMPTY}
    ${current_text}=    Set Variable    ${EMPTY}
      # Parse character by character using the same logic as above
    # Get the length of the string
    ${length}=    Get Length    ${database_clean}
    FOR    ${index}    IN RANGE    0    ${length}
        ${char}=    Get Substring    ${database_clean}    ${index}    ${index+1}
        ${is_digit}=    Run Keyword And Return Status    Should Match Regexp    ${char}    \\d
        
        # If it's a digit, add to current number
        ${current_number}=    Run Keyword If    ${is_digit}    Set Variable    ${current_number}${char}
                             ...    ELSE    Set Variable    ${current_number}
        
        # If it's text, add to current text                     
        ${current_text}=    Run Keyword If    not ${is_digit}    Set Variable    ${current_text}${char}
                          ...    ELSE    Set Variable    ${current_text}
          # Check if next character is a digit (to detect end of pattern)
        ${next_index}=    Evaluate    ${index} + 1
        ${is_last_char}=    Run Keyword And Return Status    Evaluate    ${next_index} >= ${length}
        
        # If not the last char, get the next char
        ${next_char}=    Run Keyword If    not ${is_last_char}    Get Substring    ${database_clean}    ${next_index}    ${next_index+1}
                        ...    ELSE    Set Variable    ${EMPTY}
                        
        ${next_is_digit}=    Run Keyword And Return Status    Should Match Regexp    ${next_char}    \\d
        
        # If we've completed a number-text pair OR we're at the end
        ${pair_complete}=    Run Keyword And Return Status    
        ...    Evaluate    (not ${is_digit} and ${next_is_digit}) or (not ${is_digit} and ${is_last_char})
        
        # If pair is complete and we have both number and text
        ${has_both_parts}=    Run Keyword And Return Status
        ...    Evaluate    "${current_number}" != "${EMPTY}" and "${current_text}" != "${EMPTY}"
        
        # If we've found a complete data pair
        Run Keyword If    ${pair_complete} and ${has_both_parts}
        ...    Run Keywords
        ...    Append To List    ${database_pairs}    ${current_number}:${current_text}
        ...    AND    Set To Dictionary    ${database_devices}    ${current_text}    ${current_number}
        ...    AND    Set Variable    ${current_number}    ${EMPTY}
        ...    AND    Set Variable    ${current_text}    ${EMPTY}
    END
    
    # Combine and deduplicate device types for comparison
    ${all_device_types}=    Create List
    ${frontend_keys}=    Get Dictionary Keys    ${frontend_devices}
    ${database_keys}=    Get Dictionary Keys    ${database_devices}
    
    # Add all frontend device types
    FOR    ${device}    IN    @{frontend_keys}
        Append To List    ${all_device_types}    ${device}
    END
    
    # Add database device types if not already in the list
    FOR    ${device}    IN    @{database_keys}
        ${exists}=    Run Keyword And Return Status    List Should Contain Value    ${all_device_types}    ${device}
        Run Keyword If    not ${exists}    Append To List    ${all_device_types}    ${device}
    END
    
    # Build comparison report
    ${has_differences}=    Set Variable    ${FALSE}
    
    # Summary section
    ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    Summary:\n
    ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    - Frontend data: ${frontend_data}\n
    ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    - Database data: ${database_data}\n\n
    
    # Detailed comparison
    ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    Detailed Comparison:\n
    
    FOR    ${device_type}    IN    @{all_device_types}
        # Get counts for each device, default to 0 if not found
        ${frontend_count}=    Run Keyword If    "${device_type}" in ${frontend_keys}    
        ...    Get From Dictionary    ${frontend_devices}    ${device_type}
        ...    ELSE    Set Variable    0
        
        ${database_count}=    Run Keyword If    "${device_type}" in ${database_keys}    
        ...    Get From Dictionary    ${database_devices}    ${device_type}
        ...    ELSE    Set Variable    0
        
        # Compare counts
        ${are_equal}=    Run Keyword And Return Status    Should Be Equal As Strings    ${frontend_count}    ${database_count}
        ${status}=    Set Variable If    ${are_equal}    ✓    ✗
        
        # Add to report
        ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    ${status} ${device_type}: Frontend=${frontend_count}, Database=${database_count}\n
        
        # Set flag if we found differences
        ${has_differences}=    Run Keyword If    not ${are_equal}    Set Variable    ${TRUE}
        ...    ELSE    Set Variable    ${has_differences}
    END
    
    # Final assessment
    ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    \nConclusion: 
    ${conclusion}=    Set Variable If    ${has_differences}
    ...    Differences found between frontend and database data.
    ...    Frontend and database data match perfectly.
    
    ${comparison_report}=    Catenate    SEPARATOR=    ${comparison_report}    ${conclusion}
    
    [Return]    ${comparison_report}
