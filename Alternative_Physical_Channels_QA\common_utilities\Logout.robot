*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Logs out of vms system

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                       SeleniumLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../future_fit_architecture_portal/keywords/common/Navigation.robot
Resource                                            ../future_fit_architecture_portal/keywords/common/GenericMethods.robot

*** Variables ***
${VMS_LOGOUT_BTN}                             name=ctl00$btnLogout
${LOGOUT_BTN}                                 xpath=//button[contains(text(),' Logout ')]
${LOGOUT_DROP_DOWN_BTN}                       xpath=//button[@aria-haspopup='menu']

*** Keywords ***
User logs out    

    Run Keyword and Ignore Error  Logout 
Logout
    Wait for spinner to disapear
    Click Element                                   ${LOGOUT_DROP_DOWN_BTN}
    Sleep    3s
    #Click Element                                   ${LOGOUT_BTN}
    #Sleep    6s
    Capture Page Screenshot   User_Logout.png
    Close Browser

The user logs out of VMS
    Wait Until Element Is Visible    ${VMS_LOGOUT_BTN}
    Log To Console    ------------------------------------ Logging out of VMS
    Click Button    ${VMS_LOGOUT_BTN}
    Sleep   3s
    Close Browser