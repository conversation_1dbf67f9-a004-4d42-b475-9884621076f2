<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 09:15:55.492" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\Dashboard\TC_11_GET_Dashboard_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:15:56.145" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:15:56.145" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:15:56.145" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Get all Dashboard Details using a Business User" line="43">
<kw name="GET Dashboard">
<arg>Gets Dashboard Details using a Business User auth</arg>
<arg>*********</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>Dashboard</arg>
<arg>200</arg>
<arg>OK</arg>
<arg>atmScheduleResultVersions[*]:atmNumber=08397</arg>
<arg>regionalSchedules[*]:region=North West</arg>
<arg>chartBars:province[*]=North West</arg>
<arg>chartBars:provinceLatestVersionValue[*]=1209</arg>
<arg>totalCampaigns=57</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 09:15:56.145" level="INFO">Set test documentation to:
Gets Dashboard Details using a Business User auth</msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:15:56.145" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '*********'.</msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<kw name="Given The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 09:15:56.145" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 09:15:56.145" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.145"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 09:15:56.145" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.369"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:15:56.369" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</kw>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:56.369"/>
</kw>
<kw name="When The user makes Get Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${SERVICE_PATH_ID}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<arg>${REST_PATH_ID}</arg>
<msg timestamp="******** 09:15:56.369" level="INFO">${end_point} = /Dashboard</msg>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</kw>
<status status="NOT RUN" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<msg timestamp="******** 09:15:56.369" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLR...</msg>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</kw>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</branch>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:56.369"/>
</if>
<kw name="GET On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<msg timestamp="******** 09:15:59.665" level="INFO">GET Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Dashboard 
 path_url=/Dashboard 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCIsImtpZCI6IkwxS2ZLRklfam5YYndXYzIyeFp4dzFzVUhIMCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fdSR85jLUjiP_yOEBnrdpNgKaAA7PcnB5k4nfJz9joG07H0tJMAo-XE_Wm8aGAgam-zML3jfZSNhcJL_Iw7-atPyO1gvcENTHwKgXnbIq8Hm0C81CWjepOKKVI4vH4Kd0UdadvIjusYbqV8PkaqwRacysjteDM0uF09pgxy041d8hburt3oclCrYAYPa_Ji5yrZfXvPD_mxoGgqTCzONOF4i2ntRthTmlqEjvK0G_vZfCYbqPc5zZ1qQ70PpHqkZ-LxgClyvgwmZviiawi_9DrC8zDGSu_9tCbkxDwfICx0K4LhunT9fgzakjHCJMs5RwgZnlhXGBLXUmVtAl2GGeg'} 
 body=None 
 </msg>
<msg timestamp="******** 09:15:59.665" level="INFO">GET Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/Dashboard 
 status=200, reason=OK 
 headers={'Date': 'Thu, 30 May 2024 07:15:59 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body={"totalCampaigns":57,"latestScheduleCount":840,"failedUploadSchedulesCount":0,"totalDevices":5729,"currentVersion":"v001Q22024","atmScheduleResultVersions":[{"atmNumber":"08397","address":"ATM UAT LAB, 268 Republic, ","region":"gauteng","scheduleVersion":"v001Q12024"}],"regionalSchedules":[{"region":"Blank","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Boland","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Dar es Salaam","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Eastern Cape","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Empty1","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Free State","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng","successfulCurrentScheduleUploads":52,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":55,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng North","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng South","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng West","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Internal","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Kwazulu Natal","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Limpopo","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Mobile","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Mpumalanga","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"North West","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Northern Cape","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Not selected","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"PILOT","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"RHINO CASH ULUNDI","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"SAVEWAYS BRANCH","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Score Senoane","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Test","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"TOTAL DOUGLASDALE","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"TOWERS LANGENHOVEN PARK","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Transkei","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"VIVA BINGO ATTERBURY","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"VIVA BINGO BALLITO","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Western Cape","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0}],"versionUploads":[],"chartBars":{"province":["Gauteng North","Gauteng South","Western Cape","Gauteng","Mpumalanga","Free State","North West","Eastern Cape","Internal","Kwazulu Natal","Limpopo","PRETORIA","PROTEA GLEN","Northern Cape"],"provinceLatestVersionValue":[7917,39041,40549,4558,2911,1209,349,1848,143,31,19,6,2,4]}} 
 </msg>
<msg timestamp="******** 09:15:59.665" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 09:15:59.665" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:59.665"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:15:59.665" level="INFO">${response.content} = {"totalCampaigns":57,"latestScheduleCount":840,"failedUploadSchedulesCount":0,"totalDevices":5729,"currentVersion":"v001Q22024","atmScheduleResultVersions":[{"atmNumber":"08397","address":"ATM UAT LAB...</msg>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.665"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:15:59.665" level="INFO">{"totalCampaigns":57,"latestScheduleCount":840,"failedUploadSchedulesCount":0,"totalDevices":5729,"currentVersion":"v001Q22024","atmScheduleResultVersions":[{"atmNumber":"08397","address":"ATM UAT LAB, 268 Republic, ","region":"gauteng","scheduleVersion":"v001Q12024"}],"regionalSchedules":[{"region":"Blank","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Boland","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Dar es Salaam","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Eastern Cape","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Empty1","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Free State","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng","successfulCurrentScheduleUploads":52,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":55,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng North","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng South","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Gauteng West","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Internal","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Kwazulu Natal","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Limpopo","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Mobile","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Mpumalanga","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"North West","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Northern Cape","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Not selected","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"PILOT","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"RHINO CASH ULUNDI","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"SAVEWAYS BRANCH","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Score Senoane","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Test","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"TOTAL DOUGLASDALE","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"TOWERS LANGENHOVEN PARK","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Transkei","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"VIVA BINGO ATTERBURY","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"VIVA BINGO BALLITO","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0},{"region":"Western Cape","successfulCurrentScheduleUploads":0,"failedCurrentScheduleUploads":0,"totalSuccessfulUploads":0,"totalFailedUploads":0,"targettedAtms":0}],"versionUploads":[],"chartBars":{"province":["Gauteng North","Gauteng South","Western Cape","Gauteng","Mpumalanga","Free State","North West","Eastern Cape","Internal","Kwazulu Natal","Limpopo","PRETORIA","PROTEA GLEN","Northern Cape"],"provinceLatestVersionValue":[7917,39041,40549,4558,2911,1209,349,1848,143,31,19,6,2,4]}}</msg>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.665"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:15:59.665" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '200'.</msg>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.665"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:15:59.665" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'OK'.</msg>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.665"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:15:59.665" level="INFO">${response.content} = {"totalCampaigns":57,"latestScheduleCount":840,"failedUploadSchedulesCount":0,"totalDevices":5729,"currentVersion":"v001Q22024","atmScheduleResultVersions":[{"atmNumber":"08397","address":"ATM UAT LAB...</msg>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.665"/>
</kw>
<status status="PASS" starttime="******** 09:15:56.369" endtime="******** 09:15:59.665"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:15:59.676" level="INFO">${returned_status_code} = 200</msg>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:15:59.676" level="INFO">Response Status Code : 200</msg>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:15:59.676" level="INFO">${returned_status_reason} = OK</msg>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<status status="PASS" starttime="******** 09:15:59.665" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{EXPECTED_FIELDS_VALUES}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}">atmScheduleResultVersions[*]:atmNumber</var>
<var name="${value}">08397</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:15:59.676" level="INFO">Field: atmScheduleResultVersions[*]:atmNumber , having a value: 08397 was found.</msg>
<msg timestamp="******** 09:15:59.676" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</iter>
<iter>
<var name="${key}">regionalSchedules[*]:region</var>
<var name="${value}">North West</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:15:59.676" level="INFO">Field: regionalSchedules[*]:region , having a value: North West was found.</msg>
<msg timestamp="******** 09:15:59.676" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</iter>
<iter>
<var name="${key}">chartBars:province[*]</var>
<var name="${value}">North West</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:15:59.676" level="INFO">Field: chartBars:province[*] , having a value: North West was found.</msg>
<msg timestamp="******** 09:15:59.676" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</kw>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.676"/>
</iter>
<iter>
<var name="${key}">chartBars:provinceLatestVersionValue[*]</var>
<var name="${value}">1209</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:15:59.692" level="INFO">Field: chartBars:provinceLatestVersionValue[*] , having a value: 1209 was found.</msg>
<msg timestamp="******** 09:15:59.692" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.692"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:15:59.692" endtime="******** 09:15:59.692"/>
</kw>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.692"/>
</iter>
<iter>
<var name="${key}">totalCampaigns</var>
<var name="${value}">57</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:15:59.697" level="INFO">Field: totalCampaigns , having a value: 57 was found.</msg>
<msg timestamp="******** 09:15:59.697" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:15:59.692" endtime="******** 09:15:59.697"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:15:59.697" endtime="******** 09:15:59.697"/>
</kw>
<status status="PASS" starttime="******** 09:15:59.692" endtime="******** 09:15:59.697"/>
</iter>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.697"/>
</for>
<status status="PASS" starttime="******** 09:15:59.676" endtime="******** 09:15:59.697"/>
</kw>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:59.697"/>
</kw>
<doc>Gets Dashboard Details using a Business User auth</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 09:15:56.145" endtime="******** 09:15:59.698"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 09:15:55.602" endtime="******** 09:16:02.947"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 09:16:02.947" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
