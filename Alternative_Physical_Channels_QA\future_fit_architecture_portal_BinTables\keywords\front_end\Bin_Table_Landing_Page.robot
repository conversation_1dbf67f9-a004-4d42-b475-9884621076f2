*** Settings ***
#Author Name               : <PERSON><PERSON>
#Email Address             : yaash.ram<PERSON><PERSON><EMAIL>


Documentation              Bin Tables - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime
Library                                              ../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../../future_fit_architecture_portal/keywords/common/GenericMethods.robot


*** Variables ***
${BIN_TABLES_MENU}                                xpath=//*[text()[normalize-space(.)='BIN TABLES']]
${USER_INFO_LOCATOR}                              xpath=//div[contains(@class, 'user-info')]//span
${BIN_TABLES_ADMIN_USER}                          ac30fc6b-5773-4edc-97f3-412e489e1c1f
${BIN_TABLES_CAPTURER_USER}                       decd98cd-20cf-4663-a6ad-60f6762bd241
${BIN_TABLES_APPROVER_USER}                       e2cec3df-91e1-4830-817b-0cbd22148e54
${BINS_MENU_VIEW_ENTRIES}                         xpath=//span[contains(text(),'View')]
${BINS_MENU}                                      xpath=//*[text()[normalize-space(.)='BINS']]
${BIN_TYPE_MENU}=                                 xpath=//*[text()[normalize-space(.)='BIN TYPES']]
${HOME_BUTTON}=                                   xpath=//*[contains(@class,'mat-typography')]/descendant::a
${ATM_MARKETING_MENU}=                            xpath=//*[text()[normalize-space(.)='ATM MARKETING']]
${BIN_TABLES_MENU}=                               xpath=//*[text()[normalize-space(.)='BIN TABLES']]
${BIN_TYPE_VIEW_MENU}=                            xpath=//*[text()[normalize-space(.)='View']]
${WELCOME_BOARD}=                                 xpath=//*[contains(@class,'welcome-board')]
${NOTICE_BOARD}=                                  xpath=//*[contains(@class,'notice-board')]
${APC_PORTAL_FIELD}=                              xpath=//*[text()[normalize-space(.)='APC Portal']]

*** Keywords ***
The user clicks on the Bin Table menu
    Wait Until Element Is Enabled    ${BIN_TABLES_MENU}
    Click Element    ${BIN_TABLES_MENU}
    

The user retreives the information and role displayed on the top right corner on the front-end 
    ${USER_INFO}=    Get Text    ${USER_INFO_LOCATOR}
    Log    User Info for current logged in User:${USER_INFO}
    Sleep    3s
    
    #Extract User Role Description from the front end 
    ${split_info}=    Split String    ${user_info}    ( 
    ${FRONT_END_ROLE_DESCRIPTION}=    Get From List    ${split_info}    -1
    #Remove the closing parenthesis from the role
    ${FRONT_END_ROLE_DESCRIPTION}=    Remove String    ${FRONT_END_ROLE_DESCRIPTION}    )
    
    Log    User Role: ${FRONT_END_ROLE_DESCRIPTION}
    Set Suite Variable    ${FRONT_END_ROLE_DESCRIPTION}

The user's access to Bin Tables must match the access level of the logged-in user retrieved from the backend system (devtools)
    [Arguments]     ${REQUIRED_USER_ACCESS}


    #This is the boolean value that checks if the correct user access is provided
    ${correct_access_name_provided}=        Set Variable    ${False}
    ${correct_access_name_provided}=        Set Variable If
         ...       '${REQUIRED_USER_ACCESS}' == 'Admin'        ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'Capturer'     ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'Approver'     ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'ADMIN'        ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'CAPTURER'     ${True}
         ...       '${REQUIRED_USER_ACCESS}' == 'APPROVER'     ${True}

    Run Keyword If    not ${correct_access_name_provided}
    ...    Fail     Please provide the right data for REQUIRED_USER_ACCESS. Expected values are: Admin, Capturer or Approver.

    ${uiid_to_verify}=        Set Variable If
         ...       '${REQUIRED_USER_ACCESS}' == 'Admin'        ${BIN_TABLES_ADMIN_USER}
         ...       '${REQUIRED_USER_ACCESS}' == 'Capturer'     ${BIN_TABLES_CAPTURER_USER}
         ...       '${REQUIRED_USER_ACCESS}' == 'Approver'     ${BIN_TABLES_APPROVER_USER}

    ${bin_tables_roles_not_required_by_user}=     Create List    ${BIN_TABLES_ADMIN_USER}     ${BIN_TABLES_CAPTURER_USER}       ${BIN_TABLES_APPROVER_USER}

    Remove Values From List    ${bin_tables_roles_not_required_by_user}    ${uiid_to_verify}
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${local_storage_keys}=    get local storage keys and values     net-accesstoken
    Log Many   Local Storage Keys:  ${local_storage_keys.items()}
    ${local_storage_keys_len}=   Get Length     ${local_storage_keys.items()}
    FOR    ${key}    ${value}    IN    &{local_storage_keys}
        ${json_obj}=    Evaluate    json.loads('''${value}''')    json
        ${secret}=    Get From Dictionary    ${json_obj}    secret
        Exit For Loop
    END

    ${decoded_token}=      decode jwt       ${secret}
    ${groups}=    Get From Dictionary    ${decoded_token}    groups
    Log Many        ${groups}
    ${required_group_found}=       Set Variable     ${False}
    FOR    ${item}    IN    @{groups}
        Log Many    List Item: ${item}
        #Verify that the correct groups have been allocated to the user
        FOR    ${unwanted_group}    IN    @{bin_tables_roles_not_required_by_user}
            IF    '${item.strip()}' == '${unwanted_group.strip()}'
                ${unwanted_group_name}=     Get From Dictionary     ${bin_tables_roles}         ${unwanted_group}
                Run Keyword And Continue On Failure    Fail    The Bin Tables role: '${unwanted_group_name}' has been assigned to the '${REQUIRED_USER_ACCESS}' user.
            END
        END

        IF    '${item.strip()}' == '${uiid_to_verify.strip()}'
                ${required_group_found}=       Set Variable     ${True}
        END
    END



    Run Keyword If    ${required_group_found}
    ...    Log Many    The Bin Tables role: '${REQUIRED_USER_ACCESS}' which is supposed to be assigned to the current user was found on the groups allocated to the user's access.
    ...  ELSE
    ...    Fail    The Bin Tables role: '${REQUIRED_USER_ACCESS}' which is supposed to be assigned to the current user was not found on the groups allocated to the user's access.


The user retrieves the role of the logged-in user from the backend (DevTools)
    ${local_storage_keys}=    get local storage keys and values     net-accesstoken
    Log Many   Local Storage Keys:  ${local_storage_keys.items()}
    ${local_storage_keys_len}=   Get Length     ${local_storage_keys.items()}
    FOR    ${key}    ${value}    IN    &{local_storage_keys}
        ${json_obj}=    Evaluate    json.loads('''${value}''')    json
        ${secret}=    Get From Dictionary    ${json_obj}    secret
        Exit For Loop
    END
    ${bin_tables_roles}=      Create Dictionary         ${BIN_TABLES_ADMIN_USER}=Admin      ${BIN_TABLES_CAPTURER_USER}=Capturer          ${BIN_TABLES_APPROVER_USER}=Approver
    ${decoded_token}=      decode jwt       ${secret}
    ${groups}=    Get From Dictionary    ${decoded_token}    groups
    Log Many        ${groups}
    ${required_group_found}=       Set Variable     ${False}
    FOR    ${item}    IN    @{groups}
        Log Many    List Item: ${item}
        #Check if the user has one of Bin Tables roles
        ${bin_table_role_found}=        Run Keyword And Return Status    Should Be Equal As Strings    ${item.strip()}    ${BIN_TABLES_ADMIN_USER.strip()}
        IF    ${bin_table_role_found}
            ${required_group_found}=       Set Variable     ${True}
            Exit For Loop
        END
        ${bin_table_role_found}=        Run Keyword And Return Status    Should Be Equal As Strings    ${item.strip()}    ${BIN_TABLES_CAPTURER_USER.strip()}
        IF    ${bin_table_role_found}
            ${required_group_found}=       Set Variable     ${True}
            Exit For Loop
        END
        ${bin_table_role_found}=        Run Keyword And Return Status    Should Be Equal As Strings    ${item.strip()}    ${BIN_TABLES_APPROVER_USER.strip()}
        IF    ${bin_table_role_found}
            ${required_group_found}=       Set Variable     ${True}
            Exit For Loop
        END
    END
    #If the user has a Bin Tables role then return the role
    IF    ${required_group_found}
            ${user_role_description}=    Get From Dictionary    ${bin_tables_roles}    ${item.strip()}
            Log Many     The current user is allocated the Bin Tables role of '${user_role_description}'.
            #RETURN   ${user_role_description}
    ELSE
            Fail     The current user is not allocated any of the Bin Tables roles.
            #RETURN   ${user_role_description}
    END
    
    Log    User Role:${user_role_description}
    Set Global Variable    ${USER_ROLE_RETRIEVED_DEV_TOOLS}    ${user_role_description}
    

The user verifies that the role displayed on the front end matches the assigned role retrieved from the backend system (devtools)
    #Compare front end retrived user role to backend user role 
    Should Be Equal As Strings    ${USER_ROLE_RETRIEVED_DEV_TOOLS}    ${FRONT_END_ROLE_DESCRIPTION}
    
The user clicks on the Home Icon from the Bin Table Landing Page 
    #Verify user is on the Bin Table Landing Page 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BINS_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    User is not on the Bin Table landing page

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TYPE_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    User is not on the Bin Table landing page

    #Click the home button
    Wait Until Element Is Enabled    ${HOME_BUTTON}
    Click Element    ${HOME_BUTTON}
    Sleep    2s 

The user verifies that they were directed to the APC Landing Page
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${ATM_MARKETING_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    User was not directed to the APC Landing page after clicking the home button from the Bin Table landing page

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TABLES_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    User was not directed to the APC Landing page after clicking the home button from the Bin Table landing page

The user clicks on the Bin Type Menu
    Wait Until Element Is Enabled    ${BIN_TYPE_MENU}
    Click Element    ${BIN_TYPE_MENU}
    Sleep    2s 

The user verfies that the Bin Type menu is Accessible
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TYPE_VIEW_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    Bin Type menu is not Accessible

The user verifies that the Bin Table Landing page loads correctly upon selection
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BINS_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    Bins Menu not displayed 

    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${BIN_TYPE_MENU}
    Run Keyword If    '${status}' == 'False'    Fail    Bin Type Menu not displayed

    #Welcome Panel Display
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${WELCOME_BOARD}
    Run Keyword If    '${status}' == 'False'    Fail    Welcome board not displayed 

    ${WELCOME_BOARD_TEXT}=    Get Text    ${WELCOME_BOARD}
    Log    Welcome Board contains text: ${WELCOME_BOARD_TEXT}

    #Notice Board Display 
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${NOTICE_BOARD}
    Run Keyword If    '${status}' == 'False'    Fail    Notice Board not displayed

    ${NOTICE_BOARD_TEXT}=    Get Text    ${NOTICE_BOARD}
    Log    Notice Board contains text: ${NOTICE_BOARD_TEXT}

The user clicks on the Bins Menu
    Wait Until Element Is Enabled    ${BINS_MENU}
    Click Element    ${BINS_MENU}
    Sleep    2s 

The user verfies that the Bin menu is Accessible
    ${status}=      Correct Page is displayed    View Entries
    Run Keyword If    '${status}' == 'False'    Fail    Bin menu is not Accessible

The user verfies that the "APC Portal" field appears correctly
    ${status}=    Run Keyword And Return Status    Element Should Be Visible    ${APC_PORTAL_FIELD}
    Run Keyword If    '${status}' == 'False'    Fail    APC Portal Field is not visible from the Bin Table Landing Page