<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 09:11:48.116" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Controllers\ATMMarketingResults\TC_09_POST_ATMMarketingResults_CONTROLLER.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<test id="s1-t1" name="FFT - Controllers - Post ATM Marketing Result using a Business User" line="43">
<kw name="Post ATM Marketing Result">
<arg>Post ATM Marketing Result using a Business User</arg>
<arg>155057483</arg>
<arg>ATMMarketingResult</arg>
<arg>APC_API_UAT_BASE_URL</arg>
<arg>ATMMARKETINGRESULT</arg>
<arg>201</arg>
<arg>Created</arg>
<arg>deviceId=08397</arg>
<arg>deviceType=ATM</arg>
<arg>scheduleVersion=v001Q12024</arg>
<arg>uploadDate=2023-11-27T13:00:00.001</arg>
<arg>uploadResult=Successful</arg>
<arg>resultDescription=Successfully implemented marketing schedule v001Q12024</arg>
<arg>isUpaloadSuccessful=True</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">Set test documentation to:
Post ATM Marketing Result using a Business User</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057483'.</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Given The user prepares a json payload" library="RestCalls">
<arg>${SUITE_NAME}</arg>
<arg>${DATA_FILE}</arg>
<arg>&amp;{KW_ARGS}</arg>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${DATA_FILE}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:11:48.556" level="INFO">${path} = data/ATMMarketingResult.json</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.556"/>
</kw>
<kw name="Populate Json File With" library="CreateRestPayloads">
<arg>${path}</arg>
<arg>&amp;{KW_ARGS}</arg>
<msg timestamp="******** 09:11:48.603" level="INFO">Json Loaded
JSON file updated successfully</msg>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.603"/>
</kw>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:48.603"/>
</kw>
<kw name="When The user creates a rest session" library="RestCalls">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<msg timestamp="******** 09:11:48.603" level="INFO">Property value fetched is:  https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<msg timestamp="******** 09:11:48.603" level="INFO">${base_url} = https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za</msg>
<status status="PASS" starttime="******** 09:11:48.603" endtime="******** 09:11:48.603"/>
</kw>
<kw name="Create Session" library="RequestsLibrary">
<arg>${my_session}</arg>
<arg>${baseUrl}</arg>
<arg>verify=False</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<msg timestamp="******** 09:11:48.603" level="INFO">Creating Session using : alias=, url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<status status="PASS" starttime="******** 09:11:48.603" endtime="******** 09:11:48.804"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<arg>${my_session}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:48.804"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>'Seesion Created!'</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:11:48.804" level="INFO">'Seesion Created!'</msg>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:48.804"/>
</kw>
<status status="PASS" starttime="******** 09:11:48.603" endtime="******** 09:11:48.804"/>
</kw>
<kw name="And The user makes Post Rest Call" library="RestCalls">
<arg>${SERVICE_PATH}</arg>
<arg>${DATA_FILE}</arg>
<arg>${EXPECTED_STATUS_CODE}</arg>
<kw name="Set Variable" library="BuiltIn">
<var>${root}</var>
<arg>data/</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:11:48.804" level="INFO">${root} = data/</msg>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:48.804"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${extension}</var>
<arg>.json</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:11:48.804" level="INFO">${extension} = .json</msg>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:48.804"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${path}</var>
<arg>${root}${DATA_FILE}${extension}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 09:11:48.804" level="INFO">${path} = data/ATMMarketingResult.json</msg>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:48.804"/>
</kw>
<kw name="Load Json From File" library="JSONLibrary">
<var>${payload}</var>
<arg>${path}</arg>
<doc>Load JSON from file.</doc>
<msg timestamp="******** 09:11:48.812" level="INFO">${payload} = {'deviceId': '08397', 'deviceType': 'ATM', 'scheduleVersion': 'v001Q12024', 'uploadDate': '2024-05-30T09:11:48.587Z', 'uploadResult': 'Successful', 'resultDescription': 'Successfully implemented marke...</msg>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:48.812"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${payload}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:11:48.812" level="INFO">{'deviceId': '08397', 'deviceType': 'ATM', 'scheduleVersion': 'v001Q12024', 'uploadDate': '2024-05-30T09:11:48.587Z', 'uploadResult': 'Successful', 'resultDescription': 'Successfully implemented marketing schedule v001Q12024', 'isUpaloadSuccessful': True}</msg>
<status status="PASS" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</kw>
<kw name="Get Service Path" library="CreateRestPayloads">
<var>${end_point}</var>
<arg>${REST_PATH}</arg>
<msg timestamp="******** 09:11:48.812" level="INFO">${end_point} = /ATMMarketingResult</msg>
<status status="PASS" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</kw>
<if>
<branch type="IF" condition="'${EXPECTED_STATUS_CODE}' != '200'">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${INVALID_BEARER_TOKEN}</arg>
<msg timestamp="******** 09:11:48.812" level="INFO">${REST_HEADERS} = {'Accept': '*/*', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Accept-Encoding': 'gzip, deflate, br', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthY...</msg>
<status status="PASS" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</kw>
<status status="PASS" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</branch>
<branch type="ELSE">
<kw name="Get Rest Api Headers" library="CreateRestPayloads">
<var>${REST_HEADERS}</var>
<arg>${BEARER_TOKEN}</arg>
<status status="NOT RUN" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</kw>
<status status="NOT RUN" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</branch>
<status status="PASS" starttime="******** 09:11:48.812" endtime="******** 09:11:48.812"/>
</if>
<kw name="POST On Session" library="RequestsLibrary">
<var>${response}</var>
<arg>${my_session}</arg>
<arg>${end_point}</arg>
<arg>headers=${REST_HEADERS}</arg>
<arg>json=${payload}</arg>
<arg>expected_status=anything</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<msg timestamp="******** 09:11:49.017" level="INFO">POST Request : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingResult 
 path_url=/ATMMarketingResult 
 headers={'User-Agent': 'python-requests/2.31.0', 'Accept-Encoding': 'gzip, deflate, br', 'Accept': '*/*', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyIsImtpZCI6ImtXYmthYTZxczh3c1RuQndpaU5ZT2hIYm5BdyJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FI26FGo9WE2UvoEncNGM1-63BboD54HmUGkgw_UF2TVGc8-C_0Wziv0KXECaaU-ExQNli2I5ZD4qgamPXa_daS9DVUmkurKQuRCSv_FV811I4ZkDCCYxVyl4G_U2Cbp82Wx9X7Xojur6lOkmu8Mo7F2YZ3gENVyF1httwzYPHFJ3qFJ6jQD7UtNDrG3D5IYZQbA06dRtu0Oo6AQq54EYMplf1SvpX0_nA6trvVsF6V9U09lbtYp6vjAYzebkWtBrNkNhojhVvZeKsgPruGCoPjPLkswCwb4dlKTC2mBeCapiHc3boy7RH0-tvLbnExqqGcW377ah9873_lUqRIBc4g', 'Content-Length': '255'} 
 body=b'{"deviceId": "08397", "deviceType": "ATM", "scheduleVersion": "v001Q12024", "uploadDate": "2024-05-30T09:11:48.587Z", "uploadResult": "Successful", "resultDescription": "Successfully implemented marketing schedule v001Q12024", "isUpaloadSuccessful": true}' 
 </msg>
<msg timestamp="******** 09:11:49.017" level="INFO">POST Response : url=https://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingResult 
 status=201, reason=Created 
 headers={'Date': 'Thu, 30 May 2024 07:11:48 GMT', 'Content-Type': 'application/json; charset=utf-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'Location': 'http://atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za/ATMMarketingResult/0', 'Strict-Transport-Security': 'max-age=********; includeSubDomains'} 
 body={"id":0,"deviceId":"08397","deviceType":"ATM","scheduleVersion":"v001Q12024","uploadDate":"2024-05-30T09:11:48.587Z","uploadResult":"Successful","resultDescription":"Successfully implemented marketing schedule v001Q12024","isUpaloadSuccessful":true} 
 </msg>
<msg timestamp="******** 09:11:49.019" level="INFO">C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\urllib3\connectionpool.py:1061: InsecureRequestWarning: Unverified HTTPS request is being made to host 'atm-marketing.atm-marketing-uat.rbb-banking.sdc-nonprod.caas.absa.co.za'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/1.26.x/advanced-usage.html#ssl-warnings
  warnings.warn(</msg>
<msg timestamp="******** 09:11:49.021" level="INFO">${response} = &lt;Response [201]&gt;</msg>
<status status="PASS" starttime="******** 09:11:48.812" endtime="******** 09:11:49.021"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>${response.content}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:11:49.021" level="INFO">{"id":0,"deviceId":"08397","deviceType":"ATM","scheduleVersion":"v001Q12024","uploadDate":"2024-05-30T09:11:48.587Z","uploadResult":"Successful","resultDescription":"Successfully implemented marketing schedule v001Q12024","isUpaloadSuccessful":true}</msg>
<status status="PASS" starttime="******** 09:11:49.021" endtime="******** 09:11:49.021"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_CODE</arg>
<arg>${response.status_code}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:11:49.023" level="INFO">Environment variable 'JSON_RESPONSE_CODE' set to value '201'.</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>JSON_RESPONSE_REASON</arg>
<arg>${response.reason}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 09:11:49.023" level="INFO">Environment variable 'JSON_RESPONSE_REASON' set to value 'Created'.</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Set Global Variable" library="BuiltIn">
<arg>${response.content}</arg>
<arg>${response.content}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<msg timestamp="******** 09:11:49.023" level="INFO">${response.content} = {"id":0,"deviceId":"08397","deviceType":"ATM","scheduleVersion":"v001Q12024","uploadDate":"2024-05-30T09:11:48.587Z","uploadResult":"Successful","resultDescription":"Successfully implemented marketing...</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<status status="PASS" starttime="******** 09:11:48.804" endtime="******** 09:11:49.023"/>
</kw>
<kw name="And The service returns http status" library="RestCalls">
<arg>${EXPECTED_STATUS_CODE}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_code}</var>
<arg>JSON_RESPONSE_CODE</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:11:49.023" level="INFO">${returned_status_code} = 201</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Response Status Code : ${returned_status_code}</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 09:11:49.023" level="INFO">Response Status Code : 201</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Status Should Be" library="RequestsLibrary">
<arg>${STATUS_CODE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Return From Keyword If" library="BuiltIn">
<arg>'${JSON_RESPONSE_REASON}'==''</arg>
<doc>Returns from the enclosing user keyword if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${returned_status_reason}</var>
<arg>JSON_RESPONSE_REASON</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 09:11:49.023" level="INFO">${returned_status_reason} = Created</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Should Be Equal" library="BuiltIn">
<arg>${returned_status_reason}</arg>
<arg>${JSON_RESPONSE_REASON}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.023"/>
</kw>
<kw name="Then The rest service must return the response which contains" library="RestCalls">
<arg>&amp;{KW_ARGS}</arg>
<for flavor="IN">
<var>${key}</var>
<var>${value}</var>
<value>&amp;{EXPECTED_FIELDS_VALUES}</value>
<iter>
<var name="${key}">deviceId</var>
<var name="${value}">08397</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.030" level="INFO">Field: deviceId , having a value: 08397 was found.</msg>
<msg timestamp="******** 09:11:49.030" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.030"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.030" endtime="******** 09:11:49.030"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.030"/>
</iter>
<iter>
<var name="${key}">deviceType</var>
<var name="${value}">ATM</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.038" level="INFO">Field: deviceType , having a value: ATM was found.</msg>
<msg timestamp="******** 09:11:49.038" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.030" endtime="******** 09:11:49.038"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.038" endtime="******** 09:11:49.038"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.030" endtime="******** 09:11:49.038"/>
</iter>
<iter>
<var name="${key}">scheduleVersion</var>
<var name="${value}">v001Q12024</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.046" level="INFO">Field: scheduleVersion , having a value: v001Q12024 was found.</msg>
<msg timestamp="******** 09:11:49.046" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.038" endtime="******** 09:11:49.046"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.038" endtime="******** 09:11:49.046"/>
</iter>
<iter>
<var name="${key}">uploadDate</var>
<var name="${value}">2023-11-27T13:00:00.001</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.046" level="INFO">Field: uploadDate , having a value: 2024-05-30T09:11:48.587Z was found.</msg>
<msg timestamp="******** 09:11:49.046" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</iter>
<iter>
<var name="${key}">uploadResult</var>
<var name="${value}">Successful</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.046" level="INFO">Field: uploadResult , having a value: Successful was found.</msg>
<msg timestamp="******** 09:11:49.046" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</iter>
<iter>
<var name="${key}">resultDescription</var>
<var name="${value}">Successfully implemented marketing schedule v001Q12024</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.046" level="INFO">Field: resultDescription , having a value: Successfully implemented marketing schedule v001Q12024 was found.</msg>
<msg timestamp="******** 09:11:49.046" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</iter>
<iter>
<var name="${key}">isUpaloadSuccessful</var>
<var name="${value}">True</var>
<kw name="Value Exists On Json Response" library="CreateRestPayloads">
<var>${key_value_found}</var>
<arg>${key}</arg>
<arg>${value}</arg>
<arg>${response.content}</arg>
<msg timestamp="******** 09:11:49.046" level="INFO">Field: isUpaloadSuccessful , having a value: True was found.</msg>
<msg timestamp="******** 09:11:49.046" level="INFO">${key_value_found} = True</msg>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<kw name="Should Be True" library="BuiltIn">
<arg>${key_value_found}</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</kw>
<status status="PASS" starttime="******** 09:11:49.046" endtime="******** 09:11:49.046"/>
</iter>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.046"/>
</for>
<status status="PASS" starttime="******** 09:11:49.023" endtime="******** 09:11:49.046"/>
</kw>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:49.046"/>
</kw>
<doc>Post ATM Marketing Result using a Business User</doc>
<tag>FFT_HEALTHCHECK</tag>
<tag>REGRESSION</tag>
<status status="PASS" starttime="******** 09:11:48.556" endtime="******** 09:11:49.046"/>
</test>
<doc>This is the test suite for creating an ATM Marketing Campaign using the Controller</doc>
<status status="PASS" starttime="******** 09:11:48.258" endtime="******** 09:11:52.348"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">FFT_HEALTHCHECK</stat>
<stat pass="1" fail="0" skip="0">REGRESSION</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Future-Fit Portal">Future-Fit Portal</stat>
</suite>
</statistics>
<errors>
<msg timestamp="******** 09:11:52.339" level="ERROR">Calling method 'end_test' of listener 'C:\Users\<USER>\source\repos\future_fit_repo\utility\PostExecutionUpdate.py' failed: ConnectionError: ('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))</msg>
</errors>
</robot>
