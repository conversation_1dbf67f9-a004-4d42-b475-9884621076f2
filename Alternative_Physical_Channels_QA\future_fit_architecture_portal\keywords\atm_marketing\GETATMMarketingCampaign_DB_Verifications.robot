*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             String
Variables                                          ../../utility/SQLVariables.py
#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***
Verify GET ATMMarketingCampaign controller fields on the database
    [Arguments]     ${json_response}

    ${new_string}=   Convert To String    ${json_response}
    ${dict_value}=      Convert String to Dictionary    ${new_string}
    #Loop through the ATMs dictionary
    FOR    ${item}    IN    @{dict_value}
        #Get the current campaign ID
        ${campaign_id}=    Get From Dictionary    ${item}    id
        #Get the current campaign name
        ${campaign_name}=    Get From Dictionary    ${item}    campaignName
        #Get the current campaign 'campaignBy'
        ${campaign_by}=    Get From Dictionary    ${item}    campaignBy
        #Get the current campaign 'updatedBy'
        ${campaign_updated_by}=    Get From Dictionary    ${item}    updatedBy
         #Get the current campaign 'campaignStartDate'
        ${campaign_campaign_start_date}=    Get From Dictionary    ${item}    campaignStartDate
        ${campaign_campaign_start_date}=    Replace String  ${campaign_campaign_start_date}     T       ${SPACE}
         #Get the current campaign 'campaignEndDate'
        ${campaign_campaign_end_date}=    Get From Dictionary    ${item}    campaignEndDate
        ${campaign_campaign_end_date}=    Replace String  ${campaign_campaign_end_date}     T       ${SPACE}
         #Get the current campaign 'isActive'
        ${campaign_is_active}=    Get From Dictionary    ${item}    isActive
         #Get the current campaign 'isApproved'
        ${campaign_is_approved}=    Get From Dictionary    ${item}    isApproved
        #Get the current campaign Schedule version
        ${campaign_schedule_version}=    Get From Dictionary    ${item}    version
        #Get the current campaign 'isTargetted'
        ${campaign_is_targeted}=    Get From Dictionary    ${item}    isTargetted
        #Get the campaign lastUpdate details
        ${campaign_lastUpdate_details}=    Get From Dictionary    ${item}    lastUpdate
        ${lastUpdate_id}=    Get From Dictionary    ${campaign_lastUpdate_details}    id
        ${lastUpdate_campaignId}=    Get From Dictionary    ${campaign_lastUpdate_details}    campaignId
        ${lastUpdate_updatedBy}=    Get From Dictionary    ${campaign_lastUpdate_details}    updatedBy
        ${lastUpdate_updatedDate}=    Get From Dictionary    ${campaign_lastUpdate_details}    updatedDate

        IF    '${lastUpdate_updatedDate}' != 'None'
            ${lastUpdate_updatedDate}=    Replace String  ${lastUpdate_updatedDate}     T       ${SPACE}
        END


        ${lastUpdate_updateDescription}=    Get From Dictionary    ${campaign_lastUpdate_details}    updateDescription
        ${lastUpdate_approvalId}=    Get From Dictionary    ${campaign_lastUpdate_details}    approvalId
        #Get the campaign screen details
        ${campaign_screen_details}=    Get From Dictionary    ${item}    screen
        ${screen_id}=    Get From Dictionary    ${campaign_screen_details}    id
        ${screen_type}=    Get From Dictionary    ${campaign_screen_details}    screenType
        ${screen_number}=    Get From Dictionary    ${campaign_screen_details}    screenNumber
        ${screen_channelId}=    Get From Dictionary    ${campaign_screen_details}    channelId
         #Get the campaign marketingChannel details
        ${campaign_marketing_channel}=    Get From Dictionary    ${item}    marketingChannel
        ${marketing_channel_id}=    Get From Dictionary    ${campaign_marketing_channel}    id
        ${marketing_channel}=    Get From Dictionary    ${campaign_marketing_channel}    channel
        ${marketing_channel_image_resolution}=    Get From Dictionary    ${campaign_marketing_channel}    imageResolution

         #Get the campaign targetData details
        ${campaign_target_data}=    Get From Dictionary    ${item}    targetData
         #Read the target data only if it is valid
        IF    "${campaign_target_data}" != "None"
            ${campaign_target_data_isTargetRegion}=    Get From Dictionary    ${campaign_target_data}    isTargetRegion
            ${campaign_target_data_target_region_or_atm}=    Get From Dictionary    ${campaign_target_data}    targetRegionOrAtm
        END
        #Retrive the details of the current campaign from the database and verify them against the controller details
         IF    '${lastUpdate_updatedDate}' != 'None'
            ${db_campaign_data}=        Get the campaign details from the database      ${campaign_id}     ${lastUpdate_updatedDate}      ${campaign_schedule_version}
            #verify the details of the current campaig using the database information
            ${db_campaign_id}=    Get From Dictionary    ${db_campaign_data}    id
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_id}    ${db_campaign_id}
            Log Many     Comparison result for 'Campaign ID' : Controller Results = ${screen_id}, Database Results = ${db_campaign_id}


            ${db_campaign_name}=    Get From Dictionary    ${db_campaign_data}    campaignName
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_name}    ${db_campaign_name}
            Log Many     Comparison result for 'Campaign Name' : Controller Results = ${campaign_name}, Database Results = ${db_campaign_name}

            ${db_campaign_by}=    Get From Dictionary    ${db_campaign_data}    campaignBy
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_by}    ${db_campaign_by}
            Log Many     Comparison result for 'Campaign By' : Controller Results = ${campaign_by}, Database Results = ${db_campaign_by}


            ${db_campaign_updated_by}=    Get From Dictionary    ${db_campaign_data}    campaignUpdatedBy
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_updated_by}    ${db_campaign_updated_by}
            Log Many     Comparison result for 'campaign_updated_by' : Controller Results = ${campaign_updated_by}, Database Results = ${db_campaign_updated_by}


            ${db_campaign_campaign_start_date}=    Get From Dictionary    ${db_campaign_data}    campaignStartDate
            ${length}=      Get Length    ${campaign_campaign_start_date}
            ${length_plus_one} =    Evaluate    ${length} + 1
            ${db_campaign_campaign_start_date}=    Get Substring    ${db_campaign_campaign_start_date}    0     ${length}
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_campaign_start_date}    ${db_campaign_campaign_start_date}
            Log Many     Comparison result for 'campaign_campaign_start_date' : Controller Results = ${campaign_campaign_start_date}, Database Results = ${db_campaign_campaign_start_date}


            ${db_campaign_campaign_end_date}=    Get From Dictionary    ${db_campaign_data}    campaignEndDate

            ${length}=      Get Length    ${campaign_campaign_end_date}
            ${length_plus_one} =    Evaluate    ${length} + 1
            ${db_campaign_campaign_end_date}=    Get Substring    ${db_campaign_campaign_end_date}    0     ${length}

            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_campaign_end_date}    ${db_campaign_campaign_end_date}
            Log Many     Comparison result for 'campaign_campaign_end_date' : Controller Results = ${campaign_campaign_end_date}, Database Results = ${db_campaign_campaign_end_date}

            ${db_campaign_is_active}=    Get From Dictionary    ${db_campaign_data}    isActive
            IF  '${db_campaign_is_active}' == '1'
               ${db_campaign_is_active}        Set Variable    ${True}
            ELSE
               ${db_campaign_is_active}        Set Variable    ${False}
            END
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_is_active}    ${db_campaign_is_active}
            Log Many     Comparison result for 'campaign_is_active' : Controller Results = ${campaign_is_active}, Database Results = ${db_campaign_is_active}



            ${db_campaign_is_approved}=    Get From Dictionary    ${db_campaign_data}    isApproved

            IF  '${db_campaign_is_approved}' == '1'
                ${db_campaign_is_approved}        Set Variable    ${True}
            ELSE
                ${db_campaign_is_approved}        Set Variable    ${False}
            END
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_is_approved}    ${db_campaign_is_approved}
            Log Many     Comparison result for 'campaign_is_approved' : Controller Results = ${campaign_is_approved}, Database Results = ${db_campaign_is_approved}


            ${db_campaign_schedule_version}=    Get From Dictionary    ${db_campaign_data}    version
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_schedule_version}    ${db_campaign_schedule_version}
            Log Many     Comparison result for 'campaign_schedule_version' : Controller Results = ${campaign_schedule_version}, Database Results = ${db_campaign_schedule_version}


            ${db_campaign_is_targeted}=    Get From Dictionary    ${db_campaign_data}    isTargetted
            IF  '${db_campaign_is_targeted}' == '1'

                 ${db_campaign_is_targeted}        Set Variable    ${True}


                 IF     ${campaign_target_data_isTargetRegion} == ${True}
                    ${db_campaign_target_data_target_region_or_atm}=    Get From Dictionary    ${db_campaign_data}    region
                 ELSE
                    IF      '${campaign_target_data_target_region_or_atm}' != 'null' and '${campaign_target_data_target_region_or_atm}' != 'None'
                        ${db_campaign_target_data_target_region_or_atm}=    Get From Dictionary    ${db_campaign_data}    atmNumber
                    END
                 END

                 IF    '${campaign_target_data_target_region_or_atm}' != 'null' and '${campaign_target_data_target_region_or_atm}' != 'None'
                     Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_target_data_target_region_or_atm}    ${db_campaign_target_data_target_region_or_atm}
                     Log Many     Comparison result for 'campaign_target_data_target_region_or_atm' : Controller Results = ${campaign_target_data_target_region_or_atm}, Database Results = ${db_campaign_target_data_target_region_or_atm}
                 END
            ELSE
                ${db_campaign_is_targeted}        Set Variable    ${False}
            END
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${campaign_is_targeted}    ${db_campaign_is_targeted}
            Log Many     Comparison result for 'campaign_is_targeted' : Controller Results = ${campaign_is_targeted}, Database Results = ${db_campaign_is_targeted}


            ${db_campaign_lastUpdate_id}=    Get From Dictionary    ${db_campaign_data}    campaignHistoryID
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${lastUpdate_id}    ${db_campaign_lastUpdate_id}
            Log Many     Comparison result for 'lastUpdate_id' : Controller Results = ${lastUpdate_id}, Database Results = ${db_campaign_lastUpdate_id}


            ${db_campaign_lastUpdate_campaignId}=    Get From Dictionary    ${db_campaign_data}    id
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${lastUpdate_campaignId}    ${db_campaign_lastUpdate_campaignId}
            Log Many     Comparison result for 'lastUpdate_campaignId' : Controller Results = ${lastUpdate_campaignId}, Database Results = ${db_campaign_lastUpdate_campaignId}

            ${db_campaign_lastUpdate_updatedBy}=    Get From Dictionary    ${db_campaign_data}    campaignHistoryUpdatedBy
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${lastUpdate_updatedBy}    ${db_campaign_lastUpdate_updatedBy}
            Log Many     Comparison result for 'lastUpdate_updatedBy' : Controller Results = ${lastUpdate_updatedBy}, Database Results = ${db_campaign_lastUpdate_updatedBy}

            ${db_campaign_lastUpdate_updatedDate}=    Get From Dictionary    ${db_campaign_data}    campaignHistoryUpdateDate
            #Remove the last character if it is '0'
            ${db_campaign_lastUpdate_updatedDate}=    Evaluate    '${db_campaign_lastUpdate_updatedDate}'[:-1] if '${db_campaign_lastUpdate_updatedDate}'[-1] == '0' else '${db_campaign_lastUpdate_updatedDate}'
            ${last_letter_is_zero}=     is last char zero       ${db_campaign_lastUpdate_updatedDate}
            WHILE  ${last_letter_is_zero}
                ${db_campaign_lastUpdate_updatedDate}=    Evaluate    '${db_campaign_lastUpdate_updatedDate}'[:-1] if '${db_campaign_lastUpdate_updatedDate}'[-1] == '0' else '${db_campaign_lastUpdate_updatedDate}'
                ${last_letter_is_zero}=     is last char zero       ${db_campaign_lastUpdate_updatedDate}
            END

            ${lastUpdate_updatedDate}=    Evaluate    '${lastUpdate_updatedDate}'[:-1] if '${lastUpdate_updatedDate}'[-1] == '0' else '${lastUpdate_updatedDate}'
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${lastUpdate_updatedDate}    ${db_campaign_lastUpdate_updatedDate}
            Log Many     Comparison result for 'lastUpdate_updatedDate' : Controller Results = ${lastUpdate_updatedDate}, Database Results = ${db_campaign_lastUpdate_updatedDate}


            ${db_campaign_lastUpdate_updateDescription}=    Get From Dictionary    ${db_campaign_data}    updateDescription
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${lastUpdate_updateDescription}    ${db_campaign_lastUpdate_updateDescription}
            Log Many     Comparison result for 'lastUpdate_updateDescription' : Controller Results = ${lastUpdate_updateDescription}, Database Results = ${db_campaign_lastUpdate_updateDescription}

            ${db_campaign_lastUpdate_approvalId}=    Get From Dictionary    ${db_campaign_data}    approvalId
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${lastUpdate_approvalId}    ${db_campaign_lastUpdate_approvalId}
            Log Many     Comparison result for 'lastUpdate_approvalId' : Controller Results = ${lastUpdate_approvalId}, Database Results = ${db_campaign_lastUpdate_approvalId}

            ${db_campaign_screen_id}=    Get From Dictionary    ${db_campaign_data}    marketingScreenID
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${screen_id}    ${db_campaign_screen_id}
            Log Many     Comparison result for 'screen_id' : Controller Results = ${screen_id}, Database Results = ${db_campaign_screen_id}

            ${db_campaign_screen_type}=    Get From Dictionary    ${db_campaign_data}    screenType
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${screen_type}    ${db_campaign_screen_type}
            Log Many     Comparison result for 'screen_type' : Controller Results = ${screen_type}, Database Results = ${db_campaign_screen_type}

            ${db_campaign_screen_number}=    Get From Dictionary    ${db_campaign_data}    screenNumber
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${screen_number}    ${db_campaign_screen_number}
            Log Many     Comparison result for 'screen_number' : Controller Results = ${screen_number}, Database Results = ${db_campaign_screen_number}

            ${db_campaign_screen_channelId}=    Get From Dictionary    ${db_campaign_data}    marketingScreenChannelID
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${screen_channelId}    ${db_campaign_screen_channelId}
            Log Many     Comparison result for 'screen_channelId' : Controller Results = ${screen_channelId}, Database Results = ${db_campaign_screen_channelId}

            ${db_campaign_marketing_channel_id}=    Get From Dictionary    ${db_campaign_data}    marketingChannelID
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_channel_id}    ${db_campaign_marketing_channel_id}
            Log Many     Comparison result for 'marketing_channel_id' : Controller Results = ${marketing_channel_id}, Database Results = ${db_campaign_marketing_channel_id}

            ${db_campaign_marketing_channel}=    Get From Dictionary    ${db_campaign_data}    marketingChannel
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_channel}    ${db_campaign_marketing_channel}
            Log Many     Comparison result for 'marketing_channel' : Controller Results = ${marketing_channel}, Database Results = ${db_campaign_marketing_channel}

            ${db_campaign_marketing_channel_image_resolution}=    Get From Dictionary    ${db_campaign_data}    marketingChannelImageResolution
            Run Keyword And Continue On Failure    Should Be Equal As Strings    ${marketing_channel_image_resolution}    ${db_campaign_marketing_channel_image_resolution}
            Log Many     Comparison result for 'marketing_channel_image_resolution' : Controller Results = ${marketing_channel_image_resolution}, Database Results = ${db_campaign_marketing_channel_image_resolution}

        END


    END






Get the campaign details from the database
    [Arguments]     ${CAMPAIGN_ID}      ${CAMPAIGN_HISTORY_UPDATE_DATE}      ${SCHEDULE_VERSION}

    #Verify that all parameters are supplied
    Run Keyword If    '${CAMPAIGN_ID}' == '${EMPTY}' or '${CAMPAIGN_HISTORY_UPDATE_DATE}' == '${EMPTY}' or '${CAMPAIGN_HISTORY_UPDATE_DATE}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${campaign_targted_query}   Set Variable     ${SQL_GET_CAMPAIGN_TARGET_DATA}
    ${campaign_targted_query}=  Replace String      ${campaign_targted_query}       campaign_ID     '${CAMPAIGN_ID}'

    ${data_base_campaign_target_count}=      Execute SQL Query  ${db_type}  ${campaign_targted_query}    True
    ${total_targets}=    Get From Dictionary    ${data_base_campaign_target_count}    total_targets

    IF    '${total_targets}' != '0'  #Run query for targeted campaign
        #Get  the campaign details from the database
        ${campaign_query}   Set Variable     ${SQL_GET_ATM_MARKETING_CAMPAIGN_TARGETED}
        ${campaign_query}=  Replace String      ${campaign_query}       campaign_ID     '${CAMPAIGN_ID}'
        ${campaign_query}=  Replace String      ${campaign_query}       campaign_update_date     ${CAMPAIGN_HISTORY_UPDATE_DATE}
        ${campaign_query}=  Catenate    ${campaign_query}       and C.version = '${SCHEDULE_VERSION}'
        Log Many    ${campaign_query}
        ${data_base_campaign}=      Execute SQL Query  ${db_type}  ${campaign_query}    True
        ${campaign_id}=    Get From Dictionary    ${data_base_campaign}    id
        #Log     DB Campaign ID:${campaign_id}
        RETURN   ${data_base_campaign}
    ELSE  #Run query for Untargeted campaign
        #Get  the campaign details from the database
        ${campaign_query}   Set Variable     ${SQL_GET_ATM_MARKETING_CAMPAIGN_UN_TARGETED}
        ${campaign_query}=  Replace String      ${campaign_query}       campaign_ID     '${CAMPAIGN_ID}'
        ${campaign_query}=  Replace String      ${campaign_query}       campaign_update_date     ${CAMPAIGN_HISTORY_UPDATE_DATE}
        ${campaign_query}=  Catenate    ${campaign_query}       and C.version = '${SCHEDULE_VERSION}'
        Log Many    ${campaign_query}

        ${data_base_campaign}=      Execute SQL Query  ${db_type}  ${campaign_query}    True
        ${campaign_id}=    Get From Dictionary    ${data_base_campaign}    id
        #Log     DB Campaign ID:${campaign_id}
        #Log Many    ${data_base_campaign}
        RETURN   ${data_base_campaign}
    END
    






