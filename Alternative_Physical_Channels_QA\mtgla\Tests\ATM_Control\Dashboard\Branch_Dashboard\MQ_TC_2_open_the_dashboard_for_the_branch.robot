*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK   
Documentation               ATM Control- Access Branch Dashboard
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../../keywords/Common/Login.robot
Resource                                            ../../../../keywords/Common/HomePage.robot
Resource                                            ../../../../keywords/Common/Navigation.robot
Resource                                            ../../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../../keywords/Common/SetEnvironmentVariales.robot



*** Variables ***


*** Keywords ***
Validates if user can access Branch Dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page

    And the user navigates to the ATM Control Dashboard  

    And the user selects a branch to access the Branch Dashboard 

    Then the user verifies they have accessed the Branch Dashboard


| *Test Cases*                                                                                           |      *DOCUMENTATION*                          | *TEST_ENVIRONMENT*   |
|  MQ-TC-2 open the dashboard for the branch	    | Validates if user can access Branch Dashboard     |    User can access dashboard for ATM Control  |    MTGLA_UAT         | 
