*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../keywords/controllers/GetUploadManagementInformation_Keywords.robot
Resource                            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
GetUploadManagementInformation
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${SERVER_VERSION}    ${BIN_STATUS_ACTION}  ${EXPECTED_STATUS_CODE}   
    Set Test Documentation  ${DOCUMENTATION}

    Given The User sends a Get Request for GetUploadManagementInformation using the server version & bin status action    ${BASE_URL}      ${SERVER_VERSION}    ${BIN_STATUS_ACTION}
    When The service returns an expected status code                            ${EXPECTED_STATUS_CODE}
    


| *** Test Cases ***                                                  |        *DOCUMENTATION*    		                   |         *BASE_URL*          |  *SERVER_VERSION*      | *BIN BIN_STATUS_ACTION* |  *EXPECTED_STATUS_CODE*   |
| GetUploadManagementInformation   | GetUploadManagementInformation   | Testing GetUploadManagementInformation Controller  |                             |        13                |        Added          |        200                |
