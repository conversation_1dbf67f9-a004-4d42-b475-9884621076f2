*** Settings ***
#Author Name               : Yaash
#Email Address             : ya<PERSON><PERSON><PERSON><EMAIL>

Documentation  Negative Test Scenarios & Keywords 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                            String
Library                                            ../api/Resources/CreateRestPayloads.py
Library                                             Process
Library                                            ../../utility/PostExecutionUpdate.py
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot

*** Variables ***                         
${INVALID_APPLICATION_USERNAME}                    AAABBBBAAA
${INVALID_APPLICATION_PASSWORD}                    INVALID
${BLANK_APPLICATION_USERNAME}                       ${EMPTY}
${BLANK_APPLICATION_PASSWORD}                       ${EMPTY}
# This is defined at runtime via command line argument


*** Keywords ***
The user opens the VMS URL  
    [Arguments]  ${URL}    ${BROWSER}  ${DRIVER_BROWSER}    
    
    Begin Web test

    Load    ${URL} 


The user passes an invalid username
    Log To Console    --------- Inputting an invalid username 

    Sleep    5s

    Input Text    ${VMS_USERNAME_INPUT}   ${INVALID_APPLICATION_USERNAME}  

    Sleep    5s

    Wait Until Element Is Visible    ${VMS_PASSWORD_INPUT}    

    Log To Console    --------- Inputting password

    Input Text     ${VMS_PASSWORD_INPUT}  ${APPLICATION_PASSWORD} 

    Sleep    2s

    Click Button    ${VMS_LOGIN_BTN}

    #Validation
    Wait Until Page Contains    Invalid username or bad password

    Page Should Contain    Invalid username or bad password

    Capture Page Screenshot



The user passes an invalid Password
    Log To Console    -------- Inputting a valid username
    Sleep    2s
    Input Text    ${USERNAME_INPUT}    ${APPLICATION_USERNAME}  

    Log To Console    -------- Inputting an Invalid password  

    Wait Until Element Is Visible    ${PASSWORD_INPUT}    

    Input Text     ${PASSWORD_INPUT}  ${INVALID_APPLICATION_PASSWORD} 

    Sleep    2s

    Click Button    ${VMS_LOGIN_BTN}

    #Validation
    Wait Until Page Contains    Invalid username or bad password

    Page Should Contain    Invalid username or bad password

    Capture Page Screenshot



The user allows blank username field
    Log To Console    ------- Allowing blank username field

    Sleep    2s

    Input Text    ${VMS_USERNAME_INPUT}    ${BLANK_APPLICATION_USERNAME}   

    Log To Console    ------ Inputting a valid application password 

    Wait Until Element Is Visible    ${VMS_PASSWORD_INPUT}    

    Input Text     ${VMS_PASSWORD_INPUT}  ${APPLICATION_PASSWORD}

    Sleep    2s

    Click Button    ${VMS_LOGIN_BTN}

    #Validation
    Wait Until Page Contains    Invalid username or bad password

    Page Should Contain    Invalid username or bad password

    Capture Page Screenshot


The user allows blank password field
    
    Log To Console    ------- Inputting a valid username 
    Sleep    2s
    Input Text    ${USERNAME_INPUT}    ${APPLICATION_USERNAME}   

    Log To Console    ------ Allowing blank password field 

    Wait Until Element Is Visible    ${PASSWORD_INPUT}    

    Input Text     ${PASSWORD_INPUT}  ${BLANK_APPLICATION_PASSWORD}

    Sleep    2s

    Click Button    ${VMS_LOGIN_BTN}

    #Validation
    Wait Until Page Contains    Invalid username or bad password

    Page Should Contain    Invalid username or bad password

    Capture Page Screenshot

The user Validates the Negative Login Tests  
    [Arguments]    ${TestCaseValidation}

    Run Keyword If    '${TestCaseValidation}' == "Invalid Username"    The user passes an invalid username 
    Run Keyword If    '${TestCaseValidation}' == "Invalid Password"        The user passes an invalid Password  
    Run Keyword If    '${TestCaseValidation}' == "Blank Username Field"    The user allows blank username field 
    Run Keyword If    '${TestCaseValidation}' == "Blank Password Field"    The user allows blank password field 

#SITE MAINTENANCE NEGATIVE TESTS
The user performs a search with invalid criteria
   #Search criteria not met 
    Wait Until Element Is Enabled    xpath=//*[@id="searchField"]
    Click Element    xpath=//*[@id="searchField"]
    Input Text    xpath=//*[@id="searchField"]    ATM99999999 
    Sleep    5s
The user should see no search results
    Page Should Contain    Showing 0 to 0 of 0 rows
    Log      0 results returned due to invalid search criteria