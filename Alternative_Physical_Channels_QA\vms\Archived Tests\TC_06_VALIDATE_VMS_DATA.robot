*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  CC Service
Default Tags                                        VMS HEALTHCHECK    VALIDATE DATA
Suite Setup                                         Set up environment variables
# Suite Teardown                                      Clear environment variables

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             RequestsLibrary
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             DateTime
Library                                             String
Library                                             SeleniumLibrary
Library                                             ../utility/SoapService.py

#***********************************PROJECT RESOURCES*************************************** 
Resource                                            ../keywords/api/RestCalls.robot
Resource                                            ../keywords/common/Login.robot 
Resource                                            ../keywords/common/Logout.robot 
Resource                                            ../keywords/qrcode_cc/UpdateATMComplaints.robot 
Resource                                            ../keywords/qrcode_cc/ValidateData.robot 
Resource                                            ../keywords/qrcode_cc/ViewATMComplaintsAndCompliments.robot
Resource                                            ../keywords/common/common_keywords.robot
Resource                                            ../keywords/common/Login.robot
Resource                                            ../keywords/common/SetEnvironmentVariales.robot
# Resource                                            ../keywords/api/SoapService.robot



*** Variables ***
${DATA_FILE}                                        data/service_data.csv

*** Keyword ***
Validate data on VBMS
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${Tag}  ${SERVICE_TYPE}  ${REQUEST_METHOD}    
    Set Test Documentation    ${DOCUMENTATION}

    Log to console  ${SUITE NAME}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    ${TEST_CASE_NAME}=          Set Variable        ${TEST NAME}

    ${reference}=    REST request  ${DATA_FILE}  ${TEST_CASE_NAME}  ${REQUEST_METHOD}
    
    Given The user logs into vms   https://vms.uat.absa.africa/Login  Chrome  drivers\chromedriver.exe
   
    And The user clicks navigate to QR Code Complaints and Compliments screen link
   
    And User searches for a compliant or task  ${reference}

    Sleep  2s

    And User clciks on details link
    
    And User validates compliant data on Details - ATM Complaints and Compliments screen  ${reference}

    And User logs out

| *Test Case*                                                           |               *DOCUMENTATION*                                  |  *TESTRAIL_TESTCASE_ID*    |         *Tag*        | *SERVICE_TYPE* | *REQUEST_METHOD*  |
| ATM CC - Validate data on VBMS                 | Validate data on VBMS         |     ATM CC - Validate data on VBMS                    |       101597697             |         C&C          |      REST      |     POST          | 
