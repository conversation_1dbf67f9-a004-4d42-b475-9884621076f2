# Future-Fit Architecture
The purpose of this project is to validate Future-Fit architecture portal

## Pick up result of each test run and send update testrail
robot -d reports --variable TESTRAIL_USERNAME:<EMAIL> --variable TESTRAIL_PASSWORD:password -reporttitle --variable BUSINESS_USER_USERNAME:BusinessUser-AB --variable BUSINESS_USER_PASSWORD:12345 --variable BUSINESS_APPROVER_USER_USERNAME:BusinessUser-AB --variable BUSINESS_APPROVER_USER_PASSWORD:password --variable ADMIN_USER_USERNAME:BusinessUser-AB --variable ADMIN_USER_PASSWORD:password --variable IS_HEADLESS_BROWSER:No -i "FFT HEALTHCHECK"  -N "Future-Fit Portal" --listener .\utility\PostExecutionUpdate.py '.\tests'

#Installing testrailintegration
- Download the module from Github
- Unzip the directory
- cd into it
- run " pip3 install ."
- in the code import the package 
   import 



Created mbalentle branch
Creating mlugisi branch
Creating Siviwe branch
