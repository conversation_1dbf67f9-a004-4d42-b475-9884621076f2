*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
#Test Teardown                                       User logs out

Documentation  Testing future fit Authentication & Authorisation Negative testcases 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/common/Navigation.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../Keywords/atm_marketing/CalendarView.robot
Resource                                            ../../keywords/atm_marketing/Authentication_Authorisation.robot
Resource                                            ../../keywords/atm_marketing/NegativeScenarios.robot

*** Variables ***

*** Keywords ***
Validates Authentication and Authorisation Negative Tests
 # Set Library Search Order    SeleniumLibrary    AppiumLibrary   
    [Arguments]  ${DOCUMENTATION}  ${TESTRAIL_TESTCASE_ID}  ${LOGON_USER}     ${TEST_ENVIRONMENT}   ${Test_cases}
    Set Test Documentation  ${DOCUMENTATION}

    #Set the test case id
    Set Environment Variable    TESTRAIL_TESTCASE_ID    ${TESTRAIL_TESTCASE_ID}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}  Chrome  drivers\chromedriver.exe  ${LOGON_USER}

    Sleep  5s

    And The user validates testcases    ${Test_cases} 

    And User logs out   


| *** Test Cases ***                                                                                                                                            |      *DOCUMENTATION*                                     |  *TESTRAIL_TESTCASE_ID* |      *LOGON_USER*         |    *TEST_ENVIRONMENT*    |  *Test_cases*                                        |
#| Login- BA- Validate that the BA does not have access to Capture campaigns                | Validates Authentication and Authorisation Negative Tests   | Validating BA user cannot capture a campaign             |     155057359			  | BUSINESS_APPROVER         |    APC_UAT               | Validating BA user cannot capture a campaign         |
| Login- BU- Validate that the BU does not have access to approve/authorize campaigns      | Validates Authentication and Authorisation Negative Tests   | Validating that a BU user cannot approve campaigns       |     155057383			  | BUSINESS_USER             |    APC_UAT               | Validating that a BU user cannot approve campaigns   |