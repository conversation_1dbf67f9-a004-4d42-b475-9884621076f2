server-api: 2025-06-19 08:50:35 UTC pid: 19040 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.2.others.api']

server-api: 2025-06-19 08:50:35 UTC pid: 19040 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-19 08:50:35 UTC pid: 19040 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-19 08:50:35 UTC pid: 19040 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 19040

server-api: 2025-06-19 08:50:43 UTC pid: 19040 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-19 08:50:43 UTC pid: 19040 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-19 08:50:43 UTC pid: 19040 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-19 08:54:24 UTC pid: 19040 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA

  c:\Alternative

  c:\Alternative\Alternative_Physical_Channels_QA\FIS_API\keywords

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages

  c:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords

  c:\Alternative\Alternative_Physical_Channels_QA\vms\keywords

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA', '-P', 'c:\\Alternative', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\FIS_API\\keywords', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', '-P', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
