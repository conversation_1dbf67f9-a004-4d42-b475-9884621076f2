*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON> Setuke
#Email Address             : <EMAIL>


Documentation  APC Bin Tables Portal - Landing Page

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DateTime
Library                                              ../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************
Resource                                          ../../keywords/common/DatabaseConnector.robot
Resource                                          ../../keywords/controllers/common/GenericMethods.robot
Resource                                          ../../keywords/front_end/View_Bins_Page.robot


*** Variables ***
${ADD_BIN_BTN}                        xpath=//span[contains(text(),'Add')]
${BIN_NAME_INPUT}                     xpath=//input[contains(@name, 'binNumber') and contains(@placeholder, 'Enter Number')]
${BIN_TYPE_LIST}                      xpath=//div[contains(@class, 'mat-select-arrow')]
${DUPLICATE_ERR_MSG}                  xpath=//p[contains(@class, 'duplicate') and contains(@class, 'ng-star-inserted')]
${SAVE_BIN_BTN}                       xpath=//button[contains(@class, 'mat-stroked-button') and contains(@class, 'mat-focus-indicator') and contains(@class, 'mat-button-base')]
${SAVE_BIN_DIALOG}                    xpath=//mat-dialog-content[contains(@class, 'mat-dialog-content') and contains(@class, 'mat-typography')]
${SAVE_BINS_AND_EXIT_BTN}             xpath=//button[contains(@class, 'btn-save') and contains(@class, 'mat-button-base')]
${CANCEL_SAVE_BIN}                    xpath=//button[contains(@class, 'btn-close') and contains(@class, 'mat-button-base')]
${VIEW_BIN_BTN}                       xpath=//span[contains(text(),'View')]
${ADD_BINS_BTN}                       xpath=//span[contains(text(),'Add')]
${OPEN_ACTION_DATE_CAL_BTN}           xpath=//mat-datepicker-toggle[contains(@class, 'mat-datepicker-toggle')]
${LOCAL_STORAGE_BINS_TABLE}           xpath=//table[contains(@class, 'mat-table') and contains(@class, 'cdk-table') and contains(@class, 'mat-sort')]
${SAVE_BIN_DIALOG}                    xpath=//mat-dialog-content[contains(@class, 'mat-dialog-content') and contains(@class, 'mat-typography')]
${SAVE_BIN_TYPE_TO_DB_BTN}            xpath=//mat-dialog-actions[contains(@class, 'mat-dialog-actions') and contains(@class, 'mat-dialog-actions-align-end')]/div/div[2]/button
${CANCEL_SAVE_BIN_TYPE_TO_DB_BTN}     xpath=//mat-dialog-actions[contains(@class, 'mat-dialog-actions') and contains(@class, 'mat-dialog-actions-align-end')]/div/div[1]/button
${80_PERCENT_ZOOM_LEVEL}              0.8  # Set zoom level (1.0 = 100%, 0.8 = 80%, 1.2 = 120%)
${100_PERCENT_ZOOM_LEVEL}             1.0  # Set zoom level (1.0 = 100%, 0.8 = 80%, 1.2 = 120%)
${GREYED_OUT_PAST_DATE}               xpath=//button[contains(@class, 'mat-calendar-body-disabled') and @aria-disabled='true']
${FULL_BIN_NUMBER_DISPLAYED}          xpath=//span[@class='mat-option-text']/div[@class='bin-number']





*** Keywords ***
The user navigates to 'Add' Bin tab

    ${add_bin_type_btn_displayed}=     Wait for Element to be enabled    ${ADD_BINS_BTN}

    Run Keyword If    not ${add_bin_type_btn_displayed}
    ...    Fail   The 'Add' button is not displayed on the Bins page.

     #Click on the bin type to Verify
    SeleniumLibrary.Click Element    ${ADD_BINS_BTN}
    Sleep    4s

    #Verify that the user is directed to the correct page
    ${correct_page_displayed}=      Correct Page is displayed    ADD NEW BIN


The User populates the Bin details and save the Bin to local storage
    [Arguments]     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be selected!


    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!

    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Add Bin' page.

    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Name
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    Press Key   ${BIN_NAME_INPUT}      \\9   #Press the 'TAB' button
    Sleep    3s
    #Click Bin Types List
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}
            Sleep    3s
            #SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Bin Type selection List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END
    Sleep   2s
    ${bin_types_selected}=      Run Keyword And Return Status    Select the Bin Type     ${BIN_TYPE_NAME}
    Log Many    bin_tyeps_selected: ${bin_types_selected}
    Capture Page Screenshot   ${BIN_NAME}_details_populated.png
    IF    ${bin_types_selected} == ${False}
         Fail   Not all Bin Types have been selected.
         RETURN
    END

     #Verify that the 'Add Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Action Date has not yet been selected.


    ${action_date_selected}=      Run Keyword And Return Status    Select Action Date     ${BIN_ACTION_DATE}
    Log Many    action_date_selected: ${action_date_selected}
    Capture Page Screenshot   ${BIN_NAME}actionDate_details_populated.png
    IF    ${action_date_selected} == ${False}
         Fail   Action Date was not selected.
         RETURN
    END



   #Verify that the 'Add Bin' button is now enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Add Bin Type' button is enabled becuase all the Bin details have been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is still disabled even though all the Bin details have been populated.

    SeleniumLibrary.Click Element    ${SAVE_BIN_BTN}
    Sleep    2s
    Capture Page Screenshot   ${BIN_NAME}_details_added_tolocal.png

    Set Global Variable    ${CREATED_BIN_NUMBER}     ${BIN_NAME}
    Set Global Variable    ${CREATED_BIN_TYPE_NAME}  ${BIN_TYPE_NAME}
    Set Global Variable    ${CREATED_BIN_ACTION_DATE}  ${BIN_ACTION_DATE}

Select the Bin Type
    [Arguments]    ${BIN_TYPE_NAME}

    #Verify that the 'Bin Type' names elements are displayed
    ${bin_type_web_elements}=         SeleniumLibrary.Get Webelements  xpath=//mat-option[@role='option']
    ${number_of_elements_displayed}=    Get Length      ${bin_type_web_elements}
    Run Keyword If    ${number_of_elements_displayed} == 0
    ...    Run Keyword And Continue On Failure    Fail  The Bin Types List is not displayed.
    #Select the user's Bin Type if it is available on the displayed List
    ${user_bin_type_array}=       Split String    ${BIN_TYPE_NAME}     separator=,
    ${all_user_bin_types_found}=    Set Variable    ${True}
    ${bin_types_not_found_list}=    Create List
    ${all_user_bin_types_found}=    Set Variable    ${True}
    #Loop through the user provided bin types and verify that they are all displayed
    FOR    ${user_bin_type_element}    IN    @{user_bin_type_array}
        ${bin_type_selected}=    Set Variable    ${False}
        ${user_bin_type_element}=    Set Variable   ${user_bin_type_element.strip()}
        Log    user_bin_type_element: '${user_bin_type_element}'
        #Loop through the list of displayed bin types and search for the current user's bin type
        FOR    ${displayed_bin_type_element}    IN    @{bin_type_web_elements}
            ${bin_type_element_text}=       SeleniumLibrary.Get Text  ${displayed_bin_type_element}
            Log   Element text: '${bin_type_element_text}'
            ${bin_type_element_text}=    Set Variable   ${bin_type_element_text.strip()}
            ${bin_type_element_text}=    Convert to upper case      ${bin_type_element_text.strip()}
            ${temp_user_bin_type_element}=    Convert to upper case      ${user_bin_type_element.strip()}

            IF    '${bin_type_element_text}' == '${temp_user_bin_type_element}'
                 SeleniumLibrary.Click Element    ${displayed_bin_type_element}
                 ${bin_type_selected}=    Set Variable    ${True}
            END

            IF    ${bin_type_selected}
                Exit for loop
            END
        END
        IF    not ${bin_type_selected}
            ${all_user_bin_types_found}=    Set Variable    ${False}
            Append To List      ${bin_types_not_found_list}     ${user_bin_type_element}
        END
    END
    
    Run Keyword If    ${all_user_bin_types_found}
    ...    Run Keyword And Continue On Failure    Log Many  All user provided Bin Types have been selected.
    ...  ELSE
    ...    Run Keyword And Continue On Failure    Fail  The following Bin Type(s) were not found on the Bin Types List, hence couid not be selected. The Bin Type(s) not found   is(are) '${bin_types_not_found_list}'

    ${overlay_web_element}=         SeleniumLibrary.Get Webelement  xpath=//div[@class='cdk-overlay-container']

    Press Keys   ${overlay_web_element}      \\27
    Sleep   4s

Select Action Date
    [Arguments]    ${ACTION_DATE_DATA}

    ${date_is_valid}=       Check if valid date     ${ACTION_DATE_DATA}

     Run Keyword If    not ${date_is_valid}
    ...    Fail  The provided data for Action Date is not valid. The data provided is '${ACTION_DATE_DATA}'.

    Log Many    '${ACTION_DATE_DATA}'
    #Extract date components
    ${action_day}=      Extract Date Component      ${ACTION_DATE_DATA}     Day
    ${action_month}=      Extract Date Component      ${ACTION_DATE_DATA}     Month
    ${action_year}=      Extract Date Component      ${ACTION_DATE_DATA}     Year

    #Click the button to Open Action Date calendar
     Sleep   3s
     Scroll Element Into View    ${OPEN_ACTION_DATE_CAL_BTN}

     SeleniumLibrary.Click Element    ${OPEN_ACTION_DATE_CAL_BTN}


     #Verify that the 'Calendar Icon' elements are displayed
    ${calendar_web_elements}=         SeleniumLibrary.Get Webelements  xpath=//mat-calendar[@id='mat-datepicker-0']
    ${number_of_elements_displayed}=    Get Length      ${calendar_web_elements}
    Run Keyword If    ${number_of_elements_displayed} == 0
    ...    Run Keyword And Continue On Failure    Fail  The calendar icon is not displayed.

    #Get the month-year text displayed on the calendar and verify that it is the required one
    ${expected_month_year_text}=        Catenate    ${action_month}${SPACE}${action_year}
    FOR    ${index}    IN RANGE    1    ${number_of_elements_displayed} + 1
        ${displayed_month_year_xpath}=           Catenate  //mat-calendar[@id='mat-datepicker-0'][${index}]/mat-calendar-header/div/div/button[1]
        ${displayed_month_year_text}=       SeleniumLibrary.Get Text    ${displayed_month_year_xpath}
        ${displayed_years_buttons_xpath}=              Catenate  //table[@class='mat-calendar-table']/tbody/tr/td/button

        #If the displayed month-year text is not the required one,
        #select the required month-year
        IF    '${expected_month_year_text}' != '${displayed_month_year_text}'
          SeleniumLibrary.Click Element   ${displayed_month_year_xpath}
          #Select the required year from the
          ${displayed_years_buttons_web_elements}=       SeleniumLibrary.Get Webelements    ${displayed_years_buttons_xpath}
          ${year_found}=    Set Variable    ${False}
          FOR    ${year_element}    IN    @{displayed_years_buttons_web_elements}
              ${displayed_year_text}=       SeleniumLibrary.Get Text    ${year_element}
              Log Many      displayed_year_text: ${displayed_year_text}
              IF    '${displayed_year_text.strip()}' == '${action_year}'
                    SeleniumLibrary.Click Element    ${year_element}
                    ${year_found}=    Set Variable    ${True}
                    Exit For Loop
              END
          END

          IF    ${year_found}
              Log Many  The required year, which is: '${action_year}', was selected on the calendar.
              Sleep     2s
              ${displayed_months_buttons_xpath}=              Catenate  //table[@class='mat-calendar-table']/tbody/tr/td/button
              ${displayed_months_buttons_web_elements}=       SeleniumLibrary.Get Webelements    ${displayed_years_buttons_xpath}
              ${month_found}=    Set Variable    ${False}
              FOR    ${month_element}    IN    @{displayed_months_buttons_web_elements}
                  ${displayed_month_text}=       SeleniumLibrary.Get Text    ${month_element}

                  Log Many      displayed_month_text: ${displayed_month_text}
                  IF    '${displayed_month_text.strip()}' == '${action_month}'
                        SeleniumLibrary.Click Element    ${month_element}
                        ${month_found}=    Set Variable    ${True}
                        Exit For Loop
                  END
              END

              IF    ${month_found}
                    Log Many  The required month, which is: '${month_element}', was selected on the calendar.
              ELSE
                   Run Keyword And Continue On Failure    Fail   The required month, which is: '${month_element}', was not selected on the calendar.
              END
          ELSE
              Run Keyword And Continue On Failure    Fail   The required year, which is: '${action_year}', was not selected on the calendar.
          END
        END

        #Select the day required
        ${backward_button_xpath}=           Catenate  //mat-calendar[@id='mat-datepicker-0'][${index}]/mat-calendar-header/div/div/button[2]
        ${forward_button_xpath}=           Catenate  //mat-calendar[@id='mat-datepicker-0'][${index}]/mat-calendar-header/div/div/button[3]

        ${displayed_days_buttons_xpath}=              Catenate  //table[@class='mat-calendar-table']/tbody/tr/td/button
        ${displayed_days_buttons_web_elements}=       SeleniumLibrary.Get Webelements    ${displayed_years_buttons_xpath}
        ${day_found}=    Set Variable    ${False}
        FOR    ${day_element}    IN    @{displayed_days_buttons_web_elements}
           ${displayed_day_text}=       SeleniumLibrary.Get Text    ${day_element}
           Log Many      displayed_day_text: ${displayed_day_text}
           IF    '${displayed_day_text.strip()}' == '${action_day}'
                SeleniumLibrary.Click Element    ${day_element}
                ${day_found}=    Set Variable    ${True}
                Exit For Loop
           END
        END
        IF    ${day_found}
                Log Many  The required day, which is: '${action_day}', was selected on the calendar.
        ELSE
               Run Keyword And Continue On Failure    Fail   The required day, which is: '${action_day}', was not selected on the calendar.
        END

    END




The created bins are added to the local storage
    [Arguments]    ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be verified!


    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be verified!


    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be verified!

    #mat-table cdk-table mat-sort
    ${local_bins_table_web_elements}=       SeleniumLibrary.Get Webelements    ${LOCAL_STORAGE_BINS_TABLE}
    ${local_bins_table_rows_xpath}=              Catenate  ${LOCAL_STORAGE_BINS_TABLE}/tbody/tr
    ${displayed_local_bins_table_rows_web_elements}=       SeleniumLibrary.Get Webelements    ${local_bins_table_rows_xpath}
    ${local_bins_table_rows_total}=  Get Length     ${displayed_local_bins_table_rows_web_elements}

    ${bin_found}=    Set Variable    ${False}
    FOR    ${index}    IN RANGE    1    ${local_bins_table_rows_total} + 1
           ${curr_row_bin_ele}=           Catenate  ${LOCAL_STORAGE_BINS_TABLE}/tbody/tr[${index}]/td[1]
           ${bin_number}=         SeleniumLibrary.Get Text    ${curr_row_bin_ele}
           ${curr_row_bin_type_ele}=           Catenate  ${LOCAL_STORAGE_BINS_TABLE}/tbody/tr[${index}]/td[2]
           ${bin_type}=         SeleniumLibrary.Get Text    ${curr_row_bin_type_ele}
           ${curr_row_bin_action_date_ele}=          Catenate  ${LOCAL_STORAGE_BINS_TABLE}/tbody/tr[${index}]/td[3]
           ${current_bin_action_date}=         SeleniumLibrary.Get Text    ${curr_row_bin_action_date_ele}
           Log Many    current_bin_action_date:${current_bin_action_date.strip()}, BIN_ACTION_DATE: ${BIN_ACTION_DATE.strip()}

           ${date_are_equal}=       Are Dates Equal    ${current_bin_action_date.strip()}    ${BIN_ACTION_DATE.strip()}      %d/%m/%Y
           IF    '${bin_number.strip()}' == '${BIN_NAME.strip()}' and ${date_are_equal}
                #Check how many Bin Types are linked to the current bin as per the front end display
                ${bin_type_array}=       Split String    ${bin_type}     separator=,
                ${user_bin_type_array}=       Split String    ${BIN_TYPE_NAME}     separator=,
                ${bin_found}=    Set Variable    ${True}

                ${bin_types_that_are_not_linked}=       Create List
                FOR    ${user_bin_type_element}    IN    @{user_bin_type_array}
                    Log    ${user_bin_type_element}
                    ${linked_bin_type_found}=    Set Variable    ${False}
                    FOR    ${bin_type_element}    IN    @{bin_type_array}
                        Log    ${bin_type_element}
                        IF    '${bin_type_element.strip()}' == '${user_bin_type_element.strip()}'
                            ${linked_bin_type_found}=    Set Variable    ${True}
                            Exit For Loop
                        END
                    END

                    IF    not ${linked_bin_type_found}
                        Append to List  ${bin_types_that_are_not_linked}    ${user_bin_type_element}
                    END

                END

                ${num_of_bin_types_not_found}=      Get Length      ${bin_types_that_are_not_linked}

                IF    ${num_of_bin_types_not_found} > 0
                    Run Keyword And Continue On Failure    Fail   The bin types: ${bin_types_that_are_not_linked}, that are supposed to be linked to Bin Number: '${BIN_NAME.strip()}', are not displaying on local storage.
                END

                Exit For Loop
          END

    END

    IF    ${bin_found}
            Log Many  The required Bin Number, which is: '${BIN_NAME}', was found on bins saved in local storage.
    ELSE
           Run Keyword And Continue On Failure    Fail   The required Bin Number, which is: '${BIN_NAME}', was not found on bins saved in local storage.
    END
The user saves the created bin(s) to the database

    Execute JavaScript    window.scrollTo(0, document.body.scrollHeight)    #scroll to the bottom of the page
    Execute JavaScript    document.body.style.zoom = ${80_PERCENT_ZOOM_LEVEL}

    Sleep  3s
    #Verify that the 'Save Bin' button is enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BINS_AND_EXIT_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Save & Exit' button is enabled becuase all the Bin details have been captured.
    ...  ELSE
    ...    Fail  The 'Save & Exit' button is still disabled even though all the Bin details have been captured and saved to local storage.

    #Click the 'Save and Exit' button
    Sleep  2s
    Scroll Element Into View    ${SAVE_BINS_AND_EXIT_BTN}
    SeleniumLibrary.Click Element  ${SAVE_BINS_AND_EXIT_BTN}

     #Verify that the save bin  dialog is displayed
    Capture Page Screenshot   ${CREATED_BIN_TYPE_NAME}_Confirmation_details_populated.png
    ${add_bin_type_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/p
    ${add_bin_type_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_confirmation_msg_xpath}

    Run Keyword If    '${add_bin_type_confirmation_msg}' == 'You are about to add the following BINs:'
    ...    Log Many  The 'Add Bin' confirmation message is displayed.
    ...  ELSE
    ...    Fail  The 'Add Bin' confirmation message is not displayed.


    #Save the Bin Type to Database

    SeleniumLibrary.Click Element    ${SAVE_BIN_TYPE_TO_DB_BTN}
    Sleep  3s
    Capture Page Screenshot   ${CREATED_BIN_TYPE_NAME}_saved_to_db.png



The created Bin must exist in the database

    # Ensure the results are not empty
    ${bins_details_for_bin_type_db_results}=        Get the Bin details including the linked Bin Types from the Database        ${CREATED_BIN_NUMBER}
    ${db_results_contain_data}=     Run Keyword And Return Status       Should Not Be Empty    ${bins_details_for_bin_type_db_results}
    Run Keyword If    not ${db_results_contain_data}
    ...    Fail   The Bin Number: '${CREATED_BIN_NUMBER}' that has the outcome of 'Added' was not found in the database.


   ${bins_details_for_bin_type_db_results_rows}=        Get Length      ${bins_details_for_bin_type_db_results}

   #Verify that the Bin's DB details matches the number of associated Bin Types captured
   ${user_bin_type_array}=                  Split String    ${CREATED_BIN_TYPE_NAME}        separator=,
   ${user_bin_type_array_length}=           Get Length      ${user_bin_type_array}

   ${db_rows_equal_number_of_associated_bin_types}=     Run Keyword And Return Status       Should Be Equal    ${user_bin_type_array_length}       ${bins_details_for_bin_type_db_results_rows}

    Run Keyword If    not ${db_rows_equal_number_of_associated_bin_types}
    ...    Fail   '${user_bin_type_array_length}' bin types where added to the Bin Number: '${CREATED_BIN_NUMBER}' during the capturing process. However the database returned '${bins_details_for_bin_type_db_results_rows}' bin types.

    #Verify that the database details matches the added details for the Bin
    FOR    ${db_row}    IN    @{bins_details_for_bin_type_db_results}
        Log    ${db_row}
        ${db_bin_isDeleted_status}=     Get Column Data By Name       ${db_row}       binNumberIsDeleted
        ${db_bin_review_status}=        Get Column Data By Name       ${db_row}       reviewStatus
        ${db_bin_review_status_text}=   Get Bin Status Text       ${db_bin_review_status}
        ${db_bin_outcome}=              Get Column Data By Name       ${db_row}       outcome
        ${db_bin_outcome_text}=         Get Bin Outcome Text       ${db_bin_outcome}
        ${db_bin_type_name}=            Get Column Data By Name       ${db_row}       binType
        ${db_bin_type_name}=        Set Variable        ${db_bin_type_name.strip()}
        ${db_bin_type_action_date}=            Get Column Data By Name       ${db_row}       actionDate
        ${db_bin_type_action_date}=     Convert To String       ${db_bin_type_action_date}
        ${db_bin_type_action_date}=        Set Variable        ${db_bin_type_action_date.strip()}
        ${date_are_equal}=       Are Dates Equal    ${db_bin_type_action_date}    ${CREATED_BIN_ACTION_DATE.strip()}      %d/%m/%Y

        #Verify that the column details are as expected

        Run Keyword If    ${date_are_equal}
        ...    Log Many     The DB action date: '${db_bin_type_action_date}' for the Bin Number: '${CREATED_BIN_NUMBER}' matches the action date that was selected when creating the Bin.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail    The DB action date: '${db_bin_type_action_date}' for the Bin Number: '${CREATED_BIN_NUMBER}' does not match the action date that was selected when creating the Bin. The action date selected when capturing the bin was: '${CREATED_BIN_ACTION_DATE}'.


        Run Keyword If    '${db_bin_isDeleted_status}' == '0'
        ...    Log Many     The Bin Number: '${CREATED_BIN_NUMBER}' is active in the database.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail    The Bin Number: '${CREATED_BIN_NUMBER}' is not active in the database.

        Run Keyword If    '${db_bin_review_status_text}' == 'Pending'
        ...    Log Many     The Bin Number: '${CREATED_BIN_NUMBER}' has the correct status of Pending in the database.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail    The Bin Number: '${CREATED_BIN_NUMBER}' has the incorrect review status of '${db_bin_review_status_text}' in the database.

        Run Keyword If    '${db_bin_outcome_text}' == 'Added'
        ...    Log Many     The Bin Number: '${CREATED_BIN_NUMBER}' has the correct outcome of 'Added' in the database.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail    The Bin Number: '${CREATED_BIN_NUMBER}' has the incorrect outcome of '${db_bin_outcome_text}' in the database.

        ${user_bin_type_name_matches}=        Set Variable        ${False}
        FOR    ${user_bin_type}    IN    @{user_bin_type_array}
            Log    ${user_bin_type}
            IF    '${user_bin_type.strip()}' == '${db_bin_type_name}'
                ${user_bin_type_name_matches}=        Set Variable        ${True}
                Exit For Loop
            END
        END

        Run Keyword If    ${user_bin_type_name_matches}
        ...    Log Many     The DB bin type: '${db_bin_type_name}' for the Bin Number: '${CREATED_BIN_NUMBER}' matches the bin type that was selected when creating the Bin.
        ...  ELSE
        ...    Run Keyword And Continue On Failure    Fail    The DB bin type: '${db_bin_type_name}' for the Bin Number: '${CREATED_BIN_NUMBER}' does not match the bin type that was selected when creating the Bin. The bin type(s) selected when capturing the bin was (were): '${user_bin_type_array}'.

    END

The User populates the multiple Bins details and save the Bins to local storage
    [Arguments]    ${bin_dict}

    Verify that the dictionary contains sets of 3 items     ${bin_dict}

    ${bin_keys}=    Get Dictionary Keys   ${bin_dict}
    ${bin_keys_len}=    Get Length    ${bin_keys}

    ${bin_loop_len}=    Evaluate    int(${bin_keys_len} / 3)

    FOR    ${index}    IN RANGE    1    ${bin_loop_len+1}
        ${bin_number}    Get From Dictionary    ${bin_dict}     bin_${index}
        ${bin_type_name}    Get From Dictionary    ${bin_dict}    bin_type_name_${index}
        ${bin_action_date}    Get From Dictionary    ${bin_dict}    bin_action_date_${index}
        Log    Bin Name: ${bin_number}, Bin Type Name: ${bin_type_name}, Bin Action Date: ${bin_action_date}

        The User populates the Bin details and save the Bin to local storage    ${bin_number}    ${bin_type_name}     ${bin_action_date}
    END


The created multiple bins are added to the local storage
    [Arguments]    ${bin_dict}

    ${bin_keys}=    Get Dictionary Keys   ${bin_dict}
    ${bin_keys_len}=    Get Length    ${bin_keys}

    ${bin_loop_len}=    Evaluate    int(${bin_keys_len} / 3)

    FOR    ${index}    IN RANGE    1    ${bin_loop_len+1}
        ${bin_number}    Get From Dictionary    ${bin_dict}     bin_${index}
        ${bin_type_name}    Get From Dictionary    ${bin_dict}    bin_type_name_${index}
        ${bin_action_date}    Get From Dictionary    ${bin_dict}    bin_action_date_${index}
        Log    Bin Name: ${bin_number}, Bin Type Name: ${bin_type_name}, Bin Action Date: ${bin_action_date}

        The created bins are added to the local storage    ${bin_number}    ${bin_type_name}     ${bin_action_date}
    END

The created multiple bins must exist in the database
    [Arguments]    ${bin_dict}

    ${bin_keys}=    Get Dictionary Keys   ${bin_dict}
    ${bin_keys_len}=    Get Length    ${bin_keys}

    ${bin_loop_len}=    Evaluate    int(${bin_keys_len} / 3)

    FOR    ${index}    IN RANGE    1    ${bin_loop_len+1}
        ${bin_number}    Get From Dictionary    ${bin_dict}     bin_${index}
        ${bin_type_name}    Get From Dictionary    ${bin_dict}    bin_type_name_${index}
        ${bin_action_date}    Get From Dictionary    ${bin_dict}    bin_action_date_${index}
        Log    Bin Name: ${bin_number}, Bin Type Name: ${bin_type_name}, Bin Action Date: ${bin_action_date}

        Set Global Variable    ${CREATED_BIN_NUMBER}     ${bin_number}
        Set Global Variable    ${CREATED_BIN_TYPE_NAME}  ${bin_type_name}
        Set Global Variable    ${CREATED_BIN_ACTION_DATE}  ${bin_action_date}

        The created Bin must exist in the database
    END

Verify that the dictionary contains sets of 3 items
    [Arguments]     ${total}
    ${bin_keys}=    Get Dictionary Keys   ${total}
    ${bin_keys_len}=    Get Length    ${bin_keys}
    ${is_not_even}    Evaluate    ${bin_keys_len} % 3 == 0
    ${is_at_least_3}    Evaluate    ${bin_keys_len} >= 3

    Should Be True    ${is_not_even}    msg=The bins dictionary must contain bin sets of 3 items each.
    Should Be True    ${is_at_least_3}    msg=The bins dictionary must at least 3 items.

The user clicks the Save & Exit to save the created bin(s) to the database

    Execute JavaScript    window.scrollTo(0, document.body.scrollHeight)    #scroll to the bottom of the page
    Execute JavaScript    document.body.style.zoom = ${80_PERCENT_ZOOM_LEVEL}

    Sleep  3s
    #Verify that the 'Save Bin' button is enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BINS_AND_EXIT_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Save & Exit' button is enabled becuase all the Bin details have been captured.
    ...  ELSE
    ...    Fail  The 'Save & Exit' button is still disabled even though all the Bin details have been captured and saved to local storage.

    #Click the 'Save and Exit' button
    Sleep  2s
    Scroll Element Into View    ${SAVE_BINS_AND_EXIT_BTN}
    SeleniumLibrary.Click Element  ${SAVE_BINS_AND_EXIT_BTN}

    Log    The Save & Exit action is functioning as expected.

The User verifies that the 'Add Bin' confirmation message is displayed
    #Verify that the save bin  dialog is displayed
    Capture Page Screenshot   ${CREATED_BIN_TYPE_NAME}_Confirmation_details_populated.png
    ${add_bin_type_confirmation_msg_xpath}=         Catenate    ${SAVE_BIN_DIALOG}/p
    ${add_bin_type_confirmation_msg}=       SeleniumLibrary.Get Text    ${add_bin_type_confirmation_msg_xpath}

    Run Keyword If    '${add_bin_type_confirmation_msg}' == 'You are about to add the following BINs:'
    ...    Log Many  The 'Add Bin' confirmation message is displayed.
    ...  ELSE
    ...    Fail  The 'Add Bin' confirmation message is not displayed.




The User clicks Save to save the BIN to the database

    SeleniumLibrary.Click Element    ${SAVE_BIN_TYPE_TO_DB_BTN}
    Sleep  3s
    Capture Page Screenshot   ${CREATED_BIN_TYPE_NAME}_saved_to_db.png

The user verifies that the 'Entry Added' confirmation is displayed when a Bin is saved to the database
    Element Should Be Visible    xpath=//*[text()[normalize-space(.)=': 1 BIN entries added']]
    Sleep    2s 

    Log    After saving a bin to the database, the Entries Added Message is populated

The user clicks the 'Cancel' button to cancel the Add flow
    ${CANCEL_BTN}=    Get WebElement    xpath=//*[text()[normalize-space(.)='Cancel']]
    Click Element    ${CANCEL_BTN}
    Sleep    2s

The user confirms that the Bin was not added 
    Element Should Be Visible    xpath=//*[text()[normalize-space(.)='Add']]
    Log    User remains on the Add Menu.

The User populates the Bin details
    [Arguments]     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be selected!


    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!

    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Add Bin' page.

    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Name
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}

    #Click Bin Types List
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}
            Sleep    3s
            #SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Bin Type selection List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END
    Sleep   2s
    ${bin_types_selected}=      Run Keyword And Return Status    Select the Bin Type     ${BIN_TYPE_NAME}
    Log Many    bin_tyeps_selected: ${bin_types_selected}
    Capture Page Screenshot   ${BIN_NAME}_details_populated.png
    IF    ${bin_types_selected} == ${False}
         Fail   Not all Bin Types have been selected.
         RETURN
    END

     #Verify that the 'Add Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Action Date has not yet been selected.


    ${action_date_selected}=      Run Keyword And Return Status    Select Action Date     ${BIN_ACTION_DATE}
    Log Many    action_date_selected: ${action_date_selected}
    Capture Page Screenshot   ${BIN_NAME}actionDate_details_populated.png
    IF    ${action_date_selected} == ${False}
         Fail   Action Date was not selected.
         RETURN
    END

The User Verifies that the 'Add Bin' Button is Enabled once all fields are populated
   #Verify that the 'Add Bin' button is now enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Add Bin Type' button is enabled becuase all the Bin details have been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is still disabled even though all the Bin details have been populated.

The user allows the Bin Details to be empty 
    [Arguments]     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be selected!


    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!

    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Add Bin' page.

The user verifies that the 'Add Bin' Button is disabled 
    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

The user inputs Bin Name, Bin Type, and a date in the past
    [Arguments]     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be selected!


    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!

    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Add Bin' page.

    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Name
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}

    #Click Bin Types List
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}
            Sleep    3s
            #SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Bin Type selection List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END
    Sleep   2s
    ${bin_types_selected}=      Run Keyword And Return Status    Select the Bin Type     ${BIN_TYPE_NAME}
    Log Many    bin_tyeps_selected: ${bin_types_selected}
    Capture Page Screenshot   ${BIN_NAME}_details_populated.png
    IF    ${bin_types_selected} == ${False}
         Fail   Not all Bin Types have been selected.
         RETURN
    END

     #Verify that the 'Add Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Action Date has not yet been selected.


    ${action_date_selected}=      Run Keyword And Return Status    Select Action Date     ${BIN_ACTION_DATE}
    Log Many    action_date_selected: ${action_date_selected}
    Capture Page Screenshot   ${BIN_NAME}actionDate_details_populated.png
    IF    ${action_date_selected} == ${False}
         Pass Execution   Action Date was in the past. Past Dates not allowed!
         RETURN
    END

The User populates the Bin name 
    [Arguments]     ${BIN_NAME}    

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Add Bin' page.

    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Name
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    Press Key   ${BIN_NAME_INPUT}      \\9   #Press the 'TAB' button
    Sleep    3s

    Set Global Variable    ${CREATED_BIN_NUMBER}     ${BIN_NAME}


The User populates the Bin Type 
    [Arguments]         ${BIN_TYPE_NAME}    

    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be selected!

    #Click Bin Types List
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}
            Sleep    3s
            #SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Bin Type selection List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END
    Sleep   2s
    ${bin_types_selected}=      Run Keyword And Return Status    Select the Bin Type     ${BIN_TYPE_NAME}
    Log Many    bin_tyeps_selected: ${bin_types_selected}

    IF    ${bin_types_selected} == ${False}
         Fail   Not all Bin Types have been selected.
         RETURN
    END

    #Verify that the 'Add Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Action Date has not yet been selected.

    Set Global Variable    ${CREATED_BIN_TYPE_NAME}  ${BIN_TYPE_NAME}

The User populates the Action Date 
    [Arguments]         ${BIN_ACTION_DATE}

    ${bin_action_date_provided}=        Set Variable If
         ...       '${BIN_ACTION_DATE}' == '${EMPTY}'     ${False}
         ...       '${BIN_ACTION_DATE}' == ''             ${False}
         ...       '${BIN_ACTION_DATE}' != '${EMPTY}'     ${True}
         ...       '${BIN_ACTION_DATE}' != ''             ${True}

    Run Keyword If    not ${bin_action_date_provided}
    ...    Fail     Please provide the Bin Action Date that must be selected!


    ${action_date_selected}=      Run Keyword And Return Status    Select Action Date     ${BIN_ACTION_DATE}
    Log Many    action_date_selected: ${action_date_selected}
    IF    ${action_date_selected} == ${False}
         Fail   Action Date was not selected.
         RETURN
    END

   #Verify that the 'Add Bin' button is now enabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Run Keyword If    '${disabled_attribute}' == 'False'
    ...    Log Many  The 'Add Bin Type' button is enabled becuase all the Bin details have been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is still disabled even though all the Bin details have been populated.

    SeleniumLibrary.Click Element    ${SAVE_BIN_BTN}
    Sleep    2s

    Set Global Variable    ${CREATED_BIN_ACTION_DATE}  ${BIN_ACTION_DATE}

The user clicks the 'Add Bin' Button to save the Bin to local storage
    SeleniumLibrary.Click Element    ${SAVE_BIN_BTN}
    Sleep    2s

The User populates multiple Bin Types
    [Arguments]         ${BIN_TYPE_NAME}    

    ${bin_type_name_provided}=        Set Variable If
         ...       '${BIN_TYPE_NAME}' == '${EMPTY}'     ${False}
         ...       '${BIN_TYPE_NAME}' == ''             ${False}
         ...       '${BIN_TYPE_NAME}' != '${EMPTY}'     ${True}
         ...       '${BIN_TYPE_NAME}' != ''             ${True}

    Run Keyword If    not ${bin_type_name_provided}
    ...    Fail     Please provide the Bin Type name that must be selected!

    #Click Bin Types List
    ${filter_list_is_displayed}=     Wait for Element to be enabled    ${BIN_TYPE_LIST}

    IF    ${filter_list_is_displayed}
            Sleep    3s
            SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}
            Sleep    3s
            #SeleniumLibrary.Click Element    ${BIN_TYPE_LIST}

        ELSE
            Run Keyword And Continue On Failure      Fail    The Bin Type selection List is not displayed on the page.
            Capture Page Screenshot   Filter_List_Not_Displayed.png
            Sleep    4s
            RETURN
    END
    Sleep   2s
    ${bin_types_selected}=      Run Keyword And Return Status    Select the Bin Type     ${BIN_TYPE_NAME}
    Log Many    bin_tyeps_selected: ${bin_types_selected}

    IF    ${bin_types_selected} == ${False}
         Fail   Not all Bin Types have been selected.
         RETURN
    END

    #Verify that the 'Add Bin' button is still disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Action Date has not yet been selected.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Action Date has not yet been selected.

    Set Global Variable    ${CREATED_BIN_TYPE_NAME}  ${BIN_TYPE_NAME}

The User populates a 14 character Bin name 
    [Arguments]     ${BIN_NAME}    

    ${BIN_NAME_TO_ADD}=      Remove Quotes       ${BIN_NAME}

    #This is the boolean value that checks if the bin type name and description are provided
    ${bin_name_provided}=        Set Variable If
         ...       '${BIN_NAME_TO_ADD}' == '${EMPTY}'     ${False}
         ...       '${BIN_NAME_TO_ADD}' == ''             ${False}
         ...       '${BIN_NAME_TO_ADD}' != '${EMPTY}'     ${True}
         ...       '${BIN_NAME_TO_ADD}' != ''             ${True}

    Run Keyword If    not ${bin_name_provided}
    ...    Fail     Please provide the name of the Bin Type that must be created!


    Capture Page Screenshot   ${BIN_NAME}_details_not_populated.png


    ${bin_name_input_is_displayed}=     Wait for Element to be enabled    ${BIN_NAME_INPUT}

    Run Keyword If    not ${bin_name_input_is_displayed}
    ...    Fail   The   BIN_NAME_INPUT is not displayed on the 'Add Bin' page.

    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin Type' button is disabled becuase the Bin Type details have not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin Type' button is enabled even though the Bin Type details have not yet been populated.

    #Populate the Bin Name
    SeleniumLibrary.Input Text    ${BIN_NAME_INPUT}    ${BIN_NAME}
    Press Key   ${BIN_NAME_INPUT}      \\9   #Press the 'TAB' button
    Sleep    3s

    Set Global Variable    ${CREATED_BIN_NUMBER}     ${BIN_NAME}

The user verfies that the first 12 characters of the 14 character Bin name was accepted     
    ${BIN_NAME}=    Get Text    ${BIN_NAME_INPUT}
    Log    The Bin Name accepted is:${BIN_NAME}

    ${BIN_NAME_CHARACTER_COUNT}=    Get Length    ${BIN_NAME}
    Log    The character count for the Bin Name accepted is:${BIN_NAME_CHARACTER_COUNT}
    
The user clicks on the Action Date field
    Sleep    2s
    Click Element    ${OPEN_ACTION_DATE_CAL_BTN}

The user verfies that no past dates are allowed for Action Date 
    ${DISABLED_PAST_DATE}=    Get web element    ${GREYED_OUT_PAST_DATE}
    Element Should Be Disabled    ${DISABLED_PAST_DATE}
    Log    User is not able to select an Action Date in the past!

The user searches with a partial Bin Number
    [Arguments]    ${PARTIAL_BIN_NUMBER}
    Click Element    ${BIN_NAME_INPUT}
    Input Text    ${BIN_NAME_INPUT}    ${PARTIAL_BIN_NUMBER}
    Sleep    2s
    Log    The User inputted the partial bin number:${PARTIAL_BIN_NUMBER}

The user verifies that the search results display the complete Bin Number that matches the partial input
    [Arguments]    ${EXPECTED_FULL_BIN_NUMBER}
    ${FULL_BIN_NUMBER}=    Get WebElement    ${FULL_BIN_NUMBER_DISPLAYED}
    ${FULL_BIN_NUMBER_EXTRACT}=    Get Text    ${FULL_BIN_NUMBER}
    Set Suite Variable    ${FULL_BIN_NUMBER}
    Log    The Bin Full Bin Number Displayed is:${FULL_BIN_NUMBER_EXTRACT}
    Should Be Equal As Strings   ${FULL_BIN_NUMBER_EXTRACT}    ${EXPECTED_FULL_BIN_NUMBER}

The user searches with a full Bin Number
    [Arguments]    ${FULL_BIN_NUMBER}
    Click Element    ${BIN_NAME_INPUT}
    Input Text    ${BIN_NAME_INPUT}    ${FULL_BIN_NUMBER}
    Sleep    2s
    Log    The User inputted the full bin number into the Bin Name field:${FULL_BIN_NUMBER}

The user verifies that the search result displays the correct Bin Number
    [Arguments]    ${EXPECTED_FULL_BIN_NUMBER}
    #Find all elements that match the XPath
    ${FULL_BIN_NUMBER_LIST}=    Get WebElements    ${FULL_BIN_NUMBER_DISPLAYED}
    ${FULL_BIN_NUMBER_COUNT}=    Get Length    ${FULL_BIN_NUMBER_LIST}

    #Check if more than one result is displayed
    Run Keyword If    ${FULL_BIN_NUMBER_COUNT} > 1
    ...    Fail    More than one result displayed: ${FULL_BIN_NUMBER_COUNT} results found.

    #If exactly one result is displayed
    ${FULL_BIN_NUMBER_EXTRACT}=    Get Text    ${FULL_BIN_NUMBER_LIST}[0]  
    Log    The Bin Full Bin Number Displayed is: ${FULL_BIN_NUMBER_EXTRACT}
    Should Be Equal As Strings    ${FULL_BIN_NUMBER_EXTRACT}    ${EXPECTED_FULL_BIN_NUMBER}

The user searches with an invalid Bin Number
    [Arguments]    ${INVALID_BIN_NUMBER}
    Click Element    ${BIN_NAME_INPUT}
    Input Text    ${BIN_NAME_INPUT}    ${INVALID_BIN_NUMBER}
    Sleep    2s
    Log    The User inputted invalid bin number:${INVALID_BIN_NUMBER}

The user verifies that the expected error message is displayed for an invalid Bin Number input
    [Arguments]    ${EXPECTED_ERROR_MESSAGE_INVALID_BIN_NUMBER}
    Sleep    2s
    ${ACTUAL_ERROR_DISPLAYED_INAVLID_BIN}=    Get Text    xpath=//*[text()[normalize-space(.)='Bin Number Invalid!']]
    Log    Actual error message displayed for Invalid bin:${ACTUAL_ERROR_DISPLAYED_INAVLID_BIN}
        
    Page Should Contain    ${ACTUAL_ERROR_DISPLAYED_INAVLID_BIN}
    Should Be Equal    ${EXPECTED_ERROR_MESSAGE_INVALID_BIN_NUMBER}   ${ACTUAL_ERROR_DISPLAYED_INAVLID_BIN}
    
The user inputs the existing Bin Number into the Bin Number field
    Click Element    ${BIN_NAME_INPUT}
    Input Text    ${BIN_NAME_INPUT}    ${GLOBAL_EXISTING_BIN_NUMBER}
    Sleep    5s
    Click Element    xpath=//*[contains(@class,'bin-number')]
    Sleep    2s
    Log    The User inputted the full bin number into the Bin Name field:${GLOBAL_EXISTING_BIN_NUMBER}

The user verifies that the expected error message is displayed 
    [Arguments]    ${EXPECTED_ERROR_MESSAGE_DUPLICATE_BIN}
    Wait Until Element Is Enabled    xpath=//*[text()[normalize-space(.)='Duplicate Found!']]
    Sleep    2s
    ${ACTUAL_DUPLICATE_ERROR_DISPLAYED}=    Get Text    xpath=//*[text()[normalize-space(.)='Duplicate Found!']]
    Log    Actual error message displayed for duplicate bin:${ACTUAL_DUPLICATE_ERROR_DISPLAYED}
    
    Page Should Contain    ${ACTUAL_DUPLICATE_ERROR_DISPLAYED}
    Should Be Equal    ${EXPECTED_ERROR_MESSAGE_DUPLICATE_BIN}   ${ACTUAL_DUPLICATE_ERROR_DISPLAYED}

The User allows the Action Date to be empty and verfies that the 'Add' button is disabled
    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin' button is disabled becuase the Bin Action Date has not yet been populated.
    ...  ELSE
    ...    Fail  The 'Add Bin' button is enabled even though the Bin Action Date has not yet been populated.

The User gets the bins returned on the list
   #Get all bin number elements
    @{bin_elements}=    Get WebElements    //span[@class='mat-option-text']/div[@class='bin-number']

    #Extract text from each bin on result and store in a list
    @{bin_list}=    Create List
    FOR    ${element}    IN    @{bin_elements}
        ${bin_text}=    Get Text    ${element}
        Append To List    ${bin_list}    ${bin_text}
    END

    #Log all bins to verify
    Log Many    ${bin_list}
    Set Global Variable    ${GLOBAL_BIN_LIST}    ${bin_list}

The user verifies that the Bin Number list is sorted by descending order
   #Initialize previous_date as None
    ${previous_date}=    Set Variable    None

    #Get the length of the list
    ${list_length}=    Get Length    ${GLOBAL_MATCHED_DATES}

    ${first_date}=    Get From List    ${GLOBAL_MATCHED_DATES}    0
    ${all_dates_same}=    Set Variable    True  

    
    FOR    ${index}    IN RANGE    0    ${list_length}
        ${current_date}=    Get From List    ${GLOBAL_MATCHED_DATES}    ${index}
        ${current_bin}=    Get From List    ${GLOBAL_BIN_LIST}    ${index}

        Log    Bin ${current_bin} captured on ${current_date}

        #Check if previous_date is None (first iteration)
        Run Keyword If    '${previous_date}' == 'None'    Log    First Date: ${current_date}
        
        #If previous_date is not None, check the order
        Run Keyword If    '${previous_date}' != 'None'    Run Keyword    Evaluate Date Comparison    ${current_date}    ${previous_date}    ${current_bin}

        #Set the current date as the previous date for the next comparison
        ${previous_date}=    Set Variable    ${current_date}

    END

    #Log if all dates are the same or not
    Run Keyword If    '${all_dates_same}' == 'True'    Log    All dates are the same.
    Run Keyword If    '${all_dates_same}' == 'False'    Log    Not all dates are the same.

    #Check if the list is sorted correctly (descending order)
    Run Keyword If    '${previous_date}' == '${first_date}'    Log    The dates on the Bin Number result list are in descending order.
    Run Keyword If    '${previous_date}' != '${first_date}'    Fail    The dates on the Bin Number result list are NOT in descending order!

Evaluate Date Comparison
    [Arguments]    ${current_date}    ${previous_date}    ${current_bin}
    #Check if dates are the same
    IF    '${current_date}' == '${previous_date}'
        Log    Captured Dates are the same for Bin ${current_bin} - ${current_date}
    ELSE
        #If dates are different, validate if in descending order
        IF    '${current_date}' > '${previous_date}'
            Log    Dates are NOT in descending order! ${previous_date} > ${current_date}
            Fail    Bin list is NOT in descending order!
        ELSE
            Log    Dates are in descending order.
        END
    END

The user verifies that the Search Bin By Name API Returned a Result for Partial Search on the Front-End
    [Arguments]    ${EXPECTED_FULL_BIN_NUMBER}  
    ${FULL_BIN_NUMBER}=    Get WebElement    ${FULL_BIN_NUMBER_DISPLAYED}  
    ${FULL_BIN_NUMBER_EXTRACT}=    Get Text    ${FULL_BIN_NUMBER}  

    IF    '${FULL_BIN_NUMBER_EXTRACT}' != ''
        Log    Search Bin By Name API call was successful. Displayed Bin: ${FULL_BIN_NUMBER_EXTRACT}
    ELSE
        Fail    Search Bin By Name API call failed on the front end. No bin number displayed.
    END

The user verifies that the Search Bin By Name API Returned a Result for Full Search on the Front-End
    [Arguments]    ${EXPECTED_FULL_BIN_NUMBER}  
    ${FULL_BIN_NUMBER}=    Get WebElement    ${FULL_BIN_NUMBER_DISPLAYED}  
    ${FULL_BIN_NUMBER_EXTRACT}=    Get Text    ${FULL_BIN_NUMBER}  

    IF    '${FULL_BIN_NUMBER_EXTRACT}' != ''
        Log    Search Bin By Name API call was successful. Displayed Bin: ${FULL_BIN_NUMBER_EXTRACT}
    ELSE
        Fail    Search Bin By Name API call failed on the front end. No bin number displayed.
    END

The user verifies that the 'Upload' API was triggered successfully
    Sleep    3s

    ${button_displayed}=    Run Keyword And Return Status    SeleniumLibrary.Element Should Be Visible    ${SAVE_BIN_TYPE_TO_DB_BTN}

    Run Keyword If    '${button_displayed}' == 'False'    Log     Save Bin button clicked and UPLOAD API triggered.
    Run Keyword If    '${button_displayed}' == 'True'    Fail     Save Bin button click failed. UPLOAD API not triggered.

The user verifies that the 'Add Bin' Button is disabled and confirms the 'Upload' API is not triggered
    #Verify that the 'Add Bin' button is disabled
    ${web_element}=         SeleniumLibrary.Get Webelement  ${SAVE_BIN_BTN}
    ${attr}=        Get Current Element Attributes     ${web_element}
    ${disabled_attribute}=      Get From Dictionary     ${attr}      disabled

    Log Many    attr:   ${disabled_attribute}

    Run Keyword If    '${disabled_attribute}' == 'True'
    ...    Log Many  The 'Add Bin' button is disabled because the Bin Details have not been populated, and the Upload API was not triggered.
    ...  ELSE
    ...    Fail  The 'Add Bin' button is enabled, but the Bin Details are empty, and the Upload API was triggered.