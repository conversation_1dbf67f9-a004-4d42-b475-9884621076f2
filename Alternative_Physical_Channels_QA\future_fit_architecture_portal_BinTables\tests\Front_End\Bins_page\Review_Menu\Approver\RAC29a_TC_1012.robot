*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../../keywords/front_end/Review_Bins_Page.robot
Resource            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../../common_utilities/Login.robot
Resource             ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Verify and review Bins displayed on Review Bins Page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_TO_REVIEW}     ${SELECT_BINS}      ${TO_BE_ACTIONED_BY}
    Set Test Documentation  ${DOCUMENTATION}

    IF    '${BIN_TO_REVIEW}' == '${EMPTY}'
        ${result}=    Run Keyword And Ignore Error    Get a Bin Number that is approved from the Database
        ${status}=    Get From List    ${result}    0  # Get the status part (PASS/FAIL)
        ${message}=    Get From List    ${result}    1  # Get the message part

        #Return from Keyword If  '${status}' == 'FAIL'
        IF    '${status}' == 'FAIL'
            Log    ${message}       WARN
            Return from Keyword
        END
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                    ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Review' Bins tab
    Then The bins(s) to_be_actioned_by column is mapped correctly in the database          ${BIN_TO_REVIEW}     ${TO_BE_ACTIONED_BY}


| *** Test Cases ***                                                                                                      |        *DOCUMENTATION*    		                                    |         *BASE_URL*                  |         *BIN_TO_REVIEW*           |         *SELECT_BINS*           |         *TO_BE_ACTIONED_BY*           |
| Approver_Verify that Approver is mapped as 0 on the database      | Verify and review Bins displayed on Review Bins Page   | Verifies bins against the database data and approves the Bin(s).    |           ${EMPTY}                 |         ${EMPTY}                |            False                 |                       0                    |
