<?xml version="1.0" encoding="utf-8"?>
<robot rpa="false" generator="Robot 3.2.1 (Python 3.5.4 on win32)" generated="20250609 14:19:00.261">
   <suite name="VMS Portal" id="s1" source="C:\Users\<USER>\source\repos\alternative_physical_channels\vms\tests\ATM_DETAILS\RAC29a-TC-605_ATM_DETAIL_Verify_ATM_Details.robot">
      <test name="Validate Existance of All Frontend ATMs" id="s1-t1">
         <kw library="Selenium" name="Given The user logs into the VMS Web Application">
            <doc>Validate Existance of All Frontend ATMs</doc>
            <arguments>
               <arg>Multiple keywords with name 'Verify if values are equal' found. Give the full name of the keyword you want to use:
    GenericMethods.Verify if values are equal
    common_keywords.Verify if values are equal</arg>
            </arguments>
            <msg timestamp="20250609 14:19:00.943" level="INFO">Given The user logs into the VMS Web Application</msg>
            <status endtime="20250609 14:19:51.554" status="PASS" starttime="20250609 14:19:00.943"/>
         </kw>
         <kw library="Selenium" name="When The user clicks on the ATM Details link">
            <doc>Validate Existance of All Frontend ATMs</doc>
            <arguments>
               <arg>Multiple keywords with name 'Verify if values are equal' found. Give the full name of the keyword you want to use:
    GenericMethods.Verify if values are equal
    common_keywords.Verify if values are equal</arg>
            </arguments>
            <msg timestamp="20250609 14:19:51.554" level="INFO">When The user clicks on the ATM Details link</msg>
            <status endtime="20250609 14:19:56.438" status="PASS" starttime="20250609 14:19:51.554"/>
         </kw>
         <kw library="Selenium" name="And The user lands on the ATM Details pages">
            <doc>Validate Existance of All Frontend ATMs</doc>
            <arguments>
               <arg>Multiple keywords with name 'Verify if values are equal' found. Give the full name of the keyword you want to use:
    GenericMethods.Verify if values are equal
    common_keywords.Verify if values are equal</arg>
            </arguments>
            <msg timestamp="20250609 14:19:56.439" level="INFO">And The user lands on the ATM Details pages</msg>
            <status endtime="20250609 14:20:02.304" status="PASS" starttime="20250609 14:19:56.439"/>
         </kw>
         <kw library="Selenium" name="Then The user reads number of ATM rows on Frontend">
            <doc>Validate Existance of All Frontend ATMs</doc>
            <arguments>
               <arg>Multiple keywords with name 'Verify if values are equal' found. Give the full name of the keyword you want to use:
    GenericMethods.Verify if values are equal
    common_keywords.Verify if values are equal</arg>
            </arguments>
            <msg timestamp="20250609 14:20:02.304" level="INFO">Then The user reads number of ATM rows on Frontend</msg>
            <status endtime="20250609 14:20:02.345" status="PASS" starttime="20250609 14:20:02.304"/>
         </kw>
         <kw library="Selenium" name="And The user compares Frontend ATM Details to the Backend ATM Details">
            <doc>Validate Existance of All Frontend ATMs</doc>
            <arguments>
               <arg>Multiple keywords with name 'Verify if values are equal' found. Give the full name of the keyword you want to use:
    GenericMethods.Verify if values are equal
    common_keywords.Verify if values are equal</arg>
            </arguments>
            <msg timestamp="20250609 14:20:02.345" level="INFO">And The user compares Frontend ATM Details to the Backend ATM Details</msg>
            <status endtime="20250609 14:20:03.991" status="FAIL" starttime="20250609 14:20:02.345"/>
         </kw>
         <tags>
            <tag>Validate Existance of All Frontend ATMs</tag>
         </tags>
         <status endtime="20250609 14:20:10.204" critical="yes" status="FAIL" starttime="20250609 14:19:00.941"/>
      </test>
      <status endtime="20250609 14:20:10.209" status="FAIL" starttime="20250609 14:19:00.261"/>
   </suite>
   <statistics>
      <total>
         <stat pass="0" fail="1">Critical Tests</stat>
         <stat pass="0" fail="1">All Tests</stat>
      </total>
      <tag>
         <stat pass="0" fail="1">Validate Existance of All Frontend ATMs</stat>
      </tag>
      <suite>
         <stat name="VMS Portal" pass="0" fail="1" id="s1">VMS Portal</stat>
      </suite>
   </statistics>
   <errors/>
</robot>
