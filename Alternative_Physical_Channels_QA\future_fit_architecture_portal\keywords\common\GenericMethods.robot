*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Contains generic methods

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             Process
Library                                             String
Library                                             DateTime
Library 	                                        Collections
Library                                            ../../utility/APCAccessToken.py


Resource                                            ../../keywords/common/Database.robot


*** Variables ***
${pattern}  SEPARATOR=
...  /^[A-Z]+$/i                  # one or more alphabet "





*** Keywords ***
Get the number of displayed web elements
    [Arguments]   ${ELEMENT_LOCATOR}

    ${webelements}=    SeleniumLibrary.Get WebElements    ${ELEMENT_LOCATOR}

    IF    '' == '${webelements}'
        Fail	Element: '${ELEMENT_LOCATOR}' was not found			# Fails with the given message.
    END
    ${count}=   SeleniumLibrary.Get Element Count        ${ELEMENT_LOCATOR}

    Log To Console    Element count is: ${count}
    RETURN    ${count}


Wait for spinner to disapear
    SeleniumLibrary.Wait Until Element Is Not Visible    xpath=//div[contains(@class, 'uploader-status')]  30s



String contains only numbers
    [Arguments]  ${data}
    ${matches}=  Get lines matching regexp  ${data}  ${pattern}

    IF   not $matches
        RETURN   ${True}
    ELSE
        RETURN   ${False}
    END


String should NOT match pattern
    [Arguments]  ${data}
    ${matches}=  Get lines matching regexp  ${data}  ${pattern}
    run keyword if  $matches
    ...  fail  '${data}' matched, but shouldn't have


Date is in the Past
    [Arguments]    ${Date_To_Verify}
    #Get the current date from the system calendar
    ${current_date}    Get Current Date    result_format=%Y-%m-%d %H:%M:%S
    ${date_difference_in_days}=     Subtract Date from Date    ${current_date}    ${Date_To_Verify}    verbose
    @{total_days}=    Split String    ${date_difference_in_days}
    ${final_days}=    Set Variable    ${total_days[0]}

    Log To Console    Date difference is: ${final_days}

    IF   '${final_days.replace('\n','').strip()}'=='-'
        ${date_has_passed}=     Set Variable   ${False}
    ELSE
        ${date_has_passed}=     Set Variable   ${True}
    END

    RETURN  ${date_has_passed}


Check Data Type
    [Arguments]    ${object}
    [Documentation]    Checks if the ${object } is INTEGER, NUMBER or STRING
     Return From Keyword If    not "${object}"    NONE
    ${result}    ${value}=    Run Keyword And Ignore Error    Convert To Number    ${object}
    ${isnumber}=    Run Keyword And Return Status    Should Be Equal As Strings    ${object}    ${value}
    ${result}    ${value}=    Run Keyword And Ignore Error    Convert To Integer    ${object}
    ${isinteger}=    Run Keyword And Return Status    Should Be Equal As Strings    ${object}    ${value}
    Return From Keyword If    ${isnumber}    'NUMBER'
    Return From Keyword If    ${isinteger}    'INTEGER'
    Return From Keyword    'STRING'

Convert Currency Text To Integer
	[Arguments] 	${currency_text}
	# ${currency_values} 		Get Regexp Matches 	${currency_text} 	$\d+(?:,\d{3})*(?:.\d{2})?
	${currency_values} 		Get Regexp Matches 	${currency_text} 	\\$([\\d\\.,]*) 	1
	${integer_values} 	Create List
	FOR 	${value} 	IN 	@{currency_values}
		${integer_part} 	${decimal_part} 	Split String 	${value} 	\.
		${integer_part} 	Remove String 	${integer_part} 	,
		${integer_value} 	Convert To Integer 	${integer_part}${decimal_part}
		Append To List 	${integer_values} 	${integer_value}
	END
	RETURN 	${integer_values}


Create Campaign Quarters
    #Create a ist for campaign quarters
    @{q1_months}=   Create List     Jan     Feb        Mar
    @{q2_months}=   Create List     Apr     May        Jun
    @{q3_months}=   Create List     Jul     Aug        Sep
    @{q4_months}=   Create List     Oct     Nov        Dec
    Set Global Variable    @{Q1_MONTHS}     @{q1_months}
    Set Global Variable    @{Q2_MONTHS}     @{q2_months}
    Set Global Variable    @{Q3_MONTHS}     @{q3_months}
    Set Global Variable    @{Q4_MONTHS}     @{q4_months}


Convert String to Dictionary
    [Arguments]     ${dict_string}
    ${json}=    Evaluate    json.loads('''${dict_string}''')    json
    Log Many    ${json}
    RETURN  ${json}





Get field value from JSON
    [Arguments]    ${json_object}  ${field_to_read}
    ${indent}=    Set Variable    ${SPACE*4}
    #Set Environment Variable    FIELD_VALUE        ${EMPTY}

    Get JSON Object    ${json_object}   ${field_to_read}

    ${value}=   Get Environment Variable    FIELD_VALUE
    RETURN  ${value}

Get JSON Object
    [Arguments]    ${obj}   ${field_to_read}
    ${key_found}=   Get Environment Variable    FIELD_FOUND

    Log    ${key_found}

    FOR    ${key}    IN    @{obj.keys()}

        ${value}=    Get From Dictionary    ${obj}    ${key}

        IF  "${key}" == "${field_to_read}"
                Set Environment Variable    FIELD_FOUND        ${True}
                ${key_found_2}=   Get Environment Variable    FIELD_FOUND
                Log     ${key_found_2}
                ${type}=    Evaluate    type($value)

                IF  "${type}" == "<class 'list'>"
                    Get Object from JSON List    ${value}    ${key}      ${field_to_read}
                ELSE IF  "${type}" == "<class 'dict'>"
                    Get Object from JSON Dictionary    ${value}    ${key}      ${field_to_read}
                ELSE
                    Log     (Log JSON Object) The value for field:'${field_to_read}' is '${value}'

                END

                Set Environment Variable    FIELD_VALUE        ${value}
                RETURN  ${value}
            ELSE IF  ${key_found} == ${True}
                Log     (Log JSON Object) The value for field:'${key}' is '${value}'
                Set Environment Variable    FIELD_VALUE        ${value}
                RETURN  ${value}

        END
    END

Get Object from JSON List
    [Arguments]    ${list_value}    ${key}      ${field_to_read}
    FOR    ${item}    IN    @{list_value}
        ${item_type}=    Evaluate    type($item)

        IF  "${item_type}" == "<class 'dict'>"
            Get Object from JSON Dictionary    ${item}    ${key}      ${field_to_read}
        ELSE IF  "${item_type}" == "<class 'list'>"
            Get Object from JSON List    ${item}    ${key}      ${field_to_read}
        ELSE
            Log     (Log List) The value for field:'${field_to_read}' is '${item}'
            Set Environment Variable    FIELD_VALUE       ${item}
            RETURN  ${item}
        END
    END

Get Object from JSON Dictionary
    [Arguments]    ${dict_value}    ${key}      ${field_to_read}
    Log    ${key}:
    Log   =================
    Get field value from JSON    ${dict_value}        ${field_to_read}
    Log   =================

Get the current schedule version from the database
    ${db_type}=   Set Variable   'MYSQL'
    #Get the current schedule version from the database
    ${schedule_query}   Set Variable     SELECT scheduleVersion FROM ATM_Marketing.MarketingSchedule where isCurrentVersion = true
    ${data_base_current_schedule}=      Execute SQL Query  ${db_type}  ${schedule_query}    True
    ${schedule_version}=    Get From Dictionary    ${data_base_current_schedule}    scheduleVersion
    RETURN  ${schedule_version}

Verify if values are equal
    [Arguments]     ${EXPECTED_VALUE}    ${ACTUAL_VALUE}
         Run Keyword And Continue On Failure    Should Be Equal As Strings    ${EXPECTED_VALUE}    ${ACTUAL_VALUE}
         Log Many     Comparison result  : Expected Value = ${EXPECTED_VALUE}, Actual Value = ${ACTUAL_VALUE}


