from datetime import datetime, date

from dateutil import parser


def remove_trailing_zero(input_string):
    # Check if the last character is '0'
    if input_string.endswith('0'):
        # Remove the last character
        input_string = input_string[:-1]
    return input_string


def format_timestamp_v1(timestamp, use_t_separator=True):
    """
    Format a timestamp with or without the 'T' separator based on user preference.

    :param timestamp: Original timestamp as a string (e.g. '2024-11-12 06:37:03.6943').
    :param use_t_separator: Boolean flag to indicate whether to insert a 'T' separator.
    :return: Formatted timestamp as a string.

     Example:
    format_timestamp('2024-11-12 06:37:03.6943', True) -> '2024-11-12T06:37:03.694300'
    format_timestamp('2024-11-12 06:37:03.6943', False) -> '2024-11-12 06:37:03.694300'
    """
    # If the input timestamp is a string, convert it to a datetime object
    if isinstance(timestamp, str):
        dt_obj = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S.%f')
    elif isinstance(timestamp, datetime):
        dt_obj = timestamp
    else:
        raise TypeError("timestamp must be a string or a datetime object")

    # Format the datetime object back to a string with or without 'T'
    if use_t_separator:
        formatted_timestamp = dt_obj.strftime('%Y-%m-%dT%H:%M:%S.%f')
    else:
        formatted_timestamp = dt_obj.strftime('%Y-%m-%d %H:%M:%S.%f')

    # Return the formatted timestamp
    return formatted_timestamp


def format_timestamp(timestamp, with_t_separator=True):
    """
    Formats a timestamp (either a string or datetime object) with or without the 'T' separator.

    Parameters:
        timestamp (str or datetime): The timestamp to format, either as a string or datetime object.
        with_t_separator (bool): Whether to use the 'T' separator between date and time.

    Returns:
        str: The formatted timestamp as a string.
    """

    # If the timestamp is a string, parse it using dateutil.parser
    if isinstance(timestamp, str):
        try:
            parsed_timestamp = parser.parse(timestamp)
        except (ValueError, OverflowError):
            raise ValueError(f"Invalid timestamp string: {timestamp}")
    elif isinstance(timestamp, datetime):
        parsed_timestamp = timestamp
    else:
        raise TypeError("Timestamp must be a string or a datetime object.")

    # Now format the parsed datetime object according to the user preference
    date_format = "%Y-%m-%d"
    time_format = "%H:%M:%S.%f"

    if with_t_separator:
        # Format with 'T' separator
        formatted_timestamp = parsed_timestamp.strftime(f"{date_format}T{time_format}")
    else:
        # Format without 'T' separator
        formatted_timestamp = parsed_timestamp.strftime(f"{date_format} {time_format}")

    return formatted_timestamp


def format_date(timestamp):
    """
    Formats a timestamp (either a string or datetime object) with or without the 'T' separator.

    Parameters:
        timestamp (str or datetime): The timestamp to format, either as a string or datetime object.
        with_t_separator (bool): Whether to use the 'T' separator between date and time.

    Returns:
        str: The formatted timestamp as a string.
    """

    # If the timestamp is a string, parse it using dateutil.parser
    print(type(timestamp))
    if isinstance(timestamp, str):
        try:
            parsed_timestamp = parser.parse(timestamp)
        except (ValueError, OverflowError):
            raise ValueError(f"Invalid timestamp string: {timestamp}")
    elif isinstance(timestamp, date):
        parsed_timestamp = timestamp
    else:
        raise TypeError(f"Timestamp must be a string or a date object, the type: '{type(timestamp)}' is not valid.")

    # Now format the parsed datetime object according to the user preference
    date_format = "%Y-%m-%d"

    # Format without 'T' separator
    formatted_timestamp = parsed_timestamp.strftime(f"{date_format}")

    return formatted_timestamp
