*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : <EMAIL>

Documentation  Bin Tables SearchBinsByNumber Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                            Collections
Library                                            OperatingSystem
Library                                            ../../keywords/controllers/resources/bintypes/SearchBinTypeByName.py
Library                                            ../../keywords/controllers/resources/CommonFunctions.py
Library                                            ../../keywords/controllers/resources/RestRequestsMarshal.py
Library                                            ../../keywords/controllers/resources/RestRequestsUnMarshal.py

#***********************************PROJECT RESOURCES***************************************
Resource                                           ../../keywords/controllers/common/GenericMethods.robot
Resource                                           ../../keywords/common/DatabaseConnector.robot
Resource                                            ../../../common_utilities/common_keywords.robot

#***********************************PROJECT VARIABLES***************************************
** Variables ***
${GLOBAL_BIN_TYPE_NAME}


*** Keywords ***
The User sends a GET Request to Search Bin Types by Name
    [Arguments]     ${BASE_URL}     ${BIN_TYPE_NAME}    ${ACTION}

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
       ${BASE_URL}=         Get Environment Variable    BASE_URL
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
    Log To Console     ${base_url}

    #Create the REST Request that must be sent, this request will only contain a URL and parameters
    ${instance}=        Create SearchBinTypeByName Instance    ${base_url}      ${BIN_TYPE_NAME}     ${ACTION}     #Create an instance of

    #${rest_request}=        Build Rest Request       ${base_url}     ${BIN_NUMBER}     ${ACTION}  #Instantiate the CreateRequest class
    ${endpoint}=    Get Endpoint    ${instance}  #intialize the endpoint value
    Log To Console     ${endpoint}
    Log Many    ${endpoint}
    ${params}=    Get Parameters    ${instance}  #intialize the parameters
    Log To Console     ${params}
    Log Many    ${params}

    #Send the Get Rest API request and save the response to a variable
    ${method}=     Set Variable   GET
    ${BEARER_TOKEN}=     Get Bearer Token
    ${headers}=     Get Rest API headers    ${BEARER_TOKEN}

    ${response} =       Send Rest Request    ${endpoint}   headers=${headers}   method=${method}     params=${params}


    #Save the response in a Global Variable
    Set Global Variable    ${REST_RESPONSE}        ${response}
    Log To Console     ${REST_RESPONSE}

    #Created an instance for the Response object
    Create ReadApiResponse Instance

Create SearchBinTypeByName Instance
    [Arguments]    ${BASE_URL}  ${BIN_TYPE_NAME}    ${ACTION}
    ${instance}=    Evaluate    SearchBinTypeByName.CreateRESTRequest('${BASE_URL}','${BIN_TYPE_NAME}', '${ACTION}')    modules=SearchBinTypeByName
    RETURN    ${instance}

The service returns an expected status code
    [Arguments]     ${EXPECTED_STATUS_CODE}
    #Get the API response's returned status code
    ${status_code}=     Get Response Status Code
    #Verify that the response status code is the same as the expected status code
     ${result}=  Run Keyword And Return Status    Should Be Equal     '${status_code}'     '${EXPECTED_STATUS_CODE}'
     Run Keyword If    '${result}' == 'False'    Fail    The 'SearchBinsByNumber' REST API call failed, the returned status is '${status_code}'



The expected Error Message must be displayed
    [Arguments]     ${EXPECTED_ERROR_MESSAGE}

    IF    '${EXPECTED_ERROR_MESSAGE}' == '[]'
        Verify that the API retruned an empty response
        RETURN
    END


    #Read all errors returned by the API
    ${api_error_message_detail}=    Get Error details data
    Log     ${api_error_message_detail}
    ${error_msg_one_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_detail}'     '${EXPECTED_ERROR_MESSAGE}'
    ${error_msg_two_verification} =     Set Variable        ${False}
    IF    ${error_msg_one_verification} == ${False}
         #Create a dictionary for all error fields
        ${error_fields_dict}=       Create List       BinNumber   binNumber   actionDate  binTypeIds  binUploadRequests     action

        FOR    ${field_element}    IN    @{error_fields_dict}
             ${api_error_message_fields}=       Get Field's Error   ${field_element}
             Log     '${api_error_message_fields}'
             #${error_msg_two_verification}=  Run Keyword And Return Status    Should Be Equal As Strings     '${api_error_message_fields}'     '${EXPECTED_ERROR_MESSAGE}'
             ${error_msg_two_verification}=    Set Variable If  '${EXPECTED_ERROR_MESSAGE}' in '${api_error_message_fields}'     ${True}     ${False}

             Run Keyword If    ${error_msg_two_verification}
                ...    Exit For Loop

        END
    END


    #Verify that the returned error is as expected
    Run Keyword If    '${error_msg_one_verification}' == 'False' and '${error_msg_two_verification}' == 'False'    Fail    The 'Upload' REST API call did not return the expected message which is '${EXPECTED_ERROR_MESSAGE}'.

Verify that the API retruned an empty response
    #Instantiate the Bin Response object
    ${bin_object_instace}=      Get the Bin Type Details
    Log Many    ${bin_object_instace}
    #Verify that the response is empty
    ${verification_passed}=      Run Keyword And Return Status    Verify if values are equal     '[]'     '${bin_object_instace}'

    Run Keyword If    not ${verification_passed}
    ...    Fail     The API did not return an empty response!

Create SearchBinsByNumber Instance
    [Arguments]    ${BASE_URL}  ${BIN_NUMBER}   ${ACTION}
    ${instance}=    Evaluate    SearchBinsByNumber.CreateRESTRequest('${BASE_URL}','${BIN_NUMBER}','${ACTION}')    modules=SearchBinsByNumber
    RETURN    ${instance}

Get Endpoint
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_endpoint
    RETURN    ${result}

Get Parameters
    [Arguments]    ${instance}
    ${result}=    Call Method    ${instance}    get_params
    RETURN    ${result}

# Respose Keywords

Create ReadApiResponse Instance
    #Read the response from the global variable
    ${json_data}=   Convert To Dictionary  ${REST_RESPONSE}
    Log Many    ${json_data}
    #Create an instance of the response class using the rest response
    ${instance}=    Evaluate    RestRequestsUnMarshal.ReadApiResponse(${json_data})    modules=RestRequestsUnMarshal
    #Save the response class instance as a global variable
    Set Global Variable    ${REST_RESPONSE_INSTANCE}        ${instance}



Get Response Status Code
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_field   status_code
    RETURN    ${result}

#Keywords to read error response fields
Get Error details data
     #Read the response class instance from the global variable
    #${json_data}=   Convert To Dictionary  ${REST_RESPONSE_INSTANCE}

    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_api_data_detail
    RETURN    ${result}



Get Field's Error
    [Arguments]   ${FIELD_NAME}
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_errors_for_field      ${FIELD_NAME}
    RETURN    ${result}

#Keyword to read successful response
Get the Bin Type Details
    ${result}=    Call Method    ${REST_RESPONSE_INSTANCE}    get_bin_type_details
    RETURN    ${result}



Get the Bin Type ID
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_id
    RETURN    ${result}

Get the Bin Type Name
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_bin_type_name
    RETURN    ${result}


Get the Bin Type Description
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_bin_type_description
    RETURN    ${result}


Get the Bin Type Captured By
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_bin_type_captured_by
    RETURN    ${result}


Get the Bin Type Captured Date
     [Arguments]     ${BIN_TYPE_INSTNANCE}
    ${result}=    Call Method    ${BIN_TYPE_INSTNANCE}    get_bin_type_captured_date
    RETURN    ${result}


The User verifies that the searched Bin Type exists in the database
    Log    Bin Type Name:${GLOBAL_BIN_TYPE_NAME}
    
    #Instantiate the Bin Type Details Response object
    ${bin_type_object_instace}=      Get the Bin Type Details
    Log Many    ${bin_type_object_instace}
    Log     '################################################'
    ${verification_passed}=      Run Keyword And Return Status    Should Not Be Equal     '[]'     '${bin_type_object_instace}'
    Run Keyword If    not ${verification_passed}
    ...    Fail     The 'getbintypebyid' API did not return a response!

    FOR    ${index}    ${element}    IN ENUMERATE    @{bin_type_object_instace}
        ${bin_type_id}=        Get the Bin Type ID     ${element}
        ${bin_type_name}=        Get the Bin Type Name     ${element}
        ${bin_type_name}=       Convert To Upper Case   ${bin_type_name}
        ${GLOBAL_BIN_TYPE_NAME}=       Convert To Upper Case   ${GLOBAL_BIN_TYPE_NAME}

        #Verify that the correct Bin Type has been returned by the API
        IF    '${GLOBAL_BIN_TYPE_NAME}' in '${bin_type_name}'
            ${bin_type_verification_passed}=      Set Variable      ${True}
            ${bin_type_description}=        Get the Bin Type Description     ${element}
            ${bin_type_captured_by}=        Get the Bin Type Captured By     ${element}
            ${bin_type_captured_date}=        Get the Bin Type Captured Date     ${element}

            ${GLOBAL_BIN_TYPE_NAME}=  Convert to Lower Case     ${GLOBAL_BIN_TYPE_NAME}
            ${bin_type_name}=  Convert to Lower Case     ${bin_type_name}

            ${db_bin_type_results}=    Get the Bin Type details from the Database using the partial Bin Type Name    ${bin_type_name}

            Log Many    ${db_bin_type_results}

            ${db_first_row}=    Get From List    ${db_bin_type_results}    0
            ${DB_Bin_Type_Id}=    Get From Dictionary   ${db_first_row}   Id
            ${DB_Bin_Type_Name}=    Get From Dictionary   ${db_first_row}   Name
            ${DB_Bin_Type_Description}=    Get From Dictionary   ${db_first_row}   Description
            ${DB_Bin_Type_CapturedBy}=    Get From Dictionary   ${db_first_row}   CreatedBy
            ${DB_Bin_Type_CapturedDate}=    Get From Dictionary   ${db_first_row}   CreatedDate
            ${DB_Bin_Type_CapturedDate}=        Format Timestamp for API and Database      ${DB_Bin_Type_CapturedDate}       ${True}


            ${modified_string}=    Convert To String    ${DB_Bin_Type_CapturedDate}
            ${modified_string_array}=  Split String     ${modified_string}     T
            ${modified_string_len}=   Get Length    ${modified_string_array}[1]
            IF    '${modified_string_len}' == '15'
                ${DB_Bin_Type_CapturedDate}=    Remove last zero from a String     ${DB_Bin_Type_CapturedDate}
            END

            ${bin_type_name_verification_passed}=      Run Keyword And Return Status    Should Be Equal     '${bin_type_id.strip()}'     '${DB_Bin_Type_Id.strip()}'
            Run Keyword If    ${bin_type_name_verification_passed}
            ...    Log Many    The 'searchbintypesbyname' API did returned the same bin_type_id as the data retrived from the DB for the bin_type_name: '${bin_type_name}'. The data returned by the API is '${bin_type_id}'.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail    The 'searchbintypesbyname' API did returned a different bin_type_id! The expected bin_type_id is '${DB_Bin_Type_Id}', while the returned data returned by the API is '${bin_type_id}'.

            ${bin_type_name_verification_passed}=      Run Keyword And Return Status    Should Be Equal     '${bin_type_description.strip()}'     '${DB_Bin_Type_Description.strip()}'
            Run Keyword If    ${bin_type_name_verification_passed}
            ...    Log Many    The 'searchbintypesbyname' API did returned the same bin_type_description as the data retrived from the DB for the bin_type_name: '${bin_type_name}'. The data returned by the API is '${bin_type_description}'.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail    The 'searchbintypesbyname' API did returned a different bin_type_description! The expected bin_type_description is '${DB_Bin_Type_Description}', while the returned data returned by the API is '${bin_type_description}'.

            ${bin_type_name_verification_passed}=      Run Keyword And Return Status    Should Be Equal     '${bin_type_captured_by.strip()}'     '${DB_Bin_Type_CapturedBy.strip()}'
            Run Keyword If    ${bin_type_name_verification_passed}
            ...    Log Many    The 'getbintypebyid' API did returned the same bin_type_captured_by as the data retrived from the DB for the bin_type_name: '${bin_type_name}'. The data returned by the API is '${bin_type_captured_by}'.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail    The 'getbintypebyid' API did returned a different bin_type_captured_by! The expected bin_type_captured_by is '${DB_Bin_Type_CapturedBy}', while the returned data returned by the API is '${bin_type_captured_by}'.

            ${bin_type_name_verification_passed}=      Run Keyword And Return Status    Should Be Equal     '${bin_type_captured_date}'     '${DB_Bin_Type_CapturedDate}'
            Run Keyword If    ${bin_type_name_verification_passed}
            ...    Log Many    The 'searchbintypesbyname' API did returned the same bin_type_captured_date as the data retrived from the DB for the bin_type_name: '${bin_type_name}'. The data returned by the API is '${bin_type_captured_date}'.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail    The 'searchbintypesbyname' API did returned a different bin_type_captured_date! The expected bin_type_captured_date is '${DB_Bin_Type_CapturedDate}', while the returned data returned by the API is '${bin_type_captured_date}'.

            #Verify that the current bin type is active
            ${DB_Bin_Type_isDeleted_Status}=    Get From Dictionary   ${db_first_row}   IsDeleted

            ${bin_type_isDeleted_verification_passed}=      Run Keyword And Return Status    Should Be Equal As Strings     '0'     '${DB_Bin_Type_isDeleted_Status}'
            Run Keyword If    ${bin_type_isDeleted_verification_passed}
            ...    Log Many    The bin_type_id: '${bin_type_id}' is active on th database.
            ...  ELSE
            ...    Run Keyword And Continue On Failure    Fail    The bin_type_id: '${bin_type_id}', which was returned by 'getbintypebyid' API, is deleted on the database.
            Exit For Loop
        ELSE
            ${bin_type_verification_passed}=      Set Variable      ${False}
        END


    END

    Run Keyword If    not ${bin_type_verification_passed}
    ...    Fail     The 'searchbintypesbyname' API returned a different bin_type_name! The expected bin_type_name is '${GLOBAL_BIN_TYPE_NAME}', while the returned data returned by the API is '${bin_type_name}'.
