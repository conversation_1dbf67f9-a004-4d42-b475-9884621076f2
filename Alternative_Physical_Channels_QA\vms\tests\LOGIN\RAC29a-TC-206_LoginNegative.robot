*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    Login Negative Tests
Suite Setup                                         Set up environment variables
Documentation  VMS Login Negative Tests - Test to validate that the system properly rejects login attempts with invalid username

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/VMSPage/NegativeScenarios.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../common_utilities/Login.robot


*** Variables ***
${VMS_URL}                                        https://vms.uat.absa.africa/

*** Keywords ***
Invalid or blank credential validation
    [Arguments]  ${DOCUMENTATION}    ${TEST_ENVIRONMENT}  ${TestCaseValidation}
    Set Test Documentation  ${DOCUMENTATION}

    # Open browser and VMS URL
    Begin Web test
    ${url}=    Read Config Property    ${TEST_ENVIRONMENT}
    Load    ${url}
    
    # For invalid username test
    Sleep    2s
    Input Text    ${VMS_USERNAME_INPUT}    ${INVALID_APPLICATION_USERNAME}
    Wait Until Element Is Visible    ${VMS_PASSWORD_INPUT}
    Input Text     ${VMS_PASSWORD_INPUT}  ${APPLICATION_PASSWORD}
    Sleep    2s
    Click Button    ${VMS_LOGIN_BTN}
    
    # Validate error message
    Wait Until Page Contains    There was an error processing your request.
    Page Should Contain    There was an error processing your request.
    Capture Page Screenshot


*** Test Cases ***
Invalid Username            
    [Documentation]    Invalid Username
    Invalid or blank credential validation    Invalid Username    VMS_UAT    Invalid Username
