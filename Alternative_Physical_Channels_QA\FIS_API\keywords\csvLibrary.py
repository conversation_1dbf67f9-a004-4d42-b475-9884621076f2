import os
import csv

def file_exists(file):
    return os.path.isfile(file)

#function to go to nth row and nth column
def go_to_nth_row_nth_column(File,row_no,col_no):
    inputFile = File
    row_no=int(row_no)
    col_no=int(col_no)
    with open(inputFile) as ip:
        reader = csv.reader(ip)
        for i, row in enumerate(reader):
            if i == row_no:      # here's the row 
                #print row[col_no] # here's the column
                return row[col_no]