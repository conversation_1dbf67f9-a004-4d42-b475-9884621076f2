*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Documentation  Login to vms system

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             ../common_utilities/CommonUtils.py
Library                                             Process
Library                                             ../future_fit_architecture_portal/utility/Common_Functions.py
Library                                             BuiltIn
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../future_fit_architecture_portal/keywords/common/Navigation.robot
Resource                                            ../future_fit_architecture_portal/keywords/common/GenericMethods.robot
Resource                                            common_keywords.robot

*** Variables ***
${VMS_USERNAME_INPUT}                                   name=txtUsername
${VMS_PASSWORD_INPUT}                                   name=txtPassword
${VMS_LOGIN_BTN}                                    name=btnLogon

${USERNAME_INPUT}                                   name=username
${PASSWORD_INPUT}                                   name=password
${SUBMIT_BTN}                                       xpath=//button/span[contains(text(),'Log in')]/parent::button
#${MICROSOFT_PICK_ACCOUNT}                           xpath=//small[@data-bind='text: session.unsafe_displayName']
${MICROSOFT_PICK_ACCOUNT}                           xpath=//div[contains(@data-bind, 'session.isSignedIn') or contains(@data-bind, 'session.unsafe_displayName') or contains(@data-bind, 'session.unsafe_fullName')]
${APC_HOME_PAGE_ICON}                               xpath=//img[contains(@class, 'nav-image')]
${MICROSOFT_EMAIL_INPUT}                            name=loginfmt
${MICROSOFT_EMAIL_NEXT_BTN}                         id=idSIButton9
${MICROSOFT_EMAIL_PASSWORD_INPUT}                   name=passwd
${MICROSOFT_EMAIL_SIGN_IN_BTN}                      id=idSIButton9
${MICROSOFT_DONT_SAVE_PASSWORD}                     id=idBtn_Back
${LOG_OUT_TEXT_ELEMENT}                             xpath=//div[@class='wrap-content']
${BROWSER}
${CMD}                                              Page.enable
${PARAMS}                                           {}


*** Keywords **
The user navigates to APC Home page

    ${home_page_button_exists}=     Run Keyword And Return Status    Page Should Contain Element    ${APC_HOME_PAGE_ICON}

    IF    ${home_page_button_exists}
        Log     The 'APC Home Page Icon' button is displayed on the page.
    ELSE
        Capture Page Screenshot   APC_Home_Btn_NOT_Displayed.png
        Fail    The 'APC Home Page Icon' button was not displayed on the page!
    END

    Click Element    ${APC_HOME_PAGE_ICON}
    Sleep    3s
    Capture Page Screenshot   APC_Home_Btn_Displayed.png

    Log to console  --------------------------The user has clicked APC Home Page Menu

The user logs into the VMS Web Application
    [Arguments]  ${URL}

    ${system}=    Evaluate    platform.system()    platform

    Log to Console    \nOperating System: ${system}

    Log to console  --------------------------The user logs into VMS

    Run Keyword If    '${system}' == 'Windows'    VMS Windows system Login    ${URL}    ${APPLICATION_USERNAME}    ${APPLICATION_PASSWORD}
    ...    ELSE    Linux system login    ${URL}



Create Chrome Options
    [Arguments]    ${options}    ${executable_path}
    ${_chrome_options}=    Evaluate    `from selenium.webdriver import ChromeOptions; options = ChromeOptions(); [options.split()]`
    Set Variable If    ${executable_path}    ${chrome_options.binary_location} = ${executable_path}
    Set Suite Variable    ${_chrome_options}

Create Edge Options
    [Arguments]    ${options}    ${executable_path}

    ${_edge_options}=    Evaluate    sys.modules['selenium.webdriver'].EdgeOptions()    modules=sys,selenium
    FOR    ${option}    IN    @{options}
        Call Method    ${_edge_options}    add_argument    ${option}
    END
     #Evaluate    setattr(${edge_options}, 'binary_location', r'${executable_path}')    modules=sys
     #Evaluate    setattr(${_edge_options}, 'binary_location', '${executable_path}')    modules=sys
    #Call Method    ${edge_options}    binary_location.__set__    ${_edge_options}    ${executable_path}
    #${_edge_options.binary_location}=      Set Variable        ${executable_path}

    Log    ${_edge_options.arguments}
    Log    ${_edge_options.binary_location}
    Set Suite Variable    ${_edge_options}

Begin Web test

     Kill process    ${BROWSER}


    ${is_browser_browser}=   Get Environment Variable    IS_HEADLESS_BROWSER

    #Decide whether to run headles chrome or displayed chrome
    ${is_headless_browser_type}=     Convert To Upper Case       ${is_browser_browser}
    ${browser_name}=     Convert To Upper Case       ${BROWSER}

     ${user_home}=    Get Environment Variable    UserProfile
     Log     Logged in User: ${user_home}
    #Call Method     ${chrome_options}    add_argument            --headless=new  #Run headless browser


    #Open the browser

     IF    '${browser_name}' == 'EDGE'
        ${edgedriver_path}=      Set Variable    C:/bin/msedgedriver.exe    #Get EDGE Driver Path
        ${edge_options}=    Get Edge Driver Options
        IF    '${is_headless_browser_type}' == 'YES'
            ${edge_options}=    Get EDGE Driver Options
            Open Browser     about:blank      ${BROWSER}     options=${edge_options}       executable_path=${edgedriver_path}
        ELSE
            ${edge_options}=    Evaluate    sys.modules['selenium.webdriver'].EdgeOptions()
            ${root}=            Set Variable  ${user_home}\\
            ${path}=            Set Variable  Downloads
            ${downl_path}=    Set Variable    ${root}${path}
            ${prefs}=    Create Dictionary    download.default_directory=${downl_path}  download.directory_upgrade=True  download.default_directory=${downl_path}  savefile.default_directory=${downl_path}  download.prompt_for_download=False
            Call Method    ${edge_options}    add_experimental_option    prefs    ${prefs}
            Open Browser     about:blank      ${BROWSER}     options=${edge_options}    executable_path=${edgedriver_path}
        END
    ELSE


        ${chromedriver_path}=       Set Variable    C:/bin/chromedriver.exe     #Get Chromedriver Path
        ${chrome_options}=     Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys, selenium.webdriver

        IF    '${is_headless_browser_type}' == 'YES'
            ${BROWSER}=   Set Variable      headlesschrome
            Call Method     ${chrome_options}    add_argument            --headless
        END
        ${root}=            Set Variable  ${user_home}\\
        ${path}=            Set Variable  Downloads
        ${downl_path}=    Set Variable    ${root}${path}

        ${prefs}  Create Dictionary  useAutomationExtension=${FALSE}   download.default_directory=${downl_path}  download.directory_upgrade=True  download.default_directory=${downl_path}  savefile.default_directory=${downl_path}  download.prompt_for_download=False  #excludeSwitches=['enable-automation']  #download.prompt_for_download=${FALSE}  plugins.always_open_pdf_externally=${TRUE}  plugins.plugins_disabled=${disabled}
        Call Method     ${chrome_options}   add_experimental_option  prefs  ${prefs}



        Call Method     ${chrome_options}    add_argument            user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data
        Call Method     ${chrome_options}    add_argument            --disable-dev-shm-usage  #disable page crash of chrome
        Call Method     ${chrome_options}    add_argument            --ignore-certificate-errors
        Call Method    ${chrome_options}    add_argument    --incognito

    #    Call Method     ${chrome_options}    add_argument            --enable-features=NetworkServic
        Call Method     ${chrome_options}    add_argument            --no-sandbox
        ${dc}   Evaluate    sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME  sys, selenium.webdriver
        ${Options}=    Call Method    ${ChromeOptions}    to_capabilities
        Open Browser                        about:blank      ${BROWSER}     options=${chrome_options}       executable_path=${chromedriver_path}
    END

Load
    [Arguments]         ${URL}
    #${base_url}=        Read Config Property    ${URL}

    Maximize Browser Window
    Run Keyword If    '${URL}' != '${EMPTY}'    Go To    ${URL}
    ...  ELSE IF    '$${URL}' == '${EMPTY}'  Fail    Test URL not provided
    Sleep    10s
    #Check if Microsoft Login is required
    ${element_count_1}=       Get Element Count    ${MICROSOFT_PICK_ACCOUNT}
    Run Keyword And Return If    ${element_count_1} > 0   Navigation.Run Keyword Until Success   Click Element   ${MICROSOFT_PICK_ACCOUNT}
    Sleep    10s

    ${element_count_2}=       Get Element Count    ${MICROSOFT_PICK_ACCOUNT}
    Run Keyword And Return If    ${element_count_2} > 0   Navigation.Run Keyword Until Success   Click Element   ${MICROSOFT_PICK_ACCOUNT}

    Sleep    5s
    ${element_count_3}=      Get Element Count     ${MICROSOFT_EMAIL_INPUT}
    Run Keyword And Return If    ${element_count_3} > 0   Login to Mocrosoft





End Web Test
    #Close the browser
    Sleep    5s
    Close Browser




The user logs into Future Fit Architecture portal
    [Arguments]  ${URL}    ${BROWSER}  ${DRIVER_BROWSER}  ${LOGON_USER}
    
    ${system}=    Evaluate    platform.system()    platform

    Log to Console    \nOperating System: ${system}

    Log to console  --------------------------The user logs into Future-Fit Architecture                             

    Run Keyword If    '${system}' == 'Windows'    Windows system Login    ${URL}
    ...    ELSE    Linux system login    ${URL}

    The user navigates to their Specified Dashboard     ATM_MARKETING_DASHBOARD

The user logs into Future Fit Architecture - Bin Tables portal
    [Arguments]  ${URL}

    ${system}=    Evaluate    platform.system()    platform

    Log to Console    \nOperating System: ${system}

    Log to console  --------------------------The user logs into Future-Fit Architecture - Bin Tables

    Run Keyword If    '${system}' == 'Windows'    Windows system Login    ${URL}
    ...    ELSE    Linux system login    ${URL}

    The user navigates to their Specified Dashboard     BIN_TABLES

The user logs into Future Fit Architecture
    [Arguments]  ${URL}

    ${system}=    Evaluate    platform.system()    platform

    Log to Console    \nOperating System: ${system}

    Log to console  --------------------------The user logs into Future-Fit Architecture - Bin Tables

    Run Keyword If    '${system}' == 'Windows'    Windows system Login    ${URL}
    ...    ELSE    Linux system login    ${URL}


Windows system Login
    [Arguments]  ${URL}

    Begin Web test

    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
          ${BASE_URL}=      Get Environment Variable    BASE_URL
       ELSE
          ${BASE_URL}=      Set Variable    ${URL}
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}
    Log     ${base_url}

    Load    ${BASE_URL}

    Capture Page Screenshot

VMS Windows system Login
    [Arguments]  ${URL}    ${APPLICATION_USERNAME}    ${APPLICATION_PASSWORD}


    ${url_exists_on_env_var}=   Run Keyword And Return Status  Get Environment Variable    BASE_URL

    IF   ${url_exists_on_env_var}
          ${BASE_URL}=      Get Environment Variable    BASE_URL
       ELSE
          ${BASE_URL}=      Set Variable    ${URL}
    END

    #Get the domain url details
    ${base_url}=    Read Config Property      ${BASE_URL}

    Begin Web test

    Load    ${base_url}

    #Check if the user name is returned on the results
    ${User_Name_Element_Visible}=  Run Keyword And Return Status    Element Should Be Visible   ${VMS_USERNAME_INPUT}

    WHILE    ${User_Name_Element_Visible}
        Input Text    ${VMS_USERNAME_INPUT}    ${APPLICATION_USERNAME}
        Wait Until Element Is Visible    ${VMS_PASSWORD_INPUT}
        Input Text     ${VMS_PASSWORD_INPUT}  ${APPLICATION_PASSWORD}
        Click Button    ${VMS_LOGIN_BTN}
        Sleep    2s
        ${User_Name_Element_Visible}=  Run Keyword And Return Status    Element Should Be Visible   ${VMS_USERNAME_INPUT}
    END




    Log To Console    ------------------------------ User successfully accessed VMS

    Capture Page Screenshot

Linux system login
    [Arguments]  ${URL}
    ${chrome_options}=    Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys

    Call Method    ${chrome_options}    add_argument    --disable-dev-shm-usage  #disable page crash of chrome

    Call Method    ${chrome_options}    add_argument    --no-sandbox  #disable sandbox mode

    Call Method    ${chrome_options}    add_argument    --headless  #run with no web browser

    Call Method    ${chrome_options}    add_argument    --ignore-certificate-errors
    
    Create Webdriver    driver_name=Chrome    alias=ffadriver    chrome_options=${chrome_options}    executable_path=/usr/local/bin/chromedriver
    
    Go To    ${URL}
    
   Maximize Browser Window

Enter username and password
    [Arguments]  ${APPLICATION_USERNAME}   ${APPLICATION_PASSWORD}
    Log To Console    Username is ${APPLICATION_USERNAME} password id ${APPLICATION_PASSWORD} 

    Wait for spinner to disapear
    
    Input Text    ${USERNAME_INPUT}    ${APPLICATION_USERNAME}   

    Wait Until Element Is Visible    ${PASSWORD_INPUT}    

    Input Text     ${PASSWORD_INPUT}  ${APPLICATION_PASSWORD} 



Login to Mocrosoft

    ${MS_USERNAME}=      Get Environment Variable    MS_USERNAME
    ${MS_PASSWORD}=      Get Environment Variable    MS_PASSWORD

    Log To Console    Username is ${MS_USERNAME} password id ${MS_PASSWORD}

    #Wait for spinner to disapear


    Navigation.Run Keyword Until Success   Wait Until Element Is Visible   ${MICROSOFT_EMAIL_INPUT}

    Input Text    ${MICROSOFT_EMAIL_INPUT}     ${MS_USERNAME}

    Click button  ${MICROSOFT_EMAIL_NEXT_BTN}

    Wait Until Element Is Visible   ${MICROSOFT_EMAIL_PASSWORD_INPUT}

    Input Text     ${MICROSOFT_EMAIL_PASSWORD_INPUT}  ${MS_PASSWORD}

    Click button  ${MICROSOFT_EMAIL_SIGN_IN_BTN}

    Wait Until Element Is Visible   ${MICROSOFT_DONT_SAVE_PASSWORD}

    Click button  ${MICROSOFT_DONT_SAVE_PASSWORD}

    Sleep   20s



Click on Login button
    Click button                                  ${SUBMIT_BTN}

    #Wait until ATM Compliants link is displayed
    Wait Until Page Contains  Alternative Physical Channels Portal     5     Alternative Physical Channels Portal title on landing page is missing

Kill process
    [Arguments]  ${PROCESS_NAME}

    ${PROCESS_NAME_LowerCase}=    Evaluate     "${PROCESS_NAME}".lower()

    IF    '${PROCESS_NAME_LowerCase}' == 'edge'
         ${PROCESS_NAME_LowerCase}=        Set Variable     msedge
    END
    ${handle} =   Catenate    SEPARATOR=.   ${PROCESS_NAME_LowerCase}   exe

    Log to Console    \nProcess to kill: ${handle}

    ${rc_code}    ${output}    Run And Return Rc And Output    taskkill /F /IM ${handle}
    Run Keyword If    '${rc_code}' != '0'    Log    There was error during termination of process    WARN


Wait for the spinner to disapear

    SeleniumLibrary.Wait Until Element Is Not Visible    xpath=//div[contains(@class, 'ng-star-inserted')]  5s
