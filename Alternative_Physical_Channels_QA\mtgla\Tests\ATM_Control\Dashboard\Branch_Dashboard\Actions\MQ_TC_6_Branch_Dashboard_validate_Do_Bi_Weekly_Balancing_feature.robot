*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control- Branch Dashboard- Validating the Do Bi-Weekly Blancing Flow
Suite Setup                 Set up environment variables               
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Dashboard_Actions/Do_Bi_Weekly_Balancing.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot


*** Variables ***


*** Keywords ***
The user validates the Do Bi-Weekly Blancing Flow on Branch dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page

    And the user navigates to the ATM Control Dashboard  

    And the user selects a branch to access the Branch Dashboard 

    Then The User Checks And Actions Bi-Weekly Balancing If It Exists

     
| *Test Cases*                                                                                                                                   |      *DOCUMENTATION*                          | *TEST_ENVIRONMENT*   |
|  MQ_TC_6_Branch Dashboard_validate_Do_Bi_Weekly_Balancing_feature   | The user validates the Do Bi-Weekly Blancing Flow on Branch dashboard    |    Validating the Do Bi-Weekly Blancing Flow  |    MTGLA_UAT         | 