*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource            ../../../../keywords/front_end/Landing_Page.robot
Resource            ../../../../keywords/front_end/Edit_or_Delete_BinTypes_Page.robot
Resource            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource             ../../../../../common_utilities/Login.robot
Resource             ../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Edit Bin Types displayed on the Edit/Delete page
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}       ${NEW_BIN_TYPE_NAME}     ${NEW_BIN_TYPE_DESC}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bin Type Menu
    And The user navigates to 'Edit/Delete' Bin Type tab
    And The user edits a Bin Type                                          ${CURR_BIN_TYPE_NAME}     ${CURR_BIN_TYPE_DESC}       ${NEW_BIN_TYPE_NAME}     ${NEW_BIN_TYPE_DESC}      Negative
    Then The error message must be displayed

| *** Test Cases ***                                                                                                            |        *DOCUMENTATION*    		                  |         *BASE_URL*                  |         *CURR_BIN_TYPE_NAME*             |         *CURR_BIN_TYPE_DESC*             |         *NEW_BIN_TYPE_NAME*        |         *NEW_BIN_TYPE_DESC*             |
| Admin_Cancel Edit BINtype and Return to Previous Screen                  | Edit Bin Types displayed on the Edit/Delete page   | Edit Bin Types displayed on the Edit/Delete page.   |           ${EMPTY}                  |           Token                          |           ${EMPTY}                       |           Domestic                 |     ${EMPTY}                            |
