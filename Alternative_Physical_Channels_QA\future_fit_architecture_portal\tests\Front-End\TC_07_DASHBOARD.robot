*** Settings ***
#Author Name               : Siviwe
#Email Address             : <EMAIL>

Default Tags                                        FFA_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation  Testing Future Fit APC Portal ATM Marketing Dashboard

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../Keywords/atm_marketing/Dashboard.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***

*** Keywords ***
Validates Calendar View Page
     
    [Arguments]  ${DOCUMENTATION}     ${TEST_ENVIRONMENT}     ${LOGON_USER}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture portal   ${TEST_ENVIRONMENT}  Chrome  drivers\chromedriver.exe  ${LOGON_USER}
    When The user navigates to the dashboard
    And Reads the dashboard details for current schedule version and active campaigns displayed
    And Reads the database details for current schedule version and active campaigns displayed
    Then The Database details must be the same as Front End details for schedule version and total campaigns

| *** Test Cases ***                                                                            |               *DOCUMENTATION*                           |       *TEST_ENVIRONMENT*                |      *LOGON_USER*         |
| FFT - Dashboard - validate dashboard screen   | Validates Calendar View Page           |  Testing Future Fit APC Portal ATM Marketing Dashboard  |        APC_UAT                          |   BUSINESS_USER           |