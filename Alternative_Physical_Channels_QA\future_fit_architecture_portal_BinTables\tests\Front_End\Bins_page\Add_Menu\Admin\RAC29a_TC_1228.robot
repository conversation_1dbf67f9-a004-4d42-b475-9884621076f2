*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../../common_utilities/Login.robot
Resource                                            ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Add Bin Type




*** Keywords ***
Verify the Cancel action on Add Bin flow
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     ${BIN_NAME}    ${BIN_TYPE_NAME}    ${BIN_ACTION_DATE}
    Set Test Documentation  ${DOCUMENTATION}


    IF    '${BIN_NAME}' == '${EMPTY}' or '${BIN_NAME}' == ''
         ${random_word}=     Generate random bin name
         ${BIN_NAME}=   Set Variable     ${random_word}
         ${BIN_NAME}=    Get Substring    ${BIN_NAME}    0    12
    END

    Given The user logs into Future Fit Architecture - Bin Tables portal                ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Add' Bin tab
    And The User populates the Bin details and save the Bin to local storage    ${BIN_NAME}    ${BIN_TYPE_NAME}     ${BIN_ACTION_DATE}
    And The created bins are added to the local storage                         ${BIN_NAME}    ${BIN_TYPE_NAME}     ${BIN_ACTION_DATE}
    And The user clicks the Save & Exit to save the created bin(s) to the database
    And The user clicks the 'Cancel' button to cancel the Add flow
    Then The user confirms that the Bin was not added 


| *** Test Cases ***                                                                   |        *DOCUMENTATION*             |         *BASE_URL*                  |         *BIN_NAME*          |         *BIN_TYPE_NAME*        |         *BIN_ACTION_DATE*        |
| Admin_Cancel Action on Adding New BIN   | Verify the Cancel action on Add Bin flow   | Testing the Cancel Action on Add   |           ${EMPTY}                  |            ${EMPTY}         |         Domestic               |         06-28-2027               |
