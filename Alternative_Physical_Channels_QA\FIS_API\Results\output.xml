<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.1 (Python 3.8.2 on win32)" generated="2024-10-16T19:52:22.283086" rpa="false" schemaversion="5">
<suite id="s1" name="TC FIS Test Case Execution" source="C:\Users\<USER>\OneDrive - Absa\My Documents\project\Automation\Test FIS API's\TestCases\TC_FIS_Test_Case_Execution.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariables" type="SETUP">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-16T19:52:22.973536" level="INFO">Environment variable 'DEVICE_NAME' set to value 'Wincor-285'.</msg>
<arg>DEVICE_NAME</arg>
<arg>${DEVICE_NAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2024-10-16T19:52:22.973536" level="INFO">Environment variable 'TEST_EXECUTION_NAME' set to value 'APITestingSuite'.</msg>
<arg>TEST_EXECUTION_NAME</arg>
<arg>${TEST_EXECUTION_NAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<test id="s1-t1" name="Execute Test case and check for the status" line="37">
<kw name="Execute Test case and check for the status">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2024-10-16T19:52:22.973536" level="INFO">Set test documentation to:
This test case execute and validates the results</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<kw name="Given Test File Exists" owner="keywords">
<kw name="File Exists" owner="csvLibrary">
<msg time="2024-10-16T19:52:22.973536" level="INFO">${fileExists} = False</msg>
<var>${fileExists}</var>
<arg>../Data/data.csv</arg>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${fileExists} is True</arg>
<arg>Log To Console</arg>
<arg>Exists!</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.000000"/>
</kw>
<kw name="And Do a post Request for FIS Auth and validate the response code and response body" owner="keywords">
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:22.973536" level="INFO">${BASE_URL} = http://*************/api/atm/v1.0/auth</msg>
<var>${BASE_URL}</var>
<arg>${DATA_FILE}</arg>
<arg>1</arg>
<arg>1</arg>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.015643"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:22.993011" level="INFO">${AUTH} = {'username': 'mbalentle', 'password': 'Mbali@2023'}</msg>
<var>${AUTH}</var>
<arg>${DATA_FILE}</arg>
<arg>${ROW}</arg>
<arg>${COLUMN}</arg>
<status status="PASS" start="2024-10-16T19:52:22.990459" elapsed="0.002552"/>
</kw>
<kw name="Create Session" owner="RequestsLibrary">
<msg time="2024-10-16T19:52:22.993011" level="INFO">Creating Session using : alias=mysession, url=http://*************/api/atm/v1.0/auth, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<arg>mysession</arg>
<arg>${BASE_URL}</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<status status="PASS" start="2024-10-16T19:52:22.993011" elapsed="0.000000"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:22.995871" level="INFO">${PAYLOAD} = {'username': 'mbalentle', 'password': 'Mbali@2023'}</msg>
<var>${PAYLOAD}</var>
<arg>${DATA_FILE}</arg>
<arg>${ROW}</arg>
<arg>${COLUMN}</arg>
<status status="PASS" start="2024-10-16T19:52:22.995871" elapsed="0.000000"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:22.995871" level="INFO">${END_URL} = http://*************/api/atm/v1.0/auth</msg>
<var>${END_URL}</var>
<arg>${DATA_FILE}</arg>
<arg>1</arg>
<arg>${URL}</arg>
<status status="PASS" start="2024-10-16T19:52:22.995871" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-16T19:52:22.995871" level="INFO">${HEADERS} = {'accept': 'text/plain', 'Content-Type': 'application/json-patch+json'}</msg>
<var>${HEADERS}</var>
<arg>accept=text/plain</arg>
<arg>Content-Type=application/json-patch+json</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-16T19:52:22.995871" elapsed="0.000000"/>
</kw>
<kw name="POST On Session" owner="RequestsLibrary">
<msg time="2024-10-16T19:52:23.100266" level="INFO">POST Request : url=http://*************/api/atm/v1.0/auth/ 
 path_url=/api/atm/v1.0/auth/ 
 headers={'User-Agent': 'python-requests/2.28.2', 'Accept-Encoding': 'gzip, deflate', 'accept': 'text/plain', 'Connection': 'keep-alive', 'Content-Type': 'application/json-patch+json', 'Content-Length': '51'} 
 body={'username': 'mbalentle', 'password': 'Mbali@2023'} 
 </msg>
<msg time="2024-10-16T19:52:23.100266" level="INFO">POST Response : url=http://*************/api/atm/v1.0/auth/ 
 status=200, reason=OK 
 headers={'Transfer-Encoding': 'chunked', 'Content-Type': 'application/json; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'Date': 'Wed, 16 Oct 2024 17:51:23 GMT'} 
 body={"token_type":"Bearer","access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcInB3ZFwiXSIsImV4cCI6MTcyOTEwMTk4MywiaXNzIjoiaHR0cDovL015QXRtVGVzdExhYldlYlNlcnZlci8iLCJhdWQiOiJodHRwOi8vTXlBdG1UZXN0TGFiV2ViU2VydmVyLyJ9.RhIOv6gD4PSBAQK5B3CRMDa1YxyKwY-UEoTnB1fHuIU","expires_in":900,"client_id":"208EB8FE4F0449E690ED0F87A5F04373"} 
 </msg>
<msg time="2024-10-16T19:52:23.115939" level="INFO">${RESPONSE} = &lt;Response [200]&gt;</msg>
<var>${RESPONSE}</var>
<arg>mysession</arg>
<arg>/</arg>
<arg>data=${PAYLOAD}</arg>
<arg>headers=${HEADERS}</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<status status="PASS" start="2024-10-16T19:52:22.995871" elapsed="0.120068"/>
</kw>
<kw name="Status Should Be" owner="RequestsLibrary">
<arg>200</arg>
<arg>${RESPONSE}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" start="2024-10-16T19:52:23.116576" elapsed="0.000468"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${json} = {'token_type': 'Bearer', 'access_token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcI...</msg>
<var>${json}</var>
<arg>${RESPONSE.json()}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117044" elapsed="0.000521"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${Access_Token} = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcInB3ZFwiXSIsImV4cCI6MTcyOTEwMTk4MywiaXNzIjo...</msg>
<var>${Access_Token}</var>
<arg>${json['access_token']}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${Access_Token} = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcInB3ZFwiXSIsImV4cCI6MTcyOTEwMTk4MywiaXNzIjo...</msg>
<arg>${Access_Token}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<arg>${ROW}</arg>
<arg>${COLUMN}</arg>
<arg>${AUTH_URL}</arg>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="0.144029"/>
</kw>
<kw name="And Do a post Request for FIS execution and validate the response code and response body" owner="keywords">
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${BASE_URL} = http://*************/api/atm/v1.0/simulators</msg>
<var>${BASE_URL}</var>
<arg>${DATA_FILE}</arg>
<arg>1</arg>
<arg>0</arg>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Create Session" owner="RequestsLibrary">
<msg time="2024-10-16T19:52:23.117565" level="INFO">Creating Session using : alias=mysession, url=http://*************/api/atm/v1.0/simulators, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<arg>mysession</arg>
<arg>${BASE_URL}</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${PAYLOAD} = {'name': 'Balance-Device-(Onus)-wincor'}</msg>
<var>${PAYLOAD}</var>
<arg>${DATA_FILE}</arg>
<arg>${ROW}</arg>
<arg>${COLUMN}</arg>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${END_URL} = /execution/test-suite</msg>
<var>${END_URL}</var>
<arg>${DATA_FILE}</arg>
<arg>1</arg>
<arg>${URL}</arg>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${Device} = Wincor-285</msg>
<var>${Device}</var>
<arg>DEVICE_NAME</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${Device} = Wincor-285</msg>
<arg>${Device}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${TEST_EXECUTION_NAME} = APITestingSuite</msg>
<var>${TEST_EXECUTION_NAME}</var>
<arg>TEST_EXECUTION_NAME</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${END_URL} = Wincor-285 /execution/test-suite</msg>
<var>${END_URL}</var>
<arg>${Device}</arg>
<arg>${END_URL}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${body_1} = {'name': 'APITestingSuite'}</msg>
<var>${body_1}</var>
<arg>name=${TEST_EXECUTION_NAME}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.117565" level="INFO">${headers} = {'accept': 'text/plain', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtc...</msg>
<var>${headers}</var>
<arg>accept=text/plain</arg>
<arg>Authorization=Bearer ${Access_Token}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.000000"/>
</kw>
<kw name="POST On Session" owner="RequestsLibrary">
<msg time="2024-10-16T19:52:23.254732" level="INFO">POST Request : url=http://*************/api/atm/v1.0/simulators/Wincor-285%20/execution/test-suite 
 path_url=/api/atm/v1.0/simulators/Wincor-285%20/execution/test-suite 
 headers={'User-Agent': 'python-requests/2.28.2', 'Accept-Encoding': 'gzip, deflate', 'accept': 'text/plain', 'Connection': 'keep-alive', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcInB3ZFwiXSIsImV4cCI6MTcyOTEwMTk4MywiaXNzIjoiaHR0cDovL015QXRtVGVzdExhYldlYlNlcnZlci8iLCJhdWQiOiJodHRwOi8vTXlBdG1UZXN0TGFiV2ViU2VydmVyLyJ9.RhIOv6gD4PSBAQK5B3CRMDa1YxyKwY-UEoTnB1fHuIU', 'Content-Length': '27', 'Content-Type': 'application/json'} 
 body=b'{"name": "APITestingSuite"}' 
 </msg>
<msg time="2024-10-16T19:52:23.254732" level="INFO">POST Response : url=http://*************/api/atm/v1.0/simulators/Wincor-285%20/execution/test-suite 
 status=201, reason=Created 
 headers={'Content-Length': '2314', 'Content-Type': 'application/json; charset=utf-8', 'Location': 'http://*************/api/atm/v1.0/simulators/Wincor-285 /execution/jobs/e466eff0-64f1-46a3-9cd8-d0a4a54222f8', 'Server': 'Microsoft-IIS/10.0', 'Date': 'Wed, 16 Oct 2024 17:51:23 GMT'} 
 body={"type":"StandardExecutionJob","transactions":[{"type":"TransactionJob","testCaseUuid":"a6d600cb-64e7-4f02-bab6-018aad83f3ec","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"No-receipt","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182258,"uuid":"3f2a1f15-532e-4e97-9a49-56545d1c5748","name":"Balance-Device-(Onus)-wincor","status":"Queued","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593564Z","startedAt":null,"completedAt":null,"startedBy":"Mbalentle"},{"type":"TransactionJob","testCaseUuid":"8f4d231e-646e-4e09-8747-018a88a4591b","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"No-receipt","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182259,"uuid":"2f7601eb-90c9-4bd1-812b-70cebc7458fb","name":"Ministatement-Device-(Onus)-NoReceipt-Wincor","status":"Queued","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593586Z","startedAt":null,"completedAt":null,"startedBy":"Mbalentle"},{"type":"TransactionJob","testCaseUuid":"42516108-a1b7-4738-8e72-018c1f01fbcc","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"Transaction-1","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182260,"uuid":"2fd5d304-0a65-4bcd-9615-872e934bb430","name":"Withdrawals-Device-(Onus)NoReceipt - wincor","status":"Queued","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593593Z","startedAt":null,"completedAt":null,"startedBy":"Mbalentle"},{"type":"TransactionJob","testCaseUuid":"42516108-a1b7-4738-8e72-018c1f01fbcc","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"Transaction-2","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182261,"uuid":"11c30334-9a7b-4aad-9348-2e45b4ad8579","name":"Withdrawals-Device-(Onus)NoReceipt - wincor","status":"Queued","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593598Z","startedAt":null,"completedAt":null,"startedBy":"Mbalentle"}],"comment":null,"runtimeProperties":null,"postExecutionActions":[],"subJobsCount":4,"subJobsProcessed":0,"currentSubJobUuid":null,"currentSubJobName":null,"simulatorRunId":13451,"uuid":"e466eff0-64f1-46a3-9cd8-d0a4a54222f8","name":"APITestingSuite","status":"InProgress","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593525Z","startedAt":"2024-10-16T17:51:23.293236Z","completedAt":null,"startedBy":"Mbalentle"} 
 </msg>
<msg time="2024-10-16T19:52:23.254732" level="INFO">${response} = &lt;Response [201]&gt;</msg>
<var>${response}</var>
<arg>mysession</arg>
<arg>${END_URL}</arg>
<arg>json=${body_1}</arg>
<arg>headers=${headers}</arg>
<doc>Sends a POST request on a previously created HTTP Session.</doc>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.137167"/>
</kw>
<kw name="Status Should Be" owner="RequestsLibrary">
<arg>201</arg>
<arg>${response}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" start="2024-10-16T19:52:23.254732" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-16T19:52:23.254732" level="INFO">Length is 2314.</msg>
<arg>${response.content}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-16T19:52:23.254732" elapsed="0.000000"/>
</kw>
<arg>${ROW}</arg>
<arg>${EXECUTION_PAYLOAD}</arg>
<arg>${EXECUTION_URL}</arg>
<arg>${get_name}</arg>
<status status="PASS" start="2024-10-16T19:52:23.117565" elapsed="0.137167"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2024-10-16T19:53:03.262136" level="INFO">Slept 40 seconds.</msg>
<arg>40s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2024-10-16T19:52:23.254732" elapsed="40.007404"/>
</kw>
<kw name="And Do a GET Request for the results and validate the response code and response body" owner="keywords">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-16T19:53:03.262136" level="INFO">${results} = Pass</msg>
<var>${results}</var>
<arg>Pass</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2024-10-16T19:53:03.262136" level="INFO">${status} = Completed</msg>
<var>${status}</var>
<arg>Completed</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.000000"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:53:03.262136" level="INFO">${base_url} = http://*************/api/atm/v1.0/simulators</msg>
<var>${base_url}</var>
<arg>${DATA_FILE}</arg>
<arg>1</arg>
<arg>0</arg>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.000000"/>
</kw>
<kw name="Go To Nth Row Nth Column" owner="csvLibrary">
<msg time="2024-10-16T19:53:03.262136" level="INFO">${END_URL} = /execution/jobs/current</msg>
<var>${END_URL}</var>
<arg>${DATA_FILE}</arg>
<arg>${ROW}</arg>
<arg>${END_URL}</arg>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.000000"/>
</kw>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-16T19:53:03.262136" level="INFO">${END_URL} = Wincor-285 /execution/jobs/current</msg>
<var>${END_URL}</var>
<arg>${Device}</arg>
<arg>${END_URL}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.000000"/>
</kw>
<kw name="Create Session" owner="RequestsLibrary">
<msg time="2024-10-16T19:53:03.262136" level="INFO">Creating Session using : alias=mysession, url=http://*************/api/atm/v1.0/simulators, headers={},                     cookies={}, auth=None, timeout=None, proxies=None, verify=False,                     debug=0 </msg>
<arg>mysession</arg>
<arg>${base_url}</arg>
<doc>Create Session: create a HTTP session to a server</doc>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2024-10-16T19:53:03.277683" level="INFO">${header_status} = {'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcInB3ZFwiXSIsImV4c...</msg>
<var>${header_status}</var>
<arg>Authorization=Bearer ${Access_Token}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2024-10-16T19:53:03.277683" elapsed="0.000000"/>
</kw>
<kw name="GET On Session" owner="RequestsLibrary">
<msg time="2024-10-16T19:53:03.372801" level="INFO">GET Request : url=http://*************/api/atm/v1.0/simulators/Wincor-285%20/execution/jobs/current 
 path_url=/api/atm/v1.0/simulators/Wincor-285%20/execution/jobs/current 
 headers={'User-Agent': 'python-requests/2.28.2', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive', 'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI3IiwiZ2l2ZW5fbmFtZSI6Ik1iYWxlbnRsZSIsImp0aSI6IjQzY2FiZTkyLWVhODAtNGZhMy05ZjZlLWQyYWNjOTc4ZGJkZSIsImFtciI6IltcInB3ZFwiXSIsImV4cCI6MTcyOTEwMTk4MywiaXNzIjoiaHR0cDovL015QXRtVGVzdExhYldlYlNlcnZlci8iLCJhdWQiOiJodHRwOi8vTXlBdG1UZXN0TGFiV2ViU2VydmVyLyJ9.RhIOv6gD4PSBAQK5B3CRMDa1YxyKwY-UEoTnB1fHuIU'} 
 body=None 
 </msg>
<msg time="2024-10-16T19:53:03.372801" level="INFO">GET Response : url=http://*************/api/atm/v1.0/simulators/Wincor-285%20/execution/jobs/current 
 status=200, reason=OK 
 headers={'Transfer-Encoding': 'chunked', 'Content-Type': 'application/json; charset=utf-8', 'Content-Encoding': 'gzip', 'Vary': 'Accept-Encoding', 'Server': 'Microsoft-IIS/10.0', 'Date': 'Wed, 16 Oct 2024 17:52:03 GMT'} 
 body={"type":"StandardExecutionJob","transactions":[{"type":"TransactionJob","testCaseUuid":"a6d600cb-64e7-4f02-bab6-018aad83f3ec","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"No-receipt","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182258,"uuid":"3f2a1f15-532e-4e97-9a49-56545d1c5748","name":"Balance-Device-(Onus)-wincor","status":"Completed","verdict":"Pass","queuedAt":"2024-10-16T17:51:23.2593564Z","startedAt":"2024-10-16T17:51:23.3140359Z","completedAt":"2024-10-16T17:51:57.7573808Z","startedBy":"Mbalentle"},{"type":"TransactionJob","testCaseUuid":"8f4d231e-646e-4e09-8747-018a88a4591b","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"No-receipt","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182259,"uuid":"2f7601eb-90c9-4bd1-812b-70cebc7458fb","name":"Ministatement-Device-(Onus)-NoReceipt-Wincor","status":"InProgress","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593586Z","startedAt":"2024-10-16T17:51:57.7574961Z","completedAt":null,"startedBy":"Mbalentle"},{"type":"TransactionJob","testCaseUuid":"42516108-a1b7-4738-8e72-018c1f01fbcc","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"Transaction-1","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182260,"uuid":"2fd5d304-0a65-4bcd-9615-872e934bb430","name":"Withdrawals-Device-(Onus)NoReceipt - wincor","status":"Queued","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593593Z","startedAt":null,"completedAt":null,"startedBy":"Mbalentle"},{"type":"TransactionJob","testCaseUuid":"42516108-a1b7-4738-8e72-018c1f01fbcc","testSuiteUuid":"b47f54da-8069-4cef-9796-01921e2e0b99","recordId":"Transaction-2","testPlanRunId":null,"testPlanRunTransactionId":null,"traceId":182261,"uuid":"11c30334-9a7b-4aad-9348-2e45b4ad8579","name":"Withdrawals-Device-(Onus)NoReceipt - wincor","status":"Queued","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593598Z","startedAt":null,"completedAt":null,"startedBy":"Mbalentle"}],"comment":null,"runtimeProperties":null,"postExecutionActions":[],"subJobsCount":4,"subJobsProcessed":1,"currentSubJobUuid":"2f7601eb-90c9-4bd1-812b-70cebc7458fb","currentSubJobName":"Ministatement-Device-(Onus)-NoReceipt-Wincor","simulatorRunId":13451,"uuid":"e466eff0-64f1-46a3-9cd8-d0a4a54222f8","name":"APITestingSuite","status":"InProgress","verdict":"None","queuedAt":"2024-10-16T17:51:23.2593525Z","startedAt":"2024-10-16T17:51:23.293236Z","completedAt":null,"startedBy":"Mbalentle"} 
 </msg>
<msg time="2024-10-16T19:53:03.376310" level="INFO">${response} = &lt;Response [200]&gt;</msg>
<var>${response}</var>
<arg>mysession</arg>
<arg>${END_URL}</arg>
<arg>headers=${header_status}</arg>
<doc>Sends a GET request on a previously created HTTP Session.</doc>
<status status="PASS" start="2024-10-16T19:53:03.277683" elapsed="0.098627"/>
</kw>
<kw name="Status Should Be" owner="RequestsLibrary">
<arg>200</arg>
<arg>${response}</arg>
<doc>Fails if response status code is different than the expected.</doc>
<status status="PASS" start="2024-10-16T19:53:03.376310" elapsed="0.000000"/>
</kw>
<kw name="Should Not Be Empty" owner="BuiltIn">
<msg time="2024-10-16T19:53:03.376310" level="INFO">Length is 2475.</msg>
<arg>${response.content}</arg>
<doc>Verifies that the given item is not empty.</doc>
<status status="PASS" start="2024-10-16T19:53:03.376310" elapsed="0.000000"/>
</kw>
<kw name="Get Value From Json" owner="JSONLibrary">
<msg time="2024-10-16T19:53:03.499883" level="INFO">${transactions} = [[{'type': 'TransactionJob', 'testCaseUuid': 'a6d600cb-64e7-4f02-bab6-018aad83f3ec', 'testSuiteUuid': 'b47f54da-8069-4cef-9796-01921e2e0b99', 'recordId': 'No-receipt', 'testPlanRunId': None, 'testPlan...</msg>
<var>${transactions}</var>
<arg>${response.json()}</arg>
<arg>transactions</arg>
<doc>Get Value From JSON using JSONPath</doc>
<status status="PASS" start="2024-10-16T19:53:03.376310" elapsed="0.123573"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2024-10-16T19:53:03.499883" level="INFO">${transactionsList} = [{'type': 'TransactionJob', 'testCaseUuid': 'a6d600cb-64e7-4f02-bab6-018aad83f3ec', 'testSuiteUuid': 'b47f54da-8069-4cef-9796-01921e2e0b99', 'recordId': 'No-receipt', 'testPlanRunId': None, 'testPlanR...</msg>
<var>${transactionsList}</var>
<arg>${transactions}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2024-10-16T19:53:03.499883" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>${transactionsList[0]["verdict"]}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-16T19:53:03.499883" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>${transactionsList[0]["status"]}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-16T19:53:03.499883" elapsed="0.000000"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${transactionsList[0]["verdict"]}</arg>
<arg>${results}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2024-10-16T19:53:03.499883" elapsed="0.015663"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${transactionsList[0]["status"]}</arg>
<arg>${status}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2024-10-16T19:53:03.515546" elapsed="0.001099"/>
</kw>
<arg>${ROW}</arg>
<arg>${RESULTS_URL}</arg>
<status status="PASS" start="2024-10-16T19:53:03.262136" elapsed="0.254509"/>
</kw>
<arg>This test case execute and validates the results</arg>
<arg>1</arg>
<arg>4</arg>
<arg>1</arg>
<arg>2</arg>
<arg>3</arg>
<arg>5</arg>
<arg>8</arg>
<arg>6</arg>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="40.543109"/>
</kw>
<doc>This test case execute and validates the results</doc>
<status status="PASS" start="2024-10-16T19:52:22.973536" elapsed="40.543109"/>
</test>
<doc>This test case execute and validates the results</doc>
<status status="PASS" start="2024-10-16T19:52:22.283086" elapsed="41.237090"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="TC FIS Test Case Execution">TC FIS Test Case Execution</stat>
</suite>
</statistics>
<errors>
<msg time="2024-10-16T19:52:22.267473" level="WARN">Error in file 'C:\Users\<USER>\OneDrive - Absa\My Documents\project\Automation\Test FIS API's\TestCases\TC_FIS_Test_Case_Execution.robot' on line 21: Singular section headers like '*** Keyword ***' are deprecated. Use plural format like '*** Keywords ***' instead.</msg>
<msg time="2024-10-16T19:52:22.267473" level="WARN">Error in file 'C:\Users\<USER>\OneDrive - Absa\My Documents\project\Automation\Test FIS API's\TestCases\TC_FIS_Test_Case_Execution.robot' on line 36: Singular section headers like '*Test Case*' are deprecated. Use plural format like '*** Test Cases ***' instead.</msg>
</errors>
</robot>
