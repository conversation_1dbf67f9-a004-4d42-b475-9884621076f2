*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource            ../../../keywords/controllers/Confirm_BinTable_Download_Keywords.robot
Resource            ../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Execute the 'Confirm Bin Tables Download' and verify that the database is updated
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}   ${EXPECTED_STATUS_CODE}  ${DEVICE_NAME}  ${IS_SUCCESSFUL_HAN<PERSON>HAKE}   ${IS_SUCCESSFUL_DOWNLOAD}   ${DOWNLOAD_ERROR}   &{HANDSHAKE_ERROR}
    Set Test Documentation  ${DOCUMENTATION}

    ${is_successful_download_scenario}=       Get From Dictionary    ${HANDSHAKE_ERROR}    error     default=Key not found

    IF    '${is_successful_download_scenario}' != 'Key not found'
        ${HANDSHAKE_ERROR}=    Set Variable   ${None}
    END

    Given The User Populates the Confirm Bin Table Download JSON payload                                        ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}
    When The User sends a POST Request to confirm the download of bin table                                     ${BASE_URL}
    And The service returns the expected status code                                                            ${EXPECTED_STATUS_CODE}
    Then The DeviceVersions database table must save the correct details posted by the ConfirmDownload API      ${DEVICE_NAME}     ${IS_SUCCESSFUL_HANDSHAKE}    ${IS_SUCCESSFUL_DOWNLOAD}       ${DOWNLOAD_ERROR}       ${HANDSHAKE_ERROR}


| *** Test Cases ***                                                                                                                                                                                                   |        *DOCUMENTATION*    		          |         *BASE_URL*                  |  *EXPECTED_STATUS_CODE*  |     *DEVICE_NAME*      | *IS_SUCCESSFUL_HANDSHAKE*   |   *IS_SUCCESSFUL_DOWNLOAD* |   *DOWNLOAD_ERROR*                           |   *HANDSHAKE_ERROR*                                                                                                     |
#| Send Confirm Bin Table download request were Handshake is not established.                                                    | Execute the 'Confirm Bin Tables Download' and verify that the database is updated    | Verify the ConfirmDownload controller.   |                                     |          200             |         S08397         |          ${False}           |        ${False}            |           ${None}                            |  type=network connection  |  title=network connection  |  status=500  | detail=Thabo unit test                          |
#| Send Confirm Bin Table download request were Handshake is is established but error happen during the download process.        | Execute the 'Confirm Bin Tables Download' and verify that the database is updated    | Verify the ConfirmDownload controller.   |                                     |          200             |         S08397         |          ${True}            |        ${False}            |   Could not back up old bintable file        |  error=None               |
| Send Confirm Bin Table download request the download is successful.                                                           | Execute the 'Confirm Bin Tables Download' and verify that the database is updated    | Verify the ConfirmDownload controller.   |                                     |          200             |         S08397         |          ${True}            |        ${True}            |           ${None}                            |  error=None               |