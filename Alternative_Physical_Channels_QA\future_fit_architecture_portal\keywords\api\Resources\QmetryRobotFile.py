from xml.dom import minidom

from datetime import datetime


from robot.api.deco import keyword, library


@library(scope='GLOBAL', auto_keywords=True)
class QmetryRobotFile:
    @keyword
    def generate_robot_xml(self, file_name):

        suite_name = 'Future Fit API test run'
        suite_id = 'RAC29a-TS-20'

        test_name = 'Unit Test'
        test_id = 's1-t1'
        test_doc = 'Test Documentation'

        kw_library = 'SeleniumLibrary'
        kw_name = 'The user logs into Future Fit Architecture portal'
        test_step_info = "Opening browser 'chrome' to base url 'https://www.qmetry.com/'."

        # Get current Datetime
        dt = datetime.now()
        print('Current Time:', dt)

        current_date = dt.strftime('%Y%m%d %H:%M:%S.%f')[:-3]

        root = minidom.Document()

        robot = root.createElement('robot')
        robot.setAttribute('rpa', 'false')
        robot.setAttribute('generator', 'Robot 3.2.1 (Python 3.5.4 on win32)')
        robot.setAttribute('generated', current_date)

        suite = root.createElement('suite')
        suite.setAttribute('source',"C:Users/AB032to/source/repos/future_fit_repo/tests/Front-End/TC_03_CAPTURE_CAMPAIGN.robot")
        suite.setAttribute('name', suite_name)
        suite.setAttribute('id', suite_id)

        test = root.createElement('test')
        test.setAttribute('name', test_name)
        test.setAttribute('id', test_id)

        # Elements for Test steps
        kw = root.createElement('kw')
        kw.setAttribute('library', kw_library)
        kw.setAttribute('name', kw_name)
        doc = root.createElement('doc')

        doc.appendChild(root.createTextNode(test_doc))
        kw.appendChild(doc)

        arguments = root.createElement('arguments')
        arg = root.createElement('arg')
        arg.appendChild(root.createTextNode('https://www.qmetry.com/'))
        arguments.appendChild(arg)

        arg = root.createElement('arg')
        arg.appendChild(root.createTextNode('chrome'))
        arguments.appendChild(arg)
        kw.appendChild(arguments)

        msg = root.createElement('msg')
        msg.setAttribute('timestamp', current_date)
        msg.setAttribute('level', 'INFO')
        msg.appendChild(root.createTextNode(test_step_info))
        kw.appendChild(msg)

        status = root.createElement('status')
        status.setAttribute('endtime', current_date)
        status.setAttribute('status', 'PASS')
        status.setAttribute('starttime', current_date)
        kw.appendChild(status)  # Step status

        # Add test step to Test Case
        test.appendChild(kw)

        # Elements for Test tags and status
        tags = root.createElement('tags')
        tag = root.createElement('tag')
        tag.appendChild(root.createTextNode(test_name))

        tags.appendChild(tag)
        test.appendChild(tags)

        status = root.createElement('status')
        status.setAttribute('endtime', current_date)
        status.setAttribute('critical', 'yes')
        status.setAttribute('status', 'PASS')
        status.setAttribute('starttime', current_date)
        test.appendChild(status)
        suite.appendChild(test)

        # Add test Case to the Suite
        status = root.createElement('status')
        status.setAttribute('endtime', current_date)
        status.setAttribute('status', 'PASS')
        status.setAttribute('starttime', current_date)
        suite.appendChild(status)

        robot.appendChild(suite)

        statistics = root.createElement('statistics')

        pass_count = '2'
        fail_count = '0'
        stat_1_tag = 'Critical Tests'
        stat_2_tag = 'All Tests'

        total = root.createElement('total')
        stat = root.createElement('stat')
        stat.setAttribute('pass', pass_count)
        stat.setAttribute('fail', fail_count)
        stat.appendChild(root.createTextNode(stat_1_tag))
        total.appendChild(stat)

        stat = root.createElement('stat')
        stat.setAttribute('pass', pass_count)
        stat.setAttribute('fail', fail_count)
        stat.appendChild(root.createTextNode(stat_2_tag))
        total.appendChild(stat)
        statistics.appendChild(total)

        tag = root.createElement('tag')
        stat = root.createElement('stat')
        stat.setAttribute('pass', pass_count)
        stat.setAttribute('fail', fail_count)
        stat.appendChild(root.createTextNode('TC1'))
        tag.appendChild(stat)

        stat = root.createElement('stat')
        stat.setAttribute('pass', pass_count)
        stat.setAttribute('fail', fail_count)
        stat.appendChild(root.createTextNode('TC2'))
        tag.appendChild(stat)
        statistics.appendChild(tag)

        suite = root.createElement('suite')
        stat = root.createElement('stat')
        stat.setAttribute('name', suite_name)
        stat.setAttribute('pass', pass_count)
        stat.setAttribute('fail', fail_count)
        stat.setAttribute('id', suite_id)
        stat.appendChild(root.createTextNode(suite_name))
        suite.appendChild(stat)
        statistics.appendChild(suite)
        robot.appendChild(statistics)

        errors = root.createElement('errors')
        robot.appendChild(errors)

        root.appendChild(robot)

        xml_str = root.toprettyxml(indent="   ", encoding="utf-8")

        # print(xml_str)

        with open(file_name, "w") as f:
            f.write(xml_str.decode("utf-8"))
