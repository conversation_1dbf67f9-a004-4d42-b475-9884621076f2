*** Settings ***
#Author Name               : Yaash
#Email Address             : yaash.ramsa<PERSON><EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation                                       Bin Tables Front End Test Suite


Library                                             ../../../../../common_utilities/CommonUtils.py

#***********************************PROJECT RESOURCES***************************************

Resource                                            ../../../../keywords/front_end/Landing_Page.robot
Resource                                            ../../../../keywords/front_end/Add_Bins_Page.robot
Resource                                            ../../../../keywords/common/SetEnvironmentVariales.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../../common_utilities/Login.robot
Resource                                            ../../../../keywords/front_end/Bin_Table_Landing_Page.robot
Resource                                            ../../../../keywords/front_end/APC_Portal_Landing_Page.robot


*** Variables ***
${SUITE NAME}               APC Landing Page 
${TEST_CASE_ID}             RAC29a-TC-755




*** Keywords ***
Verify User Details are Aligned to the User's Access and Role
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}     
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture                ${BASE_URL}
    When The user is redirected to the APC Portal Landing page
    And The user retreives the information and role displayed on the top right corner on the front-end 
    And The user retrieves the role of the logged-in user from the backend (DevTools)
    Then The user verifies that the role displayed on the front end matches the assigned role retrieved from the backend system (devtools)


| *** Test Cases ***                                                                                                                                                             |        *DOCUMENTATION*       |         *BASE_URL*                  |         
| Capturer_Verify User Details are Aligned to the User's Access and Role        | Verify User Details are Aligned to the User's Access and Role   | User Role Details            |           ${EMPTY}                  |           
