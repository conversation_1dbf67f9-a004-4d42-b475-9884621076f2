*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Documentation  Negative test run- Capture campaign 

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             XML

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../keywords/common/Navigation.robot
Resource                                            ../common/Navigation.robot
Resource                                            ../../keywords/atm_marketing/FilloutCampaignTarget.robot
Resource                                            ../../keywords/atm_marketing/FilloutYourCampaign.robot
*** Variables ***
${ATM_Selection}=                                    xpath=//*[@id="mat-option-15"]/span
${Receiver_Device_Selection}                         xpath=
${Admin}                                             xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav/div/mat-nav-list/div/mat-expansion-panel/mat-expansion-panel-header




*** Keywords ***
BA can only have a view of approvals  
    Element Should Be Visible    xpath=//*[@id="mat-expansion-panel-header-1"]/span[1]

Campaign 2 years in the future 
    #Fill out campaign 
    Click Element    ${CAPTURE_CAMPAIGN_LINK}
    Wait Until page contains      Fill out Campaign Targeted
    Sleep    2s 
    Click Element     ${CAMPAIGN_TARGET_YES_RADIO_BUTTON}
    Sleep    1s 
    Click Element    ${ATM_RADIO_BUTTON}
    Sleep    1s
    Click Element     ${ATM_DROPDOWN}
    Sleep    1s 
    Click Element     ${ATM_Selection}
    Wait Until Element Is Enabled    ${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}
    Click Element     ${FILLOUT_YOUR_CAMPAIGN_NEXT_BUTTON}

    #Fill out your campaign 
    Sleep     2s
    Click Element     xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input
    input text         ${CAMPAIGN_NAME_INPUT}         Automation Test
    Click Element    ${RECEIVER_DEVICE_TYPE_DROPDOWN} 
    Click Element    ${Receiver_Device_Selection}



Validating BA user cannot capture a campaign
    Element Should Be Visible    ${Admin}

Validating that a BU user cannot approve campaigns
    Element Should Be Visible    ${CAPTURE_CAMPAIGN_LINK}
    
    
The user validates testcases
    [Arguments]  ${Test_cases} 
    Run Keyword If    '${Test_cases}' == "Validating BA user cannot capture a campaign"    Validating BA user cannot capture a campaign
    Run Keyword If    '${Test_cases}' == "Validating that a BU user cannot approve campaigns"    Validating that a BU user cannot approve campaigns
    Run Keyword If    '${Test_cases}' == "B-Approver not permitted to capture a campaign"    Validating BA user cannot capture a campaign
    
      
    
    

    


    