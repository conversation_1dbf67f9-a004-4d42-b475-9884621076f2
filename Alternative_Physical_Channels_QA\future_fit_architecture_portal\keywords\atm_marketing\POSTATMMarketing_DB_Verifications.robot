*** Settings ***
#Author Name               : <PERSON>habo
#Email Address             : <EMAIL>

Documentation  Rest webservice regression

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             OperatingSystem
Library                                             JSONLibrary
Library                                             String
Variables                                          ../../utility/SQLVariables.py
#***********************************PROJECT RESOURCES***************************************


Resource                                            ../../keywords/common/Database.robot
Resource                                            ../../keywords/common/GenericMethods.robot

#***********************************PROJECT VARIABLES***************************************

** Variables ***


*** Keywords ***
Verify GET ATMMarketing controller fields on the database
    [Arguments]     ${json_response}

    ${new_string}=   Convert To String    ${json_response}
    ${dict_value}=      Convert String to Dictionary    ${new_string}

    #Get the marketing_schedule_id
    ${marketing_schedule_id}=    Get From Dictionary    ${dict_value}    id
    #Get the cycle details
    ${cycle_details}=    Get From Dictionary    ${dict_value}    cycle
    #${cycle_details_dict_value}=      Convert String to Dictionary    ${cycle_details}
    ${cycle_id}=    Get From Dictionary    ${cycle_details}    id
    ${cycle_start}=    Get From Dictionary    ${cycle_details}    cycleStart
    ${cycle_start}=    Replace String  ${cycle_start}     T       ${SPACE}

    ${cycle_end}=    Get From Dictionary    ${cycle_details}    cycleEnd
    ${cycle_end}=    Replace String  ${cycle_end}     T       ${SPACE}

    ${cycle_interval}=    Get From Dictionary    ${cycle_details}    interval
    ${cycle_marketing_schedules}=    Get From Dictionary    ${cycle_details}    marketingSchedules


    #Get the current campaign 'scheduleVersion'

    ${scheduleVersion}=    Get From Dictionary    ${dict_value}    scheduleVersion
    #Get the 'isCurrentVersion'
    ${isCurrentVersion}=    Get From Dictionary    ${dict_value}    isCurrentVersion
     #Get the 'updatedBy'
    ${updatedBy}=    Get From Dictionary    ${dict_value}    updatedBy
     #Get the current campaign 'campaignEndDate'
    ${updateDate}=    Get From Dictionary    ${dict_value}    updateDate
    ${updateDate}=    Replace String  ${updateDate}     T       ${SPACE}
     #Get the current campaign 'isActive'
    ${updateDescription}=    Get From Dictionary    ${dict_value}    updateDescription

    #Verify the cycle and schedule details against the database
    ${data_base_campaign_schedule_and_cycle_details}=       Get the campaign schedule and cycle details from the database       ${scheduleVersion}
    ${db_marketing_schedule_id}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    marketineScheduleID
    Verify if values are equal     ${marketing_schedule_id}        ${db_marketing_schedule_id}

    ${db_cycle_id}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    cycleID
    Verify if values are equal     ${cycle_id}        ${db_cycle_id}

    ${db_cycle_start}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    cycleStart
    Verify if values are equal     ${cycle_start}        ${db_cycle_start}

    ${db_cycle_end}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    cycleEnd
    Verify if values are equal     ${cycle_end}        ${db_cycle_end}

    ${db_cycle_interval}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    interval
    Verify if values are equal     ${cycle_interval}        ${db_cycle_interval}

    ${db_scheduleVersion}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    scheduleVersion
    Verify if values are equal     ${scheduleVersion}        ${db_scheduleVersion}

    ${db_isCurrentVersion}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    isCurrentVersion

    IF  '${db_isCurrentVersion}' == '1'
        ${db_isCurrentVersion}        Set Variable    ${True}
    ELSE
        ${db_isCurrentVersion}        Set Variable    ${False}
    END
    Verify if values are equal     ${isCurrentVersion}        ${db_isCurrentVersion}


    ${db_updatedBy}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    updatedBy
    Verify if values are equal     ${updatedBy}        ${db_updatedBy}

    ${db_updateDate}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    updateDate
    Verify if values are equal     ${updateDate}        ${db_updateDate}

    ${db_updateDescription}=    Get From Dictionary    ${data_base_campaign_schedule_and_cycle_details}    updateDescription
    Verify if values are equal     ${updateDescription}        ${db_updateDescription}

     #Get the  'campaigns'
    ${campaigns}=    Get From Dictionary    ${dict_value}    campaigns


    FOR     ${dict}    IN    @{campaigns}
         ${campaign_id_value}=      Get From Dictionary    ${dict}    id
         ${campaignId}=      Get From Dictionary    ${dict}    campaignId
         ${campaignName}=      Get From Dictionary    ${dict}    campaignName
         ${campaignBy}=      Get From Dictionary    ${dict}    campaignBy

         ${lastUpdate}=      Get From Dictionary    ${dict}    lastUpdate
         ${lastUpdate_id}=      Get From Dictionary    ${lastUpdate}    id
         ${lastUpdate_campaign_id}=      Get From Dictionary    ${lastUpdate}    campaignId
         ${lastUpdate_updatedBy}=      Get From Dictionary    ${lastUpdate}    updatedBy
         ${lastUpdate_updatedDate}=      Get From Dictionary    ${lastUpdate}    updatedDate
         ${lastUpdate_updatedDate}=    Replace String  ${lastUpdate_updatedDate}     T       ${SPACE}
         ${lastUpdate_description}=      Get From Dictionary    ${lastUpdate}    updateDescription
         ${lastUpdate_approvalId}=      Get From Dictionary    ${lastUpdate}    approvalId
         ${campaign_updatedBy}=      Get From Dictionary    ${dict}    updatedBy

         ${screen_details}=      Get From Dictionary    ${dict}    screen
         ${screen_id}=      Get From Dictionary    ${screen_details}    id
         ${screen_type}=      Get From Dictionary    ${screen_details}    screenType
         ${screen_number}=      Get From Dictionary    ${screen_details}    screenNumber
         ${screen_channel_id}=      Get From Dictionary    ${screen_details}    channelId

         ${marketing_channel_details}=      Get From Dictionary    ${dict}    marketingChannel
         ${marketing_channel_id}=      Get From Dictionary    ${marketing_channel_details}    id
         ${marketing_channel}=      Get From Dictionary    ${marketing_channel_details}    channel
         ${marketing_channel_image_resolution}=      Get From Dictionary    ${marketing_channel_details}    imageResolution
         ${campaign_start_date}=      Get From Dictionary    ${dict}    campaignStartDate
         ${campaign_start_date}=    Replace String  ${campaign_start_date}     T       ${SPACE}
         ${campaign_end_date}=      Get From Dictionary    ${dict}    campaignEndDate
         ${campaign_end_date}=    Replace String  ${campaign_end_date}     T       ${SPACE}
         ${image_list_details}=      Get From Dictionary    ${dict}    imageList

         ${campaign_isActive}=      Get From Dictionary    ${dict}    isActive
         ${campaign_isApproved}=      Get From Dictionary    ${dict}    isApproved
         ${campaign_version}=      Get From Dictionary    ${dict}    version
         ${campaign_isTargetted}=      Get From Dictionary    ${dict}    isTargetted
         ${campaign_targetData}=      Get From Dictionary    ${dict}    targetData



         FOR     ${image_list_dict}    IN    @{image_list_details}


            #${image_list_id}=      Get From Dictionary    ${image_list_dict}    id
            #${db_image_list_id}=    Get From Dictionary    ${data_base_campaign_details}    imageListID
            #Verify if values are equal     ${image_list_id}        ${db_image_list_id}

            ${image_list_imageName}=      Get From Dictionary    ${image_list_dict}    imageName

            IF  ${campaign_isTargetted} == ${True}
                ${data_base_campaign_details}=       Get the targeted campaign details from the database      ${campaign_id_value}       ${lastUpdate_id}    ${image_list_imageName}
            ELSE
                ${data_base_campaign_details}=       Get the non targeted campaign details from the database      ${campaign_id_value}       ${lastUpdate_id}   ${image_list_imageName}
            END

            ${db_campaignId}=    Get From Dictionary    ${data_base_campaign_details}    campaignId
            Verify if values are equal     ${campaignId}        ${db_campaignId}

            ${db_campaignName}=    Get From Dictionary    ${data_base_campaign_details}    campaignName
            Verify if values are equal     ${campaignName}        ${db_campaignName}

            ${db_campaignBy}=    Get From Dictionary    ${data_base_campaign_details}    campaignBy
            Verify if values are equal     ${campaignBy}        ${db_campaignBy}

            ${db_lastUpdate_id}=    Get From Dictionary    ${data_base_campaign_details}    campaignHistoryID
            Verify if values are equal     ${lastUpdate_id}        ${db_lastUpdate_id}

            ${db_lastUpdate_updatedBy}=    Get From Dictionary    ${data_base_campaign_details}    campaignHistoryUpdatedBy
            Verify if values are equal     ${lastUpdate_updatedBy}        ${db_lastUpdate_updatedBy}

            ${db_lastUpdate_updatedDate}=    Get From Dictionary    ${data_base_campaign_details}    campaignHistoryUpdateDate
            ${len}=  Get Length    ${lastUpdate_updatedDate}

            ${len_increment}=    Evaluate   ${len} + 1

            ${db_lastUpdate_updatedDate}=    Get Substring    ${db_lastUpdate_updatedDate}    0     ${len}
            Verify if values are equal     ${lastUpdate_updatedDate}        ${db_lastUpdate_updatedDate}

            ${db_lastUpdate_description}=    Get From Dictionary    ${data_base_campaign_details}    updateDescription
            Verify if values are equal     ${lastUpdate_description}        ${db_lastUpdate_description}

            ${db_lastUpdate_approvalId}=    Get From Dictionary    ${data_base_campaign_details}    approvalId
            Verify if values are equal     ${lastUpdate_approvalId}        ${db_lastUpdate_approvalId}


            ${db_campaign_updatedBy}=    Get From Dictionary    ${data_base_campaign_details}    campaignUpdatedBy
            Verify if values are equal     ${campaign_updatedBy}        ${db_campaign_updatedBy}

            ${db_screen_id}=    Get From Dictionary    ${data_base_campaign_details}    marketingScreenID
            Verify if values are equal     ${screen_id}        ${db_screen_id}

            ${db_screen_type}=    Get From Dictionary    ${data_base_campaign_details}    screenType
            Verify if values are equal     ${screen_type}        ${db_screen_type}

            ${db_screen_number}=    Get From Dictionary    ${data_base_campaign_details}    screenNumber
            Verify if values are equal     ${screen_number}        ${db_screen_number}

            ${db_screen_channel_id}=    Get From Dictionary    ${data_base_campaign_details}    marketingScreenChannelID
            Verify if values are equal     ${screen_channel_id}        ${db_screen_channel_id}

            ${db_marketing_channel_id}=    Get From Dictionary    ${data_base_campaign_details}    marketingChannelID
            Verify if values are equal     ${marketing_channel_id}        ${db_marketing_channel_id}

            ${db_marketing_channel}=    Get From Dictionary    ${data_base_campaign_details}    marketingChannel
            Verify if values are equal     ${marketing_channel}        ${db_marketing_channel}

            ${db_marketing_channel_image_resolution}=    Get From Dictionary    ${data_base_campaign_details}    marketingChannelImageResolution
            Verify if values are equal     ${marketing_channel_image_resolution}        ${db_marketing_channel_image_resolution}
            ${db_campaign_start_date}=    Get From Dictionary    ${data_base_campaign_details}    campaignStartDate
            Verify if values are equal     ${campaign_start_date}        ${db_campaign_start_date}

            ${db_campaign_end_date}=    Get From Dictionary    ${data_base_campaign_details}    campaignEndDate
            Verify if values are equal     ${campaign_end_date}        ${db_campaign_end_date}

            ${db_campaign_isActive}=    Get From Dictionary    ${data_base_campaign_details}    isActive
            IF  '${db_campaign_isActive}' == '1'
                ${db_campaign_isActive}        Set Variable    ${True}
            ELSE
                ${db_campaign_isActive}        Set Variable    ${False}
            END
            Verify if values are equal     ${campaign_isActive}        ${db_campaign_isActive}

            ${db_campaign_isApproved}=    Get From Dictionary    ${data_base_campaign_details}    isApproved
            IF  '${db_campaign_isApproved}' == '1'
               ${db_campaign_isApproved}        Set Variable    ${True}
            ELSE
               ${db_campaign_isApproved}        Set Variable    ${False}
            END
            Verify if values are equal     ${campaign_isApproved}        ${db_campaign_isApproved}


            ${db_campaign_version}=    Get From Dictionary    ${data_base_campaign_details}    version
            Verify if values are equal     ${campaign_version}        ${db_campaign_version}

            ${db_campaign_isTargetted}=    Get From Dictionary    ${data_base_campaign_details}    isTargetted
            IF  '${db_campaign_isTargetted}' == '1'
               ${db_campaign_isTargetted}        Set Variable    ${True}
            ELSE
               ${db_campaign_isTargetted}        Set Variable    ${False}
            END
            Verify if values are equal     ${campaign_isTargetted}        ${db_campaign_isTargetted}

            ${db_image_list_imageName}=    Get From Dictionary    ${data_base_campaign_details}    imageName
            Verify if values are equal     ${image_list_imageName}        ${db_image_list_imageName}

            ${image_list_language_details}=      Get From Dictionary    ${image_list_dict}    language

            ${image_list_language_id}=      Get From Dictionary    ${image_list_language_details}    id
            ${db_image_list_language_id}=    Get From Dictionary    ${data_base_campaign_details}    languageId
            Verify if values are equal     ${image_list_language_id}        ${db_image_list_language_id}

            ${image_list_language}=      Get From Dictionary    ${image_list_language_details}    language
            ${db_image_list_language}=    Get From Dictionary    ${data_base_campaign_details}    language
            Verify if values are equal     ${image_list_language}        ${db_image_list_language}

            ${image_list_language_code}=      Get From Dictionary    ${image_list_language_details}    languageCode
            ${db_image_list_language_code}=    Get From Dictionary    ${data_base_campaign_details}    languageCode
            Verify if values are equal     ${image_list_language_code}        ${db_image_list_language_code}

            ${image_list_duration}=      Get From Dictionary    ${image_list_dict}    duration
            ${db_image_list_duration}=    Get From Dictionary    ${data_base_campaign_details}    duration
            Verify if values are equal     ${image_list_duration}        ${db_image_list_duration}

            ${image_list_priority}=      Get From Dictionary    ${image_list_dict}    priority
            ${db_image_list_priority}=    Get From Dictionary    ${data_base_campaign_details}    priority
            Verify if values are equal     ${image_list_priority}        ${db_image_list_priority}

            ${image_list_marketingImage}=      Get From Dictionary    ${image_list_dict}    marketingImage
            ${db_image_list_marketingImage}=    Get From Dictionary    ${data_base_campaign_details}    marketingImage
            Verify if values are equal     ${image_list_marketingImage}        ${db_image_list_marketingImage}

            ${image_list_campaignId}=      Get From Dictionary    ${image_list_dict}    campaignId
            ${db_image_list_campaignId}=    Get From Dictionary    ${data_base_campaign_details}    id
            Verify if values are equal     ${image_list_campaignId}        ${db_image_list_campaignId}

            ${image_list_languageId}=      Get From Dictionary    ${image_list_dict}    languageId
            ${db_image_list_languageId}=    Get From Dictionary    ${data_base_campaign_details}    languageId
            Verify if values are equal     ${image_list_languageId}        ${db_image_list_languageId}

         END


    END


Get the campaign schedule and cycle details from the database
    [Arguments]     ${SCH_VERSION}

    #Verify that all parameters are supplied
    Run Keyword If    '${SCH_VERSION}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${schedule_and_cycle_query}   Set Variable     ${SQL_GET_MARKETING_SCHEDULE_AND_CYCLE_DETAILS}
    ${schedule_and_cycle_query}=  Replace String      ${schedule_and_cycle_query}       sch_version     '${SCH_VERSION}'


    ${data_base_campaign_schedule_and_cycle_details}=      Execute SQL Query  ${db_type}  ${schedule_and_cycle_query}    True
    RETURN      ${data_base_campaign_schedule_and_cycle_details}




Get the non targeted campaign details from the database
    [Arguments]     ${CAMPAIGN_ID}      ${CAMPAIGN_HISTORY_ID}      ${IMAGE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${CAMPAIGN_ID}' == '${EMPTY}' or '${CAMPAIGN_HISTORY_ID}' == '${EMPTY}' or '${IMAGE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${campaign_non_targted_query}   Set Variable     ${SQL_GET_ATM_MARKETING_UN_TARGETED}
    ${campaign_non_targted_query}=  Replace String      ${campaign_non_targted_query}       campaign_ID     '${CAMPAIGN_ID}'
    ${campaign_non_targted_query}=  Replace String      ${campaign_non_targted_query}       campaign_Hist_ID     '${CAMPAIGN_HISTORY_ID}'
    ${campaign_non_targted_query}=  Replace String      ${campaign_non_targted_query}       img_name     '${IMAGE_NAME}'

    Log Many    ${campaign_non_targted_query}

    ${data_base_campaign_non_targeted_campaign_details}=      Execute SQL Query  ${db_type}  ${campaign_non_targted_query}    True
    RETURN      ${data_base_campaign_non_targeted_campaign_details}
    


Get the targeted campaign details from the database
    [Arguments]     ${CAMPAIGN_ID}      ${CAMPAIGN_HISTORY_ID}      ${IMAGE_NAME}

    #Verify that all parameters are supplied
    Run Keyword If    '${CAMPAIGN_ID}' == '${EMPTY}' or '${CAMPAIGN_HISTORY_ID}' == '${EMPTY}' or '${IMAGE_NAME}' == '${EMPTY}'
    ...     Fail    Please make sure that values of all parameters are provided!

    ${db_type}=   Set Variable   'MYSQL'
    #Check if Target Data Exists for the Campaign
    ${campaign_targted_query}   Set Variable     ${SQL_GET_ATM_MARKETING_TARGETED}
    ${campaign_targted_query}=  Replace String      ${campaign_targted_query}       campaign_ID     '${CAMPAIGN_ID}'
    ${campaign_targted_query}=  Replace String      ${campaign_targted_query}       campaign_Hist_ID     '${CAMPAIGN_HISTORY_ID}'
    ${campaign_targted_query}=  Replace String      ${campaign_targted_query}       img_name     '${IMAGE_NAME}'

    Log Many    ${campaign_targted_query}

    ${data_base_campaign_targeted_campaign_details}=      Execute SQL Query  ${db_type}  ${campaign_targted_query}    True
    RETURN      ${data_base_campaign_targeted_campaign_details}






