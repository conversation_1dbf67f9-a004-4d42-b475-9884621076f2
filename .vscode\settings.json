{"python.defaultInterpreterPath": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe", "python.analysis.extraPaths": ["${workspaceFolder}", "${workspaceFolder}/Alternative_Physical_Channels_QA", "${workspaceFolder}/Alternative_Physical_Channels_QA/common_utilities", "${workspaceFolder}/Alternative_Physical_Channels_QA/vms/keywords", "${workspaceFolder}/Alternative_Physical_Channels_QA/FIS_API/keywords", "${workspaceFolder}/Alternative_Physical_Channels_QA/future_fit_architecture_portal/keywords"], "robot.pythonpath": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages", "${workspaceFolder}", "${workspaceFolder}/Alternative_Physical_Channels_QA", "${workspaceFolder}/Alternative_Physical_Channels_QA/common_utilities", "${workspaceFolder}/Alternative_Physical_Channels_QA/vms/keywords", "${workspaceFolder}/Alternative_Physical_Channels_QA/FIS_API/keywords", "${workspaceFolder}/Alternative_Physical_Channels_QA/future_fit_architecture_portal/keywords"], "robot.language-server.python": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe", "robot.variables": {}, "robot.libraryListener.port": 0, "robot.completions.keywords.format": "First word with spaces", "robot.pythonExecutor": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe", "robot.lint.robocop.enabled": true, "robot.language-server.args": ["--log-file=c:\\Alternative\\robot_lsp.log", "--verbose"], "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder};${workspaceFolder}/Alternative_Physical_Channels_QA;${workspaceFolder}/Alternative_Physical_Channels_QA/common_utilities;${workspaceFolder}/Alternative_Physical_Channels_QA/vms/keywords", "PATH": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Scripts\\;${env:PATH}"}, "files.associations": {"*.robot": "robotframework"}, "python.linting.enabled": true}