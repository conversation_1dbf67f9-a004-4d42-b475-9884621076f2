<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.0 on win32)" generated="******** 15:15:11.399" rpa="false" schemaversion="4">
<suite id="s1" name="Future-Fit Portal" source="C:\Users\<USER>\source\repos\future_fit_repo\tests\Front-End\TC_03_CAPTURE_CAMPAIGN.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_USERNAME</arg>
<arg>${TESTRAIL_USERNAME}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:15:13.513" level="INFO">Environment variable 'TESTRAIL_USERNAME' set to value '<EMAIL>'.</msg>
<status status="PASS" starttime="******** 15:15:13.513" endtime="******** 15:15:13.513"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_PASSWORD</arg>
<arg>${TESTRAIL_PASSWORD}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:15:13.515" level="INFO">Environment variable 'TESTRAIL_PASSWORD' set to value 'Tshwarelo@1'.</msg>
<status status="PASS" starttime="******** 15:15:13.514" endtime="******** 15:15:13.515"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:15:13.515" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 15:15:13.515" endtime="******** 15:15:13.515"/>
</kw>
<status status="PASS" starttime="******** 15:15:13.513" endtime="******** 15:15:13.515"/>
</kw>
<test id="s1-t1" name="FFT - Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English_08395" line="59">
<kw name="Create marketing campaign">
<arg>Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English</arg>
<arg>155057370</arg>
<arg>Yes</arg>
<arg>ATM</arg>
<arg>08395  ATM UAT LAB, 268 Republic   - Randburg</arg>
<arg>AUTOMATION UAT 08397 Targted_English</arg>
<arg>Idle</arg>
<arg>ATM</arg>
<arg>2</arg>
<arg>English</arg>
<arg>images\\3048_2010091absagenericatms_easter365x470-65eb2169a9de2_en.jpg</arg>
<arg>BUSINESS_USER</arg>
<arg>APC_UAT</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 15:15:13.518" level="INFO">Set test documentation to:
Positive          Create campaign - is the campaign targeted - Yes - campaign targeted - ATM - English</msg>
<status status="PASS" starttime="******** 15:15:13.517" endtime="******** 15:15:13.518"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>TESTRAIL_TESTCASE_ID</arg>
<arg>${TESTRAIL_TESTCASE_ID}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 15:15:13.518" level="INFO">Environment variable 'TESTRAIL_TESTCASE_ID' set to value '155057370'.</msg>
<status status="PASS" starttime="******** 15:15:13.518" endtime="******** 15:15:13.518"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:15:14.872" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 15:15:13.519" endtime="******** 15:15:14.872"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:15:14.874" endtime="******** 15:15:14.876"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:15:14.878" endtime="******** 15:15:14.881"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:15:14.883" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<status status="PASS" starttime="******** 15:15:14.882" endtime="******** 15:15:14.883"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:15:14.884" level="INFO">${handle} = chrome.exe</msg>
<status status="PASS" starttime="******** 15:15:14.884" endtime="******** 15:15:14.884"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:15:14.884" endtime="******** 15:15:14.887"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<msg timestamp="******** 15:15:15.434" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 15:15:16.819" level="INFO">${rc_code} = 0</msg>
<msg timestamp="******** 15:15:16.819" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 27416 has been terminated.</msg>
<status status="PASS" starttime="******** 15:15:14.887" endtime="******** 15:15:16.819"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" starttime="******** 15:15:16.819" endtime="******** 15:15:16.821"/>
</kw>
<status status="PASS" starttime="******** 15:15:14.882" endtime="******** 15:15:16.821"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:15:16.821" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002AF26887EF0&gt;</msg>
<status status="PASS" starttime="******** 15:15:16.821" endtime="******** 15:15:16.821"/>
</kw>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<msg timestamp="******** 15:15:16.821" level="INFO">${prefs} = {'useAutomationExtension': False}</msg>
<status status="PASS" starttime="******** 15:15:16.821" endtime="******** 15:15:16.821"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 15:15:16.821" endtime="******** 15:15:16.821"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 15:15:16.821" level="INFO">${user_home} = C:\Users\<USER>\Users\AB032to</msg>
<status status="PASS" starttime="******** 15:15:16.821" endtime="******** 15:15:16.822"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 15:15:16.822" endtime="******** 15:15:16.822"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" starttime="******** 15:15:16.822" endtime="******** 15:15:16.822"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:15:16.822" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<status status="PASS" starttime="******** 15:15:16.822" endtime="******** 15:15:16.822"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<msg timestamp="******** 15:15:16.822" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': 'normal', 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False}, 'extensions': [], 'args': ['user-data-dir=C:\\Users\\<USER>\bin\chromedriver.exe might not be compatible with the detected chrome version (125.0.6422.77); currently, chromedriver 125.0.6422.78 is recommended for chrome 125.*, so it is advised to delete the driver in PATH and retry</msg>
<status status="PASS" starttime="******** 15:15:16.822" endtime="******** 15:15:28.679"/>
</kw>
<status status="PASS" starttime="******** 15:15:14.882" endtime="******** 15:15:28.679"/>
</kw>
<kw name="Load" library="Login">
<arg>${URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 15:15:28.707" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 15:15:28.707" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 15:15:28.682" endtime="******** 15:15:28.707"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 15:15:28.707" endtime="******** 15:15:28.712"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 15:15:28.714" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 15:15:28.714" endtime="******** 15:15:51.680"/>
</kw>
<status status="PASS" starttime="******** 15:15:28.713" endtime="******** 15:15:51.680"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:01.723" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 15:15:51.680" endtime="******** 15:16:01.723"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 15:16:01.724" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:16:01.806" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 15:16:01.724" endtime="******** 15:16:01.806"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 15:16:01.806" endtime="******** 15:16:01.806"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:21.808" level="INFO">Slept 20 seconds</msg>
<status status="PASS" starttime="******** 15:16:01.806" endtime="******** 15:16:21.808"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 15:16:21.808" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:16:21.830" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 15:16:21.808" endtime="******** 15:16:21.830"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 15:16:21.831" endtime="******** 15:16:21.831"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:31.832" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 15:16:21.832" endtime="******** 15:16:31.832"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 15:16:31.832" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 15:16:31.849" level="INFO">${element_count_3} = 0</msg>
<status status="PASS" starttime="******** 15:16:31.832" endtime="******** 15:16:31.849"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 15:16:31.849" endtime="******** 15:16:31.849"/>
</kw>
<status status="PASS" starttime="******** 15:15:28.681" endtime="******** 15:16:31.849"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:16:32.026" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-23.png"&gt;&lt;img src="selenium-screenshot-23.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:16:31.850" endtime="******** 15:16:32.026"/>
</kw>
<status status="PASS" starttime="******** 15:15:14.881" endtime="******** 15:16:32.026"/>
</kw>
<status status="PASS" starttime="******** 15:15:14.881" endtime="******** 15:16:32.026"/>
</kw>
<status status="PASS" starttime="******** 15:15:13.518" endtime="******** 15:16:32.027"/>
</kw>
<kw name="And The user clicks on the Capture campaign link" library="Navigation">
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user clicks the Capture campaign link</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:32.027" endtime="******** 15:16:32.033"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:37.033" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 15:16:32.033" endtime="******** 15:16:37.033"/>
</kw>
<kw name="Run Keyword Until Success" library="Navigation">
<arg>Click Element</arg>
<arg>${CAPTURE_CAMPAIGN_LINK}</arg>
<kw name="Wait Until Keyword Succeeds" library="BuiltIn">
<arg>30s</arg>
<arg>1s</arg>
<arg>${KW}</arg>
<arg>${KWARGS}</arg>
<doc>Runs the specified keyword and retries if it fails.</doc>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${KWARGS}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:37.044" level="INFO">Clicking element 'xpath=//*[@id="cdk-accordion-child-0"]/div/mat-nav-list/div/mat-list-item/span/span[3]'.</msg>
<status status="PASS" starttime="******** 15:16:37.043" endtime="******** 15:16:37.246"/>
</kw>
<status status="PASS" starttime="******** 15:16:37.034" endtime="******** 15:16:37.246"/>
</kw>
<status status="PASS" starttime="******** 15:16:37.033" endtime="******** 15:16:37.246"/>
</kw>
<kw name="Wait Until Page Contains" library="SeleniumLibrary">
<arg>Fill out Campaign Targeted</arg>
<doc>Waits until ``text`` appears on the current page.</doc>
<status status="PASS" starttime="******** 15:16:37.247" endtime="******** 15:16:37.270"/>
</kw>
<status status="PASS" starttime="******** 15:16:32.027" endtime="******** 15:16:37.271"/>
</kw>
<kw name="And The user fills out Campaign Targeted" library="FilloutCampaignTarget">
<arg>${IS_CAMPAIGN_TARGETED}</arg>
<arg>${CAMPAIGN_TARGET}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Targeted: ${IS_CAMPAIGN_TARGETED}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:37.273" endtime="******** 15:16:37.276"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Target: ${CAMPAIGN_TARGET}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:37.278" endtime="******** 15:16:37.278"/>
</kw>
<kw name="User select radio button" library="Navigation">
<arg>${IS_CAMPAIGN_TARGETED}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Selecting radio button ${RADIO_BUTTON_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:37.280" endtime="******** 15:16:37.282"/>
</kw>
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<msg timestamp="******** 15:16:38.288" level="INFO">Suppressing StaleElementReferenceException from Selenium.</msg>
<status status="PASS" starttime="******** 15:16:37.282" endtime="******** 15:16:38.503"/>
</kw>
<status status="PASS" starttime="******** 15:16:37.282" endtime="******** 15:16:38.503"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${RADIO_BUTTON_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:38.504" level="INFO">${path_string} = //input[@value='</msg>
<status status="PASS" starttime="******** 15:16:38.503" endtime="******** 15:16:38.504"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.504" endtime="******** 15:16:38.504"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:38.505" level="INFO">${path_string} = //input[@value='Yes</msg>
<status status="PASS" starttime="******** 15:16:38.504" endtime="******** 15:16:38.505"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.505" endtime="******** 15:16:38.506"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:38.506" level="INFO">${path_string} = //input[@value='Yes']/parent::*/parent::*/parent::*</msg>
<status status="PASS" starttime="******** 15:16:38.506" endtime="******** 15:16:38.506"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.506" endtime="******** 15:16:38.507"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:16:38.508" endtime="******** 15:16:38.557"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:38.558" level="INFO">Clicking element 'xpath=//input[@value='Yes']/parent::*/parent::*/parent::*'.</msg>
<status status="PASS" starttime="******** 15:16:38.558" endtime="******** 15:16:38.618"/>
</kw>
<status status="PASS" starttime="******** 15:16:37.278" endtime="******** 15:16:38.618"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>User select radio button</arg>
<arg>Region</arg>
<arg>ELSE IF</arg>
<arg>'${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>User select radio button</arg>
<arg>ATM</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="User select radio button" library="Navigation">
<arg>ATM</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Selecting radio button ${RADIO_BUTTON_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.621" endtime="******** 15:16:38.622"/>
</kw>
<kw name="Wait for spinner to disapear" library="Navigation">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<status status="PASS" starttime="******** 15:16:38.622" endtime="******** 15:16:38.635"/>
</kw>
<status status="PASS" starttime="******** 15:16:38.622" endtime="******** 15:16:38.635"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${RADIO_BUTTON_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:38.636" level="INFO">${path_string} = //input[@value='</msg>
<status status="PASS" starttime="******** 15:16:38.635" endtime="******** 15:16:38.636"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.636" endtime="******** 15:16:38.636"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:38.637" level="INFO">${path_string} = //input[@value='ATM</msg>
<status status="PASS" starttime="******** 15:16:38.637" endtime="******** 15:16:38.637"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.637" endtime="******** 15:16:38.637"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${RADIO_BUTTON_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:38.638" level="INFO">${path_string} = //input[@value='ATM']/parent::*/parent::*/parent::*</msg>
<status status="PASS" starttime="******** 15:16:38.637" endtime="******** 15:16:38.638"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:38.638" endtime="******** 15:16:38.639"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:16:38.639" endtime="******** 15:16:38.676"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:38.677" level="INFO">Clicking element 'xpath=//input[@value='ATM']/parent::*/parent::*/parent::*'.</msg>
<status status="PASS" starttime="******** 15:16:38.676" endtime="******** 15:16:39.018"/>
</kw>
<status status="PASS" starttime="******** 15:16:38.620" endtime="******** 15:16:39.018"/>
</kw>
<status status="PASS" starttime="******** 15:16:38.619" endtime="******** 15:16:39.018"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:41.019" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:16:39.019" endtime="******** 15:16:41.019"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${CAMPAIGN_TARGET}' == 'Region' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>Select from dropdown</arg>
<arg>${REGIONAL_CAMPAIGN_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<arg>ELSE IF</arg>
<arg>'${CAMPAIGN_TARGET}' == 'ATM' and '${IS_CAMPAIGN_TARGETED}' == 'Yes'</arg>
<arg>Select ATM from dropdown</arg>
<arg>${ATM_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Select ATM from dropdown" library="Navigation">
<arg>${ATM_DROPDOWN}</arg>
<arg>${CAMPAIGN_TARGETED_REGION_OR_ATM}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:41.021" endtime="******** 15:16:41.024"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:43.025" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:16:41.024" endtime="******** 15:16:43.026"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:43.027" level="INFO">Clicking element 'xpath=/html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[1]/app-campaign-targeted/div[2]/div[2]/mat-form-field/div/div[1]/div'.</msg>
<status status="PASS" starttime="******** 15:16:43.026" endtime="******** 15:16:44.201"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:49.201" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 15:16:44.201" endtime="******** 15:16:49.201"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_ATM_SELECTOR1}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:49.203" level="INFO">${path_string} = //span[contains(text(),</msg>
<status status="PASS" starttime="******** 15:16:49.202" endtime="******** 15:16:49.203"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:49.203" endtime="******** 15:16:49.204"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:49.205" level="INFO">${path_string} = //span[contains(text(),'08395  ATM UAT LAB, 268 Republic   - Randburg</msg>
<status status="PASS" starttime="******** 15:16:49.204" endtime="******** 15:16:49.205"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:49.205" endtime="******** 15:16:49.206"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_ATM_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:16:49.207" level="INFO">${path_string} = //span[contains(text(),'08395  ATM UAT LAB, 268 Republic   - Randburg')]</msg>
<status status="PASS" starttime="******** 15:16:49.207" endtime="******** 15:16:49.207"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:49.207" endtime="******** 15:16:49.207"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:16:49.208" endtime="******** 15:16:49.465"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:49.466" level="INFO">Clicking element 'xpath=//span[contains(text(),'08395  ATM UAT LAB, 268 Republic   - Randburg')]'.</msg>
<status status="PASS" starttime="******** 15:16:49.465" endtime="******** 15:16:50.087"/>
</kw>
<status status="PASS" starttime="******** 15:16:41.021" endtime="******** 15:16:50.087"/>
</kw>
<status status="PASS" starttime="******** 15:16:41.019" endtime="******** 15:16:50.087"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<arg>FilloutCampaignTarget.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<msg timestamp="******** 15:16:50.312" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="FilloutCampaignTarget.png"&gt;&lt;img src="FilloutCampaignTarget.png" width="800px"&gt;&lt;/a&gt;</msg>
<status status="PASS" starttime="******** 15:16:50.087" endtime="******** 15:16:50.312"/>
</kw>
<status status="PASS" starttime="******** 15:16:37.273" endtime="******** 15:16:50.313"/>
</kw>
<kw name="And The user clicks on Next button" library="Navigation">
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>${NEXT_BUTTON}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:16:50.314" endtime="******** 15:16:50.356"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${Next_Button}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:50.357" level="INFO">Clicking element 'xpath=//span[contains(text(),'Next')]/parent::button'.</msg>
<status status="PASS" starttime="******** 15:16:50.356" endtime="******** 15:16:50.455"/>
</kw>
<status status="PASS" starttime="******** 15:16:50.313" endtime="******** 15:16:50.455"/>
</kw>
<kw name="And The user fills out Campaign" library="FilloutYourCampaign">
<arg>${CAMPAIGN_NAME}</arg>
<arg>${MARKETING_TYPE}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>${NUMBER_OF_DAYS_FROM_START_DATE}</arg>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:52.456" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:16:50.456" endtime="******** 15:16:52.456"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:52.456" level="INFO">Clicking element 'xpath=//html/body/app-root/app-sidenav/mat-sidenav-container/mat-sidenav-content/app-marketing-view/div[2]/div/div/mat-horizontal-stepper/div/div[2]/div[2]/app-capture-campaign/div/div[1]/mat-form-field[1]/div/div[1]/div[3]/input'.</msg>
<status status="PASS" starttime="******** 15:16:52.456" endtime="******** 15:16:52.696"/>
</kw>
<kw name="Input Text" library="SeleniumLibrary">
<arg>${CAMPAIGN_NAME_INPUT}</arg>
<arg>${CAMPAIGN_NAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<msg timestamp="******** 15:16:52.696" level="INFO">Typing text 'AUTOMATION UAT 08397 Targted_English' into text field 'name=campaignName'.</msg>
<status status="PASS" starttime="******** 15:16:52.696" endtime="******** 15:16:53.298"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Marketing Type: ${MARKETING_TYPE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:53.298" endtime="******** 15:16:53.299"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Receiver Device Type: ${RECEIVER_DEVICE_TYPE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:53.299" endtime="******** 15:16:53.299"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${RECEIVER_DEVICE_TYPE}' == 'ATM'</arg>
<arg>Select from dropdown</arg>
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<arg>ELSE</arg>
<arg>Select from dropdown</arg>
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Select from dropdown" library="Navigation">
<arg>${RECEIVER_DEVICE_TYPE_DROPDOWN}</arg>
<arg>${RECEIVER_DEVICE_TYPE}</arg>
<kw name="Log To Console" library="BuiltIn">
<arg>Dropdown value is ${DROPDOWN_SELECTION_VALUE}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:16:53.302" endtime="******** 15:16:53.303"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:16:55.303" level="INFO">Slept 2 seconds</msg>
<status status="PASS" starttime="******** 15:16:53.303" endtime="******** 15:16:55.303"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>${DROPDOWN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:16:55.305" level="INFO">Clicking element 'xpath=//mat-select[@role="combobox" and @name="receiverDeviceType"]'.</msg>
<status status="PASS" starttime="******** 15:16:55.303" endtime="******** 15:16:55.464"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 15:17:00.466" level="INFO">Slept 5 seconds</msg>
<status status="PASS" starttime="******** 15:16:55.465" endtime="******** 15:17:00.466"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${DROPDOWN_SELECTOR1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:17:00.467" level="INFO">${path_string} = //span[text()=' </msg>
<status status="PASS" starttime="******** 15:17:00.466" endtime="******** 15:17:00.467"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 1 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:00.467" endtime="******** 15:17:00.585"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTION_VALUE}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:17:00.587" level="INFO">${path_string} = //span[text()=' ATM </msg>
<status status="PASS" starttime="******** 15:17:00.586" endtime="******** 15:17:00.587"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 2 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:00.587" endtime="******** 15:17:00.592"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${path_string}</arg>
<arg>${DROPDOWN_SELECTOR2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:17:00.593" level="INFO">${path_string} = //span[text()=' ATM ']</msg>
<status status="PASS" starttime="******** 15:17:00.592" endtime="******** 15:17:00.593"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>String 3 ${path_string}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:00.593" endtime="******** 15:17:00.597"/>
</kw>
<kw name="Wait Until Element Is Enabled" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Waits until the element ``locator`` is enabled.</doc>
<status status="PASS" starttime="******** 15:17:00.597" endtime="******** 15:17:01.013"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:17:01.014" level="INFO">Clicking element 'xpath=//span[text()=' ATM ']'.</msg>
<status status="PASS" starttime="******** 15:17:01.013" endtime="******** 15:17:01.093"/>
</kw>
<status status="PASS" starttime="******** 15:16:53.301" endtime="******** 15:17:01.093"/>
</kw>
<status status="PASS" starttime="******** 15:16:53.301" endtime="******** 15:17:01.093"/>
</kw>
<kw name="Get Day To Select" library="Utility">
<var>${campaign_start_date}</var>
<arg>1</arg>
<msg timestamp="******** 15:17:01.106" level="INFO">Selected day is  1
dayalone: 28
The date is 10 or greater</msg>
<msg timestamp="******** 15:17:01.106" level="INFO">${campaign_start_date} = 28</msg>
<status status="PASS" starttime="******** 15:17:01.093" endtime="******** 15:17:01.106"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign Start date: ${campaign_start_date}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:01.106" endtime="******** 15:17:01.119"/>
</kw>
<kw name="Curent Day" library="Utility">
<var>${current_day_month}</var>
<arg>1</arg>
<msg timestamp="******** 15:17:01.121" level="INFO">27
271</msg>
<msg timestamp="******** 15:17:01.121" level="INFO">${current_day_month} = 0</msg>
<status status="PASS" starttime="******** 15:17:01.119" endtime="******** 15:17:01.121"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${item_text}</var>
<arg>${campaign_start_date}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:17:01.121" level="INFO">${item_text} = 28</msg>
<status status="PASS" starttime="******** 15:17:01.121" endtime="******** 15:17:01.122"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR='</arg>
<arg>${CALENDAR_DAY_PATH1}</arg>
<arg>${SPACE}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:17:01.122" level="INFO">${path_string} = //div[text()=' </msg>
<status status="PASS" starttime="******** 15:17:01.122" endtime="******** 15:17:01.122"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR=</arg>
<arg>${path_string}</arg>
<arg>${item_text}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:17:01.123" level="INFO">${path_string} = //div[text()=' 28</msg>
<status status="PASS" starttime="******** 15:17:01.122" endtime="******** 15:17:01.123"/>
</kw>
<kw name="Catenate" library="BuiltIn">
<var>${path_string}</var>
<arg>SEPARATOR= '</arg>
<arg>${path_string}</arg>
<arg>${CALENDAR_DAY_PATH2}</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<msg timestamp="******** 15:17:01.123" level="INFO">${path_string} = //div[text()=' 28 ']/parent::*</msg>
<status status="PASS" starttime="******** 15:17:01.123" endtime="******** 15:17:01.123"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=${path_string}</arg>
<doc>Click the element identified by ``locator``.</doc>
<msg timestamp="******** 15:17:01.124" level="INFO">Clicking element 'xpath=//div[text()=' 28 ']/parent::*'.</msg>
<status status="PASS" starttime="******** 15:17:01.123" endtime="******** 15:17:01.329"/>
</kw>
<kw name="Get Day To Select" library="Utility">
<var>${campaign_end_date}</var>
<arg>${NUMBER_OF_DAYS_FROM_START_DATE}</arg>
<msg timestamp="******** 15:17:01.329" level="INFO">Selected day is  2
dayalone: 29
The date is 10 or greater</msg>
<msg timestamp="******** 15:17:01.329" level="INFO">${campaign_end_date} = 29</msg>
<status status="PASS" starttime="******** 15:17:01.329" endtime="******** 15:17:01.329"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>Campaign End date: ${campaign_start_date}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:01.329" endtime="******** 15:17:01.331"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${item_text}</var>
<arg>${campaign_end_date}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:17:01.331" level="INFO">${item_text} = 29</msg>
<status status="PASS" starttime="******** 15:17:01.331" endtime="******** 15:17:01.331"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${campaign_start_date}</var>
<arg>${campaign_start_date}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:17:01.332" level="INFO">${campaign_start_date} = 28</msg>
<status status="PASS" starttime="******** 15:17:01.331" endtime="******** 15:17:01.332"/>
</kw>
<kw name="Set Variable" library="BuiltIn">
<var>${NUMBER_OF_DAYS_FROM_START_DATE}</var>
<arg>${NUMBER_OF_DAYS_FROM_START_DATE}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 15:17:01.332" level="INFO">${NUMBER_OF_DAYS_FROM_START_DATE} = 2</msg>
<status status="PASS" starttime="******** 15:17:01.332" endtime="******** 15:17:01.332"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${total_date}</var>
<arg>${NUMBER_OF_DAYS_FROM_START_DATE} + ${campaign_start_date}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 15:17:01.334" level="INFO">${total_date} = 30</msg>
<status status="PASS" starttime="******** 15:17:01.334" endtime="******** 15:17:01.334"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${total_date}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:01.335" endtime="******** 15:17:01.336"/>
</kw>
<kw name="Page Should Contain Element" library="SeleniumLibrary">
<var>${Result}</var>
<arg>${LAST_DAY_OF_THE_MONTH}</arg>
<doc>Verifies that element ``locator`` is found on the current page.</doc>
<msg timestamp="******** 15:17:01.382" level="INFO">Current page contains element 'xpath=//div[text()=' 31 ']/parent::*'.</msg>
<msg timestamp="******** 15:17:01.382" level="INFO">${Result} = None</msg>
<status status="PASS" starttime="******** 15:17:01.336" endtime="******** 15:17:01.382"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>${Result}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 15:17:01.383" endtime="******** 15:17:01.456"/>
</kw>
<if>
<branch type="IF" condition="'${Result}' == 'None'">
<if>
<branch type="IF" condition="'${total_date}' &gt;= '31'">
<kw name="Log To Console" library="BuiltIn">
<arg>31 days</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" starttime="******** 15:17:01.457" endtime="******** 15:17:01.457"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" starttime="******** 15:17:01.457" endtime="******** 15:17:01.457"/>
</kw>
<kw name="Click Element" library="SeleniumLibrary">
<arg>xpath=//*[@aria-label="Next month"]</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" starttime="******** 15:17:01.457" endtime="******** 15:17:01.457"/>
</kw>
<status status="NOT RUN" starttime="******** 15:17:01.456" endtime="******** 15:17:01.457"/>
</branch>
<status status="PASS" starttime="******** 15:17:01.456" endtime="******** 15:17:01.457"/>
</if>
<status status="PASS" starttime="******** 15:17:01.456" endtime="******** 15:17:01.457"/>
</branch>
<branch type="ELSE IF" condition="'${total_date}' &gt;= '30'">
<kw name="Click Element" library="SeleniumLibrary">
<arg>