*** Settings ***
#Author Name               : <PERSON>jabulo
#Email Address             : <EMAIL>

Documentation  VMS Dashboard Validation

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             ../../utility/UiActions.py
Library                                             String
Library                                             OperatingSystem
Library                                             DatabaseLibrary
Library                                             ../../utility/DatabaseUtility.py
Resource                                             ../../keywords/common/DBUtility.robot
Resource    ../common/Navigation.robot
Library                                             XML
Library                                             Collections


#***********************************PROJECT RESOURCES***************************************

*** Variables ***
${PAGE_HEADER_MONTH}        xpath=//h2
${MONTH_FORMAT}    %Y-%m

*** Keywords ***

The user verifies Generic campaing data
    ${elements}=    Get WebElements    xpath=//div//i[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'generic')]
    ${elements_length}=     Get Length    ${elements}
    Run Keyword If    ${elements_length} == 0    Fail    No elements found matching the XPath expression.
    ${first_element}=    Get From List    ${elements}    0
    Click Element    ${first_element}
    Element Should Be Visible    xpath=//input[contains(@value, 'NO')]
    Element Should Be Visible    xpath=//input[contains(@value, 'Generic')]
    Capture Page Screenshot

The user verifies ATM Targeted campaign data
    ${elements}=    Get WebElements    xpath=//i[contains(text(), '09005')]
    ${elements_length}=     Get Length    ${elements}
    Run Keyword If    ${elements_length} == 0    Fail    No elements found matching the XPath expression.
    ${first_element}=    Get From List    ${elements}    2
    Click Element    ${first_element}
    Element Should Be Visible    xpath=//input[contains(@value, 'NO')]
    SeleniumLibrary.Element Should Not Be Visible    xpath=//input[contains(@value, 'Generic')]
    Capture Page Screenshot

The user verifies Region Targeted campaign data
    ${elements}=    Get WebElements    xpath=//i[contains(text(), 'Gauteng')]
    ${elements_length}=     Get Length    ${elements}
    Run Keyword If    ${elements_length} == 0    Fail    No elements found matching the XPath expression.
    ${first_element}=    Get From List    ${elements}    2
    Click Element    ${first_element}
    Element Should Be Visible    xpath=//input[contains(@value, 'YES')]
    SeleniumLibrary.Element Should Not Be Visible  xpath=//input[contains(@value, 'Generic')]
    Capture Page Screenshot