<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 6.1.1 (Python 3.12.2 on win32)" generated="******** 10:06:07.487" rpa="false" schemaversion="4">
<suite id="s1" name="Future Fit Portal" source="C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\tests\Front-End\Approver_Role\RAC29a_TC_117_Approve_Campaign.robot">
<kw name="Set up environment variables" library="SetEnvironmentVariales" type="SETUP">
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:06:09.433" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<status status="PASS" starttime="******** 10:06:09.432" endtime="******** 10:06:09.433"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:06:09.433" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<status status="PASS" starttime="******** 10:06:09.433" endtime="******** 10:06:09.433"/>
</kw>
<kw name="Set Environment Variable" library="OperatingSystem">
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<msg timestamp="******** 10:06:09.433" level="INFO">Environment variable 'BASE_URL' set to value 'APC_UAT'.</msg>
<status status="PASS" starttime="******** 10:06:09.433" endtime="******** 10:06:09.433"/>
</kw>
<status status="PASS" starttime="******** 10:06:09.432" endtime="******** 10:06:09.433"/>
</kw>
<test id="s1-t1" name="RAC29a_TC_117_FFT_Approval_Approve_Campaign" line="43">
<kw name="Validating the Approval function on Campaign Approvals">
<arg>Approve Campaign</arg>
<arg>BUSINESS_APPROVER</arg>
<arg>APC_UAT</arg>
<kw name="Set Test Documentation" library="BuiltIn">
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<msg timestamp="******** 10:06:09.435" level="INFO">Set test documentation to:
Approve Campaign</msg>
<status status="PASS" starttime="******** 10:06:09.434" endtime="******** 10:06:09.435"/>
</kw>
<kw name="Given The user logs into Future Fit Architecture portal" library="Login">
<arg>${TEST_ENVIRONMENT}</arg>
<arg>Chrome</arg>
<arg>drivers\chromedriver.exe</arg>
<arg>${LOGON_USER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<<<<<<< HEAD
<status status="PASS" start="2024-10-30T16:46:23.398111" elapsed="0.108425"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T16:46:23.507537" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T16:46:23.508533" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Windows system Login" owner="Login">
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2024-10-30T16:46:23.509557" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2024-10-30T16:46:23.509557" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2024-10-30T16:46:23.509557" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2024-10-30T16:46:23.509557" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2024-10-30T16:46:23.509557" elapsed="0.000976"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2024-10-30T16:46:23.510533" level="INFO">${handle} = chrome.exe</msg>
=======
<msg timestamp="******** 10:06:09.605" level="INFO">${system} = Windows</msg>
<status status="PASS" starttime="******** 10:06:09.435" endtime="******** 10:06:09.605"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:06:09.605" endtime="******** 10:06:09.606"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>--------------------------The user logs into Future-Fit Architecture</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:06:09.606" endtime="******** 10:06:09.607"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${system}' == 'Windows'</arg>
<arg>Windows system Login</arg>
<arg>${URL}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Windows system Login" library="Login">
<arg>${URL}</arg>
<kw name="Begin Web test" library="Login">
<kw name="Kill process" library="Login">
<arg>${BROWSER}</arg>
<kw name="Evaluate" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<msg timestamp="******** 10:06:09.609" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<status status="PASS" starttime="******** 10:06:09.609" endtime="******** 10:06:09.609"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" library="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:06:09.610" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<status status="PASS" starttime="******** 10:06:09.610" endtime="******** 10:06:09.610"/>
</kw>
<status status="PASS" starttime="******** 10:06:09.609" endtime="******** 10:06:09.610"/>
</branch>
<status status="PASS" starttime="******** 10:06:09.609" endtime="******** 10:06:09.610"/>
</if>
<kw name="Catenate" library="BuiltIn">
>>>>>>> origin/development
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<<<<<<< HEAD
<status status="PASS" start="2024-10-30T16:46:23.510533" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2024-10-30T16:46:23.510533" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2024-10-30T16:46:23.545533" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2024-10-30T16:46:23.875718" level="INFO">${rc_code} = 128</msg>
<msg time="2024-10-30T16:46:23.875718" level="INFO">${output} = ERROR: The process "chrome.exe" not found.</msg>
=======
<msg timestamp="******** 10:06:09.610" level="INFO">${handle} = msedge.exe</msg>
<status status="PASS" starttime="******** 10:06:09.610" endtime="******** 10:06:09.610"/>
</kw>
<kw name="Log To Console" library="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" starttime="******** 10:06:09.610" endtime="******** 10:06:09.611"/>
</kw>
<kw name="Run And Return Rc And Output" library="OperatingSystem">
>>>>>>> origin/development
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<<<<<<< HEAD
<status status="PASS" start="2024-10-30T16:46:23.511533" elapsed="0.364185"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2024-10-30T16:46:23.875718" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2024-10-30T16:46:23.875718" elapsed="0.000999"/>
</kw>
=======
<msg timestamp="******** 10:06:09.672" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg timestamp="******** 10:06:10.359" level="INFO">${rc_code} = 128</msg>
<msg timestamp="******** 10:06:10.359" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<status status="PASS" starttime="******** 10:06:09.611" endtime="******** 10:06:10.359"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
>>>>>>> origin/development
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<<<<<<< HEAD
<status status="PASS" start="2024-10-30T16:46:23.875718" elapsed="0.000999"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2024-10-30T16:46:23.509557" elapsed="0.367160"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2024-10-30T16:46:23.876717" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2024-10-30T16:46:23.876717" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T16:46:23.876717" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2024-10-30T16:46:23.876717" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2024-10-30T16:46:23.876717" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>
=======
<kw name="Log" library="BuiltIn">
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<msg timestamp="******** 10:06:10.360" level="WARN">There was error during termination of process</msg>
<status status="PASS" starttime="******** 10:06:10.360" endtime="******** 10:06:10.361"/>
</kw>
<status status="PASS" starttime="******** 10:06:10.360" endtime="******** 10:06:10.361"/>
</kw>
<status status="PASS" starttime="******** 10:06:09.609" endtime="******** 10:06:10.361"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:06:10.361" level="INFO">${is_browser_browser} = No</msg>
<status status="PASS" starttime="******** 10:06:10.361" endtime="******** 10:06:10.361"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 10:06:10.362" level="INFO">${is_headless_browser_type} = NO</msg>
<status status="PASS" starttime="******** 10:06:10.362" endtime="******** 10:06:10.362"/>
</kw>
<kw name="Convert To Upper Case" library="String">
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<msg timestamp="******** 10:06:10.362" level="INFO">${browser_name} = EDGE</msg>
<status status="PASS" starttime="******** 10:06:10.362" endtime="******** 10:06:10.362"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" library="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<msg timestamp="******** 10:06:10.363" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<status status="PASS" starttime="******** 10:06:10.363" endtime="******** 10:06:10.363"/>
</kw>
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<msg timestamp="******** 10:06:10.363" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000020745104050&gt;</msg>
<status status="PASS" starttime="******** 10:06:10.363" endtime="******** 10:06:10.363"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" library="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" starttime="******** 10:06:10.364" endtime="******** 10:06:10.364"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 10:06:10.364" endtime="******** 10:06:10.364"/>
</kw>
<status status="NOT RUN" starttime="******** 10:06:10.363" endtime="******** 10:06:10.364"/>
</branch>
<branch type="ELSE">
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<msg timestamp="******** 10:06:10.366" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<status status="PASS" starttime="******** 10:06:10.364" endtime="******** 10:06:13.634"/>
</kw>
<status status="PASS" starttime="******** 10:06:10.364" endtime="******** 10:06:13.635"/>
</branch>
<status status="PASS" starttime="******** 10:06:10.363" endtime="******** 10:06:13.635"/>
</if>
<status status="PASS" starttime="******** 10:06:10.362" endtime="******** 10:06:13.635"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" library="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.635" endtime="******** 10:06:13.635"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.635" endtime="******** 10:06:13.635"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" library="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.636" endtime="******** 10:06:13.636"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.636" endtime="******** 10:06:13.636"/>
</kw>
<status status="NOT RUN" starttime="******** 10:06:13.636" endtime="******** 10:06:13.636"/>
</branch>
<status status="NOT RUN" starttime="******** 10:06:13.635" endtime="******** 10:06:13.636"/>
</if>
<kw name="Create Dictionary" library="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.636" endtime="******** 10:06:13.637"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.637" endtime="******** 10:06:13.637"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${user_home}</var>
<arg>UserProfile</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.637" endtime="******** 10:06:13.637"/>
</kw>
<kw name="Log" library="BuiltIn">
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.637" endtime="******** 10:06:13.637"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.638" endtime="******** 10:06:13.638"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.638" endtime="******** 10:06:13.638"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.638" endtime="******** 10:06:13.638"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.638" endtime="******** 10:06:13.638"/>
</kw>
<kw name="Evaluate" library="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.638" endtime="******** 10:06:13.638"/>
</kw>
<kw name="Call Method" library="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.639" endtime="******** 10:06:13.639"/>
</kw>
<kw name="Open Browser" library="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" starttime="******** 10:06:13.639" endtime="******** 10:06:13.639"/>
</kw>
<status status="NOT RUN" starttime="******** 10:06:13.635" endtime="******** 10:06:13.639"/>
</branch>
<status status="PASS" starttime="******** 10:06:10.362" endtime="******** 10:06:13.639"/>
</if>
<status status="PASS" starttime="******** 10:06:09.608" endtime="******** 10:06:13.639"/>
</kw>
<kw name="Get Environment Variable" library="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<msg timestamp="******** 10:06:13.640" level="INFO">${BASE_URL} = APC_UAT</msg>
<status status="PASS" starttime="******** 10:06:13.640" endtime="******** 10:06:13.640"/>
</kw>
<kw name="Load" library="Login">
<arg>${BASE_URL}</arg>
<kw name="Get Property From File" library="CreateRestPayloads">
<var>${base_url}</var>
<arg>${URL}</arg>
<msg timestamp="******** 10:06:13.655" level="INFO">Property value fetched is:  https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<msg timestamp="******** 10:06:13.655" level="INFO">${base_url} = https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/</msg>
<status status="PASS" starttime="******** 10:06:13.641" endtime="******** 10:06:13.656"/>
</kw>
<kw name="Maximize Browser Window" library="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" starttime="******** 10:06:13.657" endtime="******** 10:06:13.850"/>
</kw>
<kw name="Run Keyword If" library="BuiltIn">
<arg>'${base_url}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${base_url}</arg>
<arg>ELSE IF</arg>
<arg>'$${base_url}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<kw name="Go To" library="SeleniumLibrary">
<arg>${base_url}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<msg timestamp="******** 10:06:13.858" level="INFO">Opening url 'https://apc-portal.atm-marketing-uat.rbb-banking.270-nonprod.caas.absa.co.za/'</msg>
<status status="PASS" starttime="******** 10:06:13.857" endtime="******** 10:06:14.347"/>
</kw>
<status status="PASS" starttime="******** 10:06:13.851" endtime="******** 10:06:14.347"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:06:24.348" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 10:06:14.347" endtime="******** 10:06:24.348"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:06:24.348" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:06:24.685" level="INFO">${element_count_1} = 0</msg>
<status status="PASS" starttime="******** 10:06:24.348" endtime="******** 10:06:24.685"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 10:06:24.686" endtime="******** 10:06:24.687"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<msg timestamp="******** 10:06:34.689" level="INFO">Slept 10 seconds</msg>
<status status="PASS" starttime="******** 10:06:24.687" endtime="******** 10:06:34.689"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<msg timestamp="******** 10:06:34.689" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:06:34.704" level="INFO">${element_count_2} = 0</msg>
<status status="PASS" starttime="******** 10:06:34.689" endtime="******** 10:06:34.704"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" starttime="******** 10:06:34.704" endtime="******** 10:06:34.705"/>
</kw>
<kw name="Sleep" library="BuiltIn">
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="FAIL" starttime="******** 10:06:34.705" endtime="******** 10:06:35.412"/>
</kw>
<kw name="Get Element Count" library="SeleniumLibrary">
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="NOT RUN" starttime="******** 10:06:35.412" endtime="******** 10:06:35.412"/>
</kw>
<kw name="Run Keyword And Return If" library="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="NOT RUN" starttime="******** 10:06:35.412" endtime="******** 10:06:35.412"/>
</kw>
<status status="FAIL" starttime="******** 10:06:13.640" endtime="******** 10:06:35.413"/>
</kw>
<kw name="Capture Page Screenshot" library="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" starttime="******** 10:06:35.413" endtime="******** 10:06:35.413"/>
</kw>
<status status="FAIL" starttime="******** 10:06:09.607" endtime="******** 10:06:35.413"/>
</kw>
<status status="FAIL" starttime="******** 10:06:09.607" endtime="******** 10:06:35.413"/>
</kw>
<status status="FAIL" starttime="******** 10:06:09.435" endtime="******** 10:06:35.413"/>
</kw>
<kw name="When The user navigates to the Campaign Approvals page" library="Navigation">
<status status="NOT RUN" starttime="******** 10:06:35.413" endtime="******** 10:06:35.413"/>
</kw>
<kw name="And The clicks on the preview button to preview an un-approved campaign" library="Approvals">
<status status="NOT RUN" starttime="******** 10:06:35.414" endtime="******** 10:06:35.414"/>
</kw>
<kw name="And The user approves the campaign" library="Approvals">
<status status="NOT RUN" starttime="******** 10:06:35.414" endtime="******** 10:06:35.414"/>
</kw>
<kw name="And The user navigates back to the Campaign Approvals page" library="Navigation">
<status status="NOT RUN" starttime="******** 10:06:35.414" endtime="******** 10:06:35.414"/>
</kw>
<kw name="Then The user verifies that the campaign has been approved" library="Approvals">
<status status="NOT RUN" starttime="******** 10:06:35.414" endtime="******** 10:06:35.414"/>
</kw>
<status status="FAIL" starttime="******** 10:06:09.434" endtime="******** 10:06:35.414"/>
</kw>
<kw name="User logs out" library="Logout" type="TEARDOWN">
<kw name="Wait for spinner to disapear" library="GenericMethods">
<kw name="Wait Until Element Is Not Visible" library="SeleniumLibrary">
<arg>xpath=//div[contains(@class, 'uploader-status')]</arg>
<arg>30s</arg>
<doc>Waits until the element ``locator`` is not visible.</doc>
<msg timestamp="******** 10:06:39.481" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x00000207451619D0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/44343684ba76925a77f4ee3cf933fcb8/screenshot</msg>
<msg timestamp="******** 10:06:43.522" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000020745161760&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/44343684ba76925a77f4ee3cf933fcb8/screenshot</msg>
<errors>
<msg timestamp="******** 10:06:07.495" level="ERROR">Taking listener 'future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg timestamp="******** 10:06:09.334" level="ERROR">Error in file 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\keywords\common\SetEnvironmentVariales.robot' on line 11: Importing library 'C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Users\<USER>\PycharmProjects\Alternative_Physical_Channels_QA\future_fit_architecture_portal\utility
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg timestamp="******** 10:06:10.360" level="WARN">There was error during termination of process</msg>
<msg timestamp="******** 10:06:24.348" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:06:34.689" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg timestamp="******** 10:06:39.481" level="WARN">Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x00000207451619D0&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/44343684ba76925a77f4ee3cf933fcb8/screenshot</msg>
<msg timestamp="******** 10:06:43.522" level="WARN">Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('&lt;urllib3.connection.HTTPConnection object at 0x0000020745161760&gt;: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it')': /session/44343684ba76925a77f4ee3cf933fcb8/screenshot</msg>
<msg timestamp="******** 10:06:47.566" level="ERROR">Execution stopped by user.</msg>
</errors>
</robot>
>>>>>>> origin/development
