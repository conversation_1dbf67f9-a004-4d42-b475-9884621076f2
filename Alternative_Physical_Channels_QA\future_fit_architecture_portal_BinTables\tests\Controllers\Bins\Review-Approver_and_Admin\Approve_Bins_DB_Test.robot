*** Settings ***
#Author Name               : <PERSON>hab<PERSON>
#Email Address             : thabobenja<PERSON>.<EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables

Documentation       Bin Tables Controllers Test Suite

#***********************************PROJECT RESOURCES***************************************

Resource                            ../../../../keywords/controllers/ApproveBin_Keywords.robot
Resource                            ../../../../keywords/common/SetEnvironmentVariales.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Search for a Bin by Number




*** Keywords ***
Get all Bins to be reviewed using from the database and approve them using the Approve Bin Controller
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}  ${BIN_ID}   ${BIN_NUMBER}    ${EXPECTED_STATUS_CODE}
    Set Test Documentation  ${DOCUMENTATION}
    @{BINS}=    Create List
    IF    '${BIN_ID}' != '${EMPTY}' and '${BIN_NUMBER}' != '${EMPTY}'
        ${bin_dict}=    Create Dictionary    binId=${BIN_ID}    binNumber=${BIN_NUMBER}
        Append To List    ${BINS}       ${bin_dict}
        Log Many        ${BINS}
    END

    Given The User gets all the Bins that are awaiting approval from the Database
    When The user gets the last action type(outcome) performed on the Bins to determine which bins can be approved      @{BINS}
    And The User Populates the Approve Bin JSON payload with data
    And The User sends an API request to Approve the Bin(s)       ${BASE_URL}
    And The service returns an expected status code     ${EXPECTED_STATUS_CODE}
    Then The bin(s) database status must be updated to approved     @{BINS}

| *** Test Cases ***                                                                                                                                                      |        *BASE_URL*       |                         *DOCUMENTATION*                                 |          *BIN_ID*                          |          *BIN_NUMBER*         |       *EXPECTED_STATUS_CODE*  |
| Approve all database BINS using the Approve bins controller   | Get all Bins to be reviewed using from the database and approve them using the Approve Bin Controller   |                         |    Approve all pending bins that are pending approval in the database   |  0d1a7576-285f-4716-88ef-5c0695ae593d      |                               |               200             |
