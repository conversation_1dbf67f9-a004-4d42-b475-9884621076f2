import sqlite3
import os

class PythonSqllite:
    sqliteConnection = None
    cursor = None
    database = "C:\\automation\\databases\\vms\\vms_db"
    table_name = 'vms_tbl'
    def __init__(self):       
        pass
    
    #*****************************Create database table*************************************
    def create_table(self):

        try:
            
            self.create_database_conection()   

            create_table_sql = """CREATE TABLE IF NOT EXISTS """ + self.table_name + """ (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        scenario_name text NOT NULL,
                        reference text,
                        status text
                    );"""
       
            self.cursor.execute(create_table_sql)

        except Exception as e:

            print(e)

        finally:
            self.sqliteConnection.commit()

            self.sqliteConnection.close()  
    
    #*****************************Add new record to the table*************************************
    def add_record(self,scenario_name,reference,status):

       try: 

        self.create_table()

        self.create_database_conection() 
 
        new_record_sql = """INSERT INTO """ + self.table_name + """
                          (scenario_name, reference, status) 
                           VALUES 
                          ('""" + scenario_name + """','""" + reference + """','""" + status + """')"""
      
        self.cursor.execute(new_record_sql)

       except Exception as e:

            print(e)

       finally:

            self.sqliteConnection.commit()

            self.sqliteConnection.close()    
    
    #*****************************Fetch all records from the table*************************************
    def get_all_data(self):

        try:   

            self.create_database_conection()  

            sqlite_select_query = """SELECT * from vms_tbl"""

            self.cursor.execute(sqlite_select_query)

            records = self.cursor.fetchall()
        
            print("Total rows are:  ", len(records))

            print("Printing each row")

            for row in records:
                print("Scenario ID: ", row[0])
                print("Scenario: ", row[1])
                print("Reference: ", row[2])
                print("Status: ",row[3])
                print("\n")

        except Exception as e:

            print(e)

        finally:

            self.sqliteConnection.commit()

            self.sqliteConnection.close()

    def get_single_record(self,reference):

        results=None

        try:

            self.create_database_conection() 
           
            self.cursor.execute("SELECT * FROM " + self.table_name + " WHERE reference=?", (reference,))

            results = self.cursor.fetchall()

            if len(results)  == 0:
                print("The record for reference ", reference, " could not be found")

            return results      

        except sqlite3.Error as error:
           
            print("Failed to read single row from sqlite table", error)
           
            return results 

        finally:

            if self.sqliteConnection:
                
                self.sqliteConnection.close()

                print("The SQLite connection is closed")
                
    def update_status(self,reference, new_status):

        try:

            self.create_database_conection() 
            
            sql_update_query = """Update """ + self.table_name + """ set status = '""" + new_status + """' where reference = '""" + reference + """'"""
            
            self.cursor.execute(sql_update_query)
            
            self.sqliteConnection.commit()
            
            print("Record Updated successfully ")
            
        except sqlite3.Error as error:

            print("Failed to update sqlite table", error)

        finally:

            if self.sqliteConnection:

                self.sqliteConnection.close()

                print("The SQLite connection is closed")
                
    def create_database_conection(self):

        try:

            self.sqliteConnection = sqlite3.connect(self.database.strip(' '))
           
            self.cursor = self.sqliteConnection.cursor()
          
            print("Database created and Successfully Connected to SQLite")

        except sqlite3.Error as error:
          
            print("Error while connecting to sqlite", error)

    def delete_record(self,reference):

        try:
            self.create_database_conection() 
            
            # Deleting single record now
            sql_delete_query = """DELETE from """ + self.table_name + """ where reference = '""" + reference +"""'"""
            
            self.cursor.execute(sql_delete_query)
            
            self.sqliteConnection.commit()
            
            print("Record deleted successfully ")
            
        except sqlite3.Error as error:
            
            print("Failed to delete record from sqlite table", error)

        finally:

            if self.sqliteConnection:

                self.sqliteConnection.close()

                print("the sqlite connection is closed")



# pythonSqllite = PythonSqllite()
# pythonSqllite.get_all_data()
