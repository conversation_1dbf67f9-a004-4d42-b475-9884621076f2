from typing import List, Union


class CreateRESTRequest:
    def __init__(self, domain, bin_number):
        self.domain = domain
        self.bin_number = bin_number

        self.params = {
            "binNumber": self.bin_number,  # Adding a query parameter to filter the results by Bin Number
        }

    def get_endpoint(self):
        path = "/api/v1/bintables/all/bins/getbinstoreview"
        url = f"{self.domain}{path}"
        return url

    def get_params(self):
        return self.params

