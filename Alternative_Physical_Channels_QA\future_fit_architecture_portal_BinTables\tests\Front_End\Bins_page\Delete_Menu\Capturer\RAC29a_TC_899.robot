*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                                        BIN_TABLES_HEALTHCHECK  REGRESSION
Suite Setup                                         Set up environment variables
Test Teardown                                       User logs out

Documentation       Bin Tables Front End Test Suite
#***********************************PROJECT RESOURCES***************************************

Resource                ../../../../../keywords/front_end/Landing_Page.robot
Resource                ../../../../../keywords/front_end/Delete_Bins_Page.robot
Resource                ../../../../../keywords/common/SetEnvironmentVariales.robot
Resource                ../../../../../../common_utilities/Login.robot
Resource                ../../../../../../common_utilities/Login.robot


*** Variables ***
${SUITE NAME}               BIN Tables - Delete Menu
${TEST_CASE_ID}             RAC29a-TC-899




*** Keywords ***
Delete Bin button disabled when a Bin is not Found
    [Arguments]        ${DOCUMENTATION}    ${BASE_URL}      ${INVALID_BIN_NUMBER}    ${EXPECTED_ERROR_MESSAGE}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into Future Fit Architecture - Bin Tables portal    ${BASE_URL}
    When The User clicks Bins Menu
    And The user navigates to 'Delete' Bin tab
    And The User populates an invalid Bin Number        ${INVALID_BIN_NUMBER}    
    And The user verifies that the 'Bin not found' error message is displayed    ${EXPECTED_ERROR_MESSAGE}
    Then The user verifies that the Delete Bin button remains disabled

| *** Test Cases ***                                                                                                           |        *DOCUMENTATION*        |         *BASE_URL*             |         *INVALID_BIN_NUMBER*        |     *EXPECTED_ERROR_MESSAGE*    |
| Capturer_Delete Bin Button Disabled When BIN Not Found            | Delete Bin button disabled when a Bin is not Found       |       Delete a Bin Number.    |           ${EMPTY}             |          09                         |      Bin number not found!      |
