*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control- Branch Dashboard- Validating the Custodian Takeover Flow
Suite Setup                 Set up environment variables               
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem


#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot



*** Variables ***


*** Keywords ***
The user validates the Custodian Takeover Flow on Branch dashboard  
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page

    And the user navigates to the ATM Control Dashboard  

    And the user selects a branch to access the Branch Dashboard 

    And the user selects the Custodian Takeover menu

    And the user selects a Custodian from the list

    And the user verifies Custodian info is auto-populated after selection

    And the user selects an ATM Number to assign a device to the Custodian

    And the user enters a comment

    And the user clicks save to confirm the takeover

    Then the user verifies the Custodian Takeover was saved successfully 


| *Test Cases*                                                                                                                               |      *DOCUMENTATION*                                          | *TEST_ENVIRONMENT*   |
|   MQ_TC_10_Branch_Dashboard_validate_Custodian_takeover_feature    | The user validates the Custodian Takeover Flow on Branch dashboard    |    Validating the Custodian Takeover Flow on Branch dashboard |    MTGLA_UAT         | 
