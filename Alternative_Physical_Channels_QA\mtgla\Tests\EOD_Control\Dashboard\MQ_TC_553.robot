*** Settings ***
#Author Name               : Yaash
#Email Address             : <EMAIL>

Default Tags                MTGLA HEALTHCHECK    
Documentation               ATM Control Dashboard Validation 
Suite Setup                 Set up environment variables  
Test Teardown               Close All Browsers

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary   
Library                                             OperatingSystem
#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../keywords/Common/Login.robot
Resource                                            ../../../keywords/Common/HomePage.robot
Resource                                            ../../../keywords/Common/Navigation.robot
Resource                                            ../../../keywords/ATM_Control_Dashboard.robot
Resource                                            ../../../keywords/Common/SetEnvironmentVariales.robot
Resource                                            ../../../keywords/EOD_Control_Dashboard.robot

*** Variables ***

*** Keywords ***
Validates that the user can expand Regions to view Cost Centre information
    [Arguments]  ${DOCUMENTATION}   ${TEST_ENVIRONMENT}  
    Set Test Documentation  ${DOCUMENTATION}

    Given the user logs into the MTGLA Web Application

    When the user lands on the Home page 

    And the user navigates to the EOD Control Dashboard Menu

    Then the user verifies that the regions can be expanded to display the cost centres


| *Test Cases*                                                                                                                                    |      *DOCUMENTATION* | *TEST_ENVIRONMENT*   |
| 	Verify user can expand Regions to view Cost Centre information	| Validates that the user can expand Regions to view Cost Centre information  |    Attestation Tile  |    MTGLA_UAT         | 
